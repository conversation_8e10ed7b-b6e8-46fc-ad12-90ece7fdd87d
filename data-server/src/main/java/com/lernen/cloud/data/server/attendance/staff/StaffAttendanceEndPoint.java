package com.lernen.cloud.data.server.attendance.staff;

import com.lernen.cloud.core.api.attendance.staff.StaffAttendanceDetails;
import com.lernen.cloud.core.api.attendance.staff.StaffAttendancePayload;
import com.lernen.cloud.core.api.attendance.staff.v2.StaffAttendancePayloadV2;
import com.lernen.cloud.core.api.attendance.staff.v2.StaffAttendanceRegisterData;
import com.lernen.cloud.core.lib.attendance.staff.StaffAttendanceManager;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.List;
import java.util.UUID;


@Path("/2.0/staff-attendance")
@Deprecated
public class StaffAttendanceEndPoint {

    private final StaffAttendanceManager staffAttendanceManager;

    public StaffAttendanceEndPoint(StaffAttendanceManager staffAttendanceManager) {
        this.staffAttendanceManager = staffAttendanceManager;
    }

    @GET
    @Path("staff-attendance-details/{institute_id}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @Deprecated
    public Response getStaffAttendanceDetails(@PathParam("institute_id") int instituteId,
                                              @QueryParam("attendance_date") Integer attendanceDate) {
        final List<StaffAttendanceDetails> staffAttendanceDetailsList = staffAttendanceManager.getStaffAttendanceDetailsList(instituteId, attendanceDate);
        return Response.status(Response.Status.OK.getStatusCode()).entity(staffAttendanceDetailsList).build();
    }


    @POST
    @Path("save")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @Deprecated
    public Response saveStaffAttendanceDetails(StaffAttendancePayload staffAttendancePayload,
                                               @QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId) {
        final List<String> errorList = staffAttendanceManager.saveStaffAttendanceDetails(instituteId, userId, staffAttendancePayload, false);
        return Response.status(Response.Status.OK.getStatusCode()).entity(errorList).build();
    }

}
