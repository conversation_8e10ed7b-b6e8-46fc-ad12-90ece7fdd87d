package com.lernen.cloud.data.server.audit.log;

import java.util.UUID;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import com.lernen.cloud.core.api.audit.log.AuditLog;
import com.lernen.cloud.core.api.common.SearchResultWithPagination;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.lib.audit.log.AuditLogManager;

/**
 *
 * <AUTHOR>
 *
 */
@Path("/2.0/audit-log")
public class AuditLogEndpoint {

	private final AuditLogManager auditLogManager;

	public AuditLogEndpoint(AuditLogManager auditLogManager) {
		this.auditLogManager = auditLogManager;
	}

	@GET
	@Produces(MediaType.APPLICATION_JSON)
	public Response getAuditLogs(@QueryParam("institute_id") int instituteId,
			@QueryParam("search_text") String searchText, @QueryParam("start") int start, @QueryParam("end") int end,
			@QueryParam("offset") int offset, @QueryParam("limit") int limit) {
		final SearchResultWithPagination<AuditLog> auditLogs = auditLogManager.getAuditLogs(instituteId, searchText,
				start, end, offset, limit);
		if (auditLogs == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to get audit logs"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(auditLogs).build();
	}

	@GET
	@Path("{log_id}/details")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getClassCoursesByInstitute(@PathParam("log_id") UUID logId,
			@QueryParam("institute_id") int instituteId) {
		final AuditLog detailedLog = auditLogManager.getResolvedAuditLog(instituteId, logId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(detailedLog).build();
	}

}
