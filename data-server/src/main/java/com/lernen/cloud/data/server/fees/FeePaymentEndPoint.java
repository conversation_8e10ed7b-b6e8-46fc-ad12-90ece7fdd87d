package com.lernen.cloud.data.server.fees;

import com.embrate.cloud.core.api.fee.FeeCancelledPayload;
import com.embrate.cloud.core.api.service.notification.BellNotificationDetails;
import com.embrate.cloud.core.api.service.notification.NotificationEntity;
import com.embrate.cloud.core.lib.push.notification.PushNotificationManager;
import com.embrate.cloud.emailer.service.IEmailService;
import com.embrate.cloud.push.notifications.handler.FeePaymentPushNotificationHandler;
import com.embrate.cloud.push.notifications.handler.FeePaymentReminderPushNotificationHandler;
import com.embrate.cloud.voicecall.handler.DueFeesVoiceHandler;
import com.lernen.cloud.core.api.common.DeliveryMode;
import com.lernen.cloud.core.api.common.SearchResultWithPagination;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.fees.payment.*;
import com.lernen.cloud.core.api.fees.payment.reminders.FeePaymentReminderPayload;
import com.lernen.cloud.core.api.notification.NotificationBatchDetails;
import com.lernen.cloud.core.api.notification.NotificationDetails;
import com.lernen.cloud.core.api.notification.NotificationType;
import com.lernen.cloud.core.api.sms.SMSResponse;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.api.voicecall.VoiceCallResponse;
import com.lernen.cloud.core.lib.fees.payment.FeePaymentInsightManager;
import com.lernen.cloud.core.lib.fees.payment.FeePaymentManager;
import com.lernen.cloud.core.lib.notifications.NotificationManager;
import com.lernen.cloud.pdf.demand.notice.DemandNoticeHandler;
import com.lernen.cloud.pdf.invoice.fee.FeeInvoiceHandler;
import com.lernen.cloud.sms.handler.DueFeesSMSHandler;
import com.lernen.cloud.sms.handler.FeePaymentSMSHandler;
import org.springframework.util.CollectionUtils;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.ByteArrayInputStream;
import java.util.*;

/**
 *
 * <AUTHOR>
 *
 */
@Path("/2.0/fee-payment")
public class FeePaymentEndPoint {

	private final FeePaymentManager feePaymentManager;

	private final FeePaymentInsightManager feePaymentInsightManager;

	private final DueFeesSMSHandler dueFeesSMSHandler;

	private final DueFeesVoiceHandler dueFeesVoiceHandler;

	private final FeePaymentSMSHandler feePaymentSMSHandler;

	private final DemandNoticeHandler demandNoticeHandler;

	private final NotificationManager notificationManager;

	private final FeeInvoiceHandler feeInvoiceHandler;

	private final FeePaymentPushNotificationHandler feePaymentPushNotificationHandler;
	private final FeePaymentReminderPushNotificationHandler feePaymentReminderPushNotificationHandler;

	private final PushNotificationManager pushNotificationManager;
	public FeePaymentEndPoint(FeePaymentManager feePaymentManager, FeePaymentInsightManager feePaymentInsightManager,
			IEmailService emailService, DueFeesSMSHandler dueFeesSMSHandler, DueFeesVoiceHandler dueFeesVoiceHandler,
			FeePaymentSMSHandler feePaymentSMSHandler, DemandNoticeHandler demandNoticeHandler,
			NotificationManager notificationManager, FeeInvoiceHandler feeInvoiceHandler,
							  FeePaymentPushNotificationHandler feePaymentPushNotificationHandler,
							  FeePaymentReminderPushNotificationHandler feePaymentReminderPushNotificationHandler,
							  PushNotificationManager pushNotificationManager) {
		this.feePaymentManager = feePaymentManager;
		this.feePaymentInsightManager = feePaymentInsightManager;
		this.dueFeesSMSHandler = dueFeesSMSHandler;
		this.dueFeesVoiceHandler = dueFeesVoiceHandler;
		this.feePaymentSMSHandler = feePaymentSMSHandler;
		this.demandNoticeHandler = demandNoticeHandler;
		this.notificationManager = notificationManager;
		this.feeInvoiceHandler = feeInvoiceHandler;
		this.feePaymentPushNotificationHandler = feePaymentPushNotificationHandler;
		this.feePaymentReminderPushNotificationHandler = feePaymentReminderPushNotificationHandler;
		this.pushNotificationManager = pushNotificationManager;
	}

	@GET
	@Path("class-stats/{institute_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getTransactionStatistics(@PathParam("institute_id") int instituteId,
			@QueryParam("academic_session_id") Integer academicSessionId) {
		if (instituteId <= 0 || academicSessionId == null || academicSessionId < 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid parameters provided "));
		}
		final FeePaymentAggregatedData feePaymentAggregatedData = feePaymentManager
				.getClassPaymentStats(instituteId, academicSessionId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(feePaymentAggregatedData).build();
	}

	@GET
	@Path("date-wise-class-stats/{institute_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getDateWiseTransactionStatistics(@PathParam("institute_id") int instituteId,
				@QueryParam("date") int date, @QueryParam("academic_session_id") Integer academicSessionId) {
		if (instituteId <= 0 || academicSessionId == null || academicSessionId < 0 || date <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid parameters provided "));
		}
		final FeePaymentAggregatedData feePaymentAggregatedData = feePaymentManager
				.getDateWiseClassPaymentStats(instituteId, academicSessionId, date);
		return Response.status(Response.Status.OK.getStatusCode()).entity(feePaymentAggregatedData).build();
	}

	@POST
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response addFeePayment(@QueryParam("sleep") boolean sleep, @QueryParam("user_id") UUID userId,
			FeePaymentPayload feePaymentPayload) {
		final FeePaymentResponse feePaymentResponse = feePaymentManager.addFeePayment(feePaymentPayload, sleep, userId, false);
		if (feePaymentResponse == null || feePaymentResponse.getTransactionId() == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_FEE_TRANSACTION, "Unable to add fee transaction"));
		}

		feePaymentSMSHandler.sendFeePaymentSMSAsync(
				feePaymentPayload.getFeePaymentTransactionMetaData().getInstituteId(),
				feePaymentResponse.getTransactionId(), userId, true);

		feePaymentPushNotificationHandler.sendFeePaymentNotificationsAsync(feePaymentPayload.getFeePaymentTransactionMetaData().getInstituteId(), feePaymentResponse);

		return Response.status(Response.Status.OK.getStatusCode()).entity(feePaymentResponse).build();
	}

	@POST
	@Path("student-wallet")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response walletFeePayment(@QueryParam("institute_id") int  instituteId,
									 StudentWalletFeePaymentPayload studentWalletFeePaymentPayload) {
		final WalletFeePaymentResponse walletFeePaymentResponse = feePaymentManager.addFeePaymentFromWallet(instituteId, studentWalletFeePaymentPayload);
//		feePaymentSMSHandler.sendFeePaymentSMSAsync(
//				instituteId,
//				feePaymentResponse.getTransactionId(), studentWalletFeePaymentPayload.getStudentId(), true);
		return Response.status(Response.Status.OK.getStatusCode()).entity(walletFeePaymentResponse).build();
	}

	@GET
	@Path("transactions/{student_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStudentFeePaymentTransactions(@PathParam("student_id") UUID studentId,
			@QueryParam("institute_id") int instituteId) {
		List<FeePaymentTransactionStatus> feePaymentTransactionStatusList = new ArrayList<FeePaymentTransactionStatus>();
		feePaymentTransactionStatusList.add(FeePaymentTransactionStatus.ACTIVE);
		feePaymentTransactionStatusList.add(FeePaymentTransactionStatus.CANCELLED);
		final List<FeePaymentTransactionMetaData> feePaymentTransactionMetaDatas = feePaymentManager
				.getFeePaymentTransactions(instituteId, studentId, feePaymentTransactionStatusList);
		if (feePaymentTransactionMetaDatas == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_TRANSACTION,
					"Unable to get fee transaction details"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(feePaymentTransactionMetaDatas).build();
	}

	@Deprecated
	@GET
	@Path("transactions")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getAllFeePaymentTransactions(@QueryParam("institute_id") int instituteId,
			@QueryParam("academic_session_id") Integer academicSessionId,
			@QueryParam("status") FeePaymentTransactionStatus status, @QueryParam("offset") Integer offset,
			@QueryParam("limit") Integer limit) {
		final SearchResultWithPagination<FeePaymentTransactionMetaData> feePaymentTransactionMetaDatas = feePaymentManager
				.searchFeePaymentTransactions(instituteId, null, academicSessionId, status, offset, limit, true);
		if (feePaymentTransactionMetaDatas == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_TRANSACTION,
					"Unable to get fee transaction details"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(feePaymentTransactionMetaDatas).build();
	}

	@GET
	@Path("transactions-search")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response searchFeePaymentTransactions(@QueryParam("institute_id") int instituteId,
												 @QueryParam("academic_session_id") Integer academicSessionId,
												 @QueryParam("status") FeePaymentTransactionStatus status, @QueryParam("offset") Integer offset,
												 @QueryParam("limit") Integer limit, @QueryParam("search_text") String searchText) {
		final SearchResultWithPagination<FeePaymentTransactionMetaData> feePaymentTransactionMetaDatas = feePaymentManager
				.searchFeePaymentTransactions(instituteId, searchText, academicSessionId, status, offset, limit, true);
		if (feePaymentTransactionMetaDatas == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_TRANSACTION,
					"Unable to get fee transaction details"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(feePaymentTransactionMetaDatas).build();
	}

	@GET
	@Path("transaction-details/{transaction_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getTranscationDetails(@PathParam("transaction_id") UUID transactionId,
			@QueryParam("institute_id") int instituteId) {
		final FeePaymentTransactionDetails feePaymentTransactionDetails = feePaymentManager
				.getTransactionDetails(instituteId, transactionId);
		if (feePaymentTransactionDetails == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_TRANSACTION,
					"Unable to get fee transaction details"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(feePaymentTransactionDetails).build();
	}

	@GET
	@Path("student-transaction-details/{transaction_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStudentFeePaymentTransactionDetails(@PathParam("transaction_id") UUID transactionId,
			@QueryParam("institute_id") int instituteId) {
		final StudentFeePaymentTransactionDetails feePaymentTransactionDetails = feePaymentManager
				.getStudentFeePaymentTransactionDetails(instituteId, transactionId);
		if (feePaymentTransactionDetails == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_TRANSACTION,
					"Unable to get fee transaction details"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(feePaymentTransactionDetails).build();
	}

	@GET
	@Path("invoice/{transaction_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getTransactionInvoice(@PathParam("transaction_id") UUID transactionId,
			@QueryParam("institute_id") int instituteId) {
		final StudentFeePaymentTransactionDetails feePaymentInvoice = feePaymentManager
				.getTransactionInvoice(instituteId, transactionId);
		if (feePaymentInvoice == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_FEE_TRANSACTION, "Unable to get fee invoice"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(feePaymentInvoice).build();
	}

	@GET
	@Path("invoice/{transaction_id}/summary")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getTransactionInvoiceSummary(@PathParam("transaction_id") UUID transactionId,
			@QueryParam("institute_id") int instituteId) {
		final FeePaymentInvoiceSummary feePaymentInvoiceSummary = feePaymentManager
				.getTransactionInvoiceSummary(instituteId, transactionId, false);
		if (feePaymentInvoiceSummary == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_TRANSACTION,
					"Unable to get fee transaction details"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(feePaymentInvoiceSummary).build();
	}

	@GET
	@Path("invoice/{transaction_id}/pdf-summary")
	@Produces("application/pdf")
	public Response getTransactionInvoiceSummaryPDF(@PathParam("transaction_id") UUID transactionId,
			@QueryParam("institute_id") int instituteId, @QueryParam("office_copy") Boolean officeCopy,
			@QueryParam("user_type_str") String userTypeStr) {
		final DocumentOutput documentOutput = feeInvoiceHandler.generateInvoice(instituteId, transactionId, officeCopy,
				userTypeStr);
		// Just write filename= no need for attachment/inline for displaying pdf
		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}

	@GET
	@Path("bulk-invoice-pdf")
	@Produces("application/pdf")
	public Response getBulkPdfInvoices(@QueryParam("institute_id") int instituteId,
			@QueryParam("academic_session_id") int academicSessionId, @QueryParam("start_date") int startDate,
			@QueryParam("end_date") int endDate, @QueryParam("fee_transaction_status") FeePaymentTransactionStatus feePaymentTransactionStatus,
			@QueryParam("office_copy") Boolean officeCopy, @QueryParam("user_id") UUID userId) {
		final DocumentOutput documentOutput = feeInvoiceHandler.generateBulkInvoices(instituteId, academicSessionId, officeCopy,
				userId, startDate, endDate, feePaymentTransactionStatus);
		// Just write filename= no need for attachment/inline for displaying pdf
		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}

	@GET
	@Path("bulk-invoice/pdf-summary")
	@Produces("application/pdf")
	public Response getBulkTransactionInvoiceSummaryPDF(@QueryParam("transaction_ids_str") String transactionIdsStr,
			@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
			@QueryParam("office_copy") Boolean officeCopy, @QueryParam("user_id") UUID userId) {
		final DocumentOutput documentOutput = feeInvoiceHandler.generateBulkInvoices(instituteId, academicSessionId, officeCopy,
				userId, transactionIdsStr);
		// Just write filename= no need for attachment/inline for displaying pdf
		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}

	@POST
	@Path("cancel-transaction/{transaction_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response cancelFeePaymentTransaction(@PathParam("transaction_id") UUID transactionId,
			@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId) {
		final boolean cancelSuccess = feePaymentManager.cancelFeePaymentTransaction(instituteId, transactionId, userId);
		if (!cancelSuccess) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_TRANSACTION,
					"Unable to cancel fee transaction "));
		}

		feePaymentSMSHandler.sendFeePaymentCancellationSMSAsync(instituteId, transactionId, userId, true);

		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@POST
	@Path("cancel-transaction")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response cancelFeePaymentTransactionWithPayload(@QueryParam("institute_id") int instituteId,
					   @QueryParam("user_id") UUID userId, FeeCancelledPayload feeCancelledPayload) {
		final boolean cancelSuccess = feePaymentManager.cancelFeePaymentTransactionWithPayload(instituteId, userId, feeCancelledPayload);
		if (!cancelSuccess) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_TRANSACTION,
					"Unable to cancel fee transaction "));
		}

		if(feeCancelledPayload.getTransactionId() != null) {
			feePaymentSMSHandler.sendFeePaymentCancellationSMSAsync(instituteId, feeCancelledPayload.getTransactionId(), userId, true);
		}

		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@GET
	@Path("summary/{student_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getFeePaymentSummary(@PathParam("student_id") UUID studentId,
			@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") Integer academicSessionId) {
		if (academicSessionId != null && academicSessionId > 0) {
			final StudentFeesDetails studentFeesDetails = feePaymentManager.getPaymentDetails(instituteId,
					academicSessionId, studentId, true);
			if (studentFeesDetails == null) {
				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_PAYMENT,
						"Unable to get fee payment details"));
			}
			return Response.status(Response.Status.OK.getStatusCode()).entity(studentFeesDetails).build();
		}

		final List<StudentFeesDetails> studentFeesDetails = feePaymentManager.getPaymentDetails(instituteId, studentId, true);
		if (studentFeesDetails == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_FEE_PAYMENT, "Unable to get fee payment details"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentFeesDetails).build();

	}

	@GET
	@Path("other-session-dues/{student_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStudentOtherSessionDueDetails(@PathParam("student_id") UUID studentId,
										 @QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId) {
		final List<StudentFeesDetails> studentFeesDetails = feePaymentManager.getStudentOtherSessionDueDetails(instituteId,
				academicSessionId, studentId, true);
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentFeesDetails).build();

	}

	@GET
	@Path("summary-lite/{student_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStudentFeesDetailsLite(@PathParam("student_id") UUID studentId,
			@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") Integer academicSessionId) {
		final StudentFeesDetailsLite studentFeesDetailsLite = feePaymentManager.getStudentFeesDetailsLite(instituteId,
				academicSessionId, studentId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentFeesDetailsLite).build();
	}

	/**
	 *
	 * @param studentId
	 * @param instituteId
	 * @param academicSessionId
	 * @return
	 *
	 * use this only when we need wallet details along with fees details
	 */
	@GET
	@Path("summary-wallet-amount/{student_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getFeePaymentSummaryWithWallet(@PathParam("student_id") UUID studentId,
										 @QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") Integer academicSessionId) {
		final StudentFeesDetailsWithWallet studentFeesDetailsWithWallet = feePaymentManager.getFeePaymentSummaryWithWallet(instituteId,
				academicSessionId, studentId, true);
		if (studentFeesDetailsWithWallet == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_FEE_PAYMENT,
					"Unable to get fee payment details"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentFeesDetailsWithWallet).build();
	}

	@GET
	@Path("due-fees-students")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getDueFeeStudents(@QueryParam("institute_id") int instituteId,
			@QueryParam("academic_session_id") int academicSessionId, @QueryParam("due_date") int dueDate,
			@QueryParam("include_fine") boolean computeFine,
			@QueryParam("required_standards") String requiredStandardsCSV, @QueryParam("session_count") Integer sessionCount) {
		final List<StudentDueFeesData> studentDueFeesDataList = feePaymentManager.getDueFeesStudents(instituteId,
				academicSessionId, dueDate, computeFine, requiredStandardsCSV, sessionCount);
		if (studentDueFeesDataList == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_FEE_PAYMENT, "Unable to get due fee students"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentDueFeesDataList).build();
	}

	@GET
	@Path("due-payment-emails/{email}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response sendEmails(@PathParam("email") String email) {
//		final String messageId = emailService.sendEmail(new EmailData(null, null, null, Arrays.asList(email)));
//		if (messageId == null) {
//			throw new ApplicationException(
//					new ErrorResponse(ApplicationErrorCode.INVALID_FEE_TRANSACTION, "Unable to send email "));
//		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}

	@POST
	@Path("send-due-payment-reminder")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response sendDueFeesReminders(@QueryParam("institute_id") int instituteId,
			FeePaymentReminderPayload feePaymentReminderPayload, @QueryParam("user_id") UUID userId) {
		if(feePaymentReminderPayload.getDeliveryMode() == DeliveryMode.SMS){
			final SMSResponse smsResponse = dueFeesSMSHandler.bulkSend(instituteId, feePaymentReminderPayload, userId, true);
			return Response.status(Response.Status.OK.getStatusCode()).entity(smsResponse).build();
		}else if(feePaymentReminderPayload.getDeliveryMode() == DeliveryMode.CALL){
			VoiceCallResponse voiceCallResponse = dueFeesVoiceHandler.bulkSend(instituteId, feePaymentReminderPayload, userId);
			return Response.status(Response.Status.OK.getStatusCode()).entity(voiceCallResponse).build();
		}else if(feePaymentReminderPayload.getDeliveryMode() == DeliveryMode.APP){
			feePaymentReminderPushNotificationHandler.sendFeePaymentReminderNotificationsAsync(instituteId, feePaymentReminderPayload, userId);
			return Response.status(Response.Status.OK.getStatusCode()).build();
		}
		throw new ApplicationException(
				new ErrorResponse(ApplicationErrorCode.INVALID_FEE_PAYMENT, "Invalid delivery mode for due fee send reminder"));
	}

	@GET
	@Path("batch-notification-history/{delivery_mode}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getBatchNotificationHistory(@PathParam("delivery_mode") DeliveryMode deliveryMode,
			@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
			@QueryParam("offset") int offset, @QueryParam("limit") int limit) {

		final SearchResultWithPagination<NotificationBatchDetails<NotificationDetails>> notificationHistory = notificationManager
				.getBatchNotificationHistory(instituteId, academicSessionId,
						new HashSet<>(Arrays.asList(NotificationType.DUE_FEES, NotificationType.FEE_PAYMENT,
								NotificationType.FEE_PAYMENT_CANCELLATION, NotificationType.FEE_PAYMENT_CANCELLATION_ADMIN)),
						deliveryMode, UserType.STUDENT, offset, limit);

		return Response.status(Response.Status.OK.getStatusCode()).entity(notificationHistory).build();
	}

	@GET
	@Path("individual-notification-history/{delivery_mode}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getIndividualNotificationHistory(@PathParam("delivery_mode") DeliveryMode deliveryMode,
			@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
			@QueryParam("offset") int offset, @QueryParam("limit") int limit) {

		final SearchResultWithPagination<NotificationDetails> notificationHistory = notificationManager
				.getIndividualNotificationHistory(instituteId, academicSessionId,
						new HashSet<>(Arrays.asList(NotificationType.DUE_FEES, NotificationType.FEE_PAYMENT,
								NotificationType.FEE_PAYMENT_CANCELLATION, NotificationType.FEE_PAYMENT_CANCELLATION_ADMIN)),
						deliveryMode, UserType.STUDENT, offset, limit);

		return Response.status(Response.Status.OK.getStatusCode()).entity(notificationHistory).build();
	}

	@GET
	@Path("batch-notification-detail/{user_type}/{batch_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getBatchNotificationDetails(@PathParam("user_type") UserType userType,
			@PathParam("batch_id") UUID batchId, @QueryParam("institute_id") int instituteId) {

		final List<NotificationDetails> notificationDetails = notificationManager
				.getBatchNotificationDetails(instituteId, batchId, userType);
		return Response.status(Response.Status.OK.getStatusCode()).entity(notificationDetails).build();
	}

//	@GET
//	@Path("due-reminder-history/{delivery_mode}")
//	@Consumes(MediaType.APPLICATION_JSON)
//	@Produces(MediaType.APPLICATION_JSON)
//	public Response getSentDueFeesReminders(@PathParam("delivery_mode") DeliveryMode deliveryMode,
//			@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId) {
//
//		final NotificationHistory<StudentNotificationDetailsLite> notificationHistory = notificationManager
//				.getNotificationHistory(instituteId, academicSessionId, NotificationType.DUE_FEES, deliveryMode,
//						UserType.STUDENT);
//
//		return Response.status(Response.Status.OK.getStatusCode()).entity(notificationHistory).build();
//	}

	@POST
	@Path("generate-demand-notice")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces("application/pdf")
	public Response generateDemandNotice(@QueryParam("institute_id") int instituteId,
			FeePaymentReminderPayload feePaymentReminderPayload, @QueryParam("user_id") UUID userId) {

		final DocumentOutput documentOutput = demandNoticeHandler.generateBulkDemandNotices(instituteId,
				feePaymentReminderPayload, userId);

		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}

	@GET
	@Path("siblings-fee-detail/{student_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getSiblingFeeDetails(@PathParam("student_id") UUID studentId,
				@QueryParam("academic_session_id") int academicSessionId, @QueryParam("institute_id") int instituteId) {
		final List<StudentFeePaymentAggregatedData> studentFeePaymentAggregatedDataList =
				feePaymentManager.getStudentSiblingsDueAmounts(instituteId, academicSessionId, studentId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(studentFeePaymentAggregatedDataList).build();
	}

	@POST
	@Path("collect-fees-lite")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response collectFeePaymentLite(@QueryParam("sleep") boolean sleep, @QueryParam("user_id") UUID userId,
										  @QueryParam("institute_id") int instituteId, FeePaymentPayloadLite feePaymentPayloadLite) {
		final FeePaymentResponse feePaymentResponse = feePaymentManager.collectFeePaymentLite(instituteId,
				feePaymentPayloadLite, userId, sleep);
		if (feePaymentResponse == null || feePaymentResponse.getTransactionId() == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_FEE_TRANSACTION, "Unable to add fee transaction"));
		}

		feePaymentSMSHandler.sendFeePaymentSMSAsync(
				instituteId, feePaymentResponse.getTransactionId(), userId, true);

		feePaymentPushNotificationHandler.sendFeePaymentNotificationsAsync(instituteId, feePaymentResponse);

		return Response.status(Response.Status.OK.getStatusCode()).entity(feePaymentResponse).build();
	}

	@GET
	@Path("bell-notifications-with-pagination")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getBellNotificationByEntity(@QueryParam("institute_id") int instituteId,
												@QueryParam("offset") int offset, @QueryParam("limit") int limit) {
		final SearchResultWithPagination<BellNotificationDetails> notificationHistory = pushNotificationManager
				.getBellNotificationByEntity(instituteId, new HashSet<>(Arrays.asList(NotificationEntity.FEE_PAYMENT_REMINDER)), offset, limit);

		return Response.status(Response.Status.OK.getStatusCode()).entity(notificationHistory).build();
	}

	@GET
	@Path("transaction/{status}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getFeePaymentTransactionsByStatusLimitOffset(@QueryParam("institute_id") int instituteId,
												 @QueryParam("academic_session_id") int academicSessionId,
												 @PathParam("status") FeePaymentTransactionStatus status, @QueryParam("offset") int offset,
												 @QueryParam("limit") int limit) {
		final List<FeePaymentTransactionMetaData> feePaymentTransactionMetaDataList = feePaymentManager
				.getFeePaymentTransactionsByStatusLimitOffset(instituteId, academicSessionId, status, offset, limit);
		return Response.status(Response.Status.OK.getStatusCode()).entity(feePaymentTransactionMetaDataList).build();
	}

	@POST
	@Path("collect-bulk-student-fees")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response collectBulkFeePaymentLite(@QueryParam("sleep") boolean sleep, @QueryParam("user_id") UUID userId,
				  @QueryParam("academic_session_id") int academicSessionId,
				  @QueryParam("institute_id") int instituteId, List<FeePaymentPayloadLite> feePaymentPayloadLiteList) {
		final List<FeePaymentResponse> feePaymentResponseList = feePaymentManager.collectBulkFeePaymentLite(instituteId, academicSessionId,
				feePaymentPayloadLiteList, userId, sleep);
		if (CollectionUtils.isEmpty(feePaymentResponseList)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_FEE_TRANSACTION, "Unable to add fee transaction"));
		}

		for(FeePaymentResponse feePaymentResponse : feePaymentResponseList) {

			if(feePaymentResponse == null || feePaymentResponse.getTransactionId() == null) {
				continue;
			}

			feePaymentSMSHandler.sendFeePaymentSMSAsync(
					instituteId, feePaymentResponse.getTransactionId(), userId, true);

			feePaymentPushNotificationHandler.sendFeePaymentNotificationsAsync(instituteId, feePaymentResponse);
		}

		return Response.status(Response.Status.OK.getStatusCode()).entity(feePaymentResponseList).build();
	}

	@GET
	@Path("restrict-session-fee-payment/{student_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response restrictSessionFeesPayment(@PathParam("student_id") UUID studentId,
											   @QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") Integer academicSessionId) {
		final boolean isRestrictSessionFeesPayment = feePaymentManager.restrictSessionFeesPayment(instituteId, academicSessionId, studentId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(isRestrictSessionFeesPayment).build();

	}
}