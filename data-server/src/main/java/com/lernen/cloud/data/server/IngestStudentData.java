//package com.lernen.cloud.data.server;
//
//import javax.ws.rs.GET;
//import javax.ws.rs.Path;
//import javax.ws.rs.PathParam;
//import javax.ws.rs.Produces;
//import javax.ws.rs.core.MediaType;
//import javax.ws.rs.core.Response;
//
//import com.lernen.cloud.dev.tools.ingest.student.IngestBalajiConventStudents;
//import com.lernen.cloud.dev.tools.ingest.student.IngestRaghunathStudents;
//import com.lernen.cloud.dev.tools.ingest.student.IngestSesomuStudents;
//import com.lernen.cloud.dev.tools.ingest.student.IngestVidhyasthaliStudents;
//
//@Path("/2.0/student/ingest")
//public class IngestStudentData {
//	private final IngestVidhyasthaliStudents ingestVidhyasthaliStudents;
//	private final IngestSesomuStudents ingestSesomuStudents;
//	private final IngestBalajiConventStudents ingestBalajiConventStudents;
//	private final IngestRaghunathStudents ingestRaghunathStudents;
//
//	public IngestStudentData(IngestVidhyasthaliStudents ingestVidhyasthaliStudents,
//			IngestSesomuStudents ingestSesomuStudents, IngestBalajiConventStudents ingestBalajiConventStudents, IngestRaghunathStudents ingestRaghunathStudents) {
//		this.ingestVidhyasthaliStudents = ingestVidhyasthaliStudents;
//		this.ingestSesomuStudents = ingestSesomuStudents;
//		this.ingestBalajiConventStudents = ingestBalajiConventStudents;
//		this.ingestRaghunathStudents = ingestRaghunathStudents;
//	}
//
//	@GET
//	@Path("/10005/{session}")
//	@Produces(MediaType.APPLICATION_JSON)
//	public Response ingest(@PathParam("session") int academicSession) {
//		System.out.println("Starting student ingestion for 10005 session " + academicSession);
//		ingestVidhyasthaliStudents.ingestStudents("/tmp/10005_student_data.csv", academicSession);
//		return Response.ok().build();
//	}
//
//	@GET
//	@Path("10001/{session}")
//	@Produces(MediaType.APPLICATION_JSON)
//	public Response ingest10001(@PathParam("session") int academicSession) {
//		System.out.println("Starting student ingestion for 10001 session " + academicSession);
//		ingestSesomuStudents.ingestStudents("/tmp/10001_student_data.csv", academicSession);
//		return Response.ok().build();
//	}
//
//	@GET
//	@Path("10010/{session}")
//	@Produces(MediaType.APPLICATION_JSON)
//	public Response ingest10010(@PathParam("session") int academicSession) {
//		System.out.println("Starting student ingestion for 10010 session " + academicSession);
//		ingestBalajiConventStudents.ingestStudents("/tmp/10010_student_data.csv", academicSession);
//		return Response.ok().build();
//	}
//
//	@GET
//	@Path("10015/{session}")
//	@Produces(MediaType.APPLICATION_JSON)
//	public Response ingest10015(@PathParam("session") int academicSession) {
//		System.out.println("Starting student ingestion for 10015 session " + academicSession);
//		ingestRaghunathStudents.ingestStudents("/tmp/10015_student_data.csv", academicSession);
//		return Response.ok().build();
//	}
//
//}
