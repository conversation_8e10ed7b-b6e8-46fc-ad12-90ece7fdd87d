/**
 * 
 */
package com.embrate.cloud.data.server.notification;

import com.embrate.cloud.voicecall.report.handler.VoiceStatusUpdateHandler;
import com.lernen.cloud.core.api.common.DeliveryMode;
import com.lernen.cloud.core.api.common.SearchResultWithPagination;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.notification.NotificationBatchDetails;
import com.lernen.cloud.core.api.notification.NotificationDetails;
import com.lernen.cloud.core.api.notification.NotificationType;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.lib.notifications.NotificationManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.sms.report.handler.SMSStatusUpdateHandler;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.*;

/**
 * <AUTHOR>
 *
 */
@Path("/2.0/notifications")
public class NotificationEndpoint {
	private static final Logger logger = LogManager.getLogger(NotificationEndpoint.class);

	private final NotificationManager notificationManager;
	private final SMSStatusUpdateHandler smsStatusUpdateHandler;
	private final VoiceStatusUpdateHandler voiceStatusUpdateHandler;

	public NotificationEndpoint(NotificationManager notificationManager, SMSStatusUpdateHandler smsStatusUpdateHandler, VoiceStatusUpdateHandler voiceStatusUpdateHandler) {
		this.notificationManager = notificationManager;
		this.smsStatusUpdateHandler = smsStatusUpdateHandler;
		this.voiceStatusUpdateHandler = voiceStatusUpdateHandler;
	}

	@GET
	@Path("batch-notification-history/{user_type}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getBatchNotificationHistory(@PathParam("user_type") UserType userType,
												@QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId, @QueryParam("delivery_mode") DeliveryMode deliveryMode,
												@QueryParam("offset") int offset, @QueryParam("limit") int limit) {

		final SearchResultWithPagination<NotificationBatchDetails<NotificationDetails>> notificationHistory = notificationManager
				.getBatchNotificationHistory(instituteId, academicSessionId,
						new HashSet<>(Arrays.asList(NotificationType.CUSTOM, NotificationType.RENEW_CREDENTIALS,
								NotificationType.STUDENT_BIRTHDAY, NotificationType.STUDENT_EARLY_DEPARTURE,
								NotificationType.STUDENT_ADMISSION, NotificationType.STUDENT_REGISTRATION,
								NotificationType.STUDENT_ATTENDANCE_STATUS, NotificationType.ADMIN_STAFF_ATTENDANCE)),
						deliveryMode, userType, offset, limit);

		return Response.status(Response.Status.OK.getStatusCode()).entity(notificationHistory).build();
	}

	@GET
	@Path("individual-notification-history/{user_type}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getIndividualNotificationHistory(@PathParam("user_type") UserType userType,
													 @QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
													 @QueryParam("delivery_mode") DeliveryMode deliveryMode,
													 @QueryParam("offset") int offset, @QueryParam("limit") int limit) {

		final SearchResultWithPagination<NotificationDetails> notificationHistory = notificationManager
				.getIndividualNotificationHistory(instituteId, academicSessionId,
						new HashSet<>(Arrays.asList(NotificationType.CUSTOM, NotificationType.RENEW_CREDENTIALS,
								NotificationType.STUDENT_BIRTHDAY, NotificationType.STUDENT_EARLY_DEPARTURE,
								NotificationType.STUDENT_ADMISSION, NotificationType.STUDENT_REGISTRATION,
								NotificationType.STUDENT_ATTENDANCE_STATUS, NotificationType.ADMIN_STAFF_ATTENDANCE)), deliveryMode, userType, offset,
						limit);

		return Response.status(Response.Status.OK.getStatusCode()).entity(notificationHistory).build();
	}

	@GET
	@Path("batch-notification-detail/{user_type}/{batch_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getBatchNotificationDetails(@PathParam("user_type") UserType userType,
												@PathParam("batch_id") UUID batchId, @QueryParam("institute_id") int instituteId) {

		final List<NotificationDetails> notificationDetails = notificationManager
				.getBatchNotificationDetails(instituteId, batchId, userType);
		return Response.status(Response.Status.OK.getStatusCode()).entity(notificationDetails).build();
	}

	/**
	 * Cron : dev-tools/src/main/java/com/embrate/cloud/dev/tools/communication/handler/CommunicationStatusUpdater.java
	 *
	 * @param pastLookupDays
	 * @return
	 */
	@POST
	@Path("update-status")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateNotificationStatus(@QueryParam("past_lookup_days") int pastLookupDays) {

		if(pastLookupDays <= 0){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "invalid past lookup days"));
		}

		logger.info("Running smsStatusUpdateHandler for past days {}, now at {}", pastLookupDays, DateUtils.now());
		smsStatusUpdateHandler.updateSMSStatusAsync(pastLookupDays);

		logger.info("Running voiceStatusUpdateHandler past days {}, now at {}", pastLookupDays, DateUtils.now());
		voiceStatusUpdateHandler.updateVoiceCallStatusAsync(pastLookupDays);

		return Response.status(Response.Status.OK.getStatusCode()).build();

	}
}
