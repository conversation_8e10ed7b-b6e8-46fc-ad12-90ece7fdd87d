package com.embrate.cloud.data.server.attendance.service;

import com.embrate.cloud.core.api.attendance.DeviceEventResponse;
import com.embrate.cloud.core.lib.attendance.service.camsunit.CamsUnitDeviceManager;
import com.embrate.cloud.push.notifications.handler.AttendanceUpdatePushNotificationHandler;
import com.embrate.cloud.push.notifications.handler.StaffAttendancePushNotificationHandler;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.sms.handler.StaffAttendanceSMSHandler;
import com.lernen.cloud.sms.handler.StudentAttendanceSMSHandler;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

@Path("/2.0/camsunit-device")
public class CamsUnitDeviceAttendanceEndpoint {
    private static final Logger logger = LogManager.getLogger(CamsUnitDeviceAttendanceEndpoint.class);

    private final CamsUnitDeviceManager camsUnitDeviceManager;
    private final AttendanceUpdatePushNotificationHandler attendanceUpdatePushNotificationHandler;
    private final StaffAttendancePushNotificationHandler staffAttendancePushNotificationHandler;
    private final StudentAttendanceSMSHandler studentAttendanceSMSHandler;
    private final StaffAttendanceSMSHandler staffAttendanceSMSHandler;
    public CamsUnitDeviceAttendanceEndpoint(CamsUnitDeviceManager camsUnitDeviceManager,
                                            AttendanceUpdatePushNotificationHandler attendanceUpdatePushNotificationHandler,
                                            StaffAttendancePushNotificationHandler staffAttendancePushNotificationHandler,
                                            StudentAttendanceSMSHandler studentAttendanceSMSHandler,
                                            StaffAttendanceSMSHandler staffAttendanceSMSHandler) {
        this.camsUnitDeviceManager = camsUnitDeviceManager;
        this.attendanceUpdatePushNotificationHandler = attendanceUpdatePushNotificationHandler;
        this.staffAttendancePushNotificationHandler = staffAttendancePushNotificationHandler;
        this.studentAttendanceSMSHandler = studentAttendanceSMSHandler;
        this.staffAttendanceSMSHandler = staffAttendanceSMSHandler;
    }


    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response handleEvent(@QueryParam("stgid") String deviceId, String payload) {
        DeviceEventResponse deviceEventResponse = camsUnitDeviceManager.handleEvent(deviceId, payload);
        if(deviceEventResponse == null){
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Invalid event from attendance device"));
        }
        logger.info("deviceEventResponse = {}", deviceEventResponse);
        if(deviceEventResponse.getDeviceAttendanceResult() != null){
            if(!deviceEventResponse.getDeviceAttendanceResult().isSuccess()){
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Invalid event from attendance device"));
            }

            try {
                if (deviceEventResponse.getDeviceAttendanceResult().isSendNotification()) {
                    if (deviceEventResponse.getDeviceAttendanceResult().getUserType() == UserType.STUDENT) {
                        attendanceUpdatePushNotificationHandler.sendAttendanceUpdateBroadcastNotificationsAsync(deviceEventResponse.getDeviceAttendanceResult().getInstituteId(),
                                deviceEventResponse.getDeviceAttendanceResult());
                    }

                    /**
                     * all the checking of whether to send notification or not is handled in attendanceUpdatePushNotificationHandler
                     */
                    else if (deviceEventResponse.getDeviceAttendanceResult().getUserType() == UserType.STAFF) {
                        staffAttendancePushNotificationHandler.sendAttendanceUpdateNotificationsAsync(deviceEventResponse.getDeviceAttendanceResult());
                        staffAttendanceSMSHandler.bulkSend(deviceEventResponse.getDeviceAttendanceResult());
                    }
                }

                if (deviceEventResponse.getDeviceAttendanceResult().isSendSMS()) {
                    if (deviceEventResponse.getDeviceAttendanceResult().getUserType() == UserType.STUDENT) {
                        studentAttendanceSMSHandler.bulkSend(deviceEventResponse.getDeviceAttendanceResult().getInstituteId(), deviceEventResponse.getDeviceAttendanceResult());
                    }
                }
            } catch (Exception e) {
                logger.warn("Unable to send notification/sms. Skipping it", e);
            }

        } else if (deviceEventResponse.getDeviceUserUpdateResult() != null) {
            if(!deviceEventResponse.getDeviceUserUpdateResult().isSuccess()){
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Invalid event from attendance device"));
            }
        }


        Map<String, String> res = new HashMap<>();
        res.put("status", "done");
        return Response.status(Response.Status.OK.getStatusCode()).entity(res).build();
    }
}
