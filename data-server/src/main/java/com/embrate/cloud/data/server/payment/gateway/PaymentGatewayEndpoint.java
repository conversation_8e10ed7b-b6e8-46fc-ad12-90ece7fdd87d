package com.embrate.cloud.data.server.payment.gateway;

import com.embrate.cloud.core.api.application.mobile.MobileAppPlatform;
import com.embrate.cloud.core.api.payment.PaymentRequest;
import com.embrate.cloud.core.api.scratchcard.ScratchCardDetails;
import com.embrate.cloud.core.api.service.payment.gateway.*;
import com.embrate.cloud.core.api.service.payment.gateway.utils.PGPlatform;
import com.embrate.cloud.core.lib.service.payment.gateway.PaymentGatewayManager;
import com.embrate.cloud.push.notifications.handler.ScratchCardBroadcastPushNotificationHandler;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import org.springframework.util.CollectionUtils;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */

@Path("/2.0/payment-gateway")
public class PaymentGatewayEndpoint {

    private final PaymentGatewayManager paymentGatewayManager;
    private final ScratchCardBroadcastPushNotificationHandler scratchCardBroadcastPushNotificationHandler;

    public PaymentGatewayEndpoint(PaymentGatewayManager paymentGatewayManager, ScratchCardBroadcastPushNotificationHandler scratchCardBroadcastPushNotificationHandler) {
        this.paymentGatewayManager = paymentGatewayManager;
        this.scratchCardBroadcastPushNotificationHandler = scratchCardBroadcastPushNotificationHandler;
    }

    @GET
    @Path("{user_id}/metadata")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getPaymentGatewayTransactionMetadata(@PathParam("user_id") UUID userId, @QueryParam("institute_id") int instituteId,  @QueryParam("wallet_amount") boolean walletAmount) {

        UserPaymentGatewayMetadata userPaymentGatewayMetadata = paymentGatewayManager.getPaymentGatewayTransactionMetadata(userId, instituteId, walletAmount, null);
        return Response.status(Response.Status.OK.getStatusCode()).entity(userPaymentGatewayMetadata).build();
    }

    @POST
    @Path("{user_id}/metadata")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getPaymentGatewayTransactionMetadataV2(@PathParam("user_id") UUID userId, @QueryParam("institute_id") int instituteId,  @QueryParam("wallet_amount") boolean walletAmount, PaymentRequest paymentRequest) {

        UserPaymentGatewayMetadata userPaymentGatewayMetadata = paymentGatewayManager.getPaymentGatewayTransactionMetadata(userId, instituteId, walletAmount, paymentRequest);
        return Response.status(Response.Status.OK.getStatusCode()).entity(userPaymentGatewayMetadata).build();
    }

    @GET
    @Path("{user_id}/transactions")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getPaymentGatewayTransactions(@PathParam("user_id") UUID userId, @QueryParam("institute_id") int instituteId,
                                                  @QueryParam("transaction_type") PGTransactionType transactionType,  @QueryParam("include_cancelled_transaction") boolean includeCancelledTransaction) {

        List<PaymentGatewayTransactionData> paymentGatewayTransactionDataList = paymentGatewayManager.getTransactions(instituteId, userId, transactionType, includeCancelledTransaction);
        if(paymentGatewayTransactionDataList == null){
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid request"));
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(paymentGatewayTransactionDataList).build();
    }

    @POST
    @Path("initiate-transaction")
    @Produces(MediaType.APPLICATION_JSON)
    public Response initiateTransaction(@QueryParam("institute_id") int instituteId, PGInitiateTransactionPayload initiateTransactionPayload,
                                        @QueryParam("mobileapp_platform") MobileAppPlatform appPlatform,
                                        @QueryParam("pg_platform") PGPlatform pgPlatformInput) {

        if (initiateTransactionPayload == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid request"));
        }

        // For backward compatibility in mobile applications. Subsequently, mobileapp_platform param shoud be removed
        PGPlatform pgPlatform = pgPlatformInput;
        if(appPlatform != null){
            switch (appPlatform){
                case IOS:
                    pgPlatform = PGPlatform.IOS;
                    break;
                case GOOGLE:
                    pgPlatform = PGPlatform.GOOGLE;
                    break;
            }
        }
        PGInitiateTransactionResponse initiateTransactionResponse = paymentGatewayManager.initiatePaymentTransaction(instituteId, initiateTransactionPayload,
                pgPlatform);

        return Response.status(Response.Status.OK.getStatusCode()).entity(initiateTransactionResponse).build();
    }

    @POST
    @Path("process-transaction")
    @Produces(MediaType.APPLICATION_JSON)
    public Response processTransaction(@QueryParam("institute_id") int instituteId, PGProcessTransactionPayload processTransactionPayload) {

        if (processTransactionPayload == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid request"));
        }

        PGProcessTransactionResponse initiateTransactionResponse = paymentGatewayManager.processTransaction(instituteId, processTransactionPayload, true);

        if(initiateTransactionResponse.getPaymentGatewayAmountTxnResponse() != null &&
                !CollectionUtils.isEmpty(initiateTransactionResponse.getPaymentGatewayAmountTxnResponse()
                        .getScratchCardDetailsList())) {
            List<UUID> scratchCardIds = new ArrayList<UUID>();
            for(ScratchCardDetails scratchCardDetails : initiateTransactionResponse.getPaymentGatewayAmountTxnResponse().getScratchCardDetailsList()) {
                scratchCardIds.add(scratchCardDetails.getScratchCardId());
            }
            scratchCardBroadcastPushNotificationHandler.sendScratchCardBroadcastPushNotificationsAsync(instituteId,
                    scratchCardIds, initiateTransactionResponse.getPaymentGatewayAmountTxnResponse()
                            .getScratchCardDetailsList().get(0).getUserId());
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(initiateTransactionResponse).build();
    }
}
