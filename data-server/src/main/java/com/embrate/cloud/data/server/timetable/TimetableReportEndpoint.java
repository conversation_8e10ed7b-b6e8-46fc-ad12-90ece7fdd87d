package com.embrate.cloud.data.server.timetable;

import com.embrate.cloud.core.api.timetable.TimetableReportType;
import com.embrate.cloud.core.lib.timetable.TimetableReportGenerator;
import com.embrate.cloud.pdf.reports.PdfReportGenerator;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.report.DownloadFormat;
import com.lernen.cloud.core.api.report.HTMLReportTable;
import com.lernen.cloud.core.api.report.ReportDetails;
import com.lernen.cloud.core.api.report.ReportOutput;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.lib.reports.ExcelReportGenerator;
import com.lernen.cloud.core.utils.report.ReportUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.time.DayOfWeek;
import java.util.UUID;

/**
 *  <AUTHOR>
 */
@Path("/2.0/timetable-reports/")
 public class TimetableReportEndpoint {

    private static final Logger logger = LogManager.getLogger(TimetableReportEndpoint.class);
    private final TimetableReportGenerator timetableReportGenerator;
    private final ExcelReportGenerator excelReportGenerator;
    private final PdfReportGenerator pdfReportGenerator;

    public TimetableReportEndpoint(TimetableReportGenerator timetableReportGenerator, ExcelReportGenerator excelReportGenerator, PdfReportGenerator pdfReportGenerator) {
        this.timetableReportGenerator = timetableReportGenerator;
        this.excelReportGenerator = excelReportGenerator;
        this.pdfReportGenerator = pdfReportGenerator;
    }

    @GET
    @Path("{institute_id}/view-report/{report_type}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response generateReportData(@PathParam("institute_id") int instituteId,
                                       @PathParam("report_type") TimetableReportType timetableReportType,
                                       @QueryParam("academic_session_id") int academicSessionId,
                                       @QueryParam("shift_id") UUID shiftId,
                                       @QueryParam("day") DayOfWeek day,
                                       @QueryParam("standard_ids") String requiredStandardsCSV,
                                       @QueryParam("show_classes_with_no_timetable") boolean showClassesWithNoTimetableDetails,
                                       @QueryParam("user_id") UUID userId) {

        final ReportDetails reportDetails = timetableReportGenerator.generateReport(instituteId, academicSessionId,
                timetableReportType, shiftId, day, requiredStandardsCSV, showClassesWithNoTimetableDetails, userId, null);

        if (reportDetails == null) {
            logger.error("Unable to render report");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to render report"));
        }

        HTMLReportTable htmlReportTable = ReportUtils.convertToHTMLTable(reportDetails);

        return Response.status(Response.Status.OK.getStatusCode()).entity(htmlReportTable).build();
    }

    @GET
    @Path("{institute_id}/generate-report/{report_type}")
    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    public Response generateReport(@PathParam("institute_id") int instituteId,
                                   @PathParam("report_type") TimetableReportType timetableReportType,
                                   @QueryParam("academic_session_id") int academicSessionId,
                                   @QueryParam("shift_id") UUID shiftId,
                                   @QueryParam("day") DayOfWeek day,
                                   @QueryParam("standard_ids") String requiredStandardsCSV,
                                   @QueryParam("show_classes_with_no_timetable") boolean showClassesWithNoTimetableDetails,
                                   @QueryParam("user_id") UUID userId,
                                   @QueryParam("download_format") DownloadFormat downloadFormat) throws IOException {

        final ReportDetails reportDetails = timetableReportGenerator.generateReport(instituteId, academicSessionId,
                timetableReportType, shiftId, day, requiredStandardsCSV, showClassesWithNoTimetableDetails, userId, downloadFormat);
        if(downloadFormat == DownloadFormat.PDF) {
            final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails, Module.TIMETABLE_MANAGEMENT);
            if (documentOutput == null) {
                return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
            }
            return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
                    .header("Content-Disposition", "filename=" + documentOutput.getName())
                    .entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
        } else {
            final ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
            if (reportOutput == null) {
                return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
            }
            return Response.status(Response.Status.OK.getStatusCode())
                    .header("Content-Disposition", "attachment;filename=" + reportOutput.getReportName())
                    .entity(new ByteArrayInputStream(reportOutput.getReportContent().toByteArray())).build();
        }
    }
}
