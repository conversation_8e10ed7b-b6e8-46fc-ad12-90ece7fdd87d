package com.embrate.cloud.data.server.dashboards.attendance;

import com.embrate.cloud.core.api.dashboards.attendance.StaffSummaryWithAttendanceOrgStats;
import com.embrate.cloud.core.api.dashboards.attendance.StudentSummaryWithAttendanceOrgStats;
import com.embrate.cloud.core.lib.dashboards.attendance.AttendanceDashboardManager;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.UUID;

/**
 * <AUTHOR>
 **/
@Path("2.1/dashboards/attendance")
public class AttendanceDashboardEndpoint {

	private final AttendanceDashboardManager attendanceDashboardManager;

	public AttendanceDashboardEndpoint(AttendanceDashboardManager attendanceDashboardManager) {
		this.attendanceDashboardManager = attendanceDashboardManager;
	}

	@GET
	@Path("organisation/{organisation_id}/students")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStudentAttendanceOrganisationStats(@PathParam("organisation_id") UUID organisationId,
														  @QueryParam("institute_ids") String selectedInstituteIds,
														  @QueryParam("user_id") UUID userId, @QueryParam("date") int date) {
		final StudentSummaryWithAttendanceOrgStats attendanceOrgStats = attendanceDashboardManager.getStudentSummaryWithAttendanceOrgStats(organisationId, selectedInstituteIds, userId, date);
		return Response.status(Response.Status.OK.getStatusCode()).entity(attendanceOrgStats).build();
	}

	@GET
	@Path("organisation/{organisation_id}/staff")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getStaffAttendanceOrganisationStats(@PathParam("organisation_id") UUID organisationId,
														@QueryParam("institute_ids") String selectedInstituteIds,
														@QueryParam("user_id") UUID userId, @QueryParam("date") int date) {
		final StaffSummaryWithAttendanceOrgStats attendanceOrgStats = attendanceDashboardManager.getStaffSummaryWithAttendanceOrgStats(organisationId, selectedInstituteIds, userId, date);
		return Response.status(Response.Status.OK.getStatusCode()).entity(attendanceOrgStats).build();
	}
}
