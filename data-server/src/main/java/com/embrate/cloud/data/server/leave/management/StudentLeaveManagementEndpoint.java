package com.embrate.cloud.data.server.leave.management;

import com.embrate.cloud.core.api.leave.management.LeaveDocumentType;
import com.embrate.cloud.core.api.leave.management.student.StudentLeaveAttendanceErrorPayload;
import com.embrate.cloud.core.api.leave.management.student.StudentLeaveDetails;
import com.embrate.cloud.core.api.leave.management.student.StudentLeavePayload;
import com.embrate.cloud.core.api.leave.management.transaction.LeaveTransactionStatus;
import com.embrate.cloud.core.api.leave.management.user.LeaveReviewPayload;
import com.embrate.cloud.core.lib.leave.management.StudentLeaveManagementManager;
import com.embrate.cloud.push.notifications.handler.StudentLeaveReviewPushNotificationHandler;
import com.embrate.cloud.push.notifications.handler.StudentLeaveApplicationPushNotificationHandler;
import com.lernen.cloud.core.api.common.FileData;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.student.DownloadDocumentWrapper;
import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.data.server.RestFormDataHandler;
import com.sun.jersey.multipart.FormDataBodyPart;
import com.sun.jersey.multipart.FormDataParam;
import org.springframework.util.CollectionUtils;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.ByteArrayInputStream;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import static com.lernen.cloud.core.utils.SharedConstants.GSON;

/**
 * <AUTHOR>
 * @created_at 01/01/24 : 14:40
 **/

@Path("2.0/student-leave")
public class StudentLeaveManagementEndpoint {

    private final StudentLeaveManagementManager studentLeaveManagementManager;
    private final StudentLeaveReviewPushNotificationHandler studentLeaveReviewPushNotificationHandler;
    private final StudentLeaveApplicationPushNotificationHandler studentLeaveApplicationPushNotificationHandler;
    public StudentLeaveManagementEndpoint(StudentLeaveManagementManager studentLeaveManagementManager,
                                          StudentLeaveReviewPushNotificationHandler studentLeaveReviewPushNotificationHandler, StudentLeaveApplicationPushNotificationHandler studentLeaveApplicationPushNotificationHandler) {
        this.studentLeaveManagementManager = studentLeaveManagementManager;
        this.studentLeaveReviewPushNotificationHandler = studentLeaveReviewPushNotificationHandler;
        this.studentLeaveApplicationPushNotificationHandler = studentLeaveApplicationPushNotificationHandler;
    }

    @GET
    @Path("leaves/{student_id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getStudentLeaves(@PathParam("student_id") UUID studentId,
                                     @QueryParam("institute_id") int instituteId,
                                     @QueryParam("academic_session_id") int academicSessionId) {
        List<StudentLeaveDetails> studentLeaveDetailsList = studentLeaveManagementManager.getStudentLeaves(instituteId, academicSessionId, studentId);
        return Response.status(Response.Status.OK.getStatusCode()).entity(studentLeaveDetailsList).build();
    }

    @POST
    @Path("{student_id}")
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Produces(MediaType.APPLICATION_JSON)
    public Response addStudentLeaves(@FormDataParam("student_leave_payload") String studentLeavePayloadJson,
                                     @FormDataParam("file") List<FormDataBodyPart> bodyParts,
                                     @QueryParam("institute_id") int instituteId, @PathParam("student_id") UUID studentId) {

        final List<FileData> attachments = RestFormDataHandler.getFileDataFromFormDataBodyPart(bodyParts);
        final StudentLeavePayload studentLeavePayload = GSON.fromJson(studentLeavePayloadJson, StudentLeavePayload.class);
        Set<UUID> transactionIdSet = studentLeaveManagementManager.addStudentLeaves(instituteId, studentLeavePayload, attachments);
        if (CollectionUtils.isEmpty(transactionIdSet)) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to add student leaves"));
        }
        studentLeaveApplicationPushNotificationHandler.sendStudentLeaveApplicationPushNotificationsAsync(instituteId, transactionIdSet, studentLeavePayload.getAcademicSessionId());
        return Response.status(Response.Status.OK.getStatusCode()).build();

    }

    @DELETE
    @Path("transaction/{transaction_id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response deleteStudentLeaves(@PathParam("transaction_id") UUID transactionId, @QueryParam("institute_id") int instituteId,
                                        @QueryParam("academic_session_id") int academicSessionId) {

        final boolean success = studentLeaveManagementManager.deleteStudentLeaves(instituteId, academicSessionId, transactionId);
        if (!success) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to delete leave type"));
        }
        return Response.status(Response.Status.OK.getStatusCode()).build();
    }

    @GET
    @Path("/download-leave-document/{transaction_id}/{document_id}")
    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    public Response downloadLeaveDocument(@PathParam("document_id") UUID documentId, @PathParam("transaction_id") UUID transactionId,
                                          @QueryParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId) {
        final DownloadDocumentWrapper<Document<LeaveDocumentType>> documentWrapper = studentLeaveManagementManager
                .downloadStudentLeaveDocument(instituteId, academicSessionId, documentId, transactionId);
        if (documentWrapper == null || documentWrapper.getDocumentContent() == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Unable to download file"));
        }
        return Response.status(Response.Status.OK.getStatusCode())
                .header("Content-Disposition",
                        "attachment;filename=" + documentWrapper.getDocumentDetails().getDocumentName() + "."
                                + documentWrapper.getDocumentDetails().getFileExtension())
                .entity(new ByteArrayInputStream(documentWrapper.getDocumentContent().toByteArray())).build();
    }

    @GET
    @Path("{institute_id}/leaves/{academic_session_id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getAllLeaves(@PathParam("institute_id") int instituteId,
                                     @PathParam("academic_session_id") int academicSessionId, @QueryParam("user_id") UUID userId) {
        List<StudentLeaveDetails> studentLeaveDetailsList = studentLeaveManagementManager.getAllLeaves(instituteId, academicSessionId, userId);
        return Response.status(Response.Status.OK.getStatusCode()).entity(studentLeaveDetailsList).build();
    }

    @GET
    @Path("{institute_id}/leaves-by-status/{academic_session_id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getAllLeavesByStatus(@PathParam("institute_id") int instituteId,
                                 @PathParam("academic_session_id") int academicSessionId, @QueryParam("user_id") UUID userId) {
        Map<LeaveTransactionStatus, List<StudentLeaveDetails>> leaveTransactionStatusStudentLeaveDetailsListMap = studentLeaveManagementManager.getAllLeavesByStatus(instituteId, academicSessionId, userId);
        return Response.status(Response.Status.OK.getStatusCode()).entity(leaveTransactionStatusStudentLeaveDetailsListMap).build();
    }

    /**
     * 'updateAttendance' flag is for updating attendance status when leave is approved for
     * a transaction and user agrees to overwrite the existing marked attendances.
     * If no attendance is marked, we can directly update the status of attendance,
     * this flag is only for case when attendance is marked, and we are applying leave
     */
    @PUT
    @Path("review-leave")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response updateLeaveReviewDetails(@QueryParam("institute_id") int instituteId, @QueryParam("user_id") UUID userId,
        LeaveReviewPayload leaveReviewPayload, @QueryParam("update_attendance") boolean updateAttendance) {
        final boolean leaveReviewDetailsUpdated = studentLeaveManagementManager.updateLeaveReviewDetails(instituteId, userId, updateAttendance, leaveReviewPayload);
        if (!leaveReviewDetailsUpdated) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_HOMEWORK_DETAILS, "Unable to update leave review details."));
        }

        if(leaveReviewPayload.getTransactionStatus() == LeaveTransactionStatus.APPROVED
            ||leaveReviewPayload.getTransactionStatus() == LeaveTransactionStatus.REJECTED) {
            studentLeaveReviewPushNotificationHandler.sendStudentLeaveReviewedNotificationsAsync(instituteId,
                    leaveReviewPayload.getAcademicSessionId(),
                    leaveReviewPayload);
        }

        return Response.status(Response.Status.OK.getStatusCode()).build();
    }

    @GET
    @Path("error-reason")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response getStudentLeaveAttendanceErrorReason(@QueryParam("institute_id") int instituteId,
            @QueryParam("transaction_id") UUID transactionId, @QueryParam("academic_session_id") int academicSessionId) {
        final StudentLeaveAttendanceErrorPayload studentLeaveAttendanceErrorPayload = studentLeaveManagementManager.getStudentLeaveAttendanceErrorReason(instituteId, academicSessionId, transactionId);
        return Response.status(Response.Status.OK.getStatusCode()).entity(studentLeaveAttendanceErrorPayload).build();
    }

    @GET
    @Path("{institute_id}/transaction/{transaction_id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getLeaveByTransactionId(@PathParam("institute_id") int instituteId, @QueryParam("academic_session_id") int academicSessionId,
                                 @PathParam("transaction_id") UUID transactionId) {
        StudentLeaveDetails studentLeaveDetails = studentLeaveManagementManager.getLeaveByTransactionId(instituteId,
                academicSessionId, transactionId);
        return Response.status(Response.Status.OK.getStatusCode()).entity(studentLeaveDetails).build();
    }
}