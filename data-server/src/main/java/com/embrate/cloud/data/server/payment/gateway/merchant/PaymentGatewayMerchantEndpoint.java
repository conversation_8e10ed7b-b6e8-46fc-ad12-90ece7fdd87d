package com.embrate.cloud.data.server.payment.gateway.merchant;

import com.embrate.cloud.core.api.service.payment.gateway.PGInitiateTransactionPayload;
import com.embrate.cloud.core.api.service.payment.gateway.PGInitiateTransactionResponse;
import com.embrate.cloud.core.api.service.payment.gateway.PaymentGatewayServiceProvider;
import com.embrate.cloud.core.api.service.payment.gateway.UserPaymentGatewayMetadata;
import com.embrate.cloud.core.api.service.payment.gateway.merchants.PGMerchantDetails;
import com.embrate.cloud.core.lib.service.payment.gateway.PaymentGatewayManager;
import com.embrate.cloud.core.lib.service.payment.gateway.merchant.PaymentGatewayMerchantManager;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 */

@Path("/2.0/payment-gateway-merchant")
public class PaymentGatewayMerchantEndpoint {

    private final PaymentGatewayMerchantManager paymentGatewayMerchantManager;

    public PaymentGatewayMerchantEndpoint(PaymentGatewayMerchantManager paymentGatewayMerchantManager) {
        this.paymentGatewayMerchantManager = paymentGatewayMerchantManager;
    }

    @GET
    @Produces(MediaType.APPLICATION_JSON)
    public Response getActiveMerchants(@QueryParam("institute_id") int instituteId,  @QueryParam("payment_gateway") PaymentGatewayServiceProvider paymentGatewayServiceProvider) {
        List<PGMerchantDetails> merchantDetailsList = paymentGatewayMerchantManager.getAllActiveMerchants(instituteId, paymentGatewayServiceProvider);
        return Response.status(Response.Status.OK.getStatusCode()).entity(merchantDetailsList).build();
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    public Response addMerchant(@QueryParam("institute_id") int instituteId, PGMerchantDetails merchantDetails) {
        if (merchantDetails == null || merchantDetails.getInstituteId() != instituteId) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid request"));
        }

        UUID merchantId = paymentGatewayMerchantManager.addMerchant(merchantDetails);
        if(merchantId == null){
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Unable to create the merchant"));
        }
        Map<String, String> res = new HashMap<>();
        res.put("merchantId", merchantId.toString());
        return Response.status(Response.Status.OK.getStatusCode()).entity(res).build();
    }
}
