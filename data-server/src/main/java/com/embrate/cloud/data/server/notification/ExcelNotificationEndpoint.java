package com.embrate.cloud.data.server.notification;

import com.embrate.cloud.voicecall.handler.ExcelVoiceCallHandler;
import com.lernen.cloud.core.api.common.FileData;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.notification.ExcelFileValidationDetails;
import com.lernen.cloud.core.api.notification.ExcelNotificationPayload;
import com.lernen.cloud.core.api.report.ReportOutput;
import com.lernen.cloud.core.api.sms.SMSResponse;
import com.lernen.cloud.core.api.voicecall.VoiceCallResponse;
import com.lernen.cloud.core.lib.notifications.excel.ExcelNotificationManager;
import com.lernen.cloud.data.server.RestFormDataHandler;
import com.lernen.cloud.sms.handler.ExcelSMSHandler;
import com.sun.jersey.multipart.FormDataBodyPart;
import com.sun.jersey.multipart.FormDataParam;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.ByteArrayInputStream;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Path("/2.0/excel-notifications")
public class ExcelNotificationEndpoint {

    private static final Logger logger = LogManager.getLogger(ExcelNotificationEndpoint.class);

    private final ExcelNotificationManager excelNotificationManager;

    private final ExcelSMSHandler excelSMSHandler;

    private final ExcelVoiceCallHandler excelVoiceCallHandler;


    public ExcelNotificationEndpoint(ExcelNotificationManager excelNotificationManager,
                                     ExcelSMSHandler excelSMSHandler, ExcelVoiceCallHandler excelVoiceCallHandler) {
        this.excelNotificationManager = excelNotificationManager;
        this.excelSMSHandler = excelSMSHandler;
        this.excelVoiceCallHandler = excelVoiceCallHandler;
    }


    @GET
    @Path("/download-excel-template/{institute_id}/{template_id}")
    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    public Response downloadExcelTemplate(@PathParam("institute_id") int instituteId,
                                          @PathParam("template_id") UUID templateId) {
        final ReportOutput reportOutput = excelNotificationManager.downloadExcelTemplate(instituteId, templateId);
        if (reportOutput == null) {
            return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
        }
        return Response.status(Response.Status.OK.getStatusCode())
                .header("Content-Disposition", "attachment;filename=" + reportOutput.getReportName())
                .entity(new ByteArrayInputStream(reportOutput.getReportContent().toByteArray())).build();
    }

    @POST
    @Path("upload-validate-template/{institute_id}/{template_id}")
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Produces(MediaType.APPLICATION_JSON)
    public Response uploadValidateTemplate(@PathParam("institute_id") int instituteId,
                                          @PathParam("template_id") UUID templateId,
                                          @FormDataParam("file") List<FormDataBodyPart> bodyParts) {

        final List<FileData> files = RestFormDataHandler.getFileDataFromFormDataBodyPart(bodyParts);
        if (CollectionUtils.isEmpty(files)) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "No file attached for upload."));
        }
        final ExcelFileValidationDetails excelFileValidationDetails = excelNotificationManager.uploadValidateTemplate(instituteId, templateId, files.get(0));
        if (excelFileValidationDetails == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to upload & validate template."));
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(excelFileValidationDetails).build();
    }

    @POST
    @Path("send-notification")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response sendExcelSMS(@QueryParam("institute_id") int instituteId,
                                 @QueryParam("user_id") UUID userId, ExcelNotificationPayload excelNotificationPayload) {
        if (excelNotificationPayload == null) {
            return Response.status(Response.Status.NOT_ACCEPTABLE.getStatusCode()).build();
        }
        switch(excelNotificationPayload.getDeliveryMode()) {
            case SMS:
                final SMSResponse smsResponse = excelSMSHandler.bulkSend(instituteId, userId, excelNotificationPayload);
                if(smsResponse == null){
                    throw new ApplicationException(
                            new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to send sms."));
                }
                return Response.status(Response.Status.OK.getStatusCode()).entity(smsResponse).build();
            case CALL:
                VoiceCallResponse voiceCallResponse = excelVoiceCallHandler.bulkSend(instituteId, userId, excelNotificationPayload);
                if(voiceCallResponse == null){
                    throw new ApplicationException(
                            new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to send voice call."));
                }
                return Response.status(Response.Status.OK.getStatusCode()).entity(voiceCallResponse).build();
            default:
                return Response.status(Response.Status.NOT_ACCEPTABLE.getStatusCode()).build();
        }
    }
}
