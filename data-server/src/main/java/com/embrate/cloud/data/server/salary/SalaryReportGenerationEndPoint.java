package com.embrate.cloud.data.server.salary;

import com.embrate.cloud.core.api.salary.SalaryReportType;
import com.embrate.cloud.core.lib.salary.SalaryReportGenerationManager;
import com.lernen.cloud.core.api.report.ReportOutput;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.ByteArrayInputStream;
import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
@Path("/2.0/salary/reports")
public class SalaryReportGenerationEndPoint {

	private SalaryReportGenerationManager salaryReportGenerationManager;

	public SalaryReportGenerationEndPoint(SalaryReportGenerationManager salaryReportGenerationManager) {
		this.salaryReportGenerationManager = salaryReportGenerationManager;
	}

	@GET
	@Path("{institute_id}/generate-report/{report_type}")
	@Produces(MediaType.APPLICATION_OCTET_STREAM)
	public Response generateReport(@PathParam("institute_id") int instituteId,
			@PathParam("report_type") SalaryReportType reportType,
			@QueryParam("cycle") Integer cycle,
			@QueryParam("user_id") UUID userId) {
		ReportOutput reportOutput = salaryReportGenerationManager.
				generateReport(instituteId, reportType, cycle, userId);
		if (reportOutput == null) {
			return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
		}
		return Response.status(Response.Status.OK.getStatusCode())
				.header("Content-Disposition", "attachment;filename=" + reportOutput.getReportName())
				.entity(new ByteArrayInputStream(reportOutput.getReportContent().toByteArray())).build();
	}
}