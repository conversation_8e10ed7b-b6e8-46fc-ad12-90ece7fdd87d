package com.embrate.cloud.data.server.inventory.v2;

import com.embrate.cloud.core.lib.inventory.v2.InventoryReportGenerationManager;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.report.*;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.lib.reports.ExcelReportGenerator;
import com.embrate.cloud.pdf.reports.PdfReportGenerator;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.utils.report.ReportUtils;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
@Path("/2.1/inventory/reports")
public class InventoryReportGenerationEndPoint {

	private InventoryReportGenerationManager inventoryReportGenerationManager;

	private final PdfReportGenerator pdfReportGenerator;

	private final ExcelReportGenerator excelReportGenerator;

	public InventoryReportGenerationEndPoint(InventoryReportGenerationManager inventoryReportGenerationManager,
			PdfReportGenerator pdfReportGenerator, ExcelReportGenerator excelReportGenerator) {
		this.inventoryReportGenerationManager = inventoryReportGenerationManager;
		this.pdfReportGenerator = pdfReportGenerator;
		this.excelReportGenerator = excelReportGenerator;
	}

	@GET
	@Path("{institute_id}/view-report/{report_type}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getReport(@PathParam("institute_id") int instituteId,
							  @PathParam("report_type") String reportType, @QueryParam("user_id") UUID userId,
							  @QueryParam("start") int start, @QueryParam("end") int end,
							  @QueryParam("download_format") DownloadFormat downloadFormat) {

		final ReportDetails reportDetails = inventoryReportGenerationManager.generateReport(instituteId,
				ReportType.valueOf(reportType), userId, start, end, downloadFormat);

		if(reportDetails == null){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to generate report"));
		}

		HTMLReportTable htmlReportTable = ReportUtils.convertToHTMLTable(reportDetails);
		return Response.status(Response.Status.OK.getStatusCode()).entity(htmlReportTable).build();

	}

	@GET
	@Path("{institute_id}/generate-report/{report_type}")
	@Produces(MediaType.APPLICATION_OCTET_STREAM)
	public Response generateReport(@PathParam("institute_id") int instituteId,
			@PathParam("report_type") String reportType, @QueryParam("user_id") UUID userId,
			@QueryParam("start") int start, @QueryParam("end") int end,
			@QueryParam("download_format") DownloadFormat downloadFormat) throws IOException {
		
		final ReportDetails reportDetails = inventoryReportGenerationManager.generateReport(instituteId,
				ReportType.valueOf(reportType), userId, start, end, downloadFormat);

		if (downloadFormat == DownloadFormat.PDF) {
			final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails, Module.STORE);
			if (documentOutput == null) {
				return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
			}
			return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
					.header("Content-Disposition", "filename=" + documentOutput.getName())
					.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
		} else {
			final ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
			if (reportOutput == null) {
				return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
			}
			return Response.status(Response.Status.OK.getStatusCode())
					.header("Content-Disposition", "attachment;filename=" + reportOutput.getReportName())
					.entity(new ByteArrayInputStream(reportOutput.getReportContent().toByteArray())).build();
		}
	}
}