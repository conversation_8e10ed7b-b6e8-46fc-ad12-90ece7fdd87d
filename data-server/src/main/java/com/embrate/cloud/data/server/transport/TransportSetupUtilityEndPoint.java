package com.embrate.cloud.data.server.transport;

import com.embrate.cloud.core.lib.transport.TransportSetupUtilityManager;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.transport.CloneTransportServiceRoutes;
import com.lernen.cloud.core.api.transport.setup.CloneAreaStoppagesRequest;
import com.lernen.cloud.core.api.transport.setup.CloneAreaStoppagesResponse;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

/**
 * <AUTHOR>
 * @created_at 19/04/24 : 10:56
 **/
@Path("/2.0/transport-setup")
public class TransportSetupUtilityEndPoint {
    private final TransportSetupUtilityManager transportSetupUtilityManager;

    public TransportSetupUtilityEndPoint(TransportSetupUtilityManager transportSetupUtilityManager) {
        this.transportSetupUtilityManager = transportSetupUtilityManager;
    }

    @POST
    @Path("area-vehicle-amounts/clone/session")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response cloneTransportAreaVehicleAmountsAcrossSession(@QueryParam("src_institute_id") int srcInstituteId, @QueryParam("dest_institute_id") int destInstituteId,
                                                      @QueryParam("src_academic_session_id") int srcAcademicSessionId,
                                                      @QueryParam("dest_academic_session_id") int destAcademicSessionId) {
        final boolean status = transportSetupUtilityManager.cloneTransportAreaVehicleAmountsAcrossSession(srcInstituteId, destInstituteId, srcAcademicSessionId, destAcademicSessionId);
        if (!status) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Unable to clone transport routes across sessions."));
        }
        return Response.status(Response.Status.OK.getStatusCode()).build();
    }

    @POST
    @Path("transport-routes/clone/session")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response cloneTransportRoutesAcrossSession(CloneTransportServiceRoutes cloneTransportServiceRoutes) {
        final boolean status = transportSetupUtilityManager.cloneTransportRoutesAcrossSession(cloneTransportServiceRoutes);
        if (!status) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Unable to clone transport routes across sessions."));
        }
        return Response.status(Response.Status.OK.getStatusCode()).build();
    }

    @POST
    @Path("clone-area-stoppages")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response cloneAreaAndStoppages(CloneAreaStoppagesRequest cloneManageStoppagesRequest) {
        CloneAreaStoppagesResponse cloneManageStoppageResponse = transportSetupUtilityManager.cloneManageStoppages(cloneManageStoppagesRequest);
        if (cloneManageStoppageResponse == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to clone class courses."));
        }
        return Response.status(Response.Status.OK.getStatusCode()).entity(cloneManageStoppageResponse).build();
    }
}
