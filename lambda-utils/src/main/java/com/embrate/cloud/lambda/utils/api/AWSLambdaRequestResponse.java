package com.embrate.cloud.lambda.utils.api;

import org.springframework.context.ApplicationContext;

import java.io.ByteArrayOutputStream;

/**
 * <AUTHOR>
 */

public class AWSLambdaRequestResponse {

    private final boolean success;

    private final String fileName;

    private final ByteArrayOutputStream content;

    private final ApplicationContext applicationContext;

    public AWSLambdaRequestResponse(boolean success, String fileName, ByteArrayOutputStream content, ApplicationContext applicationContext) {
        this.success = success;
        this.fileName = fileName;
        this.content = content;
        this.applicationContext = applicationContext;
    }

    public boolean isSuccess() {
        return success;
    }

    public String getFileName() {
        return fileName;
    }

    public ByteArrayOutputStream getContent() {
        return content;
    }

    public ApplicationContext getApplicationContext() {
        return applicationContext;
    }
}
