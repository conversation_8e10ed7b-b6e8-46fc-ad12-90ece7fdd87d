package com.embrate.cloud.lambda.utils.handler;

import com.embrate.cloud.lambda.utils.handler.impl.*;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

public class AWSLambdaRequestHandlerRegistry {

    private final Map<String, AWSLambdaRequestHandler> requestHandlerMap = new HashMap<>();

    public AWSLambdaRequestHandlerRegistry() {
        init();
    }

    private void init() {
        register(new StudentIdentityCardsRequestHandler());
        register(new StudentAdmitCardsRequestHandler());
        register(new StudentReportCardsRequestHandler());
        register(new ExamGreenSheetRequestHandler());
        register(new StudentHPCReportCardsRequestHandler());
    }

    private void register(AWSLambdaRequestHandler requestHandler) {
        requestHandlerMap.put(requestHandler.getId(), requestHandler);
    }

    public AWSLambdaRequestHandler get(String handlerId) {
        return requestHandlerMap.get(handlerId);
    }
}
