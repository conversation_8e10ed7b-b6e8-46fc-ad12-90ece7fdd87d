package com.embrate.cloud.voicecall.report.handler;

import com.embrate.cloud.voicecall.service.DatagenVoiceCallService;
import com.lernen.cloud.core.api.common.CommunicationServiceProvider;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 
 * <AUTHOR>
 *
 */
public class VoiceStatusUpdaterServiceFactory {

	private static final Logger logger = LogManager.getLogger(VoiceStatusUpdaterServiceFactory.class);

	private final DatagenVoiceStatusUpdaterService datagenVoiceStatusUpdaterService;

	public VoiceStatusUpdaterServiceFactory(DatagenVoiceStatusUpdaterService datagenVoiceStatusUpdaterService) {
		this.datagenVoiceStatusUpdaterService = datagenVoiceStatusUpdaterService;
	}

	public IVoiceStatusUpdaterService getVoiceStatusUpdaterService(
			CommunicationServiceProvider communicationServiceProvider) {
		if (communicationServiceProvider == null) {
			logger.error("Invalid communicationServiceProvider");
			return null;
		}
		switch (communicationServiceProvider) {
			case DATAGEN_AUDIO_VOICECALL:
			case DATAGEN_TEXT_VOICECALL:
				return datagenVoiceStatusUpdaterService;
			default:
				logger.error("{} communicationServiceProvider is not supported for voice report service", communicationServiceProvider);
				throw new UnsupportedOperationException(
						"Service provider " + communicationServiceProvider + " not supported");
		}
	}
}
