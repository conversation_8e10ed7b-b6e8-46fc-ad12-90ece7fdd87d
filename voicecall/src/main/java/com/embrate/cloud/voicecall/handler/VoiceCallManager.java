package com.embrate.cloud.voicecall.handler;

import com.embrate.cloud.core.api.service.communication.*;
import com.embrate.cloud.core.lib.service.communication.CommunicationServiceManager;
import com.embrate.cloud.voicecall.service.IVoiceCallService;
import com.embrate.cloud.voicecall.service.VoiceCallServiceFactory;
import com.lernen.cloud.core.api.common.CommunicationServiceProvider;
import com.lernen.cloud.core.api.configurations.MetaDataPreferences;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.api.voicecall.*;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.utils.PhoneNumberUtils;
import com.lernen.cloud.core.utils.communication.service.CommunicationServiceUtils;
import com.lernen.cloud.dao.tier.notification.NotificationStatusDao;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 */
public class VoiceCallManager {
    private static final Logger logger = LogManager.getLogger(VoiceCallManager.class);
    private static final String DEFAULT_COUNTRY = "IN";

    private final VoiceCallServiceFactory voiceCallServiceFactory;
    private final CommunicationServiceManager communicationServiceManager;
    private final UserPreferenceSettings userPreferenceSettings;

    public VoiceCallManager(VoiceCallServiceFactory voiceCallServiceFactory, CommunicationServiceManager communicationServiceManager, UserPreferenceSettings userPreferenceSettings) {
        this.voiceCallServiceFactory = voiceCallServiceFactory;
        this.communicationServiceManager = communicationServiceManager;
        this.userPreferenceSettings = userPreferenceSettings;
    }

    public VoiceCallResponse sendVoiceCallAsync(int instituteId, IVoiceCallPayloadBuilder voiceCallAudioPayloadBuilder, UUID userId) {
        return sendVoiceCall(instituteId, voiceCallAudioPayloadBuilder, userId, false);
    }

    public <T extends UserCommunicationServicePayload> VoiceCallResponse sendVoiceCall(int instituteId, IVoiceCallPayloadBuilder<T> voiceCallAudioPayloadBuilder, UUID userId,
                                                boolean synchronous) {

        final MetaDataPreferences metaDataPreferences = userPreferenceSettings.getMetaDataPreferences(instituteId);

        if (!metaDataPreferences.isVoiceCallServiceEnabled()) {
            logger.info("Voice call service is not enabled for institute {}", instituteId);
            return VoiceCallResponse.failureResponse("Voice call service is not enabled for your institute");
        }

        VoiceCallPreferences voiceCallPreferences = userPreferenceSettings.getVoiceCallPreferences(instituteId);
        CommunicationServiceProvider serviceProvider = voiceCallPreferences.getServiceProvider();
        IVoiceCallService voiceCallService = voiceCallServiceFactory.getVoiceCallService(serviceProvider);
        if (voiceCallService == null) {
            logger.error("Voice call service not setup for instituteId {}. Skipping voice call!", instituteId);
            return VoiceCallResponse.failureResponse("Voice call service not setup for instituteId.");
        }

        VoiceCallPayloadWrapper<T> userAudioVoiceCallPreviewPayload = voiceCallAudioPayloadBuilder.getUserVoiceCallPreviewPayload();
        if (userAudioVoiceCallPreviewPayload == null) {
            logger.error("Invalid audio voice call preview content for instituteId {} for user {}. Skipping voice call!", instituteId, userId);
            return VoiceCallResponse.failureResponse("Invalid voice call payload details.");
        }

        if (StringUtils.isNotBlank(userAudioVoiceCallPreviewPayload.getErrorReason())) {
            logger.error("Payload error {} for instituteId {} for user {}. Skipping voice call!",
                    userAudioVoiceCallPreviewPayload.getErrorReason(), instituteId, userId);
            return VoiceCallResponse.failureResponse(userAudioVoiceCallPreviewPayload.getErrorReason());
        }

        if (!validateAudioVoiceCallPayloadWrapper(instituteId, userId, userAudioVoiceCallPreviewPayload)) {
            logger.error("Invalid userAudioVoiceCallPreviewPayload for instituteId {} for user {} of batchId {}. Skipping voice call !",
                    instituteId, userId, userAudioVoiceCallPreviewPayload.getBatchId());
            return VoiceCallResponse.failureResponse("Invalid voice call payload details.");
        }

        Integer academicSessionId = userAudioVoiceCallPreviewPayload.getAcademicSessionId();
        UserType userType = userAudioVoiceCallPreviewPayload.getUserType();
        UserVoiceCallCreditData userAudioVoiceCallCreditData = getUserVoiceCallCreditData(instituteId, serviceProvider,
                userAudioVoiceCallPreviewPayload.getUserVoiceCallPayload());

        if (CollectionUtils.isEmpty(userAudioVoiceCallCreditData.getUserVoiceCallPayloadList())) {
            logger.error("No valid user to send voice call for instituteId {} for user {} of batchId {}. Skipping voice call!",
                    instituteId, userId, userAudioVoiceCallPreviewPayload.getBatchId());
            return VoiceCallResponse
                    .failureResponse("No valid user found to send voice call. Please check contact details and voice call content.");
        }

        logger.info(
                "Total voice call to be send {}, total credit {}, unique users {}, for institute {}, userType {}, payload {}",
                userAudioVoiceCallCreditData.getUserCount(), userAudioVoiceCallCreditData.getVoiceCallCredits(),
                userAudioVoiceCallCreditData.getUserVoiceCallPayloadList().size(), instituteId, userType,
                userAudioVoiceCallPreviewPayload);


        CommunicationServiceTransactionResponse voiceCallTransactionResponse = communicationServiceManager.validateAndTransaction(instituteId,serviceProvider,
                userAudioVoiceCallCreditData.getVoiceCallCredits(), userType, userId,
                userAudioVoiceCallPreviewPayload.getTransactionType(), voiceCallPreferences.getBufferVoiceCallCount());

        if (voiceCallTransactionResponse == null || voiceCallTransactionResponse.getTransactionId() == null) {
            logger.error("Failed to add voice call transaction. Not sending voice call for institute {} and payload {}", instituteId,
                    userAudioVoiceCallPreviewPayload);
            return VoiceCallResponse.failureResponse(voiceCallTransactionResponse == null ? "Unable to perform voice call transaction"
                    : voiceCallTransactionResponse.getErrorReason());
        }

        List<T> finalUserAudioVoiceCallPayload = getFinalVoiceCallPayload(instituteId, serviceProvider, voiceCallAudioPayloadBuilder,
                userAudioVoiceCallCreditData);

        if (CollectionUtils.isEmpty(finalUserAudioVoiceCallPayload)) {
            logger.error(
                    "No final valid user to send voice call for instituteId {} for user {}. Reverting transaction and skipping voice call!",
                    instituteId, userId);
            communicationServiceManager.refundServiceTransaction(instituteId, serviceProvider, userType, userId, userAudioVoiceCallCreditData.getVoiceCallCredits(),
                    voiceCallTransactionResponse.getTransactionId());
            return VoiceCallResponse.failureResponse(
                    "Unable to send voice call for given users. If any credits are deducted, they will be refunded.");
        }

        VoiceCallServiceHandler voiceCallServiceHandler = new VoiceCallServiceHandler(voiceCallService);
        if (!synchronous) {
            sendVoiceCallAsync(instituteId, serviceProvider, userId, academicSessionId, userAudioVoiceCallPreviewPayload, finalUserAudioVoiceCallPayload,
                    userType, voiceCallServiceHandler, voiceCallTransactionResponse.getTransactionId());
            return VoiceCallResponse.successResponse(userAudioVoiceCallCreditData.getUserCount(),
                    userAudioVoiceCallCreditData.getVoiceCallCredits());
        } else {
            CommunicationServiceUserCreditStatus communicationServiceUserCreditStatus = sendVoiceCall(instituteId, serviceProvider, userId, academicSessionId, userAudioVoiceCallPreviewPayload, finalUserAudioVoiceCallPayload,
                    userType, voiceCallServiceHandler, voiceCallTransactionResponse.getTransactionId());

            return VoiceCallResponse.successResponse(
                    communicationServiceUserCreditStatus.getTotalContacts() - communicationServiceUserCreditStatus.getFailedContacts(),
                    communicationServiceUserCreditStatus.getTotalCredits() - communicationServiceUserCreditStatus.getFailedCredits());
        }

    }


    private <T extends UserCommunicationServicePayload> void sendVoiceCallAsync(int instituteId, CommunicationServiceProvider communicationServiceProvider, UUID userId, Integer academicSessionId,
                                    VoiceCallPayloadWrapper<T> userAudioVoiceCallPreviewPayload, List<T> finalUserAudioVoiceCallPayloadList, UserType userType, VoiceCallServiceHandler voiceCallServiceHandler, UUID transactionId) {

        Thread t = new Thread(new Runnable() {
            @Override
            public void run() {
                sendVoiceCall(instituteId, communicationServiceProvider, userId, academicSessionId, userAudioVoiceCallPreviewPayload, finalUserAudioVoiceCallPayloadList,
                        userType, voiceCallServiceHandler, transactionId);
            }
        });
        t.start();
    }

    private <T extends UserCommunicationServicePayload> CommunicationServiceUserCreditStatus sendVoiceCall(int instituteId, CommunicationServiceProvider communicationServiceProvider,UUID userId, Integer academicSessionId,
                                              VoiceCallPayloadWrapper<T> userAudioVoiceCallPreviewPayload ,List<T> finalUserAudioVoiceCallPayloadList, UserType userType,
                                                               VoiceCallServiceHandler voiceCallServiceHandler, UUID transactionId) {

        CommunicationServiceUserCreditStatus creditStatus = communicationServiceManager.executeService(instituteId,
                communicationServiceProvider, academicSessionId, userAudioVoiceCallPreviewPayload.getNotificationType(),
                userAudioVoiceCallPreviewPayload.getBatchId(), userAudioVoiceCallPreviewPayload.getBatchName(),
                finalUserAudioVoiceCallPayloadList, userAudioVoiceCallPreviewPayload.getUserType(), voiceCallServiceHandler, transactionId, true);

        if (creditStatus.getFailedCredits() > 0) {
            logger.info("Total failed voice credits = {} out of {}, refunding credits.",
                    creditStatus.getFailedCredits(), creditStatus.getTotalCredits());
            communicationServiceManager.refundServiceTransaction(instituteId, communicationServiceProvider, userType, userId, creditStatus.getFailedCredits(),
                    transactionId);
        } else {
            logger.info("Successfully sent all voice credits = {}", creditStatus.getTotalCredits());
        }
        return creditStatus;
    }

    private <T extends UserCommunicationServicePayload> List<T> getFinalVoiceCallPayload(int instituteId, CommunicationServiceProvider serviceProvider, IVoiceCallPayloadBuilder<T> voiceCallAudioPayloadBuilder,
                                                    UserVoiceCallCreditData<T> userVoiceCallCreditDataPreview) {
        try {
            if (!voiceCallAudioPayloadBuilder.executeVoiceCallAction()) {
                logger.error("Error while executing voice cal action for institute {}", instituteId);
                return null;
            }

            if (voiceCallAudioPayloadBuilder.previewPayloadIsFinal()) {
                return userVoiceCallCreditDataPreview.getUserVoiceCallPayloadList();
            }

            List<T> finalUserVoiceCallPayloadListWithoutValidation = voiceCallAudioPayloadBuilder.getFinalUserVoiceCallPayload();
            List<T> finalUserVoiceCallPayloadList = validateAndGetFinalVoiceCallPayload(instituteId, serviceProvider, userVoiceCallCreditDataPreview, finalUserVoiceCallPayloadListWithoutValidation);
            if (finalUserVoiceCallPayloadList == null) {
                return null;
            }

            return finalUserVoiceCallPayloadList;

        } catch (Exception e) {
            logger.error(
                    "Error while executing voice call action and getting final payload for instituteId {}, userVoiceCallCreditDataPreview {} ",
                    instituteId, userVoiceCallCreditDataPreview, e);
            return null;
        }
    }

    private  <T extends UserCommunicationServicePayload> List<T> validateAndGetFinalVoiceCallPayload(int instituteId, CommunicationServiceProvider serviceProvider, UserVoiceCallCreditData<T> previewUserVoiceCallCreditData,
                                            List<T> finalUserVoiceCallPayloadList) {

        if (CollectionUtils.isEmpty(finalUserVoiceCallPayloadList)) {
            logger.error("Empty final voice call payload list for instituteId {}", instituteId);
            return null;
        }

        UserVoiceCallCreditData<T> finalUserVoiceCallCreditData = getUserVoiceCallCreditData(instituteId, serviceProvider, finalUserVoiceCallPayloadList);
        if (finalUserVoiceCallCreditData.getVoiceCallCredits() != previewUserVoiceCallCreditData.getVoiceCallCredits()) {
            logger.error("Final voice call credit {} does not match with preview voice call credits {} , instituteId {}",
                    finalUserVoiceCallCreditData.getVoiceCallCredits(), previewUserVoiceCallCreditData.getVoiceCallCredits(), instituteId);
            return null;
        }

        if (finalUserVoiceCallCreditData.getUserCount() != previewUserVoiceCallCreditData.getUserCount()) {
            logger.error("Final voice call user count {} does not match with preview voice call user count {}, instituteId {}",
                    finalUserVoiceCallCreditData.getUserCount(), previewUserVoiceCallCreditData.getUserCount(), instituteId);
            return null;
        }

        if (finalUserVoiceCallCreditData.getUserVoiceCallPayloadList().size() != previewUserVoiceCallCreditData.getUserVoiceCallPayloadList()
                .size()) {
            logger.error(
                    "Final voice call unique user count {} does not match with preview voice call unique user count {}, instituteId {}",
                    finalUserVoiceCallCreditData.getUserVoiceCallPayloadList().size(),
                    previewUserVoiceCallCreditData.getUserVoiceCallPayloadList().size(), instituteId);
            return null;
        }

        return finalUserVoiceCallCreditData.getUserVoiceCallPayloadList();
    }

    private <T extends UserCommunicationServicePayload> boolean validateAudioVoiceCallPayloadWrapper(int instituteId, UUID userId, VoiceCallPayloadWrapper<T> audioVoiceCallPayloadVoiceCallPayloadWrapper) {

        if (CollectionUtils.isEmpty(audioVoiceCallPayloadVoiceCallPayloadWrapper.getUserVoiceCallPayload())) {
            logger.error("Empty voice content for instituteId {} for user {}. Skipping sms!", instituteId, userId);
            return false;
        }

        if (audioVoiceCallPayloadVoiceCallPayloadWrapper.getNotificationType() == null) {
            logger.error("Invalid notification type for instituteId {} for user {}. Skipping sms!", instituteId, userId);
            return false;
        }

        return true;
    }


    private <T extends UserCommunicationServicePayload> UserVoiceCallCreditData<T> getUserVoiceCallCreditData(int instituteId, CommunicationServiceProvider serviceProvider, List<T> inputUserVoiceCallPayloadList) {

        List<T> userAudioVoiceCallPayloadList = new ArrayList<>();

        int pulseCount = 0;
        int userCount = 0;
        for (T userCommunicationServicePayload : inputUserVoiceCallPayloadList) {
            List<String> mobileNumbers = new ArrayList<>();

            if (CollectionUtils.isEmpty(userCommunicationServicePayload.getDestinationChannelIds())) {
                logger.error("Empty contacts for instituteId {} for user {}. Skipping voice call.", instituteId,
                        userCommunicationServicePayload.getUserId());
                continue;
            }

            if (StringUtils.isBlank(userCommunicationServicePayload.getMessagePayload())) {
                logger.error("Invalid voice content for instituteId {} for user {}, Skipping voice call.", instituteId,
                        userCommunicationServicePayload.getUserId());
                continue;
            }

            int userPulseCount = 0;
            for (String number : userCommunicationServicePayload.getDestinationChannelIds()) {
                final String e164PhoneNumber = PhoneNumberUtils.getE164FormattedNumber(number, DEFAULT_COUNTRY);
                if (StringUtils.isBlank(e164PhoneNumber)) {
                    logger.error("Invalid contact number {} for user {}, instituteId {} . Skipping voice call.", number,
                            userCommunicationServicePayload.getUserId(), instituteId);
                    continue;
                }

                int serviceCredits = CommunicationServiceUtils.getServiceCredits(serviceProvider, userCommunicationServicePayload.getMessagePayload(), userCommunicationServicePayload.getMessageDuration());
                userPulseCount += serviceCredits;

                pulseCount += serviceCredits;
                userCount++;
                mobileNumbers.add(number);
            }

            if (!CollectionUtils.isEmpty(mobileNumbers)) {
                if(userCommunicationServicePayload instanceof UserAudioVoiceCallPayload){
                    UserAudioVoiceCallPayload userAudioVoiceCallPayload = (UserAudioVoiceCallPayload) userCommunicationServicePayload;
                    UserCommunicationServicePayload newUserCommunicationServicePayload = new UserAudioVoiceCallPayload(userCommunicationServicePayload.getUserId(), mobileNumbers, userAudioVoiceCallPayload.getAudioVoiceCallData(),
                            userCommunicationServicePayload.getMetaData(), userPulseCount, null);

                    userAudioVoiceCallPayloadList.add((T) newUserCommunicationServicePayload);
                }else if(userCommunicationServicePayload instanceof  UserTextVoiceCallPayload){
                    UserTextVoiceCallPayload userTextVoiceCallPayload = (UserTextVoiceCallPayload) userCommunicationServicePayload;
                    UserCommunicationServicePayload newUserCommunicationServicePayload = new UserTextVoiceCallPayload(userCommunicationServicePayload.getUserId(), mobileNumbers, userTextVoiceCallPayload.getMessagePayload(),
                            userCommunicationServicePayload.getMetaData(), userPulseCount, null);

                    userAudioVoiceCallPayloadList.add((T) newUserCommunicationServicePayload);
                }else{
                    throw new EmbrateRunTimeException("Unsupported voice call type");
                }

            }
        }

        return new UserVoiceCallCreditData<T>(userCount, pulseCount, userAudioVoiceCallPayloadList);
    }

    private class UserVoiceCallCreditData<T extends UserCommunicationServicePayload> {

        private final int userCount;
        private final int voiceCallCredits;
        private final List<T> userVoiceCallPayloadList;

        public UserVoiceCallCreditData(int userCount, int voiceCallCredits, List<T> userVoiceCallPayloadList) {
            this.userCount = userCount;
            this.voiceCallCredits = voiceCallCredits;
            this.userVoiceCallPayloadList = userVoiceCallPayloadList;
        }

        public int getUserCount() {
            return userCount;
        }

        public int getVoiceCallCredits() {
            return voiceCallCredits;
        }

        public List<T> getUserVoiceCallPayloadList() {
            return userVoiceCallPayloadList;
        }

        @Override
        public String toString() {
            return "UserVoiceCallCreditData{" +
                    "userCount=" + userCount +
                    ", voiceCallCredits=" + voiceCallCredits +
                    ", userVoiceCallPayloadList=" + userVoiceCallPayloadList +
                    '}';
        }
    }

}
