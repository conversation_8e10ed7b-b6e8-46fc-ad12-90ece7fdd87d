package com.embrate.cloud.voicecall.service;

import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.voicecall.VoiceCallSubmitResponse;
import com.lernen.cloud.core.api.voicecall.datagen.DatagenAudioUploadResponse;
import com.lernen.cloud.core.api.voicecall.datagen.DatagenVoiceCallResponse;
import com.lernen.cloud.core.api.voicecall.VoiceCallAudioTemplateUploadResponse;
import com.lernen.cloud.core.utils.PhoneNumberUtils;
import com.lernen.cloud.core.utils.SharedConstants;
import com.lernen.cloud.core.utils.rest.RestAPIHandler;
import com.lernen.cloud.core.utils.rest.RestClient;
import com.sun.jersey.api.client.ClientHandlerException;
import com.sun.jersey.api.client.ClientResponse;
import com.sun.jersey.api.client.UniformInterfaceException;
import com.sun.jersey.api.client.WebResource;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.http.client.HttpClient;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.mime.MultipartEntity;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.sound.sampled.*;
import javax.ws.rs.core.MediaType;
import java.io.*;
import java.net.URI;
import java.util.*;

import static com.lernen.cloud.core.api.voicecall.datagen.DatagenVoiceCallResponse.DATAGEN_VOICE_CALL_RESPONSE;
import static com.lernen.cloud.core.utils.StringConstants.*;

/**
 * <AUTHOR>
 */
public class DatagenVoiceCallService implements IVoiceCallService {

    private static final Logger logger = LogManager.getLogger(DatagenVoiceCallService.class);

    private static final String SEND_VOICE_CALL_API = "https://voice.datagenit.com/API/start_campaign.php";
    private static final String UPLOAD_VOICE_CALL_API = "https://voice.datagenit.com/API/upload_voice.php";

//    private static final String API_KEY = "WWhpXu41vFVI1O";
    private static final String API_KEY = "MbIl7u1363iDp6hq";
    private static final String VOICE_ID_QUERY_PARAM_KEY = "voiceid";
    private static final String ROUTE_QUERY_PARAM_KEY = "type";
    private static final String RETRY_QUERY_PARAM_KEY = "retry";
    private static final String MOBILES_QUERY_PARAM_KEY = "msisdn";
    private static final String AUTHKEY_QUERY_PARAM_KEY = "auth";

    private static final String BASE_TEMP_PATH = "/tmp/";


    private final RestClient restClient;
    private final HttpClient httpClient;

    public DatagenVoiceCallService(RestClient restClient) {
        this.restClient = restClient;
        this.httpClient = new DefaultHttpClient();
    }

    @Override
    public VoiceCallSubmitResponse sendVoiceCall(String mobileNumber, String voiceId) {
        logger.info("sending voice call to mobileNumber " + mobileNumber + ", voiceId= " + voiceId);
        Map<String, String> sendVoiceCallQueryParams = createSendVoiceQueryParams(Arrays.asList(mobileNumber), voiceId);
        if (sendVoiceCallQueryParams == null) {
            logger.error("Invalid voice call params...");
            return null;
        }

        ClientResponse response = null;
        try {
            WebResource webResource = restClient.resource(new URI(SEND_VOICE_CALL_API));
            webResource = RestAPIHandler.addQueryParam(webResource, sendVoiceCallQueryParams);
            logger.info("Sending voice call with URI {}" , webResource.getURI());
            response = webResource.type(MediaType.APPLICATION_JSON).post(ClientResponse.class);
            if (response.getStatus() == HttpStatus.SC_OK) {
                String responseData = response.getEntity(String.class);
                logger.info("datagen responseData = {}" + responseData);
                if (StringUtils.isBlank(responseData)) {
                    logger.error("Error response from Datagen service {}. Voice call failed due to error {}, status code {}",
                            SEND_VOICE_CALL_API, responseData, response.getStatus());
                    throw new EmbrateRunTimeException("Error response from Datagen service provider.");
                }

                DatagenVoiceCallResponse datagenVoiceCallResponse = SharedConstants.OBJECT_MAPPER.readValue(responseData,DatagenVoiceCallResponse.class);
                logger.info("datagenVoiceCallResponse {}", datagenVoiceCallResponse.toString());
                Map<String,Object> metadata = new HashMap<>();
                metadata.put(DATAGEN_VOICE_CALL_RESPONSE, SharedConstants.GSON.toJson(datagenVoiceCallResponse));

                if(datagenVoiceCallResponse.getStatus().equalsIgnoreCase("success") && datagenVoiceCallResponse.getCode() == 100 && StringUtils.isNotBlank(datagenVoiceCallResponse.getCampaignId())){
                    logger.info("Successfully sent the voice call for url {}", webResource.getURI());
                    return new VoiceCallSubmitResponse(true, datagenVoiceCallResponse.getCampaignId(), mobileNumber, null, null, datagenVoiceCallResponse.getValidCount(), metadata);
                }

                logger.error("Error while sending the voice call for url {}", webResource.getURI());
                return new VoiceCallSubmitResponse(false, datagenVoiceCallResponse.getCampaignId(), mobileNumber,  String.valueOf(datagenVoiceCallResponse.getCode()), datagenVoiceCallResponse.getDescription(), datagenVoiceCallResponse.getValidCount(), metadata);

            }
        } catch (UniformInterfaceException | ClientHandlerException e) {
            logger.error("Unable to perform get call to URL {}", SEND_VOICE_CALL_API, e);
        } catch (final Exception e) {
            logger.error("Exception occured while performing get call to URL {}", SEND_VOICE_CALL_API, e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return new VoiceCallSubmitResponse(false, null, mobileNumber, null, null, null, null);
    }

    @Override
    public VoiceCallSubmitResponse sendTextVoiceCall(String mobileNumber, String voiceTextMessage) {
        return null;
    }

    @Override
    public VoiceCallAudioTemplateUploadResponse uploadAudioTemplate(UUID templateId, String fileExtension, ByteArrayOutputStream audioTemplate) {
        String url = UPLOAD_VOICE_CALL_API + QUESTION_MARK +  AUTHKEY_QUERY_PARAM_KEY + EQUAL + API_KEY;
        try {
            HttpPost httpPost = new HttpPost(url);
            String tempFilePath =  BASE_TEMP_PATH + UUID.randomUUID().toString() + DOT + fileExtension;
            File tempFile = new File(tempFilePath);
            logger.info("Writing voice audio file to temp path {}, templateId {}", tempFilePath, templateId);
            try (FileOutputStream outputStream = new FileOutputStream(tempFile, false)) {
                outputStream.write(audioTemplate.toByteArray());
            }catch (Exception e){
                logger.error("Error while writing temporary file to path {}, templateId {}", tempFilePath, templateId, e);
            }

            FileBody uploadFilePart = new FileBody(tempFile);
            MultipartEntity reqEntity = new MultipartEntity();
            reqEntity.addPart("voice", uploadFilePart);
            httpPost.setEntity(reqEntity);

            ResponseHandler<String> responseHandler = new BasicResponseHandler();
            String response = httpClient.execute(httpPost, responseHandler);
            logger.info("response from voice upload api {}, templateId {}", response, templateId);
            if(StringUtils.isBlank(response)){
                logger.error("Invalid response from upload api for templateId {}", templateId);
                return new VoiceCallAudioTemplateUploadResponse(false, null, null, null, null);
            }

            DatagenAudioUploadResponse datagenAudioUploadResponse = SharedConstants.OBJECT_MAPPER.readValue(response, DatagenAudioUploadResponse.class);
            logger.info("datagenAudioUploadResponse {}, templateId {}", datagenAudioUploadResponse.toString(), templateId);
            tempFile.delete();
            logger.info("Deleted the temp file {} for templateId {}", tempFilePath, templateId);

            Map<String,Object> metadata = new HashMap<>();
            metadata.put("timestamp", datagenAudioUploadResponse.getTimestampStr());
            if(datagenAudioUploadResponse.getStatus().equalsIgnoreCase("success") && datagenAudioUploadResponse.getCode() == 100 && StringUtils.isNotBlank(datagenAudioUploadResponse.getVoiceId())){
                logger.info("Successfully uploaded the voice call audio for file {}, templateId {}", fileExtension, templateId);
                return new VoiceCallAudioTemplateUploadResponse(true, datagenAudioUploadResponse.getVoiceId(), null, null,  metadata);
            }

            logger.error("Error while uploading the voice call for name {}, templateId {}", fileExtension, templateId);
            return new VoiceCallAudioTemplateUploadResponse(false, null, String.valueOf(datagenAudioUploadResponse.getCode()), datagenAudioUploadResponse.getDescription(), metadata);
        } catch (UniformInterfaceException | ClientHandlerException e) {
            logger.error("Unable to upload audio to URL {} for templateId {}", SEND_VOICE_CALL_API, templateId, e);
        } catch (final Exception e) {
            logger.error("Exception occurred while performing get call to URL {}, templateId {}", SEND_VOICE_CALL_API, templateId, e);
        }

       return new VoiceCallAudioTemplateUploadResponse(false, null, null, null, null);
    }

    private Map<String, String> createSendVoiceQueryParams(List<String> mobileNumbers, String voiceId) {
        Map<String, String> sendSMSQueryParams = new HashMap<>();
        sendSMSQueryParams.put(ROUTE_QUERY_PARAM_KEY, "1");
        sendSMSQueryParams.put(VOICE_ID_QUERY_PARAM_KEY, voiceId);
        sendSMSQueryParams.put(RETRY_QUERY_PARAM_KEY, "0");
        sendSMSQueryParams.put(AUTHKEY_QUERY_PARAM_KEY, API_KEY);

        String mobiles = "";
        boolean first = true;
        for (String mobileNumber : mobileNumbers) {
            String _10DigitMobileNumber = PhoneNumberUtils.formatTo10Digit(mobileNumber);
            if(StringUtils.isBlank(_10DigitMobileNumber)){
                logger.error("Invalid mobile number {}", _10DigitMobileNumber);
                return null;
            }
            if (first) {
                mobiles += _10DigitMobileNumber;
                first = false;
                continue;
            }
            mobiles = mobiles + "," + _10DigitMobileNumber;
        }
        sendSMSQueryParams.put(MOBILES_QUERY_PARAM_KEY, mobiles);

        return sendSMSQueryParams;

    }

    public static void main(String [] args) throws Exception {
        DatagenVoiceCallService datagenVoiceCallService = new DatagenVoiceCallService(RestClient.REST_CLIENT);
//        datagenVoiceCallService.sendVoiceCall("9982370071", "4657");
        File file = new File("/Users/<USER>/Downloads/test1.wav");
//        File file = new File("/Users/<USER>/Downloads/song1.mp3");
//        File file = new File("/Users/<USER>/Desktop/test.m4a");

        final ByteArrayOutputStream bos = new ByteArrayOutputStream();
        IOUtils.copy(new FileInputStream(file), bos);
//        datagenVoiceCallService.uploadAudioTemplate(bos);

//        System.out.println(getDuration(file));
    }

    private static float getDuration(File file) throws Exception {
        AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(file);
        AudioFormat format = audioInputStream.getFormat();
        System.out.println("format : " + format);
        long audioFileLength = file.length();
        int frameSize = format.getFrameSize();
        float frameRate = format.getFrameRate();
        float durationInSeconds = (audioFileLength / (frameSize * frameRate));
        System.out.println("frameSize : " + frameSize + ", frameRate : "   + " : " + frameRate);



        return (durationInSeconds);
    }

}
