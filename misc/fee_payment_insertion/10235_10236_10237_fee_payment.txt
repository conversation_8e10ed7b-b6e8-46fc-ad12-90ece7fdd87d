Take the build - mvn clean install -T 4C


copy jar file to specific path in VM -
scp -i ~/.ssh/lernen-be-2022.cer /Users/<USER>/Lernen/lernen-backend/dev-tools/target/dev-tools-1.0.1-SNAPSHOT.jar  ubuntu@*************:/tmp/

login to server (prod)
ssh -i ~/.ssh/lernen-be-2022.cer ubuntu@*************

check the file in server -
cd /tmp/
ls -lrt
File should be there

final script
scp -i ~/.ssh/lernen-be-2022.cer /Users/<USER>/Lernen/lernen-backend/student_data/FeeDataInsertion/FeePayment/_10235_10236_10237/10235_fee_payment_single_data.csv  ubuntu@*************:/tmp/
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=wzpDFxPxS2U7AzGf -Dsecurity_mysql_url="mysql://lernen-prod.cetybhs3xuam.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=wzpDFxPxS2U7AzGf -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.embrate.cloud.dev.tools.fees.payment.FeePaymentTransactionCreator -f /tmp/10235_fee_payment_single_data.csv -i 10235 -s 119 -user a5e4c314-16d9-4a86-8165-6cde8a834db7 -h true
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=wzpDFxPxS2U7AzGf -Dsecurity_mysql_url="mysql://lernen-prod.cetybhs3xuam.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=wzpDFxPxS2U7AzGf -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.embrate.cloud.dev.tools.fees.payment.FeePaymentTransactionCreator -f /tmp/10235_fee_payment_single_data.csv -i 10235 -s 119 -user a5e4c314-16d9-4a86-8165-6cde8a834db7 -h true -u true


scp -i ~/.ssh/lernen-be-2022.cer /Users/<USER>/Lernen/lernen-backend/student_data/FeeDataInsertion/FeePayment/_10235_10236_10237/10235_fee_payment_data.csv  ubuntu@*************:/tmp/
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=wzpDFxPxS2U7AzGf -Dsecurity_mysql_url="mysql://lernen-prod.cetybhs3xuam.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=wzpDFxPxS2U7AzGf -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.embrate.cloud.dev.tools.fees.payment.FeePaymentTransactionCreator10235 -f /tmp/10235_fee_payment_data.csv -i 10235 -s 119 -user a5e4c314-16d9-4a86-8165-6cde8a834db7 -h true
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=wzpDFxPxS2U7AzGf -Dsecurity_mysql_url="mysql://lernen-prod.cetybhs3xuam.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=wzpDFxPxS2U7AzGf -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.embrate.cloud.dev.tools.fees.payment.FeePaymentTransactionCreator10235 -f /tmp/10235_fee_payment_data.csv -i 10235 -s 119 -user a5e4c314-16d9-4a86-8165-6cde8a834db7 -h true -u true

scp -i ~/.ssh/lernen-be-2022.cer /Users/<USER>/Lernen/lernen-backend/student_data/FeeDataInsertion/FeePayment/_10235_10236_10237/10236_fee_payment_data.csv  ubuntu@*************:/tmp/
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=wzpDFxPxS2U7AzGf -Dsecurity_mysql_url="mysql://lernen-prod.cetybhs3xuam.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=wzpDFxPxS2U7AzGf -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.embrate.cloud.dev.tools.fees.payment.FeePaymentTransactionCreator10235 -f /tmp/10236_fee_payment_data.csv -i 10236 -s 120 -user a5e4c314-16d9-4a86-8165-6cde8a834db7 -h true
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=wzpDFxPxS2U7AzGf -Dsecurity_mysql_url="mysql://lernen-prod.cetybhs3xuam.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=wzpDFxPxS2U7AzGf -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.embrate.cloud.dev.tools.fees.payment.FeePaymentTransactionCreator10235 -f /tmp/10236_fee_payment_data.csv -i 10236 -s 120 -user a5e4c314-16d9-4a86-8165-6cde8a834db7 -h true -u true

scp -i ~/.ssh/lernen-be-2022.cer /Users/<USER>/Lernen/lernen-backend/student_data/FeeDataInsertion/FeePayment/_10235_10236_10237/10237_fee_payment_data.csv  ubuntu@*************:/tmp/
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=wzpDFxPxS2U7AzGf -Dsecurity_mysql_url="mysql://lernen-prod.cetybhs3xuam.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=wzpDFxPxS2U7AzGf -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.embrate.cloud.dev.tools.fees.payment.FeePaymentTransactionCreator -f /tmp/10237_fee_payment_data.csv -i 10237 -s 121 -user a5e4c314-16d9-4a86-8165-6cde8a834db7 -h true
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=wzpDFxPxS2U7AzGf -Dsecurity_mysql_url="mysql://lernen-prod.cetybhs3xuam.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=wzpDFxPxS2U7AzGf -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.embrate.cloud.dev.tools.fees.payment.FeePaymentTransactionCreator -f /tmp/10237_fee_payment_data.csv -i 10237 -s 121 -user a5e4c314-16d9-4a86-8165-6cde8a834db7 -h true -u true


Check logs in dev tools and normal server -
login to server -
ssh -i ~/.ssh/lernen-be.pem ubuntu@*************

devtools logs -
then go to path -
cd /var/log/lernen/devTools/
then run -
tail -100f devtools.log
