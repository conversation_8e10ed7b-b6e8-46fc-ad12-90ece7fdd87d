sudo java -Dlernen_env=local -cp dev-tools-1.0.1-SNAPSHOT.jar com.embrate.cloud.dev.tools.fees.payment.FeePaymentTransactionCreator10265 -f /tmp/10275_fee_ingestion_data.csv -i 10275 -s 171 -user 0ab3cb46-a2fd-47e5-ac34-56eb0f28f8df -h true
sudo java -Dlernen_env=local -cp dev-tools-1.0.1-SNAPSHOT.jar com.embrate.cloud.dev.tools.fees.payment.FeePaymentTransactionCreator10265 -f /Users/<USER>/Desktop/10275_fee_ingestion_data.csv -i 10275 -s 171 -user 0ab3cb46-a2fd-47e5-ac34-56eb0f28f8df -h true -u true



scp -i ~/.ssh/lernen-be-2023.cer /Users/<USER>/Lernen/lernen-backend/student_data/FeeDataInsertion/FeePayment/10190_fee_payment_single_data.csv  <EMAIL>:/tmp/

sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=FMMrFd58Spnffpx -Dsecurity_mysql_url="mysql://lernen-prod.cjvqf7murjkc.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=FMMrFd58Spnffpx -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp dev-tools-1.0.1-SNAPSHOT.jar com.embrate.cloud.dev.tools.fees.payment.FeePaymentTransactionCreator10265 -f /tmp/10395_fee_payment_data.csv -i 10390 -s 262 -user 1c0b3f6e-49fa-4984-9cea-ff088553fc42 -h true
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=FMMrFd58Spnffpx -Dsecurity_mysql_url="mysql://lernen-prod.cjvqf7murjkc.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=FMMrFd58Spnffpx -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp dev-tools-1.0.1-SNAPSHOT.jar com.embrate.cloud.dev.tools.fees.payment.FeePaymentTransactionCreator10265 -f /Users/<USER>/Desktop/10395_fee_payment_data.csv.csv -i 10390 -s 262 -user 1c0b3f6e-49fa-4984-9cea-ff088553fc42 -h true -u true



Take the build - mvn clean install -T 4C


copy jar file to specific path in VM -
scp -i ~/.ssh/lernen-be-2023.cer /Users/<USER>/Lernen/lernen-backend/dev-tools/target/dev-tools-1.0.1-SNAPSHOT.jar  <EMAIL>:/tmp/

login to server (prod)
ssh -i ~/.ssh/lernen-be-2021.cer <EMAIL>

check the file in server -
cd /tmp/
ls -lrt
File should be there

final script
scp -i ~/.ssh/lernen-be-2021.cer /Users/<USER>/Lernen/lernen-backend/student_data/FeeDataInsertion/FeePayment/10190_fee_payment_single_data.csv  ubuntu@************:/tmp/
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=wzpDFxPxS2U7AzGf -Dsecurity_mysql_url="mysql://lernen-prod.czuhadhihdes.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=wzpDFxPxS2U7AzGf -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.embrate.cloud.dev.tools.fees.payment.FeePaymentTransactionCreator -f /tmp/10190_fee_payment_single_data.csv -i 10190 -s 84 -user 3a51b85d-8458-4024-b180-97a4270ba879 -h true -u false
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=wzpDFxPxS2U7AzGf -Dsecurity_mysql_url="mysql://lernen-prod.czuhadhihdes.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=wzpDFxPxS2U7AzGf -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.embrate.cloud.dev.tools.fees.payment.FeePaymentTransactionCreator -f /tmp/10190_fee_payment_single_data.csv -i 10190 -s 84 -user 3a51b85d-8458-4024-b180-97a4270ba879 -h true -u true


scp -i ~/.ssh/lernen-be-2021.cer /Users/<USER>/Lernen/lernen-backend/student_data/FeeDataInsertion/FeePayment/10190_fee_payment_data.csv  ubuntu@************:/tmp/
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=wzpDFxPxS2U7AzGf -Dsecurity_mysql_url="mysql://lernen-prod.czuhadhihdes.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=wzpDFxPxS2U7AzGf -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.embrate.cloud.dev.tools.fees.payment.FeePaymentTransactionCreator -f /tmp/10190_fee_payment_data.csv -i 10190 -s 84 -user 3a51b85d-8458-4024-b180-97a4270ba879 -h true -u false
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=wzpDFxPxS2U7AzGf -Dsecurity_mysql_url="mysql://lernen-prod.czuhadhihdes.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=wzpDFxPxS2U7AzGf -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.embrate.cloud.dev.tools.fees.payment.FeePaymentTransactionCreator -f /tmp/10190_fee_payment_data.csv -i 10190 -s 84 -user 3a51b85d-8458-4024-b180-97a4270ba879 -h true -u true



scp -i ~/.ssh/lernen-be-2021.cer /Users/<USER>/Lernen/lernen-backend/student_data/FeeDataInsertion/FeePayment/10190_fee_payment_prev_session_fees.csv  ubuntu@************:/tmp/
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=wzpDFxPxS2U7AzGf -Dsecurity_mysql_url="mysql://lernen-prod.czuhadhihdes.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=wzpDFxPxS2U7AzGf -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.embrate.cloud.dev.tools.fees.payment.FeePaymentTransactionCreator -f /tmp/10190_fee_payment_prev_session_fees.csv -i 10190 -s 84 -user 3a51b85d-8458-4024-b180-97a4270ba879 -h true -u false
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=wzpDFxPxS2U7AzGf -Dsecurity_mysql_url="mysql://lernen-prod.czuhadhihdes.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=wzpDFxPxS2U7AzGf -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.embrate.cloud.dev.tools.fees.payment.FeePaymentTransactionCreator -f /tmp/10190_fee_payment_prev_session_fees.csv -i 10190 -s 84 -user 3a51b85d-8458-4024-b180-97a4270ba879 -h true -u true


Check logs in dev tools and normal server -
login to server -
ssh -i ~/.ssh/lernen-be.pem ubuntu@************

devtools logs -
then go to path -
cd /var/log/lernen/devTools/
then run -
tail -100f devtools.log
