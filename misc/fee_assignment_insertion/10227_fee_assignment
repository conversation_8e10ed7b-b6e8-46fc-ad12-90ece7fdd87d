Take the build - mvn clean install -T 4C


copy jar file to specific path in VM -
scp -i ~/.ssh/lernen-be-2022.cer /Users/<USER>/Lernen/lernen-backend/dev-tools/target/dev-tools-1.0.1-SNAPSHOT.jar  ubuntu@************:/tmp/

login to server (prod)
ssh -i ~/.ssh/lernen-be-2022.cer ubuntu@************

check the file in server -
cd /tmp/
ls -lrt
File should be there

final script
scp -i ~/.ssh/lernen-be-2022.cer /Users/<USER>/Lernen/lernen-backend/student_data/FeeDataInsertion/FeeAssignment/_10227/10227_fee_assignment_single_data.csv  ubuntu@************:/tmp/
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=wzpDFxPxS2U7AzGf -Dsecurity_mysql_url="mysql://lernen-prod.cetybhs3xuam.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=wzpDFxPxS2U7AzGf -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.lernen.cloud.dev.tools.fees.assignment.FeeAmountAssigner -f /tmp/10227_fee_assignment_single_data.csv -i 10227 -s 128 -user 66dc0bf1-e852-42bf-8f92-d6d18c68e0c7 -h true
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=wzpDFxPxS2U7AzGf -Dsecurity_mysql_url="mysql://lernen-prod.cetybhs3xuam.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=wzpDFxPxS2U7AzGf -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.lernen.cloud.dev.tools.fees.assignment.FeeAmountAssigner -f /tmp/10227_fee_assignment_single_data.csv -i 10227 -s 128 -user 66dc0bf1-e852-42bf-8f92-d6d18c68e0c7 -h true -u true

scp -i ~/.ssh/lernen-be-2022.cer /Users/<USER>/Lernen/lernen-backend/student_data/FeeDataInsertion/FeeAssignment/_10227/10227_fee_assignment.csv  ubuntu@************:/tmp/
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=wzpDFxPxS2U7AzGf -Dsecurity_mysql_url="mysql://lernen-prod.cetybhs3xuam.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=wzpDFxPxS2U7AzGf -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.lernen.cloud.dev.tools.fees.assignment.FeeAmountAssigner -f /tmp/10227_fee_assignment.csv -i 10227 -s 128 -user 66dc0bf1-e852-42bf-8f92-d6d18c68e0c7 -h true
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=wzpDFxPxS2U7AzGf -Dsecurity_mysql_url="mysql://lernen-prod.cetybhs3xuam.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=wzpDFxPxS2U7AzGf -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -cp ********************************* com.lernen.cloud.dev.tools.fees.assignment.FeeAmountAssigner -f /tmp/10227_fee_assignment.csv -i 10227 -s 128 -user 66dc0bf1-e852-42bf-8f92-d6d18c68e0c7 -h true -u true

Check logs in dev tools and normal server -
login to server -
ssh -i ~/.ssh/lernen-be-2022.cer ubuntu@************

devtools logs -
then go to path -
cd /var/log/lernen/devTools/
then run -
tail -100f devtools.log


sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=FMMrFd58Spnffpx -Dsecurity_mysql_url="mysql://lernen-prod.c5m2qecqy0qe.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=FMMrFd58Spnffpx -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -Doauth_defaultReadOnlyClientId=read_only_embrate_client_id -Doauth_defaultReadOnlyClientSecret=FdJdqWJ3rxWVBddeB5tX -cp ********************************* com.lernen.cloud.dev.tools.fees.assignment.FeeAmountAssigner -f /tmp/fee_assignment_data_output_after_zero_amount_removal.csv -i 10225 -s 281 -user f0484d4b-9608-4d5a-a97b-6ef919022b10 -h true
sudo java -Dlernen_env=prod -Dsecurity_mysql_username=lernenprod -Dsecurity_mysql_password=FMMrFd58Spnffpx -Dsecurity_mysql_url="mysql://lernen-prod.c5m2qecqy0qe.us-east-1.rds.amazonaws.com:3306/security_db?useSSL=false&useUnicode=yes&characterEncoding=UTF-8" -Dmysql_url="*********************************************************************************************************************************" -Dmysql_username=lernenprod -Dmysql_password=FMMrFd58Spnffpx -Doauth_defaultClientId=api_client -Doauth_defaultClientSecret=pf2wbyqaQ9b5WbkC -Doauth_defaultReadOnlyClientId=read_only_embrate_client_id -Doauth_defaultReadOnlyClientSecret=FdJdqWJ3rxWVBddeB5tX -cp ********************************* com.lernen.cloud.dev.tools.fees.assignment.FeeAmountAssigner -f /tmp/fee_assignment_data_output_after_zero_amount_removal.csv -i 10225 -s 281 -user f0484d4b-9608-4d5a-a97b-6ef919022b10 -h true -u true





