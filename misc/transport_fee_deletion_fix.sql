create view temp_transport as SELECT
    th.transport_history_id,
    th.student_id,
    th.academic_session_id,
    th.institute_id,
    th.transport_status,
    fhc.fee_head_id AS fh_id,
    thfm.fee_id AS thfm_fee_id,
    fa.fee_id AS fee_id,
    fa.amount AS a_amount,
    thfm.amount AS tfa
FROM
    transport_history th
JOIN
    transport_history_fee_id_mapping thfm
    ON th.transport_history_id = thfm.transport_history_id
JOIN
    (
        SELECT fee_head_id, institute_id
        FROM fee_head_configuration
        WHERE fee_head_type = 'SYSTEM' AND fee_head_tag = 'TRANSPORT'
    ) fhc
    ON th.institute_id = fhc.institute_id
LEFT JOIN
    fee_assignment fa
    ON fa.institute_id = th.institute_id
    AND fa.entity_id = th.student_id
    AND fa.fee_id = thfm.fee_id
    AND fa.fee_head_id = fhc.fee_head_id
WHERE
    th.transport_status = 'ACTIVE' and thfm.amount is not NULL and thfm.amount > 0 and fa.amount is NULL ;
    th.transport_status = 'ACTIVE' and thfm.amount is not NULL and thfm.amount > 0 and th.institute_id = 10355 and fa.amount is NULL ;


insert into fee_assignment (institute_id, entity_id, entity_name, fee_id, fee_head_id, amount) select institute_id, student_id, 'STUDENT', thfm_fee_id, fh_id, tfa from temp_transport where institute_id = 10001 and student_id = "188eb327-baac-4354-bbf1-35ebb893ea4c";

insert into fee_assignment (institute_id, entity_id, entity_name, fee_id, fee_head_id, amount) select institute_id, student_id, 'STUDENT', thfm_fee_id, fh_id, tfa from temp_transport ;