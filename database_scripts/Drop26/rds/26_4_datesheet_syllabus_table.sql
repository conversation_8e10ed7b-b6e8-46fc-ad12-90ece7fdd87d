CREATE TABLE IF NOT EXISTS syllabus_and_datesheet_metadata(
institute_id int(11) NOT NULL,
academic_session_id int(11) NOT NULL,
datesheet_id varchar(36) NOT NULL,
standard_id varchar(36) NOT NULL,
section_id int(11),
exam_id varchar(36) NOT NULL,
exam_start_date timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
exam_end_date timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
status varchar(36) NOT NULL,
added_by varchar(36) NOT NULL,
updated_by varchar(36),
added_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ,
updated_at timestamp DEFAULT CURRENT_TIMESTAMP ,
primary key(datesheet_id),
FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id),
FOREIG<PERSON>EY (exam_id) REFERENCES exam_metadata(exam_id),
FOREIGN KEY (standard_id) REFERENCES standards(standard_id)
);

CREATE INDEX syllabus_and_datesheet_metadata_standard_id ON syllabus_and_datesheet_metadata (standard_id);

CREATE TABLE IF NOT EXISTS syllabus_and_datesheet_details(
datesheet_id varchar(36) NOT NULL,
course_id varchar(36) NOT NULL,
dimension_id int(11) NOT NULL,
start_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
end_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
syllabus text,
unique key(datesheet_id, course_id, dimension_id),
FOREIGN KEY (datesheet_id) REFERENCES syllabus_and_datesheet_metadata(datesheet_id),
FOREIGN KEY (course_id) REFERENCES class_courses(course_id),
FOREIGN KEY (dimension_id) REFERENCES exam_dimensions(dimension_id)
);