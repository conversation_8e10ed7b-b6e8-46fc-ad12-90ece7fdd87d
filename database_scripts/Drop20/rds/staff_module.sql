CREATE TABLE IF NOT EXISTS staff_category(
	institute_id int NOT NULL,
	staff_category_id varchar(36) NOT NULL,
	staff_category_name varchar(1000) NOT NULL,
	created_by varchar(36) NOT NULL,
	created_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_by varchar(36),
	updated_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
	primary key(staff_category_id),
	unique key (institute_id, staff_category_name),
	FOREIGN KEY (created_by) REFERENCES users(user_id)
);

CREATE TABLE IF NOT EXISTS staff_department(
	institute_id int NOT NULL,
	staff_department_id varchar(36) NOT NULL,
	staff_department_name varchar(1000) NOT NULL,
	created_by varchar(36) NOT NULL,
	created_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_by varchar(36),
	updated_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
	primary key(staff_department_id),
	unique key (institute_id, staff_department_name),
	FOREIGN KEY (created_by) REFERENCES users(user_id)
);

CREATE TABLE IF NOT EXISTS staff_designation(
	institute_id int NOT NULL,
	staff_designation_id varchar(36) NOT NULL,
	staff_designation_name varchar(1000) NOT NULL,
	created_by varchar(36) NOT NULL,
	created_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_by varchar(36),
	updated_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
	primary key(staff_designation_id),
	unique key (institute_id, staff_designation_name),
	FOREIGN KEY (created_by) REFERENCES users(user_id)
);

//staff_details changes

//delete columns
ALTER TABLE staff_details DROP COLUMN area_type;
ALTER TABLE staff_details DROP COLUMN blood_group;
ALTER TABLE staff_details DROP COLUMN blood_pressure;
ALTER TABLE staff_details DROP COLUMN pulse;
ALTER TABLE staff_details DROP COLUMN height;
ALTER TABLE staff_details DROP COLUMN weight;
ALTER TABLE staff_details DROP COLUMN date_of_physical_examination;
ALTER TABLE staff_details DROP COLUMN location;
ALTER TABLE staff_details DROP COLUMN education_details;
ALTER TABLE staff_details DROP COLUMN emergancy_contact_details;
ALTER TABLE staff_details DROP COLUMN staff_curr_yn;
ALTER TABLE staff_details DROP COLUMN designation;
ALTER TABLE staff_details DROP COLUMN last_occupation_details;
ALTER TABLE staff_details DROP COLUMN staff_type;

//rename columns
ALTER TABLE staff_details CHANGE `permanent_address` `permanent_address_1` text;
ALTER TABLE staff_details CHANGE `city` `permanent_city` varchar(256);
ALTER TABLE staff_details CHANGE `state` `permanent_state` varchar(256);
ALTER TABLE staff_details CHANGE `zipcode` `permanent_zipcode` varchar(15);
ALTER TABLE staff_details CHANGE `current_address` `present_address_1` text;
ALTER TABLE staff_details CHANGE `secondary_contact_number` `alternate_number` varchar(225);
ALTER TABLE staff_details CHANGE `emergancy_no` `emergency_contact_number` varchar(225);
ALTER TABLE staff_details CHANGE `qualification` `highest_qualification` text;
ALTER TABLE staff_details CHANGE `ex_organization_name` `last_organization_name` varchar(500);
ALTER TABLE staff_details CHANGE `ex_location` `last_organization_address` text;
ALTER TABLE staff_details CHANGE `ex_designation` `last_designation` varchar(225);

//add columns
ALTER TABLE staff_details
ADD COLUMN department_designation_mapping text AFTER gender,
ADD COLUMN staff_category_id varchar(36) AFTER gender,
ADD COLUMN permanent_address_2 text AFTER permanent_address_1,
ADD COLUMN permanent_country varchar(256) AFTER permanent_state,
ADD COLUMN present_zipcode varchar(15) AFTER present_address_1,
ADD COLUMN present_country varchar(256) AFTER present_address_1,
ADD COLUMN present_state varchar(256) AFTER present_address_1,
ADD COLUMN present_city varchar(256) AFTER present_address_1,
ADD COLUMN present_address_2 text AFTER present_address_1,
ADD COLUMN secondary_email varchar(500) AFTER primary_email,
ADD COLUMN last_job_duration varchar(100) AFTER last_designation,
ADD COLUMN account_type varchar(100),
ADD COLUMN bank_name varchar(1020),
ADD COLUMN account_holder_name varchar(500),
ADD COLUMN account_number varchar(500),
ADD COLUMN ifsc_code varchar(500);

//make columns not null
ALTER TABLE staff_details CHANGE gender gender varchar(15) NOT NULL;
ALTER TABLE staff_details CHANGE primary_contact_number primary_contact_number varchar(225) NOT NULL;
ALTER TABLE staff_details CHANGE primary_email primary_email varchar(500) NOT NULL;
ALTER TABLE staff_details CHANGE staff_category_id staff_category_id varchar(36) NOT NULL;

//foreign key constrain
ALTER TABLE staff_details ADD FOREIGN KEY (staff_category_id) REFERENCES staff_category(staff_category_id);





select distinct CONSTRAINT_NAME from information_schema.TABLE_CONSTRAINTS where table_name = 'staff_category' and constraint_type = 'UNIQUE';
alter table staff_category drop index institute_id;
alter table staff_category add unique key (institute_id, staff_category_name);



select distinct CONSTRAINT_NAME from information_schema.TABLE_CONSTRAINTS where table_name = 'staff_department' and constraint_type = 'UNIQUE';
alter table staff_department drop index institute_id;
alter table staff_department add unique key (institute_id, staff_department_name);


select distinct CONSTRAINT_NAME from information_schema.TABLE_CONSTRAINTS where table_name = 'staff_designation' and constraint_type = 'UNIQUE';
alter table staff_designation drop index institute_id;
alter table staff_designation add unique key (institute_id, staff_designation_name);