SET @institute_id := 10300;
SET @academic_session_id := 179;
SET @course_type := "SCHOLASTIC";

--insert into standards_metadata select @institute_id, @academic_session_id , standard_id, 1, 0 from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A', 9, 0.855, 1.1, "86 - 100" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B', 8, 0.705, 0.855, "71 - 85" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C', 7, 0.505, 0.705, "51 - 70" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 6, 0.305, 0.505, "31 - 50" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'E', 5, 0.0 , 0.305, "0 - 30" from standards where institute_id = @institute_id;

-- Setting COSCHOLASTIC Grades
SET @course_type := "COSCHOLASTIC";
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A', 9, 0.855, 1.1, "86 - 100" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B', 8, 0.705, 0.855, "71 - 85" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C', 7, 0.505, 0.705, "51 - 70" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 6, 0.325, 0.505, "33 - 50" from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'E', 5, 0.0 , 0.325, "0 - 32" from standards where institute_id = @institute_id;

select * from examination_grades where institute_id = 10300;