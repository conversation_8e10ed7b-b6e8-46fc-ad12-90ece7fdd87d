select * from academic_session where institute_id = 10240;
select * from standard_section_mapping where academic_session_id = 134;
select * from standards_metadata where academic_session_id = 134;
select * from examination_grades where academic_session_id = 134;

--select standard_id, 210, section_name from standard_section_mapping where academic_session_id = 134;
--insert into standard_section_mapping(standard_id, academic_session_id, section_name) select standard_id, 210, section_name from standard_section_mapping where academic_session_id = 134;
--
--select institute_id, 210, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 134;
--insert into standards_metadata select institute_id, 210, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 134;
--
--select institute_id, 210, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 134;
--insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name) select institute_id, 210, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 134;

select * from academic_session where institute_id = 10240;
select * from standard_section_mapping where academic_session_id = 210;
select * from standards_metadata where academic_session_id = 210;
select * from examination_grades where academic_session_id = 210;



select * from academic_session where institute_id = 10241;
select * from standard_section_mapping where academic_session_id = 135;
select * from standards_metadata where academic_session_id = 135;
select * from examination_grades where academic_session_id = 135;

--select standard_id, 211, section_name from standard_section_mapping where academic_session_id = 135;
--insert into standard_section_mapping(standard_id, academic_session_id, section_name) select standard_id, 211, section_name from standard_section_mapping where academic_session_id = 135;
--
--select institute_id, 211, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 135;
--insert into standards_metadata select institute_id, 211, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 135;
--
--select institute_id, 211, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 135;
--insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name) select institute_id, 211, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 135;

select * from academic_session where institute_id = 10241;
select * from standard_section_mapping where academic_session_id = 211;
select * from standards_metadata where academic_session_id = 211;
select * from examination_grades where academic_session_id = 211;
