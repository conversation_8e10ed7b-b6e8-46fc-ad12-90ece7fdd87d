use lernen

INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month)
values(10001, 2019, 2020, 4, 3);

INSERT INTO fee_head_configuration(institute_id, fee_category_id, fee_head, refundable, description, fee_head_type, fee_head_tag) 
VALUES(10001, (SELECT fee_category_id from fee_category where institute_id = 10001 and fee_category = 'Transport Fee'), 'Transport', 1, null, 'SYSTEM', 'TRANSPORT');


insert into counters (institute_id, counter_type, count, counter_prefix) values (10001, 'FEE_INVOICE', 1, "");
