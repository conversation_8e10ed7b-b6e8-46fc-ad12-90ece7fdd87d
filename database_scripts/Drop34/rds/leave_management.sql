CREATE TABLE IF NOT EXISTS leave_type (
    institute_id int NOT NULL,
    leave_type_id int PRIMARY KEY AUTO_INCREMENT NOT NULL,
    name varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    short_code varchar(32) NOT NULL,
    payment_type varchar(32) NOT NULL,
    description text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    metadata text,
    added_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY (institute_id, name),
    UNIQUE KEY (institute_id, short_code),
    FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
    INDEX (institute_id)
);


CREATE TABLE IF NOT EXISTS leave_policy_template (
    institute_id int NOT NULL,
    academic_session_id int NOT NULL,
    template_id varchar(36) PRIMARY KEY NOT NULL,
    name varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    type varchar(36) NOT NULL,  -- custom leave policy type
    description text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    metadata text,
    added_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY (institute_id, academic_session_id, name),
    INDEX (institute_id, academic_session_id),
    FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
    FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id)
);

CREATE TABLE IF NOT EXISTS leave_policy_template_mapping (
    institute_id int NOT NULL,
    template_id varchar(36) NOT NULL,
    leave_type_id int NOT NULL,
    policy_details text,
    metadata text,
    PRIMARY KEY (template_id, leave_type_id),
    FOREIGN KEY (template_id) REFERENCES leave_policy_template(template_id),
    FOREIGN KEY (leave_type_id) REFERENCES leave_type(leave_type_id)
);

CREATE TABLE IF NOT EXISTS user_leave_policy (
    institute_id int NOT NULL,
    user_id varchar(36) NOT NULL,
    academic_session_id int NOT NULL,
    leave_type_id int NOT NULL,
    policy_details text,
    metadata text,
    PRIMARY KEY (user_id, academic_session_id, leave_type_id),
    FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
    FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id),
    FOREIGN KEY (leave_type_id) REFERENCES leave_type(leave_type_id),
    INDEX (institute_id, academic_session_id)
);

CREATE TABLE IF NOT EXISTS user_leave_balance (
    institute_id int NOT NULL,
    academic_session_id int NOT NULL,
    user_id varchar(36) NOT NULL,
    leave_type_id int NOT NULL,
    opening_balance double NOT NULL DEFAULT 0,
    total_credit_count double NOT NULL DEFAULT 0,
    total_debit_count double NOT NULL DEFAULT 0,
    PRIMARY KEY (user_id, academic_session_id, leave_type_id),
    FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
    FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id),
    FOREIGN KEY (leave_type_id) REFERENCES leave_type(leave_type_id),
    INDEX (institute_id, academic_session_id)
);

CREATE TABLE IF NOT EXISTS user_leave_transaction_metadata (
    institute_id int NOT NULL,
    transaction_id varchar(36) PRIMARY KEY NOT NULL,
    user_id varchar(36) NOT NULL,
    academic_session_id int NOT NULL,
    type varchar(36) NOT NULL, -- Debit/Credit
    status varchar(36) NOT NULL, -- APPROVED/PENDING/DELETED
    category varchar(36) NOT NULL, -- POLICY_GRANT/POLICY_EXPIRE/USER_APPLIED
    applied_by varchar(36),
    applied_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    description text,
    reviewed_by varchar(36),
    reviewed_at timestamp NULL DEFAULT NULL,
    review_remarks text,
    FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
    FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id),
    INDEX (institute_id, academic_session_id)
);

CREATE TABLE IF NOT EXISTS user_leave_transaction_mapping (
    institute_id int NOT NULL,
    transaction_id varchar(36) PRIMARY KEY NOT NULL,
    leave_type_id int NOT NULL,
    start_date int NOT NULL,
    leave_count double NOT NULL,
    FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
    FOREIGN KEY (transaction_id) REFERENCES user_leave_transaction_metadata(transaction_id),
    FOREIGN KEY (leave_type_id) REFERENCES leave_type(leave_type_id),
    INDEX (institute_id)
);

