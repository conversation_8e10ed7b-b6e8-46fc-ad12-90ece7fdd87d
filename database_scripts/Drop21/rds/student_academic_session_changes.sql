ALTER TABLE student_academic_session_details ADD height varchar(50);
ALTER TABLE student_academic_session_details ADD weight varchar(50);

update student_academic_session_details sasd, students s set 
sasd.height = s.height, sasd.weight = s.weight 
where sasd.student_id = s.student_id and  sasd.academic_session_id = 
(select academic_session_id from academic_session where institute_id = ?  order by end_year desc limit 1);


--NOT RIGHT NOW, WILL REMOVE THESE COLUMNS IN FUTURE
--ALTER TABLE students DROP COLUMN height;
--ALTER TABLE students DROP COLUMN weight;