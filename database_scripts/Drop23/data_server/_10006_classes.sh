INSTITUTE_STANDARDS_URL=$1/2.0/institute/standard

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10006",
  "academicSessionId" : 45,
  "standardName": "LKG",
  "stream" : "NA",
  "level" : 1,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10006",
  "academicSessionId" : 45,
  "standardName": "UKG",
  "stream" : "NA",
  "level" : 2,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL


curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10006",
  "academicSessionId" : 45,
  "standardName": "Prep",
  "stream" : "NA",
  "level" : 3,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10006",
  "academicSessionId" : 45,
  "standardName": "Class I",
  "stream" : "NA",
  "level" : 4,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10006",
  "academicSessionId" : 45,
  "standardName": "Class II",
  "stream" : "NA",
  "level" : 5,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL


curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10006",
  "academicSessionId" : 45,
  "standardName": "Class III",
  "stream" : "NA",
  "level" : 6,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL


curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10006",
  "academicSessionId" : 45,
  "standardName": "Class IV",
  "stream" : "NA",
  "level" : 7,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL


curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10006",
  "academicSessionId" : 45,
  "standardName": "Class V",
  "stream" : "NA",
  "level" : 8,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10006",
  "academicSessionId" : 45,
  "standardName": "Class VI",
  "stream" : "NA",
  "level" : 9,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10006",
  "academicSessionId" : 45,
  "standardName": "Class VII",
  "stream" : "NA",
  "level" : 10,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10006",
  "academicSessionId" : 45,
  "standardName": "Class VIII",
  "stream" : "NA",
  "level" : 11,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10006",
  "academicSessionId" : 45,
  "standardName": "Class IX",
  "stream" : "NA",
  "level" : 12,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10006",
  "academicSessionId" : 45,
  "standardName": "Class X",
  "stream" : "NA",
  "level" : 13,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL