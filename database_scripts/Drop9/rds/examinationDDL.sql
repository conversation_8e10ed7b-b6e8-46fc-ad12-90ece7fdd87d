ALTER TABLE `marks_feeding` ADD COLUMN `status` VARCHAR(255) NULL AFTER `grace_marks`;
-- Needed for migration
-- UPDATE marks_feeding SET status = "SAVED";


CREATE TABLE IF NOT EXISTS standards_metadata (
institute_id int NOT NULL,
academic_session_id int NOT NULL,
standard_id varchar(36) NOT NULL,
coScholasticGradeEnabled tinyint(1) DEFAULT 0,
PRIMARY KEY (academic_session_id, standard_id),
FOREIGN KEY (standard_id) REFERENCES standards(standard_id),
FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id)
);


CREATE TABLE IF NOT EXISTS examination_grades (
grade_id int PRIMARY KEY AUTO_INCREMENT,
institute_id int NOT NULL,
academic_session_id int NOT NULL,
standard_id varchar(36) NOT NULL,
course_type varchar(36) NOT NULL,
grade_name varchar(36) NOT NULL,
grade_value double NOT NULL,
marks_range_start double,
marks_range_end double,
UNIQUE KEY (academic_session_id, standard_id, course_type, grade_name),
FOREIGN KEY (standard_id) REFERENCES standards(standard_id),
FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id)
);

ALTER TABLE standards_metadata change `coScholasticGradeEnabled` `coscholastic_grade_enabled` tinyint(1) DEFAULT 0;
ALTER TABLE standards_metadata add column `scholastic_grade_enabled` tinyint(1) DEFAULT 0;

ALTER TABLE exam_report_structure add column `report_card_name` varchar(512) NOT NULL after `report_type`;



---- Following queries are to migrate from old exam module to new one. Earlier both Grade and Number dimension for total were not getting created
---- Update exam_dimensions_mapping and exam_courses_assignment to have both type of total dimensions. Use these queries carefully
-- mysql> select * from exam_dimensions where institute_id = y;
-- +--------------+--------------+------------------+----------------+-----------------+----------+
-- | dimension_id | institute_id | dimension_name   | dimension_type | evaluation_type | is_total |
-- +--------------+--------------+------------------+----------------+-----------------+----------+
-- |            8 |        10025 | ग्रेड            | SYSTEM         | GRADE           |        1 |
-- |           10 |        10025 | मौखिक            | CUSTOM         | NUMBER          |        0 |
-- |            7 |        10025 | योग              | SYSTEM         | NUMBER          |        1 |
-- |            9 |        10025 | लिखित            | CUSTOM         | NUMBER          |        0 |
-- +--------------+--------------+------------------+----------------+-----------------+----------+
-- 4 rows in set (0.00 sec)

-- 182
-- 126, 56
-- 56


-- mysql> select * from exam_dimensions where institute_id = y;
-- +--------------+--------------+----------------+----------------+-----------------+----------+
-- | dimension_id | institute_id | dimension_name | dimension_type | evaluation_type | is_total |
-- +--------------+--------------+----------------+----------------+-----------------+----------+
-- |            2 |        y | Grade          | SYSTEM         | GRADE           |        1 |
-- |            4 |        y | Oral           | CUSTOM         | NUMBER          |        0 |
-- |            1 |        y | Total          | SYSTEM         | NUMBER          |        1 |
-- |            3 |        y | Written        | CUSTOM         | NUMBER          |        0 |
-- +--------------+--------------+----------------+----------------+-----------------+----------+

-- select * from exam_dimensions_mapping where exam_id in (select exam_id from exam_metadata where standard_id in (select standard_id from standards where institute_id= y));
-- select * from exam_dimensions_mapping where course_type ='SCHOLASTIC' and dimension_id=1 and exam_id in (select exam_id from exam_metadata where standard_id in (select standard_id from standards where institute_id= y));

-- -- insert into exam_dimensions_mapping select exam_id, course_type, x , null,null,null,null from exam_dimensions_mapping where course_type ='SCHOLASTIC' and dimension_id = x and exam_id in (select exam_id from exam_metadata where standard_id in (select standard_id from standards where institute_id= y));
-- -- insert into exam_dimensions_mapping select exam_id, course_type, x , null,null,null,null from exam_dimensions_mapping where course_type ='COSCHOLASTIC' and dimension_id = x and exam_id in (select exam_id from exam_metadata where standard_id in (select standard_id from standards where institute_id= y));



-- -- insert into exam_dimensions_mapping select exam_id, course_type, grade_dimension , null,null,null,null from exam_dimensions_mapping where course_type ='SCHOLASTIC' and dimension_id= nd and exam_id in (select exam_id from exam_metadata where standard_id in (select standard_id from standards where institute_id= y));
-- -- insert into exam_dimensions_mapping select exam_id, course_type, number_dimension , null,null,null,null from exam_dimensions_mapping where course_type ='COSCHOLASTIC' and dimension_id= gd and exam_id in (select exam_id from exam_metadata where standard_id in (select standard_id from standards where institute_id= y));










-- select * from exam_courses_assignment where exam_id in (select exam_id from exam_metadata where standard_id in (select standard_id from standards where institute_id= y));
-- select * from exam_courses_assignment where dimension_id = x and exam_id in (select exam_id from exam_metadata where standard_id in (select standard_id from standards where institute_id= y));

-- select exam_courses_assignment.* from exam_courses_assignment join class_courses on class_courses.course_id = exam_courses_assignment.course_id where dimension_id = x and course_type ='SCHOLASTIC' and exam_id in (select exam_id from exam_metadata where standard_id in (select standard_id from standards where institute_id= y));



-- -- insert into exam_courses_assignment select exam_id, exam_courses_assignment.course_id, x, null, null, null, null from  exam_courses_assignment join class_courses on class_courses.course_id = exam_courses_assignment.course_id where dimension_id = x and course_type ='SCHOLASTIC' and exam_id in (select exam_id from exam_metadata where standard_id in (select standard_id from standards where institute_id= y));


-- -- insert into exam_courses_assignment select exam_id, course_id, x, null, null, null, null from exam_courses_assignment where dimension_id = x and exam_id in (select exam_id from exam_metadata where standard_id in (select standard_id from standards where institute_id= y));



-- -- insert into exam_courses_assignment select exam_id, course_id, grade_dimension null, null, null, null from exam_courses_assignment where dimension_id=nd and exam_id in (select exam_id from exam_metadata where standard_id in (select standard_id from standards where institute_id= y));
-- -- insert into exam_courses_assignment select exam_id, course_id, number_dimension null, null, null, null from exam_courses_assignment where dimension_id=gd and exam_id in (select exam_id from exam_metadata where standard_id in (select standard_id from standards where institute_id= y));

