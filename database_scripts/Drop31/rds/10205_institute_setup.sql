INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country, zipcode, landmark, logo_url, email, phone_number, letter_head_line1, letter_head_line2, institute_unique_code)
VALUES (10205, 'NPS INTERNATIONAL SCHOOL', 'ROZA JALALPUR, TECH ZONE - 7', 'GREATER NODIA (WEST)', 'G.B.NAGAR', 'U.P.', 'INDIA', '201306', 'NEAR VIRAT BUILDERS', '', '<EMAIL>', '9958780504,9953466668', 'ROZ<PERSON> JALALPUR, TECH ZONE - 7, NEAR VIRAT BUILDERS', 'GREATER NODIA (WEST), G.B.NAGAR, U.P. - 201306', UUID());

INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month)
values(10205, 2022, 2023, 4, 3);

insert into fee_category (institute_id, fee_category, description) values (10205, 'Academic Fee' ,'Academic Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10205, 'Hostel Fee' ,'Hostel Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10205, 'Transport Fee' ,'Transport Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10205, 'Admission Fee' ,'Admission Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10205, 'Examination Fee' ,'Examination Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10205, 'Miscellaneous Fee' ,'Miscellaneous Fees');
insert into fee_category (institute_id, fee_category, description) values (10205, 'Other Fee' ,'Any Other Fees');


INSERT INTO fee_head_configuration(institute_id, fee_category_id, fee_head, refundable, description, fee_head_type, fee_head_tag)
VALUES(10205, (SELECT fee_category_id from fee_category where institute_id = 10205 and fee_category = 'Transport Fee'), 'Transport', 1, null, 'SYSTEM', 'TRANSPORT');


insert into categories (institute_id,category_name,genders) values (10205,'stationery',0);
insert into categories (institute_id,category_name,genders) values (10205,'books',0);
insert into categories (institute_id,category_name) values (10205,'clothing');
insert into categories (institute_id,category_name) values (10205,'foot wear');
insert into categories (institute_id,category_name,genders,colors) values (10205,'note book',0,0);
insert into categories (institute_id,category_name) values (10205,'art & craft');
insert into categories (institute_id,category_name) values (10205,'personal care');
insert into categories (institute_id,category_name) values (10205,'bedding');
insert into categories (institute_id,category_name,genders,colors) values (10205,'energy drinks',0,0);
insert into categories (institute_id,category_name) values (10205,'accessories');
insert into categories (institute_id,category_name) values (10205,'furniture');
insert into categories (institute_id,category_name) values (10205,'electronics');
insert into categories (institute_id,category_name) values (10205,'sports');


insert into colors (institute_id, color_name) values (10205,'maroon');
insert into colors (institute_id, color_name) values (10205,'black');
insert into colors (institute_id, color_name) values (10205,'brown');
insert into colors (institute_id, color_name) values (10205,'white');
insert into colors (institute_id, color_name) values (10205,'red');
insert into colors (institute_id, color_name) values (10205,'yellow');
insert into colors (institute_id, color_name) values (10205,'blue');
insert into colors (institute_id, color_name) values (10205,'navy blue');
insert into colors (institute_id, color_name) values (10205,'green');
insert into colors (institute_id, color_name) values (10205,'dark green');
insert into colors (institute_id, color_name) values (10205,'pink');
insert into colors (institute_id, color_name) values (10205,'purple');
insert into colors (institute_id, color_name) values (10205,'grey');
insert into colors (institute_id, color_name) values (10205,'olive');
insert into colors (institute_id, color_name) values (10205,'cyan');
insert into colors (institute_id, color_name) values (10205,'magenta');


insert into counters (institute_id, counter_type, count, counter_prefix) values (10205, 'FEE_INVOICE', 1, "");
insert into counters (institute_id, counter_type, count, counter_prefix) values (10205, 'AUDIO_VOICE_CALL_COUNTER', 0, "");
insert into counters (institute_id, counter_type, count, counter_prefix) values (10205, 'SMS_COUNTER', 0, "");
insert into counters (institute_id, counter_type, count, counter_prefix) values (10205, 'REGISTRATION_NUMBER', 1, 'REG - ');
insert into counters (institute_id, counter_type, count, counter_prefix) values (10205, 'ADMISSION_NUMBER', 1, 'NPS-');
--insert into counters (institute_id, counter_type, count, counter_prefix) values (10205, 'STAFF_NUMBER', 1, 'Staff - ');

insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10205', 'meta_data', 'enable_permission', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10205', 'exam_admit_card_preferences', 'include_class_roll_number', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10205', 'meta_data', 'institute_name_in_sms', 'NPS INTERNATIONAL SCHOOL');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10205', 'meta_data', 'institute_unique_code', 'nps205');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10205', 'meta_data', 'sms_service_enabled', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10205', 'sms_preferences', 'buffer_sms_count', 0);
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10205', 'inventory_preferences', 'inventory_institutes_scope', '[10205]');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10205', 'meta_data', 'registration_counter', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10205', 'meta_data', 'admission_counter', 'true');
--insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10205', 'meta_data', 'staff_counter', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10205', 'meta_data', 'module_access', '["STORE","FEES","ADMISSION","TRANSPORT","COURSES","EXAMINATION","ATTENDANCE","STAFF_MANAGEMENT","INCOME_EXPENSE","USER_MANAGEMENT","HOMEWORK_MANAGEMENT","NOTICE_BOARD_MANAGEMENT","STUDENT_MANAGEMENT","COMMUNICATION","TIMETABLE_MANAGEMENT"]');
--["STORE","FEES","ADMISSION","TRANSPORT","COURSES","EXAMINATION","ATTENDANCE","STAFF_MANAGEMENT","INCOME_EXPENSE","USER_MANAGEMENT","LECTURE_MANAGEMENT","HOMEWORK_MANAGEMENT","SALARY_MANAGEMENT","NOTICE_BOARD_MANAGEMENT","STUDENT_MANAGEMENT","AUDIT_LOGS","COMMUNICATION","TIMETABLE_MANAGEMENT","STAFF_ATTENDANCE"]
--insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10205', 'sms_preferences', 'student_registration_sms_enabled', 'true');
--insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10205', 'sms_preferences', 'student_admission_sms_enabled', 'true');
--insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10205', 'sms_preferences', 'fee_payment_cancellation_admin_sms_enabled', 'true');
--insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10205', 'sms_preferences', 'fee_payment_cancellation_admin_sms_contact_number', '9462267596');
--insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10205', 'meta_data', 'birthday_sms_enabled', 'true');

insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(10205, "Total", "SYSTEM", "NUMBER", 1);
insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(10205, "Grade", "SYSTEM", "GRADE", 1);

insert into default_fee_assignment_structure_meta_data values(10205, 87, "Installments", UUID(), "ENROLLMENT");
insert into default_fee_assignment_structure select "29b97112-4fa5-11ed-a9b7-120cd1ce09b5", (select standard_id from standards where institute_id=10205 and standard_name = 'NURSERY'), "CLASS", fee_id , 274, 800 from fee_configuration where institute_id = 10205 and fee_type = "REGULAR" and academic_session_id = 87;
insert into default_fee_assignment_structure select "29b97112-4fa5-11ed-a9b7-120cd1ce09b5", (select standard_id from standards where institute_id=10205 and standard_name = 'LKG'), "CLASS", fee_id , 274, 800 from fee_configuration where institute_id = 10205 and fee_type = "REGULAR" and academic_session_id = 87;
insert into default_fee_assignment_structure select "29b97112-4fa5-11ed-a9b7-120cd1ce09b5", (select standard_id from standards where institute_id=10205 and standard_name = 'UKG'), "CLASS", fee_id , 274, 800 from fee_configuration where institute_id = 10205 and fee_type = "REGULAR" and academic_session_id = 87;
insert into default_fee_assignment_structure select "29b97112-4fa5-11ed-a9b7-120cd1ce09b5", (select standard_id from standards where institute_id=10205 and standard_name = 'I'), "CLASS", fee_id , 274, 850 from fee_configuration where institute_id = 10205 and fee_type = "REGULAR" and academic_session_id = 87;
insert into default_fee_assignment_structure select "29b97112-4fa5-11ed-a9b7-120cd1ce09b5", (select standard_id from standards where institute_id=10205 and standard_name = 'II'), "CLASS", fee_id , 274, 850 from fee_configuration where institute_id = 10205 and fee_type = "REGULAR" and academic_session_id = 87;
insert into default_fee_assignment_structure select "29b97112-4fa5-11ed-a9b7-120cd1ce09b5", (select standard_id from standards where institute_id=10205 and standard_name = 'III'), "CLASS", fee_id , 274, 900 from fee_configuration where institute_id = 10205 and fee_type = "REGULAR" and academic_session_id = 87;
insert into default_fee_assignment_structure select "29b97112-4fa5-11ed-a9b7-120cd1ce09b5", (select standard_id from standards where institute_id=10205 and standard_name = 'IV'), "CLASS", fee_id , 274, 900 from fee_configuration where institute_id = 10205 and fee_type = "REGULAR" and academic_session_id = 87;
insert into default_fee_assignment_structure select "29b97112-4fa5-11ed-a9b7-120cd1ce09b5", (select standard_id from standards where institute_id=10205 and standard_name = 'V'), "CLASS", fee_id , 274, 900 from fee_configuration where institute_id = 10205 and fee_type = "REGULAR" and academic_session_id = 87;
insert into default_fee_assignment_structure select "29b97112-4fa5-11ed-a9b7-120cd1ce09b5", (select standard_id from standards where institute_id=10205 and standard_name = 'VI'), "CLASS", fee_id , 274, 950 from fee_configuration where institute_id = 10205 and fee_type = "REGULAR" and academic_session_id = 87;
insert into default_fee_assignment_structure select "29b97112-4fa5-11ed-a9b7-120cd1ce09b5", (select standard_id from standards where institute_id=10205 and standard_name = 'VII'), "CLASS", fee_id , 274, 950 from fee_configuration where institute_id = 10205 and fee_type = "REGULAR" and academic_session_id = 87;
insert into default_fee_assignment_structure select "29b97112-4fa5-11ed-a9b7-120cd1ce09b5", (select standard_id from standards where institute_id=10205 and standard_name = 'IX'), "CLASS", fee_id , 274, 1150 from fee_configuration where institute_id = 10205 and fee_type = "REGULAR" and academic_session_id = 87;
--insert into default_fee_assignment_structure select "1dd925f4-4137-4eda-96f5-eebb8467d42d", (select standard_id from standards where institute_id=10205 and standard_name = 'X'), "CLASS", fee_id , 202, 2700 from fee_configuration where institute_id = 10205 and fee_type = "REGULAR" and academic_session_id = 87;
--insert into default_fee_assignment_structure select "1dd925f4-4137-4eda-96f5-eebb8467d42d", (select standard_id from standards where institute_id=10205 and standard_name = 'XI' and stream = 'SCIENCE'), "CLASS", fee_id , 202, 3480 from fee_configuration where institute_id = 10205 and fee_type = "REGULAR" and academic_session_id = 87;
--insert into default_fee_assignment_structure select "1dd925f4-4137-4eda-96f5-eebb8467d42d", (select standard_id from standards where institute_id=10205 and standard_name = 'XI' and stream = 'COMMERCE'), "CLASS", fee_id , 202, 3180 from fee_configuration where institute_id = 10205 and fee_type = "REGULAR" and academic_session_id = 87;
--insert into default_fee_assignment_structure select "1dd925f4-4137-4eda-96f5-eebb8467d42d", (select standard_id from standards where institute_id=10205 and standard_name = 'XII' and stream = 'SCIENCE'), "CLASS", fee_id , 202, 3400 from fee_configuration where institute_id = 10205 and fee_type = "REGULAR" and academic_session_id = 87;
--insert into default_fee_assignment_structure select "1dd925f4-4137-4eda-96f5-eebb8467d42d", (select standard_id from standards where institute_id=10205 and standard_name = 'XII' and stream = 'COMMERCE'), "CLASS", fee_id , 202, 3100 from fee_configuration where institute_id = 10205 and fee_type = "REGULAR" and academic_session_id = 87;

--Insert classes
-- cd Lernen/lernen-backend/database_scripts/Drop31/data-server/
-- sh _10205_classes.sh http://api.embrate.com:8080/data-server 0006cba5-a7d7-4acb-9819-dd9ce01d9852
insert into standards_metadata select 10205, 87, standard_id, 0, 0 from standards where institute_id = 10205;

select standard_id, 10205, section_name from standard_section_mapping where academic_session_id = 87;
insert into standard_section_mapping(standard_id, academic_session_id, section_name) values("7c693e4f-ec9e-41b8-b974-4c885f141836", 87, "B");

--lkg
insert into standard_section_mapping(standard_id, academic_session_id, section_name) values("b00d92c5-f3f1-4516-82a5-7c4a15ff12c0", 87, "B");
insert into standard_section_mapping(standard_id, academic_session_id, section_name) values("b00d92c5-f3f1-4516-82a5-7c4a15ff12c0", 87, "C");

-- ukg
insert into standard_section_mapping(standard_id, academic_session_id, section_name) values("7f8691d9-b91b-4f75-ac66-dc060ab8a8da", 87, "B");
insert into standard_section_mapping(standard_id, academic_session_id, section_name) values("7f8691d9-b91b-4f75-ac66-dc060ab8a8da", 87, "C");

insert into standard_section_mapping(standard_id, academic_session_id, section_name) values("c43454e3-32a1-407e-a518-1e9866a48e5a", 87, "B");

insert into standard_section_mapping(standard_id, academic_session_id, section_name) values("d8d2fc06-6101-4ed2-ad61-4aa1421ace6b", 87, "B");
insert into standard_section_mapping(standard_id, academic_session_id, section_name) values("d8d2fc06-6101-4ed2-ad61-4aa1421ace6b", 87, "C");


insert into standard_section_mapping(standard_id, academic_session_id, section_name) values("7febb679-f75b-4544-bdcf-0a5997a108f9", 87, "B");

insert into standard_section_mapping(standard_id, academic_session_id, section_name) values("021a156a-f310-42d5-a041-760478d0b337", 87, "B");

insert into standard_section_mapping(standard_id, academic_session_id, section_name) values("a5de8535-37d5-459d-b499-4d473db59a3a", 87, "B");

insert into standard_section_mapping(standard_id, academic_session_id, section_name) values("873bcbe7-9748-4b4f-99d9-e5db02807f78", 87, "B");


--add range display name also
--SET @institute_id := 10205;
--SET @academic_session_id := 87;
--SET @course_type := "SCHOLASTIC";
--
--insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
--select @institute_id, @academic_session_id, standard_id, @course_type, 'A1', 9, 0.905, 1.1, '91-100' from standards where institute_id = @institute_id;
--
--insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
--select @institute_id, @academic_session_id, standard_id, @course_type, 'A2', 8, 0.805, 0.905, '81-90' from standards where institute_id = @institute_id;
--
--insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
--select @institute_id, @academic_session_id, standard_id, @course_type, 'B1', 7, 0.705, 0.805, '71-80' from standards where institute_id = @institute_id;
--
--insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
--select @institute_id, @academic_session_id, standard_id, @course_type, 'B2', 6, 0.605, 0.705, '61-70' from standards where institute_id = @institute_id;
--
--insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
--select @institute_id, @academic_session_id, standard_id, @course_type, 'C1', 5, 0.505 , 0.605, '51-60' from standards where institute_id = @institute_id;
--
--insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
--select @institute_id, @academic_session_id, standard_id, @course_type, 'C2', 4, 0.405 , 0.505, '41-50' from standards where institute_id = @institute_id;
--
--insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
--select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 3, 0.325 , 0.405, '33-40' from standards where institute_id = @institute_id;
--
--insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
--select @institute_id, @academic_session_id, standard_id, @course_type, 'E1', 0, 0.205 , 0.325, '21-32' from standards where institute_id = @institute_id;
--
--insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
--select @institute_id, @academic_session_id, standard_id, @course_type, 'E2', 0, 0.0 , 0.205, '00-20' from standards where institute_id = @institute_id;
--
--
---- Setting CO-SCHOLASTIC Grades
--SET @course_type := "COSCHOLASTIC";
--
--insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
--select @institute_id, @academic_session_id, standard_id, @course_type, 'A', 10, 0.9, 1.1, '91-100' from standards where institute_id = @institute_id;
--
--insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
--select @institute_id, @academic_session_id, standard_id, @course_type, 'B', 9, 0.8, 0.9, '81-90' from standards where institute_id = @institute_id;
--
--insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
--select @institute_id, @academic_session_id, standard_id, @course_type, 'C', 8, 0.7, 0.8, '71-80' from standards where institute_id = @institute_id;
--
--insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
--select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 7, 0.6, 0.7, '61-70' from standards where institute_id = @institute_id;
--
--insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
--select @institute_id, @academic_session_id, standard_id, @course_type, 'E', 6, 0.0, 0.6, '00-60' from standards where institute_id = @institute_id;


--select "348bfd28-7193-4cf1-ac4b-18ebb0ce1b4d", (select standard_id from standards where institute_id=10205 and standard_name = 'Standard-VI'), "CLASS", fee_id , 150, 1400 from fee_configuration where institute_id = 10205 and fee_type = "REGULAR" and academic_session_id = 87;
--insert into default_fee_assignment_structure select "348bfd28-7193-4cf1-ac4b-18ebb0ce1b4d", (select standard_id from standards where institute_id=10205 and standard_name = 'Standard-VI'), "CLASS", fee_id , 150, 1400 from fee_configuration where institute_id = 10205 and fee_type = "REGULAR" and academic_session_id = 87;

--insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10205', 'web_ui_preferences', 'background_image_enable', 'true');
--insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10205', 'web_ui_preferences', 'background_image_url', '/static/core/images/10205_bg_logo.png');
--insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10205', 'web_ui_preferences', 'school_name_logo_position', 'CENTER');

--Add default exam
