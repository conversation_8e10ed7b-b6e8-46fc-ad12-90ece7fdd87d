CREATE TABLE IF NOT EXISTS standard_remarks (
        institute_id int NOT NULL,
        standard_remark_id varchar(36) PRIMARY KEY NOT NULL,
        title varchar(256) NOT NULL,
        description text,
        diary_remark_category_id int NOT NULL,
        FOREIGN KEY (diary_remark_category_id) REFERENCES diary_remarks_category(category_id),
        FOREIGN KEY (institute_id) REFERENCES institute(institute_id)
);