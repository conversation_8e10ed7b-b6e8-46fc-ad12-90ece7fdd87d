CREATE TABLE IF NOT EXISTS fee_assignment_permissions(
	fee_id varchar(36) NOT NULL,
	fee_head_id int NOT NULL,
	module_name varchar(64) NOT NULL,
	institute_id int NOT NULL,
	proportion_numerator double,
	proportion_denominator double,
	primary key(fee_id, fee_head_id, module_name),
	FOREIGN KEY (fee_id) REFERENCES fee_configuration(fee_id),
	FOREIGN KEY (fee_head_id) REFERENCES fee_head_configuration(fee_head_id)
);




