CREATE TABLE IF NOT EXISTS transport_area(
	institute_id int NOT NULL,
	area_id int PRIMARY KEY AUTO_INCREMENT NOT NULL,
	area varchar(1024) NOT NULL,
	area_key varchar(255) NOT NULL,
	UNIQUE KEY (institute_id, area_key)
);

CREATE TABLE IF NOT EXISTS transport_vehicle(
	institute_id int NOT NULL,
	vehicle_id int PRIMARY KEY AUTO_INCREMENT NOT NULL,
	vehicle_number varchar(255) NOT NULL,
	vehicle_code varchar(525) NOT NULL,
	registration_number varchar(525),
	vehicle_type varchar(100) NOT NULL,
	capacity int NOT NULL,
	active tinyint NOT NULL,
	UNIQUE KEY (institute_id, vehicle_code),
	UNIQUE KEY (institute_id, vehicle_number),
	UNIQUE KEY (institute_id, registration_number)
);

CREATE TABLE IF NOT EXISTS transport_service_route(
	institute_id int NOT NULL,
	service_route_id varchar(36) PRIMARY KEY NOT NULL,
	service_route_name varchar(1024) NOT NULL,
	vehicle_id int NOT NULL,
	UNIQUE KEY (institute_id, service_route_name),
	FOREIGN KEY (vehicle_id) REFERENCES transport_vehicle(vehicle_id)
);


CREATE TABLE IF NOT EXISTS transport_service_route_stoppages(
	service_route_id varchar(36) NOT NULL,
	area_id int NOT NULL,
	pickup_time varchar(255),
	drop_time varchar(255),
	assigned_amount double,
	PRIMARY KEY (service_route_id, area_id),
	FOREIGN KEY (service_route_id) REFERENCES transport_service_route(service_route_id),
	FOREIGN KEY (area_id) REFERENCES transport_area(area_id)
);


CREATE TABLE IF NOT EXISTS transport_history(
	institute_id int NOT NULL,
	transport_history_id varchar(36) PRIMARY KEY NOT NULL,
	academic_session_id int NOT NULL,
	student_id varchar(36) NOT NULL,
	service_route_id varchar(36) NOT NULL,
	area_id int NOT NULL,
	current_time_stamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
	start_date TIMESTAMP NULL DEFAULT NULL ,
	end_date TIMESTAMP NULL DEFAULT NULL,
	transport_status varchar(100),
	FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id),
	FOREIGN KEY (student_id) REFERENCES students(student_id),
	FOREIGN KEY (service_route_id) REFERENCES transport_service_route(service_route_id),
	FOREIGN KEY (area_id) REFERENCES transport_area(area_id)
);


CREATE TABLE IF NOT EXISTS transport_history_fee_id_mapping(
	transport_history_id varchar(36) NOT NULL,
	fee_id varchar(36) NOT NULL,
	amount double,
	PRIMARY KEY (transport_history_id, fee_id),
	FOREIGN KEY (transport_history_id) REFERENCES transport_history(transport_history_id),
	FOREIGN KEY (fee_id) REFERENCES fee_configuration(fee_id)
);