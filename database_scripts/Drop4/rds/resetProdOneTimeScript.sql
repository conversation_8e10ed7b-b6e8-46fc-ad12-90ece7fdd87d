drop table fee_payment_transaction_amounts;
drop table fee_payment_transactions;
drop table student_fee_payment_discounts;
drop table student_fee_payments;
drop table student_discount_configuration;
drop table fee_assignment;
drop table discount_fee_head_mapping;
drop table fee_discount_configuration;
drop table fee_configuration;

drop table fee_head_configuration;

drop table student_academic_session_details;
drop table students;

drop table academic_session;


-- Drop 2 ---------------------

CREATE TABLE IF NOT EXISTS academic_session(
		institute_id int NOT NULL,
		academic_session_id int PRIMARY KEY AUTO_INCREMENT NOT NULL,
		start_year int(4) NOT NULL,
		end_year int(4) NOT NULL,
		start_month int(2) NOT NULL,
		end_month int(2) NOT NULL,
		UNIQUE KEY (institute_id, start_year)
	);


CREATE TABLE IF NOT EXISTS students(
		institute_id int NOT NULL,
		registration_number varchar(225) NOT NULL,
		student_id varchar(36) PRIMARY KEY NOT NULL,
		admission_date TIMESTAMP NULL DEFAULT NULL,
		name varchar(225)NOT NULL,
		date_of_birth TIMESTAMP NULL DEFAULT NULL,
		birth_place varchar(225),
		gender varchar(15),
		caste varchar(256),
		rte tinyint(1) default 0,
		aadhar_number varchar(225),
		permanent_address text,
		city varchar(256),
		state varchar(256),
		zipcode varchar (15),
		father_name varchar(225),
		mother_name varchar(225),
		father_occupation varchar(225),
		mother_occupation varchar(225),
		father_contact_number varchar(225),
		mother_contact_number varchar(225),
		family_approx_income varchar(225),
		gardians_details text,
		previous_school_name varchar(225),
		class_passed varchar(225),
		previous_school_medium varchar(225),
		result varchar(225),
		percentage varchar(225),
		year_of_passing int,
		blood_group varchar(225),
		blood_pressure varchar(225),
		pulse varchar(225),
		height varchar(50),
		weight varchar(50),
		date_of_physical_examination TIMESTAMP NULL DEFAULT NULL,
		admission_academic_session int,
		UNIQUE KEY (institute_id, registration_number),
		FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
		FOREIGN KEY (admission_academic_session) REFERENCES academic_session(academic_session_id)
	);


	CREATE TABLE IF NOT EXISTS student_academic_session_details(
		academic_session_id int NOT NULL,
		student_id varchar(36) NOT NULL,
		standard_id varchar(36) NOT NULL,
		section_id int,
		roll_number varchar(225),
		primary key(academic_session_id, student_id),
		FOREIGN KEY (standard_id) REFERENCES standards(standard_id),
		FOREIGN KEY (student_id) REFERENCES students(student_id),
		FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id)
	);
	
	
	
	
	-- -----------------------------------------------
	
	-- FEES



	CREATE TABLE IF NOT EXISTS fee_configuration(
			institute_id int NOT NULL ,
			fee_id varchar(36) NOT NULL ,
			fee_name varchar(1024) ,
			fee_type varchar(225) ,
			due_date TIMESTAMP NULL DEFAULT NULL ,
			academic_session_id int NOT NULL,
			start_month int(2),
			start_year int,
			end_month int(2),
			end_year int,
			description text,
			primary key(fee_id),
			UNIQUE KEY (institute_id, academic_session_id, fee_name),
			FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id)
		);

	CREATE TABLE IF NOT EXISTS fee_category(
		fee_category_id int PRIMARY KEY AUTO_INCREMENT NOT NULL,
		institute_id int,
		fee_category varchar(1024) NOT NULL,
		description text,
		UNIQUE KEY (institute_id, fee_category)
	);


	CREATE TABLE IF NOT EXISTS fee_head_configuration(
		institute_id int NOT NULL,
		fee_head_id int PRIMARY KEY AUTO_INCREMENT,
		fee_category_id int NOT NULL,
		fee_head varchar(1024) NOT NULL,
		refundable TinyInt(1),
		description text,
		FOREIGN KEY (fee_category_id) REFERENCES fee_category(fee_category_id),
		UNIQUE KEY (institute_id, fee_head)
	);


	CREATE TABLE IF NOT EXISTS fee_discount_configuration(
		institute_id int NOT NULL,
		discount_id varchar(36) NOT NULL,
		discount_name varchar(1024) NOT NULL,
		variable TinyInt(1),
		description text,
		primary key(discount_id),
		UNIQUE KEY (institute_id, discount_name)
	);

	CREATE TABLE IF NOT EXISTS discount_fee_head_mapping(
		discount_id varchar(36) NOT NULL,
		fee_head_id int NOT NULL,
		is_percent tinyint(1) NOT NULL,
		amount double,
		primary key(discount_id, fee_head_id),
		FOREIGN KEY (discount_id) REFERENCES fee_discount_configuration(discount_id),
		FOREIGN KEY (fee_head_id) REFERENCES fee_head_configuration(fee_head_id)
	);


	CREATE TABLE IF NOT EXISTS fee_assignment(
		institute_id int NOT NULL,
		entity_id varchar(256) NOT NULL,
		entity_name varchar(36) NOT NULL,
		fee_id varchar(36) NOT NULL,
		fee_head_id int NOT NULL,
		amount double,
		primary key(institute_id, entity_id, fee_id, fee_head_id),
		FOREIGN KEY (fee_id) REFERENCES fee_configuration(fee_id),
		FOREIGN KEY (fee_head_id) REFERENCES fee_head_configuration(fee_head_id)
	);



	CREATE TABLE IF NOT EXISTS student_discount_configuration(
		institute_id int NOT NULL,
		student_id varchar(36) NOT NULL,
		academic_session_id int NOT NULL,
		discount_id varchar(36) NOT NULL,
		primary key(student_id, academic_session_id, discount_id),
		FOREIGN KEY (student_id) REFERENCES students(student_id),
		FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id),
		FOREIGN KEY (discount_id) REFERENCES fee_discount_configuration(discount_id)
	);



	CREATE TABLE IF NOT EXISTS student_fee_payments(
		institute_id int NOT NULL ,
		student_id varchar(36) NOT NULL,
		fee_id varchar(36) NOT NULL,
		fee_head_id int NOT NULL,
		assigned_amount double NOT NULL default 0,
		paid_amount double NOT NULL default 0,
		instant_discount_amount double NOT NULL default 0,
		transaction_count int NOT NULL default 0,
		discount_group_id varchar(36),
		primary key(student_id, fee_id, fee_head_id),
		unique key(discount_group_id),
		FOREIGN KEY (student_id) REFERENCES students(student_id),
		FOREIGN KEY (fee_id) REFERENCES fee_configuration(fee_id),
		FOREIGN KEY (fee_head_id) REFERENCES fee_head_configuration(fee_head_id)
	);


	CREATE TABLE IF NOT EXISTS student_fee_payment_discounts(
		discount_group_id varchar(36) NOT NULL,
		discount_id varchar(36) NOT NULL,
		discount_amount double NOT NULL,
		primary key(discount_group_id, discount_id),
		FOREIGN KEY (discount_group_id) REFERENCES student_fee_payments(discount_group_id),
		FOREIGN KEY (discount_id) REFERENCES fee_discount_configuration(discount_id)
	);



	CREATE TABLE IF NOT EXISTS fee_payment_transactions(
		institute_id int NOT NULL ,
		transaction_id varchar(36) NOT NULL,
		student_id varchar(36) NOT NULL,
		transaction_mode varchar(36) NOT NULL,
		transaction_reference varchar(256),
		transaction_date timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ,
		transaction_added_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ,
		status varchar(36) NOT NULL,
		description text,
		primary key(transaction_id),
		FOREIGN KEY (student_id) REFERENCES students(student_id)
	);

	CREATE TABLE IF NOT EXISTS fee_payment_transaction_amounts(
		transaction_id varchar(36) NOT NULL,
		fee_id varchar(36) NOT NULL,
		fee_head_id int NOT NULL,
		paid_amount double NOT NULL,
		instant_discount_amount double NOT NULL,
		primary key(transaction_id, fee_id, fee_head_id),
		FOREIGN KEY (transaction_id) REFERENCES fee_payment_transactions(transaction_id),
		FOREIGN KEY (fee_id) REFERENCES fee_configuration(fee_id),
		FOREIGN KEY (fee_head_id) REFERENCES fee_head_configuration(fee_head_id)
	);




	-- DML
	
	insert into academic_session(institute_id, start_year, end_year, start_month, end_month)
	values(10005, 2018, 2019, 5, 4);
	

	
	-- Drop 3
	-- DML 
	
	ALTER TABLE `lernen`.`academic_session` ADD COLUMN `display_name` VARCHAR(255) NULL AFTER `end_month`;

	ALTER TABLE `lernen`.`students` ADD COLUMN `status` VARCHAR(255) NOT NULL DEFAULT "ENROLLED";

	ALTER TABLE `lernen`.`students` ADD COLUMN `student_documents` text NULL;
	
	
	-- Drop 4
	
	CREATE TABLE IF NOT EXISTS student_wallet(
			institute_id int NOT NULL,
			student_id varchar(36) NOT NULL,
			net_amount double NOT NULL default 0,
			primary key(student_id),
			FOREIGN KEY (student_id) REFERENCES students(student_id)
		);



	
	CREATE TABLE IF NOT EXISTS fee_assignment_permissions(
			fee_id varchar(36) NOT NULL,
			fee_head_id int NOT NULL,
			module_name varchar(64) NOT NULL,
			institute_id int NOT NULL,
			proportion_numerator double,
			proportion_denominator double,
			primary key(fee_id, fee_head_id, module_name),
			FOREIGN KEY (fee_id) REFERENCES fee_configuration(fee_id),
			FOREIGN KEY (fee_head_id) REFERENCES fee_head_configuration(fee_head_id)
		);
	
	
	ALTER TABLE `lernen`.`fee_payment_transactions` ADD COLUMN `debit_wallet_amount` double NOT NULL default 0;
	ALTER TABLE `lernen`.`fee_payment_transactions` ADD COLUMN `credit_wallet_amount` double NOT NULL default 0;
	ALTER TABLE `lernen`.`fee_payment_transactions` ADD COLUMN `academic_session_id` double NOT NULL;

	ALTER TABLE `lernen`.`fee_head_configuration` ADD COLUMN `fee_head_type` varchar(255) NOT NULL default 'CUSTOM';
	ALTER TABLE `lernen`.`fee_head_configuration` ADD COLUMN `fee_head_tag` varchar(255);
	
	
	ALTER TABLE `lernen`.`institute` ADD COLUMN `letter_head_line1` varchar(1023);
	ALTER TABLE `lernen`.`institute` ADD COLUMN `letter_head_line2` varchar(1023);

	-- 2nd Jan, 2018
	ALTER TABLE `lernen`.`students` 
	CHANGE COLUMN `status` `status` VARCHAR(255) NOT NULL DEFAULT 'ENROLMENT_PENDING' ;

	-- 3rd Jan, 2018
	ALTER TABLE `lernen`.`students` 
	ADD COLUMN `admission_number` VARCHAR(225) NOT NULL AFTER `registration_number`;
	
	ALTER TABLE `lernen`.`students` 
	ADD CONSTRAINT admission_number_unique UNIQUE (institute_id, admission_number);

	ALTER TABLE `lernen`.`students` 
	ADD COLUMN `primary_contact_number` VARCHAR(500) NULL DEFAULT NULL AFTER `zipcode`,
	ADD COLUMN `primary_email` VARCHAR(500) NULL DEFAULT NULL AFTER `primary_contact_number`,
	ADD COLUMN `father_aadhar_number` VARCHAR(225) NULL DEFAULT NULL AFTER `mother_contact_number`,
	ADD COLUMN `mother_aadhar_number` VARCHAR(225) NULL DEFAULT NULL AFTER `father_aadhar_number`,
	ADD COLUMN `religion` VARCHAR(225) NULL DEFAULT NULL AFTER `caste`;



	ALTER TABLE `lernen`.`students` 
	ADD COLUMN `mother_tongue` VARCHAR(225) NULL DEFAULT NULL AFTER `aadhar_number`,
	ADD COLUMN `area_type` VARCHAR(32) NULL DEFAULT NULL AFTER `mother_tongue`,
	ADD COLUMN `specially_abled` TINYINT(1) NULL DEFAULT NULL AFTER `area_type`,
	ADD COLUMN `bpl` TINYINT(1) NULL DEFAULT NULL AFTER `specially_abled`;

	ALTER TABLE `lernen`.`students` 
	CHANGE COLUMN `caste` `category` VARCHAR(256) NULL DEFAULT NULL ;
	
	
	update institute set letter_head_line1 = "(Co-Educational English Medium School)" , letter_head_line2 = "NH-11, Shri Dungargarh-Bikaner-(Raj)" where institute_id = 10001;
	update institute set letter_head_line1 = "(Co-Educational English Medium School)" , letter_head_line2 = "NH-11, Shri Dungargarh-Bikaner-(Raj)" where institute_id = 700;


	update institute set logo_url = "/static/core/images/vidhyasthali_logo.png", letter_head_line1 = "(Co-Educational English Medium School)" , letter_head_line2 = "Bigga Bass, Shri Dungargarh-Bikaner-(Raj)" where institute_id = 10005;

	INSERT INTO fee_head_configuration(institute_id, fee_category_id, fee_head, refundable, description, fee_head_type, fee_head_tag) 
	VALUES(10005, (SELECT fee_category_id from fee_category where institute_id = 10005 and fee_category = 'Transport Fee'), 'Transport', 1, null, 'SYSTEM', 'TRANSPORT');


