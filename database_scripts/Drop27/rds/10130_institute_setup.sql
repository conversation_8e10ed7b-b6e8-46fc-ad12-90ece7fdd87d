use lernen

INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country, zipcode, landmark, logo_url, email, phone_number, letter_head_line1, letter_head_line2, institute_unique_code)
VALUES (10130,'CH. RAGHUBIR SINGH MEMORIAL CONVENT SCHOOL', 'V.P.O. WARYAM KHERA TEHSIL ABOHAR', 'DISTRICT FAZILKA', 'ABOHAR', 'PUNJAB', 'India', '152116', 'HAKMABAD ROAD', '/static/core/images/10130_logo.png', '<EMAIL>', '8558948804', 'VILL. WARYAM KHERA, ABOHAR, FAZILKA, PUNJAB', '(AFFILIATED TO CBSE NEW DELHI, AFFILIATION NO : 1631228)', UUID());

INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month)
values(10130, 2022, 2023, 4, 3);

insert into fee_category (institute_id, fee_category, description) values (10130, 'Academic Fee' ,'Academic Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10130, 'Hostel Fee' ,'Hostel Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10130, 'Transport Fee' ,'Transport Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10130, 'Admission Fee' ,'Admission Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10130, 'Examination Fee' ,'Examination Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10130, 'Miscellaneous Fee' ,'Miscellaneous Fees');
insert into fee_category (institute_id, fee_category, description) values (10130, 'Other Fee' ,'Any Other Fees');


INSERT INTO fee_head_configuration(institute_id, fee_category_id, fee_head, refundable, description, fee_head_type, fee_head_tag)
VALUES(10130, (SELECT fee_category_id from fee_category where institute_id = 10130 and fee_category = 'Transport Fee'), 'Transport', 1, null, 'SYSTEM', 'TRANSPORT');


insert into categories (institute_id,category_name,genders) values (10130,'stationery',0);
insert into categories (institute_id,category_name,genders) values (10130,'books',0);
insert into categories (institute_id,category_name) values (10130,'clothing');
insert into categories (institute_id,category_name) values (10130,'foot wear');
insert into categories (institute_id,category_name,genders,colors) values (10130,'note book',0,0);
insert into categories (institute_id,category_name) values (10130,'art & craft');
insert into categories (institute_id,category_name) values (10130,'personal care');
insert into categories (institute_id,category_name) values (10130,'bedding');
insert into categories (institute_id,category_name,genders,colors) values (10130,'energy drinks',0,0);
insert into categories (institute_id,category_name) values (10130,'accessories');
insert into categories (institute_id,category_name) values (10130,'furniture');
insert into categories (institute_id,category_name) values (10130,'electronics');
insert into categories (institute_id,category_name) values (10130,'sports');


insert into colors (institute_id, color_name) values (10130,'maroon');
insert into colors (institute_id, color_name) values (10130,'black');
insert into colors (institute_id, color_name) values (10130,'brown');
insert into colors (institute_id, color_name) values (10130,'white');
insert into colors (institute_id, color_name) values (10130,'red');
insert into colors (institute_id, color_name) values (10130,'yellow');
insert into colors (institute_id, color_name) values (10130,'blue');
insert into colors (institute_id, color_name) values (10130,'navy blue');
insert into colors (institute_id, color_name) values (10130,'green');
insert into colors (institute_id, color_name) values (10130,'dark green');
insert into colors (institute_id, color_name) values (10130,'pink');
insert into colors (institute_id, color_name) values (10130,'purple');
insert into colors (institute_id, color_name) values (10130,'grey');
insert into colors (institute_id, color_name) values (10130,'olive');
insert into colors (institute_id, color_name) values (10130,'cyan');
insert into colors (institute_id, color_name) values (10130,'magenta');


insert into counters (institute_id, counter_type, count, counter_prefix) values (10130, 'FEE_INVOICE', 1, "");
insert into counters (institute_id, counter_type, count, counter_prefix) values (10130, 'AUDIO_VOICE_CALL_COUNTER', 0, "");
insert into counters (institute_id, counter_type, count, counter_prefix) values (10130, 'SMS_COUNTER', 0, "");

insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10130', 'meta_data', 'enable_permission', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10130', 'exam_admit_card_preferences', 'include_class_roll_number', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10130', 'meta_data', 'institute_name_in_sms', 'CH. RAGHUBIR SINGH MEMORIAL CONVENT SCHOOL');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10130', 'meta_data', 'institute_unique_code', 'crsmcs130');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10130', 'meta_data', 'sms_service_enabled', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10130', 'sms_preferences', 'buffer_sms_count', 0);

insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(10130, "Total", "SYSTEM", "NUMBER", 1);
insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(10130, "Grade", "SYSTEM", "GRADE", 1);

--insert into default_fee_assignment_structure_meta_data values(10130, "Installments", "1aed4d2d-2d8e-4741-8b4c-6454126c1701", "ENROLLMENT");
--insert into default_fee_assignment_structure select "1aed4d2d-2d8e-4741-8b4c-6454126c1701", (select standard_id from standards where institute_id=10130 and standard_name = '12th' and stream = 'COMMERCE'), "CLASS", fee_id , 80, 6900 from fee_configuration where institute_id = 10105 and fee_type = "REGULAR" and academic_session_id = 61;


--add range display name also
SET @institute_id := 10130;
SET @academic_session_id := 61;
SET @course_type := "SCHOLASTIC";

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A1', 9, 0.90, 1.1, '91-100' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A2', 8, 0.80, 0.90, '81-90' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B1', 7, 0.70, 0.80, '71-80' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B2', 6, 0.60, 0.70, '61-70' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C1', 5, 0.50 , 0.60, '51-60' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C2', 4, 0.40 , 0.50, '41-50' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 3, 0.30 , 0.40, '30-40' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'E', 2, 0.00 , 0.30, '00-30' from standards where institute_id = @institute_id;

--
---- Setting CO-SCHOLASTIC Grades
SET @institute_id := 10130;
SET @academic_session_id := 61;
SET @course_type := "COSCHOLASTIC";

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A', 10, 0.9, 1.1, '91-100' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B', 9, 0.8, 0.9, '81-90' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C', 8, 0.7, 0.8, '71-80' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 7, 0.6, 0.7, '61-70' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'E', 6, 0.0, 0.6, '00-60' from standards where institute_id = @institute_id;


insert into standards_metadata select 10130, 61, standard_id, 1, 0 from standards where institute_id = 10130;
insert into configuration values('INSTITUTE', '10130', 'meta_data', 'module_access', '["ADMISSION","FEES","TRANSPORT","STORE","COURSES","EXAMINATION","ATTENDANCE","LECTURE_MANAGEMENT","HOMEWORK_MANAGEMENT","NOTICE_BOARD_MANAGEMENT","STUDENT_MANAGEMENT","STAFF_MANAGEMENT","INCOME_EXPENSE","USER_MANAGEMENT","COMMUNICATION", "SALARY_MANAGEMENT","STAFF_ATTENDANCE"]');

--select "348bfd28-7193-4cf1-ac4b-18ebb0ce1b4d", (select standard_id from standards where institute_id=10130 and standard_name = 'Standard-VI'), "CLASS", fee_id , 150, 1400 from fee_configuration where institute_id = 10130 and fee_type = "REGULAR" and academic_session_id = 61;
--insert into default_fee_assignment_structure select "348bfd28-7193-4cf1-ac4b-18ebb0ce1b4d", (select standard_id from standards where institute_id=10130 and standard_name = 'Standard-VI'), "CLASS", fee_id , 150, 1400 from fee_configuration where institute_id = 10130 and fee_type = "REGULAR" and academic_session_id = 61;

--insert into configuration values('INSTITUTE', '10130', 'meta_data', 'registration_counter', 'true');
--insert into configuration values('INSTITUTE', '10130', 'meta_data', 'admission_counter', 'true');

--insert into counters values(10130, 'REGISTRATION_NUMBER', 4, '');
--insert into counters values(10130, 'ADMISSION_NUMBER', 4, '');







