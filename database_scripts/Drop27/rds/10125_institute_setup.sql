use lernen

INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country, zipcode, landmark, logo_url, email, phone_number, letter_head_line1, letter_head_line2, institute_unique_code)
VALUES (10125,'SHAHEED CAPTAIN CHANDRA CHOUDHURY SR SEC SCHOOL', 'Bigga Bas Ramsara', '', '', 'Rajasthan', 'India', '', '', '', '', '', '', '', UUID());

INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month)
values(10125, 2022, 2023, 5, 4);

insert into fee_category (institute_id, fee_category, description) values (10125, 'Academic Fee' ,'Academic Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10125, 'Hostel Fee' ,'Hostel Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10125, 'Transport Fee' ,'Transport Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10125, 'Admission Fee' ,'Admission Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10125, 'Examination Fee' ,'Examination Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10125, 'Miscellaneous Fee' ,'Miscellaneous Fees');
insert into fee_category (institute_id, fee_category, description) values (10125, 'Other Fee' ,'Any Other Fees');


INSERT INTO fee_head_configuration(institute_id, fee_category_id, fee_head, refundable, description, fee_head_type, fee_head_tag)
VALUES(10125, (SELECT fee_category_id from fee_category where institute_id = 10125 and fee_category = 'Transport Fee'), 'Transport', 1, null, 'SYSTEM', 'TRANSPORT');


insert into categories (institute_id,category_name,genders) values (10125,'stationery',0);
insert into categories (institute_id,category_name,genders) values (10125,'books',0);
insert into categories (institute_id,category_name) values (10125,'clothing');
insert into categories (institute_id,category_name) values (10125,'foot wear');
insert into categories (institute_id,category_name,genders,colors) values (10125,'note book',0,0);
insert into categories (institute_id,category_name) values (10125,'art & craft');
insert into categories (institute_id,category_name) values (10125,'personal care');
insert into categories (institute_id,category_name) values (10125,'bedding');
insert into categories (institute_id,category_name,genders,colors) values (10125,'energy drinks',0,0);
insert into categories (institute_id,category_name) values (10125,'accessories');
insert into categories (institute_id,category_name) values (10125,'furniture');
insert into categories (institute_id,category_name) values (10125,'electronics');
insert into categories (institute_id,category_name) values (10125,'sports');


insert into colors (institute_id, color_name) values (10125,'maroon');
insert into colors (institute_id, color_name) values (10125,'black');
insert into colors (institute_id, color_name) values (10125,'brown');
insert into colors (institute_id, color_name) values (10125,'white');
insert into colors (institute_id, color_name) values (10125,'red');
insert into colors (institute_id, color_name) values (10125,'yellow');
insert into colors (institute_id, color_name) values (10125,'blue');
insert into colors (institute_id, color_name) values (10125,'navy blue');
insert into colors (institute_id, color_name) values (10125,'green');
insert into colors (institute_id, color_name) values (10125,'dark green');
insert into colors (institute_id, color_name) values (10125,'pink');
insert into colors (institute_id, color_name) values (10125,'purple');
insert into colors (institute_id, color_name) values (10125,'grey');
insert into colors (institute_id, color_name) values (10125,'olive');
insert into colors (institute_id, color_name) values (10125,'cyan');
insert into colors (institute_id, color_name) values (10125,'magenta');


insert into counters (institute_id, counter_type, count, counter_prefix) values (10125, 'FEE_INVOICE', 1, "");
insert into counters (institute_id, counter_type, count, counter_prefix) values (10125, 'AUDIO_VOICE_CALL_COUNTER', 0, "");
insert into counters (institute_id, counter_type, count, counter_prefix) values (10125, 'SMS_COUNTER', 0, "");

insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10125', 'meta_data', 'enable_permission', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10125', 'exam_admit_card_preferences', 'include_class_roll_number', 'true');
--insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10125', 'meta_data', 'institute_name_in_sms', 'SHAHEED CAPTAIN CHANDRA CHOUDHURY SR SEC SCHOOL');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10125', 'meta_data', 'institute_unique_code', 'scccsss125');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10125', 'meta_data', 'sms_service_enabled', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10125', 'sms_preferences', 'buffer_sms_count', 0);

insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(10125, "Total", "SYSTEM", "NUMBER", 1);
insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(10125, "Grade", "SYSTEM", "GRADE", 1);

--insert into default_fee_assignment_structure_meta_data values(10125, "Installments", "1aed4d2d-2d8e-4741-8b4c-6454126c1701", "ENROLLMENT");
--insert into default_fee_assignment_structure select "1aed4d2d-2d8e-4741-8b4c-6454126c1701", (select standard_id from standards where institute_id=10125 and standard_name = '12th' and stream = 'COMMERCE'), "CLASS", fee_id , 80, 6900 from fee_configuration where institute_id = 10105 and fee_type = "REGULAR" and academic_session_id = 57;


--add range display name also
SET @institute_id := 10125;
SET @academic_session_id := 57;
SET @course_type := "SCHOLASTIC";

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A1', 9, 0.905, 1.1, '91-100' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A2', 8, 0.805, 0.905, '81-90' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B1', 7, 0.705, 0.805, '71-80' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B2', 6, 0.605, 0.705, '61-70' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C1', 5, 0.505 , 0.605, '51-60' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C2', 4, 0.405 , 0.505, '41-50' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 3, 0.325 , 0.405, '33-40' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'E1', 0, 0.205 , 0.325, '21-32' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'E2', 0, 0.0 , 0.205, '00-20' from standards where institute_id = @institute_id;


-- Setting CO-SCHOLASTIC Grades
SET @course_type := "COSCHOLASTIC";

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A', 10, 0.9, 1.1, '91-100' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B', 9, 0.8, 0.9, '81-90' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C', 8, 0.7, 0.8, '71-80' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 7, 0.6, 0.7, '61-70' from standards where institute_id = @institute_id;

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'E', 6, 0.0, 0.6, '00-60' from standards where institute_id = @institute_id;


insert into standards_metadata select 10125, 57, standard_id, 1, 0 from standards where institute_id = 10125;
insert into configuration values('INSTITUTE', '10125', 'meta_data', 'module_access', '["ADMISSION","FEES","COURSES","EXAMINATION","USER_MANAGEMENT","STUDENT_MANAGEMENT","COMMUNICATION","STAFF_MANAGEMENT"]');

--select "348bfd28-7193-4cf1-ac4b-18ebb0ce1b4d", (select standard_id from standards where institute_id=10125 and standard_name = 'Standard-VI'), "CLASS", fee_id , 150, 1400 from fee_configuration where institute_id = 10125 and fee_type = "REGULAR" and academic_session_id = 57;
--insert into default_fee_assignment_structure select "348bfd28-7193-4cf1-ac4b-18ebb0ce1b4d", (select standard_id from standards where institute_id=10125 and standard_name = 'Standard-VI'), "CLASS", fee_id , 150, 1400 from fee_configuration where institute_id = 10125 and fee_type = "REGULAR" and academic_session_id = 57;

--insert into configuration values('INSTITUTE', '10125', 'meta_data', 'registration_counter', 'true');
--insert into configuration values('INSTITUTE', '10125', 'meta_data', 'admission_counter', 'true');

--insert into counters values(10125, 'REGISTRATION_NUMBER', 4, '');
--insert into counters values(10125, 'ADMISSION_NUMBER', 4, '');



----DATA DELETION FOR 10125 (3rd Apr'2022)----

insert into configuration values('INSTITUTE', '10125', 'meta_data', 'registration_counter', 'true');

--updating counters queries
select * from counters where institute_id = 10125;
--select * from counters where institute_id = 10125 and counter_type = 'ADMISSION_NUMBER';
--update counters set count = 1 where institute_id = 10125 and counter_type = 'ADMISSION_NUMBER';
update counters set count = 1 where institute_id = 10125 and counter_type = 'FEE_INVOICE';
insert into counters values(10125, 'REGISTRATION_NUMBER', 1, '');
--update counters set count = 1 where institute_id = 10125 and counter_type = 'REGISTRATION_NUMBER';
--update counters set count = 5000 where institute_id = 10125 and counter_type = 'SMS_COUNTER';
--update counters set count = 1 where institute_id = 10125 and counter_type = 'STAFF_NUMBER';
select * from counters where institute_id = 10125;

--Notification deletion queries
--select * from notification_status where institute_id = 10125;
--delete from notification_status where institute_id = 10125;

--Exam Deletion Queries
select * from marks_feeding where exam_id  in (select exam_id from exam_metadata where academic_session_id = 57);
delete from marks_feeding where exam_id  in (select exam_id from exam_metadata where academic_session_id = 57);
select * from exam_courses_assignment where exam_id in (select exam_id from exam_metadata where academic_session_id = 57);
delete from exam_courses_assignment where exam_id in (select exam_id from exam_metadata where academic_session_id = 57);
select * from exam_dimensions_mapping where exam_id  in (select exam_id from exam_metadata where academic_session_id = 57);
delete from exam_dimensions_mapping where exam_id  in (select exam_id from exam_metadata where academic_session_id = 57);
select * from exam_graph_edges where exam_id in (select exam_id from exam_metadata where academic_session_id = 57);
delete from exam_graph_edges where exam_id in (select exam_id from exam_metadata where academic_session_id = 57);
select * from exam_metadata where academic_session_id = 57;
delete from exam_metadata where academic_session_id = 57;

--Optional Course Assignment deletion queries
--select * from student_course_assignment where course_id  in (select course_id from class_courses where academic_session_id = 57);
--delete from student_course_assignment where course_id  in (select course_id from class_courses where academic_session_id = 57);

--Class courses
select * from class_courses where academic_session_id = 57;
delete from class_courses where academic_session_id = 57;

--Lecture deletion queries
--select * from student_lecture_view_details where lecture_id in (select lecture_id  from lecture_details where institute_id = 10125);
--delete from student_lecture_view_details where lecture_id in (select lecture_id  from lecture_details where institute_id = 10125);
--select * from student_lecture_complete_details where lecture_id in (select lecture_id  from lecture_details where institute_id = 10125);
--delete from student_lecture_complete_details where lecture_id in (select lecture_id  from lecture_details where institute_id = 10125);
--select * from lecture_details where institute_id = 10125;
--delete from lecture_details where institute_id = 10125;

--Homework deletion queries
--select * from homework_submission_details where homework_id in (select homework_id from homework_details where institute_id = 10125);
--delete from homework_submission_details where homework_id in (select homework_id from homework_details where institute_id = 10125);
--select * from homework_details where institute_id = 10125;
--delete from homework_details where institute_id = 10125;

--Discussion Fourm deletion queries
--select * from channel_details where institute_id = 10125;
--delete from channel_details where institute_id = 10125;
--select * from channel_threads where channel_id in (select channel_id from channel_details where institute_id = 10125);
--delete from channel_threads where channel_id in (select channel_id from channel_details where institute_id = 10125);
--select * from conversation_details where thread_id in (select thread_id from channel_threads where channel_id in (select channel_id from channel_details where institute_id = 10125));
--delete from conversation_details where thread_id in (select thread_id from channel_threads where channel_id in (select channel_id from channel_details where institute_id = 10125));

--Notice board deletion queries
--select * from notice_details where institute_id = 10125;
--delete from notice_details where institute_id = 10125;
--select * from notice_entity_mapping where notice_id in (select notice_id from notice_details where institute_id = 10125);
--delete from notice_entity_mapping where notice_id in (select notice_id from notice_details where institute_id = 10125);

--Income & Expense Select queries
select * from income_expense_category where institute_id = 10125;
select * from income_expense_entities where institute_id = 10125;
delete from income_expense_entities where institute_id = 10125;
select * from income_expense_transactions where institute_id = 10125;
delete from income_expense_transactions where institute_id = 10125;

--Student Attendance deletion queries
--select * from student_attendance_register where institute_id = 10125;
--delete from student_attendance_register where institute_id = 10125;

--Audit Logs Select queries
--select * from audit_logs where institute_id = 10125;

--Salary Management Select queries
--select * from salary_payhead_details where pay_head_id in (select pay_head_id from pay_head_configuration where institute_id = 10125);
--select * from salary_payslip_meta_data where institute_id = 10125;
--select * from staff_advance_transaction_history where institute_id = 10125;
--select * from staff_salary_structure_meta_data where institute_id = 10125;
--select * from staff_salary_structure_payhead_details where structure_id in (select structure_id from staff_salary_structure_meta_data where institute_id = 10125);
--pay_head_configuration -- dont delete

--Staff Attendance deletion queries
--select * from staff_attendance_register where institute_id = 10125;

--Reset Password request
--select * from reset_password_details where institute_id = 10125;
--delete from reset_password_details where institute_id = 10125;

-- Bell Notificaiton
--select * from bell_notification_details where institute_id = 10125;
--delete from bell_notification_details where institute_id = 10125;

--Staff Manageement
--select * from staff_details where institute_id = 10125;
--delete from staff_details where institute_id = 10125;
select * from staff_category where institute_id = 10125;
select * from staff_department where institute_id = 10125;
select * from staff_designation where institute_id = 10125;

--User Management
select * from user_role_mapping where user_id in (select user_id from users where institute_id = 10125 and user_type <> 'ADMIN');
delete from user_role_mapping where user_id in (select user_id from users where institute_id = 10125 and user_type <> 'ADMIN');
select * from users where institute_id = 10125 and user_type <> 'ADMIN';
delete from users where institute_id = 10125 and user_type <> 'ADMIN';


--Inventory deletion queries
--select * from transactions where institute_id = 10125;
--delete from transactions where institute_id = 10125;
--select * from product_group where institute_id = 10125;
--delete from product_group where institute_id = 10125;
--select * from vendors where institute_id = 10125;
--delete from vendors where institute_id = 10125;

--select * from brands where institute_id = 10125;
--delete from brands where institute_id = 10125;
--select * from brand_category_mapping where brand_id in (select brand_id from brands where institute_id = 10125);
--delete from brand_category_mapping where brand_id in (select brand_id from brands where institute_id = 10125);

--select * from products where institute_id = 10125;
--delete from products where institute_id = 10125;
--select * from product_group_sku_mapping where sku_id in (select sku_id from products where institute_id = 10125);

--select * from product_transaction_metadata where institute_id = 10125;
--delete from product_transaction_metadata where institute_id = 10125;
--select * from product_transaction_details where transaction_id in (select transaction_id from product_transaction_metadata where institute_id = 10125);
--delete from product_transaction_details where transaction_id in (select transaction_id from product_transaction_metadata where institute_id = 10125);

--Fees Assignemnt deletion
select * from fee_payment_transaction_amounts where transaction_id in (select transaction_id from fee_payment_transactions  where institute_id = 10125);
delete from fee_payment_transaction_amounts where transaction_id in (select transaction_id from fee_payment_transactions  where institute_id = 10125);
select * from fee_payment_transactions where institute_id = 10125;
delete from fee_payment_transactions where institute_id = 10125;
select * from student_fee_payments where institute_id = 10125;
delete from student_fee_payments where institute_id = 10125;
select * from fee_assignment where institute_id = 10125;
delete from fee_assignment where institute_id = 10125;
select * from default_fee_assignment_structure where structure_id in (select structure_id from default_fee_assignment_structure_meta_data where institute_id = 10125);
delete from default_fee_assignment_structure where structure_id in (select structure_id from default_fee_assignment_structure_meta_data where institute_id = 10125);
select * from default_fee_assignment_structure_meta_data where institute_id = 10125;
delete from default_fee_assignment_structure_meta_data where institute_id = 10125;
select * from fee_configuration where institute_id = 10125;
delete from fee_configuration where institute_id = 10125;


--User wallet deletion
--select * from user_wallet_transaction_history where institute_id = 10125;
select * from user_wallet where institute_id = 10125;
delete from user_wallet where institute_id = 10125;

--Student details deletion
select * from student_academic_session_details where academic_session_id = 57;
delete from student_academic_session_details where academic_session_id = 57;
select * from students where institute_id = 10125;
delete from students where institute_id = 10125;

select * from academic_session where institute_id = 10125;
--delete from academic_session where institute_id = 10125;
update academic_session set start_year = 2021, end_year = 2022, start_month = 9, end_month = 8 where institute_id = 10125;

insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10125', 'meta_data', 'institute_name_in_sms', 'SHAHEED CAPTAIN CHANDRA CHOUDHURY SR SEC SCHOOL');

select * from examination_grades where institute_id = 10125;
delete from examination_grades where institute_id = 10125;

select * from standards where institute_id = 10125;
update standards set stream = 'ARTS' where standard_id in ('bbe699b5-316a-4bee-bc03-fc4da74c2026', 'c9544cdb-8d5f-4baa-b40b-623c82f647a4');