CREATE TABLE IF NOT EXISTS staff_details(
institute_id int NOT NULL,
staff_institute_id varchar(225) NOT NULL,
staff_id varchar(36) NOT NULL,
staff_type varchar(225) NOT NULL,
name varchar(225) NOT NULL,
gender varchar(15),
designation varchar(225),
service_start_date TIMESTAMP NULL DEFAULT NULL,
service_end_date TIMESTAMP NULL DEFAULT NULL,
date_of_birth TIMESTAMP NULL DEFAULT NULL,
birth_place varchar(225),
category varchar(225),
religion varchar(225),
aadhar_number varchar(225),
pan_number varchar(225),
mother_tongue varchar(225),
area_type varchar(32),
specially_abled	tinyint(1),
bpl tinyint(1),
permanent_address text,
current_address text,
city varchar(256),
state varchar(256),
zipcode varchar (15),
primary_contact_number varchar(225),
primary_email varchar(500),
father_name varchar(225),
mother_name varchar(225),
last_occupation_details text,
blood_group varchar(225),
blood_pressure varchar(225),
pulse varchar(225),
height varchar(50),
weight varchar(50),
date_of_physical_examination TIMESTAMP NULL DEFAULT NULL,
documents text,
secondary_contact_number varchar(225),
experience text,
qualification text,
background_verification tinyint(1),
status varchar(225),
location varchar(225),
education_details text,
offer_acceptance_date TIMESTAMP NULL DEFAULT NULL,
tentative_date_of_joining TIMESTAMP NULL DEFAULT NULL,
marital_status varchar(225),
relieve_date TIMESTAMP NULL DEFAULT NULL,
ex_organization_name varchar(500),
ex_location varchar(225),
ex_designation varchar(225),
rehire tinyint(1),
emergancy_contact_details varchar(225),
emergancy_no varchar(225),
absconding_yn tinyint(1),
staff_curr_yn tinyint(1),
primary key(staff_id),
unique key(institute_id, staff_institute_id),
FOREIGN KEY (institute_id) REFERENCES institute(institute_id)
);

