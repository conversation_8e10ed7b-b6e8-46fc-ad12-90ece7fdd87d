ALTER TABLE user_attendance_log ADD attendance_date timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP AFTER attendance_time;

DROP INDEX user_id ON user_attendance_log;
CREATE INDEX user_id_attendance_date ON user_attendance_log (user_id, attendance_date);

update user_attendance_log set attendance_date = CONVERT_TZ(TIMESTAMP(CONCAT(DATE_FORMAT(CONVERT_TZ(attendance_time, '+00:00', '+05:30'), '%Y-%m-%d'), ' 00:00:00')),  '+05:30', '+00:00');

--Steps to execute
--1. Update attendance_date column with apt data