select * from standards where institute_id = 10030;
select * from academic_session where institute_id = 10030;
select * from standard_section_mapping where academic_session_id = 53;
insert into standards_metadata select 10030, 53, standard_id, 1, 0 from standards where institute_id = 10030;
select * from standards_metadata where academic_session_id = 53;
select * from examination_grades where academic_session_id = 53;

insert into standards values(10030, UUID(), 'NURSERY', 'NA', 1);
select * from standards where institute_id = 10030;
insert into academic_session(institute_id, start_year, end_year, start_month, end_month, display_name) values(10030, 2023, 2024, 4, 3, NULL);

select standard_id, 113, section_name from standard_section_mapping where academic_session_id = 53;
insert into standard_section_mapping(standard_id, academic_session_id, section_name) select standard_id, 113, section_name from standard_section_mapping where academic_session_id = 53;

select institute_id, 113, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 53;
insert into standards_metadata select institute_id, 113, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 53;

select institute_id, 113, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 53;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name) select institute_id, 113, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 53;

select * from academic_session where institute_id = 10030;
select * from standard_section_mapping where academic_session_id = 113;
select * from standards_metadata where academic_session_id = 113;
select * from examination_grades where academic_session_id = 113;

insert into default_fee_assignment_structure_meta_data values(10030, 113, "Monthly Fees (2023-24)", UUID(), "ENROLLMENT");
insert into default_fee_assignment_structure select "35265f79-c315-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10030 and standard_name = 'NURSERY'), "CLASS", fee_id , 43, 500 from fee_configuration where institute_id = 10030 and fee_type = "REGULAR" and academic_session_id = 113;
insert into default_fee_assignment_structure select "35265f79-c315-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10030 and standard_name = 'L.K.G'), "CLASS", fee_id , 43, 550 from fee_configuration where institute_id = 10030 and fee_type = "REGULAR" and academic_session_id = 113;
insert into default_fee_assignment_structure select "35265f79-c315-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10030 and standard_name = 'U.K.G'), "CLASS", fee_id , 43, 550 from fee_configuration where institute_id = 10030 and fee_type = "REGULAR" and academic_session_id = 113;
insert into default_fee_assignment_structure select "35265f79-c315-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10030 and standard_name = '1st'), "CLASS", fee_id , 43, 550 from fee_configuration where institute_id = 10030 and fee_type = "REGULAR" and academic_session_id = 113;
insert into default_fee_assignment_structure select "35265f79-c315-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10030 and standard_name = '2nd'), "CLASS", fee_id , 43, 550 from fee_configuration where institute_id = 10030 and fee_type = "REGULAR" and academic_session_id = 113;
insert into default_fee_assignment_structure select "35265f79-c315-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10030 and standard_name = '3rd'), "CLASS", fee_id , 43, 550 from fee_configuration where institute_id = 10030 and fee_type = "REGULAR" and academic_session_id = 113;
insert into default_fee_assignment_structure select "35265f79-c315-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10030 and standard_name = '4th'), "CLASS", fee_id , 43, 600 from fee_configuration where institute_id = 10030 and fee_type = "REGULAR" and academic_session_id = 113;
insert into default_fee_assignment_structure select "35265f79-c315-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10030 and standard_name = '5th'), "CLASS", fee_id , 43, 600 from fee_configuration where institute_id = 10030 and fee_type = "REGULAR" and academic_session_id = 113;
--insert into default_fee_assignment_structure select "35265f79-c315-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10030 and standard_name = 'VI'), "CLASS", fee_id , 43, 800 from fee_configuration where institute_id = 10030 and fee_type = "REGULAR" and academic_session_id = 113;
--insert into default_fee_assignment_structure select "35265f79-c315-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10030 and standard_name = 'VII'), "CLASS", fee_id , 43, 800 from fee_configuration where institute_id = 10030 and fee_type = "REGULAR" and academic_session_id = 113;
--insert into default_fee_assignment_structure select "35265f79-c315-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10030 and standard_name = 'VIII'), "CLASS", fee_id , 43, 800 from fee_configuration where institute_id = 10030 and fee_type = "REGULAR" and academic_session_id = 113;
--insert into default_fee_assignment_structure select "35265f79-c315-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10030 and standard_name = 'IX'), "CLASS", fee_id , 43, 900 from fee_configuration where institute_id = 10030 and fee_type = "REGULAR" and academic_session_id = 113;
--insert into default_fee_assignment_structure select "35265f79-c315-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10030 and standard_name = 'X'), "CLASS", fee_id , 43, 900 from fee_configuration where institute_id = 10030 and fee_type = "REGULAR" and academic_session_id = 113;
--insert into default_fee_assignment_structure select "35265f79-c315-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10030 and standard_name = 'XI' and stream = 'SCIENCE'), "CLASS", fee_id , 43, 1500 from fee_configuration where institute_id = 10030 and fee_type = "REGULAR" and academic_session_id = 113;
--insert into default_fee_assignment_structure select "35265f79-c315-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10030 and standard_name = 'XI' and stream = 'ARTS'), "CLASS", fee_id , 43, 1000 from fee_configuration where institute_id = 10030 and fee_type = "REGULAR" and academic_session_id = 113;
--insert into default_fee_assignment_structure select "35265f79-c315-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10030 and standard_name = 'XII' and stream = 'SCIENCE'), "CLASS", fee_id , 43, 1500 from fee_configuration where institute_id = 10030 and fee_type = "REGULAR" and academic_session_id = 113;
--insert into default_fee_assignment_structure select "35265f79-c315-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10030 and standard_name = 'XII' and stream = 'ARTS'), "CLASS", fee_id , 43, 1000 from fee_configuration where institute_id = 10030 and fee_type = "REGULAR" and academic_session_id = 113;

--Add Default Exam Structure





select * from standards where institute_id = 10031;
select * from academic_session where institute_id = 10031;
select * from standard_section_mapping where academic_session_id = 54;
--insert into standards_metadata select 10031, 40, standard_id, 1, 0 from standards where institute_id = 10031;
--insert into standards_metadata select 10031, 54, standard_id, 1, 0 from standards where institute_id = 10031;
select * from standards_metadata where academic_session_id = 54;
select * from examination_grades where academic_session_id = 54;

insert into academic_session(institute_id, start_year, end_year, start_month, end_month, display_name) values(10031, 2023, 2024, 4, 3, NULL);

select standard_id, 114, section_name from standard_section_mapping where academic_session_id = 54;
insert into standard_section_mapping(standard_id, academic_session_id, section_name) select standard_id, 114, section_name from standard_section_mapping where academic_session_id = 54;

select institute_id, 114, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 54;
insert into standards_metadata select institute_id, 114, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 54;

select institute_id, 114, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 54;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name) select institute_id, 114, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 54;

select * from academic_session where institute_id = 10031;
select * from standard_section_mapping where academic_session_id = 114;
select * from standards_metadata where academic_session_id = 114;
select * from examination_grades where academic_session_id = 114;

insert into default_fee_assignment_structure_meta_data values(10031, 114, "Monthly Fees (2023-24)", UUID(), "ENROLLMENT");
--insert into default_fee_assignment_structure select "135a482b-c316-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10031 and standard_name = 'NURSERY'), "CLASS", fee_id , 47, 600 from fee_configuration where institute_id = 10031 and fee_type = "REGULAR" and academic_session_id = 114;
--insert into default_fee_assignment_structure select "135a482b-c316-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10031 and standard_name = 'LKG'), "CLASS", fee_id , 47, 600 from fee_configuration where institute_id = 10031 and fee_type = "REGULAR" and academic_session_id = 114;
--insert into default_fee_assignment_structure select "135a482b-c316-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10031 and standard_name = 'UKG'), "CLASS", fee_id , 47, 600 from fee_configuration where institute_id = 10031 and fee_type = "REGULAR" and academic_session_id = 114;
--insert into default_fee_assignment_structure select "135a482b-c316-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10031 and standard_name = 'I'), "CLASS", fee_id , 47, 700 from fee_configuration where institute_id = 10031 and fee_type = "REGULAR" and academic_session_id = 114;
--insert into default_fee_assignment_structure select "135a482b-c316-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10031 and standard_name = 'II'), "CLASS", fee_id , 47, 700 from fee_configuration where institute_id = 10031 and fee_type = "REGULAR" and academic_session_id = 114;
--insert into default_fee_assignment_structure select "135a482b-c316-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10031 and standard_name = 'III'), "CLASS", fee_id , 47, 700 from fee_configuration where institute_id = 10031 and fee_type = "REGULAR" and academic_session_id = 114;
--insert into default_fee_assignment_structure select "135a482b-c316-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10031 and standard_name = 'IV'), "CLASS", fee_id , 47, 700 from fee_configuration where institute_id = 10031 and fee_type = "REGULAR" and academic_session_id = 114;
--insert into default_fee_assignment_structure select "135a482b-c316-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10031 and standard_name = 'V'), "CLASS", fee_id , 47, 700 from fee_configuration where institute_id = 10031 and fee_type = "REGULAR" and academic_session_id = 114;
insert into default_fee_assignment_structure select "135a482b-c316-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10031 and standard_name = '6th'), "CLASS", fee_id , 47, 600 from fee_configuration where institute_id = 10031 and fee_type = "REGULAR" and academic_session_id = 114;
insert into default_fee_assignment_structure select "135a482b-c316-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10031 and standard_name = '7th'), "CLASS", fee_id , 47, 650 from fee_configuration where institute_id = 10031 and fee_type = "REGULAR" and academic_session_id = 114;
insert into default_fee_assignment_structure select "135a482b-c316-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10031 and standard_name = '8th'), "CLASS", fee_id , 47, 650 from fee_configuration where institute_id = 10031 and fee_type = "REGULAR" and academic_session_id = 114;
insert into default_fee_assignment_structure select "135a482b-c316-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10031 and standard_name = '9th'), "CLASS", fee_id , 47, 650 from fee_configuration where institute_id = 10031 and fee_type = "REGULAR" and academic_session_id = 114;
insert into default_fee_assignment_structure select "135a482b-c316-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10031 and standard_name = '10th'), "CLASS", fee_id , 47, 650 from fee_configuration where institute_id = 10031 and fee_type = "REGULAR" and academic_session_id = 114;
insert into default_fee_assignment_structure select "135a482b-c316-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10031 and standard_name = '11th'), "CLASS", fee_id , 47, 700 from fee_configuration where institute_id = 10031 and fee_type = "REGULAR" and academic_session_id = 114;
insert into default_fee_assignment_structure select "135a482b-c316-11ed-bbe2-0aa7a9710bf1", (select standard_id from standards where institute_id=10031 and standard_name = '12th'), "CLASS", fee_id , 47, 700 from fee_configuration where institute_id = 10031 and fee_type = "REGULAR" and academic_session_id = 114;

--Add Default Exam Structure