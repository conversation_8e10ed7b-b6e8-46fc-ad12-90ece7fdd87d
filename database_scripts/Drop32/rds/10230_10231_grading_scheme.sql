SET @institute_id := 10230;
SET @academic_session_id := 115;
SET @academic_session_id := 136;

SET @institute_id := 10231;
SET @academic_session_id := 116;
SET @academic_session_id := 137;

SET @institute_id := 10232;
SET @academic_session_id := 157;
SET @academic_session_id := 245;


SET @course_type := "SCHOLASTIC";

--0.85 - 1.1 : A+
--0.70 - 0.85 : A
--0.60 - 0.70 : B+
--0.55 - 0.60 : B
--0.50 : 0.55 : C+
--0 - 0.50 : C

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A+', 9, 0.85, 1.1, '85 - 100' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A', 8, 0.70, 0.85, '70 - 85' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B+', 7, 0.60, 0.70, '60 - 70' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B', 6, 0.55, 0.60, '55 - 60' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C+', 5, 0.50, 0.55, '50 - 55' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C', 4, 0.0, 0.50, '00 - 50' from standards where institute_id = @institute_id;

-- Setting COSCHOLASTIC Grades

SET @institute_id := 10232;
SET @academic_session_id := 157;


SET @course_type := "COSCHOLASTIC";

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A+', 9, 0.85, 1.1, '85 - 100' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A', 8, 0.70, 0.85, '70 - 85' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B+', 7, 0.60, 0.70, '60 - 70' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B', 6, 0.55, 0.60, '55 - 60' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C+', 5, 0.50, 0.55, '50 - 55' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C', 4, 0.0, 0.50, '00 - 50' from standards where institute_id = @institute_id;
