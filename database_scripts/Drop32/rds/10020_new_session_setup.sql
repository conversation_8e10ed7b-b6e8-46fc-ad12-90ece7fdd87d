select * from academic_session where institute_id = 10020;
select * from standard_section_mapping where academic_session_id = 69;
--insert into standards_metadata select 10020, 69, standard_id, 1, 0 from standards where institute_id = 10020;
select * from standards_metadata where academic_session_id = 69;
select * from examination_grades where academic_session_id = 69;

insert into academic_session(institute_id, start_year, end_year, start_month, end_month, display_name) values(10020, 2023, 2024, 5, 4, NULL);

select standard_id, 145, section_name from standard_section_mapping where academic_session_id = 69;
insert into standard_section_mapping(standard_id, academic_session_id, section_name) select standard_id, 145, section_name from standard_section_mapping where academic_session_id = 69;

select institute_id, 145, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 69;
insert into standards_metadata select institute_id, 145, standard_id, coscholastic_grade_enabled, scholastic_grade_enabled from standards_metadata where academic_session_id = 69;

select institute_id, 145, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 69;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name) select institute_id, 145, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name from examination_grades where academic_session_id = 69;

select * from academic_session where institute_id = 10020;
select * from standard_section_mapping where academic_session_id = 145;
select * from standards_metadata where academic_session_id = 145;
select * from examination_grades where academic_session_id = 145;

--Add Default Exam Structure