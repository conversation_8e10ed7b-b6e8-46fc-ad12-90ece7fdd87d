INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country, zipcode, landmark, logo_url, email, phone_number, letter_head_line1, letter_head_line2, institute_unique_code)
VALUES (10190, 'SANSKAR INTERNATIONAL SCHOOL', 'CHARAN SINGH GATE', 'NAWALGARH ROAD', 'SIKAR', 'RAJASTHAN', 'INDIA', '332001', 'OPP. OM TOWAR', 'https://assets.embrate.com/static/core/images/10190_logo.png', '<EMAIL>', '9413344262, 01572-249200, 299266', 'CHARAN SINGH GATE, NAWALGARH ROAD', 'OPP. OM TOWAR, SIKAR, RAJASTHAN - 332001', UUID());

INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month)
values(10190, 2022, 2023, 4, 3);

insert into fee_category (institute_id, fee_category, description) values (10190, 'Academic Fee' ,'Academic Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10190, 'Hostel Fee' ,'Hostel Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10190, 'Transport Fee' ,'Transport Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10190, 'Admission Fee' ,'Admission Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10190, 'Examination Fee' ,'Examination Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10190, 'Miscellaneous Fee' ,'Miscellaneous Fees');
insert into fee_category (institute_id, fee_category, description) values (10190, 'Other Fee' ,'Any Other Fees');


INSERT INTO fee_head_configuration(institute_id, fee_category_id, fee_head, refundable, description, fee_head_type, fee_head_tag)
VALUES(10190, (SELECT fee_category_id from fee_category where institute_id = 10190 and fee_category = 'Transport Fee'), 'Transport', 1, null, 'SYSTEM', 'TRANSPORT');


insert into categories (institute_id,category_name,genders) values (10190,'stationery',0);
insert into categories (institute_id,category_name,genders) values (10190,'books',0);
insert into categories (institute_id,category_name) values (10190,'clothing');
insert into categories (institute_id,category_name) values (10190,'foot wear');
insert into categories (institute_id,category_name,genders,colors) values (10190,'note book',0,0);
insert into categories (institute_id,category_name) values (10190,'art & craft');
insert into categories (institute_id,category_name) values (10190,'personal care');
insert into categories (institute_id,category_name) values (10190,'bedding');
insert into categories (institute_id,category_name,genders,colors) values (10190,'energy drinks',0,0);
insert into categories (institute_id,category_name) values (10190,'accessories');
insert into categories (institute_id,category_name) values (10190,'furniture');
insert into categories (institute_id,category_name) values (10190,'electronics');
insert into categories (institute_id,category_name) values (10190,'sports');


insert into colors (institute_id, color_name) values (10190,'maroon');
insert into colors (institute_id, color_name) values (10190,'black');
insert into colors (institute_id, color_name) values (10190,'brown');
insert into colors (institute_id, color_name) values (10190,'white');
insert into colors (institute_id, color_name) values (10190,'red');
insert into colors (institute_id, color_name) values (10190,'yellow');
insert into colors (institute_id, color_name) values (10190,'blue');
insert into colors (institute_id, color_name) values (10190,'navy blue');
insert into colors (institute_id, color_name) values (10190,'green');
insert into colors (institute_id, color_name) values (10190,'dark green');
insert into colors (institute_id, color_name) values (10190,'pink');
insert into colors (institute_id, color_name) values (10190,'purple');
insert into colors (institute_id, color_name) values (10190,'grey');
insert into colors (institute_id, color_name) values (10190,'olive');
insert into colors (institute_id, color_name) values (10190,'cyan');
insert into colors (institute_id, color_name) values (10190,'magenta');

--If institute is taking income expense module
insert into income_expense_category (institute_id, category_id, category_name, category_description) values (10190, UUID(), 'General Expense', NULL);
insert into income_expense_category (institute_id, category_id, category_name, category_description) values (10190, UUID(), 'Bus Expense', NULL);
insert into income_expense_category (institute_id, category_id, category_name, category_description) values (10190, UUID(), 'Telephone', NULL);
insert into income_expense_category (institute_id, category_id, category_name, category_description) values (10190, UUID(), 'Electricity', NULL);
insert into income_expense_category (institute_id, category_id, category_name, category_description) values (10190, UUID(), 'Advertisement', NULL);
insert into income_expense_category (institute_id, category_id, category_name, category_description) values (10190, UUID(), 'Diesel Expense', NULL);
insert into income_expense_category (institute_id, category_id, category_name, category_description) values (10190, UUID(), 'Rent Expense', NULL);
insert into income_expense_category (institute_id, category_id, category_name, category_description) values (10190, UUID(), 'Refreshments', NULL);
insert into income_expense_category (institute_id, category_id, category_name, category_description) values (10190, UUID(), 'Construction Expense', NULL);


insert into counters (institute_id, counter_type, count, counter_prefix) values (10190, 'FEE_INVOICE', 1, "");
insert into counters (institute_id, counter_type, count, counter_prefix) values (10190, 'AUDIO_VOICE_CALL_COUNTER', 0, "");
insert into counters (institute_id, counter_type, count, counter_prefix) values (10190, 'SMS_COUNTER', 0, "");
insert into counters (institute_id, counter_type, count, counter_prefix) values (10190, 'REGISTRATION_NUMBER', 1, 'SIS 2022 - ');
--insert into counters (institute_id, counter_type, count, counter_prefix) values (10190, 'ADMISSION_NUMBER', 1120, 'MASSSL220');
insert into counters (institute_id, counter_type, count, counter_prefix) values (10190, 'STAFF_NUMBER', 1, 'SIS Staff - ');

insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10190', 'meta_data', 'enable_permission', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10190', 'exam_admit_card_preferences', 'include_class_roll_number', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10190', 'meta_data', 'institute_name_in_sms', 'SANSKAR INT. SCHOOL');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10190', 'meta_data', 'institute_unique_code', 'sis190');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10190', 'meta_data', 'sms_service_enabled', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10190', 'sms_preferences', 'buffer_sms_count', 0);
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10190', 'inventory_preferences', 'inventory_institutes_scope', '[10190]');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10190', 'meta_data', 'registration_counter', 'true');
--insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10190', 'meta_data', 'admission_counter', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10190', 'meta_data', 'staff_counter', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10190', 'meta_data', 'module_access', '["ADMISSION","FEES","STORE","TRANSPORT","COURSES","EXAMINATION","ATTENDANCE","STAFF_MANAGEMENT","INCOME_EXPENSE","USER_MANAGEMENT","LECTURE_MANAGEMENT","HOMEWORK_MANAGEMENT","SALARY_MANAGEMENT","NOTICE_BOARD_MANAGEMENT","STUDENT_MANAGEMENT","AUDIT_LOGS","COMMUNICATION","STAFF_ATTENDANCE"]');
--["ADMISSION","FEES","STORE","TRANSPORT","COURSES","EXAMINATION","ATTENDANCE","STAFF_MANAGEMENT","INCOME_EXPENSE","USER_MANAGEMENT","LECTURE_MANAGEMENT","HOMEWORK_MANAGEMENT","SALARY_MANAGEMENT","NOTICE_BOARD_MANAGEMENT","STUDENT_MANAGEMENT","AUDIT_LOGS","COMMUNICATION","STAFF_ATTENDANCE"]
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10190', 'sms_preferences', 'student_registration_sms_enabled', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10190', 'sms_preferences', 'student_admission_sms_enabled', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10190', 'sms_preferences', 'fee_payment_cancellation_admin_sms_enabled', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10190', 'sms_preferences', 'fee_payment_cancellation_admin_sms_contact_number', '9462267596');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10190', 'meta_data', 'birthday_sms_enabled', 'true');

insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(10190, "Total", "SYSTEM", "NUMBER", 1);
insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(10190, "Grade", "SYSTEM", "GRADE", 1);

--insert into default_fee_assignment_structure_meta_data values(10190, "Installments", "1aed4d2d-2d8e-4741-8b4c-6454126c1701", "ENROLLMENT");
--insert into default_fee_assignment_structure select "1dd925f4-4137-4eda-96f5-eebb8467d42d", (select standard_id from standards where institute_id=10190 and standard_name = 'LKG'), "CLASS", fee_id , 202, 1750 from fee_configuration where institute_id = 10190 and fee_type = "REGULAR" and academic_session_id = 84;
--insert into default_fee_assignment_structure select "1dd925f4-4137-4eda-96f5-eebb8467d42d", (select standard_id from standards where institute_id=10190 and standard_name = 'UKG'), "CLASS", fee_id , 202, 1760 from fee_configuration where institute_id = 10190 and fee_type = "REGULAR" and academic_session_id = 84;
--insert into default_fee_assignment_structure select "1dd925f4-4137-4eda-96f5-eebb8467d42d", (select standard_id from standards where institute_id=10190 and standard_name = 'I'), "CLASS", fee_id , 202, 1950 from fee_configuration where institute_id = 10190 and fee_type = "REGULAR" and academic_session_id = 84;
--insert into default_fee_assignment_structure select "1dd925f4-4137-4eda-96f5-eebb8467d42d", (select standard_id from standards where institute_id=10190 and standard_name = 'II'), "CLASS", fee_id , 202, 2000 from fee_configuration where institute_id = 10190 and fee_type = "REGULAR" and academic_session_id = 84;
--insert into default_fee_assignment_structure select "1dd925f4-4137-4eda-96f5-eebb8467d42d", (select standard_id from standards where institute_id=10190 and standard_name = 'III'), "CLASS", fee_id , 202, 2000 from fee_configuration where institute_id = 10190 and fee_type = "REGULAR" and academic_session_id = 84;
--insert into default_fee_assignment_structure select "1dd925f4-4137-4eda-96f5-eebb8467d42d", (select standard_id from standards where institute_id=10190 and standard_name = 'IV'), "CLASS", fee_id , 202, 2150 from fee_configuration where institute_id = 10190 and fee_type = "REGULAR" and academic_session_id = 84;
--insert into default_fee_assignment_structure select "1dd925f4-4137-4eda-96f5-eebb8467d42d", (select standard_id from standards where institute_id=10190 and standard_name = 'V'), "CLASS", fee_id , 202, 2150 from fee_configuration where institute_id = 10190 and fee_type = "REGULAR" and academic_session_id = 84;
--insert into default_fee_assignment_structure select "1dd925f4-4137-4eda-96f5-eebb8467d42d", (select standard_id from standards where institute_id=10190 and standard_name = 'VI'), "CLASS", fee_id , 202, 2200 from fee_configuration where institute_id = 10190 and fee_type = "REGULAR" and academic_session_id = 84;
--insert into default_fee_assignment_structure select "1dd925f4-4137-4eda-96f5-eebb8467d42d", (select standard_id from standards where institute_id=10190 and standard_name = 'VII'), "CLASS", fee_id , 202, 2350 from fee_configuration where institute_id = 10190 and fee_type = "REGULAR" and academic_session_id = 84;
--insert into default_fee_assignment_structure select "1dd925f4-4137-4eda-96f5-eebb8467d42d", (select standard_id from standards where institute_id=10190 and standard_name = 'VIII'), "CLASS", fee_id , 202, 2350 from fee_configuration where institute_id = 10190 and fee_type = "REGULAR" and academic_session_id = 84;
--insert into default_fee_assignment_structure select "1dd925f4-4137-4eda-96f5-eebb8467d42d", (select standard_id from standards where institute_id=10190 and standard_name = 'IX'), "CLASS", fee_id , 202, 2700 from fee_configuration where institute_id = 10190 and fee_type = "REGULAR" and academic_session_id = 84;
--insert into default_fee_assignment_structure select "1dd925f4-4137-4eda-96f5-eebb8467d42d", (select standard_id from standards where institute_id=10190 and standard_name = 'X'), "CLASS", fee_id , 202, 2700 from fee_configuration where institute_id = 10190 and fee_type = "REGULAR" and academic_session_id = 84;
--insert into default_fee_assignment_structure select "1dd925f4-4137-4eda-96f5-eebb8467d42d", (select standard_id from standards where institute_id=10190 and standard_name = 'XI' and stream = 'SCIENCE'), "CLASS", fee_id , 202, 3480 from fee_configuration where institute_id = 10190 and fee_type = "REGULAR" and academic_session_id = 84;
--insert into default_fee_assignment_structure select "1dd925f4-4137-4eda-96f5-eebb8467d42d", (select standard_id from standards where institute_id=10190 and standard_name = 'XI' and stream = 'COMMERCE'), "CLASS", fee_id , 202, 3180 from fee_configuration where institute_id = 10190 and fee_type = "REGULAR" and academic_session_id = 84;
--insert into default_fee_assignment_structure select "1dd925f4-4137-4eda-96f5-eebb8467d42d", (select standard_id from standards where institute_id=10190 and standard_name = 'XII' and stream = 'SCIENCE'), "CLASS", fee_id , 202, 3400 from fee_configuration where institute_id = 10190 and fee_type = "REGULAR" and academic_session_id = 84;
--insert into default_fee_assignment_structure select "1dd925f4-4137-4eda-96f5-eebb8467d42d", (select standard_id from standards where institute_id=10190 and standard_name = 'XII' and stream = 'COMMERCE'), "CLASS", fee_id , 202, 3100 from fee_configuration where institute_id = 10190 and fee_type = "REGULAR" and academic_session_id = 84;

--Insert classes
-- cd Lernen/lernen-backend/database_scripts/Drop30/data-server/
-- sh _10190_classes.sh http://api.embrate.com:8080/data-server 0006cba5-a7d7-4acb-9819-dd9ce01d9852
insert into standards_metadata select 10190, 84, standard_id, 1, 0 from standards where institute_id = 10190;

--add range display name also
SET @institute_id := 10190;
SET @academic_session_id := 84;
SET @course_type := "SCHOLASTIC";

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A1', 10, 0.895, 1.1, '90-100' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A2', 9, 0.795, 0.895, '80-89' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B1', 8, 0.695, 0.795, '70-79' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B2', 7, 0.595, 0.695, '60-69' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C1', 6, 0.495, 0.595, '50-59' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C2', 5, 0.395, 0.495, '40-49' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 4, 0.0, 0.395, '00-39' from standards where institute_id = @institute_id;

-- Setting CO-SCHOLASTIC Grades
SET @institute_id := 10190;
SET @academic_session_id := 84;
SET @course_type := "COSCHOLASTIC";

--A1       - 0.895 - 1.100        - 90-100
--A2       - 0.795 - 0.895     - 80-89
--B1       - 0.695 - 0.795     - 70-79
--B2       - 0.595 - 0.695      - 60-69
--C1       - 0.495 - 0.595     - 50-59
--C2       - 0.395 - 0.495      - 40-49
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A1', 10, 0.895, 1.1, '90-100' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A2', 9, 0.795, 0.895, '80-89' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B1', 8, 0.695, 0.795, '70-79' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B2', 7, 0.595, 0.695, '60-69' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C1', 6, 0.495, 0.595, '50-59' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C2', 5, 0.395, 0.495, '40-49' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 4, 0.0, 0.395, '00-39' from standards where institute_id = @institute_id;


--select "348bfd28-7193-4cf1-ac4b-18ebb0ce1b4d", (select standard_id from standards where institute_id=10190 and standard_name = 'Standard-VI'), "CLASS", fee_id , 150, 1400 from fee_configuration where institute_id = 10190 and fee_type = "REGULAR" and academic_session_id = 84;
--insert into default_fee_assignment_structure select "348bfd28-7193-4cf1-ac4b-18ebb0ce1b4d", (select standard_id from standards where institute_id=10190 and standard_name = 'Standard-VI'), "CLASS", fee_id , 150, 1400 from fee_configuration where institute_id = 10190 and fee_type = "REGULAR" and academic_session_id = 84;

--insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10190', 'web_ui_preferences', 'background_image_enable', 'true');
--insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10190', 'web_ui_preferences', 'background_image_url', '/static/core/images/10190_bg_logo.png');
--insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10190', 'web_ui_preferences', 'school_name_logo_position', 'CENTER');

--Add default exam
