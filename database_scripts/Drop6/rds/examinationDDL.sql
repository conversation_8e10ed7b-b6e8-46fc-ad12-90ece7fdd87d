CREATE TABLE IF NOT EXISTS exam_dimensions (
dimension_id int PRIMARY KEY AUTO_INCREMENT,
institute_id int NOT NULL,
dimension_name varchar(255) NOT NULL,
dimension_type varchar(16) NOT NULL,
evaluation_type varchar(16) NOT NULL,
is_total boolean NOT NULL,
FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
UNIQUE KEY (institute_id, dimension_name)
);


CREATE TABLE IF NOT EXISTS exam_metadata (
exam_id varchar(36) PRIMARY KEY NOT NULL,
exam_name varchar(1024) NOT NULL,
exam_type varchar(16) NOT NULL,
standard_id varchar(36) NOT NULL,
academic_session_id int NOT NULL,
parent_exam_id varchar(36),
FOREIGN KEY (standard_id) REFERENCES standards(standard_id),
FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id),
UNIQUE KEY (academic_session_id, standard_id, exam_name)
);

CREATE TABLE IF NOT EXISTS exam_dimensions_mapping (
exam_id varchar(36) NOT NULL,
course_type varchar(16) NOT NULL,
dimension_id int NOT NULL,
default_max_marks double,
default_min_marks double,
default_max_grade int,
default_min_grade int,
PRIMARY KEY (exam_id, course_type, dimension_id),
FOREIGN KEY (exam_id) REFERENCES exam_metadata(exam_id),
FOREIGN KEY (dimension_id) REFERENCES exam_dimensions(dimension_id)
);

CREATE TABLE IF NOT EXISTS exam_graph_edges (
exam_id varchar(36) NOT NULL,
child_exam_id varchar(36) NOT NULL,
PRIMARY KEY (exam_id, child_exam_id),
FOREIGN KEY (exam_id) REFERENCES exam_metadata(exam_id),
FOREIGN KEY (child_exam_id) REFERENCES exam_metadata(exam_id)
);

CREATE TABLE IF NOT EXISTS exam_courses_assignment (
exam_id varchar(36) NOT NULL,
course_id varchar(36) NOT NULL,
dimension_id int NOT NULL,
max_marks double,
min_marks double,
max_grade int,
min_grade int,
PRIMARY KEY (exam_id, course_id, dimension_id),
FOREIGN KEY (exam_id) REFERENCES exam_metadata(exam_id),
FOREIGN KEY (course_id) REFERENCES class_courses(course_id),
FOREIGN KEY (dimension_id) REFERENCES exam_dimensions(dimension_id)
);

CREATE TABLE IF NOT EXISTS marks_feeding (
student_id varchar(36) NOT NULL,
exam_id varchar(36) NOT NULL,
course_id varchar(36) NOT NULL,
dimension_id int NOT NULL,
marks_obtained double,
grade_obtained int,
grace_marks int,
PRIMARY KEY (student_id, exam_id, course_id, dimension_id),
FOREIGN KEY (student_id) REFERENCES students(student_id),
FOREIGN KEY (exam_id, course_id, dimension_id) REFERENCES exam_courses_assignment(exam_id, course_id, dimension_id),
FOREIGN KEY (course_id) REFERENCES class_courses(course_id),
FOREIGN KEY (dimension_id) REFERENCES exam_dimensions(dimension_id)
);
