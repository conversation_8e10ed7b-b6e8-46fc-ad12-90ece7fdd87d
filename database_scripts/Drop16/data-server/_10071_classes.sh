
INSTITUTE_STANDARDS_URL=$1/2.0/institute/standard
    
curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10071",
  "academicSessionId" : 29,
  "standardName": "Class IA",
  "stream" : "NA",
  "level" : 1,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10071",
  "academicSessionId" : 29,
  "standardName": "Class IB",
  "stream" : "NA",
  "level" : 2,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10071",
  "academicSessionId" : 29,
  "standardName": "Class 2",
  "stream" : "NA",
  "level" : 3,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10071",
  "academicSessionId" : 29,
  "standardName": "Class 3",
  "stream" : "NA",
  "level" : 4,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10071",
  "academicSessionId" : 29,
  "standardName": "Class 4",
  "stream" : "NA",
  "level" : 5,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10071",
  "academicSessionId" : 29,
  "standardName": "Class 5",
  "stream" : "NA",
  "level" : 6,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10071",
  "academicSessionId" : 29,
  "standardName": "Class 6",
  "stream" : "NA",
  "level" : 7,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10071",
  "academicSessionId" : 29,
  "standardName": "Class 7",
  "stream" : "NA",
  "level" : 8,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL


curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10071",
  "academicSessionId" : 29,
  "standardName": "Class 8",
  "stream" : "NA",
  "level" : 9,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10071",
  "academicSessionId" : 29,
  "standardName": "Class 9",
  "stream" : "NA",
  "level" : 10,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10071",
  "academicSessionId" : 29,
  "standardName": "Class 10",
  "stream" : "NA",
  "level" : 11,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10071",
  "academicSessionId" : 29,
  "standardName": "Class 11",
  "stream" : "SCIENCE",
  "level" : 12,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10071",
  "academicSessionId" : 29,
  "standardName": "Class 11",
  "stream" : "COMMERCE",
  "level" : 13,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10071",
  "academicSessionId" : 29,
  "standardName": "Class 11",
  "stream" : "ARTS",
  "level" : 14,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10071",
  "academicSessionId" : 29,
  "standardName": "Class 12",
  "stream" : "SCIENCE",
  "level" : 15,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10071",
  "academicSessionId" : 29,
  "standardName": "Class 12",
  "stream" : "COMMERCE",
  "level" : 16,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10071",
  "academicSessionId" : 29,
  "standardName": "Class 12",
  "stream" : "ARTS",
  "level" : 17,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL