insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('GLOBAL', 'GLOBAL', 'exam_admit_card_preferences', 'items_per_row', '2');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('GLO<PERSON>L', 'GL<PERSON>BAL', 'exam_admit_card_preferences', 'rows_per_page', '5');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('GLOBAL', 'GLOBAL', 'exam_admit_card_preferences', 'title_text', 'EXAM ADMIT CARD');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('GLO<PERSON>L', 'GLOBAL', 'exam_admit_card_preferences', 'bold_exam_name', 'false');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('<PERSON><PERSON><PERSON><PERSON><PERSON>', 'GLOBAL', 'exam_admit_card_preferences', 'session_display_format', 'START_END_YEAR_FULL');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('GLOBAL', 'GLOBAL', 'exam_admit_card_preferences', 'admission_number_key_text', 'ADM. No.');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('GLOBAL', 'GLOBAL', 'exam_admit_card_preferences', 'signature_designation_text', 'Class Teacher Signature');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('GLOBAL', 'GLOBAL', 'exam_admit_card_preferences', 'include_class_roll_number', 'false');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('GLOBAL', 'GLOBAL', 'exam_admit_card_preferences', 'header_section_table_widths', '[0.15, 0.7, 0.15]');


insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10045', 'exam_admit_card_preferences', 'rows_per_page', '4');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10045', 'exam_admit_card_preferences', 'title_text', 'ADMIT CARD');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10045', 'exam_admit_card_preferences', 'bold_exam_name', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10045', 'exam_admit_card_preferences', 'session_display_format', 'ONLY_START_YEAR');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10045', 'exam_admit_card_preferences', 'admission_number_key_text', 'ADM. No.');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10045', 'exam_admit_card_preferences', 'signature_designation_text', 'Signature of Institution Head');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10045', 'exam_admit_card_preferences', 'include_class_roll_number', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10045', 'exam_admit_card_preferences', 'header_section_table_widths', '[0.12, 0.76, 0.12]');


insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10046', 'exam_admit_card_preferences', 'rows_per_page', '4');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10046', 'exam_admit_card_preferences', 'title_text', 'ADMIT CARD');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10046', 'exam_admit_card_preferences', 'bold_exam_name', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10046', 'exam_admit_card_preferences', 'session_display_format', 'ONLY_START_YEAR');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10046', 'exam_admit_card_preferences', 'admission_number_key_text', 'ADM. No.');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10046', 'exam_admit_card_preferences', 'signature_designation_text', 'Signature of Institution Head');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10046', 'exam_admit_card_preferences', 'include_class_roll_number', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10046', 'exam_admit_card_preferences', 'header_section_table_widths', '[0.12, 0.76, 0.12]');


insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10050', 'exam_admit_card_preferences', 'rows_per_page', '4');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10051', 'exam_admit_card_preferences', 'rows_per_page', '4');
