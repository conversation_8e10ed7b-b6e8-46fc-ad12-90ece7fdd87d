CREATE TABLE IF NOT EXISTS complain_category (
        institute_id int  NOT NULL,
        complain_category_id int PRIMARY KEY AUTO_INCREMENT NOT NULL,
        complaint_category_name varchar(500) NOT NULL,
        created_by varchar(36) NOT NULL,
        created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_by varchar(36),
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY (institute_id,complaint_category_name),
        FOREIGN KEY (institute_id) REFERENCES institute(institute_id)
);

CREATE TABLE IF NOT EXISTS standard_complain_response (
        institute_id int NOT NULL,
        standard_complain_response_id varchar(36) PRIMARY KEY NOT NULL,
        standard_response text NOT NULL,
        complain_category_id int NOT NULL,
        FOREIGN KEY (complain_category_id) REFERENCES         complain_category(complain_category_id),
        FOREI<PERSON><PERSON> KEY (institute_id) REFERENCES institute(institute_id)
);

CREATE TABLE IF NOT EXISTS complain_metadata (
        institute_id int NOT NULL,
        complain_id varchar(36) PRIMARY KEY NOT NULL,
        complain_number varchar(256)  NOT NULL,
        title varchar(256) NOT NULL,
        description text,
        complaint_by varchar(256) NOT NULL,
        complaint_for varchar(36),
        status varchar(256)  NOT NULL,    
        attachments text,
        category_id int NOT NULL,
        created_by varchar(36) NOT NULL,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        closed_by varchar(36),
        closed_at TIMESTAMP NULL,
        FOREIGN KEY (category_id) REFERENCES complain_category(complain_category_id),
        FOREIGN KEY (institute_id) REFERENCES institute(institute_id)
);

CREATE TABLE IF NOT EXISTS complain_responses (
        response_id varchar(36) PRIMARY KEY NOT NULL,
        response text NOT NULL,
        response_by  varchar(36) NOT NULL,
        response_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        complain_id varchar(36) NOT NULL,
        FOREIGN KEY (complain_id) REFERENCES complain_metadata(complain_id)
  );

insert into counters (institute_id, counter_type, count, counter_prefix) values (702, 'COMPLAIN_NUMBER', 1, "");
--Run on PROD
--insert into counters select institute_id, 'COMPLAIN_NUMBER', 1, "" from institute;

ALTER TABLE standard_complain_response CHANGE standard_response standard_response text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL;

ALTER TABLE complain_metadata CHANGE title title varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL;
ALTER TABLE complain_metadata CHANGE description description text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

ALTER TABLE complain_responses CHANGE response response text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL;

