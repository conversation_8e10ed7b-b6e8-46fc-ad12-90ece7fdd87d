
CREATE TABLE IF NOT EXISTS diary_remarks_category (
    institute_id int  NOT NULL,
    category_id int PRIMARY KEY AUTO_INCREMENT NOT NULL,
    category_name varchar(500) NOT NULL,
    created_by varchar(36) NOT NULL,
    created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by varchar(36),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY (institute_id,category_name),
    FOREIGN KEY (institute_id) REFERENCES institute(institute_id)
);

CREATE TABLE IF NOT EXISTS remark_details (
        institute_id int NOT NULL,
        academic_session_id int NOT NULL,
        remark_id varchar(36) PRIMARY KEY NOT NULL,
        title varchar(256) NOT NULL,
        description text,
        attachments text,
        category_id int NOT NULL,
        date timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        created_by varchar(36) NOT NULL,
        created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_by varchar(36),
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES diary_remarks_category(category_id),
        FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
        FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id)
);

CREATE TABLE IF NOT EXISTS remark_student_mappings (
    remark_id varchar(36) NOT NULL,
    student_id varchar(36) NOT NULL,
    viewed_on timestamp,
    PRIMARY KEY (remark_id, student_id),
    FOREIGN KEY (remark_id) REFERENCES remark_details(remark_id),
    FOREIGN KEY (student_id) REFERENCES students(student_id)
);