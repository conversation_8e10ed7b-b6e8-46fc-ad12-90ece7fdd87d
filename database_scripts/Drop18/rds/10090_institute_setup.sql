use lernen

--INSERT INTO organisation (organisation_id, name)
--VALUES ("048a25e4-62e6-11ea-bc55-0242ac130003", "A.G. MISSION SCHOOL");

INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country, zipcode, landmark, logo_url, email, phone_number, letter_head_line1, letter_head_line2, institute_unique_code) 
VALUES (10090,'THAKUR K.S.H.D. PUBLIC INTER COLLEGE', 'RAJ NAGAR, N.T.P.C.,', 'DADRI, G.B.NAGAR', '', '', 'India', '', '', '', '<EMAIL>', '8826755064,8630422006,9891369069', '', '', "2de1b155-8cf5-4676-a783-d6719901fdd7");

--update institute set branch_name = "A.G. MISSION SCHOOL", institute_name = "A.G. MISSION SCHOOL", logo_url="" where institute_id = 10090;

INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month)
values(10090, 2020, 2021, 4, 3);

insert into fee_category (institute_id, fee_category, description) values (10090, 'Academic Fee' ,'Academic Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10090, 'Hostel Fee' ,'Hostel Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10090, 'Transport Fee' ,'Transport Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10090, 'Admission Fee' ,'Admission Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10090, 'Examination Fee' ,'Examination Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10090, 'Miscellaneous Fee' ,'Miscellaneous Fees');
insert into fee_category (institute_id, fee_category, description) values (10090, 'Other Fee' ,'Any Other Fees');


INSERT INTO fee_head_configuration(institute_id, fee_category_id, fee_head, refundable, description, fee_head_type, fee_head_tag)
VALUES(10090, (SELECT fee_category_id from fee_category where institute_id = 10090 and fee_category = 'Transport Fee'), 'Transport', 1, null, 'SYSTEM', 'TRANSPORT');


insert into categories (institute_id,category_name,genders) values (10090,'stationery',0);
insert into categories (institute_id,category_name,genders) values (10090,'books',0);
insert into categories (institute_id,category_name) values (10090,'clothing');
insert into categories (institute_id,category_name) values (10090,'foot wear');
insert into categories (institute_id,category_name,genders,colors) values (10090,'note book',0,0);
insert into categories (institute_id,category_name) values (10090,'art & craft');
insert into categories (institute_id,category_name) values (10090,'personal care');
insert into categories (institute_id,category_name) values (10090,'bedding');
insert into categories (institute_id,category_name,genders,colors) values (10090,'energy drinks',0,0);
insert into categories (institute_id,category_name) values (10090,'accessories');
insert into categories (institute_id,category_name) values (10090,'furniture');
insert into categories (institute_id,category_name) values (10090,'electronics');
insert into categories (institute_id,category_name) values (10090,'sports');


insert into colors (institute_id, color_name) values (10090,'maroon');
insert into colors (institute_id, color_name) values (10090,'black');
insert into colors (institute_id, color_name) values (10090,'brown');
insert into colors (institute_id, color_name) values (10090,'white');
insert into colors (institute_id, color_name) values (10090,'red');
insert into colors (institute_id, color_name) values (10090,'yellow');
insert into colors (institute_id, color_name) values (10090,'blue');
insert into colors (institute_id, color_name) values (10090,'navy blue');
insert into colors (institute_id, color_name) values (10090,'green');
insert into colors (institute_id, color_name) values (10090,'dark green');
insert into colors (institute_id, color_name) values (10090,'pink');
insert into colors (institute_id, color_name) values (10090,'purple');
insert into colors (institute_id, color_name) values (10090,'grey');
insert into colors (institute_id, color_name) values (10090,'olive');
insert into colors (institute_id, color_name) values (10090,'cyan');
insert into colors (institute_id, color_name) values (10090,'magenta');


insert into counters (institute_id, counter_type, count, counter_prefix) values (10090, 'FEE_INVOICE', 1, "");
-- insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10090', 'meta_data', 'enable_permission', 'true');

--insert into default_fee_assignment_structure_meta_data values(10090, "Installments", "1aed4d2d-2d8e-4741-8b4c-6454126c1701", "ENROLLMENT");
--insert into default_fee_assignment_structure select "1aed4d2d-2d8e-4741-8b4c-6454126c1701", (select standard_id from standards where institute_id=10090 and standard_name = '12th' and stream = 'COMMERCE'), "CLASS", fee_id , 80, 6900 from fee_configuration where institute_id = 10090 and fee_type = "REGULAR" and academic_session_id = 26;



insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(10090, "Total", "SYSTEM", "NUMBER", 1);
insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(10090, "Grade", "SYSTEM", "GRADE", 1);