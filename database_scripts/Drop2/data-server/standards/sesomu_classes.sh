
INSTITUTE_STANDARDS_URL=$1/2.0/institute/standard

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10001",
  "standardName": "Play Group",
  "stream" : "NA",
  "level" : 1,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10001",
  "standardName": "Nursery",
  "stream" : "NA",
  "level" : 2,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10001",
  "standardName": "LKG",
  "stream" : "NA",
  "level" : 3,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10001",
  "standardName": "UKG",
  "stream" : "NA",
  "level" : 4,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10001",
  "standardName": "Class I",
  "stream" : "NA",
  "level" : 5,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10001",
  "standardName": "Class II",
  "stream" : "NA",
  "level" : 6,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL


curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10001",
  "standardName": "Class III",
  "stream" : "NA",
  "level" : 7,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL


curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10001",
  "standardName": "Class IV",
  "stream" : "NA",
  "level" : 8,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL


curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10001",
  "standardName": "Class V",
  "stream" : "NA",
  "level" : 9,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10001",
  "standardName": "Class VI",
  "stream" : "NA",
  "level" : 10,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10001",
  "standardName": "Class VII",
  "stream" : "NA",
  "level" : 11,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10001",
  "standardName": "Class VIII",
  "stream" : "NA",
  "level" : 12,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10001",
  "standardName": "Class IX",
  "stream" : "NA",
  "level" : 13,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10001",
  "standardName": "Class X",
  "stream" : "NA",
  "level" : 14,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10001",
  "standardName": "Class XI",
  "stream" : "SCIENCE",
  "level" : 15,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10001",
  "standardName": "Class XI",
  "stream" : "COMMERCE",
  "level" : 16,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10001",
  "standardName": "Class XII",
  "stream" : "SCIENCE",
  "level" : 17,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10001",
  "standardName": "Class XII",
  "stream" : "COMMERCE",
  "level" : 18,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL