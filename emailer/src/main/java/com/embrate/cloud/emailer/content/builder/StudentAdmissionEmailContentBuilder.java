/**
 * 
 */
package com.embrate.cloud.emailer.content.builder;

import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.user.authentication.RegeneratePasswordUserData;
import com.lernen.cloud.core.utils.TemplateProvider;
import org.apache.velocity.Template;
import org.apache.velocity.VelocityContext;

import java.io.IOException;
import java.io.StringWriter;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 *
 */
public class StudentAdmissionEmailContentBuilder extends EmailContentBuilder<RegeneratePasswordUserData<Void>> {

	private static final String INSTITUTE_NAME = "institute_name";
	private static final String USERNAME = "username";
	private static final String PASSWORD = "password";
	private static String IOS_APP_LINK_URL_KEY = "ios_app_link_url";
	private static String ANDROID_APP_LINK_URL_KEY = "android_app_link_url";


	private static String IOS_APP_LINK_URL_VALUE = "https://apps.apple.com/in/app/embrate/id1629953705";
	private static String _10170_10171_IOS_APP_LINK_URL_VALUE = "https://apps.apple.com/in/app/navjyoti-convent-school/id6443655800";
	private static String _10180_IOS_APP_LINK_URL_VALUE = "https://apps.apple.com/in/app/nosegay-convent-school/id1664867159";
	private static String _10190_IOS_APP_LINK_URL_VALUE = "https://apps.apple.com/in/app/sanskar-school-sikar/id6443972097";
	private static String _10225_IOS_APP_LINK_URL_VALUE = "https://apps.apple.com/in/app/jhunjhunu-international-school/id6451259996";
	private static String _10226_IOS_APP_LINK_URL_VALUE = "https://apps.apple.com/in/app/jhunjhunu-academy-maan-nagar/id6451262548";
	private static String _10227_IOS_APP_LINK_URL_VALUE = "https://apps.apple.com/in/app/jhunjhunu-academy-gaushala-rd/id6451377877";


	private static String ANDROID_APP_LINK_URL_VALUE = "https://play.google.com/store/apps/details?id=com.embrate.cloud";
	private static String _10170_10171_ANDROID_APP_LINK_URL_VALUE = "https://play.google.com/store/apps/details?id=com.embrate.cloud.navjyoti";
	private static String _10180_ANDROID_APP_LINK_URL_VALUE = "https://play.google.com/store/apps/details?id=com.embrate.cloud.nosegay";
	private static String _10190_ANDROID_APP_LINK_URL_VALUE = "https://play.google.com/store/apps/details?id=com.embrate.cloud.sanskar";
	private static String _10225_ANDROID_APP_LINK_URL_VALUE = "https://play.google.com/store/apps/details?id=com.embrate.cloud.jis";
	private static String _10226_ANDROID_APP_LINK_URL_VALUE = "https://play.google.com/store/apps/details?id=com.embrate.cloud.jamn";
	private static String _10227_ANDROID_APP_LINK_URL_VALUE = "https://play.google.com/store/apps/details?id=com.embrate.cloud.jagr";

	
	private static Map<Integer, String> INSTITUTE_IOS_APP_LINK_MAP = new HashMap<>();
	private static Map<Integer, String> INSTITUTE_ANDROID_APP_LINK_MAP = new HashMap<>();
	
	static {
		INSTITUTE_IOS_APP_LINK_MAP.put(10170, _10170_10171_IOS_APP_LINK_URL_VALUE);
		INSTITUTE_IOS_APP_LINK_MAP.put(10171, _10170_10171_IOS_APP_LINK_URL_VALUE);
		INSTITUTE_IOS_APP_LINK_MAP.put(10180, _10180_IOS_APP_LINK_URL_VALUE);
		INSTITUTE_IOS_APP_LINK_MAP.put(10190, _10190_IOS_APP_LINK_URL_VALUE);
		INSTITUTE_IOS_APP_LINK_MAP.put(10225, _10225_IOS_APP_LINK_URL_VALUE);
		INSTITUTE_IOS_APP_LINK_MAP.put(10226, _10226_IOS_APP_LINK_URL_VALUE);
		INSTITUTE_IOS_APP_LINK_MAP.put(10227, _10227_IOS_APP_LINK_URL_VALUE);
		
		INSTITUTE_ANDROID_APP_LINK_MAP.put(10170, _10170_10171_ANDROID_APP_LINK_URL_VALUE);
		INSTITUTE_ANDROID_APP_LINK_MAP.put(10171, _10170_10171_ANDROID_APP_LINK_URL_VALUE);
		INSTITUTE_ANDROID_APP_LINK_MAP.put(10180, _10180_ANDROID_APP_LINK_URL_VALUE);
		INSTITUTE_ANDROID_APP_LINK_MAP.put(10190, _10190_ANDROID_APP_LINK_URL_VALUE);
		INSTITUTE_ANDROID_APP_LINK_MAP.put(10225, _10225_ANDROID_APP_LINK_URL_VALUE);
		INSTITUTE_ANDROID_APP_LINK_MAP.put(10226, _10226_ANDROID_APP_LINK_URL_VALUE);
		INSTITUTE_ANDROID_APP_LINK_MAP.put(10227, _10227_ANDROID_APP_LINK_URL_VALUE);
	}
	
	@Override
	public String getEmailSubject() {
		return null;
	}

	public String getEmailSubjectWithInstituteName(String instituteName) {
		return "Welcome to " + instituteName +"!";
	}

	@Override
	public String getHTMLContent(RegeneratePasswordUserData<Void> regeneratePasswordUserData) {
		return null;
	}

	public String getHTMLContentWithInstituteName(int instituteId, String instituteName, RegeneratePasswordUserData<Student> regeneratePasswordUserData) {

		try {
			Template t = TemplateProvider.getTemplate(TemplateProvider.STUDENT_ADMISSION_WITH_CREDENTIALS_HTML);
			final VelocityContext context = new VelocityContext();
			context.put(INSTITUTE_NAME, instituteName);
			context.put(USERNAME, regeneratePasswordUserData.getUser().getUserName());
			context.put(PASSWORD, regeneratePasswordUserData.getNewPassword());
			context.put(IOS_APP_LINK_URL_KEY, getInstituteIOSLink(instituteId));
			context.put(ANDROID_APP_LINK_URL_KEY, getInstituteAndroidLink(instituteId));

			final StringWriter stringWriter = new StringWriter();
			t.merge(context, stringWriter);

			return stringWriter.toString();

		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}

	private String getInstituteIOSLink(int instituteId) {
		if(INSTITUTE_IOS_APP_LINK_MAP.containsKey(instituteId)) {
			return INSTITUTE_IOS_APP_LINK_MAP.get(instituteId);
		}
		return IOS_APP_LINK_URL_VALUE;
	}

	private String getInstituteAndroidLink(int instituteId) {
		if(INSTITUTE_ANDROID_APP_LINK_MAP.containsKey(instituteId)) {
			return INSTITUTE_ANDROID_APP_LINK_MAP.get(instituteId);
		}
		return ANDROID_APP_LINK_URL_VALUE;
	}

//	public String getHTMLContentWithInstituteNames(String instituteName, String userName, String password) {
//
//		try {
//			Template t = TemplateProvider.getTemplate(TemplateProvider.STUDENT_ADMISSION_WITH_CREDENTIALS_HTML);
//			final VelocityContext context = new VelocityContext();
//			context.put(INSTITUTE_NAME, instituteName);
//			context.put(USERNAME, userName);
//			context.put(PASSWORD, password);
//			context.put(IOS_APP_LINK_URL_KEY, IOS_APP_LINK_URL_VALUE);
//			context.put(ANDROID_APP_LINK_URL_KEY, ANDROID_APP_LINK_URL_VALUE);
//
//			final StringWriter stringWriter = new StringWriter();
//			t.merge(context, stringWriter);
//
//			return stringWriter.toString();
//
//		} catch (IOException e) {
//			e.printStackTrace();
//		}
//		return null;
//	}

}
