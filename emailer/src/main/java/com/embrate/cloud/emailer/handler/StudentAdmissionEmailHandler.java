/**
 * 
 */
package com.embrate.cloud.emailer.handler;

import com.embrate.cloud.emailer.content.builder.StudentAdmissionEmailContentBuilder;
import com.embrate.cloud.emailer.service.IEmailService;
import com.lernen.cloud.core.api.email.EmailData;
import com.lernen.cloud.core.api.email.EmailDestination;
import com.lernen.cloud.core.api.email.SendEmailResponse;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.user.authentication.RegeneratePasswordUserData;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Arrays;

/**
 * <AUTHOR>
 *
 */
public class StudentAdmissionEmailHandler {
	private static final Logger logger = LogManager.getLogger(StudentAdmissionEmailHandler.class);

	private final static String FROM_ADDRESS = "<EMAIL>";
	private final static String FROM_NAME = "Embrate School Management Portal";

	private final StudentAdmissionEmailContentBuilder studentAdmissionEmailContentBuilder;
	private final InstituteManager instituteManager;
	private final IEmailService emailService;

	public StudentAdmissionEmailHandler(StudentAdmissionEmailContentBuilder studentAdmissionEmailContentBuilder, InstituteManager instituteManager,
                                        IEmailService emailService) {
		this.studentAdmissionEmailContentBuilder = studentAdmissionEmailContentBuilder;
		this.instituteManager = instituteManager;
		this.emailService = emailService;
		
	}
	
	public void sendStudentAdmissionEmail(int instituteId, RegeneratePasswordUserData<Student> regeneratePasswordUserData) {

		if(instituteId <= 0) {
			logger.error("Invalid institute");
			return;
		}
		if (regeneratePasswordUserData == null || regeneratePasswordUserData.getUser() == null
				|| StringUtils.isBlank(regeneratePasswordUserData.getNewPassword())) {
			logger.error("Invalid input to send user credentials");
			return;
		}

		if(StringUtils.isBlank(regeneratePasswordUserData.getUser().getEmail())) {
			logger.error("Invalid student primary email {} to send email", regeneratePasswordUserData.getUser().getEmail());
			return;
		}

		Institute institute = instituteManager.getInstitute(instituteId);
		SendEmailResponse sendEmailResponse = emailService.sendEmail(new EmailData(FROM_ADDRESS,
				FROM_NAME, studentAdmissionEmailContentBuilder.getEmailSubjectWithInstituteName(institute.getInstituteName()),
				studentAdmissionEmailContentBuilder.getTextContent(),
				studentAdmissionEmailContentBuilder.getHTMLContentWithInstituteName(institute.getInstituteId(),
						institute.getInstituteName(), regeneratePasswordUserData),
				new EmailDestination(Arrays.asList(regeneratePasswordUserData.getUser().getEmail().trim()))));

		if (!sendEmailResponse.isSuccess()) {
			logger.error("Failed to generate student admission email for student {}, institute {}",
					regeneratePasswordUserData.getUser().getUuid(),
					regeneratePasswordUserData.getUser().getInstituteId());
			return;
		}

		logger.info("Student Admission Email send successfully for user {}, institute {}, message id {}",
				regeneratePasswordUserData.getUser().getUuid(), regeneratePasswordUserData.getUser().getInstituteId(),
				sendEmailResponse.getMessageId());

	}

}
