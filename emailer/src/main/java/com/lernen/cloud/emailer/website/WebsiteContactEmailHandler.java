package com.lernen.cloud.emailer.website;

import java.util.Arrays;
import java.util.Random;

import com.lernen.cloud.core.lib.google.auth.GoogleRecaptchaManager;
import org.apache.commons.lang3.StringUtils;

import com.embrate.cloud.emailer.service.IEmailService;
import com.lernen.cloud.core.api.email.EmailData;
import com.lernen.cloud.core.api.email.EmailDestination;
import com.lernen.cloud.core.api.email.SendEmailResponse;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.website.WebsiteContactEnquiryForm;

/**
 * 
 * <AUTHOR>
 *
 */
public class WebsiteContactEmailHandler {
	private final String FROM_ADDRESS = "<EMAIL>";
	private final String FROM_NAME = "Enquiry Embrate";
	private final String TO_ADDRESS = "<EMAIL>";
	private final String SUBJECT = "Website Contact";
	private final String NEXT_LINE = "\n";

	private final IEmailService emailService;
	private static final Random RANDOM = new Random();

	private final GoogleRecaptchaManager googleRecaptchaManager;

	public WebsiteContactEmailHandler(IEmailService emailService, GoogleRecaptchaManager googleRecaptchaManager) {
		this.emailService = emailService;
		this.googleRecaptchaManager = googleRecaptchaManager;
	}

	public boolean sendWebsiteEnquiryFormEmail(WebsiteContactEnquiryForm websiteContactEnquiryForm,  String recaptchaAuthenticationKey) {
		
		if(StringUtils.isBlank(websiteContactEnquiryForm.getName())) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_ENQUIRY_DETAILS, "Please enter your name."));
		}
		
		if(StringUtils.isBlank(websiteContactEnquiryForm.getEmail())) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_ENQUIRY_DETAILS, "Please enter your email id."));
		}
		
		if(StringUtils.isBlank(websiteContactEnquiryForm.getContactNumber())) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_ENQUIRY_DETAILS, "Please enter your contact number."));
		}
		
		if(StringUtils.isBlank(websiteContactEnquiryForm.getMessage())) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_ENQUIRY_DETAILS, "Please enter a message."));
		}

		final boolean verified = googleRecaptchaManager.validateRecaptchaUserToken(recaptchaAuthenticationKey);

		if (!verified) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_CAPTCHA, "Invalid captcha code"));
		}

		String subject = SUBJECT + " ("
				+ (StringUtils.isBlank(websiteContactEnquiryForm.getEmail()) ? RANDOM.nextInt(999999)
						: websiteContactEnquiryForm.getEmail())
				+ ")";

		String msg = "Name : " + websiteContactEnquiryForm.getName() + NEXT_LINE;
		msg += "Email : " + websiteContactEnquiryForm.getEmail() + NEXT_LINE;
		msg += "Contact Number : " + websiteContactEnquiryForm.getContactNumber() + NEXT_LINE;
		msg += "Message : " + websiteContactEnquiryForm.getMessage() + NEXT_LINE;
		EmailData emailData = new EmailData(FROM_ADDRESS, FROM_NAME, subject, msg, null,
				new EmailDestination(Arrays.asList(TO_ADDRESS)));
		SendEmailResponse sendEmailResponse = emailService.sendEmail(emailData);
		return sendEmailResponse == null ? false : sendEmailResponse.isSuccess();

	}

}
