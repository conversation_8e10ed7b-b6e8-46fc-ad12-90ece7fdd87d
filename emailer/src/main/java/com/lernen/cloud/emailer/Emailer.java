package com.lernen.cloud.emailer;

import com.embrate.cloud.emailer.service.IEmailService;
import com.lernen.cloud.core.api.email.EmailData;
import com.lernen.cloud.core.api.email.SendEmailResponse;

/**
 * 
 * <AUTHOR>
 *
 */
public abstract class Emailer {

	private final IEmailService emailService;

	public Emailer(IEmailService emailService) {
		this.emailService = emailService;
	}

	/**
	 * Method which is responsible for sending email given the preprared content
	 * to send
	 * 
	 * @return
	 */
	public boolean sendEmail(EmailData emailData) {
		SendEmailResponse sendEmailResponse = emailService.sendEmail(emailData);
		return sendEmailResponse.isSuccess();
	}
}
