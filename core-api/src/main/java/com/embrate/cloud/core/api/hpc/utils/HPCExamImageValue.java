package com.embrate.cloud.core.api.hpc.utils;

import com.embrate.cloud.core.api.hpc.layout.HPCPDFAttributes;
import com.embrate.cloud.core.api.hpc.payload.HPCDocumentType;
import com.lernen.cloud.core.api.user.Document;

import java.util.List;

/**
 * <AUTHOR>
 */
public class HPCExamImageValue {

	private HPCExamType hpcExamType;

	private Float imageHeight;

	private Float imageWidth;

	private HPCDocumentType imageType;

	private Document<HPCDocumentType> image;

	public HPCExamType getHpcExamType() {
		return hpcExamType;
	}

	public void setHpcExamType(HPCExamType hpcExamType) {
		this.hpcExamType = hpcExamType;
	}

	public Float getImageHeight() {
		return imageHeight;
	}

	public void setImageHeight(Float imageHeight) {
		this.imageHeight = imageHeight;
	}

	public Float getImageWidth() {
		return imageWidth;
	}

	public void setImageWidth(Float imageWidth) {
		this.imageWidth = imageWidth;
	}

	public HPCDocumentType getImageType() {
		return imageType;
	}

	public void setImageType(HPCDocumentType imageType) {
		this.imageType = imageType;
	}

	public Document<HPCDocumentType> getImage() {
		return image;
	}

	public void setImage(Document<HPCDocumentType> image) {
		this.image = image;
	}

	@Override
	public String toString() {
		return "HPCExamImageValue{" +
				"hpcExamType=" + hpcExamType +
				", imageHeight=" + imageHeight +
				", imageWidth=" + imageWidth +
				", imageType=" + imageType +
				", image=" + image +
				'}';
	}
}
