package com.embrate.cloud.core.api.courses.config;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class CloneCoursesResponse {

    private final CoursesCloneStatus status;
    private final CloneCoursesRequest request;
    private final List<String> successClasses;
    private final List<String> coursesAlreadyPresentClasses;
    private final List<String> errorClasses;

    public CloneCoursesResponse(CoursesCloneStatus status, CloneCoursesRequest request, List<String> successClasses, List<String> coursesAlreadyPresentClasses, List<String> errorClasses) {
        this.status = status;
        this.request = request;
        this.successClasses = successClasses;
        this.coursesAlreadyPresentClasses = coursesAlreadyPresentClasses;
        this.errorClasses = errorClasses;
    }

    public CoursesCloneStatus getStatus() {
        return status;
    }

    public CloneCoursesRequest getRequest() {
        return request;
    }

    public List<String> getSuccessClasses() {
        return successClasses;
    }

    public List<String> getCoursesAlreadyPresentClasses() {
        return coursesAlreadyPresentClasses;
    }

    public List<String> getErrorClasses() {
        return errorClasses;
    }

    @Override
    public String toString() {
        return "CloneCoursesResponse{" +
                "status=" + status +
                ", request=" + request +
                ", successClasses=" + successClasses +
                ", coursesAlreadyPresentClasses=" + coursesAlreadyPresentClasses +
                ", errorClasses=" + errorClasses +
                '}';
    }
}
