package com.embrate.cloud.core.api.inventory.v2.sale;

import java.util.UUID;

/**
 * <AUTHOR>
 */
public class ReturnedProduct {

	private UUID skuId;

	private UUID batchId;

	private int selectedQty;

	public UUID getSkuId() {
		return skuId;
	}

	public void setSkuId(UUID skuId) {
		this.skuId = skuId;
	}

	public UUID getBatchId() {
		return batchId;
	}

	public void setBatchId(UUID batchId) {
		this.batchId = batchId;
	}

	public int getSelectedQty() {
		return selectedQty;
	}

	public void setSelectedQty(int selectedQty) {
		this.selectedQty = selectedQty;
	}

	@Override
	public String toString() {
		return "ReturnedProduct{" +
				"skuId=" + skuId +
				", batchId=" + batchId +
				", selectedQty=" + selectedQty +
				'}';
	}
}
