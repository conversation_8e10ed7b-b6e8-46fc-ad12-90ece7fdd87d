package com.embrate.cloud.core.api.inventory.v2;

import java.util.UUID;

/**
 * <AUTHOR>
 */

public class ProductBatchUpdatePayload {

    private UUID batchId;

    private String batchName;

    private String description;

    private Double initialQuantity;

    private Double sellingPrice;

    private Double discount;

    private Boolean discountInPercent;

    private Double purchasePrice;

    private Double mrp;

    private Boolean defaultBatch;

    private Boolean active;

    public UUID getBatchId() {
        return batchId;
    }

    public void setBatchId(UUID batchId) {
        this.batchId = batchId;
    }

    public String getBatchName() {
        return batchName;
    }

    public void setBatchName(String batchName) {
        this.batchName = batchName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Double getInitialQuantity() {
        return initialQuantity;
    }

    public void setInitialQuantity(Double initialQuantity) {
        this.initialQuantity = initialQuantity;
    }

    public Double getSellingPrice() {
        return sellingPrice;
    }

    public void setSellingPrice(Double sellingPrice) {
        this.sellingPrice = sellingPrice;
    }

    public Double getDiscount() {
        return discount;
    }

    public void setDiscount(Double discount) {
        this.discount = discount;
    }

    public Boolean getDiscountInPercent() {
        return discountInPercent;
    }

    public void setDiscountInPercent(Boolean discountInPercent) {
        this.discountInPercent = discountInPercent;
    }

    public Double getPurchasePrice() {
        return purchasePrice;
    }

    public void setPurchasePrice(Double purchasePrice) {
        this.purchasePrice = purchasePrice;
    }

    public Double getMrp() {
        return mrp;
    }

    public void setMrp(Double mrp) {
        this.mrp = mrp;
    }

    public Boolean getDefaultBatch() {
        return defaultBatch;
    }

    public void setDefaultBatch(Boolean defaultBatch) {
        this.defaultBatch = defaultBatch;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    @Override
    public String toString() {
        return "ProductBatchUpdatePayload{" +
                "batchId=" + batchId +
                ", batchName='" + batchName + '\'' +
                ", description='" + description + '\'' +
                ", initialQuantity=" + initialQuantity +
                ", sellingPrice=" + sellingPrice +
                ", discount=" + discount +
                ", discountInPercent=" + discountInPercent +
                ", purchasePrice=" + purchasePrice +
                ", mrp=" + mrp +
                ", defaultBatch=" + defaultBatch +
                ", active=" + active +
                '}';
    }
}
