/**
 * 
 */
package com.embrate.cloud.core.api.homework;

import com.lernen.cloud.core.api.course.Course;
import com.lernen.cloud.core.api.institute.Standard;

/**
 * <AUTHOR>
 *
 */
public class StandardHomeworkSubmissionDetailsRow {

	private final int instituteId;
	
	private final Standard standard;
	
	private final Course course;
	
	private final HomeworkDetails homeworkDetails;
	
	private final HomeworkSubmissionDetails homeworkSubmissionDetails;

	/**
	 * @param instituteId
	 * @param standard
	 * @param course
	 * @param homeworkDetails
	 * @param homeworkSubmissionDetails
	 */
	public StandardHomeworkSubmissionDetailsRow(int instituteId, Standard standard, Course course,
			HomeworkDetails homeworkDetails, HomeworkSubmissionDetails homeworkSubmissionDetails) {
		this.instituteId = instituteId;
		this.standard = standard;
		this.course = course;
		this.homeworkDetails = homeworkDetails;
		this.homeworkSubmissionDetails = homeworkSubmissionDetails;
	}

	/**
	 * @return the instituteId
	 */
	public int getInstituteId() {
		return instituteId;
	}

	/**
	 * @return the standard
	 */
	public Standard getStandard() {
		return standard;
	}

	/**
	 * @return the course
	 */
	public Course getCourse() {
		return course;
	}

	/**
	 * @return the homeworkDetails
	 */
	public HomeworkDetails getHomeworkDetails() {
		return homeworkDetails;
	}

	/**
	 * @return the homeworkSubmissionDetails
	 */
	public HomeworkSubmissionDetails getHomeworkSubmissionDetails() {
		return homeworkSubmissionDetails;
	}

	@Override
	public String toString() {
		return "StandardHomeworkSubmissionDetailsRow [instituteId=" + instituteId + ", standard=" + standard
				+ ", course=" + course + ", homeworkDetails=" + homeworkDetails + ", homeworkSubmissionDetails="
				+ homeworkSubmissionDetails + "]";
	}
}
