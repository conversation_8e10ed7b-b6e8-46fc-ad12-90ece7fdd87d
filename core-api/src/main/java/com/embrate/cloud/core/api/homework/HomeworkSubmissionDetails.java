/**
 * 
 */
package com.embrate.cloud.core.api.homework;

import java.util.List;
import java.util.UUID;

import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.user.Document;

/**
 * <AUTHOR>
 *
 */
public class HomeworkSubmissionDetails {	
	
	private final UUID homeworkSubmissionId;
	
	private final HomeworkDetails homeworkDetails;
	
	private final StudentLite studentLite;

	private final UUID submittedBy;
	
	private final String description;
	
	private final HomeworkSubmissionStatus homeworkSubmissionStatus;
	
	private final Integer submissionTimestamp;
	
	private final Integer facultyStatusTimestamp;
	
	private final UUID facultyUserId;
	
	private final Integer updatedTimestamp;
	
	private final String result;
	
	private final String remarks;
	
	private final List<Document<HomeworkDocumentType>> homeworkSubmissionAttachments;

	/**
	 * @param homeworkSubmissionId
	 * @param homeworkDetails
	 * @param studentLite
	 * @param submittedBy
	 * @param description
	 * @param homeworkSubmissionStatus
	 * @param submissionTimestamp
	 * @param facultyStatusTimestamp
	 * @param facultyUserId
	 * @param updatedTimestamp
	 * @param result
	 * @param remarks
	 * @param homeworkSubmissionAttachments
	 */
	public HomeworkSubmissionDetails(UUID homeworkSubmissionId, HomeworkDetails homeworkDetails,
			StudentLite studentLite,UUID submittedBy, String description, HomeworkSubmissionStatus homeworkSubmissionStatus,
			Integer submissionTimestamp,
			Integer facultyStatusTimestamp, UUID facultyUserId, Integer updatedTimestamp, String result, String remarks,
			List<Document<HomeworkDocumentType>> homeworkSubmissionAttachments) {
		this.homeworkSubmissionId = homeworkSubmissionId;
		this.homeworkDetails = homeworkDetails;
		this.studentLite = studentLite;
		this.submittedBy = submittedBy;
		this.description = description;
		this.homeworkSubmissionStatus = homeworkSubmissionStatus;
		this.submissionTimestamp = submissionTimestamp;
		this.facultyStatusTimestamp = facultyStatusTimestamp;
		this.facultyUserId = facultyUserId;
		this.updatedTimestamp = updatedTimestamp;
		this.result = result;
		this.remarks = remarks;
		this.homeworkSubmissionAttachments = homeworkSubmissionAttachments;
	}

	/**
	 * @return the homeworkSubmissionId
	 */
	public UUID getHomeworkSubmissionId() {
		return homeworkSubmissionId;
	}

	/**
	 * @return the homeworkDetails
	 */
	public HomeworkDetails getHomeworkDetails() {
		return homeworkDetails;
	}

	/**
	 * @return the studentLite
	 */
	public StudentLite getStudentLite() {
		return studentLite;
	}

	/**
	 * @return the submitted by
	 */
	public UUID getSubmittedBy() {
		return submittedBy;
	}

	/**
	 * @return the description
	 */
	public String getDescription() {
		return description;
	}

	/**
	 * @return the homeworkSubmissionStatus
	 */
	public HomeworkSubmissionStatus getHomeworkSubmissionStatus() {
		return homeworkSubmissionStatus;
	}

	/**
	 * @return the submissionTimestamp
	 */
	public Integer getSubmissionTimestamp() {
		return submissionTimestamp;
	}

	/**
	 * @return the facultyStatusTimestamp
	 */
	public Integer getFacultyStatusTimestamp() {
		return facultyStatusTimestamp;
	}

	/**
	 * @return the facultyUserId
	 */
	public UUID getFacultyUserId() {
		return facultyUserId;
	}

	/**
	 * @return the updatedTimestamp
	 */
	public Integer getUpdatedTimestamp() {
		return updatedTimestamp;
	}

	/**
	 * @return the result
	 */
	public String getResult() {
		return result;
	}

	/**
	 * @return the remarks
	 */
	public String getRemarks() {
		return remarks;
	}

	/**
	 * @return the homeworkAttachemnts
	 */
	public List<Document<HomeworkDocumentType>> getHomeworkSubmissionAttachments() {
		return homeworkSubmissionAttachments;
	}

	@Override
	public String toString() {
		return "HomeworkSubmissionDetails [homeworkSubmissionId=" + homeworkSubmissionId + ", homeworkDetails="
				+ homeworkDetails + ", studentLite=" + studentLite +", submittedBy="+ submittedBy +", description=" + description
				+ ", homeworkSubmissionStatus=" + homeworkSubmissionStatus + ", submissionTimestamp="
				+ submissionTimestamp + ", facultyStatusTimestamp=" + facultyStatusTimestamp + ", facultyUserId=" + facultyUserId
				+ ", updatedTimestamp=" + updatedTimestamp + ", result=" + result + ", remarks=" + remarks
				+ ", homeworkSubmissionAttachments=" + homeworkSubmissionAttachments + "]";
	}
}
