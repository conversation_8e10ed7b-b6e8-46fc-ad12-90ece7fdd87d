package com.embrate.cloud.core.api.service.payment.gateway.cashfree;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CashFreePaymentWebhookPaymentMethod {

    @JsonProperty("card")
    private CashFreePaymentWebhookCardMethod card;

    @JsonProperty("netbanking")
    private CashFreePaymentWebhookNetBankingMethod netBanking;

    @JsonProperty("upi")
    private CashFreePaymentWebhookUPIMethod upi;

    @JsonProperty("app")
    private CashFreePaymentWebhookWalletMethod wallet;

    public CashFreePaymentWebhookCardMethod getCard() {
        return card;
    }

    public void setCard(CashFreePaymentWebhookCardMethod card) {
        this.card = card;
    }

    public CashFreePaymentWebhookNetBankingMethod getNetBanking() {
        return netBanking;
    }

    public void setNetBanking(CashFreePaymentWebhookNetBankingMethod netBanking) {
        this.netBanking = netBanking;
    }

    public CashFreePaymentWebhookUPIMethod getUpi() {
        return upi;
    }

    public void setUpi(CashFreePaymentWebhookUPIMethod upi) {
        this.upi = upi;
    }

    public CashFreePaymentWebhookWalletMethod getWallet() {
        return wallet;
    }

    public void setWallet(CashFreePaymentWebhookWalletMethod wallet) {
        this.wallet = wallet;
    }

    @Override
    public String toString() {
        return "CashFreePaymentWebhookPaymentMethods{" +
                "card=" + card +
                ", netBanking=" + netBanking +
                ", upi=" + upi +
                ", wallet=" + wallet +
                '}';
    }
}
