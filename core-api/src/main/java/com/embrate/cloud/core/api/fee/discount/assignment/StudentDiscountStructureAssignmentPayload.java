package com.embrate.cloud.core.api.fee.discount.assignment;

import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
 *
 * <AUTHOR>
 *
 */
public class StudentDiscountStructureAssignmentPayload {

	private int academicSessionId;

	private Map<String, Set<String>> discountStructureStudents;

	public StudentDiscountStructureAssignmentPayload() {
	}

	public int getAcademicSessionId() {
		return academicSessionId;
	}

	public void setAcademicSessionId(int academicSessionId) {
		this.academicSessionId = academicSessionId;
	}

	public Map<String, Set<String>> getDiscountStructureStudents() {
		return discountStructureStudents;
	}

	public void setDiscountStructureStudents(Map<String, Set<String>> discountStructureStudents) {
		this.discountStructureStudents = discountStructureStudents;
	}

	@Override
	public String toString() {
		return "StudentDiscountStructureAssignmentPayload{" +
				"academicSessionId=" + academicSessionId +
				", discountStructureStudents=" + discountStructureStudents +
				'}';
	}
}
