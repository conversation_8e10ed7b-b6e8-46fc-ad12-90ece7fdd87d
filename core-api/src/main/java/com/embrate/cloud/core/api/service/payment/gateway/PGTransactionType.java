package com.embrate.cloud.core.api.service.payment.gateway;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */

public enum PGTransactionType {
    STUDENT_FEE_PAYMENT, WALLET_RECHARGE, STUDENT_REGISTRATION;

    public static PGTransactionType getTransactionType(String transactionTypeStr) {
        if (StringUtils.isBlank(transactionTypeStr)) {
            return null;
        }
        for (PGTransactionType transactionType : PGTransactionType
                .values()) {
            if (transactionType.name()
                    .equalsIgnoreCase(transactionTypeStr)) {
                return transactionType;
            }
        }
        return null;
    }
}
