package com.embrate.cloud.core.api.service.payment.gateway.jodo;

import com.embrate.cloud.core.api.service.payment.gateway.atom.AtomMerchantDetails;
import org.apache.commons.collections.CollectionUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @created_at 28/10/23 : 14:12
 **/
public class JodoMerchantDetails {
    private final String apiBaseUrl;
    private final String apiAuthUrl;
    private final String authorizationHeaderToken;
    private final String apiKey;
    private final String apiSecret;

    public JodoMerchantDetails(String apiBaseUrl, String apiAuthUrl, String authorizationHeaderToken, String apiKey, String apiSecret) {
        this.apiBaseUrl = apiBaseUrl;
        this.apiAuthUrl = apiAuthUrl;
        this.authorizationHeaderToken = authorizationHeaderToken;
        this.apiKey = apiKey;
        this.apiSecret = apiSecret;
    }

    public String getApiBaseUrl() {
        return apiBaseUrl;
    }

    public String getApiAuthUrl() {
        return apiAuthUrl;
    }

    public String getAuthorizationHeaderToken() {
        return authorizationHeaderToken;
    }

    public String getApiKey() {
        return apiKey;
    }

    public String getApiSecret() {
        return apiSecret;
    }

    @Override
    public String toString() {
        return "JodoMerchantDetails{" +
                "apiBaseUrl='" + apiBaseUrl + '\'' +
                ", apiAuthUrl='" + apiAuthUrl + '\'' +
                ", authorizationHeaderToken='" + authorizationHeaderToken + '\'' +
                ", apiKey='" + apiKey + '\'' +
                ", apiSecret='" + apiSecret + '\'' +
                '}';
    }

    public static JodoMerchantDetails getJodoMerchantDetails(Map<String, Object> metaData) {
        if(metaData == null || CollectionUtils.isEmpty(metaData.entrySet())) {
            return null;
        }

        String apiBaseUrl = getValue(metaData,"apiBaseUrl");
        String apiAuthUrl = getValue(metaData,"apiAuthUrl");
        String authorizationHeaderToken = getValue(metaData,"authorizationHeaderToken");
        String apiKey = getValue(metaData,"apiKey");
        String apiSecret = getValue(metaData,"apiSecret");

        return new JodoMerchantDetails(apiBaseUrl, apiAuthUrl, authorizationHeaderToken, apiKey, apiSecret);
    }

    private static String getValue(Map<String, Object> metaData, String key) {
        Object value = metaData.get(key);
        return value == null ? null : value.toString();
    }
}
