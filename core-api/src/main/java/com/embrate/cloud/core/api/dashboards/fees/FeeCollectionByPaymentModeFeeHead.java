package com.embrate.cloud.core.api.dashboards.fees;

import com.lernen.cloud.core.api.common.TransactionMode;

/**
 * <AUTHOR>
 */
public class FeeCollectionByPaymentModeFeeHead {

    private final int instituteId;
    private final TransactionMode mode;
    private final String feeHead;
    private final double totalAmount;

    public FeeCollectionByPaymentModeFeeHead(int instituteId, TransactionMode mode, String feeHead, double totalAmount) {
        this.instituteId = instituteId;
        this.mode = mode;
        this.feeHead = feeHead;
        this.totalAmount = totalAmount;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public TransactionMode getMode() {
        return mode;
    }

    public String getFeeHead() {
        return feeHead;
    }

    public double getTotalAmount() {
        return totalAmount;
    }

    @Override
    public String toString() {
        return "FeeCollectionByPaymentModeFeeHead{" +
                "instituteId=" + instituteId +
                ", mode=" + mode +
                ", feeHead='" + feeHead + '\'' +
                ", totalAmount=" + totalAmount +
                '}';
    }
}