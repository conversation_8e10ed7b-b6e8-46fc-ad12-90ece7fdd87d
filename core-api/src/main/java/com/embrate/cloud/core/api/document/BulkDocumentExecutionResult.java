package com.embrate.cloud.core.api.document;

import java.util.List;

/**
 * <AUTHOR>
 */

public class BulkDocumentExecutionResult {

    private final int total;

    private final int success;

    private final int failure;

    private final List<String> failureReasonList;

    public BulkDocumentExecutionResult(int total, int success, int failure, List<String> failureReasonList) {
        this.total = total;
        this.success = success;
        this.failure = failure;
        this.failureReasonList = failureReasonList;
    }

    public int getTotal() {
        return total;
    }

    public int getSuccess() {
        return success;
    }

    public int getFailure() {
        return failure;
    }

    public List<String> getFailureReasonList() {
        return failureReasonList;
    }

    @Override
    public String toString() {
        return "BulkDocumentExecutionResult{" +
                "total=" + total +
                ", success=" + success +
                ", failure=" + failure +
                ", failureReasonList=" + failureReasonList +
                '}';
    }
}
