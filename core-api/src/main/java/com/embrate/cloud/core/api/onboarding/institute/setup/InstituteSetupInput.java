package com.embrate.cloud.core.api.onboarding.institute.setup;

import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.user.Module;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */

public class InstituteSetupInput {

    private InstitutePayload institute;

    private List<InstituteSessionPayload> instituteSessionPayloadList;

    private ConfigInput configs;

    private List<InstituteStandardPayload> standards;

    private List<Module> authorizedModules;

    public InstitutePayload getInstitute() {
        return institute;
    }

    public void setInstitute(InstitutePayload institute) {
        this.institute = institute;
    }

    public List<InstituteSessionPayload> getInstituteSessionPayloadList() {
        return instituteSessionPayloadList;
    }

    public void setInstituteSessionPayloadList(List<InstituteSessionPayload> instituteSessionPayloadList) {
        this.instituteSessionPayloadList = instituteSessionPayloadList;
    }

    public ConfigInput getConfigs() {
        return configs;
    }

    public void setConfigs(ConfigInput configs) {
        this.configs = configs;
    }

    public List<InstituteStandardPayload> getStandards() {
        return standards;
    }

    public void setStandards(List<InstituteStandardPayload> standards) {
        this.standards = standards;
    }

    public List<Module> getAuthorizedModules() {
        return authorizedModules;
    }

    public void setAuthorizedModules(List<Module> authorizedModules) {
        this.authorizedModules = authorizedModules;
    }

    @Override
    public String toString() {
        return "InstituteSetupInput{" +
                "institute=" + institute +
                ", instituteSessionPayloadList=" + instituteSessionPayloadList +
                ", configs=" + configs +
                ", standards=" + standards +
                ", authorizedModules=" + authorizedModules +
                '}';
    }
}
