package com.embrate.cloud.core.api.hpc.utils;

import com.embrate.cloud.core.api.hpc.layout.HPCForm;
import com.lernen.cloud.core.api.examination.report.ExamReportData;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.student.Student;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 */
public class HPCFormWithStudent implements Comparable<HPCFormWithStudent>{

	private final Student student;

	private final HPCForm form;

	public HPCFormWithStudent(Student student, HPCForm form) {
		this.student = student;
		this.form = form;
	}

	public Student getStudent() {
		return student;
	}

	public HPCForm getForm() {
		return form;
	}

	@Override
	public int compareTo(HPCFormWithStudent hpc) {

		if(hpc == null) {
			return -1;
		}

		List<StandardSections> standardSections1 = this.getStudent().getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList();
		StandardSections section1 = null;
		if(!CollectionUtils.isEmpty(standardSections1)) {
			section1 = standardSections1.get(0);
		}

		List<StandardSections> standardSections2 = hpc.getStudent().getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList();
		StandardSections section2 = null;
		if(!CollectionUtils.isEmpty(standardSections2)) {
			section2 = standardSections2.get(0);
		}

		if (section1 != null && section2 != null) {
			int sectionCompare = section1.getSectionName().compareToIgnoreCase(section2.getSectionName());
			if (sectionCompare != 0) {
				return sectionCompare;
			}
		}

		return this.getStudent().getStudentBasicInfo().getName().compareToIgnoreCase(hpc.getStudent().getStudentBasicInfo().getName());
	}

	@Override
	public String toString() {
		return "HPCFormWithStudent{" +
				"student=" + student +
				", form=" + form +
				'}';
	}
}
