/**
 * 
 */
package com.embrate.cloud.core.api.lecture;

import java.util.Set;
import java.util.UUID;

/**
 * <AUTHOR>
 *
 */
public class LectureDetailsPayload {
	
	private Integer instituteId;
	
	private UUID lectureId;
	
	private UUID standardId;

	private Set<Integer> sectionIdList;
	
	private UUID courseId;
	
	private String chapter;
	
	private String title;
	
	private String videoLink;
	
	private UUID lecturerUserId;
	
	private String lecturerName;
	
	private UUID createdUserId;
	
	private Integer createdTimestamp;
	
	private UUID updatedUserId;
	
	private Integer updatedTimestamp;

	private String description;
	
	private Integer recommendedViewDate;

	/**
	 * @param instituteId
	 * @param lectureId
	 * @param standardId
	 * @param courseId
	 * @param chapter
	 * @param title
	 * @param videoLink
	 * @param lecturerUserId
	 * @param lecturerName
	 * @param createdUserId
	 * @param createdTimestamp
	 * @param updatedUserId
	 * @param updatedTimestamp
	 * @param description
	 * @param recommendedViewDate
	 */
	public LectureDetailsPayload(Integer instituteId, UUID lectureId, UUID standardId, Set<Integer> sectionIdList, UUID courseId, String chapter,
			String title, String videoLink, UUID lecturerUserId, String lecturerName, UUID createdUserId,
			Integer createdTimestamp, UUID updatedUserId, Integer updatedTimestamp, String description,
			Integer recommendedViewDate) {
		this.instituteId = instituteId;
		this.lectureId = lectureId;
		this.standardId = standardId;
		this.sectionIdList = sectionIdList;
		this.courseId = courseId;
		this.chapter = chapter;
		this.title = title;
		this.videoLink = videoLink;
		this.lecturerUserId = lecturerUserId;
		this.lecturerName = lecturerName;
		this.createdUserId = createdUserId;
		this.createdTimestamp = createdTimestamp;
		this.updatedUserId = updatedUserId;
		this.updatedTimestamp = updatedTimestamp;
		this.description = description;
		this.recommendedViewDate = recommendedViewDate;
	}

	/**
	 * 
	 */
	public LectureDetailsPayload() {
	}

	/**
	 * @return the instituteId
	 */
	public Integer getInstituteId() {
		return instituteId;
	}

	/**
	 * @param instituteId the instituteId to set
	 */
	public void setInstituteId(Integer instituteId) {
		this.instituteId = instituteId;
	}

	/**
	 * @return the lectureId
	 */
	public UUID getLectureId() {
		return lectureId;
	}

	/**
	 * @param lectureId the lectureId to set
	 */
	public void setLectureId(UUID lectureId) {
		this.lectureId = lectureId;
	}

	/**
	 * @return the standardId
	 */
	public UUID getStandardId() {
		return standardId;
	}

	/**
	 * @param standardId the standardId to set
	 */
	public void setStandardId(UUID standardId) {
		this.standardId = standardId;
	}

	/**
	 * @return the courseId
	 */
	public UUID getCourseId() {
		return courseId;
	}

	/**
	 * @param courseId the courseId to set
	 */
	public void setCourseId(UUID courseId) {
		this.courseId = courseId;
	}

	/**
	 * @return the chapter
	 */
	public String getChapter() {
		return chapter;
	}

	/**
	 * @param chapter the chapter to set
	 */
	public void setChapter(String chapter) {
		this.chapter = chapter;
	}

	/**
	 * @return the title
	 */
	public String getTitle() {
		return title;
	}

	/**
	 * @param title the title to set
	 */
	public void setTitle(String title) {
		this.title = title;
	}

	/**
	 * @return the videoLink
	 */
	public String getVideoLink() {
		return videoLink;
	}

	/**
	 * @param videoLink the videoLink to set
	 */
	public void setVideoLink(String videoLink) {
		this.videoLink = videoLink;
	}

	/**
	 * @return the lecturerUserId
	 */
	public UUID getLecturerUserId() {
		return lecturerUserId;
	}

	/**
	 * @param lecturerUserId the lecturerUserId to set
	 */
	public void setLecturerUserId(UUID lecturerUserId) {
		this.lecturerUserId = lecturerUserId;
	}

	/**
	 * @return the lecturerName
	 */
	public String getLecturerName() {
		return lecturerName;
	}

	/**
	 * @param lecturerName the lecturerName to set
	 */
	public void setLecturerName(String lecturerName) {
		this.lecturerName = lecturerName;
	}

	/**
	 * @return the createdUserId
	 */
	public UUID getCreatedUserId() {
		return createdUserId;
	}

	/**
	 * @param createdUserId the createdUserId to set
	 */
	public void setCreatedUserId(UUID createdUserId) {
		this.createdUserId = createdUserId;
	}

	/**
	 * @return the createdTimestamp
	 */
	public Integer getCreatedTimestamp() {
		return createdTimestamp;
	}

	/**
	 * @param createdTimestamp the createdTimestamp to set
	 */
	public void setCreatedTimestamp(Integer createdTimestamp) {
		this.createdTimestamp = createdTimestamp;
	}

	/**
	 * @return the updatedUserId
	 */
	public UUID getUpdatedUserId() {
		return updatedUserId;
	}

	/**
	 * @param updatedUserId the updatedUserId to set
	 */
	public void setUpdatedUserId(UUID updatedUserId) {
		this.updatedUserId = updatedUserId;
	}

	/**
	 * @return the updatedTimestamp
	 */
	public Integer getUpdatedTimestamp() {
		return updatedTimestamp;
	}

	/**
	 * @param updatedTimestamp the updatedTimestamp to set
	 */
	public void setUpdatedTimestamp(Integer updatedTimestamp) {
		this.updatedTimestamp = updatedTimestamp;
	}

	/**
	 * @return the description
	 */
	public String getDescription() {
		return description;
	}

	/**
	 * @param description the description to set
	 */
	public void setDescription(String description) {
		this.description = description;
	}

	/**
	 * @return the recommendedViewDate
	 */
	public Integer getRecommendedViewDate() {
		return recommendedViewDate;
	}

	/**
	 * @param recommendedViewDate the recommendedViewDate to set
	 */
	public void setRecommendedViewDate(Integer recommendedViewDate) {
		this.recommendedViewDate = recommendedViewDate;
	}

	public Set<Integer> getSectionIdList() {
		return sectionIdList;
	}

	@Override
	public String toString() {
		return "LectureDetailsPayload{" +
				"instituteId=" + instituteId +
				", lectureId=" + lectureId +
				", standardId=" + standardId +
				", sectionIdList=" + sectionIdList +
				", courseId=" + courseId +
				", chapter='" + chapter + '\'' +
				", title='" + title + '\'' +
				", videoLink='" + videoLink + '\'' +
				", lecturerUserId=" + lecturerUserId +
				", lecturerName='" + lecturerName + '\'' +
				", createdUserId=" + createdUserId +
				", createdTimestamp=" + createdTimestamp +
				", updatedUserId=" + updatedUserId +
				", updatedTimestamp=" + updatedTimestamp +
				", description='" + description + '\'' +
				", recommendedViewDate=" + recommendedViewDate +
				'}';
	}
}
