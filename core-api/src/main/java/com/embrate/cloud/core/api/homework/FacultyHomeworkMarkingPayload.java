package com.embrate.cloud.core.api.homework;

import java.util.UUID;

public class FacultyHomeworkMarkingPayload {

    private UUID homeworkSubmissionId;

    private UUID homeworkId;

    private UUID studentId;

    private HomeworkSubmissionStatus homeworkSubmissionStatus;

    private UUID facultyId;

    private String remarks;

    private String result;

    public FacultyHomeworkMarkingPayload() {
    }

    public FacultyHomeworkMarkingPayload(UUID homeworkSubmissionId, UUID homeworkId, UUID studentId, HomeworkSubmissionStatus homeworkSubmissionStatus, UUID facultyId, String remarks, String result) {
        this.homeworkSubmissionId = homeworkSubmissionId;
        this.homeworkId = homeworkId;
        this.studentId = studentId;
        this.homeworkSubmissionStatus = homeworkSubmissionStatus;
        this.facultyId = facultyId;
        this.remarks = remarks;
        this.result = result;
    }

    public UUID getHomeworkSubmissionId() {
        return homeworkSubmissionId;
    }

    public UUID getHomeworkId() {
        return homeworkId;
    }

    public UUID getStudentId() {
        return studentId;
    }

    public HomeworkSubmissionStatus getHomeworkSubmissionStatus() {
        return homeworkSubmissionStatus;
    }

    public UUID getFacultyId() {
        return facultyId;
    }

    public String getRemarks() {
        return remarks;
    }

    public String getResult() {
        return result;
    }

    public void setHomeworkSubmissionId(UUID homeworkSubmissionId) {
        this.homeworkSubmissionId = homeworkSubmissionId;
    }

    public void setHomeworkId(UUID homeworkId) {
        this.homeworkId = homeworkId;
    }

    public void setStudentId(UUID studentId) {
        this.studentId = studentId;
    }

    public void setHomeworkSubmissionStatus(HomeworkSubmissionStatus homeworkSubmissionStatus) {
        this.homeworkSubmissionStatus = homeworkSubmissionStatus;
    }

    public void setFacultyId(UUID facultyId) {
        this.facultyId = facultyId;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public void setResult(String result) {
        this.result = result;
    }

    @Override
    public String toString() {
        return "FacultyHomeworkMarkingPayload{" +
                "homeworkSubmissionId=" + homeworkSubmissionId +
                ", homeworkId=" + homeworkId +
                ", studentId=" + studentId +
                ", homeworkSubmissionStatus=" + homeworkSubmissionStatus +
                ", facultyId=" + facultyId +
                ", remarks='" + remarks + '\'' +
                ", result='" + result + '\'' +
                '}';
    }
}
