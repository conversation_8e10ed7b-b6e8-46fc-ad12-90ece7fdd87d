/**
 * 
 */
package com.embrate.cloud.core.api.homework;

import java.util.List;

import com.lernen.cloud.core.api.institute.Standard;

/**
 * <AUTHOR>
 *
 */
public class StandardHomeworkDetails {
	
	private final int instituteId;
	
	private final Standard standard;
	
	private final List<StandardCoursesHomeworkDetails> standardCoursesHomeworkDetailsList;

	/**
	 * @param instituteId
	 * @param standard
	 * @param standardCoursesHomeworkDetailsList
	 */
	public StandardHomeworkDetails(int instituteId, Standard standard,
			List<StandardCoursesHomeworkDetails> standardCoursesHomeworkDetailsList) {
		this.instituteId = instituteId;
		this.standard = standard;
		this.standardCoursesHomeworkDetailsList = standardCoursesHomeworkDetailsList;
	}

	/**
	 * @return the instituteId
	 */
	public int getInstituteId() {
		return instituteId;
	}

	/**
	 * @return the standard
	 */
	public Standard getStandard() {
		return standard;
	}

	/**
	 * @return the standardCoursesHomeworkDetailsList
	 */
	public List<StandardCoursesHomeworkDetails> getStandardCoursesHomeworkDetailsList() {
		return standardCoursesHomeworkDetailsList;
	}

	@Override
	public String toString() {
		return "StandardHomeworkDetails [instituteId=" + instituteId + ", standard=" + standard
				+ ", standardCoursesHomeworkDetailsList=" + standardCoursesHomeworkDetailsList + "]";
	}
}
