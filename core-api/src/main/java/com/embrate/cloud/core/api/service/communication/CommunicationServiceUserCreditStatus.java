package com.embrate.cloud.core.api.service.communication;

/**
 * <AUTHOR>
 */
public class CommunicationServiceUserCreditStatus {

    private final int totalContacts;
    private final int failedContacts;
    private final int totalCredits;
    private final int failedCredits;

    public CommunicationServiceUserCreditStatus(int totalContacts, int failedContacts, int totalCredits, int failedCredits) {
        this.totalContacts = totalContacts;
        this.failedContacts = failedContacts;
        this.totalCredits = totalCredits;
        this.failedCredits = failedCredits;
    }

    public int getTotalContacts() {
        return totalContacts;
    }

    public int getFailedContacts() {
        return failedContacts;
    }

    public int getTotalCredits() {
        return totalCredits;
    }

    public int getFailedCredits() {
        return failedCredits;
    }

    @Override
    public String toString() {
        return "CommunicationServiceUserCreditStatus{" +
                "totalContacts=" + totalContacts +
                ", failedContacts=" + failedContacts +
                ", totalCredits=" + totalCredits +
                ", failedCredits=" + failedCredits +
                '}';
    }
}
