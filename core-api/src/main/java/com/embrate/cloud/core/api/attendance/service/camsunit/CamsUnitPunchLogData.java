package com.embrate.cloud.core.api.attendance.service.camsunit;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CamsUnitPunchLogData {

    @JsonProperty("Type")
    private String type;

    @JsonProperty("Temperature")
    private String temperature;

    @JsonProperty("FaceMask")
    private Boolean faceMask;

    @JsonProperty("InputType")
    private String inputType;

    @JsonProperty("UserId")
    private String userId;

    @JsonProperty("LogTime")
    private String logTime;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTemperature() {
        return temperature;
    }

    public void setTemperature(String temperature) {
        this.temperature = temperature;
    }

    public Boolean getFaceMask() {
        return faceMask;
    }

    public void setFaceMask(Boolean faceMask) {
        this.faceMask = faceMask;
    }

    public String getInputType() {
        return inputType;
    }

    public void setInputType(String inputType) {
        this.inputType = inputType;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getLogTime() {
        return logTime;
    }

    public void setLogTime(String logTime) {
        this.logTime = logTime;
    }

    @Override
    public String toString() {
        return "CamsUnitPunchLogData{" +
                "type='" + type + '\'' +
                ", temperature='" + temperature + '\'' +
                ", faceMask=" + faceMask +
                ", inputType='" + inputType + '\'' +
                ", userId='" + userId + '\'' +
                ", logTime='" + logTime + '\'' +
                '}';
    }
}
