package com.embrate.cloud.core.api.service.payment.gateway.cashfree.v2;

import com.embrate.cloud.core.api.service.payment.gateway.cashfree.CashFreePaymentWebhookCustomerDetails;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CashFreeTokenResponseV2 {

    @JsonProperty("cf_order_id")
    private String cfOrderId;

    @JsonProperty("created_at")
    private String createdAt;

    @JsonProperty("customer_details")
    private CashFreePaymentWebhookCustomerDetails customerDetails;

    @JsonProperty("entity")
    private String entity;

    @JsonProperty("order_id")
    private String orderId;

    @JsonProperty("order_amount")
    private String orderAmount;

    @JsonProperty("order_currency")
    private String orderCurrency;

    @JsonProperty("order_expiry_time")
    private String orderExpiryTime;

    @JsonProperty("order_meta")
    private CashFreeOrderMetadata orderMetadata;

    @JsonProperty("payment_session_id")
    private String paymentSessionId;

    @JsonProperty("order_status")
    private String orderStatus;

    @JsonProperty("order_note")
    private String orderNote;

    @JsonProperty("payments")
    private CashFreePaymentUrl payments;

    @JsonProperty("refunds")
    private CashFreePaymentUrl refunds;

    @JsonProperty("settlements")
    private CashFreePaymentUrl settlements;


    public String getCfOrderId() {
        return cfOrderId;
    }

    public void setCfOrderId(String cfOrderId) {
        this.cfOrderId = cfOrderId;
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(String orderAmount) {
        this.orderAmount = orderAmount;
    }

    public String getOrderCurrency() {
        return orderCurrency;
    }

    public void setOrderCurrency(String orderCurrency) {
        this.orderCurrency = orderCurrency;
    }

    public String getOrderExpiryTime() {
        return orderExpiryTime;
    }

    public void setOrderExpiryTime(String orderExpiryTime) {
        this.orderExpiryTime = orderExpiryTime;
    }

    public String getPaymentSessionId() {
        return paymentSessionId;
    }

    public void setPaymentSessionId(String paymentSessionId) {
        this.paymentSessionId = paymentSessionId;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getOrderNote() {
        return orderNote;
    }

    public void setOrderNote(String orderNote) {
        this.orderNote = orderNote;
    }

    public CashFreePaymentWebhookCustomerDetails getCustomerDetails() {
        return customerDetails;
    }

    public void setCustomerDetails(CashFreePaymentWebhookCustomerDetails customerDetails) {
        this.customerDetails = customerDetails;
    }

    public CashFreeOrderMetadata getOrderMetadata() {
        return orderMetadata;
    }

    public void setOrderMetadata(CashFreeOrderMetadata orderMetadata) {
        this.orderMetadata = orderMetadata;
    }


    public String getEntity() {
        return entity;
    }

    public void setEntity(String entity) {
        this.entity = entity;
    }

    public CashFreePaymentUrl getPayments() {
        return payments;
    }

    public void setPayments(CashFreePaymentUrl payments) {
        this.payments = payments;
    }

    public CashFreePaymentUrl getRefunds() {
        return refunds;
    }

    public void setRefunds(CashFreePaymentUrl refunds) {
        this.refunds = refunds;
    }

    public CashFreePaymentUrl getSettlements() {
        return settlements;
    }

    public void setSettlements(CashFreePaymentUrl settlements) {
        this.settlements = settlements;
    }

    @Override
    public String toString() {
        return "CashFreeTokenResponseV2{" +
                "cfOrderId='" + cfOrderId + '\'' +
                ", createdAt='" + createdAt + '\'' +
                ", customerDetails=" + customerDetails +
                ", entity='" + entity + '\'' +
                ", orderId='" + orderId + '\'' +
                ", orderAmount='" + orderAmount + '\'' +
                ", orderCurrency='" + orderCurrency + '\'' +
                ", orderExpiryTime='" + orderExpiryTime + '\'' +
                ", orderMetadata=" + orderMetadata +
                ", paymentSessionId='" + paymentSessionId + '\'' +
                ", orderStatus='" + orderStatus + '\'' +
                ", orderNote='" + orderNote + '\'' +
                ", payments=" + payments +
                ", refunds=" + refunds +
                ", settlements=" + settlements +
                '}';
    }
}
