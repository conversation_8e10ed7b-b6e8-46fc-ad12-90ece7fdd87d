package com.embrate.cloud.core.api.leave.management.policy;

import java.util.List;

/**
 * <AUTHOR>
 */

public class LeavePolicyTemplate {

    private final LeavePolicyMetadata leavePolicyMetadata;

    private final List<LeaveTypePolicyDetails> leaveTypePolicyDetailsList;

    public LeavePolicyTemplate(LeavePolicyMetadata leavePolicyMetadata, List<LeaveTypePolicyDetails> leaveTypePolicyDetailsList) {
        this.leavePolicyMetadata = leavePolicyMetadata;
        this.leaveTypePolicyDetailsList = leaveTypePolicyDetailsList;
    }

    public LeavePolicyMetadata getLeavePolicyMetadata() {
        return leavePolicyMetadata;
    }

    public List<LeaveTypePolicyDetails> getLeaveTypePolicyDetailsList() {
        return leaveTypePolicyDetailsList;
    }

    @Override
    public String toString() {
        return "LeavePolicyTemplate{" +
                "leavePolicyMetadata=" + leavePolicyMetadata +
                ", leaveTypePolicyDetailsList=" + leaveTypePolicyDetailsList +
                '}';
    }
}
