package com.embrate.cloud.core.api.service.payment.gateway.razorpay;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 * @created_at 12/09/24 : 16:16
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class RazorpayPaymentWebhookResponseUPIPayload {

    @JsonProperty("payer_account_type")
    private String payer_account_type;

    @JsonProperty("vpa")
    private String vpa;

    @JsonProperty("flow")
    private String flow;

    public RazorpayPaymentWebhookResponseUPIPayload() {
    }

    public RazorpayPaymentWebhookResponseUPIPayload(String payer_account_type, String vpa, String flow) {
        this.payer_account_type = payer_account_type;
        this.vpa = vpa;
        this.flow = flow;
    }

    public String getPayer_account_type() {
        return payer_account_type;
    }

    public void setPayer_account_type(String payer_account_type) {
        this.payer_account_type = payer_account_type;
    }

    public String getVpa() {
        return vpa;
    }

    public void setVpa(String vpa) {
        this.vpa = vpa;
    }

    public String getFlow() {
        return flow;
    }

    public void setFlow(String flow) {
        this.flow = flow;
    }

    @Override
    public String toString() {
        return "RazorpayPaymentWebhookResponseUPIPayload{" +
                "payerAccountType='" + payer_account_type + '\'' +
                ", vpa='" + vpa + '\'' +
                ", flow='" + flow + '\'' +
                '}';
    }
}

