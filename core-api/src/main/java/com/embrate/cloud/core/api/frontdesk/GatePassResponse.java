package com.embrate.cloud.core.api.frontdesk;

import java.util.UUID;

public class GatePassResponse {

    private final UUID gatePassId;

    private final String gatePassNumber;

    public GatePassResponse(UUID gatePassId, String gatePassNumber) {
        this.gatePassId = gatePassId;
        this.gatePassNumber = gatePassNumber;
    }

    public UUID getGatePassId() {
        return gatePassId;
    }

    public String getGatePassNumber() {
        return gatePassNumber;
    }

    @Override
    public String toString() {
        return "GatePassReponse{" +
                "gatePassId=" + gatePassId +
                ", gatePassNumber='" + gatePassNumber + '\'' +
                '}';
    }
}
