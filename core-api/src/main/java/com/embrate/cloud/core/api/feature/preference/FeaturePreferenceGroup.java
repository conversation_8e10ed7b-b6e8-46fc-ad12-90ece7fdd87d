package com.embrate.cloud.core.api.feature.preference;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */

public class FeaturePreferenceGroup {

    private final String id;

    private final String name;

    private final String description;

    private final List<FeaturePreferenceEntity> featurePreferenceEntities;


    public FeaturePreferenceGroup(String id, String name, String description, List<FeaturePreferenceEntity> featurePreferenceEntities) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.featurePreferenceEntities = featurePreferenceEntities;
    }

    public String getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public List<FeaturePreferenceEntity> getFeaturePreferenceEntities() {
        return featurePreferenceEntities;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof FeaturePreferenceGroup)) return false;

        FeaturePreferenceGroup that = (FeaturePreferenceGroup) o;

        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }

    @Override
    public String toString() {
        return "FeaturePreferenceGroup{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", featurePreferenceEntities=" + featurePreferenceEntities +
                '}';
    }
}
