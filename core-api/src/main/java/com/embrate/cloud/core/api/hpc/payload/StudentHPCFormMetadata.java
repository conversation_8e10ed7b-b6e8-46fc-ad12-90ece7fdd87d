package com.embrate.cloud.core.api.hpc.payload;

import com.embrate.cloud.core.api.hpc.utils.HPCExamType;
import com.embrate.cloud.core.api.hpc.web.HPCFilledStatus;

public class StudentHPCFormMetadata implements Comparable<StudentHPCFormMetadata> {

    private final String name;
    private final HPCExamType hpcExamType;
    private final HPCFilledStatus teacherSectionStatus;
    private final HPCFilledStatus parentSectionStatus;

    public StudentHPCFormMetadata(String name, HPCExamType hpcExamType, HPCFilledStatus teacherSectionStatus, HPCFilledStatus parentSectionStatus) {
        this.name = name;
        this.hpcExamType = hpcExamType;
        this.teacherSectionStatus = teacherSectionStatus;
        this.parentSectionStatus = parentSectionStatus;
    }

    public String getName() {
        return name;
    }

    public HPCExamType getHpcExamType() {
        return hpcExamType;
    }

    public HPCFilledStatus getTeacherSectionStatus() {
        return teacherSectionStatus;
    }

    public HPCFilledStatus getParentSectionStatus() {
        return parentSectionStatus;
    }

    @Override
    public String toString() {
        return "StudentHPCFormMetadata{" +
                "name='" + name + '\'' +
                ", teacherSectionStatus=" + teacherSectionStatus +
                ", parentSectionStatus=" + parentSectionStatus +
                '}';
    }

    @Override
    public int compareTo(StudentHPCFormMetadata o) {
        return this.name.compareToIgnoreCase(o.name);
    }
}
