/**
 * 
 */
package com.embrate.cloud.core.api.lecture;

import java.util.UUID;

/**
 * <AUTHOR>
 *
 */
public class StudentLectureViewDetails {

	private UUID studentId;
	
	private UUID lectureId;
	
	private Integer viewedOn;

	/**
	 * @param studentId
	 * @param lectureId
	 * @param viewedOn
	 */
	public StudentLectureViewDetails(UUID studentId, UUID lectureId, Integer viewedOn) {
		this.studentId = studentId;
		this.lectureId = lectureId;
		this.viewedOn = viewedOn;
	}

	/**
	 * 
	 */
	public StudentLectureViewDetails() {
	}

	/**
	 * @return the studentId
	 */
	public UUID getStudentId() {
		return studentId;
	}

	/**
	 * @param studentId the studentId to set
	 */
	public void setStudentId(UUID studentId) {
		this.studentId = studentId;
	}

	/**
	 * @return the lectureId
	 */
	public UUID getLectureId() {
		return lectureId;
	}

	/**
	 * @param lectureId the lectureId to set
	 */
	public void setLectureId(UUID lectureId) {
		this.lectureId = lectureId;
	}

	/**
	 * @return the viewedOn
	 */
	public Integer getViewedOn() {
		return viewedOn;
	}

	/**
	 * @param viewedOn the viewedOn to set
	 */
	public void setViewedOn(Integer viewedOn) {
		this.viewedOn = viewedOn;
	}

	@Override
	public String toString() {
		return "StudentLectureViewDetails [studentId=" + studentId + ", lectureId=" + lectureId + ", viewedOn="
				+ viewedOn + "]";
	}
}
