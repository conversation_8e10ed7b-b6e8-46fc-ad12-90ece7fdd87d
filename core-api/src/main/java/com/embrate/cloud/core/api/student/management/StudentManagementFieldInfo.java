package com.embrate.cloud.core.api.student.management;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
public class StudentManagementFieldInfo {

	private final StudentManagementField field;

	private final StudentManagementDataType dataType;

	private final StudentManagementFieldType fieldType;

	private final List<ValidationType> validations;

	private final String displayName;

	private final LinkedHashMap<String, String> possibleValues;

	public StudentManagementFieldInfo(StudentManagementField field, StudentManagementDataType dataType, StudentManagementFieldType fieldType, List<ValidationType> validations, String displayName, LinkedHashMap<String, String> possibleValues) {
		this.field = field;
		this.dataType = dataType;
		this.fieldType = fieldType;
		this.validations = validations;
		this.displayName = displayName;
		this.possibleValues = possibleValues;
	}

	public StudentManagementField getField() {
		return field;
	}

	public StudentManagementDataType getDataType() {
		return dataType;
	}

	public StudentManagementFieldType getFieldType() {
		return fieldType;
	}

	public List<ValidationType> getValidations() {
		return validations;
	}

	public String getDisplayName() {
		return displayName;
	}

	public LinkedHashMap<String, String> getPossibleValues() {
		return possibleValues;
	}

	@Override
	public String toString() {
		return "StudentManagementFieldInfo{" +
				"field=" + field +
				", dataType=" + dataType +
				", fieldType=" + fieldType +
				", validations=" + validations +
				", displayName='" + displayName + '\'' +
				", possibleValues=" + possibleValues +
				'}';
	}
}
