package com.embrate.cloud.core.api.timetable;

import com.lernen.cloud.core.api.staff.Staff;

import java.util.List;
import java.util.UUID;

public class CoursesActivityDetails {

    private final Entity entity;

    private final UUID entityId;

    private final String entityIdVal;

    private final List<Staff> staffList;

    public CoursesActivityDetails(Entity entity, UUID entityId, String entityIdVal, List<Staff> staffList) {
        this.entity = entity;
        this.entityId = entityId;
        this.entityIdVal = entityIdVal;
        this.staffList = staffList;
    }

    public Entity getEntity() {
        return entity;
    }

    public UUID getEntityId() {
        return entityId;
    }

    public String getEntityIdVal() {
        return entityIdVal;
    }

    public List<Staff> getStaffList() {
        return staffList;
    }

    @Override
    public String toString() {
        return "CoursesActivityDetails{" +
                "entity=" + entity +
                ", entityId=" + entityId +
                ", entityIdVal='" + entityIdVal + '\'' +
                ", staffList=" + staffList +
                '}';
    }
}
