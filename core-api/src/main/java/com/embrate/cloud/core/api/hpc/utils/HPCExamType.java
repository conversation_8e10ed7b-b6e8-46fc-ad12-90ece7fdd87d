package com.embrate.cloud.core.api.hpc.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public enum HPCExamType {

	TERM1("Term 1", new ArrayList<>()), TERM2("Term 2", Arrays.asList(TERM1));

	private String displayName;

	private List<HPCExamType> otherExamTypes;

	HPCExamType(String displayName, List<HPCExamType> otherExamTypes) {
		this.displayName = displayName;
		this.otherExamTypes = otherExamTypes;
	}

	public String getDisplayName() {
		return displayName;
	}

	public List<HPCExamType> getOtherExamTypes() {
		return otherExamTypes;
	}

	public List<HPCExamType> getApplicableExamTypes() {
		List<HPCExamType> examTypes = new ArrayList<>();
		examTypes.add(this);
		examTypes.addAll(this.getOtherExamTypes());
		return examTypes;
	}

	public static HPCExamType getHPCExamType(String hpcExamType) {
		if (StringUtils.isBlank(hpcExamType)) {
			return null;
		}
		for (HPCExamType hpcExamTypeEnum : HPCExamType.values()) {
			if (hpcExamTypeEnum.name().equalsIgnoreCase(hpcExamType)) {
				return hpcExamTypeEnum;
			}
		}
		return null;
	}
}
