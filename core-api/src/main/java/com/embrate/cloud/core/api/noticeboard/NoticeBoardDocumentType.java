/**
 * 
 */
package com.embrate.cloud.core.api.noticeboard;

import com.lernen.cloud.core.api.common.DocumentType;

/**
 * <AUTHOR>
 *
 */
public enum NoticeBoardDocumentType implements DocumentType{
	
	NOTICE_BOARD_ATTACHMENTS("Notice Board Attachments", "Notice Board Attachments"),
	OTHER(null, "Other");

	private String documentName;
	private String displayName;
	private boolean isThumbnail;

	private NoticeBoardDocumentType(String documentName, String displayName, boolean isThumbnail) {
		this.documentName = documentName;
		this.displayName = displayName;
		this.isThumbnail = isThumbnail;
	}

	private NoticeBoardDocumentType(String documentName, String displayName) {
		this.documentName = documentName;
		this.displayName = displayName;
		this.isThumbnail = false;
	}
	
	@Override
	public String getDocumentName() {
		return documentName;
	}
	
	@Override
	public String getDisplayName() {
		return displayName;
	}

	@Override
	public boolean isThumbnail() { return isThumbnail; }

}
