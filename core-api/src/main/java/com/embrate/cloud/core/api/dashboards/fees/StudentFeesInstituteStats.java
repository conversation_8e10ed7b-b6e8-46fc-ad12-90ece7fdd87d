package com.embrate.cloud.core.api.dashboards.fees;

import com.embrate.cloud.core.api.dashboards.common.InstituteValue;

import java.util.List;

/**
 * <AUTHOR>
 */
public class StudentFeesInstituteStats {

	private final int instituteId;

	private final String instituteName;

	private final List<PaymentModeCollection> paymentModeCollections;

	private final List<FeeHeadCollection> feeHeadCollections;

	private final double totalAmount;


	public StudentFeesInstituteStats(int instituteId, String instituteName, List<PaymentModeCollection> paymentModeCollections, List<FeeHeadCollection> feeHeadCollections, double totalAmount) {
		this.instituteId = instituteId;
		this.instituteName = instituteName;
		this.paymentModeCollections = paymentModeCollections;
		this.feeHeadCollections = feeHeadCollections;
		this.totalAmount = totalAmount;
	}

	public int getInstituteId() {
		return instituteId;
	}

	public String getInstituteName() {
		return instituteName;
	}

	public List<PaymentModeCollection> getPaymentModeCollections() {
		return paymentModeCollections;
	}

	public List<FeeHeadCollection> getFeeHeadCollections() {
		return feeHeadCollections;
	}

	public double getTotalAmount() {
		return totalAmount;
	}

	@Override
	public String toString() {
		return "StudentFeesInstituteStats{" +
				"instituteId=" + instituteId +
				", instituteName='" + instituteName + '\'' +
				", paymentModeCollections=" + paymentModeCollections +
				", feeHeadCollections=" + feeHeadCollections +
				", totalAmount=" + totalAmount +
				'}';
	}
}
