package com.embrate.cloud.core.api.inventory.v2.product.group;

import com.lernen.cloud.core.api.inventory.ProductGroupBasicInfo;
import com.lernen.cloud.core.api.inventory.ProductGroupItem;

import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class InventoryProductGroupPayload {

	private InventoryProductGroupBasicInfo productGroupBasicInfo;

	private List<InventoryProductGroupItem> productGroupItems;

	// Dummy Constructor
	public InventoryProductGroupPayload() {
	}

	public InventoryProductGroupPayload(InventoryProductGroupBasicInfo productGroupBasicInfo, List<InventoryProductGroupItem> productGroupItems) {
		this.productGroupBasicInfo = productGroupBasicInfo;
		this.productGroupItems = productGroupItems;
	}

	public InventoryProductGroupBasicInfo getProductGroupBasicInfo() {
		return productGroupBasicInfo;
	}

	public void setProductGroupBasicInfo(InventoryProductGroupBasicInfo productGroupBasicInfo) {
		this.productGroupBasicInfo = productGroupBasicInfo;
	}

	public List<InventoryProductGroupItem> getProductGroupItems() {
		return productGroupItems;
	}

	public void setProductGroupItems(List<InventoryProductGroupItem> productGroupItems) {
		this.productGroupItems = productGroupItems;
	}

	@Override
	public String toString() {
		return "InventoryProductGroupPayload{" +
				"productGroupBasicInfo=" + productGroupBasicInfo +
				", productGroupItems=" + productGroupItems +
				'}';
	}
}
