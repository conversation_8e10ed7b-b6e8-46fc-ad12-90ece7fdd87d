/**
 * 
 */
package com.embrate.cloud.core.api.lecture;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class LectureViewingDetails {
	
	private LectureDetails lectureDetails;
	
	private List<StudentLectureViewCompleteDetails> studentLectureViewCompleteDetails;

	/**
	 * @param lectureDetails
	 * @param studentLectureViewCompleteDetails
	 */
	public LectureViewingDetails(LectureDetails lectureDetails,
			List<StudentLectureViewCompleteDetails> studentLectureViewCompleteDetails) {
		this.lectureDetails = lectureDetails;
		this.studentLectureViewCompleteDetails = studentLectureViewCompleteDetails;
	}

	/**
	 * 
	 */
	public LectureViewingDetails() {
	}

	/**
	 * @return the lectureDetails
	 */
	public LectureDetails getLectureDetails() {
		return lectureDetails;
	}

	/**
	 * @param lectureDetails the lectureDetails to set
	 */
	public void setLectureDetails(LectureDetails lectureDetails) {
		this.lectureDetails = lectureDetails;
	}

	/**
	 * @return the studentLectureViewCompleteDetails
	 */
	public List<StudentLectureViewCompleteDetails> getStudentLectureViewCompleteDetails() {
		return studentLectureViewCompleteDetails;
	}

	/**
	 * @param studentLectureViewCompleteDetails the studentLectureViewCompleteDetails to set
	 */
	public void setStudentLectureViewCompleteDetails(
			List<StudentLectureViewCompleteDetails> studentLectureViewCompleteDetails) {
		this.studentLectureViewCompleteDetails = studentLectureViewCompleteDetails;
	}

	@Override
	public String toString() {
		return "LectureViewingDetails [lectureDetails=" + lectureDetails + ", studentLectureViewCompleteDetails="
				+ studentLectureViewCompleteDetails + "]";
	}

}
