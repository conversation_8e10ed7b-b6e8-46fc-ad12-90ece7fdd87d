package com.embrate.cloud.core.api.service.communication.templates;

/**
 * <AUTHOR>
 */
public class TemplateVariable {

    private String varName;

    private TemplateVariableType templateVariableType;

    private String defaultValue;

    private String varValue;

    private String description;

    public String getVarName() {
        return varName;
    }

    public void setVarName(String varName) {
        this.varName = varName;
    }

    public TemplateVariableType getTemplateVariableType() {
        return templateVariableType;
    }

    public void setTemplateVariableType(TemplateVariableType templateVariableType) {
        this.templateVariableType = templateVariableType;
    }

    public String getTemplateVariableTypeDisplayName() {
        return templateVariableType.getDisplayName();
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public String getVarValue() {
        return varValue;
    }

    public void setVarValue(String varValue) {
        this.varValue = varValue;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return "TemplateVariable{" +
                "varName='" + varName + '\'' +
                ", templateVariableType=" + templateVariableType +
                ", defaultValue='" + defaultValue + '\'' +
                ", varValue='" + varValue + '\'' +
                ", description='" + description + '\'' +
                '}';
    }
}
