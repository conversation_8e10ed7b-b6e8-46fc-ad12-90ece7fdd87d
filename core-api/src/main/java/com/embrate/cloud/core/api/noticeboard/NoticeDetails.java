/**
 * 
 */
package com.embrate.cloud.core.api.noticeboard;

import java.util.List;
import java.util.Map;
import java.util.UUID;

import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.core.api.user.UserType;

/**
 * <AUTHOR>
 *
 */
public class NoticeDetails {

	private final int instituteId;

	private int academicSessionId;
	
	private final UUID noticeId;
	
	private final String title;
	
	private final String description;
	
	private final UUID createdBy;
	
	private final Integer createdTimestamp;
	
	private final UUID updatedBy;
	
	private final Integer updatedTimestamp;
	
	private final Integer expiryDate;
	
	private final Boolean pinned;
	
	private final UUID pinnedBy;
	
	private final Integer pinnedTimestamp;
	
	private final NoticeBoardStatus status;
	
	private final UUID broadcastedBy;
	
	private final Integer broadcastedTimestamp;
	
	private final Map<UserType, List<String>> entityNameAndIdsMapping;
	
	private int attachmentCount;
	
	private long attachmentSize;
	
	private String allowedMimeTypes;
	
	private final List<Document<NoticeBoardDocumentType>> noticeBoardAttachments;

	/**
	 * @param instituteId
	 * @param noticeId
	 * @param title
	 * @param body
	 * @param createdBy
	 * @param createdTimestamp
	 * @param updatedBy
	 * @param updatedTimestamp
	 * @param expiryDate
	 * @param pinned
	 * @param pinnedBy
	 * @param pinnedTimestamp
	 * @param status
	 * @param broadcastedBy
	 * @param broadcastedTimestamp
	 * @param attachmentCount
	 * @param attachmentSize
	 * @param allowedMimeTypes
	 * @param homeworkAttachments
	 */
	public NoticeDetails(int instituteId, int academicSessionId, UUID noticeId, String title, String description, UUID createdBy,
			Integer createdTimestamp, UUID updatedBy, Integer updatedTimestamp, Integer expiryDate, Boolean pinned,
			UUID pinnedBy, Integer pinnedTimestamp, NoticeBoardStatus status, UUID broadcastedBy,
			Integer broadcastedTimestamp, Map<UserType, List<String>> entityNameAndIdsMapping, 
			int attachmentCount, long attachmentSize, String allowedMimeTypes,
			List<Document<NoticeBoardDocumentType>> noticeBoardAttachments) {
		this.instituteId = instituteId;
		this.academicSessionId = academicSessionId;
		this.noticeId = noticeId;
		this.title = title;
		this.description = description;
		this.createdBy = createdBy;
		this.createdTimestamp = createdTimestamp;
		this.updatedBy = updatedBy;
		this.updatedTimestamp = updatedTimestamp;
		this.expiryDate = expiryDate;
		this.pinned = pinned;
		this.pinnedBy = pinnedBy;
		this.pinnedTimestamp = pinnedTimestamp;
		this.status = status;
		this.broadcastedBy = broadcastedBy;
		this.broadcastedTimestamp = broadcastedTimestamp;
		this.entityNameAndIdsMapping = entityNameAndIdsMapping;
		this.attachmentCount = attachmentCount;
		this.attachmentSize = attachmentSize;
		this.allowedMimeTypes = allowedMimeTypes;
		this.noticeBoardAttachments = noticeBoardAttachments;
	}

	/**
	 * @return the instituteId
	 */
	public int getInstituteId() {
		return instituteId;
	}

	public int getAcademicSessionId() {
		return academicSessionId;
	}

	/**
	 * @return the noticeId
	 */
	public UUID getNoticeId() {
		return noticeId;
	}

	/**
	 * @return the title
	 */
	public String getTitle() {
		return title;
	}

	/**
	 * @return the body
	 */
	public String getDescription() {
		return description;
	}

	/**
	 * @return the createdBy
	 */
	public UUID getCreatedBy() {
		return createdBy;
	}

	/**
	 * @return the createdTimestamp
	 */
	public Integer getCreatedTimestamp() {
		return createdTimestamp;
	}

	/**
	 * @return the updatedBy
	 */
	public UUID getUpdatedBy() {
		return updatedBy;
	}

	/**
	 * @return the updatedTimestamp
	 */
	public Integer getUpdatedTimestamp() {
		return updatedTimestamp;
	}

	/**
	 * @return the expiryDate
	 */
	public Integer getExpiryDate() {
		return expiryDate;
	}

	/**
	 * @return the pinned
	 */
	public Boolean getPinned() {
		return pinned;
	}

	/**
	 * @return the pinnedBy
	 */
	public UUID getPinnedBy() {
		return pinnedBy;
	}

	/**
	 * @return the pinnedTimestamp
	 */
	public Integer getPinnedTimestamp() {
		return pinnedTimestamp;
	}

	/**
	 * @return the status
	 */
	public NoticeBoardStatus getStatus() {
		return status;
	}

	/**
	 * @return the broadcastedBy
	 */
	public UUID getBroadcastedBy() {
		return broadcastedBy;
	}

	/**
	 * @return the broadcastedTimestamp
	 */
	public Integer getBroadcastedTimestamp() {
		return broadcastedTimestamp;
	}

	/**
	 * @return the entityNameAndIdsMapping
	 */
	public Map<UserType, List<String>> getEntityNameAndIdsMapping() {
		return entityNameAndIdsMapping;
	}

	/**
	 * @return the attachmentCount
	 */
	public int getAttachmentCount() {
		return attachmentCount;
	}

	/**
	 * @param attachmentCount the attachmentCount to set
	 */
	public void setAttachmentCount(int attachmentCount) {
		this.attachmentCount = attachmentCount;
	}

	/**
	 * @return the attachmentSize
	 */
	public long getAttachmentSize() {
		return attachmentSize;
	}

	/**
	 * @param attachmentSize the attachmentSize to set
	 */
	public void setAttachmentSize(long attachmentSize) {
		this.attachmentSize = attachmentSize;
	}

	/**
	 * @return the allowedMimeTypes
	 */
	public String getAllowedMimeTypes() {
		return allowedMimeTypes;
	}

	/**
	 * @param allowedMimeTypes the allowedMimeTypes to set
	 */
	public void setAllowedMimeTypes(String allowedMimeTypes) {
		this.allowedMimeTypes = allowedMimeTypes;
	}
	
	/**
	 * @return the homeworkAttachments
	 */
	public List<Document<NoticeBoardDocumentType>> getNoticeBoardAttachments() {
		return noticeBoardAttachments;
	}

	public void setAcademicSessionId(int academicSessionId) {
		this.academicSessionId = academicSessionId;
	}

	@Override
	public String toString() {
		return "NoticeDetails{" +
				"instituteId=" + instituteId +
				", academicSessionId=" + academicSessionId +
				", noticeId=" + noticeId +
				", title='" + title + '\'' +
				", description='" + description + '\'' +
				", createdBy=" + createdBy +
				", createdTimestamp=" + createdTimestamp +
				", updatedBy=" + updatedBy +
				", updatedTimestamp=" + updatedTimestamp +
				", expiryDate=" + expiryDate +
				", pinned=" + pinned +
				", pinnedBy=" + pinnedBy +
				", pinnedTimestamp=" + pinnedTimestamp +
				", status=" + status +
				", broadcastedBy=" + broadcastedBy +
				", broadcastedTimestamp=" + broadcastedTimestamp +
				", entityNameAndIdsMapping=" + entityNameAndIdsMapping +
				", attachmentCount=" + attachmentCount +
				", attachmentSize=" + attachmentSize +
				", allowedMimeTypes='" + allowedMimeTypes + '\'' +
				", noticeBoardAttachments=" + noticeBoardAttachments +
				'}';
	}

}
