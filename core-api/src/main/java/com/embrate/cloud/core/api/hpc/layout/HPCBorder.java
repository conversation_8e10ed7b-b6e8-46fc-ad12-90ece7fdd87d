package com.embrate.cloud.core.api.hpc.layout;

/**
 * <AUTHOR>
 */
public class HPCBorder {

	private Float allSideBorderWidth;
	private Float leftBorderWidth;
	private Float rightBorderWidth;
	private Float topBorderWidth;
	private Float bottomBorderWidth;

	public Float getAllSideBorderWidth() {
		return allSideBorderWidth;
	}

	public void setAllSideBorderWidth(Float allSideBorderWidth) {
		this.allSideBorderWidth = allSideBorderWidth;
	}

	public Float getLeftBorderWidth() {
		return leftBorderWidth;
	}

	public void setLeftBorderWidth(Float leftBorderWidth) {
		this.leftBorderWidth = leftBorderWidth;
	}

	public Float getRightBorderWidth() {
		return rightBorderWidth;
	}

	public void setRightBorderWidth(Float rightBorderWidth) {
		this.rightBorderWidth = rightBorderWidth;
	}

	public Float getTopBorderWidth() {
		return topBorderWidth;
	}

	public void setTopBorderWidth(Float topBorderWidth) {
		this.topBorderWidth = topBorderWidth;
	}

	public Float getBottomBorderWidth() {
		return bottomBorderWidth;
	}

	public void setBottomBorderWidth(Float bottomBorderWidth) {
		this.bottomBorderWidth = bottomBorderWidth;
	}

	@Override
	public String toString() {
		return "HPCBorder{" +
				"allSideBorderWidth=" + allSideBorderWidth +
				", leftBorderWidth=" + leftBorderWidth +
				", rightBorderWidth=" + rightBorderWidth +
				", topBorderWidth=" + topBorderWidth +
				", bottomBorderWidth=" + bottomBorderWidth +
				'}';
	}
}
