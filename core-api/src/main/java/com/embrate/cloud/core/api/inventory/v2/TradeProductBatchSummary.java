package com.embrate.cloud.core.api.inventory.v2;

import java.util.UUID;

/**
 * <AUTHOR>
 */

public class TradeProductBatchSummary {

    private final UUID batchId;

    private final String batchName;

    private final double quantity;

    private final double totalPrice;

    private final double totalDiscount;

    private final double totalTax;

    public TradeProductBatchSummary(UUID batchId, String batchName, double quantity, double totalPrice, double totalDiscount, double totalTax) {
        this.batchId = batchId;
        this.batchName = batchName;
        this.quantity = quantity;
        this.totalPrice = totalPrice;
        this.totalDiscount = totalDiscount;
        this.totalTax = totalTax;
    }

    public UUID getBatchId() {
        return batchId;
    }

    public String getBatchName() {
        return batchName;
    }

    public double getQuantity() {
        return quantity;
    }

    public double getTotalPrice() {
        return totalPrice;
    }

    public double getPricePerItem() {
        if(quantity <= 0){
            return 0;
        }
        return totalPrice/quantity;
    }

    public double getTotalDiscount() {
        return totalDiscount;
    }

    public double getTotalTax() {
        return totalTax;
    }

    public double getNetAmount() {
        return totalPrice + totalTax - totalDiscount;
    }

    @Override
    public String toString() {
        return "TradeProductBatchSummary{" +
                "batchId=" + batchId +
                ", batchName='" + batchName + '\'' +
                ", quantity=" + quantity +
                ", totalPrice=" + totalPrice +
                ", totalDiscount=" + totalDiscount +
                ", totalTax=" + totalTax +
                '}';
    }
}
