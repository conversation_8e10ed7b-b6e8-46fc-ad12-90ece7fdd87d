package com.embrate.cloud.core.api.payment;

/**
 * <AUTHOR>
 */

public class PaymentApplicableDiscount {

    private final boolean discountApplicable;

    private final PaymentDiscountType discountType;

    private final Double instantDiscountAmount;

    private final String discountMessageTitle;

    private final String discountMessageDescription;

    public PaymentApplicableDiscount(boolean discountApplicable, PaymentDiscountType discountType, Double instantDiscountAmount, String discountMessageTitle, String discountMessageDescription) {
        this.discountApplicable = discountApplicable;
        this.discountType = discountType;
        this.instantDiscountAmount = instantDiscountAmount;
        this.discountMessageTitle = discountMessageTitle;
        this.discountMessageDescription = discountMessageDescription;
    }

    public boolean isDiscountApplicable() {
        return discountApplicable;
    }

    public PaymentDiscountType getDiscountType() {
        return discountType;
    }

    public Double getInstantDiscountAmount() {
        return instantDiscountAmount;
    }

    public String getDiscountMessageTitle() {
        return discountMessageTitle;
    }

    public String getDiscountMessageDescription() {
        return discountMessageDescription;
    }

    @Override
    public String toString() {
        return "FeePaymentApplicableDiscount{" +
                "discountApplicable=" + discountApplicable +
                ", discountType=" + discountType +
                ", instantDiscountAmount=" + instantDiscountAmount +
                ", discountMessageTitle='" + discountMessageTitle + '\'' +
                ", discountMessageDescription='" + discountMessageDescription + '\'' +
                '}';
    }
}

