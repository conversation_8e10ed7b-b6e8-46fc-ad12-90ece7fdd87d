package com.embrate.cloud.core.api.leave.management.user;

import com.embrate.cloud.core.api.leave.management.LeaveDocumentType;
import com.embrate.cloud.core.api.leave.management.LeaveType;
import com.embrate.cloud.core.api.leave.management.transaction.LeaveTransactionStatus;
import com.embrate.cloud.core.api.leave.management.transaction.LeaveTransactionType;
import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.core.api.user.UserType;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @created_at 20/09/24 : 10:00
 **/

public class UserLeavePayload {
    private UUID transactionId;
    private  int academicSessionId;
    private int startDate;
    private int endDate;
    /**
     * id of user whose leave is applied
     */
    private  UUID userId;
    private UserType userType;
    private LeaveTransactionStatus transactionStatus;
    /**
     * id of user who have applied leave for userId
     */
    private  UUID appliedBy;
    private  int appliedAt;
    private  String description;
    private List<Document<LeaveDocumentType>> leaveAttachments;
    private LeaveType leaveType;

    public UserLeavePayload(UUID transactionId, int academicSessionId, int startDate, int endDate, UUID userId, LeaveTransactionStatus transactionStatus, UUID appliedBy, int appliedAt, String description, List<Document<LeaveDocumentType>> leaveAttachments, LeaveType leaveType, UserType userType) {
        this.transactionId = transactionId;
        this.academicSessionId = academicSessionId;
        this.startDate = startDate;
        this.endDate = endDate;
        this.userId = userId;
        this.transactionStatus = transactionStatus;
        this.appliedBy = appliedBy;
        this.appliedAt = appliedAt;
        this.description = description;
        this.leaveAttachments = leaveAttachments;
        this.leaveType = leaveType;
        this.userType = userType;
    }

    public UUID getTransactionId() {
        return transactionId;
    }

    public UserType getUserType() {
        return userType;
    }

    public void setUserType(UserType userType) {
        this.userType = userType;
    }

    public void setTransactionId(UUID transactionId) {
        this.transactionId = transactionId;
    }

    public int getAcademicSessionId() {
        return academicSessionId;
    }

    public LeaveType getLeaveType() {
        return leaveType;
    }

    public void setLeaveType(LeaveType leaveType) {
        this.leaveType = leaveType;
    }

    public void setAcademicSessionId(int academicSessionId) {
        this.academicSessionId = academicSessionId;
    }

    public int getStartDate() {
        return startDate;
    }

    public void setStartDate(int startDate) {
        this.startDate = startDate;
    }

    public int getEndDate() {
        return endDate;
    }

    public void setEndDate(int endDate) {
        this.endDate = endDate;
    }

    public UUID getUserId() {
        return userId;
    }

    public void setUserId(UUID userId) {
        this.userId = userId;
    }

    public LeaveTransactionStatus getTransactionStatus() {
        return transactionStatus;
    }

    public void setTransactionStatus(LeaveTransactionStatus transactionStatus) {
        this.transactionStatus = transactionStatus;
    }

    public UUID getAppliedBy() {
        return appliedBy;
    }

    public void setAppliedBy(UUID appliedBy) {
        this.appliedBy = appliedBy;
    }

    public int getAppliedAt() {
        return appliedAt;
    }

    public void setAppliedAt(int appliedAt) {
        this.appliedAt = appliedAt;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<Document<LeaveDocumentType>> getLeaveAttachments() {
        return leaveAttachments;
    }

    public void setLeaveAttachments(List<Document<LeaveDocumentType>> leaveAttachments) {
        this.leaveAttachments = leaveAttachments;
    }

    @Override
    public String toString() {
        return "UserLeavePayload{" +
                "transactionId=" + transactionId +
                ", academicSessionId=" + academicSessionId +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                ", userId=" + userId +
                ", transactionStatus=" + transactionStatus +
                ", appliedBy=" + appliedBy +
                ", appliedAt=" + appliedAt +
                ", description='" + description + '\'' +
                ", leaveAttachments=" + leaveAttachments +
                ", leaveType="+ leaveType +
                '}';
    }
}
