package com.embrate.cloud.core.api.inventory.v2.transaction;

import com.lernen.cloud.core.api.common.TransactionMode;
import com.lernen.cloud.core.api.inventory.InventoryTransactionStatus;
import com.lernen.cloud.core.api.inventory.InventoryTransactionType;
import com.lernen.cloud.core.api.inventory.InventoryUserType;
import com.lernen.cloud.core.api.inventory.PaymentStatus;

import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public class InventoryTransactionMetadata {

    private final UUID transactionId;
    private final String invoiceId;

    private final String reference;

    private final String email;

    private final InventoryUserType inventoryUserType;

    private final InventoryTransactionType transactionType;

    private final String transactionTo;

    private final String transactionBy;

    private final long transactionDate;

    private final long transactionAddedAt;

    private final PaymentStatus paymentStatus;

    private final TransactionMode transactionMode;

    private final InventoryTransactionStatus inventoryTransactionStatus;

    private final String description;

    private final Map<String, String> metadata;

    public InventoryTransactionMetadata(UUID transactionId, String invoiceId, String reference, String email,
                                        InventoryUserType inventoryUserType, InventoryTransactionType transactionType, String transactionTo, String transactionBy, long transactionDate,
                                        long transactionAddedAt, PaymentStatus paymentStatus, TransactionMode transactionMode,
                                        InventoryTransactionStatus inventoryTransactionStatus, String description, Map<String, String> metadata) {
        this.transactionId = transactionId;
        this.invoiceId = invoiceId;
        this.reference = reference;
        this.email = email;
        this.inventoryUserType = inventoryUserType;
        this.transactionType = transactionType;
        this.transactionTo = transactionTo;
        this.transactionBy = transactionBy;
        this.transactionDate = transactionDate;
        this.transactionAddedAt = transactionAddedAt;
        this.paymentStatus = paymentStatus;
        this.transactionMode = transactionMode;
        this.inventoryTransactionStatus = inventoryTransactionStatus;
        this.description = description;
        this.metadata = metadata;
    }

    public UUID getTransactionId() {
        return transactionId;
    }

    public String getInvoiceId() {
        return invoiceId;
    }

    public String getReference() {
        return reference;
    }

    public String getEmail() {
        return email;
    }

    public InventoryUserType getInventoryUserType() {
        return inventoryUserType;
    }

    public InventoryTransactionType getTransactionType() {
        return transactionType;
    }

    public String getTransactionTo() {
        return transactionTo;
    }

    public String getTransactionBy() {
        return transactionBy;
    }

    public long getTransactionDate() {
        return transactionDate;
    }

    public long getTransactionAddedAt() {
        return transactionAddedAt;
    }

    public PaymentStatus getPaymentStatus() {
        return paymentStatus;
    }

    public String getDescription() {
        return description;
    }

    public TransactionMode getTransactionMode() {
        return transactionMode;
    }

    public String getTransactionModeDisplayName() {
        return transactionMode.getDisplayName();
    }

    public InventoryTransactionStatus getInventoryTransactionStatus() {
        return inventoryTransactionStatus;
    }

    public Map<String, String> getMetadata() {
        return metadata;
    }

    @Override
    public String toString() {
        return "InventoryTransactionMetadata{" +
                "transactionId=" + transactionId +
                ", invoiceId='" + invoiceId + '\'' +
                ", reference='" + reference + '\'' +
                ", email='" + email + '\'' +
                ", inventoryUserType=" + inventoryUserType +
                ", transactionType=" + transactionType +
                ", transactionTo='" + transactionTo + '\'' +
                ", transactionBy='" + transactionBy + '\'' +
                ", transactionDate=" + transactionDate +
                ", transactionAddedAt=" + transactionAddedAt +
                ", paymentStatus=" + paymentStatus +
                ", transactionMode=" + transactionMode +
                ", inventoryTransactionStatus=" + inventoryTransactionStatus +
                ", description='" + description + '\'' +
                ", metadata=" + metadata +
                '}';
    }

}
