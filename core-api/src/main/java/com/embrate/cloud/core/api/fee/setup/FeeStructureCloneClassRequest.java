package com.embrate.cloud.core.api.fee.setup;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class FeeStructureCloneClassRequest {

	private int instituteId;
	private int academicSessionId;
	private String structureName;
	private String srcClassName;
	private Set<String> destClassNames;

	public int getInstituteId() {
		return instituteId;
	}

	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	public int getAcademicSessionId() {
		return academicSessionId;
	}

	public void setAcademicSessionId(int academicSessionId) {
		this.academicSessionId = academicSessionId;
	}

	public String getStructureName() {
		return structureName;
	}

	public void setStructureName(String structureName) {
		this.structureName = structureName;
	}

	public String getSrcClassName() {
		return srcClassName;
	}

	public void setSrcClassName(String srcClassName) {
		this.srcClassName = srcClassName;
	}

	public Set<String> getDestClassNames() {
		return destClassNames;
	}

	public void setDestClassNames(Set<String> destClassNames) {
		this.destClassNames = destClassNames;
	}

	@Override
	public String toString() {
		return "FeeStructureCloneClassRequest{" +
				"instituteId=" + instituteId +
				", academicSessionId=" + academicSessionId +
				", structureName='" + structureName + '\'' +
				", srcClassName='" + srcClassName + '\'' +
				", destClassNames=" + destClassNames +
				'}';
	}
}
