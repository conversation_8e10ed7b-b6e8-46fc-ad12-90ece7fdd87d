package com.embrate.cloud.core.api.wallet;

import com.lernen.cloud.core.api.configurations.FeeInvoicePreferences;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.user.UserType;

public class WalletTransactionInvoiceDocumentDetails {

    private final String documentName;
    private final boolean officeCopy;
    private final UserType userType;
    private final Institute institute;
    private final Student student;
    private final WalletTransactionPayload walletTransactionPayload;
    private final FeeInvoicePreferences feeInvoicePreferences;

    public WalletTransactionInvoiceDocumentDetails(String documentName, boolean officeCopy, UserType userType, Institute institute, Student student, WalletTransactionPayload walletTransactionPayload, FeeInvoicePreferences feeInvoicePreferences) {
        this.documentName = documentName;
        this.officeCopy = officeCopy;
        this.userType = userType;
        this.institute = institute;
        this.student = student;
        this.walletTransactionPayload = walletTransactionPayload;
        this.feeInvoicePreferences = feeInvoicePreferences;
    }

    public String getDocumentName() {
        return documentName;
    }

    public boolean isOfficeCopy() {
        return officeCopy;
    }

    public UserType getUserType() {
        return userType;
    }

    public Institute getInstitute() {
        return institute;
    }

    public Student getStudent() {
        return student;
    }

    public WalletTransactionPayload getWalletTransactionPayload() {
        return walletTransactionPayload;
    }

    public FeeInvoicePreferences getFeeInvoicePreferences() {
        return feeInvoicePreferences;
    }

    @Override
    public String toString() {
        return "WalletTransactionInvoiceDocumentDetails{" +
                "documentName='" + documentName + '\'' +
                ", officeCopy=" + officeCopy +
                ", userType=" + userType +
                ", institute=" + institute +
                ", student=" + student +
                ", walletTransactionPayload=" + walletTransactionPayload +
                ", feeInvoicePreferences=" + feeInvoicePreferences +
                '}';
    }
}
