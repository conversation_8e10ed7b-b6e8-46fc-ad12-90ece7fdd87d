/**
 * 
 */
package com.embrate.cloud.core.api.salary;

import java.util.UUID;

import com.lernen.cloud.core.api.staff.Staff;

/**
 * <AUTHOR>
 *
 */
public class StaffSalaryStructureRow {

	private Integer instituteId;
	
	private UUID structureId;
	
	private String structureName;
	
	private Staff staff;
	
	private Integer salaryCycleStart;
	
	private StructureStatus structureStatus;
	
	private PayHeadConfiguration payHeadConfiguration;
	
	private Double amount;

	/**
	 * @param instituteId
	 * @param structureId
	 * @param structureName
	 * @param staff
	 * @param salaryCycleStart
	 * @param structureStatus
	 * @param payHeadConfiguration
	 * @param amount
	 * @param description
	 */
	public StaffSalaryStructureRow(Integer instituteId, UUID structureId, String structureName, Staff staff,
			Integer salaryCycleStart, StructureStatus structureStatus, PayHeadConfiguration payHeadConfiguration,
			Double amount) {
		this.instituteId = instituteId;
		this.structureId = structureId;
		this.structureName = structureName;
		this.staff = staff;
		this.salaryCycleStart = salaryCycleStart;
		this.structureStatus = structureStatus;
		this.payHeadConfiguration = payHeadConfiguration;
		this.amount = amount;
	}

	/**
	 * 
	 */
	public StaffSalaryStructureRow() {
	}

	/**
	 * @return the instituteId
	 */
	public Integer getInstituteId() {
		return instituteId;
	}

	/**
	 * @param instituteId the instituteId to set
	 */
	public void setInstituteId(Integer instituteId) {
		this.instituteId = instituteId;
	}

	/**
	 * @return the structureId
	 */
	public UUID getStructureId() {
		return structureId;
	}

	/**
	 * @param structureId the structureId to set
	 */
	public void setStructureId(UUID structureId) {
		this.structureId = structureId;
	}

	/**
	 * @return the structureName
	 */
	public String getStructureName() {
		return structureName;
	}

	/**
	 * @param structureName the structureName to set
	 */
	public void setStructureName(String structureName) {
		this.structureName = structureName;
	}

	/**
	 * @return the staff
	 */
	public Staff getStaff() {
		return staff;
	}

	/**
	 * @param staff the staff to set
	 */
	public void setStaff(Staff staff) {
		this.staff = staff;
	}

	/**
	 * @return the salaryCycleStart
	 */
	public Integer getSalaryCycleStart() {
		return salaryCycleStart;
	}

	/**
	 * @param salaryCycleStart the salaryCycleStart to set
	 */
	public void setSalaryCycleStart(Integer salaryCycleStart) {
		this.salaryCycleStart = salaryCycleStart;
	}

	/**
	 * @return the structureStatus
	 */
	public StructureStatus getStructureStatus() {
		return structureStatus;
	}

	/**
	 * @param structureStatus the structureStatus to set
	 */
	public void setStructureStatus(StructureStatus structureStatus) {
		this.structureStatus = structureStatus;
	}

	/**
	 * @return the payHeadConfiguration
	 */
	public PayHeadConfiguration getPayHeadConfiguration() {
		return payHeadConfiguration;
	}

	/**
	 * @param payHeadConfiguration the payHeadConfiguration to set
	 */
	public void setPayHeadConfiguration(PayHeadConfiguration payHeadConfiguration) {
		this.payHeadConfiguration = payHeadConfiguration;
	}

	/**
	 * @return the amount
	 */
	public Double getAmount() {
		return amount;
	}

	/**
	 * @param amount the amount to set
	 */
	public void setAmount(Double amount) {
		this.amount = amount;
	}


	@Override
	public String toString() {
		return "StaffSalaryStructureRow [instituteId=" + instituteId + ", structureId=" + structureId
				+ ", structureName=" + structureName + ", staff=" + staff + ", salaryCycleStart=" + salaryCycleStart
				+ ", structureStatus=" + structureStatus + ", payHeadConfiguration=" + payHeadConfiguration
				+ ", amount=" + amount + "]";
	}
}
