package com.embrate.cloud.core.api.service.communication.templates;

/**
 * <AUTHOR>
 */
public enum TemplateVariableType {

    STUDENT_NAME("Student Name"),
    INSTITUTE_NAME("Institute Name"),
    CURRENT_DATE("Today Date"),
    CURRENT_DATE_WITH_TIME("Current Date with time"),
    CUSTOM("Custom"),
    OBTAINED_MARKS("Obtained Marks"),
    TOTAL_MARKS("Total Marks"),
    STANDARD_NAME("Standard Name"),
    REGISTRATION_NUMBER("Registration Number"),
    USERNAME("Username"),
    PASSWORD("Password"),
    FATHER_NAME("Father Name"),
    USERS_NAME("User's Name"),
    ADMISSION_NUMBER("Admission Number"),
    DUE_FEE_AMOUNT("Due Fee Amount"),
    DATE("Date"),
    ATTENDANCE_STATUS("Attendance Status");

    private final String displayName;

    TemplateVariableType(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }
}
