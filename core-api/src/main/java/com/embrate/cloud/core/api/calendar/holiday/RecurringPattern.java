package com.embrate.cloud.core.api.calendar.holiday;

import java.util.Set;

/**
 * <AUTHOR>
 */

public class RecurringPattern {

    private HolidayFrequency recurringFrequency;

    private int dayOfWeek;

    private Set<Integer> weekOfMonth;

    public RecurringPattern() {
    }

    public RecurringPattern(HolidayFrequency recurringFrequency, int dayOfWeek, Set<Integer> weekOfMonth) {
        this.recurringFrequency = recurringFrequency;
        this.dayOfWeek = dayOfWeek;
        this.weekOfMonth = weekOfMonth;
    }

    public HolidayFrequency getRecurringFrequency() {
        return recurringFrequency;
    }

    public void setRecurringFrequency(HolidayFrequency recurringFrequency) {
        this.recurringFrequency = recurringFrequency;
    }

    public int getDayOfWeek() {
        return dayOfWeek;
    }

    public void setDayOfWeek(int dayOfWeek) {
        this.dayOfWeek = dayOfWeek;
    }

    public Set<Integer> getWeekOfMonth() {
        return weekOfMonth;
    }

    public void setWeekOfMonth(Set<Integer> weekOfMonth) {
        this.weekOfMonth = weekOfMonth;
    }

    @Override
    public String toString() {
        return "RecurringPattern{" +
                "recurringFrequency=" + recurringFrequency +
                ", dayOfWeek=" + dayOfWeek +
                ", weekOfMonth=" + weekOfMonth +
                '}';
    }
}
