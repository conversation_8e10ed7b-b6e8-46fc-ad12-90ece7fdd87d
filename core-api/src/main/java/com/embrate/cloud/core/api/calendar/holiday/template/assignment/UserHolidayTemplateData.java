package com.embrate.cloud.core.api.calendar.holiday.template.assignment;

import com.embrate.cloud.core.api.calendar.holiday.template.HolidayTemplate;
import com.embrate.cloud.core.api.calendar.holiday.template.HolidayTemplateDetails;
import com.lernen.cloud.core.api.user.UserType;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */

public class UserHolidayTemplateData {

    private final UUID userId;

    private final UserType userType;

    private final List<HolidayTemplateDuration> holidayTemplateDurationList;

    public UserHolidayTemplateData(UUID userId, UserType userType, List<HolidayTemplateDuration> holidayTemplateDurationList) {
        this.userId = userId;
        this.userType = userType;
        this.holidayTemplateDurationList = holidayTemplateDurationList;
    }

    public UUID getUserId() {
        return userId;
    }

    public UserType getUserType() {
        return userType;
    }

    public List<HolidayTemplateDuration> getHolidayTemplateDurationList() {
        return holidayTemplateDurationList;
    }

    @Override
    public String toString() {
        return "UserHolidayTemplateData{" +
                "userId=" + userId +
                ", userType=" + userType +
                ", holidayTemplateDurationList=" + holidayTemplateDurationList +
                '}';
    }
}
