package com.embrate.cloud.core.api.leave.management.policy.user;

import com.lernen.cloud.core.api.user.UserType;

import java.util.Set;
import java.util.UUID;

/**
 * <AUTHOR>
 */

public class UserLeavePolicyTemplateAssignPayload {
    private int academicSessionId;

    private UUID templateId;

    private Set<UUID> userList;

    public int getAcademicSessionId() {
        return academicSessionId;
    }

    public void setAcademicSessionId(int academicSessionId) {
        this.academicSessionId = academicSessionId;
    }

    public UUID getTemplateId() {
        return templateId;
    }

    public void setTemplateId(UUID templateId) {
        this.templateId = templateId;
    }

    public Set<UUID> getUserList() {
        return userList;
    }

    public void setUserList(Set<UUID> userList) {
        this.userList = userList;
    }

    @Override
    public String toString() {
        return "UserLeavePolicyTemplateAssignPayload{" +
                "academicSessionId=" + academicSessionId +
                ", templateId=" + templateId +
                ", userList=" + userList +
                '}';
    }
}
