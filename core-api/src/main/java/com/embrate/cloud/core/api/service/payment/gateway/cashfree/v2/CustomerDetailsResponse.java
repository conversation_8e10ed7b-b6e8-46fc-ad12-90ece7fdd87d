package com.embrate.cloud.core.api.service.payment.gateway.cashfree.v2;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
public class CustomerDetailsResponse {

	@JsonProperty("customer_id")
	private String customerId;

	@JsonProperty("customer_name")
	private String customerName;

	@JsonProperty("customer_email")
	private String customerEmail;

	@JsonProperty("customer_phone")
	private String customerPhone;

	@JsonProperty("customer_uid")
	private String customerUid;

	public String getCustomerId() {
		return customerId;
	}

	public void setCustomerId(String customerId) {
		this.customerId = customerId;
	}

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public String getCustomerEmail() {
		return customerEmail;
	}

	public void setCustomerEmail(String customerEmail) {
		this.customerEmail = customerEmail;
	}

	public String getCustomerPhone() {
		return customerPhone;
	}

	public void setCustomerPhone(String customerPhone) {
		this.customerPhone = customerPhone;
	}

	public String getCustomerUid() {
		return customerUid;
	}

	public void setCustomerUid(String customerUid) {
		this.customerUid = customerUid;
	}

	@Override
	public String toString() {
		return "CustomerDetailsResponse{" +
				"customerId='" + customerId + '\'' +
				", customerName='" + customerName + '\'' +
				", customerEmail='" + customerEmail + '\'' +
				", customerPhone='" + customerPhone + '\'' +
				", customerUid='" + customerUid + '\'' +
				'}';
	}
}
