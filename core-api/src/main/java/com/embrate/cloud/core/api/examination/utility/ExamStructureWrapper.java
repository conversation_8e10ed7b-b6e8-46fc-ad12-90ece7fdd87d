package com.embrate.cloud.core.api.examination.utility;

import java.util.List;
import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class ExamStructureWrapper {

	private int academicSessionId;

	private UUID standardId;

	private List<ExamStructure> examStructures;

	public int getAcademicSessionId() {
		return academicSessionId;
	}

	public void setAcademicSessionId(int academicSessionId) {
		this.academicSessionId = academicSessionId;
	}

	public UUID getStandardId() {
		return standardId;
	}

	public void setStandardId(UUID standardId) {
		this.standardId = standardId;
	}

	public List<ExamStructure> getExamStructures() {
		return examStructures;
	}

	public void setExamStructures(List<ExamStructure> examStructures) {
		this.examStructures = examStructures;
	}

}
