package com.embrate.cloud.core.api.hpc.utils;

import com.embrate.cloud.core.api.hpc.layout.HPCPDFAttributes;
import com.embrate.cloud.core.api.hpc.utils.HPCExamType;

import java.util.List;

/**
 * <AUTHOR>
 */
public class HPCExamValue {

	private HPCExamType hpcExamType;

	private String value;

	private List<String> suggestedDropDownValues;

	private HPCPDFAttributes pdfAttributes;

	public HPCExamType getHpcExamType() {
		return hpcExamType;
	}

	public void setHpcExamType(HPCExamType hpcExamType) {
		this.hpcExamType = hpcExamType;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public List<String> getSuggestedDropDownValues() {
		return suggestedDropDownValues;
	}

	public void setSuggestedDropDownValues(List<String> suggestedDropDownValues) {
		this.suggestedDropDownValues = suggestedDropDownValues;
	}

	public HPCPDFAttributes getPdfAttributes() {
		return pdfAttributes;
	}

	public void setPdfAttributes(HPCPDFAttributes pdfAttributes) {
		this.pdfAttributes = pdfAttributes;
	}

	@Override
	public String toString() {
		return "HPCExamValue{" +
				"hpcExamType=" + hpcExamType +
				", value='" + value + '\'' +
				", suggestedDropDownValues=" + suggestedDropDownValues +
				", pdfAttributes=" + pdfAttributes +
				'}';
	}
}
