package com.embrate.cloud.core.api.leave.management.transaction;

import java.util.List;
import java.util.Set;
import java.util.UUID;

/**
 * <AUTHOR>
 */

public class UserLeaveUpdatePayload {

    private UUID userId;
    private Set<UUID> deleteTransactionIds;

    private List<UserLeaveTransactionDetails> userLeaveTransactionDetailsList;

    public UserLeaveUpdatePayload() {
    }

    public UserLeaveUpdatePayload(UUID userId, Set<UUID> deleteTransactionIds, List<UserLeaveTransactionDetails> userLeaveTransactionDetailsList) {
        this.userId = userId;
        this.deleteTransactionIds = deleteTransactionIds;
        this.userLeaveTransactionDetailsList = userLeaveTransactionDetailsList;
    }

    public UUID getUserId() {
        return userId;
    }

    public void setUserId(UUID userId) {
        this.userId = userId;
    }

    public Set<UUID> getDeleteTransactionIds() {
        return deleteTransactionIds;
    }

    public void setDeleteTransactionIds(Set<UUID> deleteTransactionIds) {
        this.deleteTransactionIds = deleteTransactionIds;
    }

    public List<UserLeaveTransactionDetails> getUserLeaveTransactionDetailsList() {
        return userLeaveTransactionDetailsList;
    }

    public void setUserLeaveTransactionDetailsList(List<UserLeaveTransactionDetails> userLeaveTransactionDetailsList) {
        this.userLeaveTransactionDetailsList = userLeaveTransactionDetailsList;
    }

    @Override
    public String toString() {
        return "UserLeaveUpdatePayload{" +
                "userId=" + userId +
                ", deleteTransactionIds=" + deleteTransactionIds +
                ", userLeaveTransactionDetailsList=" + userLeaveTransactionDetailsList +
                '}';
    }
}
