/**
 * 
 */
package com.embrate.cloud.core.api.salary;

import com.embrate.cloud.core.api.salary.v2.PayHeadTag;

/**
 * <AUTHOR>
 *
 */
public class PayHeadConfiguration {

	private Integer payHeadId;

	private String payHead;

	private PayHeadType payHeadType;

	private boolean systemPayHead;

	private PayHeadTag tag;

	private boolean epfApplicable;

	private boolean esiApplicable;

	private boolean proRataBasis;

//	private PayHeadAmountRule payHeadAmountRule;

	private String description;

	private boolean flatInclusion;


	public PayHeadConfiguration(Integer payHeadId, String payHead, PayHeadType payHeadType, boolean systemPayHead, PayHeadTag tag, boolean epfApplicable, boolean esiApplicable, boolean proRataBasis, String description) {
		this.payHeadId = payHeadId;
		this.payHead = payHead;
		this.payHeadType = payHeadType;
		this.systemPayHead = systemPayHead;
		this.tag = tag;
		this.epfApplicable = epfApplicable;
		this.esiApplicable = esiApplicable;
		this.proRataBasis = proRataBasis;
		this.description = description;
		this.flatInclusion = !epfApplicable && !esiApplicable && !proRataBasis;
	}

	/**
	 * 
	 */
	public PayHeadConfiguration() {
	}


	public Integer getPayHeadId() {
		return payHeadId;
	}

	public void setPayHeadId(Integer payHeadId) {
		this.payHeadId = payHeadId;
	}

	public String getPayHead() {
		return payHead;
	}

	public void setPayHead(String payHead) {
		this.payHead = payHead;
	}

	public PayHeadType getPayHeadType() {
		return payHeadType;
	}

	public void setPayHeadType(PayHeadType payHeadType) {
		this.payHeadType = payHeadType;
	}

	public boolean isSystemPayHead() {
		return systemPayHead;
	}

	public void setSystemPayHead(boolean systemPayHead) {
		this.systemPayHead = systemPayHead;
	}

	public PayHeadTag getTag() {
		return tag;
	}

	public void setTag(PayHeadTag tag) {
		this.tag = tag;
	}

	public boolean isEpfApplicable() {
		return epfApplicable;
	}

	public void setEpfApplicable(boolean epfApplicable) {
		this.epfApplicable = epfApplicable;
	}

	public boolean isEsiApplicable() {
		return esiApplicable;
	}

	public void setEsiApplicable(boolean esiApplicable) {
		this.esiApplicable = esiApplicable;
	}

	public boolean isProRataBasis() {
		return proRataBasis;
	}

	public void setProRataBasis(boolean proRataBasis) {
		this.proRataBasis = proRataBasis;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}


	public boolean isFlatInclusion() {
		return flatInclusion;
	}

	@Override
	public String toString() {
		return "PayHeadConfiguration{" +
				"payHeadId=" + payHeadId +
				", payHead='" + payHead + '\'' +
				", payHeadType=" + payHeadType +
				", systemPayHead=" + systemPayHead +
				", tag=" + tag +
				", epfApplicable=" + epfApplicable +
				", esiApplicable=" + esiApplicable +
				", proRataBasis=" + proRataBasis +
				", description='" + description + '\'' +
				", flatInclusion=" + flatInclusion +
				'}';
	}
}
