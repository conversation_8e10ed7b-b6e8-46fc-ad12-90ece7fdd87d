package com.embrate.cloud.core.api.salary.v2.utils;

import com.embrate.cloud.core.api.leave.management.balance.UserNetLeaveBalance;

import java.util.List;

/**
 * <AUTHOR>
 */

public class StaffSalaryAttendanceSummary {

    private final double totalDays;

    private final double totalHolidays;

    private final double presentDays;

    private final double overTimeDays;

    private final double absentDays;

//    private final List<LeaveCount> leaveCountList;

    private final double leaveDays;

    private final double totalWorkingDays;

    private final double totalLOPDays;

    private final UserNetLeaveBalance userNetLeaveBalance;

    private final List<StaffSalaryDayAttendance> dayAttendanceList;

    public StaffSalaryAttendanceSummary(double totalDays, double totalHolidays, double presentDays, double overTimeDays, double absentDays, double leaveDays, UserNetLeaveBalance userNetLeaveBalance, List<StaffSalaryDayAttendance> dayAttendanceList) {
        this.totalDays = totalDays;
        this.totalHolidays = totalHolidays;
        this.presentDays = presentDays;
        this.overTimeDays = overTimeDays;
        this.absentDays = absentDays;
        this.leaveDays = leaveDays;
        this.totalWorkingDays = totalDays - (absentDays - leaveDays);
        this.userNetLeaveBalance = userNetLeaveBalance;
        this.dayAttendanceList = dayAttendanceList;
        this.totalLOPDays = absentDays - leaveDays;
    }

    public double getTotalDays() {
        return totalDays;
    }

    public double getTotalHolidays() {
        return totalHolidays;
    }

    public double getPresentDays() {
        return presentDays;
    }

    public double getOverTimeDays() {
        return overTimeDays;
    }

    public double getAbsentDays() {
        return absentDays;
    }

    public double getLeaveDays() {
        return leaveDays;
    }

    public double getTotalWorkingDays() {
        return totalWorkingDays;
    }

    public double getTotalLOPDays() {
        return totalLOPDays;
    }

    public UserNetLeaveBalance getUserNetLeaveBalance() {
        return userNetLeaveBalance;
    }

    public List<StaffSalaryDayAttendance> getDayAttendanceList() {
        return dayAttendanceList;
    }

    @Override
    public String toString() {
        return "StaffSalaryAttendanceSummary{" +
                "totalDays=" + totalDays +
                ", totalHolidays=" + totalHolidays +
                ", presentDays=" + presentDays +
                ", overTimeDays=" + overTimeDays +
                ", absentDays=" + absentDays +
                ", leaveDays=" + leaveDays +
                ", totalWorkingDays=" + totalWorkingDays +
                ", totalLOPDays=" + totalLOPDays +
                ", userNetLeaveBalance=" + userNetLeaveBalance +
                ", dayAttendanceList=" + dayAttendanceList +
                '}';
    }
}
