package com.embrate.cloud.core.api.whatsapp.webpay;

import com.lernen.cloud.core.api.notification.NotificationStatus;

/**
 *
 * <AUTHOR>
 *
 */
public enum WebPayWhatsappStatus {

	DELIVERED("Delivered", NotificationStatus.DELIVERED), UNDELIVERED("Undelivered", NotificationStatus.FAILED),
	FAILED("Failed", NotificationStatus.FAILED), SENT("Submitted", NotificationStatus.SENT), OTHER("Other", NotificationStatus.SENT);

	private final String statusValue;
	private final NotificationStatus notificationStatus;

	private WebPayWhatsappStatus(String statusValue,
								 NotificationStatus notificationStatus) {
		this.statusValue = statusValue;
		this.notificationStatus = notificationStatus;
	}

	public String getStatusValue() {
		return statusValue;
	}

	public NotificationStatus getNotificationStatus() {
		return notificationStatus;
	}

	public static NotificationStatus getNotificationStatus(String statusValue) {
		for (final WebPayWhatsappStatus webPayWhatsappStatus : WebPayWhatsappStatus.values()) {
			if (webPayWhatsappStatus.getStatusValue()
					.equalsIgnoreCase(statusValue)) {
				return webPayWhatsappStatus.getNotificationStatus();
			}
		}
		return null;
	}
}
