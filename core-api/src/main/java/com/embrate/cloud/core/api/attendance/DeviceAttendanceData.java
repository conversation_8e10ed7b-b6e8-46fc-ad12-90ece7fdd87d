package com.embrate.cloud.core.api.attendance;

import java.util.Map;

/**
 * <AUTHOR>
 */

public class DeviceAttendanceData {

    private final String transactionId;
    private final String extDeviceId;
    private final String authToken;
    private final String extUserId;

    private final DeviceAttendanceInputCategory attendanceInputCategory;

    private final long logTime;

    private final long dataReceiveTime;

    private final Map<String, String> metadata;

    public DeviceAttendanceData(String transactionId, String extDeviceId, String authToken, String extUserId, DeviceAttendanceInputCategory attendanceInputCategory, long logTime, long dataReceiveTime, Map<String, String> metadata) {
        this.transactionId = transactionId;
        this.extDeviceId = extDeviceId;
        this.authToken = authToken;
        this.extUserId = extUserId;
        this.attendanceInputCategory = attendanceInputCategory;
        this.logTime = logTime;
        this.dataReceiveTime = dataReceiveTime;
        this.metadata = metadata;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public String getExtDeviceId() {
        return extDeviceId;
    }

    public String getAuthToken() {
        return authToken;
    }

    public String getExtUserId() {
        return extUserId;
    }

    public DeviceAttendanceInputCategory getAttendanceInputCategory() {
        return attendanceInputCategory;
    }

    public long getLogTime() {
        return logTime;
    }

    public long getDataReceiveTime() {
        return dataReceiveTime;
    }

    public Map<String, String> getMetadata() {
        return metadata;
    }

    @Override
    public String toString() {
        return "DeviceAttendanceData{" +
                "transactionId='" + transactionId + '\'' +
                ", extDeviceId='" + extDeviceId + '\'' +
                ", authToken='" + authToken + '\'' +
                ", extUserId='" + extUserId + '\'' +
                ", attendanceInputCategory=" + attendanceInputCategory +
                ", logTime=" + logTime +
                ", dataReceiveTime=" + dataReceiveTime +
                ", metadata=" + metadata +
                '}';
    }
}
