package com.embrate.cloud.core.api.dashboards.attendance;

import com.lernen.cloud.core.api.attendance.AttendanceStatus;

/**
 * <AUTHOR>
 */
public class StaffAttendanceCount {

    private final int instituteId;
    private final AttendanceStatus attendanceStatus;
    private final int count;

    public StaffAttendanceCount(int instituteId, AttendanceStatus attendanceStatus, int count) {
        this.instituteId = instituteId;
        this.attendanceStatus = attendanceStatus;
        this.count = count;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public AttendanceStatus getAttendanceStatus() {
        return attendanceStatus;
    }

    public int getCount() {
        return count;
    }

    @Override
    public String toString() {
        return "StaffAttendanceCount{" +
                "instituteId=" + instituteId +
                ", attendanceStatus=" + attendanceStatus +
                ", count=" + count +
                '}';
    }
}
