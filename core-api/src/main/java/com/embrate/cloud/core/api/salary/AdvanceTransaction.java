/**
 * 
 */
package com.embrate.cloud.core.api.salary;

import java.util.UUID;

import com.lernen.cloud.core.api.common.TransactionMode;
import com.lernen.cloud.core.api.fees.payment.FeePaymentTransactionStatus;
import com.lernen.cloud.core.api.staff.Staff;

/**
 * <AUTHOR>
 *
 */
public class AdvanceTransaction {

	private int instituteId;

	private UUID transactionId;

	private Staff staff;

	private String reason;

	private AdvanceTransactionCategory advanceTransactionCategory;
	
	private TransactionMode transactionMode;
	
	private Integer transactionDate;
	
	private FeePaymentTransactionStatus transactionStatus;

	private double amount;

	private UUID transactionBy;

	private Integer transactionAddedAt;

	private UUID payslipId;

	/**
	 * @param instituteId
	 * @param transactionId
	 * @param staff
	 * @param reason
	 * @param advanceTransactionCategory
	 * @param transactionMode
	 * @param transactionDate
	 * @param transactionStatus
	 * @param amount
	 * @param transactionBy
	 * @param transactionAddedAt
	 * @param payslipId
	 */
	public AdvanceTransaction(int instituteId, UUID transactionId, Staff staff, String reason,
			AdvanceTransactionCategory advanceTransactionCategory, TransactionMode transactionMode,
			Integer transactionDate, FeePaymentTransactionStatus transactionStatus, double amount, UUID transactionBy,
			Integer transactionAddedAt, UUID payslipId) {
		this.instituteId = instituteId;
		this.transactionId = transactionId;
		this.staff = staff;
		this.reason = reason;
		this.advanceTransactionCategory = advanceTransactionCategory;
		this.transactionMode = transactionMode;
		this.transactionDate = transactionDate;
		this.transactionStatus = transactionStatus;
		this.amount = amount;
		this.transactionBy = transactionBy;
		this.transactionAddedAt = transactionAddedAt;
		this.payslipId = payslipId;
	}

	/**
	 * 
	 */
	public AdvanceTransaction() {
	}

	/**
	 * @return the instituteId
	 */
	public int getInstituteId() {
		return instituteId;
	}

	/**
	 * @param instituteId the instituteId to set
	 */
	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	/**
	 * @return the transactionId
	 */
	public UUID getTransactionId() {
		return transactionId;
	}

	/**
	 * @param transactionId the transactionId to set
	 */
	public void setTransactionId(UUID transactionId) {
		this.transactionId = transactionId;
	}

	/**
	 * @return the staff
	 */
	public Staff getStaff() {
		return staff;
	}

	/**
	 * @param staff the staff to set
	 */
	public void setStaff(Staff staff) {
		this.staff = staff;
	}

	/**
	 * @return the reason
	 */
	public String getReason() {
		return reason;
	}

	/**
	 * @param reason the reason to set
	 */
	public void setReason(String reason) {
		this.reason = reason;
	}

	/**
	 * @return the advanceTransactionCategory
	 */
	public AdvanceTransactionCategory getAdvanceTransactionCategory() {
		return advanceTransactionCategory;
	}

	/**
	 * @param advanceTransactionCategory the advanceTransactionCategory to set
	 */
	public void setAdvanceTransactionCategory(AdvanceTransactionCategory advanceTransactionCategory) {
		this.advanceTransactionCategory = advanceTransactionCategory;
	}

	/**
	 * @return the transactionMode
	 */
	public TransactionMode getTransactionMode() {
		return transactionMode;
	}

	/**
	 * @param transactionMode the transactionMode to set
	 */
	public void setTransactionMode(TransactionMode transactionMode) {
		this.transactionMode = transactionMode;
	}

	/**
	 * @return the transactionDate
	 */
	public Integer getTransactionDate() {
		return transactionDate;
	}

	/**
	 * @param transactionDate the transactionDate to set
	 */
	public void setTransactionDate(Integer transactionDate) {
		this.transactionDate = transactionDate;
	}

	/**
	 * @return the transactionStatus
	 */
	public FeePaymentTransactionStatus getTransactionStatus() {
		return transactionStatus;
	}

	/**
	 * @param transactionStatus the transactionStatus to set
	 */
	public void setTransactionStatus(FeePaymentTransactionStatus transactionStatus) {
		this.transactionStatus = transactionStatus;
	}

	/**
	 * @return the amount
	 */
	public double getAmount() {
		return amount;
	}

	/**
	 * @param amount the amount to set
	 */
	public void setAmount(double amount) {
		this.amount = amount;
	}

	/**
	 * @return the transactionBy
	 */
	public UUID getTransactionBy() {
		return transactionBy;
	}

	/**
	 * @param transactionBy the transactionBy to set
	 */
	public void setTransactionBy(UUID transactionBy) {
		this.transactionBy = transactionBy;
	}

	/**
	 * @return the transactionAddedAt
	 */
	public Integer getTransactionAddedAt() {
		return transactionAddedAt;
	}

	/**
	 * @param transactionAddedAt the transactionAddedAt to set
	 */
	public void setTransactionAddedAt(Integer transactionAddedAt) {
		this.transactionAddedAt = transactionAddedAt;
	}

	/**
	 * @return the payslipId
	 */
	public UUID getPayslipId() {
		return payslipId;
	}

	/**
	 * @param payslipId the payslipId to set
	 */
	public void setPayslipId(UUID payslipId) {
		this.payslipId = payslipId;
	}

	@Override
	public String toString() {
		return "AdvanceTransaction [instituteId=" + instituteId + ", transactionId=" + transactionId + ", staff="
				+ staff + ", reason=" + reason + ", advanceTransactionCategory=" + advanceTransactionCategory
				+ ", transactionMode=" + transactionMode + ", transactionDate=" + transactionDate
				+ ", transactionStatus=" + transactionStatus + ", amount=" + amount + ", transactionBy=" + transactionBy
				+ ", transactionAddedAt=" + transactionAddedAt + ", payslipId=" + payslipId + "]";
	}
	
	

}
