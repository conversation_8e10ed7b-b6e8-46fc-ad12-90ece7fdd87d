package com.embrate.cloud.core.api.inventory.v2.product.group;

import java.util.UUID;

/**
 * <AUTHOR>
 */
public class InventoryProductGroupItem {

    private UUID skuId;

    private UUID batchId;

    private double quantity;

    private Double discount;

    public UUID getSkuId() {
        return skuId;
    }

    public void setSkuId(UUID skuId) {
        this.skuId = skuId;
    }

    public UUID getBatchId() {
        return batchId;
    }

    public void setBatchId(UUID batchId) {
        this.batchId = batchId;
    }

    public double getQuantity() {
        return quantity;
    }

    public void setQuantity(double quantity) {
        this.quantity = quantity;
    }

    public Double getDiscount() {
        return discount;
    }

    public void setDiscount(Double discount) {
        this.discount = discount;
    }

    @Override
    public String toString() {
        return "InventoryProductGroupItem{" +
                "skuId=" + skuId +
                ", batchId=" + batchId +
                ", quantity=" + quantity +
                ", discount=" + discount +
                '}';
    }
}
