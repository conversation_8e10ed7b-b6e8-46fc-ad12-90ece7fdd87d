package com.embrate.cloud.core.api.service.notification;

/**
 * 
 * <AUTHOR>
 *
 */
public class PushNotificationContent {

	private final String title;

	private final String body;

	private final String imageURL;

	public PushNotificationContent(String title, String body, String imageURL) {
		this.title = title;
		this.body = body;
		this.imageURL = imageURL;
	}

	public String getTitle() {
		return title;
	}

	public String getBody() {
		return body;
	}

	public String getImageURL() {
		return imageURL;
	}

	@Override
	public String toString() {
		return "PushNotificationContent [title=" + title + ", body=" + body
				+ ", imageURL=" + imageURL + "]";
	}

}
