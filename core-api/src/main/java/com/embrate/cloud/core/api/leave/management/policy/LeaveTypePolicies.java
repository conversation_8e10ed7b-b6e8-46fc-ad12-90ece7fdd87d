package com.embrate.cloud.core.api.leave.management.policy;

/**
 * <AUTHOR>
 */

public class LeaveTypePolicies {

    private LeaveGrantPolicy leaveGrantPolicy;

    private LeaveCarryForwardPolicy leaveCarryForwardPolicy;

    private LeaveEncashmentPolicy leaveEncashmentPolicy;

    private LeaveHolidayClubbingPolicy leaveHolidayClubbingPolicy;

    public LeaveTypePolicies() {
    }

    public LeaveTypePolicies(LeaveGrantPolicy leaveGrantPolicy, LeaveCarryForwardPolicy leaveCarryForwardPolicy, LeaveEncashmentPolicy leaveEncashmentPolicy, LeaveHolidayClubbingPolicy leaveHolidayClubbingPolicy) {
        this.leaveGrantPolicy = leaveGrantPolicy;
        this.leaveCarryForwardPolicy = leaveCarryForwardPolicy;
        this.leaveEncashmentPolicy = leaveEncashmentPolicy;
        this.leaveHolidayClubbingPolicy = leaveHolidayClubbingPolicy;
    }

    public LeaveGrantPolicy getLeaveGrantPolicy() {
        return leaveGrantPolicy;
    }

    public void setLeaveGrantPolicy(LeaveGrantPolicy leaveGrantPolicy) {
        this.leaveGrantPolicy = leaveGrantPolicy;
    }

    public LeaveCarryForwardPolicy getLeaveCarryForwardPolicy() {
        return leaveCarryForwardPolicy;
    }

    public void setLeaveCarryForwardPolicy(LeaveCarryForwardPolicy leaveCarryForwardPolicy) {
        this.leaveCarryForwardPolicy = leaveCarryForwardPolicy;
    }

    public LeaveEncashmentPolicy getLeaveEncashmentPolicy() {
        return leaveEncashmentPolicy;
    }

    public void setLeaveEncashmentPolicy(LeaveEncashmentPolicy leaveEncashmentPolicy) {
        this.leaveEncashmentPolicy = leaveEncashmentPolicy;
    }

    public LeaveHolidayClubbingPolicy getLeaveHolidayClubbingPolicy() {
        return leaveHolidayClubbingPolicy;
    }

    public void setLeaveHolidayClubbingPolicy(LeaveHolidayClubbingPolicy leaveHolidayClubbingPolicy) {
        this.leaveHolidayClubbingPolicy = leaveHolidayClubbingPolicy;
    }

    @Override
    public String toString() {
        return "LeaveTypePolicies{" +
                "leaveGrantPolicy=" + leaveGrantPolicy +
                ", leaveCarryForwardPolicy=" + leaveCarryForwardPolicy +
                ", leaveEncashmentPolicy=" + leaveEncashmentPolicy +
                ", leaveHolidayClubbingPolicy=" + leaveHolidayClubbingPolicy +
                '}';
    }
}
