package com.embrate.cloud.core.api.dashboards.admission;

import com.lernen.cloud.core.api.user.Gender;

/**
 * POJO class to hold staff count data by gender from database queries
 * Used for dashboard queries that return staff counts grouped by gender
 * 
 * <AUTHOR>
 */
public class StaffCountByGender {

    private final int instituteId;
    private final Gender gender;
    private final int count;

    public StaffCountByGender(int instituteId, Gender gender, int count) {
        this.instituteId = instituteId;
        this.gender = gender;
        this.count = count;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public Gender getGender() {
        return gender;
    }

    public int getCount() {
        return count;
    }

    @Override
    public String toString() {
        return "StaffCountByGender{" +
                "instituteId=" + instituteId +
                ", gender=" + gender +
                ", count=" + count +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        StaffCountByGender that = (StaffCountByGender) o;

        if (instituteId != that.instituteId) return false;
        if (count != that.count) return false;
        return gender == that.gender;
    }

    @Override
    public int hashCode() {
        int result = instituteId;
        result = 31 * result + (gender != null ? gender.hashCode() : 0);
        result = 31 * result + count;
        return result;
    }
}
