package com.embrate.cloud.core.api.inventory.v2;

import java.util.List;

/**
 * <AUTHOR>
 */
public class NewProductRequestV2 {

    private List<ProductPayload> productPayloadList;


    public NewProductRequestV2() {
    }

    public NewProductRequestV2(List<ProductPayload> productPayloadList) {
        this.productPayloadList = productPayloadList;
    }

    public List<ProductPayload> getProductPayloadList() {
        return productPayloadList;
    }

    public void setProductPayloadList(List<ProductPayload> productPayloadList) {
        this.productPayloadList = productPayloadList;
    }

    @Override
    public String toString() {
        return "NewProductRequestV2{" +
                "productPayloadList=" + productPayloadList +
                '}';
    }
}
