package com.embrate.cloud.core.api.homework;
import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.Set;

/**
 * <AUTHOR>
 *
 */
public enum HomeworkType {
    HOMEWORK, CLASSWORK, HOLIDAY_HOMEWORK, PROJECT, OTHERS;

    public static HomeworkType getHomeworkType(String status) {
        if (StringUtils.isBlank(status)) {
            return null;
        }
        for (HomeworkType homeworkTypeEnum : HomeworkType.values()) {
            if (homeworkTypeEnum.name().equalsIgnoreCase(status)) {
                return homeworkTypeEnum;
            }
        }
        return null;
    }
    public static Set<HomeworkType> getHomeworkTypeSetFromStr(String homeworkTypeStr) {
        final Set<HomeworkType> homeworkTypeSet = new HashSet<>();
        if (StringUtils.isBlank(homeworkTypeStr)) {
            return homeworkTypeSet;
        }
        final String[] homeworkTypeToken= homeworkTypeStr.split(",");

        for (final String homeworkType : homeworkTypeToken) {
            if (StringUtils.isBlank(homeworkType)){
                continue;
            }
            homeworkTypeSet.add(HomeworkType.getHomeworkType(homeworkType.trim()));
        }
        return homeworkTypeSet;
    }

    /**
     * Helper method to compare homework types for sorting in alphabetical order
     */
    public static int compare(HomeworkType homeworkType1, HomeworkType homeworkType2) {

        if (homeworkType1 == null && homeworkType2 == null) return 0;
        if (homeworkType1 == null) return 1;
        if (homeworkType2 == null) return -1;

        // Compare by name to sort in alphabetical order
        return homeworkType1.name().compareTo(homeworkType2.name());
    }
}

