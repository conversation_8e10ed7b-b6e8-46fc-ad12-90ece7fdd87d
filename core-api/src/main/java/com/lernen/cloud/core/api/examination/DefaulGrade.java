package com.lernen.cloud.core.api.examination;

/**
 * Grades should be moved to db
 * 
 * <AUTHOR>
 *
 */
public enum DefaulGrade {

	A_PLUS("A+", 10, 91, 100), A("A", 9, 76, 90), B("B", 8, 61, 75), C("C", 7, 41, 60), D("D", 6, 0, 40), E("E", 5, 0,
			0), F("F", 5, 0, 0);

	private final String displayName;

	private final int points;

	private final int minMarks;

	private final int maxMarks;

	private DefaulGrade(String displayName, int points, int minMarks, int maxMarks) {
		this.displayName = displayName;
		this.points = points;
		this.minMarks = minMarks;
		this.maxMarks = maxMarks;
	}

	public String getDisplayName() {
		return displayName;
	}

	public int getPoints() {
		return points;
	}

	public int getMinMarks() {
		return minMarks;
	}

	public int getMaxMarks() {
		return maxMarks;
	}

	public static DefaulGrade getGrade(double points) {
		for (DefaulGrade defaulGrade : DefaulGrade.values()) {
			if (points >= defaulGrade.getPoints()) {
				return defaulGrade;
			}
		}
		return null;
	}

	public static DefaulGrade getGradeFromMarks(Double marks, Double maxMarks) {
		if (marks == null || maxMarks == null) {
			return null;
		}
		int roundMarks = (int) Math.round(marks * 100 / maxMarks);
		for (DefaulGrade defaulGrade : DefaulGrade.values()) {
			if (roundMarks >= defaulGrade.getMinMarks() && roundMarks <= defaulGrade.getMaxMarks()) {
				return defaulGrade;
			}
		}
		return null;
	}

}
