package com.lernen.cloud.core.api.course;

import java.util.List;
import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class StudentCoursesPayload {

	private UUID studentId;

	private List<UUID> courseIds;

	/**
	 * 
	 */
	public StudentCoursesPayload() {
	}

	/**
	 * @param studentId
	 * @param courseIds
	 */
	public StudentCoursesPayload(UUID studentId, List<UUID> courseIds) {
		this.studentId = studentId;
		this.courseIds = courseIds;
	}

	public UUID getStudentId() {
		return studentId;
	}

	public void setStudentId(UUID studentId) {
		this.studentId = studentId;
	}

	public List<UUID> getCourseIds() {
		return courseIds;
	}

	public void setCourseIds(List<UUID> courseIds) {
		this.courseIds = courseIds;
	}

	@Override
	public String toString() {
		return "StudentCoursesPayload [studentId=" + studentId + ", courseIds=" + courseIds + "]";
	}

}