package com.lernen.cloud.core.api.voicecall.datagen;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 *
 * <AUTHOR>
 *
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class DatagenVoiceTemplateStatusResponse {

	@JsonProperty("status")
	private String status;

	@JsonProperty("code")
	private Integer code;

	@JsonProperty("voiceid")
	private String voiceId;

	@JsonProperty("voice_status")
	private String voiceStatus;

	@JsonProperty("duration")
	private String duration;

	@JsonProperty("ts")
	private String timestampStr;

	@JsonProperty("desc")
	private String description;

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Integer getCode() {
		return code;
	}

	public void setCode(Integer code) {
		this.code = code;
	}

	public String getVoiceId() {
		return voiceId;
	}

	public void setVoiceId(String voiceId) {
		this.voiceId = voiceId;
	}

	public String getVoiceStatus() {
		return voiceStatus;
	}

	public void setVoiceStatus(String voiceStatus) {
		this.voiceStatus = voiceStatus;
	}

	public String getDuration() {
		return duration;
	}

	public void setDuration(String duration) {
		this.duration = duration;
	}

	public String getTimestampStr() {
		return timestampStr;
	}

	public void setTimestampStr(String timestampStr) {
		this.timestampStr = timestampStr;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	@Override
	public String toString() {
		return "DatagenVoiceTemplateStatusResponse{" +
				"status='" + status + '\'' +
				", code=" + code +
				", voiceId='" + voiceId + '\'' +
				", voiceStatus='" + voiceStatus + '\'' +
				", duration='" + duration + '\'' +
				", timestampStr='" + timestampStr + '\'' +
				", description='" + description + '\'' +
				'}';
	}
}
