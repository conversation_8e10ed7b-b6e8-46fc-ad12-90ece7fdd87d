package com.lernen.cloud.core.api.documents;

import java.util.List;
import java.util.UUID;

import org.springframework.web.multipart.MultipartFile;

import com.lernen.cloud.core.api.user.UserType;

public class DocumentPayload {
	
	private int instituteId;
	
	private UserType userType;

	private UUID folderId;
	
	private List<MultipartFile> documents;

	public int getInstituteId() {
		return instituteId;
	}

	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	public UserType getUserType() {
		return userType;
	}

	public void setUserType(UserType userType) {
		this.userType = userType;
	}

	public UUID getFolderId() {
		return folderId;
	}

	public void setFolderId(UUID folderId) {
		this.folderId = folderId;
	}

	public List<MultipartFile> getDocuments() {
		return documents;
	}

	public void setDocuments(List<MultipartFile> documents) {
		this.documents = documents;
	}
}
