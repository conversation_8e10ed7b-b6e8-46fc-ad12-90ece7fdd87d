package com.lernen.cloud.core.api.fees;

import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 */
public class FeeIdFeeHeadDetails {

    private final FeeConfigurationResponse feeConfigurationResponse;

    private final List<FeeHeadAmountDetails> feeHeadAmountDetailsList;

    private final double totalAmount;

    public FeeIdFeeHeadDetails(
            FeeConfigurationResponse feeConfigurationResponse,
            List<FeeHeadAmountDetails> feeHeadAmountDetailsList) {
        this.feeConfigurationResponse = feeConfigurationResponse;
        this.feeHeadAmountDetailsList = feeHeadAmountDetailsList;
        this.totalAmount = computeTotalAmount();
    }

    public static List<FeeIdFeeHeadDetails> sortFeeIdFeeHeadDetails(List<FeeIdFeeHeadDetails> feeIdFeeHeadDetails) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(feeIdFeeHeadDetails)) {
            return feeIdFeeHeadDetails;
        }

        Collections.sort(feeIdFeeHeadDetails, new Comparator<FeeIdFeeHeadDetails>() {
            public int compare(FeeIdFeeHeadDetails o1, FeeIdFeeHeadDetails o2) {
                FeeConfigurationBasicInfo feeConfigurationBasicInfo1 = o1.getFeeConfigurationResponse().getFeeConfigurationBasicInfo();
                FeeConfigurationBasicInfo feeConfigurationBasicInfo2 = o2.getFeeConfigurationResponse().getFeeConfigurationBasicInfo();
                int dueDateCompare = feeConfigurationBasicInfo1.getDueDate() - feeConfigurationBasicInfo2.getDueDate();
                if (dueDateCompare != 0) {
                    return dueDateCompare;
                }

                int endYear1 = feeConfigurationBasicInfo1.getEndMonthYear().getYear();
                int endYear2 = feeConfigurationBasicInfo2.getEndMonthYear().getYear();
                int endYearCompare = endYear1 - endYear2;
                if (endYearCompare != 0) {
                    return endYearCompare;
                }

                if (feeConfigurationBasicInfo1.getEndMonthYear().getMonth() == null ||
                        feeConfigurationBasicInfo2.getEndMonthYear().getMonth() == null) {
                    return endYearCompare;
                }

                return feeConfigurationBasicInfo1.getEndMonthYear().getMonth().getValue()
                        - feeConfigurationBasicInfo2.getEndMonthYear().getMonth().getValue();
            }
        });
        return feeIdFeeHeadDetails;
    }

    private double computeTotalAmount() {
        double sum = 0d;
        if (CollectionUtils.isEmpty(feeHeadAmountDetailsList)) {
            return sum;
        }
        for (final FeeHeadAmountDetails feeHeadAmountDetails : feeHeadAmountDetailsList) {
            sum += feeHeadAmountDetails.getAmount();
        }
        return sum;
    }

    public double getTotalAmount() {
        return totalAmount;
    }

    public FeeConfigurationResponse getFeeConfigurationResponse() {
        return feeConfigurationResponse;
    }

    public List<FeeHeadAmountDetails> getFeeHeadAmountDetailsList() {
        return feeHeadAmountDetailsList;
    }

    @Override
    public String toString() {
        return "FeeIdFeeHeadDetails [feeConfigurationResponse="
                + feeConfigurationResponse + ", feeHeadAmountDetailsList="
                + feeHeadAmountDetailsList + ", totalAmount=" + totalAmount
                + "]";
    }

}
