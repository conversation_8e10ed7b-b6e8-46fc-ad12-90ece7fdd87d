package com.lernen.cloud.core.api.examination.report;

import java.util.List;

/**
 * <AUTHOR>
 */

public class ExamReportResultConfigsPayload {

    private String resultCalculatorId;
    private List<ExamResultComputationGroupPayload> resultComputationGroupList;

    public ExamReportResultConfigsPayload() {
    }

    public String getResultCalculatorId() {
        return resultCalculatorId;
    }

    public void setResultCalculatorId(String resultCalculatorId) {
        this.resultCalculatorId = resultCalculatorId;
    }

    public List<ExamResultComputationGroupPayload> getResultComputationGroupList() {
        return resultComputationGroupList;
    }

    public void setResultComputationGroupList(List<ExamResultComputationGroupPayload> resultComputationGroupList) {
        this.resultComputationGroupList = resultComputationGroupList;
    }

    @Override
    public String toString() {
        return "ExamReportResultConfigsPayload{" +
                "resultCalculatorId='" + resultCalculatorId + '\'' +
                ", resultComputationGroupList=" + resultComputationGroupList +
                '}';
    }
}
