package com.lernen.cloud.core.api.staff;

import com.embrate.cloud.core.api.calendar.holiday.RecurringHoliday;
import com.embrate.cloud.core.api.calendar.holiday.RecurringPattern;

import java.time.DayOfWeek;
import java.util.List;

public class StaffVisitingDays {
    private DayOfWeek visitingDay;

    private List<StaffVisitingHours> visitingHoursList;

    private RecurringPattern recurringPattern;

    public  StaffVisitingDays() {

    }

    public StaffVisitingDays(DayOfWeek visitingDay, List<StaffVisitingHours> visitingHoursList, RecurringPattern recurringPattern) {
        this.visitingDay = visitingDay;
        this.visitingHoursList = visitingHoursList;
        this.recurringPattern = recurringPattern;
    }

    public DayOfWeek getVisitingDay() {
        return visitingDay;
    }

    public void setVisitingDay(DayOfWeek visitingDay) {
        this.visitingDay = visitingDay;
    }

    public List<StaffVisitingHours> getVisitingHoursList() {
        return visitingHoursList;
    }

    public void setVisitingHoursList(List<StaffVisitingHours> visitingHoursList) {
        this.visitingHoursList = visitingHoursList;
    }

    public RecurringPattern getRecurringPattern() {
        return recurringPattern;
    }

    public void setRecurringPattern(RecurringPattern recurringPattern) {
        this.recurringPattern = recurringPattern;
    }

    @Override
    public String toString() {
        return "StaffVisitingDays{" +
                "visitingDay=" + visitingDay +
                ", visitingHourList=" + visitingHoursList +
                ", recurringPattern=" + recurringPattern +
                '}';
    }
}
