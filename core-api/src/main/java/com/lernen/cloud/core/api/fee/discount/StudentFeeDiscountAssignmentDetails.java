package com.lernen.cloud.core.api.fee.discount;

import java.util.List;

import com.lernen.cloud.core.api.student.StudentLite;

/**
 *
 * <AUTHOR>
 *
 */
public class StudentFeeDiscountAssignmentDetails {

	private final StudentLite studentLite;

	private final List<FeeDiscountAssignmentDetails> feeDiscountAssignmentDetailsList;

	public StudentFeeDiscountAssignmentDetails(StudentLite studentLite,
			List<FeeDiscountAssignmentDetails> feeDiscountAssignmentDetailsList) {
		this.studentLite = studentLite;
		this.feeDiscountAssignmentDetailsList = feeDiscountAssignmentDetailsList;
	}

	public StudentLite getStudentLite() {
		return studentLite;
	}

	public List<FeeDiscountAssignmentDetails> getFeeDiscountAssignmentDetailsList() {
		return feeDiscountAssignmentDetailsList;
	}

	@Override
	public String toString() {
		return "StudentFeeDiscountAssignmentDetails [studentLite=" + studentLite
				+ ", feeDiscountAssignmentDetailsList="
				+ feeDiscountAssignmentDetailsList + "]";
	}

}
