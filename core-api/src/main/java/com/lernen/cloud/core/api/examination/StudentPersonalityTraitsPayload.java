/**
 * 
 */
package com.lernen.cloud.core.api.examination;

import com.lernen.cloud.core.api.student.Student;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 *
 */
public class StudentPersonalityTraitsPayload {

	private UUID studentId;
	private UUID personalityTraitId;
	private String remarks;

	public StudentPersonalityTraitsPayload() {
	}

	public StudentPersonalityTraitsPayload(UUID studentId, UUID personalityTraitId, String remarks) {
		this.studentId = studentId;
		this.personalityTraitId = personalityTraitId;
		this.remarks = remarks;
	}

	public UUID getStudentId() {
		return studentId;
	}

	public void setStudentId(UUID studentId) {
		this.studentId = studentId;
	}

	public UUID getPersonalityTraitId() {
		return personalityTraitId;
	}

	public void setPersonalityTraitId(UUID personalityTraitId) {
		this.personalityTraitId = personalityTraitId;
	}

	public String getRemarks() {
		return remarks;
	}

	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	@Override
	public String toString() {
		return "StudentPersonalityTraitsPayload{" +
				"studentId=" + studentId +
				", personalityTraitId=" + personalityTraitId +
				", remarks='" + remarks + '\'' +
				'}';
	}
}
