package com.lernen.cloud.core.api.student;

import com.lernen.cloud.core.api.institute.InstituteHouse;
import com.lernen.cloud.core.api.user.BloodGroup;
import com.lernen.cloud.core.api.user.Gender;
import com.lernen.cloud.core.api.user.UserCategory;

import java.util.List;
import java.util.UUID;

/**
 * Minified version of student object where only relevant information is defined
 * 
 * <AUTHOR>
 *
 */
public class StudentLite {

	private final UUID studentId;

	private final StudentStatus studentStatus;

	private final StudentStatus finalStudentStatus;

	private final String registrationNumber;

	private final String admissionNumber;

	private final String name;
	private final String aadharNumber;
	private final Integer dateOfBirth;

	private final Gender gender;

	private final UserCategory userCategory;

	private final boolean rte;

	private final String mothersName;

	private final String fathersName;

	private final String permanentAddress;

	private final String studentFullAddress;

	private final UUID siblingGroupId;

	private final StudentSessionDataLite studentSessionData;

	private final Double walletAmount;

	private final String primaryContactNumber;

	private UUID instituteHouseId;

	private InstituteHouse instituteHouse;

	private BloodGroup bloodGroup;

	private final boolean hosteller;

	private final List<StudentTaggedDetails> studentTaggedDetailsList;


	public StudentLite(UUID studentId, StudentStatus studentStatus,
					   StudentStatus finalStudentStatus,
					   String registrationNumber, String admissionNumber, String name,
					   Integer dateOfBirth, Gender gender, UserCategory userCategory, boolean rte, String mothersName,
					   String fathersName, String permanentAddress, String studentFullAddress, UUID siblingGroupId, String aadharNumber,
					   StudentSessionDataLite studentSessionData, Double walletAmount, String primaryContactNumber,
					   UUID instituteHouseId, InstituteHouse instituteHouse, BloodGroup bloodGroup, boolean hosteller, List<StudentTaggedDetails> studentTaggedDetailsList) {
		this.studentId = studentId;
		this.studentStatus = studentStatus;
		this.finalStudentStatus = finalStudentStatus;
		this.registrationNumber = registrationNumber;
		this.admissionNumber = admissionNumber;
		this.name = name;
		this.dateOfBirth = dateOfBirth;
		this.gender = gender;
		this.userCategory = userCategory;
		this.rte = rte;
		this.mothersName = mothersName;
		this.fathersName = fathersName;
		this.permanentAddress = permanentAddress;
		this.studentFullAddress = studentFullAddress;
		this.siblingGroupId = siblingGroupId;
		this.aadharNumber = aadharNumber;
		this.studentSessionData = studentSessionData;
		this.walletAmount = walletAmount;
		this.primaryContactNumber = primaryContactNumber;
		this.instituteHouseId = instituteHouseId;
		this.instituteHouse = instituteHouse;
		this.bloodGroup = bloodGroup;
		this.hosteller = hosteller;
		this.studentTaggedDetailsList= studentTaggedDetailsList;
	}

	public UUID getStudentId() {
		return studentId;
	}

	public StudentStatus getStudentStatus() {
		return studentStatus;
	}

	public StudentStatus getFinalStudentStatus() {
		return finalStudentStatus;
	}

	public String getRegistrationNumber() {
		return registrationNumber;
	}

	public String getAdmissionNumber() {
		return admissionNumber;
	}

	public String getName() {
		return name;
	}

	public Integer getDateOfBirth() {
		return dateOfBirth;
	}

	public Gender getGender() {
		return gender;
	}

	public UserCategory getUserCategory() {
		return userCategory;
	}

	public boolean isRte() {
		return rte;
	}

	public String getMothersName() {
		return mothersName;
	}

	public String getFathersName() {
		return fathersName;
	}

	public UUID getSiblingGroupId() {
		return siblingGroupId;
	}

	public String getPermanentAddress() {
		return permanentAddress;
	}

	public String getStudentFullAddress() {
		return studentFullAddress;
	}

	public StudentSessionDataLite getStudentSessionData() {
		return studentSessionData;
	}

	public Double getWalletAmount() {
		return walletAmount;
	}

	public String getPrimaryContactNumber() {
		return primaryContactNumber;
	}

	public InstituteHouse getInstituteHouse() {
		return instituteHouse;
	}

	public void setInstituteHouse(InstituteHouse instituteHouse) {
		this.instituteHouse = instituteHouse;
	}

	public UUID getInstituteHouseId() {
		return instituteHouseId;
	}

	public void setInstituteHouseId(UUID instituteHouseId) {
		this.instituteHouseId = instituteHouseId;
	}

	public BloodGroup getBloodGroup() {
		return bloodGroup;
	}

	public void setBloodGroup(BloodGroup bloodGroup) {
		this.bloodGroup = bloodGroup;
	}

	public String getAadharNumber() {
		return aadharNumber;
	}

	public boolean isHosteller() { return hosteller;}

	public List<StudentTaggedDetails> getStudentTaggedDetailsList() {
		return studentTaggedDetailsList;
	}

	@Override
	public String toString() {
		return "StudentLite{" +
				"studentId=" + studentId +
				", studentStatus=" + studentStatus +
				", finalStudentStatus=" + finalStudentStatus +
				", registrationNumber='" + registrationNumber + '\'' +
				", admissionNumber='" + admissionNumber + '\'' +
				", name='" + name + '\'' +
				", aadharNumber='" + aadharNumber + '\'' +
				", dateOfBirth=" + dateOfBirth +
				", gender=" + gender +
				", userCategory=" + userCategory +
				", rte=" + rte +
				", mothersName='" + mothersName + '\'' +
				", fathersName='" + fathersName + '\'' +
				", permanentAddress='" + permanentAddress + '\'' +
				", studentFullAddress='" + studentFullAddress + '\'' +
				", siblingGroupId=" + siblingGroupId +
				", studentSessionData=" + studentSessionData +
				", walletAmount=" + walletAmount +
				", primaryContactNumber='" + primaryContactNumber + '\'' +
				", instituteHouseId=" + instituteHouseId +
				", instituteHouse=" + instituteHouse +
				", bloodGroup=" + bloodGroup +
				", hosteller=" + hosteller +
				", studentTaggedDetailsList=" + studentTaggedDetailsList +
				'}';
	}
}


