package com.lernen.cloud.core.api.student;

import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 *
 */
public class StudentReligionReport {

	private UUID standardId;

	private Integer totalMale;

	private Integer totalFemale;

	private Map<String, Integer> religionMaleStudentCount;

	private Map<String, Integer> religionFemaleStudentCount;

	public StudentReligionReport(UUID standardId, Integer totalMale, Integer totalFemale, Map<String, Integer> religionMaleStudentCount, Map<String, Integer> religionFemaleStudentCount) {
		this.standardId = standardId;
		this.totalMale = totalMale;
		this.totalFemale = totalFemale;
		this.religionMaleStudentCount = religionMaleStudentCount;
		this.religionFemaleStudentCount = religionFemaleStudentCount;
	}

	public StudentReligionReport() {
	}

	public UUID getStandardId() {
		return standardId;
	}

	public void setStandardId(UUID standardId) {
		this.standardId = standardId;
	}

	public Integer getTotalMale() {
		return totalMale;
	}

	public void setTotalMale(Integer totalMale) {
		this.totalMale = totalMale;
	}

	public Integer getTotalFemale() {
		return totalFemale;
	}

	public void setTotalFemale(Integer totalFemale) {
		this.totalFemale = totalFemale;
	}

	public Map<String, Integer> getReligionMaleStudentCount() {
		return religionMaleStudentCount;
	}

	public void setReligionMaleStudentCount(Map<String, Integer> religionMaleStudentCount) {
		this.religionMaleStudentCount = religionMaleStudentCount;
	}

	public Map<String, Integer> getReligionFemaleStudentCount() {
		return religionFemaleStudentCount;
	}

	public void setReligionFemaleStudentCount(Map<String, Integer> religionFemaleStudentCount) {
		this.religionFemaleStudentCount = religionFemaleStudentCount;
	}

	@Override
	public String toString() {
		return "StudentReligionReport{" +
				"standardId=" + standardId +
				", totalMale=" + totalMale +
				", totalFemale=" + totalFemale +
				", religionMaleStudentCount=" + religionMaleStudentCount +
				", religionFemaleStudentCount=" + religionFemaleStudentCount +
				'}';
	}
}
