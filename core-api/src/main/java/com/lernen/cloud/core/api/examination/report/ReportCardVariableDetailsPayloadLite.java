package com.lernen.cloud.core.api.examination.report;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @created_at 07/07/23 : 12:54
 **/
public class ReportCardVariableDetailsPayloadLite {

    private int instituteId;
    private String reportType;
    private int academicSessionId;
    private Set<ReportCardVariableParameters> reportCardVariableParametersSet;
    private List<StudentReportCardVariableDetailsPayload> studentReportCardVariableDetailsPayload;

    public ReportCardVariableDetailsPayloadLite() {
    }

    public ReportCardVariableDetailsPayloadLite(int instituteId, String reportType, int academicSessionId, Set<ReportCardVariableParameters> reportCardVariableParametersSet, List<StudentReportCardVariableDetailsPayload> studentReportCardVariableDetailsPayload) {
        this.instituteId = instituteId;
        this.reportType = reportType;
        this.academicSessionId = academicSessionId;
        this.reportCardVariableParametersSet = reportCardVariableParametersSet;
        this.studentReportCardVariableDetailsPayload = studentReportCardVariableDetailsPayload;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public void setInstituteId(int instituteId) {
        this.instituteId = instituteId;
    }

    public String getReportType() {
        return reportType;
    }

    public void setReportType(String reportType) {
        this.reportType = reportType;
    }

    public int getAcademicSessionId() {
        return academicSessionId;
    }

    public void setAcademicSessionId(int academicSessionId) {
        this.academicSessionId = academicSessionId;
    }

    public Set<ReportCardVariableParameters> getReportCardVariableParametersSet() {
        return reportCardVariableParametersSet;
    }

    public void setReportCardVariableParametersSet(Set<ReportCardVariableParameters> reportCardVariableParametersSet) {
        this.reportCardVariableParametersSet = reportCardVariableParametersSet;
    }

    public List<StudentReportCardVariableDetailsPayload> getStudentReportCardVariableDetailsPayload() {
        return studentReportCardVariableDetailsPayload;
    }

    public void setStudentReportCardVariableDetailsPayload(List<StudentReportCardVariableDetailsPayload> studentReportCardVariableDetailsPayload) {
        this.studentReportCardVariableDetailsPayload = studentReportCardVariableDetailsPayload;
    }

    @Override
    public String toString() {
        return "ReportCardVariableDetailsPayloadLite{" +
                "instituteId=" + instituteId +
                ", reportType='" + reportType + '\'' +
                ", academicSessionId=" + academicSessionId +
                ", reportCardVariableParametersSet=" + reportCardVariableParametersSet +
                ", studentReportCardVariableDetailsPayload=" + studentReportCardVariableDetailsPayload +
                '}';
    }
}
