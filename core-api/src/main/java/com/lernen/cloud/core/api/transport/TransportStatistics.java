package com.lernen.cloud.core.api.transport;

import java.util.List;

import org.springframework.util.CollectionUtils;

/**
 * 
 * <AUTHOR>
 *
 */
public class TransportStatistics {

	private final int academicSessionId;

	private final int totalTransportStudents;

	private final List<TransportServiceRouteResponse> transportServiceRouteResponses;

	public TransportStatistics(int academicSessionId,
			List<TransportServiceRouteResponse> transportServiceRouteResponses) {
		this.academicSessionId = academicSessionId;
		this.transportServiceRouteResponses = transportServiceRouteResponses;
		this.totalTransportStudents = getTotalStudents();
	}

	private int getTotalStudents() {
		if (CollectionUtils.isEmpty(transportServiceRouteResponses)) {
			return 0;
		}
		int total = 0;
		for (TransportServiceRouteResponse transportServiceRouteResponse : transportServiceRouteResponses) {
			total += transportServiceRouteResponse.getStudentCount();
		}
		return total;
	}

	public int getAcademicSessionId() {
		return academicSessionId;
	}

	public int getTotalTransportStudents() {
		return totalTransportStudents;
	}

	public List<TransportServiceRouteResponse> getTransportServiceRouteResponses() {
		return transportServiceRouteResponses;
	}

	@Override
	public String toString() {
		return "TransportStatistics [academicSessionId=" + academicSessionId + ", totalTransportStudents="
				+ totalTransportStudents + ", transportServiceRouteResponses=" + transportServiceRouteResponses + "]";
	}

}
