package com.lernen.cloud.core.api.fee.discount;

import java.util.Set;
import java.util.UUID;

/**
 *
 * <AUTHOR>
 *
 */
public class BulkStudentDiscountStructureAssignmentPayload {

	private Set<UUID> studentIds;

	private Set<UUID> discountStructureIds;

	public BulkStudentDiscountStructureAssignmentPayload() {
	}

	public BulkStudentDiscountStructureAssignmentPayload(Set<UUID> studentIds,
			Set<UUID> discountStructureIds) {
		this.studentIds = studentIds;
		this.discountStructureIds = discountStructureIds;
	}

	public Set<UUID> getStudentIds() {
		return studentIds;
	}

	public void setStudentIds(Set<UUID> studentIds) {
		this.studentIds = studentIds;
	}

	public Set<UUID> getDiscountStructureIds() {
		return discountStructureIds;
	}

	public void setDiscountStructureIds(Set<UUID> discountStructureIds) {
		this.discountStructureIds = discountStructureIds;
	}

	@Override
	public String toString() {
		return "BulkStudentDiscountStructureAssignmentPayload [studentIds="
				+ studentIds + ", discountStructureIds=" + discountStructureIds
				+ "]";
	}

}
