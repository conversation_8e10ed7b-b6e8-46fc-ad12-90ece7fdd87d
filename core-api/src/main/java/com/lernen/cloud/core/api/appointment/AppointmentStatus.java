package com.lernen.cloud.core.api.appointment;

import com.lernen.cloud.core.api.student.StudentStatus;
import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashSet;
import java.util.Set;

public enum AppointmentStatus {
    RAISED, ACCEPTED, REJECTED, COMPLETED, DELETED;

    public static AppointmentStatus getAppointmentStatus(String status) {
        if (StringUtils.isBlank(status)) {
            return null;
        }
        for (AppointmentStatus statusEnum : AppointmentStatus.values()) {
            if (statusEnum.name().equalsIgnoreCase(status)) {
                return statusEnum;
            }
        }
        return null;
    }

    public static Set<AppointmentStatus> getAppointmentStatusSet(String appointmentStatusCSV) {
        final Set<AppointmentStatus> appointmentStatuses = new LinkedHashSet<>();
        if (StringUtils.isBlank(appointmentStatusCSV)) {
            return appointmentStatuses;
        }

        final String[] statusTokens = appointmentStatusCSV.split(",");

        for (final String status : statusTokens) {
            if (StringUtils.isBlank(status))
            {
                continue;
            }
            appointmentStatuses.add(getAppointmentStatus(status.trim()));
        }
        return appointmentStatuses;
    }
}