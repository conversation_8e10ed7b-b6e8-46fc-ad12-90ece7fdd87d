package com.lernen.cloud.core.api.transport;

import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.core.api.user.UserLite;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class Vehicle {

	private int instituteId;

	private int vehicleId;

	private String vehicleNumber;

	private String vehicleCode;

	private String registrationNumber;

	private VehicleType vehicleType;

	private int capacity;

	private boolean active;

	private String engineNumber;

	private String batteryNumber;

	private String chassisNumber;

	private Integer purchaseDate;

	private String tyreNumber;

	private String manufacturer;

	private double mileage;

	private double bodyCost;

	private String make;

	private String model;

	private Integer year;

	private String financer;

	private double chassisCost;

	private String pucNumber;

	private List<Document<VehicleDocumentType>> vehicleDocuments;

	/**
	 * @param instituteId
	 * @param vehicleId
	 * @param vehicleNumber
	 * @param vehicleCode
	 * @param registrationNumber
	 * @param vehicleType
	 * @param capacity
	 * @param active
	 */
	public Vehicle(int instituteId, int vehicleId, String vehicleNumber, String vehicleCode, String registrationNumber, VehicleType vehicleType, int capacity, boolean active, String engineNumber, String batteryNumber, String chassisNumber, Integer purchaseDate, String tyreNumber, String manufacturer, double mileage, double bodyCost, String make, String model, Integer year, String financer, double chassisCost, String pucNumber, List<Document<VehicleDocumentType>> vehicleDocuments) {
		this.instituteId = instituteId;
		this.vehicleId = vehicleId;
		this.vehicleNumber = vehicleNumber;
		this.vehicleCode = vehicleCode;
		this.registrationNumber = registrationNumber;
		this.vehicleType = vehicleType;
		this.capacity = capacity;
		this.active = active;
		this.engineNumber = engineNumber;
		this.batteryNumber = batteryNumber;
		this.chassisNumber = chassisNumber;
		this.purchaseDate = purchaseDate;
		this.tyreNumber = tyreNumber;
		this.manufacturer = manufacturer;
		this.mileage = mileage;
		this.bodyCost = bodyCost;
		this.make = make;
		this.model = model;
		this.year = year;
		this.financer = financer;
		this.chassisCost = chassisCost;
		this.pucNumber = pucNumber;
		this.vehicleDocuments = vehicleDocuments;
	}

	/**
	 * @return the instituteId
	 */
	public int getInstituteId() {
		return instituteId;
	}

	/**
	 * @param instituteId the instituteId to set
	 */
	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	/**
	 * @return the vehicleId
	 */
	public int getVehicleId() {
		return vehicleId;
	}

	/**
	 * @param vehicleId the vehicleId to set
	 */
	public void setVehicleId(int vehicleId) {
		this.vehicleId = vehicleId;
	}

	/**
	 * @return the vehicleNumber
	 */
	public String getVehicleNumber() {
		return vehicleNumber;
	}

	/**
	 * @param vehicleNumber the vehicleNumber to set
	 */
	public void setVehicleNumber(String vehicleNumber) {
		this.vehicleNumber = vehicleNumber;
	}

	/**
	 * @return the vehicleCode
	 */
	public String getVehicleCode() {
		return vehicleCode;
	}

	/**
	 * @param vehicleCode the vehicleCode to set
	 */
	public void setVehicleCode(String vehicleCode) {
		this.vehicleCode = vehicleCode;
	}

	/**
	 * @return the registrationNumber
	 */
	public String getRegistrationNumber() {
		return registrationNumber;
	}

	/**
	 * @param registrationNumber the registrationNumber to set
	 */
	public void setRegistrationNumber(String registrationNumber) {
		this.registrationNumber = registrationNumber;
	}

	/**
	 * @return the vehicleType
	 */
	public VehicleType getVehicleType() {
		return vehicleType;
	}

	/**
	 * @param vehicleType the vehicleType to set
	 */
	public void setVehicleType(VehicleType vehicleType) {
		this.vehicleType = vehicleType;
	}

	/**
	 * @return the capacity
	 */
	public int getCapacity() {
		return capacity;
	}

	/**
	 * @param capacity the capacity to set
	 */
	public void setCapacity(int capacity) {
		this.capacity = capacity;
	}

	/**
	 * @return the active
	 */
	public boolean isActive() {
		return active;
	}

	/**
	 * @param active the active to set
	 */
	public void setActive(boolean active) {
		this.active = active;
	}

	public String getEngineNumber() { return engineNumber;}

	public void setEngineNumber(String engineNumber) { this.engineNumber = engineNumber;}

	public String getBatteryNumber() { return batteryNumber;}

	public void setBatteryNumber(String batteryNumber) { this.batteryNumber = batteryNumber;}

	public String getChassisNumber() { return chassisNumber;}

	public void setChassisNumber(String chassisNumber) { this.chassisNumber = chassisNumber;}

	public Integer getPurchaseDate() { return purchaseDate;}

	public void setPurchaseDate(Integer purchaseDate) { this.purchaseDate = purchaseDate;}

	public String getTyreNumber() { return tyreNumber;}

	public void setTyreNumber(String tyreNumber) { this.tyreNumber = tyreNumber; }

	public String getManufacturer() { return manufacturer;}

	public void setManufacturer(String manufacturer) { this.manufacturer = manufacturer;}

	public double getMileage() { return mileage;}

	public void setMileage(double mileage) { this.mileage = mileage;}

	public double getBodyCost() { return bodyCost;}

	public void setBodyCost(double bodyCost) { this.bodyCost = bodyCost;}

	public String getMake() { return make;}

	public void setMake(String make) { this.make = make;}

	public String getModel() { return model;}

	public void setModel(String model) { this.model = model;}

	public Integer getYear() { return year;}

	public void setYear(Integer year) { this.year = year;}

	public String getFinancer() { return financer;}

	public void setFinancer(String financer) { this.financer = financer;}

	public double getChassisCost() { return chassisCost;}

	public void setChassisCost(double chassisCost) { this.chassisCost = chassisCost;}

	public String getPucNumber() { return pucNumber;}

	public void setPucNumber(String pucNumber) { this.pucNumber = pucNumber;}

	public List<Document<VehicleDocumentType>> getVehicleDocuments() { return vehicleDocuments;}

	public void setVehicleDocuments(List<Document<VehicleDocumentType>> vehicleDocuments) { this.vehicleDocuments = vehicleDocuments;}

	@Override
	public String toString() {
		return "Vehicle{" +
				"instituteId=" + instituteId +
				", vehicleId=" + vehicleId +
				", vehicleNumber='" + vehicleNumber + '\'' +
				", vehicleCode='" + vehicleCode + '\'' +
				", registrationNumber='" + registrationNumber + '\'' +
				", vehicleType=" + vehicleType +
				", capacity=" + capacity +
				", active=" + active +
				", engineNumber='" + engineNumber + '\'' +
				", batteryNumber='" + batteryNumber + '\'' +
				", chassisNumber='" + chassisNumber + '\'' +
				", purchaseDate=" + purchaseDate +
				", tyreNumber='" + tyreNumber + '\'' +
				", manufacturer='" + manufacturer + '\'' +
				", mileage=" + mileage +
				", bodyCost=" + bodyCost +
				", make='" + make + '\'' +
				", model='" + model + '\'' +
				", year=" + year +
				", financer='" + financer + '\'' +
				", chassisCost=" + chassisCost +
				", pucNumber='" + pucNumber + '\'' +
				", vehicleDocuments=" + vehicleDocuments +
				'}';
	}
}
