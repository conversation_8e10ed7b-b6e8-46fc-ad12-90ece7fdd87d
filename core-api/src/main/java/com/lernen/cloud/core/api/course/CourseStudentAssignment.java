/**
 * 
 */
package com.lernen.cloud.core.api.course;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class CourseStudentAssignment {
	
	private final int instituteId;
	
	private final int academicSessionId;
	
	private final Course course;
	
	private final List<StudentCourseAssignmentDetails> studentCourseAssignmentDetailsList;

	/**
	 * @param instituteId
	 * @param academicSessionId
	 * @param course
	 * @param studentCourseAssignmentDetailsList
	 */
	public CourseStudentAssignment(int instituteId, int academicSessionId, Course course,
			List<StudentCourseAssignmentDetails> studentCourseAssignmentDetailsList) {
		this.instituteId = instituteId;
		this.academicSessionId = academicSessionId;
		this.course = course;
		this.studentCourseAssignmentDetailsList = studentCourseAssignmentDetailsList;
	}

	/**
	 * @return the instituteId
	 */
	public int getInstituteId() {
		return instituteId;
	}

	/**
	 * @return the academicSessionId
	 */
	public int getAcademicSessionId() {
		return academicSessionId;
	}

	/**
	 * @return the course
	 */
	public Course getCourse() {
		return course;
	}

	/**
	 * @return the studentCourseAssignmentDetailsList
	 */
	public List<StudentCourseAssignmentDetails> getStudentCourseAssignmentDetailsList() {
		return studentCourseAssignmentDetailsList;
	}

	@Override
	public String toString() {
		return "CourseStudentAssignment [instituteId=" + instituteId + ", academicSessionId=" + academicSessionId
				+ ", course=" + course + ", studentCourseAssignmentDetailsList=" + studentCourseAssignmentDetailsList
				+ "]";
	}

}
