package com.lernen.cloud.core.api.fees.payment;

import com.lernen.cloud.core.api.common.StringHelper;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.institute.Stream;
import com.lernen.cloud.core.api.student.StudentStatus;
import org.apache.commons.lang3.StringUtils;

import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class StudentFeePaymentAggregatedData
		implements
			Comparable<StudentFeePaymentAggregatedData> {

	private final int instituteId;

	private final UUID studentId;

	private final String registrationNumber;
	private final String admissionNumber;

	private final String studentFullName;

	private final String fatherName;

	private final String primaryContactNumber;
	private final String whatsappNumber;

	private final String fatherContactNumber;

	private final String motherContactNumber;
	
	private final UUID standardId;

	private final String standardName;

	private final Stream stream;

	private final int standardLevel;

	private final String sectionName;

	private final Integer sectionId;

	private final String displayNameWithSection;

	/**
	 * this is final student status
	 */
	private final StudentStatus status;

	private final StudentStatus sessionStatus;
	
	private final double assignedAmount;

	private final double amountCollected;

	/**
	 * instant discount + total assigned discount amount for paid fees
	 */
	private final double givenDiscount;

	private final double remainingDiscountToBeGiven;

	private final double instantDiscountGiven;

	private final double dueAmount;

	private double dueAmountTillToday;

	private double paidFineAmount;
	private double dueFineAmount;

	private final boolean isFeePaymentRestricted;


	/**
	 * @param instituteId
	 * @param studentId
	 * @param admissionNumber
	 * @param studentFullName
	 * @param fatherName
	 * @param primaryContactNumber
	 * @param fatherContactNumber
	 * @param motherContactNumber
	 * @param standardId
	 * @param standardName
	 * @param stream
	 * @param standardLevel
	 * @param sectionName
	 * @param sectionId
	 * @param sessionStatus
	 * @param assignedAmount
	 * @param amountCollected
	 * @param givenDiscount
	 * @param remainingDiscountToBeGiven
	 * @param dueAmount
	 */
	public StudentFeePaymentAggregatedData(int instituteId, UUID studentId, String registrationNumber, String admissionNumber,
										   String studentFullName, String fatherName, String primaryContactNumber, String whatsappNumber, String fatherContactNumber,
										   String motherContactNumber, UUID standardId, String standardName, Stream stream, int standardLevel,
										   String sectionName, Integer sectionId, StudentStatus status, StudentStatus sessionStatus, double assignedAmount, double amountCollected,
										   double givenDiscount, double remainingDiscountToBeGiven, double instantDiscountGiven, double dueAmount, double paidFineAmount,
										   double dueFineAmount, boolean isFeePaymentRestricted) {
		this.instituteId = instituteId;
		this.studentId = studentId;
		this.registrationNumber = registrationNumber;
		this.admissionNumber = admissionNumber;
		this.studentFullName = studentFullName;
		this.fatherName = fatherName;
		this.primaryContactNumber = primaryContactNumber;
		this.whatsappNumber = whatsappNumber;
		this.fatherContactNumber = fatherContactNumber;
		this.motherContactNumber = motherContactNumber;
		this.standardId = standardId;
		this.standardName = standardName;
		this.stream = stream;
		this.standardLevel = standardLevel;
		this.sectionName = sectionName;
		this.displayNameWithSection = getStandardDisplayName(stream, standardName, sectionName);
		this.sectionId = sectionId;
		this.status = status;
		this.sessionStatus = sessionStatus;
		this.assignedAmount = assignedAmount;
		this.amountCollected = amountCollected;
		this.givenDiscount = givenDiscount;
		this.remainingDiscountToBeGiven = remainingDiscountToBeGiven;
		this.instantDiscountGiven = instantDiscountGiven;
		this.dueAmount = dueAmount;
		this.paidFineAmount = paidFineAmount;
		this.dueFineAmount = dueFineAmount;
		this.isFeePaymentRestricted = isFeePaymentRestricted;
	}

	public static String getStandardDisplayName(Stream stream,
												String standardName, String sectionName) {
		String standardDisplayName = stream == null || Stream.NA.equals(stream) ? standardName
				: standardName + " (" + stream.getDisplayName() + ")";

		return StringUtils.isBlank(sectionName) ? standardDisplayName : standardDisplayName + "-" + sectionName;

	}

	/**
	 * @return the instituteId
	 */
	public int getInstituteId() {
		return instituteId;
	}



	/**
	 * @return the studentId
	 */
	public UUID getStudentId() {
		return studentId;
	}


	public String getRegistrationNumber() {
		return registrationNumber;
	}

	/**
	 * @return the admissionNumber
	 */
	public String getAdmissionNumber() {
		return admissionNumber;
	}



	/**
	 * @return the studentFullName
	 */
	public String getStudentFullName() {
		return studentFullName;
	}



	/**
	 * @return the fatherName
	 */
	public String getFatherName() {
		return fatherName;
	}



	/**
	 * @return the primaryContactNumber
	 */
	public String getPrimaryContactNumber() {
		return primaryContactNumber;
	}


	public String getWhatsappNumber() {
		return whatsappNumber;
	}

	/**
	 * @return the fatherContactNumber
	 */
	public String getFatherContactNumber() {
		return fatherContactNumber;
	}



	/**
	 * @return the motherContactNumber
	 */
	public String getMotherContactNumber() {
		return motherContactNumber;
	}



	/**
	 * @return the standardId
	 */
	public UUID getStandardId() {
		return standardId;
	}



	/**
	 * @return the standardName
	 */
	public String getStandardName() {
		return standardName;
	}


	public String getDisplayNameWithSection() {
		return displayNameWithSection;
	}

	/**
	 * @return the stream
	 */
	public Stream getStream() {
		return stream;
	}



	/**
	 * @return the standardLevel
	 */
	public int getStandardLevel() {
		return standardLevel;
	}



	/**
	 * @return the sectionName
	 */
	public String getSectionName() {
		return sectionName;
	}

	/**
	 * @return the sectionId
	 */
	public Integer getSectionId() {
		return sectionId;
	}

	public StudentStatus getStatus() {
		return status;
	}

	/**
	 * @return the status
	 */
	public StudentStatus getSessionStatus() {
		return sessionStatus;
	}



	/**
	 * @return the assignedAmount
	 */
	public double getAssignedAmount() {
		return assignedAmount;
	}



	/**
	 * @return the amountCollected
	 */
	public double getAmountCollected() {
		return amountCollected;
	}



	/**
	 * @return the givenDiscount
	 */
	public double getGivenDiscount() {
		return givenDiscount;
	}



	/**
	 * @return the remainingDiscountToBeGiven
	 */
	public double getRemainingDiscountToBeGiven() {
		return remainingDiscountToBeGiven;
	}



	/**
	 * @return the dueAmount
	 */
	public double getDueAmount() {
		return dueAmount;
	}


	public double getDueAmountTillToday() {
		return dueAmountTillToday;
	}

	public void setDueAmountTillToday(double dueAmountTillToday) {
		this.dueAmountTillToday = dueAmountTillToday;
	}

	public double getInstantDiscountGiven() {
		return instantDiscountGiven;
	}

	public double getPaidFineAmount() {
		return paidFineAmount;
	}

	public void setPaidFineAmount(double paidFineAmount) {
		this.paidFineAmount = paidFineAmount;
	}

	@Override
	public int compareTo(StudentFeePaymentAggregatedData o) {
		if (this.standardLevel < o.standardLevel) {
			return -1;
		}
		if (this.standardLevel > o.standardLevel) {
			return 1;
		}
		int sectionCompare = StringHelper.compareIgnoreCase(this.sectionName,
				o.sectionName);
		if (sectionCompare != 0) {
			return sectionCompare;
		}
		return StringHelper.compareIgnoreCase(this.studentFullName,
				o.studentFullName);
	}

	public double getDueFineAmount() {
		return dueFineAmount;
	}

	public void setDueFineAmount(double dueFineAmount) {
		this.dueFineAmount = dueFineAmount;
	}

	public boolean isFeePaymentRestricted() {
		return isFeePaymentRestricted;
	}

	@Override
	public String toString() {
		return "StudentFeePaymentAggregatedData{" +
				"instituteId=" + instituteId +
				", studentId=" + studentId +
				", registrationNumber='" + registrationNumber + '\'' +
				", admissionNumber='" + admissionNumber + '\'' +
				", studentFullName='" + studentFullName + '\'' +
				", fatherName='" + fatherName + '\'' +
				", primaryContactNumber='" + primaryContactNumber + '\'' +
				", whatsappNumber='" + whatsappNumber + '\'' +
				", fatherContactNumber='" + fatherContactNumber + '\'' +
				", motherContactNumber='" + motherContactNumber + '\'' +
				", standardId=" + standardId +
				", standardName='" + standardName + '\'' +
				", stream=" + stream +
				", standardLevel=" + standardLevel +
				", sectionName='" + sectionName + '\'' +
				", sectionId=" + sectionId +
				", displayNameWithSection='" + displayNameWithSection + '\'' +
				", status=" + status +
				", sessionStatus=" + sessionStatus +
				", assignedAmount=" + assignedAmount +
				", amountCollected=" + amountCollected +
				", givenDiscount=" + givenDiscount +
				", remainingDiscountToBeGiven=" + remainingDiscountToBeGiven +
				", instantDiscountGiven=" + instantDiscountGiven +
				", dueAmount=" + dueAmount +
				", dueAmountTillToday=" + dueAmountTillToday +
				", paidFineAmount=" + paidFineAmount +
				", dueFineAmount=" + dueFineAmount +
				", isFeePaymentRestricted=" + isFeePaymentRestricted +
				'}';
	}
}
