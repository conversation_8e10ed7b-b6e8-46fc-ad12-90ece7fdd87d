/**
 * 
 */
package com.lernen.cloud.core.api.student;

import java.util.UUID;

import com.lernen.cloud.core.api.user.Gender;
import com.lernen.cloud.core.api.user.UserCategory;

/**
 * <AUTHOR>
 *
 */
public class StudentCastReportRow {
	
	private int instituteId;
	
	private Integer studentCount;

	private UserCategory category;
	
	private Boolean speciallyAbled;
	
	private Gender gender;
	
	private UUID standardId;
	
	private String standardName;

	

	/**
	 * @param instituteId
	 * @param studentCount
	 * @param category
	 * @param speciallyAbled
	 * @param gender
	 * @param standardId
	 * @param standardName
	 */
	public StudentCastReportRow(int instituteId, Integer studentCount, UserCategory category, Boolean speciallyAbled,
			Gender gender, UUID standardId, String standardName) {
		this.instituteId = instituteId;
		this.studentCount = studentCount;
		this.category = category;
		this.speciallyAbled = speciallyAbled;
		this.gender = gender;
		this.standardId = standardId;
		this.standardName = standardName;
	}

	/**
	 * 
	 */
	public StudentCastReportRow() {
	}

	public int getInstituteId() {
		return instituteId;
	}

	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	public Integer getStudentCount() {
		return studentCount;
	}

	public void setStudentCount(Integer studentCount) {
		this.studentCount = studentCount;
	}

	public UserCategory getCategory() {
		return category;
	}

	public void setCategory(UserCategory category) {
		this.category = category;
	}

	public Boolean getSpeciallyAbled() {
		return speciallyAbled;
	}

	public void setSpeciallyAbled(Boolean speciallyAbled) {
		this.speciallyAbled = speciallyAbled;
	}

	public Gender getGender() {
		return gender;
	}

	public void setGender(Gender gender) {
		this.gender = gender;
	}

	public UUID getStandardId() {
		return standardId;
	}

	public void setStandardId(UUID standardId) {
		this.standardId = standardId;
	}

	public String getStandardName() {
		return standardName;
	}

	public void setStandardName(String standardName) {
		this.standardName = standardName;
	}


	@Override
	public String toString() {
		return "StudentCastReportRow [instituteId=" + instituteId + ", studentCount=" + studentCount + ", category="
				+ category + ", speciallyAbled=" + speciallyAbled + ", gender=" + gender + ", standardId=" + standardId
				+ ", standardName=" + standardName + "]";
	}
}
