package com.lernen.cloud.core.api.user;

import org.apache.commons.lang3.StringUtils;

/**
 * 
 * <AUTHOR>
 *
 */
public enum BloodGroup {

	O_POSITIVE ("O+"),
	O_NEGATIVE ("O-"),
	A_POSITIVE("A+"),
	A_NEGATIVE("A-"),
	B_POSITIVE("B+"),
	B_NEGATIVE("B-"),
	AB_POSITIVE("AB+"),
	AB_NEGATIVE("AB-");
	
	private String displayName;

	private BloodGroup(String displayName) {
		this.displayName = displayName;
	}

	public String getDisplayName() {
		return displayName;
	}
	
	public static BloodGroup getBloodGroup(String bloodGroup){
		if(StringUtils.isBlank(bloodGroup)){
			return null;
		}
		for(BloodGroup bloodGroupEnum : BloodGroup.values()){
			if(bloodGroupEnum.name().equalsIgnoreCase(bloodGroup)){
				return bloodGroupEnum;
			}
		}
		return null;
	}
	
	
}
