/**
 * 
 */
package com.lernen.cloud.core.api.staff;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 *
 */
public enum AccountType {
	
	SAVINGS, CURRENT, SALARY;
	
	public static AccountType getAccountType(String accountType) {
		if (StringUtils.isBlank(accountType)) {
			return null;
		}
		for (AccountType accountTypeEnum : AccountType.values()) {
			if (accountTypeEnum.name().equalsIgnoreCase(accountType)) {
				return accountTypeEnum;
			}
		}
		return null;
	}

}
