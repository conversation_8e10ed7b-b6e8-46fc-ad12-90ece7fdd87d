package com.lernen.cloud.core.api.fees;

import java.util.List;
import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class FeeAssignmentPermission {

	private UUID feeId;

	List<FeeHeadAssginmentPermission> feeHeadAssginmentPermissions;

	public FeeAssignmentPermission() {
	}

	public FeeAssignmentPermission(UUID feeId, List<FeeHeadAssginmentPermission> feeHeadAssginmentPermissions) {
		this.feeId = feeId;
		this.feeHeadAssginmentPermissions = feeHeadAssginmentPermissions;
	}

	public UUID getFeeId() {
		return feeId;
	}

	public void setFeeId(UUID feeId) {
		this.feeId = feeId;
	}

	public List<FeeHeadAssginmentPermission> getFeeHeadAssginmentPermissions() {
		return feeHeadAssginmentPermissions;
	}

	public void setFeeHeadAssginmentPermissions(List<FeeHeadAssginmentPermission> feeHeadAssginmentPermissions) {
		this.feeHeadAssginmentPermissions = feeHeadAssginmentPermissions;
	}

	@Override
	public String toString() {
		return "FeeAssignmentPermission [feeId=" + feeId + ", feeHeadAssginmentPermissions="
				+ feeHeadAssginmentPermissions + "]";
	}

}
