package com.lernen.cloud.core.api.common;


import org.apache.commons.lang3.StringUtils;

/**
 * 
 * <AUTHOR>
 *
 */
public enum Medium {
	ENGLISH, HINDI;
	public static Medium getMedium(String medium) {
		if (StringUtils.isBlank(medium)) {
			return null;
		}
		for (Medium mediumEnum : Medium.values()) {
			if (mediumEnum.name().equalsIgnoreCase(medium)) {
				return mediumEnum;
			}
		}
		return null;
	}
}
