package com.lernen.cloud.core.api.transport;

import java.util.List;

public class ParentAreaDetailsWithStoppages {
    private TransportParentArea transportParentArea;
    private List<TransportArea> transportAreaList;
    
    public ParentAreaDetailsWithStoppages(TransportParentArea transportParentArea, List<TransportArea> transportAreaList) {
        this.transportParentArea = transportParentArea;
        this.transportAreaList = transportAreaList;
    }

    public ParentAreaDetailsWithStoppages() {

    }

    public void setTransportParentArea(TransportParentArea transportParentArea) {
        this.transportParentArea = transportParentArea;
    }

    public TransportParentArea getTransportParentArea() {
        return transportParentArea;
    }

    public void setTransportAreaList(List<TransportArea> transportAreaList) {
        this.transportAreaList = transportAreaList;
    }

    public  List<TransportArea> getTransportAreaList() {
        return transportAreaList;
    }

    @Override
	public String toString() {
		return "ParentAreaDetailsWithStoppages [transportParentArea=" + transportParentArea + ", transportAreaList=" + transportAreaList +"]";
	}
}
