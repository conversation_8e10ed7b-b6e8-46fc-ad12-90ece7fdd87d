package com.lernen.cloud.core.api.appointment;

import com.embrate.cloud.core.api.leave.management.student.StudentLeaveDetails;
import com.lernen.cloud.core.api.staff.StaffLite;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.user.UserLite;

import java.util.UUID;

public class StudentAppointmentDetails implements Comparable<StudentAppointmentDetails>{

    private final int instituteId;
    private final int academicSessionId;
    private final UUID appointmentId;
    private final StudentLite studentLite;
    private final String guardianName;
    private final String guardianContactInfo;
    private final StaffLite staffLite;
    private final int appointmentDate;
    private final String description;
    private final AppointmentStatus appointmentStatus;
    private final int raisedDate;
    private final Integer acceptedDate;
    private final UserLite acceptedUserLite;
    private final String acceptedReason;
    private final Integer rejectedDate;
    private final UserLite rejectedUserLite;
    private final String rejectedReason;
    private final Integer completedDate;
    private final UserLite completedUserLite;
    private final String outcome;


    public StudentAppointmentDetails(int instituteId, int academicSessionId, UUID appointmentId, StudentLite studentLite,
                                     String guardianName, String guardianContactInfo,
                                     StaffLite staffLite, int appointmentDate,
                                     String description, AppointmentStatus appointmentStatus, int raisedDate, Integer acceptedDate,
                                     UserLite acceptedUserLite, String acceptedReason, Integer rejectedDate, UserLite rejectedUserLite,
                                     String rejectedReason, Integer completedDate, UserLite completedUserLite, String outcome) {
        this.instituteId = instituteId;
        this.academicSessionId = academicSessionId;
        this.appointmentId = appointmentId;
        this.studentLite = studentLite;
        this.guardianName = guardianName;
        this.guardianContactInfo = guardianContactInfo;
        this.staffLite = staffLite;
        this.appointmentDate = appointmentDate;
        this.description = description;
        this.appointmentStatus = appointmentStatus;
        this.raisedDate = raisedDate;
        this.acceptedDate = acceptedDate;
        this.acceptedUserLite = acceptedUserLite;
        this.acceptedReason = acceptedReason;
        this.rejectedDate = rejectedDate;
        this.rejectedUserLite = rejectedUserLite;
        this.rejectedReason = rejectedReason;
        this.completedDate = completedDate;
        this.completedUserLite = completedUserLite;
        this.outcome = outcome;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public int getAcademicSessionId() {
        return academicSessionId;
    }

    public UUID getAppointmentId() {
        return appointmentId;
    }

    public String getGuardianName() {
        return guardianName;
    }

    public String getGuardianContactInfo() {
        return guardianContactInfo;
    }

    public int getAppointmentDate() {
        return appointmentDate;
    }

    public String getDescription() {
        return description;
    }

    public AppointmentStatus getAppointmentStatus() {
        return appointmentStatus;
    }

    public int getRaisedDate() {
        return raisedDate;
    }

    public Integer getAcceptedDate() {
        return acceptedDate;
    }

    public String getAcceptedReason() {
        return acceptedReason;
    }

    public Integer getRejectedDate() {
        return rejectedDate;
    }

    public String getRejectedReason() {
        return rejectedReason;
    }

    public Integer getCompletedDate() {
        return completedDate;
    }

    public String getOutcome() {
        return outcome;
    }

    public StudentLite getStudentLite() {
        return studentLite;
    }

    public StaffLite getStaffLite() {
        return staffLite;
    }

    public UserLite getAcceptedUserLite() {
        return acceptedUserLite;
    }

    public UserLite getRejectedUserLite() {
        return rejectedUserLite;
    }

    public UserLite getCompletedUserLite() {
        return completedUserLite;
    }


    @Override
    public String toString() {
        return "StudentAppointmentDetails{" +
                "instituteId=" + instituteId +
                ", academicSessionId=" + academicSessionId +
                ", appointmentId=" + appointmentId +
                ", studentLite=" + studentLite +
                ", guardianName='" + guardianName + '\'' +
                ", guardianContactInfo='" + guardianContactInfo + '\'' +
                ", staffLite=" + staffLite +
                ", appointmentDate=" + appointmentDate +
                ", description='" + description + '\'' +
                ", appointmentStatus=" + appointmentStatus +
                ", raisedDate=" + raisedDate +
                ", acceptedDate=" + acceptedDate +
                ", acceptedUserLite=" + acceptedUserLite +
                ", acceptedReason='" + acceptedReason + '\'' +
                ", rejectedDate=" + rejectedDate +
                ", rejectedUserLite=" + rejectedUserLite +
                ", rejectedReason='" + rejectedReason + '\'' +
                ", completedDate=" + completedDate +
                ", completedUserLite=" + completedUserLite +
                ", outcome='" + outcome + '\'' +
                '}';
    }

    @Override
    public int compareTo(StudentAppointmentDetails o) {
        int compare = this.appointmentDate - o.appointmentDate;
        if(compare != 0) {
            return compare;
        }
        return this.studentLite.getName().compareToIgnoreCase(o.studentLite.getName());
    }
}