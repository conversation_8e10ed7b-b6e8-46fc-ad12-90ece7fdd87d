package com.lernen.cloud.core.api.attendance.staff;

import com.lernen.cloud.core.api.institute.Time;
import com.lernen.cloud.core.api.staff.StaffTimingDetails;

import java.util.UUID;

/**
 * <AUTHOR>
 */
public class StaffAttendanceTypePayload {

    private UUID staffId;

    private StaffAttendanceType type;

    private Time timeOfAction;

    private Double totalDuration;

    private StaffAttendanceStatus staffAttendanceStatus;

    private StaffTimingDetails staffTimingDetails;

    public StaffAttendanceTypePayload() {
    }

    public StaffAttendanceTypePayload(UUID staffId, StaffAttendanceType type, Time timeOfAction, Double totalDuration, StaffAttendanceStatus staffAttendanceStatus, StaffTimingDetails staffTimingDetails) {
        this.staffId = staffId;
        this.type = type;
        this.timeOfAction = timeOfAction;
        this.totalDuration = totalDuration;
        this.staffAttendanceStatus = staffAttendanceStatus;
        this.staffTimingDetails = staffTimingDetails;
    }

    public UUID getStaffId() {
        return staffId;
    }

    public void setStaffId(UUID staffId) {
        this.staffId = staffId;
    }

    public StaffAttendanceType getType() {
        return type;
    }

    public void setType(StaffAttendanceType type) {
        this.type = type;
    }

    public Time getTimeOfAction() {
        return timeOfAction;
    }

    public void setTimeOfAction(Time timeOfAction) {
        this.timeOfAction = timeOfAction;
    }

    public Double getTotalDuration() {
        return totalDuration;
    }

    public void setTotalDuration(Double totalDuration) {
        this.totalDuration = totalDuration;
    }

    public StaffAttendanceStatus getStaffAttendanceStatus() {
        return staffAttendanceStatus;
    }

    public void setStaffAttendanceStatus(StaffAttendanceStatus staffAttendanceStatus) {
        this.staffAttendanceStatus = staffAttendanceStatus;
    }

    public StaffTimingDetails getStaffTimingDetails() {
        return staffTimingDetails;
    }

    public void setStaffTimingDetails(StaffTimingDetails staffTimingDetails) {
        this.staffTimingDetails = staffTimingDetails;
    }

    @Override
    public String toString() {
        return "StaffAttendanceTypePayload{" +
                "staffId=" + staffId +
                ", type=" + type +
                ", timeOfAction=" + timeOfAction +
                ", totalDuration=" + totalDuration +
                ", staffAttendanceStatus=" + staffAttendanceStatus +
                ", staffTimingDetails=" + staffTimingDetails +
                '}';
    }
}
