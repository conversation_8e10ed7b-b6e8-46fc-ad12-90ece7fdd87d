package com.lernen.cloud.core.api.institute;

import com.embrate.cloud.core.api.institute.AssetContent;
import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.lernen.cloud.core.api.user.Document;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class Institute {

	private int instituteId;

	private UUID instituteUniqueCode;

	private String instituteName;

	private String branchName;

	private String letterHeadLine1;

	private String letterHeadLine2;

	private String addressLine1;

	private String addressLine2;

	private String city;

	private String state;

	private String country;

	private String zipcode;

	private String landmark;

	private String logoUrl;

	private String secondLogoUrl;

	private String email;

	private String phoneNumber;

	private Date createdAt;

	private Date updatedAt;

	private boolean isActive;


	private List<Document<InstituteDocumentType>> instituteDocumentList;

	private Map<InstituteMetadataVariables, String> instituteMetadataVariablesMap;

	private String assetBaseUrl;

	private Map<InstituteDocumentType, AssetContent> instituteDocumentAssetMap;

	public Institute(){

	}

	public Institute(int instituteId, UUID instituteUniqueCode,
					 String instituteName, String branchName, String letterHeadLine1,
					 String letterHeadLine2, String addressLine1, String addressLine2,
					 String city, String state, String country, String zipcode,
					 String landmark, String logoUrl, String secondLogoUrl, String email, String phoneNumber,
					 Date createdAt, Date updatedAt, List<Document<InstituteDocumentType>> instituteDocumentList, Map<InstituteMetadataVariables, String> instituteMetadataVariablesMap, String assetBaseUrl, boolean isActive) {
		this.instituteId = instituteId;
		this.instituteUniqueCode = instituteUniqueCode;
		this.instituteName = instituteName;
		this.branchName = branchName;
		this.letterHeadLine1 = letterHeadLine1;
		this.letterHeadLine2 = letterHeadLine2;
		this.addressLine1 = addressLine1;
		this.addressLine2 = addressLine2;
		this.city = city;
		this.state = state;
		this.country = country;
		this.zipcode = zipcode;
		this.landmark = landmark;
		this.logoUrl = fetchUrl(logoUrl, instituteDocumentList, InstituteDocumentType.INSTITUTE_PRIMARY_LOGO, assetBaseUrl);
		this.secondLogoUrl = fetchUrl(secondLogoUrl, instituteDocumentList, InstituteDocumentType.INSTITUTE_SECONDARY_LOGO, assetBaseUrl);
		this.email = email;
		this.phoneNumber = phoneNumber;
		this.createdAt = createdAt;
		this.updatedAt = updatedAt;
		this.instituteDocumentList = instituteDocumentList;
		this.instituteMetadataVariablesMap = instituteMetadataVariablesMap;
		this.assetBaseUrl = assetBaseUrl;
		this.isActive = isActive;
	}

	public String fetchUrl(String assetUrl, List<Document<InstituteDocumentType>> instituteDocumentList, InstituteDocumentType instituteDocumentType, String assetBaseUrl) {
		if(CollectionUtils.isEmpty(instituteDocumentList)) {
			return assetUrl;
		}

		for (Document instituteDocument : instituteDocumentList) {
			if (instituteDocument.getDocumentType() == instituteDocumentType) {
				return !StringUtils.isBlank(instituteDocument.getS3Path()) ? assetBaseUrl + instituteDocument.getS3Path() : assetUrl;
			}
		}

		return assetUrl;
	}

	public int getInstituteId() {
		return instituteId;
	}

	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	public UUID getInstituteUniqueCode() {
		return instituteUniqueCode;
	}

	public void setInstituteUniqueCode(UUID instituteUniqueCode) {
		this.instituteUniqueCode = instituteUniqueCode;
	}

	public String getInstituteName() {
		return instituteName;
	}

	public void setInstituteName(String instituteName) {
		this.instituteName = instituteName;
	}

	public String getBranchName() {
		return branchName;
	}

	public void setBranchName(String branchName) {
		this.branchName = branchName;
	}

	public String getLetterHeadLine1() {
		return letterHeadLine1;
	}

	public List<Document<InstituteDocumentType>> getInstituteDocuments() {
		return instituteDocumentList;
	}

	public void setLetterHeadLine1(String letterHeadLine1) {
		this.letterHeadLine1 = letterHeadLine1;
	}

	public String getLetterHeadLine2() {
		return letterHeadLine2;
	}

	public void setLetterHeadLine2(String letterHeadLine2) {
		this.letterHeadLine2 = letterHeadLine2;
	}

	public String getAddressLine1() {
		return addressLine1;
	}

	public void setAddressLine1(String addressLine1) {
		this.addressLine1 = addressLine1;
	}

	public String getAddressLine2() {
		return addressLine2;
	}

	public void setAddressLine2(String addressLine2) {
		this.addressLine2 = addressLine2;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public String getZipcode() {
		return zipcode;
	}

	public void setZipcode(String zipcode) {
		this.zipcode = zipcode;
	}

	public String getLandmark() {
		return landmark;
	}

	public void setLandmark(String landmark) {
		this.landmark = landmark;
	}

	public String getLogoUrl() {
		return logoUrl;
	}

	public void setLogoUrl(String logoUrl) {
		this.logoUrl = logoUrl;
	}

	public String getSecondLogoUrl() {
		return secondLogoUrl;
	}

	public void setSecondLogoUrl(String secondLogoUrl) {
		this.secondLogoUrl = secondLogoUrl;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getPhoneNumber() {
		return phoneNumber;
	}

	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}

	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public Date getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	public Map<InstituteMetadataVariables, String> getInstituteMetadataVariablesMap() {
		return instituteMetadataVariablesMap;
	}

	public void setInstituteMetadataVariablesMap(Map<InstituteMetadataVariables, String> instituteMetadataVariablesMap) {
		this.instituteMetadataVariablesMap = instituteMetadataVariablesMap;
	}

	public String getAssetBaseUrl() {
		return assetBaseUrl;
	}

	public void setAssetBaseUrl(String assetBaseUrl) {
		this.assetBaseUrl = assetBaseUrl;
	}

	public List<Document<InstituteDocumentType>> getInstituteDocumentList() {
		return instituteDocumentList;
	}

	public void setInstituteDocumentList(List<Document<InstituteDocumentType>> instituteDocumentList) {
		this.instituteDocumentList = instituteDocumentList;
	}

	public Map<InstituteDocumentType, AssetContent> getInstituteDocumentAssetMap() {
		return instituteDocumentAssetMap;
	}

	public AssetContent getAssetContentByDocumentType(InstituteDocumentType instituteDocumentType) {
		if(instituteDocumentType == null) {
			return null;
		}
		return instituteDocumentAssetMap == null || CollectionUtils.isEmpty(instituteDocumentAssetMap.entrySet())
				? null : instituteDocumentAssetMap.get(instituteDocumentType);
	}

	public void setInstituteDocumentAssetMap(Map<InstituteDocumentType, AssetContent> instituteDocumentAssets) {
		this.instituteDocumentAssetMap = instituteDocumentAssets;
	}

	public boolean getIsActive() {
		return isActive;
	}

	public void setIsActive(boolean isActive) {
		this.isActive = isActive;
	}
	
	@Override
	public String toString() {
		return "Institute{" +
				"instituteId=" + instituteId +
				", instituteUniqueCode=" + instituteUniqueCode +
				", instituteName='" + instituteName + '\'' +
				", branchName='" + branchName + '\'' +
				", letterHeadLine1='" + letterHeadLine1 + '\'' +
				", letterHeadLine2='" + letterHeadLine2 + '\'' +
				", addressLine1='" + addressLine1 + '\'' +
				", addressLine2='" + addressLine2 + '\'' +
				", city='" + city + '\'' +
				", state='" + state + '\'' +
				", country='" + country + '\'' +
				", zipcode='" + zipcode + '\'' +
				", landmark='" + landmark + '\'' +
				", logoUrl='" + logoUrl + '\'' +
				", secondLogoUrl='" + secondLogoUrl + '\'' +
				", email='" + email + '\'' +
				", phoneNumber='" + phoneNumber + '\'' +
				", createdAt=" + createdAt +
				", updatedAt=" + updatedAt +
				", instituteDocumentList="+ instituteDocumentList+
				", instituteMetadataVariablesMap=" + instituteMetadataVariablesMap +
				", assetBaseUrl="+ assetBaseUrl+
				", isActive="+isActive+
				'}';
	}

}
