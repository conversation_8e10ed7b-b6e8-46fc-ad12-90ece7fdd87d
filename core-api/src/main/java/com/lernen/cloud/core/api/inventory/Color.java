package com.lernen.cloud.core.api.inventory;

import java.util.ArrayList;
import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class Color {

	private int colorId;

	private int instituteId;

	private String colorName;

	public Color() {
	}

	public Color(int colorId) {
		this.colorId = colorId;
	}

	public Color(int colorId, int instituteId, String colorName) {
		this.colorId = colorId;
		this.instituteId = instituteId;
		this.colorName = colorName;
	}

	public int getColorId() {
		return colorId;
	}

	public int getInstituteId() {
		return instituteId;
	}

	public String getColorName() {
		return colorName;
	}

	public void setColorId(int colorId) {
		this.colorId = colorId;
	}

	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	public void setColorName(String colorName) {
		this.colorName = colorName;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + colorId;
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Color other = (Color) obj;
		if (colorId != other.colorId)
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "Color [colorId=" + colorId + ", instituteId=" + instituteId + ", colorName=" + colorName + "]";
	}

	public static Color copy(Color color) {
		return new Color(color.getColorId(), color.getInstituteId(), color.getColorName());
	}

	public static List<Color> copy(List<Color> colors) {
		List<Color> copiedList = new ArrayList<>();
		for (Color color : colors) {
			copiedList.add(copy(color));
		}
		return copiedList;
	}

}
