package com.lernen.cloud.core.api.examination;

import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class ExamReportCardMetadata {

	private int instituteId;

	private int academicSessionId;

	private UUID reportCardId;

	private UUID standardId;

	private String reportCardType;

	private String reportCardName;

	public ExamReportCardMetadata() {

	}

	public ExamReportCardMetadata(int instituteId, int academicSessionId, UUID reportCardId,
			UUID standardId, String reportCardType, String reportCardName) {
		this.instituteId = instituteId;
		this.academicSessionId = academicSessionId;
		this.reportCardId = reportCardId;
		this.standardId = standardId;
		this.reportCardType = reportCardType;
		this.reportCardName = reportCardName;
	}

	public int getInstituteId() {
		return instituteId;
	}

	public int getAcademicSessionId() {
		return academicSessionId;
	}

	public UUID getReportCardId() {
		return reportCardId;
	}

	public void setReportCardId(UUID reportCardId) {
		this.reportCardId = reportCardId;
	}

	public UUID getStandardId() {
		return standardId;
	}

	public String getReportCardType() {
		return reportCardType;
	}

	public String getReportCardName() {
		return reportCardName;
	}

	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	public void setAcademicSessionId(int academicSessionId) {
		this.academicSessionId = academicSessionId;
	}

	public void setStandardId(UUID standardId) {
		this.standardId = standardId;
	}

	public void setReportCardType(String reportCardType) {
		this.reportCardType = reportCardType;
	}

	public void setReportCardName(String reportCardName) {
		this.reportCardName = reportCardName;
	}

	@Override
	public String toString() {
		return "ExamReportCardMetadata{" +
				"instituteId=" + instituteId +
				", academicSessionId=" + academicSessionId +
				", reportCardId=" + reportCardId +
				", standardId=" + standardId +
				", reportCardType='" + reportCardType + '\'' +
				", reportCardName='" + reportCardName + '\'' +
				'}';
	}

}
