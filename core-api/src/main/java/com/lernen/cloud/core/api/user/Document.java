package com.lernen.cloud.core.api.user;

import java.util.UUID;

import org.apache.commons.lang3.StringUtils;

import com.lernen.cloud.core.api.common.DocumentType;

/**
 * <AUTHOR>
 *
 */

public class Document<T extends DocumentType> {

	protected T documentType;

	protected String documentName;

	protected UUID documentId;

	protected String s3Path;

	protected String fileExtension;

	protected Integer uploadTime;

	protected Integer renewedDate;

	protected Integer expiryDate;

	protected String remarks;

	protected UUID uploadedByUserId;

	protected String uploadedBy;

	/**
	 * If we have an entry in student document what means the document is collected by institute.
	 * DocumentNotUploaded is for checking if document was uploaded in our portal or not.
	 */
	protected boolean documentNotUploaded;

	public Document(T documentType, String documentName, UUID documentId,
			String fileExtension, Integer uploadTime) {
		this.documentType = documentType;
		this.documentId = documentId;
		if (this.documentType != null
				&& StringUtils.isNotBlank(documentType.getDocumentName())) {
			this.documentName = this.documentType.getDocumentName();
		} else {
			this.documentName = documentName;
		}
		this.documentName = documentName;
		this.fileExtension = fileExtension;
		this.uploadTime = uploadTime;
		if(documentId == null) {
			this.documentNotUploaded = true;
		} else {
			this.documentNotUploaded = false;
		}
	}

	public Document(T documentType, String documentName, UUID documentId,
					String fileExtension, Integer uploadTime, Integer renewedDate, Integer expiryDate, String remarks, UUID uploadedByUserId) {
		this.documentType = documentType;
		this.documentId = documentId;
		if (this.documentType != null
				&& StringUtils.isNotBlank(documentType.getDocumentName())) {
			this.documentName = this.documentType.getDocumentName();
		} else {
			this.documentName = documentName;
		}
		this.documentName = documentName;
		this.fileExtension = fileExtension;
		this.uploadTime = uploadTime;
		if(documentId == null) {
			this.documentNotUploaded = true;
		} else {
			this.documentNotUploaded = false;
		}
		this.renewedDate = renewedDate;
		this.expiryDate = expiryDate;
		this.remarks = remarks;
		this.uploadedByUserId = uploadedByUserId;
	}
	
	public Document(T documentType, UUID documentId,
			String fileExtension, Integer uploadTime) {
		this.documentType = documentType;
		this.documentId = documentId;
		this.fileExtension = fileExtension;
		this.uploadTime = uploadTime;
		if(documentId == null) {
			this.documentNotUploaded = true;
		} else {
			this.documentNotUploaded = false;
		}
	}

	public Document(UUID documentId, String fileExtension, Integer uploadTime) {
		this.documentId = documentId;
		this.fileExtension = fileExtension;
		this.uploadTime = uploadTime;
		if(documentId == null) {
			this.documentNotUploaded = true;
		} else {
			this.documentNotUploaded = false;
		}
	}

	public T getDocumentType() {
		return documentType;
	}

	public String getDocumentTypeDisplayName() {
		return documentType.getDisplayName();
	}

	public void setDocumentType(T documentType) {
		this.documentType = documentType;
	}

	public String getDocumentName() {
		return documentName;
	}

	public UUID getDocumentId() {
		return documentId;
	}

	public String getS3Path() {
		return s3Path;
	}

	public String getFileExtension() {
		return fileExtension;
	}

	public Integer getUploadTime() {
		return uploadTime;
	}

	public void setDocumentName(String documentName) {
		this.documentName = documentName;
	}

	public void setDocumentId(UUID documentId) {
		this.documentId = documentId;
	}

	public void setS3Path(String s3Path) {
		this.s3Path = s3Path;
	}

	public void setFileExtension(String fileExtension) {
		this.fileExtension = fileExtension;
	}

	public void setUploadTime(Integer uploadTime) {
		this.uploadTime = uploadTime;
	}

	public Integer getRenewedDate() { return renewedDate;}

	public void setRenewedDate(Integer renewedDate) { this.renewedDate = renewedDate;}

	public Integer getExpiryDate() { return expiryDate;}

	public void setExpiryDate(Integer expiryDate) { this.expiryDate = expiryDate;}

	public String getRemarks() { return remarks;}

	public void setRemarks(String remarks) { this.remarks = remarks;}

	public boolean isDocumentNotUploaded() {
		return documentNotUploaded;
	}

	public void setDocumentNotUploaded(boolean documentNotUploaded) {
		this.documentNotUploaded = documentNotUploaded;
	}

	public String getUploadedBy() { return uploadedBy;}

	public void setUploadedBy(String uploadedBy) { this.uploadedBy = uploadedBy;}

	public UUID getUploadedByUserId() { return uploadedByUserId;}

	public void setUploadedByUserId(UUID uploadedByUserId) { this.uploadedByUserId = uploadedByUserId;}

	@Override
	public String toString() {
		return "Document{" +
				"documentType=" + documentType +
				", documentName='" + documentName + '\'' +
				", documentId=" + documentId +
				", s3Path='" + s3Path + '\'' +
				", fileExtension='" + fileExtension + '\'' +
				", uploadTime=" + uploadTime +
				", renewedDate=" + renewedDate +
				", expiryDate=" + expiryDate +
				", remarks='" + remarks + '\'' +
				", uploadedByUserId=" + uploadedByUserId +
				", uploadedBy='" + uploadedBy + '\'' +
				", documentNotUploaded=" + documentNotUploaded +
				'}';
	}

}
