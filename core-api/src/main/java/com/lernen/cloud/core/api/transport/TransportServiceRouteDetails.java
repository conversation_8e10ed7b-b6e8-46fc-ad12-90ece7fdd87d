package com.lernen.cloud.core.api.transport;

import com.lernen.cloud.core.api.student.StudentAreaWrapper;
import com.lernen.cloud.core.api.student.StudentLite;

import java.util.List;

/**
 * <AUTHOR>
 */
public class TransportServiceRouteDetails {

    private final TransportServiceRoute transportServiceRoute;

    private final List<StudentAreaWrapper> students;

    public TransportServiceRouteDetails(TransportServiceRoute transportServiceRoute, List<StudentAreaWrapper> students) {
        this.transportServiceRoute = transportServiceRoute;
        this.students = students;
    }

    public TransportServiceRoute getTransportServiceRoute() {
        return transportServiceRoute;
    }

    public List<StudentAreaWrapper> getStudents() {
        return students;
    }

    @Override
    public String toString() {
        return "TransportServiceRouteDetails{" +
                "transportServiceRoute=" + transportServiceRoute +
                ", students=" + students +
                '}';
    }
}
