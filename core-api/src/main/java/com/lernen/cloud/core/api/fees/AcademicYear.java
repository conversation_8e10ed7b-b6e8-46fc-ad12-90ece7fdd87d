package com.lernen.cloud.core.api.fees;

import org.apache.commons.lang3.StringUtils;
/**
 * 
 * <AUTHOR>
 *
 */
public enum AcademicYear {

	_2010_11("2010-11", 2010, 2011),
	_2011_12("2011-12", 2011, 2012),
	_2012_13("2012-13", 2012, 2013),
	_2013_14("2013-14", 2013, 2014),
	_2014_15("2014-15", 2014, 2015),
	_2015_16("2015-16", 2015, 2016),
	_2016_17("2016-17", 2016, 2017),
	_2017_18("2017-18", 2017, 2018),
	_2018_19("2018-19", 2018, 2019),
	_2019_20("2019-20", 2019, 2020),
	_2020_21("2020-21", 2020, 2021),
	_2021_22("2021-22", 2021, 2022),
	_2022_23("2022-23", 2022, 2023),
	_2023_24("2023-24", 2023, 2024),
	_2024_25("2024-25", 2024, 2025),
	_2025_26("2025-26", 2025, 2026),
	_2026_27("2026-27", 2026, 2027),
	_2027_28("2022-23", 2027, 2028),
	_2028_29("2023-24", 2028, 2029),
	_2029_30("2024-25", 2029, 2030),
	_2030_31("2025-26", 2030, 2031),
	_2031_32("2026-27", 2031, 2032);
	
	
	private String displayName;
	
	private int startYear;
	
	private int endYear;
	
	private AcademicYear(String displayName, int startYear, int endYear) {
		this.displayName = displayName;
		this.startYear = startYear;
		this.endYear = endYear;
	}

	public String getDisplayName() {
		return displayName;
	}

	public int getStartYear() {
		return startYear;
	}

	public int getEndYear() {
		return endYear;
	}

	public static AcademicYear getAcademicYear(String academicYear){
		if(StringUtils.isBlank(academicYear)){
			return null;
		}
		for(AcademicYear academicYearEnum : AcademicYear.values()){
			if(academicYearEnum.name().equalsIgnoreCase(academicYear)){
				return academicYearEnum;
			}
		}
		return null;
	}
}
