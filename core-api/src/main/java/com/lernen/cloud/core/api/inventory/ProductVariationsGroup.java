package com.lernen.cloud.core.api.inventory;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.user.Gender;

/**
 * 
 * <AUTHOR>
 *
 */
public class ProductVariationsGroup {

	private String name;

	private int categoryId;

	private UUID brandId;

	private Set<String> sizes;

	private Set<Color> colors;

	private Set<UserGroup> userGroups;

	private Set<Gender> genders;

	private String description;

	public ProductVariationsGroup() {
	}

	public ProductVariationsGroup(String name, int categoryId, UUID brandId) {
		this.name = name;
		this.categoryId = categoryId;
		this.brandId = brandId;
		sizes = new HashSet<>();
		colors = new HashSet<>();
		userGroups = new HashSet<>();
		genders = new HashSet<>();
	}

	public ProductVariationsGroup(String name, int categoryId, UUID brandId, Set<String> sizes, Set<Color> colors,
			Set<UserGroup> userGroups, Set<Gender> genders, String description) {
		this.name = name;
		this.categoryId = categoryId;
		this.brandId = brandId;
		this.sizes = sizes;
		this.colors = colors;
		this.userGroups = userGroups;
		this.genders = genders;
		this.description = description;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public int getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(int categoryId) {
		this.categoryId = categoryId;
	}

	public UUID getBrandId() {
		return brandId;
	}

	public void setBrandId(UUID brandId) {
		this.brandId = brandId;
	}

	public Set<String> getSizes() {
		return sizes;
	}

	public void setSizes(Set<String> sizes) {
		this.sizes = sizes;
	}

	public Set<Color> getColors() {
		return colors;
	}

	public void setColors(Set<Color> colors) {
		this.colors = colors;
	}

	public Set<UserGroup> getUserGroups() {
		return userGroups;
	}

	public void setUserGroups(Set<UserGroup> userGroups) {
		this.userGroups = userGroups;
	}

	public Set<Gender> getGenders() {
		return genders;
	}

	public void setGenders(Set<Gender> genders) {
		this.genders = genders;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public static ProductVariationsGroup copy(ProductVariationsGroup productVariationsGroup) {
		return new ProductVariationsGroup(productVariationsGroup.getName(), productVariationsGroup.getCategoryId(),
				productVariationsGroup.getBrandId(),
				productVariationsGroup.getSizes() != null ? new HashSet<>(productVariationsGroup.getSizes()) : null,
				productVariationsGroup.getColors() != null
						? new HashSet<>(Color.copy(new ArrayList<>(productVariationsGroup.getColors()))) : null,
				productVariationsGroup.getUserGroups() != null
						? new HashSet<UserGroup>(productVariationsGroup.getUserGroups()) : null,
				productVariationsGroup.getGenders() != null ? new HashSet<>(productVariationsGroup.getGenders()) : null,
				productVariationsGroup.getDescription());
	}

	public static void format(ProductVariationsGroup productVariationsGroup) {
		productVariationsGroup.setName(productVariationsGroup.getName().trim());
		if (StringUtils.isNotBlank((productVariationsGroup.getDescription()))) {
			productVariationsGroup.setDescription(productVariationsGroup.getDescription().trim());
		}

		if (!CollectionUtils.isEmpty(productVariationsGroup.getSizes())) {
			Set<String> sizes = new HashSet<String>();
			for (String size : productVariationsGroup.getSizes()) {
				sizes.add(size.toUpperCase().trim());
			}
			productVariationsGroup.setSizes(sizes);
		}
	}

	@Override
	public String toString() {
		return "ProductVariationsGroup [name=" + name + ", categoryId=" + categoryId + ", brandId=" + brandId
				+ ", sizes=" + sizes + ", colors=" + colors + ", userGroups=" + userGroups + ", genders=" + genders
				+ ", description=" + description + "]";
	}

}
