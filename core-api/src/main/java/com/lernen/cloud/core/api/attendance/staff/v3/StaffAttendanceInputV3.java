package com.lernen.cloud.core.api.attendance.staff.v3;

import com.lernen.cloud.core.api.attendance.staff.v2.StaffAttendanceInput;
import com.lernen.cloud.core.api.attendance.staff.v2.StaffTimeDuration;
import com.lernen.cloud.core.api.institute.Time;

import java.util.List;
import java.util.UUID;

public class StaffAttendanceInputV3 {
    private UUID staffId;

    private List<StaffTimeDuration> staffTimeDurationList;

    public StaffAttendanceInputV3() {
    }

    public StaffAttendanceInputV3(UUID staffId, List<StaffTimeDuration> staffTimeDurationList) {
        this.staffId = staffId;
        this.staffTimeDurationList = staffTimeDurationList;
    }

    public UUID getStaffId() {
        return staffId;
    }

    public void setStaffId(UUID staffId) {
        this.staffId = staffId;
    }

    public List<StaffTimeDuration> getStaffTimeDurationList() {
        return staffTimeDurationList;
    }

    public void setStaffTimeDurationList(List<StaffTimeDuration> staffTimeDurationList) {
        this.staffTimeDurationList = staffTimeDurationList;
    }

    @Override
    public String toString() {
        return "StaffAttendanceInputV3{" +
                "staffId=" + staffId +
                ", staffTimeDurationList=" + staffTimeDurationList +
                '}';
    }
}
