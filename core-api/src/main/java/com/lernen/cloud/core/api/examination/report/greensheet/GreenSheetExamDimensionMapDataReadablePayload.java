package com.lernen.cloud.core.api.examination.report.greensheet;

/**
 * 
 * <AUTHOR>
 *
 */
public class GreenSheetExamDimensionMapDataReadablePayload implements IGreenSheetExamDimensionMapData{

	private String examName;

	private String courseName;

	private String dimensionName;

	private int fractionNumerator;

	private int fractionDenominator;
	
	
	
	public GreenSheetExamDimensionMapDataReadablePayload() {
	}

	public GreenSheetExamDimensionMapDataReadablePayload(String examName,
			String courseName, String dimensionName, int fractionNumerator,
			int fractionDenominator) {
		this.examName = examName;
		this.courseName = courseName;
		this.dimensionName = dimensionName;
		this.fractionNumerator = fractionNumerator;
		this.fractionDenominator = fractionDenominator;
	}

	public String getExamName() {
		return examName;
	}

	public void setExamName(String examName) {
		this.examName = examName;
	}

	public String getCourseName() {
		return courseName;
	}

	public void setCourseName(String courseName) {
		this.courseName = courseName;
	}

	public String getDimensionName() {
		return dimensionName;
	}

	public void setDimensionName(String dimensionName) {
		this.dimensionName = dimensionName;
	}

	public int getFractionNumerator() {
		return fractionNumerator;
	}

	public void setFractionNumerator(int fractionNumerator) {
		this.fractionNumerator = fractionNumerator;
	}

	public int getFractionDenominator() {
		return fractionDenominator;
	}

	public void setFractionDenominator(int fractionDenominator) {
		this.fractionDenominator = fractionDenominator;
	}

}
