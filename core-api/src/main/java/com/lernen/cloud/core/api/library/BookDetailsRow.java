package com.lernen.cloud.core.api.library;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.user.Document;

public class BookDetailsRow {
    
    private final UUID bookId;

	private final int instituteId;

	private final Genre genre;

	private final String bookTitle;

	private final String bookNo;

	private final Author author;

	private final String isbn;

	private final Publication publication;

	private final Publisher publisher;

	private final String edition;

	private final int noOfCopies;

	private final int publishYear;

	private final String language;

	private final List<Document<BookDocumentType>> bookDocument;

	private final Document<BookDocumentType> coverImage;

	private final String typeOfBinding;

	private final int numberOfPages;

    private final IndividualBookDetails individualBookDetail;

    private final String tags;

	private final Integer addedAt;

	private final Integer updatedAt;

	private byte[] thumbnail;

    public BookDetailsRow(UUID bookId, int instituteId, Genre genre, String bookTitle, String bookNo, Author author,
            String isbn, Publication publication, Publisher publisher, String edition, int noOfCopies, int publishYear,
            String language, List<Document<BookDocumentType>> bookDocument,
            String typeOfBinding, int numberOfPages, IndividualBookDetails individualBookDetail, String tags,
            Integer addedAt, Integer updatedAt) {
        this.bookId = bookId;
        this.instituteId = instituteId;
        this.genre = genre;
        this.bookTitle = bookTitle;
        this.bookNo = bookNo;
        this.author = author;
        this.isbn = isbn;
        this.publication = publication;
        this.publisher = publisher;
        this.edition = edition;
        this.noOfCopies = noOfCopies;
        this.publishYear = publishYear;
        this.language = language;
        this.bookDocument = bookDocument;
        this.coverImage = getImageDocument();
        this.typeOfBinding = typeOfBinding;
        this.numberOfPages = numberOfPages;
        this.individualBookDetail = individualBookDetail;
        this.tags = tags;
        this.addedAt = addedAt;
        this.updatedAt = updatedAt;
    }

    public UUID getBookId() {
        return bookId;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public Genre getGenre() {
        return genre;
    }

    public String getBookTitle() {
        return bookTitle;
    }

    public String getBookNo() {
        return bookNo;
    }

    public Author getAuthor() {
        return author;
    }

    public String getIsbn() {
        return isbn;
    }

    public Publication getPublication() {
        return publication;
    }

    public Publisher getPublisher() {
        return publisher;
    }

    public String getEdition() {
        return edition;
    }


    public int getNoOfCopies() {
        return noOfCopies;
    }

    public int getPublishYear() {
        return publishYear;
    }

    public String getLanguage() {
        return language;
    }

    public List<Document<BookDocumentType>> getBookDocument() {
        return bookDocument;
    }

    private Document<BookDocumentType> getImageDocument() {
		if (CollectionUtils.isEmpty(bookDocument)) {
			return null;
		}
		for (Document<BookDocumentType> bookDocument : bookDocument) {
			if (bookDocument
					.getDocumentType() == BookDocumentType.BOOK_COVER_IMAGE) {
				return bookDocument;
			}
		}
		return null;
	}

    public String getTypeOfBinding() {
        return typeOfBinding;
    }

    public int getNumberOfPages() {
        return numberOfPages;
    }


    public IndividualBookDetails getIndividualBookDetail() {
        return individualBookDetail;
    }

    public String getTags() {
        return tags;
    }

    public Integer getAddedAt() {
        return addedAt;
    }

    public Integer getUpdatedAt() {
        return updatedAt;
    }

    public byte[] getThumbnail() {
        return thumbnail;
    }

    public void setThumbnail(byte[] thumbnail) {
        this.thumbnail = thumbnail;
    }

    @Override
    public String toString() {
        return "BookDetailRow[bookId=" + bookId + ", instituteId=" + instituteId + ", genre=" + genre + ", bookTitle="
                + bookTitle + ", bookNo=" + bookNo + ", author=" + author + ", isbn=" + isbn + ", publication="
                + publication + ", publisher=" + publisher + ", edition=" + edition + ", noOfCopies=" + noOfCopies
                + ", publishYear=" + publishYear + ", language=" + language + ", bookDocument=" + bookDocument
                + ", coverImage=" + coverImage + ", typeOfBinding=" + typeOfBinding + ", numberOfPages=" + numberOfPages
                + ", individualBookDetail=" + individualBookDetail + ", tags=" + tags + ", addedAt=" + addedAt
                + ", updatedAt=" + updatedAt + ", thumbnail=" + Arrays.toString(thumbnail) + "]";
    }

}