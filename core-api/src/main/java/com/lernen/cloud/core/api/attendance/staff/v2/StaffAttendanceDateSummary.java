package com.lernen.cloud.core.api.attendance.staff.v2;

public class StaffAttendanceDateSummary {

    private final Integer attendanceDate;

    private final Double totalDurationInSec;

    private final FinalStaffAttendanceStatus finalStaffAttendanceStatus;

    private final StaffAttendanceDayMetadata staffAttendanceDayMetadata;

    public StaffAttendanceDateSummary(Integer attendanceDate, Double totalDurationInSec, FinalStaffAttendanceStatus finalStaffAttendanceStatus, StaffAttendanceDayMetadata staffAttendanceDayMetadata) {
        this.attendanceDate = attendanceDate;
        this.totalDurationInSec = totalDurationInSec;
        this.finalStaffAttendanceStatus = finalStaffAttendanceStatus;
        this.staffAttendanceDayMetadata = staffAttendanceDayMetadata;
    }

    public Integer getAttendanceDate() {
        return attendanceDate;
    }

    public Double getTotalDurationInSec() {
        return totalDurationInSec;
    }

    public FinalStaffAttendanceStatus getFinalStaffAttendanceStatus() {
        return finalStaffAttendanceStatus;
    }

    public StaffAttendanceDayMetadata getStaffAttendanceDayMetadata() {
        return staffAttendanceDayMetadata;
    }

    @Override
    public String toString() {
        return "StaffAttendanceDateSummary{" +
                "attendanceDate=" + attendanceDate +
                ", totalDurationInSec=" + totalDurationInSec +
                ", finalStaffAttendanceStatus=" + finalStaffAttendanceStatus +
                ", staffAttendanceDayMetadata=" + staffAttendanceDayMetadata +
                '}';
    }
}
