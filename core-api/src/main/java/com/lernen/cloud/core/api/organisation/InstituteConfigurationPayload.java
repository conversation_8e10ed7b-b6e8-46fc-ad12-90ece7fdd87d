package com.lernen.cloud.core.api.organisation;

import com.embrate.cloud.core.api.onboarding.institute.setup.InstituteSetupInput;

import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class InstituteConfigurationPayload {

	private String organisationName;

	private List<InstituteSetupInput> instituteSetupInputList;


	public String getOrganisationName() {
		return organisationName;
	}

	public void setOrganisationName(String organisationName) {
		this.organisationName = organisationName;
	}

	public List<InstituteSetupInput> getInstituteSetupInputList() {
		return instituteSetupInputList;
	}

	public void setInstituteSetupInputList(List<InstituteSetupInput> instituteSetupInputList) {
		this.instituteSetupInputList = instituteSetupInputList;
	}

	@Override
	public String toString() {
		return "OrganisationPayload{" +
				"organisationName='" + organisationName + '\'' +
				", instituteSetupInputList=" + instituteSetupInputList +
				'}';
	}
}
