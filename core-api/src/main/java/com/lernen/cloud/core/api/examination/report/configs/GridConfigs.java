package com.lernen.cloud.core.api.examination.report.configs;

/**
 * <AUTHOR>
 */

public class GridConfigs {

    private final GridHeaderConfigs gridHeaderConfigs;

    private final GridTotalMarksRowConfigs gridTotalMarksRowConfigs;

    private String maxMarksColorHexCode;

    private final boolean showPercentSymbolInGrid;

    public GridConfigs(GridHeaderConfigs gridHeaderConfigs, GridTotalMarksRowConfigs gridTotalMarksRowConfigs) {
        this.gridHeaderConfigs = gridHeaderConfigs;
        this.gridTotalMarksRowConfigs = gridTotalMarksRowConfigs;
        this.showPercentSymbolInGrid = true;
    }

    public GridConfigs(GridHeaderConfigs gridHeaderConfigs, GridTotalMarksRowConfigs gridTotalMarksRowConfigs, boolean showPercentSymbolInGrid) {
        this.gridHeaderConfigs = gridHeaderConfigs;
        this.gridTotalMarksRowConfigs = gridTotalMarksRowConfigs;
        this.showPercentSymbolInGrid = showPercentSymbolInGrid;
    }

    public GridTotalMarksRowConfigs getGridTotalMarksRowConfigs() {
        return gridTotalMarksRowConfigs;
    }

    public GridHeaderConfigs getGridHeaderConfigs() {
        return gridHeaderConfigs;
    }

    public String getMaxMarksColorHexCode() {
        return maxMarksColorHexCode;
    }

    public void setMaxMarksColorHexCode(String maxMarksColorHexCode) {
        this.maxMarksColorHexCode = maxMarksColorHexCode;
    }

    public boolean isShowPercentSymbolInGrid() {
        return showPercentSymbolInGrid;
    }

    public static GridConfigs forSkipTotalRow(){
        return new GridConfigs(new GridHeaderConfigs(false), GridTotalMarksRowConfigs.skipTotalRow());
    }

    public static GridConfigs forOnlyObtainedTotalRow(){
        return new GridConfigs(new GridHeaderConfigs(false), GridTotalMarksRowConfigs.onlyObtainedTotalRow());
    }

    @Override
    public String toString() {
        return "GridConfigs [gridHeaderConfigs=" + gridHeaderConfigs + ", gridTotalMarksRowConfigs="
                + gridTotalMarksRowConfigs + ", maxMarksColorHexCode=" + maxMarksColorHexCode
                + ", showPercentSymbolInGrid=" + showPercentSymbolInGrid + "]";
    }
}
