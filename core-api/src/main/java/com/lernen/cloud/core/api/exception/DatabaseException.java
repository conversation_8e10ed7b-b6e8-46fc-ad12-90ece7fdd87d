package com.lernen.cloud.core.api.exception;

import javax.ws.rs.WebApplicationException;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

public class DatabaseException extends WebApplicationException {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1084864408111457602L;
	public DatabaseException(ErrorResponse errorResponse) {
		super(Response.status(Response.Status.fromStatusCode(errorResponse.getStatusCode())).entity(errorResponse)
				.type(MediaType.APPLICATION_JSON).build());
	}
}
