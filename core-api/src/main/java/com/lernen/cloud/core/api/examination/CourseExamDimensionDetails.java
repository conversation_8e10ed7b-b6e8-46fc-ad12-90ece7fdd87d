package com.lernen.cloud.core.api.examination;

import com.lernen.cloud.core.api.course.Course;
import org.springframework.util.StopWatch;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @created_at 29/02/24 : 14:17
 **/
public class CourseExamDimensionDetails {

    private final Course course;

    private final boolean isCoscholasticGradingEnabled;

    private final boolean isScholasticGradingEnabled;

    private final List<ExamDimensionData> examDimensionDataList;

    public CourseExamDimensionDetails(Course course, boolean isCoscholasticGradingEnabled, boolean isScholasticGradingEnabled, List<ExamDimensionData> examDimensionDataList) {
        this.course = course;
        this.isCoscholasticGradingEnabled = isCoscholasticGradingEnabled;
        this.isScholasticGradingEnabled = isScholasticGradingEnabled;
        this.examDimensionDataList = examDimensionDataList;
    }

    public Course getCourse() {
        return course;
    }

    public boolean isCoscholasticGradingEnabled() {
        return isCoscholasticGradingEnabled;
    }

    public boolean isScholasticGradingEnabled() {
        return isScholasticGradingEnabled;
    }

    public List<ExamDimensionData> getExamDimensionDataList() {
        return examDimensionDataList;
    }

    @Override
    public String toString() {
        return "CourseExamDimensionDetails{" +
                "course=" + course +
                ", isCoscholasticGradingEnabled=" + isCoscholasticGradingEnabled +
                ", isScholasticGradingEnabled=" + isScholasticGradingEnabled +
                ", examDimensionDataList=" + examDimensionDataList +
                '}';
    }

    public static void sortCourseExamDimensionDetailsBySequence(List<CourseExamDimensionDetails> courseExamDimensionDetailsList) {

        if(org.springframework.util.CollectionUtils.isEmpty(courseExamDimensionDetailsList)) {
            return;
        }
        Collections.sort(courseExamDimensionDetailsList, new Comparator<CourseExamDimensionDetails>() {
            @Override
            public int compare(CourseExamDimensionDetails e1, CourseExamDimensionDetails e2) {
                return e1.getCourse().compareTo(e2.getCourse());
            }
        });
    }
}
