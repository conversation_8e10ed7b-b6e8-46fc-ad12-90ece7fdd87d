package com.lernen.cloud.core.api.library;

import java.util.UUID;

public class LibraryType {
    
    private UUID libraryTypeId;
    private String libraryName;

    public LibraryType(){
        
    }

    public LibraryType( UUID libraryTypeId, String libraryName) {
        this.libraryTypeId = libraryTypeId;
        this.libraryName = libraryName;
    }

    public UUID getLibraryTypeId() {
        return libraryTypeId;
    }


    public void setLibraryTypeId(UUID libraryTypeId) {
        this.libraryTypeId = libraryTypeId;
    }


    public String getLibraryName() {
        return libraryName;
    }


    public void setLibraryName(String libraryName) {
        this.libraryName = libraryName;
    }


    @Override
    public String toString() {
        return "LibraryType [libraryTypeId=" + libraryTypeId + ", libraryName="
                + libraryName + "]";
    }

}
