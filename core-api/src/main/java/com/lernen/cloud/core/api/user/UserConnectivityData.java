package com.lernen.cloud.core.api.user;

/**
 * <AUTHOR>
 */

public class UserConnectivityData {

    private String platform;

    private Integer lastLogin;

    private String lastLoginVersion;

    private Integer lastAccess;

    private String manufacturer;

    private String model;

    private String sdkVersion;

    private String versionRelease;

    public UserConnectivityData() {
    }

    public UserConnectivityData(String platform, Integer lastLogin,String lastLoginVersion, Integer lastAccess, String manufacturer, String model, String sdkVersion, String versionRelease) {
        this.platform = platform;
        this.lastLogin = lastLogin;
        this.lastLoginVersion = lastLoginVersion;
        this.lastAccess = lastAccess;
        this.manufacturer = manufacturer;
        this.model = model;
        this.sdkVersion = sdkVersion;
        this.versionRelease = versionRelease;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public Integer getLastLogin() {
        return lastLogin;
    }

    public void setLastLogin(Integer lastLogin) {
        this.lastLogin = lastLogin;
    }

    public String getLastLoginVersion() {
        return lastLoginVersion;
    }

    public void setLastLoginVersion(String lastLoginVersion) {
        this.lastLoginVersion = lastLoginVersion;
    }

    public Integer getLastAccess() {
        return lastAccess;
    }

    public void setLastAccess(Integer lastAccess) {
        this.lastAccess = lastAccess;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getSdkVersion() {
        return sdkVersion;
    }

    public void setSdkVersion(String sdkVersion) {
        this.sdkVersion = sdkVersion;
    }

    public String getVersionRelease() {
        return versionRelease;
    }

    public void setVersionRelease(String versionRelease) {
        this.versionRelease = versionRelease;
    }

    @Override
    public String toString() {
        return "UserConnectivityData{" +
                "platform='" + platform + '\'' +
                ", lastLogin=" + lastLogin +
                ", lastLoginVersion='" + lastLoginVersion + '\'' +
                ", lastAccess=" + lastAccess +
                ", manufacturer='" + manufacturer + '\'' +
                ", model='" + model + '\'' +
                ", sdkVersion='" + sdkVersion + '\'' +
                ", versionRelease='" + versionRelease + '\'' +
                '}';
    }

}
