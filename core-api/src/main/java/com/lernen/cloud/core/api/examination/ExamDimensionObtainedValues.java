package com.lernen.cloud.core.api.examination;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;

/**
 *
 * <AUTHOR>
 *
 */
public class ExamDimensionObtainedValues extends ExamDimensionValues {

	public static final double PASSING_THRESHOLD = 0.33d;
	public static final int DEFAULT_MAX_STARS = 5;

	private Double obtainedMarks;

	private Double graceMarks;

	private ExamGrade obtainedGrade;

	private MarksFeedStatus marksFeedStatus;

	private ExamAttendanceStatus attendanceStatus;

	public ExamDimensionObtainedValues() {
	}

	public ExamDimensionObtainedValues(ExamDimension examDimension) {
		super(examDimension);
	}

	public ExamDimensionObtainedValues(ExamDimension examDimension,
									   Double obtainedMarks, Double graceMarks, ExamGrade obtainedGrade, ExamAttendanceStatus attendanceStatus,
									   Double originalMaxMarks, Double maxMarks, Double minMarks, ExamGrade maxGrade,
									   ExamGrade minGrade, ExamCoursePublishedStatus examCoursePublishedStatus) {
		super(examDimension, originalMaxMarks, maxMarks, minMarks, maxGrade, minGrade, examCoursePublishedStatus);
		this.obtainedMarks = obtainedMarks;
		this.graceMarks = graceMarks;
		this.obtainedGrade = obtainedGrade;
		this.attendanceStatus = attendanceStatus;
	}

	public ExamDimensionObtainedValues(ExamDimension examDimension,
									   Double obtainedMarks, Double graceMarks, ExamGrade obtainedGrade, ExamAttendanceStatus attendanceStatus,
									   MarksFeedStatus marksFeedStatus, ExamCoursePublishedStatus examCoursePublishedStatus) {
		super(examDimension, null, null, null, null, null, examCoursePublishedStatus);
		this.obtainedMarks = obtainedMarks;
		this.graceMarks = graceMarks;
		this.obtainedGrade = obtainedGrade;
		this.attendanceStatus = attendanceStatus;
		this.marksFeedStatus = marksFeedStatus;
	}

	public Double getObtainedMarks() {
		return obtainedMarks;
	}

	public Double getGraceMarks() {
		return graceMarks;
	}

	public ExamGrade getObtainedGrade() {
		return obtainedGrade;
	}

	public void setObtainedMarks(Double obtainedMarks) {
		this.obtainedMarks = obtainedMarks;
	}

	public void setGraceMarks(Double graceMarks) {
		this.graceMarks = graceMarks;
	}

	public void setObtainedGrade(ExamGrade obtainedGrade) {
		this.obtainedGrade = obtainedGrade;
	}

	// public static ExamDimensionObtainedValues
	// addDimensions(ExamDimensionObtainedValues first,
	// ExamDimensionObtainedValues second) {
	// if (first.getExamDimension().getDimensionId() !=
	// second.getExamDimension().getDimensionId()) {
	// return null;
	// }
	// Double maxMarks = addValue(first.getMaxMarks(), second.getMaxMarks());
	// Double minMarks = addValue(first.getMinMarks(), second.getMinMarks());
	// Double obtainedMarks = addValue(first.getObtainedMarks(),
	// second.getObtainedMarks());
	// Double graceMarks = addValue(first.getGraceMarks(),
	// second.getGraceMarks());
	//
	// return new ExamDimensionObtainedValues(first.getExamDimension(),
	// obtainedMarks, graceMarks, null, maxMarks,
	// minMarks, null, null);
	//
	// }

	public MarksFeedStatus getMarksFeedStatus() {
		return marksFeedStatus;
	}

	public void setMarksFeedStatus(MarksFeedStatus marksFeedStatus) {
		this.marksFeedStatus = marksFeedStatus;
	}

	public ExamAttendanceStatus getAttendanceStatus() {
		return attendanceStatus;
	}

	public String getAttendanceStatusShortName() {
		return attendanceStatus == null ? null : attendanceStatus.getShortName();
	}

	public String getAttendanceStatusDisplayName() {
		return attendanceStatus == null ? null : attendanceStatus.getDisplayName();
	}

	public void setAttendanceStatus(ExamAttendanceStatus attendanceStatus) {
		this.attendanceStatus = attendanceStatus;
	}

	public Double getObtainedMarksFraction() {
		// SEARCH_EXP : AdjustedMaxMarksUsage
		return computeObtainedMarksFraction(getObtainedMarks(), getMaxMarks());
	}

	public double getObtainedStar() {
		Double fraction = getObtainedMarksFraction();
		return computeObtainedStar(fraction);
	}

	public int getMaxStar() {
		return DEFAULT_MAX_STARS;
	}

	public static Double computeObtainedMarksFraction(Double obtainedMarks, Double maxMarks){
		if (obtainedMarks == null || maxMarks == null
				|| Double.compare(maxMarks, 0) <= 0) {
			return null;
		}
		return obtainedMarks / maxMarks;
	}

	public static Double computeObtainedStar(Double obtainedMarks, Double maxMarks){
		return computeObtainedStar(computeObtainedMarksFraction(obtainedMarks, maxMarks));
	}
	public static Double computeObtainedStar(Double obtainedMarksFraction){
		if (obtainedMarksFraction == null) {
			return 0d;
		}
		return Math.round(obtainedMarksFraction * DEFAULT_MAX_STARS * 10) / 10d;
	}

	public static String getFinalObtainedValueDisplay(Object obtainedValue, ExamAttendanceStatus attendanceStatus){
		return getFinalObtainedValueDisplay(obtainedValue, attendanceStatus, null, false, "", null, null, false);
	}
	public static String getFinalDivisionValueDisplay(Object obtainedValue, ExamAttendanceStatus attendanceStatus){
		return getFinalDivisionValueDisplay(obtainedValue, attendanceStatus, null, false, "", null, null, false);
	}

	public static String getFinalObtainedValueDisplay(Object obtainedValue, ExamAttendanceStatus attendanceStatus, boolean showStatusWithMarks){
		return getFinalObtainedValueDisplay(obtainedValue, attendanceStatus, null, false, "", null, null, showStatusWithMarks);
	}
	public static String getFinalObtainedValueDisplay(Object obtainedValue, ExamAttendanceStatus attendanceStatus,
			Double minMarks, boolean showMinMarksSuffix, String suffix, Double maxMarks, String returnTextIfNoMarksAndAttendanceIsFilled,
													  boolean showStatusWithMarks){
		if(obtainedValue == null && attendanceStatus == null) {
			return returnTextIfNoMarksAndAttendanceIsFilled;
		}
		String failedSuffix = "";
		if(isNumeric(obtainedValue)) {
			Double obtainedMarks = new Double(obtainedValue.toString());
			/**
			 * if no min marks is set then setting min marks as
			 * 0.33 of max marks
			 */
			minMarks = minMarks == null ? maxMarks == null ? null : Math.round(PASSING_THRESHOLD * maxMarks) * 1d : minMarks;
			/**
			 * Check if student has failed in the subject
			 */
			if (showMinMarksSuffix && minMarks != null && obtainedMarks < minMarks) {
				failedSuffix = suffix;
			}
		}
		if (obtainedValue != null && attendanceStatus == null){
			return obtainedValue + failedSuffix;
		} else if (obtainedValue == null) {
			return attendanceStatus.getShortName();
		} else {
			return obtainedValue + (showStatusWithMarks ? ("("+attendanceStatus.getShortName() +")") : "") + failedSuffix;
		}
	}

	public static String getFinalDivisionValueDisplay(Object obtainedValue, ExamAttendanceStatus attendanceStatus,
													  Double minMarks, boolean showMinMarksSuffix, String suffix, Double maxMarks,
													  String returnTextIfNoMarksAndAttendanceIsFilled, boolean showStatusWithMarks) {
		if (obtainedValue == null && attendanceStatus == null) {
			return returnTextIfNoMarksAndAttendanceIsFilled;
		}

		String failedSuffix = "";
		double obtainedMarks = 0d;
		String division = "";

		if (isNumeric(obtainedValue)) {
			obtainedMarks = Double.parseDouble(obtainedValue.toString());
			division = ExamDivision.getExamDivisionByMarks(obtainedMarks) == null ? null : ExamDivision.getExamDivisionByMarks(obtainedMarks).getDisplayName();

			minMarks = minMarks == null ? (maxMarks == null ? null : Math.round(PASSING_THRESHOLD * maxMarks) * 1d) : minMarks;

			if (showMinMarksSuffix && minMarks != null && obtainedMarks < minMarks) {
				failedSuffix = suffix;
			}
		}

		if ((division == null || division.isEmpty()) && attendanceStatus != null) {
			return attendanceStatus.getShortName();
		}

		if (attendanceStatus == null) {
			return division + failedSuffix;
		}

		return division + (showStatusWithMarks ? ("(" + attendanceStatus.getShortName() + ")") : "") + failedSuffix;
	}

	public static boolean isNumeric(Object strNum) {
		if (strNum == null) {
			return false;
		};
		try {
			Double d = new Double(strNum.toString());
		} catch (NumberFormatException nfe) {
			return false;
		}
		return true;
	}

	public static String getFinalPercentageValueDisplay(Object percentage, ExamAttendanceStatus attendanceStatus){
		return getFinalPercentageValueDisplay(percentage, attendanceStatus, true, false);
	}

	public static String getFinalPercentageValueDisplay(Object percentage, ExamAttendanceStatus attendanceStatus, boolean showPercentageSign, boolean showStatusWithMarks){
		if(percentage == null && attendanceStatus == null){
			return null;
		} else if (percentage != null && attendanceStatus == null){
			return percentage + (showPercentageSign ? "%" : "");
		} else if (percentage == null && attendanceStatus != null){
			return attendanceStatus.getShortName();
		} else {
			return percentage + (showPercentageSign ? "%" : "") + (showStatusWithMarks ? "("+attendanceStatus.getShortName() +")" : "");
		}
	}

	@Override
	public String toString() {
		return "ExamDimensionObtainedValues{" +
				"obtainedMarks=" + obtainedMarks +
				", graceMarks=" + graceMarks +
				", obtainedGrade=" + obtainedGrade +
				", marksFeedStatus=" + marksFeedStatus +
				", attendanceStatus=" + attendanceStatus +
				"} " + super.toString();
	}
}
