package com.lernen.cloud.core.api.student;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.lernen.cloud.core.api.common.BooleanDisplay;
import com.lernen.cloud.core.api.user.Document;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public class Student {

	private final int instituteId;
	private final UUID studentId;

	private final StudentBasicInfo studentBasicInfo;

	private final StudentFamilyInfo studentFamilyInfo;

	private final List<StudentGuardianInfo> studentGuardianInfoList;

	private final StudentPreviousSchoolInfo studentPreviousSchoolInfo;

	private final StudentMedicalInfo studentMedicalInfo;

	private final StudentAcademicSessionInfoResponse studentAcademicSessionInfoResponse;

	/**
	 * contain session status from student academic session table,
	 * this status is specific to any particular session
	 * for student details without session this will be null
	 */
	private final StudentStatus studentStatus;

	/**
	 * contain status from student table, this is students final status at any given time
	 */
	private final StudentStatus finalStudentStatus;

	private final List<Document<StudentDocumentType>> studentDocuments;

	private boolean proofOfDoB;

	private final int admissionSession;

	private Document<StudentDocumentType> studentImage;

	private Double walletAmount;

	private Map<TransferCertificateVariables, String> tcVariables;

	private String deviceUserId;

	private boolean newAdmission;

	private StudentTransferCertificateDetails studentTransferCertificateDetails;

	private byte[] thumbnail;

	private List<StudentTaggedDetails> studentTaggedDetailsList;

	public Student(int instituteId, UUID studentId, StudentBasicInfo studentBasicInfo,
			StudentFamilyInfo studentFamilyInfo,
			List<StudentGuardianInfo> studentGuardianInfoList,
			StudentPreviousSchoolInfo studentPreviousSchoolInfo,
			StudentMedicalInfo studentMedicalInfo,
			StudentAcademicSessionInfoResponse studentAcademicSessionInfoResponse,
			StudentStatus studentStatus,
			StudentStatus finalStudentStatus,
			List<Document<StudentDocumentType>> studentDocuments, int admissionSession,
				   Map<TransferCertificateVariables, String> tcVariables, String deviceUserId, boolean newAdmission,
				   StudentTransferCertificateDetails studentTransferCertificateDetails, List<StudentTaggedDetails> studentTaggedDetailsList) {
		this.instituteId = instituteId;
		this.studentId = studentId;
		this.studentBasicInfo = studentBasicInfo;
		this.studentFamilyInfo = studentFamilyInfo;
		this.studentGuardianInfoList = studentGuardianInfoList;
		this.studentPreviousSchoolInfo = studentPreviousSchoolInfo;
		this.studentMedicalInfo = studentMedicalInfo;
		this.studentAcademicSessionInfoResponse = studentAcademicSessionInfoResponse;
		this.finalStudentStatus = finalStudentStatus;
		this.studentStatus = studentStatus;
		this.studentDocuments = studentDocuments;
		this.proofOfDoB = checkProofOfDOB();
		this.admissionSession = admissionSession;
		this.studentImage = getImageDocument();
		this.tcVariables = tcVariables;
		this.deviceUserId = deviceUserId;
		this.newAdmission = newAdmission;
		this.studentTransferCertificateDetails = studentTransferCertificateDetails;
		this.studentTaggedDetailsList = studentTaggedDetailsList;
	}

	public int getInstituteId() {
		return instituteId;
	}

	public UUID getStudentId() {
		return studentId;
	}

	public StudentBasicInfo getStudentBasicInfo() {
		return studentBasicInfo;
	}

	public StudentFamilyInfo getStudentFamilyInfo() {
		return studentFamilyInfo;
	}

	public List<StudentGuardianInfo> getStudentGuardianInfoList() {
		return studentGuardianInfoList;
	}

	public StudentPreviousSchoolInfo getStudentPreviousSchoolInfo() {
		return studentPreviousSchoolInfo;
	}

	public StudentMedicalInfo getStudentMedicalInfo() {
		return studentMedicalInfo;
	}

	public StudentAcademicSessionInfoResponse getStudentAcademicSessionInfoResponse() {
		return studentAcademicSessionInfoResponse;
	}

	public StudentStatus getFinalStudentStatus() {
		return finalStudentStatus;
	}

	public StudentStatus getStudentStatus() {
		return studentStatus;
	}

	public List<Document<StudentDocumentType>> getStudentDocuments() {
		return studentDocuments;
	}

	public int getAdmissionSession() {
		return admissionSession;
	}

	public Document<StudentDocumentType> getStudentImage() {
		return studentImage;
	}

	public Double getWalletAmount() {
		return walletAmount;
	}

	public void setWalletAmount(Double walletAmount) {
		this.walletAmount = walletAmount;
	}

	public byte[] getThumbnail() {
		return thumbnail;
	}

	public void setThumbnail(byte[] thumbnail) {
		this.thumbnail = thumbnail;
	}

	public String getDeviceUserId() {
		return deviceUserId;
	}

	public void setDeviceUserId(String deviceUserId) {
		this.deviceUserId = deviceUserId;
	}

	private Document<StudentDocumentType> getImageDocument() {
		if (CollectionUtils.isEmpty(studentDocuments)) {
			return null;
		}
		for (Document<StudentDocumentType> studentDocument : studentDocuments) {
			if (studentDocument
					.getDocumentType() == StudentDocumentType.STUDENT_PROFILE_IMAGE) {
				return studentDocument;
			}
		}
		return null;
	}

	@JsonIgnore
	public static StudentLite getStudentLite(Student student) {
		if (student == null) {
			return null;
		}
		StudentSessionDataLite studentSessionData = null;
		StudentAcademicSessionInfoResponse studentAcademicSessionInfoResponse = student
				.getStudentAcademicSessionInfoResponse();
		if (studentAcademicSessionInfoResponse != null
				&& studentAcademicSessionInfoResponse
						.getAcademicSession() != null
				&& studentAcademicSessionInfoResponse.getAcademicSession()
						.getAcademicSessionId() > 0
				&& studentAcademicSessionInfoResponse.getStandard() != null
				&& studentAcademicSessionInfoResponse.getStandard()
						.getStandardId() != null) {

			studentSessionData = new StudentSessionDataLite(
					studentAcademicSessionInfoResponse.getAcademicSession()
							.getAcademicSessionId(),
					studentAcademicSessionInfoResponse.getAcademicSession()
							.getDisplayName(),
					studentAcademicSessionInfoResponse.getAcademicSession()
							.getShortYearDisplayName(),
					studentAcademicSessionInfoResponse.getStandard()
							.getStandardId(),
					studentAcademicSessionInfoResponse.getStandard()
							.getStandardName(),
					studentAcademicSessionInfoResponse.getStandard()
							.getStream(),
					studentAcademicSessionInfoResponse.getStandard().getLevel(),
					CollectionUtils.isEmpty(studentAcademicSessionInfoResponse
							.getStandard().getStandardSectionList())
									? null
									: studentAcademicSessionInfoResponse
											.getStandard()
											.getStandardSectionList().get(0),
					studentAcademicSessionInfoResponse.getRollNumber(),
					studentAcademicSessionInfoResponse.getBoardRegistrationNumber(),
					studentAcademicSessionInfoResponse.getHeight(),
					studentAcademicSessionInfoResponse.getWeight(),
					studentAcademicSessionInfoResponse.getMedium(), studentAcademicSessionInfoResponse.getHostelDetails());
		}
		StudentBasicInfo studentBasicInfo = student.getStudentBasicInfo();
		return new StudentLite(student.getStudentId(),
				student.getStudentStatus(),
				student.getFinalStudentStatus(),
				studentBasicInfo.getRegistrationNumber(),
				studentBasicInfo.getAdmissionNumber(),
				studentBasicInfo.getName(), studentBasicInfo.getDateOfBirth(),
				studentBasicInfo.getGender(), studentBasicInfo.getUserCategory(), studentBasicInfo.isRte(),
				student.getStudentFamilyInfo().getMothersName(),
				student.getStudentFamilyInfo().getFathersName(),
				studentBasicInfo.getPermanentAddress(), studentBasicInfo.getStudentFullAddress(), studentBasicInfo.getSiblingGroupId(), studentBasicInfo.getAadharNumber(), studentSessionData,
				student.getWalletAmount(), studentBasicInfo.getPrimaryContactNumber(), studentBasicInfo.getInstituteHouseId(),
				studentBasicInfo.getInstituteHouse(), student.getStudentMedicalInfo() == null ? null : student.getStudentMedicalInfo().getBloodGroup(), student.studentBasicInfo.isHosteller(), student.getStudentTaggedDetailsList());
	}

	public void setStudentImage(Document<StudentDocumentType> studentImage) {
		this.studentImage = studentImage;
	}

	public Map<TransferCertificateVariables, String> getTcVariables() {
		return tcVariables;
	}

	public void setTcVariables(Map<TransferCertificateVariables, String> tcVariables) {
		this.tcVariables = tcVariables;
	}

	public boolean isNewAdmission() {
		return newAdmission;
	}

	public void setNewAdmission(boolean newAdmission) {
		this.newAdmission = newAdmission;
	}

	public StudentTransferCertificateDetails getStudentTransferCertificateDetails() {
		return studentTransferCertificateDetails;
	}

	public void setStudentTransferCertificateDetails(StudentTransferCertificateDetails studentTransferCertificateDetails) {
		this.studentTransferCertificateDetails = studentTransferCertificateDetails;
	}

	public String getNewAdmissionDisplay() {
		return BooleanDisplay.getBooleanDisplay(newAdmission).getDisplay();
	}


	public List<StudentTaggedDetails> getStudentTaggedDetailsList() {
		return studentTaggedDetailsList;
	}

	public void setStudentTaggedDetailsList(List<StudentTaggedDetails> studentTaggedDetailsList) {
		this.studentTaggedDetailsList = studentTaggedDetailsList;
	}

	public boolean isProofOfDoB() { return proofOfDoB;}

	public void setProofOfDoB(boolean proofOfDoB) { this.proofOfDoB = proofOfDoB;}

	private boolean checkProofOfDOB () {
		boolean proofOfDoB = false;
		if(!CollectionUtils.isEmpty(studentDocuments)){
			for (Document<StudentDocumentType> StudentDocument :  studentDocuments) {
				if(StudentDocument.getDocumentType().equals(StudentDocumentType.AADHAR_CARD) || StudentDocument.getDocumentType().equals(StudentDocumentType.STUDENT_PAN_CARD) || StudentDocument.getDocumentType().equals(StudentDocumentType.BIRTH_CERTIFICATE_AND_AFFIDAVIT) || StudentDocument.getDocumentType().equals(StudentDocumentType.BIRTH_CERTIFICATE)) {
					proofOfDoB = true;
					break;
				}
			}
		}
		return proofOfDoB;
	}



	@Override
	public String toString() {
		return "Student{" +
				"instituteId=" + instituteId +
				", studentId=" + studentId +
				", studentBasicInfo=" + studentBasicInfo +
				", studentFamilyInfo=" + studentFamilyInfo +
				", studentGuardianInfoList=" + studentGuardianInfoList +
				", studentPreviousSchoolInfo=" + studentPreviousSchoolInfo +
				", studentMedicalInfo=" + studentMedicalInfo +
				", studentAcademicSessionInfoResponse=" + studentAcademicSessionInfoResponse +
				", studentStatus=" + studentStatus +
				", finalStudentStatus=" + finalStudentStatus +
				", studentDocuments=" + studentDocuments +
				", proofOfDoB=" + proofOfDoB +
				", admissionSession=" + admissionSession +
				", studentImage=" + studentImage +
				", walletAmount=" + walletAmount +
				", tcVariables=" + tcVariables +
				", deviceUserId='" + deviceUserId + '\'' +
				", newAdmission=" + newAdmission +
				", studentTransferCertificateDetails=" + studentTransferCertificateDetails +
				", thumbnail=" + Arrays.toString(thumbnail) +
				", studentTaggedDetailsList=" + studentTaggedDetailsList +
				'}';
	}
}
