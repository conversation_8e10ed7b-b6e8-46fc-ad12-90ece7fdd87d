package com.lernen.cloud.core.api.tracking.events;

import java.util.Map;
import java.util.UUID;

import com.lernen.cloud.core.api.common.Channel;

/**
 * 
 * <AUTHOR>
 *
 */

public class TrackingEventPayload {

	private Channel channel;

	private TrackingEventName trackingEventName;

	private Long eventTime;

	private String ipAddress;

	private String sessionId;

	private Integer instituteId;

	private UUID userId;

	private Map<String, Object> metaData;

	public Channel getChannel() {
		return channel;
	}

	public void setChannel(Channel channel) {
		this.channel = channel;
	}

	public TrackingEventName getTrackingEventName() {
		return trackingEventName;
	}

	public void setTrackingEventName(TrackingEventName trackingEventName) {
		this.trackingEventName = trackingEventName;
	}

	public Long getEventTime() {
		return eventTime;
	}

	public void setEventTime(Long eventTime) {
		this.eventTime = eventTime;
	}

	public String getIpAddress() {
		return ipAddress;
	}

	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}

	public String getSessionId() {
		return sessionId;
	}

	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}

	public Integer getInstituteId() {
		return instituteId;
	}

	public void setInstituteId(Integer instituteId) {
		this.instituteId = instituteId;
	}

	public UUID getUserId() {
		return userId;
	}

	public void setUserId(UUID userId) {
		this.userId = userId;
	}

	public Map<String, Object> getMetaData() {
		return metaData;
	}

	public void setMetaData(Map<String, Object> metaData) {
		this.metaData = metaData;
	}

	@Override
	public String toString() {
		return "TrackingEventPayload [channel=" + channel
				+ ", trackingEventName=" + trackingEventName + ", eventTime="
				+ eventTime + ", ipAddress=" + ipAddress + ", sessionId="
				+ sessionId + ", instituteId=" + instituteId + ", userId="
				+ userId + ", metaData=" + metaData + "]";
	}

}
