package com.lernen.cloud.core.api.examination;

public enum ExamDivision {

    I("First"),
    II("Second"),
    III("Third"),
    IV("Fourth");

    private String displayName;

    ExamDivision(String displayName) {
        this.displayName = displayName;
    }

    public static ExamDivision getExamDivisionByMarks(Double value) {
        if(value == null) {
            return null;
        }
        if (value >= 60) {
            return I;
        } else if (value < 60 && value >= 45) {
            return II;
        } else if (value < 45 && value >= 32) {
            return III;
        } else {
            return IV;
        }
    }

    public String getDisplayName() {
        return displayName;
    }

}