package com.lernen.cloud.core.api.configurations;

import com.embrate.cloud.core.api.application.mobile.MobileAppUpdatePriority;
import com.embrate.cloud.core.api.service.payment.gateway.PaymentGatewayServiceProvider;
import com.lernen.cloud.core.api.common.HourMinuteRange;
import com.lernen.cloud.core.api.common.TransactionMode;
import com.lernen.cloud.core.api.common.TutorialVideoDetails;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.student.StudentRegistrationFormFields;
import com.lernen.cloud.core.api.user.Module;

import java.time.DayOfWeek;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 *
 * <AUTHOR>
 *
 */
public class MetaDataPreferences {

	public static final String REGISTRATION_COUNTER = "registration_counter";//
	public static final String ADMISSION_COUNTER = "admission_counter";//
	public static final String SMS_SERVICE_ENABLED = "sms_service_enabled"; //
	public static final String ONLY_STUDENT_COPY_IN_FEE_RECEIPTS = "only_student_copy_in_fee_receipts"; //
	public static final String INSTITUTE_NAME_IN_SMS = "institute_name_in_sms"; //
	public static final String ENABLE_PERMISSION = "enable_permission"; //
	public static final String AUDIT_LOG_ENABLED = "audit_log_enabled"; //
	/**
	 * Fee collection page, by default hide current date
	 */
	public static final String HIDE_CURRENT_DATE_ON_FEE_PAYMENT = "hide_current_date_on_fee_payment"; //
	public static final String DEFAULT_FEE_PAYMENT_MODE = "default_fee_payment_mode"; //
	public static final String ALLOWED_WORKING_HOURS_RANGE = "allowed_working_hours_range"; //
	public static final String ALLOWED_WORKING_DAYS = "allowed_working_days"; //
	public static final String WORK_TIMINGS_ENABLED = "work_timings_enabled"; //

	public static final String CREATE_STUDENT_USER = "create_student_user"; //
	public static final String CREATE_STAFF_USER = "create_staff_user"; //
	public static final String SEND_SMS_ON_STUDENT_USER_CREATION = "send_sms_on_student_user_creation"; //
	public static final String USERNAME_LENGTH = "username_length"; //
	public static final String USER_PASSWORD_LENGTH = "user_password_length"; //

	public static final String STAFF_COUNTER = "staff_counter"; //

	/**
	 * This should not contain @ symbol as @ is added while creating user name
	 */
	public static final String INSTITUTE_UNIQUE_CODE = "institute_unique_code"; //

	public static final String VOICE_CALL_SERVICE_ENABLED = "voice_call_service_enabled"; //
	
	/**
	 * List of all the module which institute have access to
	 */
	public static final String MODULE_ACCESS = "module_access"; //
	
	/**
	 * Restricted Actions on institute or global level
	 */
	public static final String RESTRICTED_ACTIONS = "restricted_actions"; //

	public static final String RESTRICTION_REASON = "restriction_reason"; //

	public static final String PAYMENT_GATEWAY_SERVICE_ENABLED = "payment_gateway_service"; //

	public static final String PAYMENT_GATEWAY_SERVICE_PROVIDER = "payment_gateway_service_provider"; //

	public static final String BIRTHDAY_SMS_ENABLED = "birthday_sms_enabled"; //

	public static final String SINGLE_DEVICE_MAX_USERS = "single_device_max_users"; //

	public static final String MOBILE_APP_ENABLED = "mobile_app_enabled"; //

	public static final String ANDROID_MOBILE_APP_VERSION = "android_mobile_app_version"; //

	public static final String IOS_MOBILE_APP_VERSION = "ios_mobile_app_version"; //

	public static final String ADMIN_MOBILE_APP_UPDATE_PRIORITY = "admin_mobile_app_update_priority"; //

	public static final String STUDENT_MOBILE_APP_UPDATE_PRIORITY = "student_mobile_app_update_priority"; //

	/**
	 * Any login time before this threshold will get forced logout for Android
	 */
	public static final String ANDROID_FORCE_LOGOUT_TIME_THRESHOLD = "android_force_logout_time_threshold"; //

	/**
	 * Any login time before this threshold will get forced logout for IOS
	 */
	public static final String IOS_FORCE_LOGOUT_TIME_THRESHOLD = "ios_force_logout_time_threshold"; //

	public static final String TRANSFER_CERTIFICATE_COUNTER = "transfer_certificate_counter"; //

	public static final String DETAILED_TC_ENABLED = "detailed_tc_enabled"; //

	public static final String STUDENT_REGISTRATION_FORM_FIELDS = "student_registration_form_fields"; //

	public static final String STUDENT_REGISTRATION_FORM_MANDATORY_FIELDS = "student_registration_form_mandatory_fields"; //

	public static final String STUDENT_REGISTRATION_FORM_STANDARD_LIST  = "student_registration_from_standard_list";

	public static final String ADM_NUM_AUTH_FLOW = "adm_num_auth_flow"; //

	public static final String AUTO_COMPUTE_PAYMENT_DISCOUNT_AND_FINE = "auto_compute_payment_discount_and_fine"; //

	public static final String DISABLE_SYSTEM_ATTENDANCE_FOR_SALARY = "disable_system_attendance_for_salary"; //
	public static final String SETTLE_PREVIOUS_SESSION_FEES_FIRST = "settle_previous_session_fees_first"; //
	public static final String ONLINE_REGISTRATION_PAYMENT_ENABLED = "online_registration_payment_enabled"; //

	public static final String RESTRICT_ENROLLMENT_PAYMENT_PENDING ="restrict_enrollment_payment_pending";
	public static final String STUDENT_SESSION_RESTRICTION ="student_session_restriction";
	public static final String TUTORIAL_VIDEOS_ENABLED ="tutorial_videos_enabled";
	public static final String TUTORIAL_VIDEO_DETAILS ="tutorial_video_details";
	public static final String RESTRICT_STUDENT_USE_WALLET_FEES_PAYMENT = "restrict_student_use_wallet_fees_payment";

	private boolean registrationCounter;

	private boolean admissionCounter;

	private boolean staffCounter;

	private boolean transferCertificateCounter;

	private boolean detailedTCEnabled;

	private boolean smsServiceEnabled;

	private boolean onlyStudentCopyInFeeReceipts;

	private String instituteNameInSMS;

	private boolean enablePermission;

	private boolean auditLogEnabled;

	private boolean hideCurrentDateOnFeePayment;

	private TransactionMode defaultFeePaymentMode;

	private boolean workTimingsEnabled;

	private HourMinuteRange allowedWorkingHoursRange;

	private Set<DayOfWeek> allowedWorkingDays;

	private boolean createStudentUser;
	
	private boolean createStaffUser;

	private boolean sendSMSOnStudentUserCreation;

	private int userNameLength;

	private int userPasswordLength;

	private String instituteUniqueCode;

	private boolean voiceCallServiceEnabled;
	
	private Set<Module> moduleAccess;
	
	private Set<AuthorisationRequiredAction> restrictedActions;

	private String restrictionReason;

	private boolean paymentGatewayServiceEnabled;

	private PaymentGatewayServiceProvider paymentGatewayServiceProvider;

	private boolean birthdaySMSEnabled;

	private Integer singleDeviceMaxUsers;

	private boolean mobileAppEnabled;

	private String androidMobileAppVersion;

	private String iosMobileAppVersion;

	private MobileAppUpdatePriority adminMobileAppUpdatePriority;

	private MobileAppUpdatePriority studentMobileAppUpdatePriority;

	private int androidForceLogoutTimeThreshold;

	private int iosForceLogoutTimeThreshold;

	private Set<StudentRegistrationFormFields> studentRegistrationFormFields;

	private Set<StudentRegistrationFormFields> studentRegistrationFormMandatoryFields;

	private Set<String> studentRegistrationFormRequiredStandards;

	private boolean admissionNumberAuthFlow;

	private boolean autoComputePaymentDiscountAndFine;

	private boolean disableSystemAttendanceForSalary;

	private boolean settlePreviousSessionFeesFirst;

	private boolean onlineRegistrationPaymentEnabled;

	private boolean restrictEnrollmentPaymentPending;

	private Set<Integer> studentSessionRestictionSet;

	private boolean tutorialVideosEnabled;

	private Map<Module, List<TutorialVideoDetails>> tutorialVideoDetails;

	private boolean restrictStudentUseWalletFeesPayment;


	public static String getConfigType() {
		return "meta_data";
	}

	public boolean isRegistrationCounter() {
		return registrationCounter;
	}

	public Set<Integer> getStudentSessionRestriction() {
		return studentSessionRestictionSet;
	}

	public void setStudentSessionRestictionSet(Set<Integer> studentSessionRestictionSet) {
		this.studentSessionRestictionSet = studentSessionRestictionSet;
	}

	public void setRegistrationCounter(boolean registrationCounter) {
		this.registrationCounter = registrationCounter;
	}

	public boolean isTutorialVideosEnabled() {
		return tutorialVideosEnabled;
	}

	public void setTutorialVideosEnabled(boolean tutorialVideoServiceEnable) {
		this.tutorialVideosEnabled = tutorialVideoServiceEnable;
	}

	public Map<Module, List<TutorialVideoDetails>> getTutorialVideoDetails() {
		return tutorialVideoDetails;
	}

	public void setTutorialVideoDetails(Map<Module, List<TutorialVideoDetails>> tutorialVideoDetails) {
		this.tutorialVideoDetails = tutorialVideoDetails;
	}

	public boolean isRestrictEnrollmentPaymentPending() {
		return restrictEnrollmentPaymentPending;
	}

	public void setRestrictEnrollmentPaymentPending(boolean restrictEnrollmentPaymentPending) {
		this.restrictEnrollmentPaymentPending = restrictEnrollmentPaymentPending;
	}

	public boolean isStaffCounter() {
		return staffCounter;
	}

	public void setStaffCounter(boolean staffCounter) {
		this.staffCounter = staffCounter;
	}

	public boolean isAdmissionCounter() {
		return admissionCounter;
	}

	public void setAdmissionCounter(boolean admissionCounter) {
		this.admissionCounter = admissionCounter;
	}

	public boolean isSmsServiceEnabled() {
		return smsServiceEnabled;
	}

	public void setSmsServiceEnabled(boolean smsServiceEnabled) {
		this.smsServiceEnabled = smsServiceEnabled;
	}

	public boolean isOnlyStudentCopyInFeeReceipts() {
		return onlyStudentCopyInFeeReceipts;
	}

	public void setOnlyStudentCopyInFeeReceipts(
			boolean onlyStudentCopyInFeeReceipts) {
		this.onlyStudentCopyInFeeReceipts = onlyStudentCopyInFeeReceipts;
	}

	public String getInstituteNameInSMS() {
		return instituteNameInSMS;
	}

	public void setInstituteNameInSMS(String instituteNameInSMS) {
		this.instituteNameInSMS = instituteNameInSMS;
	}

	public boolean isEnablePermission() {
		return enablePermission;
	}

	public void setEnablePermission(boolean enablePermission) {
		this.enablePermission = enablePermission;
	}

	public boolean isAuditLogEnabled() {
		return auditLogEnabled;
	}

	public void setAuditLogEnabled(boolean auditLogEnabled) {
		this.auditLogEnabled = auditLogEnabled;
	}

	public boolean isHideCurrentDateOnFeePayment() {
		return hideCurrentDateOnFeePayment;
	}

	public void setHideCurrentDateOnFeePayment(
			boolean hideCurrentDateOnFeePayment) {
		this.hideCurrentDateOnFeePayment = hideCurrentDateOnFeePayment;
	}

	public TransactionMode getDefaultFeePaymentMode() {
		return defaultFeePaymentMode;
	}

	public void setDefaultFeePaymentMode(
			TransactionMode defaultFeePaymentMode) {
		this.defaultFeePaymentMode = defaultFeePaymentMode;
	}

	public boolean isWorkTimingsEnabled() {
		return workTimingsEnabled;
	}

	public void setWorkTimingsEnabled(boolean workTimingsEnabled) {
		this.workTimingsEnabled = workTimingsEnabled;
	}

	public HourMinuteRange getAllowedWorkingHoursRange() {
		return allowedWorkingHoursRange;
	}

	public void setAllowedWorkingHoursRange(
			HourMinuteRange allowedWorkingHoursRange) {
		this.allowedWorkingHoursRange = allowedWorkingHoursRange;
	}

	public Set<DayOfWeek> getAllowedWorkingDays() {
		return allowedWorkingDays;
	}

	public void setAllowedWorkingDays(Set<DayOfWeek> allowedWorkingDays) {
		this.allowedWorkingDays = allowedWorkingDays;
	}

	public boolean isCreateStudentUser() {
		return createStudentUser;
	}

	public void setCreateStudentUser(boolean createStudentUser) {
		this.createStudentUser = createStudentUser;
	}

	/**
	 * @return the createStaffUser
	 */
	public boolean isCreateStaffUser() {
		return createStaffUser;
	}

	/**
	 * @param createStaffUser the createStaffUser to set
	 */
	public void setCreateStaffUser(boolean createStaffUser) {
		this.createStaffUser = createStaffUser;
	}

	public boolean isSendSMSOnStudentUserCreation() {
		return sendSMSOnStudentUserCreation;
	}

	public void setSendSMSOnStudentUserCreation(
			boolean sendSMSOnStudentUserCreation) {
		this.sendSMSOnStudentUserCreation = sendSMSOnStudentUserCreation;
	}

	public int getUserNameLength() {
		return userNameLength;
	}

	public void setUserNameLength(int userNameLength) {
		this.userNameLength = userNameLength;
	}

	public int getUserPasswordLength() {
		return userPasswordLength;
	}

	public void setUserPasswordLength(int userPasswordLength) {
		this.userPasswordLength = userPasswordLength;
	}

	public String getInstituteUniqueCode() {
		return instituteUniqueCode;
	}

	public void setInstituteUniqueCode(String instituteUniqueCode) {
		this.instituteUniqueCode = instituteUniqueCode;
	}

	public boolean isVoiceCallServiceEnabled() {
		return voiceCallServiceEnabled;
	}

	public void setVoiceCallServiceEnabled(boolean voiceCallServiceEnabled) {
		this.voiceCallServiceEnabled = voiceCallServiceEnabled;
	}

	/**
	 * @return the moduleAccess
	 */
	public Set<Module> getModuleAccess() {
		return moduleAccess;
	}

	/**
	 * @param moduleAccess the moduleAccess to set
	 */
	public void setModuleAccess(Set<Module> moduleAccess) {
		this.moduleAccess = moduleAccess;
	}

	/**
	 * @return the restrictedActions
	 */
	public Set<AuthorisationRequiredAction> getRestrictedActions() {
		return restrictedActions;
	}

	/**
	 * @param restrictedActions the restrictedActions to set
	 */
	public void setRestrictedActions(Set<AuthorisationRequiredAction> restrictedActions) {
		this.restrictedActions = restrictedActions;
	}

	public String getRestrictionReason() {
		return restrictionReason;
	}

	public void setRestrictionReason(String restrictionReason) {
		this.restrictionReason = restrictionReason;
	}

	public boolean isPaymentGatewayServiceEnabled() {
		return paymentGatewayServiceEnabled;
	}

	public void setPaymentGatewayServiceEnabled(boolean paymentGatewayServiceEnabled) {
		this.paymentGatewayServiceEnabled = paymentGatewayServiceEnabled;
	}

	public PaymentGatewayServiceProvider getPaymentGatewayServiceProvider() {
		return paymentGatewayServiceProvider;
	}

	public void setPaymentGatewayServiceProvider(PaymentGatewayServiceProvider paymentGatewayServiceProvider) {
		this.paymentGatewayServiceProvider = paymentGatewayServiceProvider;
	}

	public boolean isBirthdaySMSEnabled() {
		return birthdaySMSEnabled;
	}

	public void setBirthdaySMSEnabled(boolean birthdaySMSEnabled) {
		this.birthdaySMSEnabled = birthdaySMSEnabled;
	}

	public Integer getSingleDeviceMaxUsers() {
		return singleDeviceMaxUsers;
	}

	public void setSingleDeviceMaxUsers(Integer singleDeviceMaxUsers) {
		this.singleDeviceMaxUsers = singleDeviceMaxUsers;
	}

	public boolean isMobileAppEnabled() {
		return mobileAppEnabled;
	}

	public void setMobileAppEnabled(boolean mobileAppEnabled) {
		this.mobileAppEnabled = mobileAppEnabled;
	}

	public String getAndroidMobileAppVersion() {
		return androidMobileAppVersion;
	}

	public void setAndroidMobileAppVersion(String androidMobileAppVersion) {
		this.androidMobileAppVersion = androidMobileAppVersion;
	}

	public String getIosMobileAppVersion() {
		return iosMobileAppVersion;
	}

	public void setIosMobileAppVersion(String iosMobileAppVersion) {
		this.iosMobileAppVersion = iosMobileAppVersion;
	}

	public MobileAppUpdatePriority getAdminMobileAppUpdatePriority() {
		return adminMobileAppUpdatePriority;
	}

	public void setAdminMobileAppUpdatePriority(MobileAppUpdatePriority adminMobileAppUpdatePriority) {
		this.adminMobileAppUpdatePriority = adminMobileAppUpdatePriority;
	}

	public MobileAppUpdatePriority getStudentMobileAppUpdatePriority() {
		return studentMobileAppUpdatePriority;
	}

	public void setStudentMobileAppUpdatePriority(MobileAppUpdatePriority studentMobileAppUpdatePriority) {
		this.studentMobileAppUpdatePriority = studentMobileAppUpdatePriority;
	}

	public int getAndroidForceLogoutTimeThreshold() {
		return androidForceLogoutTimeThreshold;
	}

	public void setAndroidForceLogoutTimeThreshold(int androidForceLogoutTimeThreshold) {
		this.androidForceLogoutTimeThreshold = androidForceLogoutTimeThreshold;
	}

	public int getIosForceLogoutTimeThreshold() {
		return iosForceLogoutTimeThreshold;
	}

	public void setIosForceLogoutTimeThreshold(int iosForceLogoutTimeThreshold) {
		this.iosForceLogoutTimeThreshold = iosForceLogoutTimeThreshold;
	}

	public boolean isTransferCertificateCounter() {
		return transferCertificateCounter;
	}

	public void setTransferCertificateCounter(boolean transferCertificateCounter) {
		this.transferCertificateCounter = transferCertificateCounter;
	}

	public boolean isDetailedTCEnabled() {
		return detailedTCEnabled;
	}

	public void setDetailedTCEnabled(boolean detailedTCEnabled) {
		this.detailedTCEnabled = detailedTCEnabled;
	}

	public Set<StudentRegistrationFormFields> getStudentRegistrationFormFields() {
		return studentRegistrationFormFields;
	}

	public void setStudentRegistrationFormFields(Set<StudentRegistrationFormFields> studentRegistrationFormFields) {
		this.studentRegistrationFormFields = studentRegistrationFormFields;
	}

	public Set<StudentRegistrationFormFields> getStudentRegistrationFormMandatoryFields() {
		return studentRegistrationFormMandatoryFields;
	}

	public void setStudentRegistrationFormMandatoryFields(Set<StudentRegistrationFormFields> studentRegistrationFormMandatoryFields) {
		this.studentRegistrationFormMandatoryFields = studentRegistrationFormMandatoryFields;
	}

	public boolean isAdmissionNumberAuthFlow() {
		return admissionNumberAuthFlow;
	}

	public void setAdmissionNumberAuthFlow(boolean admissionNumberAuthFlow) {
		this.admissionNumberAuthFlow = admissionNumberAuthFlow;
	}

	public boolean isAutoComputePaymentDiscountAndFine() {
		return autoComputePaymentDiscountAndFine;
	}

	public void setAutoComputePaymentDiscountAndFine(boolean autoComputePaymentDiscountAndFine) {
		this.autoComputePaymentDiscountAndFine = autoComputePaymentDiscountAndFine;
	}

	public boolean isDisableSystemAttendanceForSalary() {
		return disableSystemAttendanceForSalary;
	}

	public void setDisableSystemAttendanceForSalary(boolean disableSystemAttendanceForSalary) {
		this.disableSystemAttendanceForSalary = disableSystemAttendanceForSalary;
	}

	public boolean isSettlePreviousSessionFeesFirst() {
		return settlePreviousSessionFeesFirst;
	}

	public void setSettlePreviousSessionFeesFirst(boolean settlePreviousSessionFeesFirst) {
		this.settlePreviousSessionFeesFirst = settlePreviousSessionFeesFirst;
	}

	public boolean isOnlineRegistrationPaymentEnabled() {
		return onlineRegistrationPaymentEnabled;
	}

	public void setOnlineRegistrationPaymentEnabled(boolean onlineRegistrationPaymentEnabled) {
		this.onlineRegistrationPaymentEnabled = onlineRegistrationPaymentEnabled;
	}

	public Set<String> getStudentRegistrationFormRequiredStandards() {
		return studentRegistrationFormRequiredStandards;
	}

	public void setStudentRegistrationFormRequiredStandards(Set<String> studentRegistrationFormRequiredStandards) {
		this.studentRegistrationFormRequiredStandards = studentRegistrationFormRequiredStandards;
	}

	public Set<Integer> getStudentSessionRestictionSet() {
		return studentSessionRestictionSet;
	}

	public boolean isRestrictStudentUseWalletFeesPayment() {
		return restrictStudentUseWalletFeesPayment;
	}

	public void setRestrictStudentUseWalletFeesPayment(boolean restrictStudentUseWalletFeesPayment) {
		this.restrictStudentUseWalletFeesPayment = restrictStudentUseWalletFeesPayment;
	}

	@Override
	public String toString() {
		return "MetaDataPreferences{" +
				"registrationCounter=" + registrationCounter +
				", admissionCounter=" + admissionCounter +
				", staffCounter=" + staffCounter +
				", transferCertificateCounter=" + transferCertificateCounter +
				", detailedTCEnabled=" + detailedTCEnabled +
				", smsServiceEnabled=" + smsServiceEnabled +
				", onlyStudentCopyInFeeReceipts=" + onlyStudentCopyInFeeReceipts +
				", instituteNameInSMS='" + instituteNameInSMS + '\'' +
				", enablePermission=" + enablePermission +
				", auditLogEnabled=" + auditLogEnabled +
				", hideCurrentDateOnFeePayment=" + hideCurrentDateOnFeePayment +
				", defaultFeePaymentMode=" + defaultFeePaymentMode +
				", workTimingsEnabled=" + workTimingsEnabled +
				", allowedWorkingHoursRange=" + allowedWorkingHoursRange +
				", allowedWorkingDays=" + allowedWorkingDays +
				", createStudentUser=" + createStudentUser +
				", createStaffUser=" + createStaffUser +
				", sendSMSOnStudentUserCreation=" + sendSMSOnStudentUserCreation +
				", userNameLength=" + userNameLength +
				", userPasswordLength=" + userPasswordLength +
				", instituteUniqueCode='" + instituteUniqueCode + '\'' +
				", voiceCallServiceEnabled=" + voiceCallServiceEnabled +
				", moduleAccess=" + moduleAccess +
				", restrictedActions=" + restrictedActions +
				", restrictionReason='" + restrictionReason + '\'' +
				", paymentGatewayServiceEnabled=" + paymentGatewayServiceEnabled +
				", paymentGatewayServiceProvider=" + paymentGatewayServiceProvider +
				", birthdaySMSEnabled=" + birthdaySMSEnabled +
				", singleDeviceMaxUsers=" + singleDeviceMaxUsers +
				", mobileAppEnabled=" + mobileAppEnabled +
				", androidMobileAppVersion='" + androidMobileAppVersion + '\'' +
				", iosMobileAppVersion='" + iosMobileAppVersion + '\'' +
				", adminMobileAppUpdatePriority=" + adminMobileAppUpdatePriority +
				", studentMobileAppUpdatePriority=" + studentMobileAppUpdatePriority +
				", androidForceLogoutTimeThreshold=" + androidForceLogoutTimeThreshold +
				", iosForceLogoutTimeThreshold=" + iosForceLogoutTimeThreshold +
				", studentRegistrationFormFields=" + studentRegistrationFormFields +
				", studentRegistrationFormMandatoryFields=" + studentRegistrationFormMandatoryFields +
				", studentRegistrationFormRequiredStandards=" + studentRegistrationFormRequiredStandards +
				", admissionNumberAuthFlow=" + admissionNumberAuthFlow +
				", autoComputePaymentDiscountAndFine=" + autoComputePaymentDiscountAndFine +
				", disableSystemAttendanceForSalary=" + disableSystemAttendanceForSalary +
				", settlePreviousSessionFeesFirst=" + settlePreviousSessionFeesFirst +
				", onlineRegistrationPaymentEnabled=" + onlineRegistrationPaymentEnabled +
				", restrictEnrollmentPaymentPending=" + restrictEnrollmentPaymentPending +
				", studentSessionRestictionSet=" + studentSessionRestictionSet +
				", tutorialVideosEnabled=" + tutorialVideosEnabled +
				", tutorialVideoDetails=" + tutorialVideoDetails +
				", restrictStudentUseWalletFeesPayment=" + restrictStudentUseWalletFeesPayment +
				'}';
	}
}
