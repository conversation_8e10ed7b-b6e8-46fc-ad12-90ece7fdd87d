package com.lernen.cloud.core.api.staff;

import com.lernen.cloud.core.api.user.UserPayload;

/**
 * <AUTHOR>
 */
public class StaffOnboardPayload {

    private UserPayload userPayload;

    private StaffTimingDetails staffTimingDetails;

    public StaffOnboardPayload(UserPayload userPayload, StaffTimingDetails staffTimingDetails) {
        this.userPayload = userPayload;
        this.staffTimingDetails = staffTimingDetails;
    }

    public StaffOnboardPayload() {
    }

    public UserPayload getUserPayload() {
        return userPayload;
    }

    public void setUserPayload(UserPayload userPayload) {
        this.userPayload = userPayload;
    }

    public StaffTimingDetails getStaffTimingDetails() {
        return staffTimingDetails;
    }

    public void setStaffTimingDetails(StaffTimingDetails staffTimingDetails) {
        this.staffTimingDetails = staffTimingDetails;
    }

    @Override
    public String toString() {
        return "StaffOnboardPayload{" +
                "userPayload=" + userPayload +
                ", staffTimingDetails=" + staffTimingDetails +
                '}';
    }
}
