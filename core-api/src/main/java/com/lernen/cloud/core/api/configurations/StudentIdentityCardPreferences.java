package com.lernen.cloud.core.api.configurations;

/**
 * 
 * <AUTHOR>
 *
 */
public class StudentIdentityCardPreferences {

	//I-Card Header
	public static final String INSTITUTE_NAME_COLOR = "institute_name_color";
	public static final String INSTITUTE_NAME_FONT_SIZE = "institute_name_font_size";
	public static final String LETTER_HEAD_1_COLOR = "letter_head_1_color";
	public static final String LETTER_HEAD_1_FONT_SIZE = "letter_head_1_font_size";
	public static final String LETTER_HEAD_2_COLOR = "letter_head_2_color";
	public static final String LETTER_HEAD_2_FONT_SIZE = "letter_head_2_font_size";
	public static final String INSTITUTE_LOGO_HEIGHT = "institute_logo_height";
	public static final String INSTITUTE_LOGO_WIDTH = "institute_logo_width";
	public static final String HEADER_BACKGROUND_COLOR = "header_background_color";
	public static final String SESSION_BACKGROUND_COLOR = "session_background_color";
	public static final String SESSION_FONT_COLOR = "session_font_color";
	public static final String SESSION_FONT_SIZE = "session_font_size";


	//I-Card Body
	public static final String STUDENT_IMAGE_HEIGHT = "student_image_height";
	public static final String STUDENT_IMAGE_WIDTH = "student_image_width";
	public static final String I_CARD_DETAILS_TEXT_COLOR = "i_card_details_text_color";

	//I-Card Footer
	public static final String FOOTER_BAR_COLOR = "footer_bar_color";

	public static String getConfigType() {
		return "student_identity_card_preferences";
	}

	private String instituteNameColor;

	private Float instituteNameFontSize;

	private String letterHead1Color;

	private Float letterHead1FontSize;

	private String letterHead2Color;

	private Float letterHead2FontSize;

	private Float instituteLogoHeight;

	private Float instituteLogoWidth;

	private String headerBackgroundColor;

	private Float studentImageHeight;

	private Float studentImageWidth;

	private String iCardDetailsTextColor;

	private String footerBarColor;

	private String sessionBackGroundColor;

	private String sessionFontColor;

	private Float sessionFontSize;

	public String getInstituteNameColor() {
		return instituteNameColor;
	}

	public void setInstituteNameColor(String instituteNameColor) {
		this.instituteNameColor = instituteNameColor;
	}

	public Float getInstituteNameFontSize() {
		return instituteNameFontSize;
	}

	public void setInstituteNameFontSize(Float instituteNameFontSize) {
		this.instituteNameFontSize = instituteNameFontSize;
	}

	public String getLetterHead1Color() {
		return letterHead1Color;
	}

	public void setLetterHead1Color(String letterHead1Color) {
		this.letterHead1Color = letterHead1Color;
	}

	public Float getLetterHead1FontSize() {
		return letterHead1FontSize;
	}

	public void setLetterHead1FontSize(Float letterHead1FontSize) {
		this.letterHead1FontSize = letterHead1FontSize;
	}

	public String getLetterHead2Color() {
		return letterHead2Color;
	}

	public void setLetterHead2Color(String letterHead2Color) {
		this.letterHead2Color = letterHead2Color;
	}

	public Float getLetterHead2FontSize() {
		return letterHead2FontSize;
	}

	public void setLetterHead2FontSize(Float letterHead2FontSize) {
		this.letterHead2FontSize = letterHead2FontSize;
	}

	public Float getInstituteLogoHeight() {
		return instituteLogoHeight;
	}

	public void setInstituteLogoHeight(Float instituteLogoHeight) {
		this.instituteLogoHeight = instituteLogoHeight;
	}

	public Float getInstituteLogoWidth() {
		return instituteLogoWidth;
	}

	public void setInstituteLogoWidth(Float instituteLogoWidth) {
		this.instituteLogoWidth = instituteLogoWidth;
	}

	public String getHeaderBackgroundColor() {
		return headerBackgroundColor;
	}

	public void setHeaderBackgroundColor(String headerBackgroundColor) {
		this.headerBackgroundColor = headerBackgroundColor;
	}

	public Float getStudentImageHeight() {
		return studentImageHeight;
	}

	public void setStudentImageHeight(Float studentImageHeight) {
		this.studentImageHeight = studentImageHeight;
	}

	public Float getStudentImageWidth() {
		return studentImageWidth;
	}

	public void setStudentImageWidth(Float studentImageWidth) {
		this.studentImageWidth = studentImageWidth;
	}

	public String getiCardDetailsTextColor() {
		return iCardDetailsTextColor;
	}

	public void setiCardDetailsTextColor(String iCardDetailsTextColor) {
		this.iCardDetailsTextColor = iCardDetailsTextColor;
	}

	public String getFooterBarColor() {
		return footerBarColor;
	}

	public void setFooterBarColor(String footerBarColor) {
		this.footerBarColor = footerBarColor;
	}

	public String getSessionBackGroundColor() {
		return sessionBackGroundColor;
	}

	public void setSessionBackGroundColor(String sessionBackGroundColor) {
		this.sessionBackGroundColor = sessionBackGroundColor;
	}

	public String getSessionFontColor() {
		return sessionFontColor;
	}

	public void setSessionFontColor(String sessionFontColor) {
		this.sessionFontColor = sessionFontColor;
	}

	public Float getSessionFontSize() {
		return sessionFontSize;
	}

	public void setSessionFontSize(Float sessionFontSize) {
		this.sessionFontSize = sessionFontSize;
	}

	@Override
	public String toString() {
		return "StudentIdentityCardPreferences{" +
				"instituteNameColor='" + instituteNameColor + '\'' +
				", instituteNameFontSize=" + instituteNameFontSize +
				", letterHead1Color='" + letterHead1Color + '\'' +
				", letterHead1FontSize=" + letterHead1FontSize +
				", letterHead2Color='" + letterHead2Color + '\'' +
				", letterHead2FontSize=" + letterHead2FontSize +
				", instituteLogoHeight=" + instituteLogoHeight +
				", instituteLogoWidth=" + instituteLogoWidth +
				", headerBackgroundColor='" + headerBackgroundColor + '\'' +
				", studentImageHeight=" + studentImageHeight +
				", studentImageWidth=" + studentImageWidth +
				", iCardDetailsTextColor='" + iCardDetailsTextColor + '\'' +
				", footerBarColor='" + footerBarColor + '\'' +
				", sessionBackGroundColor='" + sessionBackGroundColor + '\'' +
				", sessionFontColor='" + sessionFontColor + '\'' +
				", sessionFontSize='" + sessionFontSize + '\'' +
				'}';
	}
}
