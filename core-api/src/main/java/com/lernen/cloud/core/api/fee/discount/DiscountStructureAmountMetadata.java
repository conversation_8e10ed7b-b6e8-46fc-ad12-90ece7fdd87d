package com.lernen.cloud.core.api.fee.discount;

/**
 * <AUTHOR>
 * @created_at 05/04/24 : 14:48
 **/
public class DiscountStructureAmountMetadata implements Comparable<DiscountStructureAmountMetadata> {

    /**
     * specifically not adding discountStructureId as this class will be used in organization level report
     * Also multiple instant discount can be of same name (when assign to different students)
     */
    private final String discountStructureName;

    private final Double discountAmount;

    public DiscountStructureAmountMetadata(String discountStructureName, Double discountAmount) {
        this.discountStructureName = discountStructureName;
        this.discountAmount = discountAmount;
    }

    public String getDiscountStructureName() {
        return discountStructureName;
    }

    public Double getDiscountAmount() {
        return discountAmount;
    }

    @Override
    public String toString() {
        return "DiscountStructureAmountMetadata{" +
                "discountStructureName='" + discountStructureName + '\'' +
                ", discountAmount=" + discountAmount +
                '}';
    }

    @Override
    public int compareTo(DiscountStructureAmountMetadata o) {
        return this.discountStructureName.compareToIgnoreCase(o.discountStructureName);
    }
}
