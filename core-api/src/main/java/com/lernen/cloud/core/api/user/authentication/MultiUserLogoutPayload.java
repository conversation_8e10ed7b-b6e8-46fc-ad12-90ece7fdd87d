package com.lernen.cloud.core.api.user.authentication;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public class MultiUserLogoutPayload {

    private String appToken;

    private List<UUID> logoutUsers;

    private List<UUID> existingUsers;

    private Map<String, Object> deviceData;

    public String getAppToken() {
        return appToken;
    }

    public void setAppToken(String appToken) {
        this.appToken = appToken;
    }

    public List<UUID> getLogoutUsers() {
        return logoutUsers;
    }

    public void setLogoutUsers(List<UUID> logoutUsers) {
        this.logoutUsers = logoutUsers;
    }

    public List<UUID> getExistingUsers() {
        return existingUsers;
    }

    public void setExistingUsers(List<UUID> existingUsers) {
        this.existingUsers = existingUsers;
    }

    public Map<String, Object> getDeviceData() {
        return deviceData;
    }

    public void setDeviceData(Map<String, Object> deviceData) {
        this.deviceData = deviceData;
    }

    @Override
    public String toString() {
        return "MultiUserLogoutPayload{" +
                "appToken='" + appToken + '\'' +
                ", logoutUsers=" + logoutUsers +
                ", existingUsers=" + existingUsers +
                ", deviceData=" + deviceData +
                '}';
    }
}
