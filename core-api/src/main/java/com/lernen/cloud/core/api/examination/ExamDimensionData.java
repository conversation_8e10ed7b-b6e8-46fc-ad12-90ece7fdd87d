package com.lernen.cloud.core.api.examination;

import java.util.List;

/**
 * <AUTHOR>
 * @created_at 29/02/24 : 13:08
 **/
public class ExamDimensionData {

    private final ExamMetaData examMetaData;

    //This will contain list of dimension present in an exam for a particular course - ths course here is the one on CourseMarksFeedExamWithDimensions, always use this payload inside CourseMarksFeedExamWithDimensions only.
    private final List<ExamDimension> examDimensionList;
    private final ExamCoursePublishedStatus examCoursePublishedStatus;

    public ExamDimensionData(ExamMetaData examMetaData, List<ExamDimension> examDimensionList, ExamCoursePublishedStatus examCoursePublishedStatus) {
        this.examMetaData = examMetaData;
        this.examDimensionList = examDimensionList;
        this.examCoursePublishedStatus = examCoursePublishedStatus;
    }

    public ExamMetaData getExamMetaData() {
        return examMetaData;
    }

    public List<ExamDimension> getExamDimensionList() {
        return examDimensionList;
    }

    public ExamCoursePublishedStatus getExamCoursePublishedStatus() {
        return examCoursePublishedStatus;
    }

    @Override
    public String toString() {
        return "ExamDimensionData{" +
                "examMetaData=" + examMetaData +
                ", examDimensionList=" + examDimensionList +
                ", examCoursePublishedStatus=" + examCoursePublishedStatus +
                '}';
    }
}
