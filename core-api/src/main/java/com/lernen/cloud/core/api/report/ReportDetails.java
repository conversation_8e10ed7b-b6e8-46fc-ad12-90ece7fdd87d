/**
 * 
 */
package com.lernen.cloud.core.api.report;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class ReportDetails {
	
	private final String reportName;
	
	private final List<ReportSheetDetails> reportSheetDetailsList;

	/**
	 * @param reportName
	 * @param reportSheetDetailsList
	 */
	public ReportDetails(String reportName,
			List<ReportSheetDetails> reportSheetDetailsList) {
		this.reportName = reportName;
		this.reportSheetDetailsList = reportSheetDetailsList;
	}

	/**
	 * @return the reportName
	 */
	public String getReportName() {
		return reportName;
	}

	/**
	 * @return the reportSheetDetailsList
	 */
	public List<ReportSheetDetails> getReportSheetDetailsList() {
		return reportSheetDetailsList;
	}

	@Override
	public String toString() {
		return "ReportDetails [reportName=" + reportName
				+ ", reportSheetDetailsList=" + reportSheetDetailsList + "]";
	}

}
