package com.lernen.cloud.core.api.inventory;

import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class BrandWithCategoryRow extends BrandRow {

	private final Category category;

	public BrandWithCategoryRow(int instituteId, UUID brandId, String brandName, Category category, int createdAt,
			int updatedAt) {
		super(instituteId, brandId, brandName, createdAt, updatedAt);
		this.category = category;
	}

	public Category getCategory() {
		return category;
	}

	@Override
	public String toString() {
		return "BrandWithCategoryRow [category=" + category + "]";
	}

}
