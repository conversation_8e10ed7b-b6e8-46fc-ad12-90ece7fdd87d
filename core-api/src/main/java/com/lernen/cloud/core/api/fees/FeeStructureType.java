package com.lernen.cloud.core.api.fees;

import org.apache.commons.lang3.StringUtils;

/**
 * 
 * <AUTHOR>
 *
 */
public enum FeeStructureType {

	REGISTRATION, ENROLLMENT, ONLINE_STUDENT_REGISTRATION;
	
	public static FeeStructureType getFeeStructureType(String feeStructureType) {
		if (StringUtils.isBlank(feeStructureType)) {
			return null;
		}
		for (FeeStructureType feeStructureTypeEnum : FeeStructureType.values()) {
			if (feeStructureTypeEnum.name().equalsIgnoreCase(feeStructureType)) {
				return feeStructureTypeEnum;
			}
		}
		return null;
	}
}
