package com.lernen.cloud.core.api.examination;

import com.lernen.cloud.core.api.institute.Time;

public class DimensionWiseDatesheetDetails implements Comparable<DimensionWiseDatesheetDetails> {

    private ExamDimension examDimension;

    private Integer courseExamDate;

    private Time startTime;

    private Time endTime;

    private String syllabus;

    public DimensionWiseDatesheetDetails(ExamDimension examDimension, Integer courseExamDate, Time startTime,
                                         Time endTime, String syllabus) {
        this.examDimension = examDimension;
        this.courseExamDate = courseExamDate;
        this.startTime = startTime;
        this.endTime = endTime;
        this.syllabus = syllabus;
    }

    public ExamDimension getExamDimension() {
        return examDimension;
    }

    public void setExamDimension(ExamDimension examDimension) {
        this.examDimension = examDimension;
    }

    public Integer getCourseExamDate() {
        return courseExamDate;
    }

    public void setCourseExamDate(Integer courseExamDate) {
        this.courseExamDate = courseExamDate;
    }

    public Time getStartTime() {
        return startTime;
    }

    public void setStartTime(Time startTime) {
        this.startTime = startTime;
    }

    public Time getEndTime() {
        return endTime;
    }

    public void setEndTime(Time endTime) {
        this.endTime = endTime;
    }

    public String getSyllabus() {
        return syllabus;
    }

    public void setSyllabus(String syllabus) {
        this.syllabus = syllabus;
    }

    @Override
    public String toString() {
        return "DimensionWiseDatesheetDetails{" +
                "examDimension=" + examDimension +
                ", courseExamDate=" + courseExamDate +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", syllabus='" + syllabus + '\'' +
                '}';
    }

    @Override
    public int compareTo(DimensionWiseDatesheetDetails dimensionWiseDatesheetDetails) {
        if (this.courseExamDate == dimensionWiseDatesheetDetails.courseExamDate) {
            return this.startTime.compareTo(dimensionWiseDatesheetDetails.startTime);
        } else if (this.courseExamDate < dimensionWiseDatesheetDetails.courseExamDate) {
            return -1;
        } else {
            return 1;
        }
    }
}
