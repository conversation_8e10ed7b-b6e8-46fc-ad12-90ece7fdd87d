package com.lernen.cloud.core.api.user.biometric;

import com.embrate.cloud.core.api.attendance.AttendanceDeviceServiceProviderType;

/**
 * <AUTHOR>
 */

public class FaceData {

    private AttendanceDeviceServiceProviderType serviceProvider;
    private String data;

    private String size;

    private String index;

    private Long opTime;

    public FaceData() {
    }

    public FaceData(AttendanceDeviceServiceProviderType serviceProvider, String data, String size, String index, Long opTime) {
        this.serviceProvider = serviceProvider;
        this.data = data;
        this.size = size;
        this.index = index;
        this.opTime = opTime;
    }

    public AttendanceDeviceServiceProviderType getServiceProvider() {
        return serviceProvider;
    }

    public void setServiceProvider(AttendanceDeviceServiceProviderType serviceProvider) {
        this.serviceProvider = serviceProvider;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public Long getOpTime() {
        return opTime;
    }

    public void setOpTime(Long opTime) {
        this.opTime = opTime;
    }

    @Override
    public String toString() {
        return "FaceData{" +
                "serviceProvider=" + serviceProvider +
                ", data='" + data + '\'' +
                ", size='" + size + '\'' +
                ", index='" + index + '\'' +
                ", opTime=" + opTime +
                '}';
    }
}
