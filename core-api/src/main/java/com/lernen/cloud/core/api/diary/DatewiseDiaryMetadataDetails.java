package com.lernen.cloud.core.api.diary;

import java.util.List;

/**
 * <AUTHOR>
 * @created_at 02/11/23 : 22:44
 **/
public class DatewiseDiaryMetadataDetails implements Comparable<DatewiseDiaryMetadataDetails> {

    private final int date;

    private final List<DiaryRemarkMetadata> diaryRemarkMetadataList;

    public DatewiseDiaryMetadataDetails(int date, List<DiaryRemarkMetadata> diaryRemarkMetadataList) {
        this.date = date;
        this.diaryRemarkMetadataList = diaryRemarkMetadataList;
    }

    public int getDate() {
        return date;
    }

    public List<DiaryRemarkMetadata> getDiaryRemarkMetadataList() {
        return diaryRemarkMetadataList;
    }

    @Override
    public String toString() {
        return "DatewiseDiaryMetatdataDetails{" +
                "date=" + date +
                ", diaryRemarkMetadataList=" + diaryRemarkMetadataList +
                '}';
    }

    @Override
    public int compareTo(DatewiseDiaryMetadataDetails o) {
        return Integer.compare(o.date, this.date);
    }
}