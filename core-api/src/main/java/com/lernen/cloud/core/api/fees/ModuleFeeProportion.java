package com.lernen.cloud.core.api.fees;

import com.lernen.cloud.core.api.user.Module;

/**
 * 
 * <AUTHOR>
 *
 */
public class ModuleFeeProportion {

	private Module module;

	private Double feeProportionNumerator;

	private Double feeProportionDenominator;

	public ModuleFeeProportion() {
	}

	public ModuleFeeProportion(Module module, Double feeProportionNumerator, Double feeProportionDenominator) {
		this.module = module;
		this.feeProportionNumerator = feeProportionNumerator;
		this.feeProportionDenominator = feeProportionDenominator;
	}
	
	public Double getFeeProportion() {
		if (feeProportionNumerator == null || feeProportionNumerator < 0
				|| feeProportionDenominator == null || feeProportionDenominator <= 0) {
			return null;
		}
		return feeProportionNumerator / feeProportionDenominator;
	}

	public Module getModule() {
		return module;
	}

	public void setModule(Module module) {
		this.module = module;
	}

	public Double getFeeProportionNumerator() {
		return feeProportionNumerator;
	}

	public void setFeeProportionNumerator(Double feeProportionNumerator) {
		this.feeProportionNumerator = feeProportionNumerator;
	}

	public Double getFeeProportionDenominator() {
		return feeProportionDenominator;
	}

	public void setFeeProportionDenominator(Double feeProportionDenominator) {
		this.feeProportionDenominator = feeProportionDenominator;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((module == null) ? 0 : module.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ModuleFeeProportion other = (ModuleFeeProportion) obj;
		if (module != other.module)
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ModuleFeeProportion [module=" + module + ", feeProportionNumerator=" + feeProportionNumerator
				+ ", feeProportionDenominator=" + feeProportionDenominator + "]";
	}

}
