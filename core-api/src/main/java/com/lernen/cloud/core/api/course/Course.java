package com.lernen.cloud.core.api.course;

import org.springframework.util.CollectionUtils;

import java.util.*;

public class Course implements Comparable<Course> {

	private UUID courseId;

	private String courseKey;

	private CourseType courseType;

	private String courseName;

	private boolean mandatory;

	private Integer sequence;

	public Course(String courseId, String courseKey, CourseType courseType, String courseName, Boolean mandatory,
				  Integer sequence) {
		this.courseId = UUID.fromString(courseId);
		this.courseKey = courseKey;
		this.courseType = courseType;
		this.courseName = courseName;
		this.mandatory = mandatory;
		this.sequence = sequence;
	}



	public Course() {

	}

	public UUID getCourseId() {
		return courseId;
	}

	public void setCourseId(UUID courseId) {
		this.courseId = courseId;
	}

	public String getCourseKey() {
		return courseKey;
	}

	public void setCourseKey(String courseKey) {
		this.courseKey = courseKey;
	}

	public CourseType getCourseType() {
		return courseType;
	}

	public String getCourseTypeDisplay() {
		return courseType.getDisplayName();
	}

	public void setCourseType(CourseType courseType) {
		this.courseType = courseType;
	}

	public String getCourseName() {
		return courseName;
	}

	public void setCourseName(String courseName) {
		this.courseName = courseName;
	}

	public boolean isMandatory() {
		return mandatory;
	}

	public void setMandatory(boolean mandatory) {
		this.mandatory = mandatory;
	}

	public Integer getSequence() {
		return sequence;
	}

	public void setSequence(Integer sequence) {
		this.sequence = sequence;
	}

	@Override
	public String toString() {
		return "Course [courseId=" + courseId + ", courseKey=" + courseKey + ", courseType=" + courseType
				+ ", courseName=" + courseName + ", mandatory=" + mandatory + ", sequence=" + sequence + "]";
	}

//	@Override
//	public int compareTo(Course o) {
//		if (this.getCourseType().getPriority() != o.getCourseType().getPriority()) {
//			return this.getCourseType().getPriority() - o.getCourseType().getPriority();
//		}
//		// return StringUtils.compareIgnoreCase(this.courseName, o.courseName);
//		return this.courseName.compareToIgnoreCase(o.courseName);
//	}

	@Override
	public int compareTo(Course o) {
		if(o.sequence == null && this.sequence == null) {
			return compareCourseName(o);
		}

		if(this.sequence == null) {
			return 1;
		}

		if(o.sequence == null) {
			return -1;
		}

		if (this.sequence.equals(o.sequence)) {
			return compareCourseName(o);
		}

		if (this.sequence < o.sequence) {
			return -1;
		}

		return 1;
	}

	public int compareCourseName(Course o) {
		return this.courseName.compareToIgnoreCase(o.courseName);
	}

	public static List<Course> sortCoursesBySequence(List<Course> courseList) {
		if(CollectionUtils.isEmpty(courseList)) {
			return new ArrayList<>();
		}
		Collections.sort(courseList);
		return courseList;
	}


}