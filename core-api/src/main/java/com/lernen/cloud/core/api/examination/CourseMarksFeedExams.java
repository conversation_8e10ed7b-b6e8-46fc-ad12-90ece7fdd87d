package com.lernen.cloud.core.api.examination;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import com.lernen.cloud.core.api.course.Course;
import org.apache.commons.collections.CollectionUtils;

/**
 * 
 * <AUTHOR>
 *
 */
public class CourseMarksFeedExams {
	private final Course course;

	private final List<ExamMetaData> examMetaDatas;

	public CourseMarksFeedExams(Course course, List<ExamMetaData> examMetaDatas) {
		this.course = course;
		this.examMetaDatas = sortExamByName(examMetaDatas);
	}

	public Course getCourse() {
		return course;
	}

	public List<ExamMetaData> getExamMetaDatas() {
		return examMetaDatas;
	}

	public static List<ExamMetaData> sortExamByName(List<ExamMetaData> examMetaDataList) {
		if(CollectionUtils.isEmpty(examMetaDataList)) {
			return null;
		}
		Collections.sort(examMetaDataList, new Comparator<ExamMetaData>() {
			@Override
			public int compare(ExamMetaData e1, ExamMetaData e2) {
				return e1.getExamName().compareToIgnoreCase(e2.getExamName());
			}
		});
		return examMetaDataList;
	}

}
