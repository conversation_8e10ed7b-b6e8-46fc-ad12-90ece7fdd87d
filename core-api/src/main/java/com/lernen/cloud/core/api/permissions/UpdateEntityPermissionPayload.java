package com.lernen.cloud.core.api.permissions;

import java.util.List;

public class UpdateEntityPermissionPayload {

    Integer instituteId;

    List<AuthorisationRequiredAction> existingAuthorisationRequiredActionList;

    List<AuthorisationRequiredAction> newAuthorisationRequiredActionList;

    public UpdateEntityPermissionPayload() {
    }

    public UpdateEntityPermissionPayload(Integer instituteId,
                                         List<AuthorisationRequiredAction> existingAuthorisationRequiredActionList,
                                         List<AuthorisationRequiredAction> newAuthorisationRequiredActionList) {
        this.instituteId = instituteId;
        this.existingAuthorisationRequiredActionList = existingAuthorisationRequiredActionList;
        this.newAuthorisationRequiredActionList = newAuthorisationRequiredActionList;
    }

    public Integer getInstituteId() {
        return instituteId;
    }

    public void setInstituteId(Integer instituteId) {
        this.instituteId = instituteId;
    }

    public List<AuthorisationRequiredAction> getExistingAuthorisationRequiredActionList() {
        return existingAuthorisationRequiredActionList;
    }

    public void setExistingAuthorisationRequiredActionList(List<AuthorisationRequiredAction> existingAuthorisationRequiredActionList) {
        this.existingAuthorisationRequiredActionList = existingAuthorisationRequiredActionList;
    }

    public List<AuthorisationRequiredAction> getNewAuthorisationRequiredActionList() {
        return newAuthorisationRequiredActionList;
    }

    public void setNewAuthorisationRequiredActionList(List<AuthorisationRequiredAction> newAuthorisationRequiredActionList) {
        this.newAuthorisationRequiredActionList = newAuthorisationRequiredActionList;
    }

    @Override
    public String toString() {
        return "UpdateEntityPermissionPayload{" +
                "instituteId=" + instituteId +
                ", existingAuthorisationRequiredActionList=" + existingAuthorisationRequiredActionList +
                ", newAuthorisationRequiredActionList=" + newAuthorisationRequiredActionList +
                '}';
    }
}
