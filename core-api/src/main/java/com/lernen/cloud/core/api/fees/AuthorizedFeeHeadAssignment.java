package com.lernen.cloud.core.api.fees;

import java.util.HashSet;
import java.util.Set;

import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.user.Module;

/**
 * 
 * <AUTHOR>
 *
 */
public class AuthorizedFeeHeadAssignment {

	private final FeeHeadConfigurationResponse feeHeadConfigurationResponse;

	private final Set<ModuleFeeProportion> moduleFeeProportions;

	public AuthorizedFeeHeadAssignment(FeeHeadConfigurationResponse feeHeadConfigurationResponse,
			Set<ModuleFeeProportion> moduleFeeProportions) {
		this.feeHeadConfigurationResponse = feeHeadConfigurationResponse;
		this.moduleFeeProportions = moduleFeeProportions;
	}

	public FeeHeadConfigurationResponse getFeeHeadConfigurationResponse() {
		return feeHeadConfigurationResponse;
	}

	public Set<ModuleFeeProportion> getModuleFeeProportions() {
		return moduleFeeProportions;
	}

	/**
	 * Do not remove this, FE uses this method for controlling permissions
	 * 
	 * @return
	 */
	public Set<Module> getModules() {
		Set<Module> modules = new HashSet<>();
		if (CollectionUtils.isEmpty(moduleFeeProportions)) {
			return modules;
		}
		for (ModuleFeeProportion moduleFeeProportion : moduleFeeProportions) {
			if (moduleFeeProportion != null && moduleFeeProportion.getModule() != null) {
				modules.add(moduleFeeProportion.getModule());
			}
		}
		return modules;
	}

	@Override
	public String toString() {
		return "AuthorizedFeeHeadAssginment [feeHeadConfigurationResponse=" + feeHeadConfigurationResponse
				+ ", moduleFeeProportions=" + moduleFeeProportions + "]";
	}

}
