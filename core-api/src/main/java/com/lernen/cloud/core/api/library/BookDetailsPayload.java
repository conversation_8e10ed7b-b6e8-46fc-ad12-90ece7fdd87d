package com.lernen.cloud.core.api.library;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.user.Document;

/**
 * 
 * <AUTHOR>
 * @updated-by: vasavmittal 
 */
public class BookDetailsPayload {

	private UUID bookId;

	private int instituteId;

	private UUID genreId;

	private String bookTitle;

	private String bookNo;

	private UUID authorId;

	private String isbn;

	private UUID publicationId;

	private UUID publisherId;

	private String edition;

	private int noOfCopies;

	private int publishYear;

	private String language;

	private List<Document<BookDocumentType>> bookDocument;

	private Document<BookDocumentType> coverImage;

	private String typeOfBinding;

	private int numberOfPages;

	private Integer addedAt;

	private Integer updatedAt;

	private byte[] thumbnail;

	public BookDetailsPayload(UUID bookId, int instituteId, UUID genreId, String bookTitle, UUID authorId, String isbn,
			UUID publicationId, UUID publisherId, String edition, int noOfCopies, int publishYear, String language, String rack,  List<Document<BookDocumentType>> bookDocument,
			String bookNo, String typeOfBinding, int numberOfPages, Integer addedAt, Integer updatedAt) {
		this.bookId = bookId;
		this.instituteId = instituteId;
		this.genreId = genreId;
		this.bookTitle = bookTitle;
		this.authorId = authorId;
		this.isbn = isbn;
		this.publicationId = publicationId;
		this.edition = edition;
		this.noOfCopies = noOfCopies;
		this.publishYear = publishYear;
		this.language = language;
		this.bookDocument = bookDocument;
		this.coverImage = getImageDocument();
		this.bookNo = bookNo;
		this.addedAt = addedAt;
		this.updatedAt = updatedAt;
		this.typeOfBinding = typeOfBinding;
		this.numberOfPages = numberOfPages;
		this.publisherId = publisherId;
	}

	public BookDetailsPayload() {

	}

	public UUID getBookId() {
		return bookId;
	}

	public int getInstituteId() {
		return instituteId;
	}

	public int getNoOfCopies() {
		return noOfCopies;
	}

	public void setNoOfCopies(int noOfCopies) {
		this.noOfCopies = noOfCopies;
	}

	public String getBookNo() {
		return bookNo;
	}

	public void setBookNo(String bookNo) {
		this.bookNo = bookNo;
	}

	public List<Document<BookDocumentType>> getBookDocument() {
		return bookDocument;
	}

	public void setBookDocument(List<Document<BookDocumentType>> bookDocument) {
		this.bookDocument = bookDocument;
	}

	public UUID getGenreId() {
		return genreId;
	}

	public String getBookTitle() {
		return bookTitle;
	}

	public UUID getAuthorId() {
		return authorId;
	}

	public String getIsbn() {
		return isbn;
	}

	public UUID getPublicationId() {
		return publicationId;
	}

	public String getEdition() {
		return edition;
	}

	public int getPublishYear() {
		return publishYear;
	}

	public String getLanguage() {
		return language;
	}
	
	public Document<BookDocumentType> getCoverImage() {
		return coverImage;
	}

	public Integer getAddedAt() {
		return addedAt;
	}

	public Integer getUpdatedAt() {
		return updatedAt;
	}

	public void setBookId(UUID bookGroupId) {
		this.bookId = bookGroupId;
	}

	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	public void setGenreId(UUID category) {
		this.genreId = category;
	}

	public void setBookTitle(String bookName) {
		this.bookTitle = bookName;
	}

	public void setAuthorId(UUID authorId) {
		this.authorId = authorId;
	}

	public void setIsbn(String isbn) {
		this.isbn = isbn;
	}

	public void setPublicationId(UUID publicationId) {
		this.publicationId = publicationId;
	}

	public void setEdition(String edition) {
		this.edition = edition;
	}

	public void setPublishYear(int publishYear) {
		this.publishYear = publishYear;
	}

	public void setLanguage(String language) {
		this.language = language;
	}

	public void setCoverImage(Document<BookDocumentType> coverImage) {
		this.coverImage = coverImage;
	}

	public void setAddedAt(Integer addedAt) {
		this.addedAt = addedAt;
	}

	public void setUpdatedAt(Integer updatedAt) {
		this.updatedAt = updatedAt;
	}

	private Document<BookDocumentType> getImageDocument() {
		if (CollectionUtils.isEmpty(bookDocument)) {
			return null;
		}
		for (Document<BookDocumentType> bookDocument : bookDocument) {
			if (bookDocument
					.getDocumentType() == BookDocumentType.BOOK_COVER_IMAGE) {
				return bookDocument;
			}
		}
		return null;
	}

	public byte[] getThumbnail() {
		return thumbnail;
	}

	public void setThumbnail(byte[] thumbnail) {
		this.thumbnail = thumbnail;
	}
	
	public String getTypeOfBinding() {
		return typeOfBinding;
	}

	public void setTypeOfBinding(String typeOfBinding) {
		this.typeOfBinding = typeOfBinding;
	}

	public int getNumberOfPages() {
		return numberOfPages;
	}

	public void setNumberOfPages(int numberOfPages) {
		this.numberOfPages = numberOfPages;
	}

	public UUID getPublisherId() {
		return publisherId;
	}

	public void setPublisherId(UUID publisherId) {
		this.publisherId = publisherId;
	}

	@Override
	public String toString() {
		return "BookDetails [bookId=" + bookId + ", instituteId=" + instituteId + ", genreId=" + genreId
				+ ", bookTitle=" + bookTitle + ", bookNo=" + bookNo + ", authorId=" + authorId + ", isbn=" + isbn
				+ ", publicationId=" + publicationId + ", publisherId=" + publisherId + ", edition=" + edition
				+ ", noOfCopies=" + noOfCopies + ", publishYear=" + publishYear + ", language=" + language
				+ ", bookDocument=" + bookDocument + ", coverImage=" + coverImage
				+ ", typeOfBinding=" + typeOfBinding + ", numberOfPages=" + numberOfPages + ", addedAt=" + addedAt
				+ ", updatedAt=" + updatedAt + ", thumbnail=" + Arrays.toString(thumbnail) + "]";
	}

}
