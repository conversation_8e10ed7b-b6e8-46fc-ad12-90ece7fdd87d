/**
 * 
 */
package com.lernen.cloud.core.api.transport;

/**
 * <AUTHOR>
 *
 */
public class TransportAreaAmount {
	
	private int instituteId;
	
	private int academicSessionId;
	
	private TransportArea area;
	
	private VehicleType vehicleType;
	
	private Double amount;

	/**
	 * @param instituteId
	 * @param academicSessionId
	 * @param area
	 * @param vehicleType
	 * @param amount
	 */
	public TransportAreaAmount(int instituteId, int academicSessionId, TransportArea area, VehicleType vehicleType,
							   Double amount) {
		this.instituteId = instituteId;
		this.academicSessionId = academicSessionId;
		this.area = area;
		this.vehicleType = vehicleType;
		this.amount = amount;
	}

	/**
	 * @return the instituteId
	 */
	public int getInstituteId() {
		return instituteId;
	}

	/**
	 * @param instituteId the instituteId to set
	 */
	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	/**
	 * @return the academicSessionId
	 */
	public int getAcademicSessionId() {
		return academicSessionId;
	}

	/**
	 * @param academicSessionId the academicSessionId to set
	 */
	public void setAcademicSessionId(int academicSessionId) {
		this.academicSessionId = academicSessionId;
	}

	/**
	 * @return the area
	 */
	public TransportArea getArea() {
		return area;
	}

	/**
	 * @param area the area to set
	 */
	public void setArea(TransportArea area) {
		this.area = area;
	}

	/**
	 * @return the vehicleType
	 */
	public VehicleType getVehicleType() {
		return vehicleType;
	}

	/**
	 * @param vehicleType the vehicleType to set
	 */
	public void setVehicleType(VehicleType vehicleType) {
		this.vehicleType = vehicleType;
	}

	/**
	 * @return the amount
	 */
	public Double getAmount() {
		return amount;
	}

	/**
	 * @param amount the amount to set
	 */
	public void setAmount(Double amount) {
		this.amount = amount;
	}

	@Override
	public String toString() {
		return "TransportAreaAmount [instituteId=" + instituteId + ", academicSessionId=" + academicSessionId
				+ ", area=" + area + ", vehicleType=" + vehicleType + ", amount=" + amount + "]";
	}
}
