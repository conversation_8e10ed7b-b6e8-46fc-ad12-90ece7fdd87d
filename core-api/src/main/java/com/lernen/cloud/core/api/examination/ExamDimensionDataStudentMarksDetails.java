package com.lernen.cloud.core.api.examination;

import java.util.List;
public class ExamDimensionDataStudentMarksDetails {
    private final List<ExamDimensionValues> examDimensionValues;

    private final List<ExamGrade> examGrade;

    private final List<StudentMarksDetailsDimensionsData> studentMarksDetailsList;

    public ExamDimensionDataStudentMarksDetails(List<ExamDimensionValues> examDimensionValues, List<ExamGrade> examGrade, List<StudentMarksDetailsDimensionsData> studentMarksDetailsList) {
        this.examDimensionValues = examDimensionValues;
        this.examGrade = examGrade;
        this.studentMarksDetailsList = studentMarksDetailsList;
    }

    public List<ExamDimensionValues> getExamDimensionValues() {
        return examDimensionValues;
    }

    public List<ExamGrade> getExamGrade() {
        return examGrade;
    }

    public List<StudentMarksDetailsDimensionsData> getStudentMarksDetailsList() {
        return studentMarksDetailsList;
    }

    @Override
    public String toString() {
        return "ExamDimensionStudentMarksDetails{" +
                "examDimensionValues=" + examDimensionValues +
                ", examGrade=" + examGrade +
                ", studentMarksDetailsList=" + studentMarksDetailsList +
                '}';
    }
}