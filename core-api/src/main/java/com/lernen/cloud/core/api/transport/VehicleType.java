package com.lernen.cloud.core.api.transport;

public class VehicleType {
	
	private int instituteId;
	
	private int vehicleTypeId;
	
	private String vehicleTypeName;

	/**
	 * 
	 */
	public VehicleType() {
	}

	/**
	 * @param instituteId
	 * @param vehicleTypeId
	 * @param vehicleTypeName
	 */
	public VehicleType(int instituteId, int vehicleTypeId, String vehicleTypeName) {
		this.instituteId = instituteId;
		this.vehicleTypeId = vehicleTypeId;
		this.vehicleTypeName = vehicleTypeName;
	}

	/**
	 * @return the instituteId
	 */
	public int getInstituteId() {
		return instituteId;
	}

	/**
	 * @param instituteId the instituteId to set
	 */
	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	/**
	 * @return the vehicleTypeId
	 */
	public int getVehicleTypeId() {
		return vehicleTypeId;
	}

	/**
	 * @param vehicleTypeId the vehicleTypeId to set
	 */
	public void setVehicleTypeId(int vehicleTypeId) {
		this.vehicleTypeId = vehicleTypeId;
	}

	/**
	 * @return the vehicleTypeName
	 */
	public String getVehicleTypeName() {
		return vehicleTypeName;
	}

	/**
	 * @param vehicleTypeName the vehicleTypeName to set
	 */
	public void setVehicleTypeName(String vehicleTypeName) {
		this.vehicleTypeName = vehicleTypeName;
	}

	@Override
	public String toString() {
		return "VehicleType [instituteId=" + instituteId + ", vehicleTypeId=" + vehicleTypeId + ", vehicleTypeName="
				+ vehicleTypeName + "]";
	}
}
