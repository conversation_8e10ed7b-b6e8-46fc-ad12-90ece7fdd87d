package com.lernen.cloud.core.api.student;

import com.lernen.cloud.core.api.fees.EntityDiscountStructurePayload;
import com.lernen.cloud.core.api.transport.TransportAssignmentPayload;

import java.util.List;
import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class EnrollStudentPayload {

	private UUID studentId;
	private String admissionNumber;
	private Integer admissionDate;
	private UUID houseId;
	private UUID siblingStudentId;
	private List<UUID> feeStructureIds;
	private List<UUID> optionalCourseIds;
	private boolean sendNotification;
	private boolean sendEmail;
	private List<UUID> discountStructureIds;
	private EntityDiscountStructurePayload entityDiscountStructurePayload;
	private TransportAssignmentPayload transportAssignmentPayload;
	private UUID holidayTemplateId;
	public EnrollStudentPayload() {
	}

	public UUID getHolidayTemplateId() {
		return holidayTemplateId;
	}

	public void setHolidayTemplateId(UUID templateId) {
		this.holidayTemplateId = templateId;
	}

	public UUID getStudentId() {
		return studentId;
	}

	public void setStudentId(UUID studentId) {
		this.studentId = studentId;
	}

	public String getAdmissionNumber() {
		return admissionNumber;
	}

	public void setAdmissionNumber(String admissionNumber) {
		this.admissionNumber = admissionNumber;
	}

	public Integer getAdmissionDate() {
		return admissionDate;
	}

	public void setAdmissionDate(Integer admissionDate) {
		this.admissionDate = admissionDate;
	}

	public List<UUID> getFeeStructureIds() {
		return feeStructureIds;
	}

	public void setFeeStructureIds(List<UUID> feeStructureIds) {
		this.feeStructureIds = feeStructureIds;
	}

	public List<UUID> getOptionalCourseIds() {
		return optionalCourseIds;
	}

	public void setOptionalCourseIds(List<UUID> optionalCourseIds) {
		this.optionalCourseIds = optionalCourseIds;
	}

	public boolean isSendNotification() {
		return sendNotification;
	}

	public void setSendNotification(boolean sendNotification) {
		this.sendNotification = sendNotification;
	}

	public boolean isSendEmail() {
		return sendEmail;
	}

	public void setSendEmail(boolean sendEmail) {
		this.sendEmail = sendEmail;
	}

	public List<UUID> getDiscountStructureIds() {
		return discountStructureIds;
	}

	public void setDiscountStructureIds(List<UUID> discountStructureIds) {
		this.discountStructureIds = discountStructureIds;
	}

	public EntityDiscountStructurePayload getEntityDiscountStructurePayload() {
		return entityDiscountStructurePayload;
	}

	public void setEntityDiscountStructurePayload(EntityDiscountStructurePayload entityDiscountStructurePayload) {
		this.entityDiscountStructurePayload = entityDiscountStructurePayload;
	}

	public TransportAssignmentPayload getTransportAssignmentPayload() {
		return transportAssignmentPayload;
	}

	public void setTransportAssignmentPayload(TransportAssignmentPayload transportAssignmentPayload) {
		this.transportAssignmentPayload = transportAssignmentPayload;
	}

	public UUID getHouseId() {
		return houseId;
	}

	public void setHouseId(UUID houseId) {
		this.houseId = houseId;
	}

	public UUID getSiblingStudentId() {
		return siblingStudentId;
	}

	public void setSiblingStudentId(UUID siblingStudentId) {
		this.siblingStudentId = siblingStudentId;
	}

	@Override
	public String toString() {
		return "EnrollStudentPayload{" +
				"studentId=" + studentId +
				", admissionNumber='" + admissionNumber + '\'' +
				", admissionDate=" + admissionDate +
				", feeStructureIds=" + feeStructureIds +
				", optionalCourseIds=" + optionalCourseIds +
				", sendNotification=" + sendNotification +
				", discountStructureIds=" + discountStructureIds +
				", entityDiscountStructurePayload=" + entityDiscountStructurePayload +
				", transportAssignmentPayload=" + transportAssignmentPayload +
				", holidayTemplateId="+ holidayTemplateId +
				'}';
	}

}
