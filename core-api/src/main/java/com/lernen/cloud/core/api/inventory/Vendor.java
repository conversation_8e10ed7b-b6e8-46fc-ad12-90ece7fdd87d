package com.lernen.cloud.core.api.inventory;

/**
 * 
 * <AUTHOR>
 *
 */
public class Vendor {
		
	private  int instituteId;
	
	private int vendorId;
	
	private String contactName;
	
	private String vendorName;
	
	private String address;
	
	private  String city; 
	
	private  String state;
	
	private  String zipcode;
	
	private  String country;
	
	private String email;
	
	private String primaryPhoneNumber;
	
	private String secondPhoneNumber;
	
	private String landlineNumber;
		
	public Vendor (int instituteId, int vendorId, String contactName, String vendorName, String address, 
			String city, String state, String zipcode, String country, String email, String primaryPhoneNumber, 
			String secondPhoneNumber, String landlineNumber) {
		
		this.instituteId = instituteId;
		this.vendorId = vendorId;
		this.contactName = contactName;
		this.vendorName = vendorName;
		this.address = address;
		this.city = city; 
		this.state = state;
		this.zipcode = zipcode;
		this.country = country;
		this.email = email;
		this.primaryPhoneNumber = primaryPhoneNumber;
		this.secondPhoneNumber = secondPhoneNumber;
		this.landlineNumber = landlineNumber;
	}
	
	public Vendor() {
	}

	public int getInstituteId() {
		return instituteId;
	}

	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	public int getVendorId() {
		return vendorId;
	}

	public void setVendorId(int vendorId) {
		this.vendorId = vendorId;
	}

	public String getContactName() {
		return contactName;
	}

	public void setContactName(String contactName) {
		this.contactName = contactName;
	}

	public String getVendorName() {
		return vendorName;
	}

	public void setVendorName(String vendorName) {
		this.vendorName = vendorName;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getZipcode() {
		return zipcode;
	}

	public void setZipcode(String zipcode) {
		this.zipcode = zipcode;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getPrimaryPhoneNumber() {
		return primaryPhoneNumber;
	}

	public void setPrimaryPhoneNumber(String primaryPhoneNumber) {
		this.primaryPhoneNumber = primaryPhoneNumber;
	}

	public String getSecondPhoneNumber() {
		return secondPhoneNumber;
	}

	public void setSecondPhoneNumber(String secondPhoneNumber) {
		this.secondPhoneNumber = secondPhoneNumber;
	}

	public String getLandlineNumber() {
		return landlineNumber;
	}

	public void setLandlineNumber(String landlineNumber) {
		this.landlineNumber = landlineNumber;
	}

	@Override
	public String toString() {
		return "Vendor [instituteId=" + instituteId + ", vendorId=" + vendorId + ", contactName=" + contactName
				+ ", vendorName=" + vendorName + ", address=" + address + ", city=" + city + ", state=" + state
				+ ", zipcode=" + zipcode + ", country=" + country + ", email=" + email + ", primaryPhoneNumber="
				+ primaryPhoneNumber + ", secondPhoneNumber=" + secondPhoneNumber + ", landlineNumber=" + landlineNumber
				+ "]";
	}
	
	
}
