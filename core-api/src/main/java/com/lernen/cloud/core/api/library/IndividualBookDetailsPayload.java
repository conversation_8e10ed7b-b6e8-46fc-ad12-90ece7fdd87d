package com.lernen.cloud.core.api.library;

import java.util.UUID;
public class IndividualBookDetailsPayload {

    private UUID accessionId;
    private int instituteId;
    private UUID bookId;
    private String accessionNumber;
    private String rack;
    private double price;
    private String billNumber;
    private Integer dateOfPurchase;
    private UUID vendorId;
    private BookStatus status;
    private String volume;
    private UUID libraryTypeId;
    private String remarks;

    public IndividualBookDetailsPayload(){

    }

    public IndividualBookDetailsPayload(UUID accessionId, int instituteId, UUID bookId, String accessionNumber, String rack,
            double price, String billNumber, Integer dateOfPurchase, UUID vendorId, BookStatus status, String volume,
            UUID libraryTypeId, String remarks) {
        this.accessionId = accessionId;
        this.instituteId = instituteId;
        this.bookId = bookId;
        this.accessionNumber = accessionNumber;
        this.rack = rack;
        this.price = price;
        this.billNumber = billNumber;
        this.dateOfPurchase = dateOfPurchase;
        this.vendorId = vendorId;
        this.status = status;
        this.volume = volume;
        this.libraryTypeId = libraryTypeId;
        this.remarks = remarks;
    }

   public static IndividualBookDetailsPayload createPayload(IndividualBookDetailsPayload original, int instituteId, UUID bookId, String accessionNumber) {
		return new IndividualBookDetailsPayload(null, instituteId, bookId, accessionNumber,
				original.getRack(), original.getPrice(), original.getBillNumber(),
				original.getDateOfPurchase(), original.getVendorId(), original.getStatus(),
				original.getVolume(), original.getLibraryTypeId(), original.getRemarks());
	}

    public UUID getAccessionId() {
        return accessionId;
    }

    public void setAccessionId(UUID accessionId) {
        this.accessionId = accessionId;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public void setInstituteId(int instituteId) {
        this.instituteId = instituteId;
    }

    public UUID getBookId() {
        return bookId;
    }

    public void setBookId(UUID bookId) {
        this.bookId = bookId;
    }

    public String getAccessionNumber() {
        return accessionNumber;
    }

    public void setAccessionNumber(String accessionNumber) {
        this.accessionNumber = accessionNumber;
    }

    public String getRack() {
        return rack;
    }

    public void setRack(String rack) {
        this.rack = rack;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public String getBillNumber() {
        return billNumber;
    }

    public void setBillNumber(String billNumber) {
        this.billNumber = billNumber;
    }

    public Integer getDateOfPurchase() {
        return dateOfPurchase;
    }

    public void setDateOfPurchase(Integer dateOfPurchase) {
        this.dateOfPurchase = dateOfPurchase;
    }

    public UUID getVendorId() {
        return vendorId;
    }

    public void setVendorId(UUID vendorId) {
        this.vendorId = vendorId;
    }

    public BookStatus getStatus() {
        return status;
    }

    public void setStatus(BookStatus status) {
        this.status = status;
    }

    public String getVolume() {
        return volume;
    }

    public void setVolume(String volume) {
        this.volume = volume;
    }

    public UUID getLibraryTypeId() {
        return libraryTypeId;
    }

    public void setLibraryTypeId(UUID libraryTypeId) {
        this.libraryTypeId = libraryTypeId;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    @Override
    public String toString() {
        return "IndividualBookDetailsPayload [accessionId=" + accessionId + ", instituteId=" + instituteId + ", bookId=" + bookId
                + ", accessionNumber=" + accessionNumber + ", rack=" + rack + ", price=" + price + ", billNumber="
                + billNumber + ", dateOfPurchase=" + dateOfPurchase + ", vendorId=" + vendorId + ", status=" + status
                + ", volume=" + volume + ", libraryTypeId=" + libraryTypeId + ", remarks=" + remarks + "]";
    }

    

    
}
