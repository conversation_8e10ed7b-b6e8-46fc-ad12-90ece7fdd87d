package com.lernen.cloud.core.api.institute;

import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public class InstituteHouse {

    private int instituteId;

    private UUID houseId;

    private String houseName;

    public InstituteHouse() {
    }

    public InstituteHouse(int instituteId, UUID houseId, String houseName) {
        this.instituteId = instituteId;
        this.houseId = houseId;
        this.houseName = houseName;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public void setInstituteId(int instituteId) {
        this.instituteId = instituteId;
    }

    public UUID getHouseId() {
        return houseId;
    }

    public void setHouseId(UUID houseId) {
        this.houseId = houseId;
    }

    public String getHouseName() {
        return houseName;
    }

    public void setHouseName(String houseName) {
        this.houseName = houseName;
    }

    public static Map<UUID, InstituteHouse> getInstituteHouseMap(List<InstituteHouse> instituteHouseList) {
        if(CollectionUtils.isEmpty(instituteHouseList)) {
            return null;
        }
        Map<UUID, InstituteHouse> instituteHouseMap = new HashMap<>();
        for(InstituteHouse instituteHouse : instituteHouseList) {
            instituteHouseMap.put(instituteHouse.getHouseId(), instituteHouse);
        }
        return instituteHouseMap;
    }

    @Override
    public String toString() {
        return "InstituteHouses{" +
                "instituteId=" + instituteId +
                ", houseId=" + houseId +
                ", houseName='" + houseName + '\'' +
                '}';
    }
}
