package com.lernen.cloud.core.api.diary;

public class StandardRemarkDetails {
    
    private StandardRemarks standardRemark;
    private DiaryRemarkCategory diaryRemarkCategory;

    public StandardRemarkDetails(){

    }

    public StandardRemarkDetails(StandardRemarks standardRemark, DiaryRemarkCategory diaryRemarkCategory) {
        this.standardRemark = standardRemark;
        this.diaryRemarkCategory = diaryRemarkCategory;
    }

    public StandardRemarks getStandardRemark() {
        return this.standardRemark;
    }

    public void setStandardRemark(StandardRemarks standardRemark) {
        this.standardRemark = standardRemark;
    }

    public DiaryRemarkCategory getDiaryRemarkCategory() {
         return this.diaryRemarkCategory;
    }

    public void setDiaryRemarkCategory(DiaryRemarkCategory diaryRemarkCategory) {
        this.diaryRemarkCategory = diaryRemarkCategory;
    }

    @Override
    public String toString() {
        return "StandardRemarkDetails{" +
                "standardRemark=" + standardRemark +
                ", diaryRemarkCategory=" + diaryRemarkCategory +
                '}';
    }
}
