package com.lernen.cloud.core.api.examination.report;

import com.lernen.cloud.core.api.pdf.CellData;

import java.util.List;

/**
 * <AUTHOR>
 */

public class CourseTotalMarksRows {

    private final List<CellData> maxMarksRow;

    private final List<CellData> obtainedMarksRow;

    private final List<CellData> percentMarksRow;

    private final List<CellData> divisionRow;


    public CourseTotalMarksRows(List<CellData> maxMarksRow, List<CellData> obtainedMarksRow, List<CellData> percentMarksRow, List<CellData> divisionRow) {
        this.maxMarksRow = maxMarksRow;
        this.obtainedMarksRow = obtainedMarksRow;
        this.percentMarksRow = percentMarksRow;
		this.divisionRow = divisionRow;
	}

    public List<CellData> getMaxMarksRow() {
        return maxMarksRow;
    }

    public List<CellData> getObtainedMarksRow() {
        return obtainedMarksRow;
    }

    public List<CellData> getPercentMarksRow() {
        return percentMarksRow;
    }

    public List<CellData> getDivisionRow() { return divisionRow;}
}
