/**
 * 
 */
package com.lernen.cloud.core.api.incomeexpense;

import java.util.UUID;

/**
 * <AUTHOR>
 *
 */
public class IncomeExpenseCategory {
	
	private int instituteId;
	
	private UUID categoryId;
	
	private String categoryName;

	private String categoryDescription;

	public IncomeExpenseCategory(int instituteId, UUID categoryId, String categoryName, String categoryDescription) {
		this.instituteId = instituteId;
		this.categoryId = categoryId;
		this.categoryName = categoryName;
		this.categoryDescription = categoryDescription;
	}
	
	public IncomeExpenseCategory() {
		
	}

	public int getInstituteId() {
		return instituteId;
	}

	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	public UUID getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(UUID categoryId) {
		this.categoryId = categoryId;
	}

	public String getCategoryName() {
		return categoryName;
	}

	public void setCategoryName(String categoryName) {
		this.categoryName = categoryName;
	}

	public String getCategoryDescription() {
		return categoryDescription;
	}

	public void setCategoryDescription(String categoryDescription) {
		this.categoryDescription = categoryDescription;
	}

	@Override
	public String toString() {
		return "IncomeExpenseCategory [instituteId=" + instituteId + ", categoryId=" + categoryId + ", categoryName="
				+ categoryName + ", categoryDescription=" + categoryDescription + "]";
	}
}
