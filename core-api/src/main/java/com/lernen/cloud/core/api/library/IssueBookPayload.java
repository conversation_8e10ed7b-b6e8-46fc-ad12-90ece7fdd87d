package com.lernen.cloud.core.api.library;

import java.util.UUID;

import com.lernen.cloud.core.api.user.UserType;

/**
 * 
 * <AUTHOR>
 * @update by vasav mittal
 */
public class IssueBookPayload {
	
	private UUID bookId;

	private UUID accessionId;
	
	private int instituteId;

	private int academicSessionId;

	private UUID issuedToUserId;

	private UserType issuedToUserType;
	
	private UUID issuedBy;
	
	private int duration;

	private Integer issuedAt;
	
	public IssueBookPayload() {
		
	}

	public IssueBookPayload(UUID bookId, UUID accessionId, int instituteId, int academicSessionId, UUID userId,
			UserType userType, UUID issuedBy, int duration, int issuedAt) {
		this.bookId = bookId;
		this.accessionId = accessionId;
		this.instituteId = instituteId;
		this.academicSessionId = academicSessionId;
		this.issuedToUserId = userId;
		this.issuedToUserType = userType;
		this.issuedBy = issuedBy;
		this.duration = duration;
		this.issuedAt = issuedAt;
	}

	public UUID getAccessionId() {
		return accessionId;
	}

	public void setAccessionId(UUID accessionId) {
		this.accessionId = accessionId;
	}

	public UserType getIssuedToUserType() {
		return issuedToUserType;
	}

	public int getAcademicSessionId() {
		return academicSessionId;
	}

	public Integer getIssuedAt() {
		return issuedAt;
	}

	public void setIssuedAt(Integer issuedAt) {
		this.issuedAt = issuedAt;
	}

	public void setAcademicSessionId(int academicSessionId) {
		this.academicSessionId = academicSessionId;
	}

	public void setIssuedToUserType(UserType userType) {
		this.issuedToUserType = userType;
	}
	
	public UUID getBookId() {
		return bookId;
	}

	public void setBookId(UUID bookId) {
		this.bookId = bookId;
	}

	public int getInstituteId() {
		return instituteId;
	}

	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	public UUID getIssuedToUserId() {
		return issuedToUserId;
	}

	public void setIssuedToUserId(UUID userId) {
		this.issuedToUserId = userId;
	}

	public UUID getIssuedBy() {
		return issuedBy;
	}

	public void setIssuedBy(UUID issuedBy) {
		this.issuedBy = issuedBy;
	}

	public int getDuration() {
		return duration;
	}

	public void setDuration(int duration) {
		this.duration = duration;
	}
	
	@Override
	public String toString() {
		return "IssueBookPayload [bookId=" + bookId + ", accessionId=" + accessionId + ", instituteId=" + instituteId
				+ ", academicSessionId=" + academicSessionId + ", userId=" + issuedToUserId + ", userType=" + issuedToUserType
				+ ", issuedBy=" + issuedBy + ", duration=" + duration + ", issuedAt=" + issuedAt + "]";
	}

}
