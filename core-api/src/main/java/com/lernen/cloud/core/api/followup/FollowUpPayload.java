package com.lernen.cloud.core.api.followup;


import java.util.UUID;

public class FollowUpPayload {

    private UUID followUpId;

    private UUID entityId;

    private FollowUpEntity entityName;

    private Integer followUpDate;

    private FollowUpMode followUpMode;

    private String contactPersonName;

    private String conversation;

    private Integer nextFollowUpDate;

    private Double amount;

    private FollowUpType followUpType;

    private FollowUpStatus followUpStatus;

    private UUID addedBy;

    private Integer addedAt;
    private UUID updatedBy;

    public FollowUpPayload(){

    }

    public FollowUpPayload(UUID followUpId, UUID entityId, FollowUpEntity entityName, Integer followUpDate, FollowUpMode followUpMode, String contactPersonName, String conversation, Integer nextFollowUpDate, Double amount, FollowUpType followUpType, FollowUpStatus followUpStatus, UUID addedBy, Integer addedAt, UUID updatedBy) {
        this.followUpId = followUpId;
        this.entityId = entityId;
        this.entityName = entityName;
        this.followUpDate = followUpDate;
        this.followUpMode = followUpMode;
        this.contactPersonName = contactPersonName;
        this.conversation = conversation;
        this.nextFollowUpDate = nextFollowUpDate;
        this.amount = amount;
        this.followUpType = followUpType;
        this.followUpStatus = followUpStatus;
        this.addedBy = addedBy;
        this.addedAt = addedAt;
        this.updatedBy = updatedBy;
    }

    public UUID getFollowUpId() {
        return followUpId;
    }

    public void setFollowUpId(UUID followUpId) {
        this.followUpId = followUpId;
    }

    public UUID getEntityId() {
        return entityId;
    }

    public void setEntityId(UUID entityId) {
        this.entityId = entityId;
    }

    public FollowUpEntity getEntityName() {
        return entityName;
    }

    public void setEntityName(FollowUpEntity entityName) {
        this.entityName = entityName;
    }

    public Integer getFollowUpDate() {
        return followUpDate;
    }

    public void setFollowUpDate(Integer followUpDate) {
        this.followUpDate = followUpDate;
    }

    public FollowUpMode getFollowUpMode() {
        return followUpMode;
    }

    public void setFollowUpMode(FollowUpMode followUpMode) {
        this.followUpMode = followUpMode;
    }

    public String getContactPersonName() {
        return contactPersonName;
    }

    public void setContactPersonName(String contactPersonName) {
        this.contactPersonName = contactPersonName;
    }

    public String getConversation() {
        return conversation;
    }

    public void setConversation(String conversation) {
        this.conversation = conversation;
    }

    public Integer getNextFollowUpDate() {
        return nextFollowUpDate;
    }

    public void setNextFollowUpDate(Integer nextFollowUpDate) {
        this.nextFollowUpDate = nextFollowUpDate;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public FollowUpType getFollowUpType() {
        return followUpType;
    }

    public void setFollowUpType(FollowUpType followUpType) {
        this.followUpType = followUpType;
    }

    public FollowUpStatus getFollowUpStatus() {
        return followUpStatus;
    }

    public void setFollowUpStatus(FollowUpStatus followUpStatus) {
        this.followUpStatus = followUpStatus;
    }

    public UUID getAddedBy() {
        return addedBy;
    }

    public void setAddedBy(UUID addedBy) {
        this.addedBy = addedBy;
    }

    public UUID getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(UUID updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Integer getAddedAt() {
        return addedAt;
    }

    public void setAddedAt(Integer addedAt) {
        this.addedAt = addedAt;
    }

    @Override
    public String toString() {
        return "FollowUpPayload{" +
                "followUpId=" + followUpId +
                ", entityId=" + entityId +
                ", entityName=" + entityName +
                ", followUpDate=" + followUpDate +
                ", followUpMode=" + followUpMode +
                ", contactPersonName='" + contactPersonName + '\'' +
                ", conversation='" + conversation + '\'' +
                ", nextFollowUpDate=" + nextFollowUpDate +
                ", amount=" + amount +
                ", followUpType=" + followUpType +
                ", followUpStatus=" + followUpStatus +
                ", addedBy=" + addedBy +
                ", addedAt=" + addedAt +
                ", updatedBy=" + updatedBy +
                '}';
    }
}
