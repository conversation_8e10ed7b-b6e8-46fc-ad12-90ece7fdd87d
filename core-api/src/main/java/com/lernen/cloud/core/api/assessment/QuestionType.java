package com.lernen.cloud.core.api.assessment;

import org.apache.commons.lang3.StringUtils;

public enum QuestionType {
    
    OBJECTIVE , SUBJECTIVE;

    public static QuestionType getQuestionType(String questionType) {
		if (StringUtils.isBlank(questionType)) {
			return null;
		}
		for (QuestionType questionTypeEnum : QuestionType.values()) {
			if (questionTypeEnum.name().equalsIgnoreCase(questionType)) {
				return questionTypeEnum;
			}
		}
		return null;
	}
}
