/**
 *
 */
package com.lernen.cloud.core.api.report;

/**
 * <AUTHOR>
 *
 */
public class ReportCellDetails {

	private final Object value;

	private final String dataType;

	private final int size;
	private final String textColor;
	
	private final String cellColor;

	private final boolean isBold;

	private final ReportHorizontalTextAlignment reportTextHorizontalAlignment;

	private final ReportVerticalTextAlignment reportTextVerticalAlignment;

	private float cellHeight;

	/**
	 * @param value
	 * @param size
	 * @param textColor
	 * @param cellColor
	 * @param isBold
	 */
	public ReportCellDetails(Object value, String dataType, int size, String textColor, String cellColor,
			boolean isBold) {
		this.value = value;
		this.dataType = dataType;
		this.size = size;
		this.textColor = textColor;
		this.cellColor = cellColor;
		this.isBold = isBold;
		this.reportTextHorizontalAlignment = ReportHorizontalTextAlignment.CENTER;
		this.reportTextVerticalAlignment = ReportVerticalTextAlignment.MIDDLE;
	}

	public ReportCellDetails(Object value, String dataType, int size, String textColor, String cellColor, boolean isBold, ReportHorizontalTextAlignment reportTextHorizontalAlignment,
							 ReportVerticalTextAlignment reportTextVerticalAlignment) {
		this.value = value;
		this.dataType = dataType;
		this.size = size;
		this.textColor = textColor;
		this.cellColor = cellColor;
		this.isBold = isBold;
		this.reportTextHorizontalAlignment = reportTextHorizontalAlignment;
		this.reportTextVerticalAlignment = reportTextVerticalAlignment;
	}

	public ReportCellDetails(Object value, String dataType, int size, String textColor, String cellColor, boolean isBold, ReportHorizontalTextAlignment reportTextHorizontalAlignment, ReportVerticalTextAlignment reportTextVerticalAlignment, float cellHeight) {
		this.value = value;
		this.dataType = dataType;
		this.size = size;
		this.textColor = textColor;
		this.cellColor = cellColor;
		this.isBold = isBold;
		this.reportTextHorizontalAlignment = reportTextHorizontalAlignment;
		this.reportTextVerticalAlignment = reportTextVerticalAlignment;
		this.cellHeight = cellHeight;
	}

	/**
	 * @return the value
	 */
	public Object getValue() {
		return value;
	}

	/**
	 * @return the dataType
	 */
	public String getDataType() {
		return dataType;
	}

	/**
	 * @return the size
	 */
	public int getSize() {
		return size;
	}

	/**
	 * @return the textColor
	 */
	public String getTextColor() {
		return textColor;
	}

	/**
	 * @return the cellColor
	 */
	public String getCellColor() {
		return cellColor;
	}

	/**
	 * @return the isBold
	 */
	public boolean isBold() {
		return isBold;
	}

	public ReportHorizontalTextAlignment getReportTextHorizontalAlignment() {
		return reportTextHorizontalAlignment;
	}

	public ReportVerticalTextAlignment getReportTextVerticalAlignment() {
		return reportTextVerticalAlignment;
	}

	public float getCellHeight() {
		return cellHeight;
	}

	@Override
	public String toString() {
		return "ReportCellDetails{" +
				"value=" + value +
				", dataType='" + dataType + '\'' +
				", size=" + size +
				", textColor='" + textColor + '\'' +
				", cellColor='" + cellColor + '\'' +
				", isBold=" + isBold +
				", reportTextHorizontalAlignment=" + reportTextHorizontalAlignment +
				", reportTextVerticalAlignment=" + reportTextVerticalAlignment +
				", cellHeight=" + cellHeight +
				'}';
	}

}