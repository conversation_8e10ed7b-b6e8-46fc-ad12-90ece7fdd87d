package com.lernen.cloud.core.api.institute;

import com.lernen.cloud.core.api.user.User;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;

import java.time.Month;

public class AcademicSession {

    private static final String MONTH_YEAR_DELIMITER = " ";

    private int instituteId;

    private int academicSessionId;

    private int startYear;

    private int endYear;

    private Month startMonth;

    private Month endMonth;

    private int sessionStartTime;

    private int sessionEndTime;

    private String displayName;

    private String yearDisplayName;

    private String shortYearDisplayName;

    private int payrollStartYear;

    private int payrollEndYear;

    private Month payrollStartMonth;

    private Month payrollEndMonth;

    private int payrollSessionStartTime;

    private int payrollSessionEndTime;

    private String payrollDisplayName;

    private String payrollYearDisplayName;

    private String payrollShortYearDisplayName;

    private int addedTimestamp;

    private int updatedTimestamp;

    public AcademicSession() {
    }

    public AcademicSession(int instituteId, int academicSessionId, int startYear, int endYear, Month startMonth,
                           Month endMonth, String displayName, int payrollStartYear, int payrollEndYear, Month payrollStartMonth,
                           Month payrollEndMonth, String payrollDisplayName, int addedTimestamp, int updatedTimestamp) {
        this.instituteId = instituteId;
        this.academicSessionId = academicSessionId;
        this.startYear = startYear;
        this.endYear = endYear;
        this.startMonth = startMonth;
        this.endMonth = endMonth;
        if (StringUtils.isEmpty(displayName)) {
            StringBuilder name = new StringBuilder();
            name.append(StringUtils.capitalize(startMonth.name())).append(MONTH_YEAR_DELIMITER).append(startYear)
                    .append(" - ").append(StringUtils.capitalize(endMonth.name())).append(MONTH_YEAR_DELIMITER)
                    .append(endYear);
            this.displayName = name.toString();
        } else {
            this.displayName = displayName;
        }
        this.yearDisplayName = startYear + "-" + endYear;
        this.shortYearDisplayName = startYear + "-" + (endYear % 100);
        this.sessionStartTime = computeSessionStart();
        this.sessionEndTime = computeSessionEnd();
        this.payrollStartYear = payrollStartYear;
        this.payrollEndYear = payrollEndYear;
        this.payrollStartMonth = payrollStartMonth;
        this.payrollEndMonth = payrollEndMonth;
        if (StringUtils.isEmpty(payrollDisplayName)) {
            StringBuilder name = new StringBuilder();
            name.append(StringUtils.capitalize(payrollStartMonth.name())).append(MONTH_YEAR_DELIMITER).append(payrollStartYear)
                    .append(" - ").append(StringUtils.capitalize(payrollEndMonth.name())).append(MONTH_YEAR_DELIMITER)
                    .append(payrollEndYear);
            this.payrollDisplayName = name.toString();
        } else {
            this.payrollDisplayName = displayName;
        }
        this.payrollYearDisplayName = payrollStartYear + "-" + payrollEndYear;
        this.payrollShortYearDisplayName = payrollStartYear + "-" + (payrollEndYear % 100);
        this.payrollSessionStartTime = computePayrollSessionStart();
        this.payrollSessionEndTime = computePayrollSessionEnd();
        this.addedTimestamp = addedTimestamp;
        this.updatedTimestamp = updatedTimestamp;
    }

    public AcademicSession(int instituteId, int academicSessionId, int startYear, int endYear, Month startMonth,
                           Month endMonth, String displayName) {
        this.instituteId = instituteId;
        this.academicSessionId = academicSessionId;
        this.startYear = startYear;
        this.endYear = endYear;
        this.startMonth = startMonth;
        this.endMonth = endMonth;
        if (StringUtils.isEmpty(displayName)) {
            StringBuilder name = new StringBuilder();
            name.append(StringUtils.capitalize(startMonth.name())).append(MONTH_YEAR_DELIMITER).append(startYear)
                    .append(" - ").append(StringUtils.capitalize(endMonth.name())).append(MONTH_YEAR_DELIMITER)
                    .append(endYear);
            this.displayName = name.toString();
        } else {
            this.displayName = displayName;
        }
        this.yearDisplayName = startYear + "-" + endYear;
        this.shortYearDisplayName = startYear + "-" + (endYear % 100);
        this.sessionStartTime = computeSessionStart();
        this.sessionEndTime = computeSessionEnd();

        this.payrollStartYear = this.startYear;
        this.payrollEndYear = this.endYear;
        this.payrollStartMonth = this.startMonth;
        this.payrollEndMonth = this.endMonth;
        this.payrollDisplayName = this.displayName;
        this.payrollYearDisplayName = this.yearDisplayName;
        this.payrollShortYearDisplayName = this.shortYearDisplayName;
        this.payrollSessionStartTime = this.sessionStartTime;
        this.payrollSessionEndTime = this.sessionEndTime;

    }

    private int computeSessionStart() {
        DateTime sessionStart = new DateTime(DateTimeZone.forTimeZone(User.DFAULT_TIMEZONE));
        sessionStart = sessionStart.withYear(startYear).withMonthOfYear(startMonth.ordinal() + 1).withDayOfMonth(1)
                .withHourOfDay(0).withMinuteOfHour(0).withSecondOfMinute(0).withMillisOfSecond(0);
        return (int) (sessionStart.getMillis() / 1000l);
    }

    private int computePayrollSessionStart() {
        DateTime sessionStart = new DateTime(DateTimeZone.forTimeZone(User.DFAULT_TIMEZONE));
        sessionStart = sessionStart.withYear(payrollStartYear).withMonthOfYear(payrollStartMonth.ordinal() + 1).withDayOfMonth(1)
                .withHourOfDay(0).withMinuteOfHour(0).withSecondOfMinute(0).withMillisOfSecond(0);
        return (int) (sessionStart.getMillis() / 1000l);
    }

    private int computeSessionEnd() {
        DateTime sessionEnd = new DateTime(DateTimeZone.forTimeZone(User.DFAULT_TIMEZONE));
        int maxDays = sessionEnd.withYear(endYear).withMonthOfYear(endMonth.ordinal() + 1).dayOfMonth()
                .getMaximumValue();
        sessionEnd = sessionEnd.withYear(endYear).withMonthOfYear(endMonth.ordinal() + 1).withDayOfMonth(maxDays)
                .withHourOfDay(23).withMinuteOfHour(59).withSecondOfMinute(59).withMillisOfSecond(999);

        return (int) (sessionEnd.getMillis() / 1000l);
    }

    private int computePayrollSessionEnd() {
        DateTime sessionEnd = new DateTime(DateTimeZone.forTimeZone(User.DFAULT_TIMEZONE));
        int maxDays = sessionEnd.withYear(payrollEndYear).withMonthOfYear(payrollEndMonth.ordinal() + 1).dayOfMonth()
                .getMaximumValue();
        sessionEnd = sessionEnd.withYear(payrollEndYear).withMonthOfYear(payrollEndMonth.ordinal() + 1).withDayOfMonth(maxDays)
                .withHourOfDay(23).withMinuteOfHour(59).withSecondOfMinute(59).withMillisOfSecond(999);

        return (int) (sessionEnd.getMillis() / 1000l);
    }


    public int getInstituteId() {
        return instituteId;
    }

    public int getAcademicSessionId() {
        return academicSessionId;
    }

    public int getStartYear() {
        return startYear;
    }

    public int getEndYear() {
        return endYear;
    }

    public Month getStartMonth() {
        return startMonth;
    }

    public Month getEndMonth() {
        return endMonth;
    }

    public boolean isCurrentSession() {
        int currentTime = (int) (System.currentTimeMillis() / 1000l);
        return currentTime >= sessionStartTime && currentTime <= sessionEndTime;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getYearDisplayName() {
        return yearDisplayName;
    }

    public String getShortYearDisplayName() {
        return shortYearDisplayName;
    }

    public int getSessionStartTime() {
        return sessionStartTime;
    }

    public int getSessionEndTime() {
        return sessionEndTime;
    }

    public void setInstituteId(int instituteId) {
        this.instituteId = instituteId;
    }

    public void setAcademicSessionId(int academicSessionId) {
        this.academicSessionId = academicSessionId;
    }

    public void setStartYear(int startYear) {
        this.startYear = startYear;
    }

    public void setEndYear(int endYear) {
        this.endYear = endYear;
    }

    public void setStartMonth(Month startMonth) {
        this.startMonth = startMonth;
    }

    public void setEndMonth(Month endMonth) {
        this.endMonth = endMonth;
    }

    public void setSessionStartTime(int sessionStartTime) {
        this.sessionStartTime = sessionStartTime;
    }

    public void setSessionEndTime(int sessionEndTime) {
        this.sessionEndTime = sessionEndTime;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public void setYearDisplayName(String yearDisplayName) {
        this.yearDisplayName = yearDisplayName;
    }

    public void setShortYearDisplayName(String shortYearDisplayName) {
        this.shortYearDisplayName = shortYearDisplayName;
    }

    public int getAddedTimestamp() {
        return addedTimestamp;
    }

    public void setAddedTimestamp(int addedTimestamp) {
        this.addedTimestamp = addedTimestamp;
    }

    public int getUpdatedTimestamp() {
        return updatedTimestamp;
    }

    public void setUpdatedTimestamp(int updatedTimestamp) {
        this.updatedTimestamp = updatedTimestamp;
    }

    public int getPayrollStartYear() {
        return payrollStartYear;
    }

    public void setPayrollStartYear(int payrollStartYear) {
        this.payrollStartYear = payrollStartYear;
    }

    public int getPayrollEndYear() {
        return payrollEndYear;
    }

    public void setPayrollEndYear(int payrollEndYear) {
        this.payrollEndYear = payrollEndYear;
    }

    public Month getPayrollStartMonth() {
        return payrollStartMonth;
    }

    public void setPayrollStartMonth(Month payrollStartMonth) {
        this.payrollStartMonth = payrollStartMonth;
    }

    public Month getPayrollEndMonth() {
        return payrollEndMonth;
    }

    public void setPayrollEndMonth(Month payrollEndMonth) {
        this.payrollEndMonth = payrollEndMonth;
    }

    public int getPayrollSessionStartTime() {
        return payrollSessionStartTime;
    }

    public void setPayrollSessionStartTime(int payrollSessionStartTime) {
        this.payrollSessionStartTime = payrollSessionStartTime;
    }

    public int getPayrollSessionEndTime() {
        return payrollSessionEndTime;
    }

    public void setPayrollSessionEndTime(int payrollSessionEndTime) {
        this.payrollSessionEndTime = payrollSessionEndTime;
    }

    public String getPayrollDisplayName() {
        return payrollDisplayName;
    }

    public void setPayrollDisplayName(String payrollDisplayName) {
        this.payrollDisplayName = payrollDisplayName;
    }

    public String getPayrollYearDisplayName() {
        return payrollYearDisplayName;
    }

    public void setPayrollYearDisplayName(String payrollYearDisplayName) {
        this.payrollYearDisplayName = payrollYearDisplayName;
    }

    public String getPayrollShortYearDisplayName() {
        return payrollShortYearDisplayName;
    }

    public void setPayrollShortYearDisplayName(String payrollShortYearDisplayName) {
        this.payrollShortYearDisplayName = payrollShortYearDisplayName;
    }

    @Override
    public String toString() {
        return "AcademicSession{" +
                "instituteId=" + instituteId +
                ", academicSessionId=" + academicSessionId +
                ", startYear=" + startYear +
                ", endYear=" + endYear +
                ", startMonth=" + startMonth +
                ", endMonth=" + endMonth +
                ", sessionStartTime=" + sessionStartTime +
                ", sessionEndTime=" + sessionEndTime +
                ", displayName='" + displayName + '\'' +
                ", yearDisplayName='" + yearDisplayName + '\'' +
                ", shortYearDisplayName='" + shortYearDisplayName + '\'' +
                ", payrollStartYear=" + payrollStartYear +
                ", payrollEndYear=" + payrollEndYear +
                ", payrollStartMonth=" + payrollStartMonth +
                ", payrollEndMonth=" + payrollEndMonth +
                ", payrollSessionStartTime=" + payrollSessionStartTime +
                ", payrollSessionEndTime=" + payrollSessionEndTime +
                ", payrollDisplayName='" + payrollDisplayName + '\'' +
                ", payrollYearDisplayName='" + payrollYearDisplayName + '\'' +
                ", payrollShortYearDisplayName='" + payrollShortYearDisplayName + '\'' +
                ", addedTimestamp=" + addedTimestamp +
                ", updatedTimestamp=" + updatedTimestamp +
                '}';
    }

}
