package com.lernen.cloud.core.api.fees.payment;

import com.lernen.cloud.core.api.fee.discount.StudentFeeDiscountAssignment;
import com.lernen.cloud.core.api.fees.EntityFeeAssignment;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 */

public class BulkStudentFeeData {

    private final Map<UUID, List<FeePaymentDetails>> feePaymentDetailsMaps;

    private final Map<UUID, StudentFeeDiscountAssignment> studentFeeDiscountAssignmentMap;

    private final  Map<UUID, Map<String, EntityFeeAssignment>> entityFeeAssignmentsMap;

    public BulkStudentFeeData(Map<UUID, List<FeePaymentDetails>> feePaymentDetailsMaps, Map<UUID, StudentFeeDiscountAssignment> studentFeeDiscountAssignmentMap, Map<UUID, Map<String, EntityFeeAssignment>> entityFeeAssignmentsMap) {
        this.feePaymentDetailsMaps = feePaymentDetailsMaps;
        this.studentFeeDiscountAssignmentMap = studentFeeDiscountAssignmentMap;
        this.entityFeeAssignmentsMap = entityFeeAssignmentsMap;
    }

    public Map<UUID, List<FeePaymentDetails>> getFeePaymentDetailsMaps() {
        return feePaymentDetailsMaps;
    }

    public Map<UUID, StudentFeeDiscountAssignment> getStudentFeeDiscountAssignmentMap() {
        return studentFeeDiscountAssignmentMap;
    }

    public Map<UUID, Map<String, EntityFeeAssignment>> getEntityFeeAssignmentsMap() {
        return entityFeeAssignmentsMap;
    }
}

