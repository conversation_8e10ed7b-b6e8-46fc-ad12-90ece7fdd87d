package com.lernen.cloud.core.api.examination.report;

import java.util.Set;

/**
 * <AUTHOR>
 */

public class ExamResultComputationEntryPayload {

    private String examName;

    private boolean totalDimension;

    private Set<String> dimensions;

    public ExamResultComputationEntryPayload() {
    }

    public ExamResultComputationEntryPayload(String examName, boolean totalDimension, Set<String> dimensions) {
        this.examName = examName;
        this.totalDimension = totalDimension;
        this.dimensions = dimensions;
    }

    public String getExamName() {
        return examName;
    }

    public void setExamName(String examName) {
        this.examName = examName;
    }

    public boolean isTotalDimension() {
        return totalDimension;
    }

    public void setTotalDimension(boolean totalDimension) {
        this.totalDimension = totalDimension;
    }

    public Set<String> getDimensions() {
        return dimensions;
    }

    public void setDimensions(Set<String> dimensions) {
        this.dimensions = dimensions;
    }

    @Override
    public String toString() {
        return "ExamResultComputationEntryPayload{" +
                "examName='" + examName + '\'' +
                ", totalDimension=" + totalDimension +
                ", dimensions=" + dimensions +
                '}';
    }
}

