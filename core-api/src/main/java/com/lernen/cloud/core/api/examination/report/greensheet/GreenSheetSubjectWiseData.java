/**
 * 
 */
package com.lernen.cloud.core.api.examination.report.greensheet;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class GreenSheetSubjectWiseData {
	
	private List<GreenSheetColumn> greenSheetDataStudentLevel;
	
	private Double totalMarks;

	private Double maxTotalMarks;

	private String distinctionCourses;

	public GreenSheetSubjectWiseData(List<GreenSheetColumn> greenSheetDataStudentLevel, Double totalMarks,
									 String distinctionCourses) {
		this.greenSheetDataStudentLevel = greenSheetDataStudentLevel;
		this.totalMarks = totalMarks;
		this.distinctionCourses = distinctionCourses;
	}
	public GreenSheetSubjectWiseData(List<GreenSheetColumn> greenSheetDataStudentLevel, Double totalMarks,
									 Double maxTotalMarks, String distinctionCourses) {
		this.greenSheetDataStudentLevel = greenSheetDataStudentLevel;
		this.totalMarks = totalMarks;
		this.maxTotalMarks = maxTotalMarks;
		this.distinctionCourses = distinctionCourses;
	}

	public List<GreenSheetColumn> getGreenSheetDataStudentLevel() {
		return greenSheetDataStudentLevel;
	}

	public void setGreenSheetDataStudentLevel(List<GreenSheetColumn> greenSheetDataStudentLevel) {
		this.greenSheetDataStudentLevel = greenSheetDataStudentLevel;
	}

	public Double getMaxTotalMarks() {
		return maxTotalMarks;
	}

	public void setMaxTotalMarks(Double maxTotalMarks) {
		this.maxTotalMarks = maxTotalMarks;
	}

	public Double getTotalMarks() {
		return totalMarks;
	}

	public void setTotalMarks(Double totalMarks) {
		this.totalMarks = totalMarks;
	}

	public String getDistinctionCourses() {
		return distinctionCourses;
	}

	public void setDistinctionCourses(String distinctionCourses) {
		this.distinctionCourses = distinctionCourses;
	}

	@Override
	public String toString() {
		return "GreenSheetSubjectWiseData{" +
				"greenSheetDataStudentLevel=" + greenSheetDataStudentLevel +
				", totalMarks=" + totalMarks +
				", maxTotalMarks=" + maxTotalMarks +
				", distinctionCourses='" + distinctionCourses + '\'' +
				'}';
	}
}
