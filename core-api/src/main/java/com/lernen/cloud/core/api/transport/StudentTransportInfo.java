package com.lernen.cloud.core.api.transport;

import java.util.Comparator;

import com.lernen.cloud.core.api.student.StudentLite;

public class StudentTransportInfo {
    private final StudentLite student;
    private final StudentTransportData studentTransportData;

    public StudentTransportInfo( StudentLite student, StudentTransportData studentTransportData) {
        this.student = student;
        this.studentTransportData = studentTransportData;
    }

    public StudentLite getStudent() {
        return student;
    }

    public StudentTransportData getStudentTransportData() {
        return studentTransportData;
    }

    public static Comparator<StudentTransportInfo> getTransportStudentComparator() {
        return new Comparator<StudentTransportInfo>() {
            @Override
            public int compare(StudentTransportInfo s1, StudentTransportInfo s2) {
                int standard1 = s1.getStudent().getStudentSessionData().getStandardLevel();
                int standard2 = s2.getStudent().getStudentSessionData().getStandardLevel();
                int standardCompare = standard1-standard2;
                if(standardCompare != 0){
                    return standardCompare;
                }
                String sec1 = s1.getStudent().getStudentSessionData().getStandardSection() == null ? "" : s1.getStudent().getStudentSessionData().getStandardSection().getSectionName();
                String sec2 = s2.getStudent().getStudentSessionData().getStandardSection() == null ? "" : s2.getStudent().getStudentSessionData().getStandardSection().getSectionName();
                final int sectionCompare = sec1.compareToIgnoreCase(sec2);
                if(sectionCompare != 0){
                    return sectionCompare;
                }
                
                return s1.getStudent().getName().compareTo(s2.getStudent().getName());
            }
        };
    }    

    @Override
	public String toString() {
		return "StudentTransportInfo{" +
				"student=" + student +
				", studentTransportData=" + studentTransportData +
				'}';
	}
}
