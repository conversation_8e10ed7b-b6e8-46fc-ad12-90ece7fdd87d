/**
 * 
 */
package com.lernen.cloud.core.api.incomeexpense;

import org.apache.commons.lang3.StringUtils;


/**
 * <AUTHOR>
 *
 */
public enum IncomeExpenseTransactionStatus {
	ACTIVE, CANCELLED;
	
	public static IncomeExpenseTransactionStatus getIncomeExpenseTransactionStatus(String incomeExpenseTransactionStatus) {
		if (StringUtils.isBlank(incomeExpenseTransactionStatus)) {
			return null;
		}
		for (IncomeExpenseTransactionStatus incomeExpenseTransactionStatusEnum : IncomeExpenseTransactionStatus.values()) {
			if (incomeExpenseTransactionStatusEnum.name().equalsIgnoreCase(incomeExpenseTransactionStatus)) {
				return incomeExpenseTransactionStatusEnum;
			}
		}
		return null;
	}
}
