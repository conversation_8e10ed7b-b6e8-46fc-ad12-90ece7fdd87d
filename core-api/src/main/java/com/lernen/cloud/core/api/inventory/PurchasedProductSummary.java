package com.lernen.cloud.core.api.inventory;

import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class PurchasedProductSummary extends PurchasedProduct {

	private double initialQuantity;

	private double finalQuantity;

	public PurchasedProductSummary(UUID skuId, String productName, double quantity, double totalPrice,
			double totalDiscount, double totalTax, double initialQuantity, double finalQuantity) {
		super(skuId, productName, quantity, totalPrice, totalDiscount, totalTax);
		this.initialQuantity = initialQuantity;
		this.finalQuantity = finalQuantity;
	}

	public double getInitialQuantity() {
		return initialQuantity;
	}

	public double getFinalQuantity() {
		return finalQuantity;
	}

	public void formatSummary() {
		initialQuantity = Math.round(initialQuantity * 100) / 100d;
		finalQuantity = Math.round(finalQuantity * 100) / 100d;
		format();
	}

}
