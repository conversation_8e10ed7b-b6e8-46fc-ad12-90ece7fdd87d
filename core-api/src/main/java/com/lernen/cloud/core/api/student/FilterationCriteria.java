/**
 * 
 */
package com.lernen.cloud.core.api.student;

import java.util.List;
import java.util.UUID;

import com.lernen.cloud.core.api.common.AreaType;
import com.lernen.cloud.core.api.user.Gender;
import com.lernen.cloud.core.api.user.UserCategory;

/**
 * <AUTHOR>
 *
 */
public class FilterationCriteria {

	private Boolean rte;

	private List<UserCategory> category;

	private List<Gender> gender;

	private List<String> religion;

	private List<AreaType> areaType;

	private Boolean speciallyAbled;

	private Boolean bpl;

	private List<String> state;

	private Boolean newAdmissionsOnly;

	private List<UUID> instituteHouseId;

	public FilterationCriteria() {

	}

	public FilterationCriteria(Boolean rte, List<UserCategory> category,
			List<Gender> gender, List<String> religion, List<AreaType> areaType,
			Boolean speciallyAbled, Boolean bpl, List<String> state, Boolean newAdmissionsOnly,List<UUID> instituteHouseId) {
		this.rte = rte;
		this.category = category;
		this.gender = gender;
		this.religion = religion;
		this.areaType = areaType;
		this.speciallyAbled = speciallyAbled;
		this.bpl = bpl;
		this.state = state;
		this.newAdmissionsOnly = newAdmissionsOnly;
		this.instituteHouseId = instituteHouseId;
	}

	public Boolean getRte() {
		return rte;
	}

	public void setRte(Boolean rte) {
		this.rte = rte;
	}

	public List<UserCategory> getCategory() {
		return category;
	}

	public void setCategory(List<UserCategory> category) {
		this.category = category;
	}

	public List<Gender> getGender() {
		return gender;
	}

	public void setGender(List<Gender> gender) {
		this.gender = gender;
	}

	public List<String> getReligion() {
		return religion;
	}

	public void setReligion(List<String> religion) {
		this.religion = religion;
	}

	public List<AreaType> getAreaType() {
		return areaType;
	}

	public void setAreaType(List<AreaType> areaType) {
		this.areaType = areaType;
	}

	public Boolean getSpeciallyAbled() {
		return speciallyAbled;
	}

	public void setSpeciallyAbled(Boolean speciallyAbled) {
		this.speciallyAbled = speciallyAbled;
	}

	public Boolean getBpl() {
		return bpl;
	}

	public void setBpl(Boolean bpl) {
		this.bpl = bpl;
	}

	public List<String> getState() {
		return state;
	}

	public void setState(List<String> state) {
		this.state = state;
	}


	public Boolean getNewAdmissionsOnly() {
		return newAdmissionsOnly;
	}

	public void setNewAdmissionsOnly(Boolean newAdmissionsOnly) {
		this.newAdmissionsOnly = newAdmissionsOnly;
	}

	public List<UUID> getInstituteHouseId(){
		return this.instituteHouseId;
	}

	public void setInstituteHouseId(List<UUID> instituteHouseId){
		this.instituteHouseId = instituteHouseId;
	}

	@Override
	public String toString() {
		return "FilterationCriteria{" +
				"rte=" + rte +
				", category=" + category +
				", gender=" + gender +
				", religion=" + religion +
				", areaType=" + areaType +
				", speciallyAbled=" + speciallyAbled +
				", bpl=" + bpl +
				", state=" + state +
				", newAdmissionsOnly=" + newAdmissionsOnly +
				", instituteHouseId=" + instituteHouseId + 
				'}';
	}

}
