package com.lernen.cloud.core.api.configurations;

import com.lernen.cloud.core.api.common.SessionDisplayFormat;

import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class ExamAdmitCardPreferences {

	public static final String ITEMS_PER_ROW = "items_per_row";
	public static final String ROWS_PER_PAGE = "rows_per_page";
	public static final String TITLE_TEXT = "title_text";
	public static final String BOLD_EXAM_NAME = "bold_exam_name";
	public static final String SESSION_DISPLAY_FORMAT = "session_display_format";
	public static final String ADMISSION_NUMBER_KEY_TEXT = "admission_number_key_text";
	public static final String LEFT_SIGNATURE_DESIGNATION_TEXT = "left_signature_designation_text";
	public static final String SIGNATURE_DESIGNATION_TEXT = "signature_designation_text";
	public static final String INCLUDE_CLASS_ROLL_NUMBER = "include_class_roll_number";
	public static final String HEADER_SECTION_TABLE_WIDTHS = "header_section_table_widths";
	public static final String INCLUDE_DIMENSION_IN_ADMIT_CARD = "include_dimension_in_admit_card";

	public static final String LOGO_WIDTH = "logo_width";
	public static final String LOGO_HEIGHT = "logo_height";

	private int itemsPerRow;

	private int rowsPerPage;

	private String titleText;

	private boolean boldExamName;

	private SessionDisplayFormat sessionDisplayFormat;

	private String admissionNumberKeyText;

	private String leftSignatureDesignationText;

	private String signatureDesignationText;

	private boolean includeClassRollNumber;

	private List<Float> headerSectionTableWidths;

	private boolean includeDimensionInAdmitCard;

	private Float logoWidth;

	private Float logoHeight;

	public static String getConfigType() {
		return "exam_admit_card_preferences";
	}

	public int getItemsPerRow() {
		return itemsPerRow;
	}

	public void setItemsPerRow(int itemsPerRow) {
		this.itemsPerRow = itemsPerRow;
	}

	public int getRowsPerPage() {
		return rowsPerPage;
	}

	public void setRowsPerPage(int rowsPerPage) {
		this.rowsPerPage = rowsPerPage;
	}

	public String getTitleText() {
		return titleText;
	}

	public void setTitleText(String titleText) {
		this.titleText = titleText;
	}

	public boolean isBoldExamName() {
		return boldExamName;
	}

	public void setBoldExamName(boolean boldExamName) {
		this.boldExamName = boldExamName;
	}

	public SessionDisplayFormat getSessionDisplayFormat() {
		return sessionDisplayFormat;
	}

	public void setSessionDisplayFormat(
			SessionDisplayFormat sessionDisplayFormat) {
		this.sessionDisplayFormat = sessionDisplayFormat;
	}

	public String getAdmissionNumberKeyText() {
		return admissionNumberKeyText;
	}

	public void setAdmissionNumberKeyText(String admissionNumberKeyText) {
		this.admissionNumberKeyText = admissionNumberKeyText;
	}

	public String getSignatureDesignationText() {
		return signatureDesignationText;
	}

	public void setSignatureDesignationText(String signatureDesignationText) {
		this.signatureDesignationText = signatureDesignationText;
	}

	public boolean isIncludeClassRollNumber() {
		return includeClassRollNumber;
	}

	public void setIncludeClassRollNumber(boolean includeClassRollNumber) {
		this.includeClassRollNumber = includeClassRollNumber;
	}

	public List<Float> getHeaderSectionTableWidths() {
		return headerSectionTableWidths;
	}

	public void setHeaderSectionTableWidths(
			List<Float> headerSectionTableWidths) {
		this.headerSectionTableWidths = headerSectionTableWidths;
	}

	public boolean isIncludeDimensionInAdmitCard() {
		return includeDimensionInAdmitCard;
	}

	public void setIncludeDimensionInAdmitCard(boolean includeDimensionInAdmitCard) {
		this.includeDimensionInAdmitCard = includeDimensionInAdmitCard;
	}

	public Float getLogoWidth() {
		return logoWidth;
	}

	public void setLogoWidth(Float logoWidth) {
		this.logoWidth = logoWidth;
	}

	public Float getLogoHeight() {
		return logoHeight;
	}

	public void setLogoHeight(Float logoHeight) {
		this.logoHeight = logoHeight;
	}

	public String getLeftSignatureDesignationText() {
		return leftSignatureDesignationText;
	}

	public void setLeftSignatureDesignationText(String leftSignatureDesignationText) {
		this.leftSignatureDesignationText = leftSignatureDesignationText;
	}

	@Override
	public String toString() {
		return "ExamAdmitCardPreferences{" +
				"itemsPerRow=" + itemsPerRow +
				", rowsPerPage=" + rowsPerPage +
				", titleText='" + titleText + '\'' +
				", boldExamName=" + boldExamName +
				", sessionDisplayFormat=" + sessionDisplayFormat +
				", admissionNumberKeyText='" + admissionNumberKeyText + '\'' +
				", leftSignatureDesignationText='" + leftSignatureDesignationText + '\'' +
				", signatureDesignationText='" + signatureDesignationText + '\'' +
				", includeClassRollNumber=" + includeClassRollNumber +
				", headerSectionTableWidths=" + headerSectionTableWidths +
				", includeDimensionInAdmitCard=" + includeDimensionInAdmitCard +
				", logoWidth=" + logoWidth +
				", logoHeight=" + logoHeight +
				'}';
	}
}
