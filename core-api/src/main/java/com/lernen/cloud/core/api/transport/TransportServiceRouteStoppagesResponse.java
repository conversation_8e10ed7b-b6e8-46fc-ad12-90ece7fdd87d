package com.lernen.cloud.core.api.transport;

import com.lernen.cloud.core.api.institute.Time;

/**
 * 
 * <AUTHOR>
 *
 */
public class TransportServiceRouteStoppagesResponse {

	private TransportArea transportArea;

	private Time pickupTime;

	private Time dropTime;

	private Double assignedAmount;

	private int studentCount;

	public TransportServiceRouteStoppagesResponse(TransportArea transportArea, Time pickupTime, Time dropTime,
			Double assignedAmount) {
		this.transportArea = transportArea;
		this.pickupTime = pickupTime;
		this.dropTime = dropTime;
		this.assignedAmount = assignedAmount;
	}

	public TransportArea getTransportArea() {
		return transportArea;
	}

	public void setTransportArea(TransportArea transportArea) {
		this.transportArea = transportArea;
	}

	public Time getPickupTime() {
		return pickupTime;
	}

	public void setPickupTime(Time pickupTime) {
		this.pickupTime = pickupTime;
	}

	public Time getDropTime() {
		return dropTime;
	}

	public void setDropTime(Time dropTime) {
		this.dropTime = dropTime;
	}

	public Double getAssignedAmount() {
		return assignedAmount;
	}

	public void setAssignedAmount(Double assignedAmount) {
		this.assignedAmount = assignedAmount;
	}

	public int getStudentCount() {
		return studentCount;
	}

	public void setStudentCount(int studentCount) {
		this.studentCount = studentCount;
	}

	@Override
	public String toString() {
		return "TransportServiceRouteStoppagesResponse [transportArea=" + transportArea + ", pickupTime=" + pickupTime
				+ ", dropTime=" + dropTime + ", assignedAmount=" + assignedAmount + ", studentCount=" + studentCount
				+ "]";
	}

}
