package com.lernen.cloud.core.api.examination.report.greensheet;

import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class GreenSheetExamDimensionStructure<T extends IGreenSheetExamDimensionMapData> {

	private String dimensionName;

	private Double maxMarks;

	private List<T> greenSheetExamDimensionMapDataList;

	public GreenSheetExamDimensionStructure() {

	}

	public GreenSheetExamDimensionStructure(String dimensionName,
			Double maxMarks, List<T> greenSheetExamDimensionMapDataList) {
		this.dimensionName = dimensionName;
		this.maxMarks = maxMarks;
		this.greenSheetExamDimensionMapDataList = greenSheetExamDimensionMapDataList;
	}

	public String getDimensionName() {
		return dimensionName;
	}

	public void setDimensionName(String dimensionName) {
		this.dimensionName = dimensionName;
	}

	public Double getMaxMarks() {
		return maxMarks;
	}

	public void setMaxMarks(Double maxMarks) {
		this.maxMarks = maxMarks;
	}

	public List<T> getGreenSheetExamDimensionMapDataList() {
		return greenSheetExamDimensionMapDataList;
	}

	public void setGreenSheetExamDimensionMapDataList(
			List<T> greenSheetExamDimensionMapDataList) {
		this.greenSheetExamDimensionMapDataList = greenSheetExamDimensionMapDataList;
	}

}
