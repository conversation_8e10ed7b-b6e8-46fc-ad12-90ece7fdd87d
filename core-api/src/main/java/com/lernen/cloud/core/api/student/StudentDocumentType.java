package com.lernen.cloud.core.api.student;

import java.util.HashSet;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;

import com.lernen.cloud.core.api.common.DocumentType;

/**
 * <AUTHOR>
 *
 */
public enum StudentDocumentType implements DocumentType{
	
	TRANSFER_CERTIFICATE("Transfer Certificate", "Transfer Certificate"),
	AADHAR_CARD("Aadhar Card", "Aadhar Card"),
	STUDENT_PROFILE_IMAGE("Student Profile Image", "Student Profile Image"),
	STUDENT_PROFILE_IMAGE_THUMBNAIL("Student Profile Image Thumbnail", "Student Profile Image Thumbnail", true),
	FATHER_PROFILE_IMAGE("Father Profile Image", "Father Profile Image"),
	MOTHER_PROFILE_IMAGE("Mother Profile Image", "Mother Profile Image"),
	RATION_CARD("Ration Card", "Ration Card"),
	PREVIOUS_SCHOOL_STUDY_CERTIFICATE("Previous School Study Certificate", "Previous School Study Certificate"),
	BIRTH_CERTIFICATE("Birth Certificate", "Birth Certificate"),
	STUDENT_PAN_CARD("Student Pan Card", "Student Pan Card"),
	BIRTH_CERTIFICATE_AND_AFFIDAVIT("Birth Certificate & Affidavit", "Birth Certificate & Affidavit"),

	OTHER(null, "Other");

	private String documentName;
	private String displayName;
	private boolean isThumbnail;

	private StudentDocumentType(String documentName, String displayName, boolean isThumbnail) {
		this.documentName = documentName;
		this.displayName = displayName;
		this.isThumbnail = isThumbnail;
	}

	private StudentDocumentType(String documentName, String displayName) {
		this.documentName = documentName;
		this.displayName = displayName;
		this.isThumbnail = false;
	}

	public static StudentDocumentType getStudentDocumentType(String documentType) {
		if (StringUtils.isBlank(documentType)) {
			return null;
		}
		for (StudentDocumentType documentTypeEnum : StudentDocumentType.values()) {
			if (documentTypeEnum.name().equalsIgnoreCase(documentType)) {
				return documentTypeEnum;
			}
		}
		return null;
	}

	public static Set<StudentDocumentType> getDocumentTypes(String documentsTypeStr){
		Set<StudentDocumentType> documentTypes = new HashSet<StudentDocumentType>();
		if(!StringUtils.isEmpty(documentsTypeStr)) {
			String[] types = documentsTypeStr.split(",");
			for(String stats : types) {
				if (StudentDocumentType.getStudentDocumentType(stats) == null) {
					continue;
				}
				documentTypes.add(StudentDocumentType.getStudentDocumentType(stats));
			}
			return documentTypes;
		}

		StudentDocumentType[] types = StudentDocumentType.values();
		for(StudentDocumentType documentsType: types){
			documentTypes.add(documentsType);
		}	
		return documentTypes;	
	}
	
	@Override
	public String getDocumentName() {
		return documentName;
	}
	
	@Override
	public String getDisplayName() {
		return displayName;
	}

	@Override
	public boolean isThumbnail() { return isThumbnail; }

}
