/**
 * 
 */
package com.lernen.cloud.core.api.user;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 *
 */
public class UserPayload {
	
	private int instituteId;
	
	private UUID uuid;
	
	private UserType userType;
	
	private String primaryEmail;
	
	private String primaryContactNumber;
	
	private String userName;

	private String password;
	
	private List<Module> authorizedModules;
	
	private List<UUID> roles;

	private List<Integer> instituteScope;

	private boolean isViewOnlyAccess;

	public UserPayload(int instituteId, UUID uuid, UserType userType, String primaryEmail, String primaryContactNumber,
					   String userName, String password, List<Module> authorizedModules, List<UUID> roles,
					   List<Integer> instituteScope, boolean isViewOnlyAccess) {
		this.instituteId = instituteId;
		this.uuid = uuid;
		this.userType = userType;
		this.primaryEmail = primaryEmail;
		this.primaryContactNumber = primaryContactNumber;
		this.userName = userName;
		this.password = password;
		this.authorizedModules = authorizedModules;
		this.roles = roles;
		this.instituteScope = instituteScope;
		this.isViewOnlyAccess = isViewOnlyAccess;
	}

	public UserPayload() {
	}

	public int getInstituteId() {
		return instituteId;
	}

	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	public UUID getUuid() {
		return uuid;
	}

	public void setUuid(UUID uuid) {
		this.uuid = uuid;
	}

	public UserType getUserType() {
		return userType;
	}

	public void setUserType(UserType userType) {
		this.userType = userType;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public List<Module> getAuthorizedModules() {
		return authorizedModules;
	}

	public void setAuthorizedModules(List<Module> authorizedModules) {
		this.authorizedModules = authorizedModules;
	}

	public List<UUID> getRoles() {
		return roles;
	}

	public void setRoles(List<UUID> roles) {
		this.roles = roles;
	}

	/**
	 * @return the primaryEmail
	 */
	public String getPrimaryEmail() {
		return primaryEmail;
	}

	/**
	 * @param primaryEmail the primaryEmail to set
	 */
	public void setPrimaryEmail(String primaryEmail) {
		this.primaryEmail = primaryEmail;
	}

	/**
	 * @return the primaryContactNumber
	 */
	public String getPrimaryContactNumber() {
		return primaryContactNumber;
	}

	/**
	 * @param primaryContactNumber the primaryContactNumber to set
	 */
	public void setPrimaryContactNumber(String primaryContactNumber) {
		this.primaryContactNumber = primaryContactNumber;
	}

	public List<Integer> getInstituteScope() {
		return instituteScope;
	}

	public void setInstituteScope(List<Integer> instituteScope) {
		this.instituteScope = instituteScope;
	}

	public boolean isViewOnlyAccess() {
		return isViewOnlyAccess;
	}

	public void setViewOnlyAccess(boolean viewOnlyAccess) {
		isViewOnlyAccess = viewOnlyAccess;
	}

	@Override
	public String toString() {
		return "UserPayload{" +
				"instituteId=" + instituteId +
				", uuid=" + uuid +
				", userType=" + userType +
				", primaryEmail='" + primaryEmail + '\'' +
				", primaryContactNumber='" + primaryContactNumber + '\'' +
				", userName='" + userName + '\'' +
				", password='" + password + '\'' +
				", authorizedModules=" + authorizedModules +
				", roles=" + roles +
				", instituteScope=" + instituteScope +
				", isViewOnlyAccess=" + isViewOnlyAccess +
				'}';
	}

}
