package com.lernen.cloud.core.api.student;

import com.lernen.cloud.core.api.user.BloodGroup;
import com.lernen.cloud.core.api.user.Document;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class StudentDetailedRow {

	private final int instituteId;
	private final UUID studentId;

	private final StudentBasicInfo studentBasicInfo;

	private final String mothersName;

	private final String fathersName;

	private final String mothersQualification;

	private final String fathersQualification;

	private final String mothersContactNumber;

	private final String fathersContactNumber;

	private final String mothersOccupation;

	private final String mothersAnnualIncome;

	private final String fathersOccupation;

	private final String fathersAnnualIncome;

	private final String mothersAadharNumber;

	private final String fathersAadharNumber;

	private final String mothersPanCardDetails;

	private final String fathersPanCardDetails;

	private final String approxFamilyIncome;

	private final String guardiansInfoList;

	private final String schoolName;

	private final String classPassed;

	private final String medium;

	private final String percentage;

	private final String result;

	private final Integer yearOfPassing;

	private final BloodGroup bloodGroup;

	private final String bloodPressure;

	private final String pulse;

	private final String height;

	private final String weight;

	private final Integer dateOfPhysicalExamination;

	private final StudentAcademicSessionInfoResponse studentAcademicSessionInfoResponse;

	private final StudentStatus studentStatus;

	private final StudentStatus finalStudentStatus;

	private final int admissionSession;

	private final List<Document<StudentDocumentType>> studentDocuments;

	private Map<TransferCertificateVariables, String> tcVariables;

	private boolean isAdmissionTcBased;

	private String tcNumber;

	private String deviceUserId;

	private Boolean newAdmission;

	private StudentTransferCertificateDetails studentTransferCertificateDetails;

	private List<StudentTaggedDetails> studentTaggedDetailsList;

	public StudentDetailedRow(int instituteId, UUID studentId, StudentBasicInfo studentBasicInfo, String mothersName, String fathersName, String mothersQualification, String fathersQualification, String mothersContactNumber, String fathersContactNumber, String mothersOccupation, String mothersAnnualIncome, String fathersOccupation, String fathersAnnualIncome, String mothersAadharNumber, String fathersAadharNumber, String mothersPanCardDetails, String fathersPanCardDetails, String approxFamilyIncome, String guardiansInfoList, String schoolName, String classPassed, String medium, String percentage, String result, Integer yearOfPassing, BloodGroup bloodGroup, String bloodPressure, String pulse, String height, String weight, Integer dateOfPhysicalExamination, StudentAcademicSessionInfoResponse studentAcademicSessionInfoResponse, StudentStatus studentStatus, StudentStatus finalStudentStatus, int admissionSession, List<Document<StudentDocumentType>> studentDocuments, Map<TransferCertificateVariables, String> tcVariables, boolean isAdmissionTcBased, String tcNumber, String deviceUserId, Boolean newAdmission, StudentTransferCertificateDetails studentTransferCertificateDetails, List<StudentTaggedDetails> studentTaggedDetailsList) {
		this.instituteId = instituteId;
		this.studentId = studentId;
		this.studentBasicInfo = studentBasicInfo;
		this.mothersName = mothersName;
		this.fathersName = fathersName;
		this.mothersQualification = mothersQualification;
		this.fathersQualification = fathersQualification;
		this.mothersContactNumber = mothersContactNumber;
		this.fathersContactNumber = fathersContactNumber;
		this.mothersOccupation = mothersOccupation;
		this.mothersAnnualIncome = mothersAnnualIncome;
		this.fathersOccupation = fathersOccupation;
		this.fathersAnnualIncome = fathersAnnualIncome;
		this.mothersAadharNumber = mothersAadharNumber;
		this.fathersAadharNumber = fathersAadharNumber;
		this.mothersPanCardDetails = mothersPanCardDetails;
		this.fathersPanCardDetails = fathersPanCardDetails;
		this.approxFamilyIncome = approxFamilyIncome;
		this.guardiansInfoList = guardiansInfoList;
		this.schoolName = schoolName;
		this.classPassed = classPassed;
		this.medium = medium;
		this.percentage = percentage;
		this.result = result;
		this.yearOfPassing = yearOfPassing;
		this.bloodGroup = bloodGroup;
		this.bloodPressure = bloodPressure;
		this.pulse = pulse;
		this.height = height;
		this.weight = weight;
		this.dateOfPhysicalExamination = dateOfPhysicalExamination;
		this.studentAcademicSessionInfoResponse = studentAcademicSessionInfoResponse;
		this.studentStatus = studentStatus;
		this.finalStudentStatus = finalStudentStatus;
		this.admissionSession = admissionSession;
		this.studentDocuments = studentDocuments;
		this.tcVariables = tcVariables;
		this.isAdmissionTcBased = isAdmissionTcBased;
		this.tcNumber = tcNumber;
		this.deviceUserId = deviceUserId;
		this.newAdmission = newAdmission;
		this.studentTransferCertificateDetails = studentTransferCertificateDetails;
		this.studentTaggedDetailsList = studentTaggedDetailsList;
	}

	public int getInstituteId() {
		return instituteId;
	}

	public UUID getStudentId() {
		return studentId;
	}

	public StudentBasicInfo getStudentBasicInfo() {
		return studentBasicInfo;
	}

	public String getMothersName() {
		return mothersName;
	}

	public String getFathersName() {
		return fathersName;
	}

	public String getMothersContactNumber() {
		return mothersContactNumber;
	}

	public String getFathersContactNumber() {
		return fathersContactNumber;
	}

	public String getMothersOccupation() {
		return mothersOccupation;
	}

	public String getMothersAnnualIncome() {
		return mothersAnnualIncome;
	}

	public String getFathersOccupation() {
		return fathersOccupation;
	}

	public String getFathersAnnualIncome() {
		return fathersAnnualIncome;
	}

	public String getMothersAadharNumber() {
		return mothersAadharNumber;
	}

	public String getFathersAadharNumber() {
		return fathersAadharNumber;
	}

	public String getMothersPanCardDetails() { return  mothersPanCardDetails; }

	public String getFathersPanCardDetails() { return fathersPanCardDetails; }

	public String getApproxFamilyIncome() {
		return approxFamilyIncome;
	}

	public String getGuardiansInfoList() {
		return guardiansInfoList;
	}

	public String getSchoolName() {
		return schoolName;
	}

	public String getClassPassed() {
		return classPassed;
	}

	public String getMedium() {
		return medium;
	}

	public String getPercentage() {
		return percentage;
	}

	public String getResult() {
		return result;
	}

	public Integer getYearOfPassing() {
		return yearOfPassing;
	}

	public BloodGroup getBloodGroup() {
		return bloodGroup;
	}

	public String getBloodPressure() {
		return bloodPressure;
	}

	public String getPulse() {
		return pulse;
	}

	public String getHeight() {
		return height;
	}

	public String getWeight() {
		return weight;
	}

	public Integer getDateOfPhysicalExamination() {
		return dateOfPhysicalExamination;
	}

	public StudentAcademicSessionInfoResponse getStudentAcademicSessionInfoResponse() {
		return studentAcademicSessionInfoResponse;
	}

	public StudentStatus getStudentStatus() {
		return studentStatus;
	}

	public StudentStatus getFinalStudentStatus() {
		return finalStudentStatus;
	}

	public int getAdmissionSession() {
		return admissionSession;
	}

	public List<Document<StudentDocumentType>> getStudentDocuments() {
		return studentDocuments;
	}

	public String getMothersQualification() {
		return mothersQualification;
	}

	public String getFathersQualification() {
		return fathersQualification;
	}

	public Map<TransferCertificateVariables, String> getTcVariables() {
		return tcVariables;
	}

	public void setTcVariables(Map<TransferCertificateVariables, String> tcVariables) {
		this.tcVariables = tcVariables;
	}

	public boolean isAdmissionTcBased() {
		return isAdmissionTcBased;
	}

	public void setAdmissionTcBased(boolean admissionTcBased) {
		isAdmissionTcBased = admissionTcBased;
	}

	public String getTcNumber() {
		return tcNumber;
	}

	public void setTcNumber(String tcNumber) {
		this.tcNumber = tcNumber;
	}

	public String getDeviceUserId() {
		return deviceUserId;
	}

	public void setDeviceUserId(String deviceUserId) {
		this.deviceUserId = deviceUserId;
	}

	public Boolean isNewAdmission() {
		return newAdmission;
	}

	public void setNewAdmission(Boolean newAdmission) {
		this.newAdmission = newAdmission;
	}

	public StudentTransferCertificateDetails getStudentTransferCertificateDetails() {
		return studentTransferCertificateDetails;
	}

	public void setStudentTransferCertificateDetails(StudentTransferCertificateDetails studentTransferCertificateDetails) {
		this.studentTransferCertificateDetails = studentTransferCertificateDetails;
	}

	public List<StudentTaggedDetails> getStudentTaggedDetailsList() {
		return studentTaggedDetailsList;
	}

	public void setStudentTaggedDetailsList(List<StudentTaggedDetails> studentTaggedDetailsList) {
		this.studentTaggedDetailsList = studentTaggedDetailsList;
	}

	@Override
	public String toString() {
		return "StudentDetailedRow{" +
				"instituteId=" + instituteId +
				", studentId=" + studentId +
				", studentBasicInfo=" + studentBasicInfo +
				", mothersName='" + mothersName + '\'' +
				", fathersName='" + fathersName + '\'' +
				", mothersQualification='" + mothersQualification + '\'' +
				", fathersQualification='" + fathersQualification + '\'' +
				", mothersContactNumber='" + mothersContactNumber + '\'' +
				", fathersContactNumber='" + fathersContactNumber + '\'' +
				", mothersOccupation='" + mothersOccupation + '\'' +
				", mothersAnnualIncome='" + mothersAnnualIncome + '\'' +
				", fathersOccupation='" + fathersOccupation + '\'' +
				", fathersAnnualIncome='" + fathersAnnualIncome + '\'' +
				", mothersAadharNumber='" + mothersAadharNumber + '\'' +
				", fathersAadharNumber='" + fathersAadharNumber + '\'' +
				", mothersPanCardDetails='" + mothersPanCardDetails + '\'' +
				", fathersPanCardDetails='" + fathersPanCardDetails + '\''+
				", approxFamilyIncome='" + approxFamilyIncome + '\'' +
				", guardiansInfoList='" + guardiansInfoList + '\'' +
				", schoolName='" + schoolName + '\'' +
				", classPassed='" + classPassed + '\'' +
				", medium='" + medium + '\'' +
				", percentage='" + percentage + '\'' +
				", result='" + result + '\'' +
				", yearOfPassing=" + yearOfPassing +
				", bloodGroup=" + bloodGroup +
				", bloodPressure='" + bloodPressure + '\'' +
				", pulse='" + pulse + '\'' +
				", height='" + height + '\'' +
				", weight='" + weight + '\'' +
				", dateOfPhysicalExamination=" + dateOfPhysicalExamination +
				", studentAcademicSessionInfoResponse=" + studentAcademicSessionInfoResponse +
				", studentStatus=" + studentStatus +
				", finalStudentStatus=" + finalStudentStatus +
				", admissionSession=" + admissionSession +
				", studentDocuments=" + studentDocuments +
				", tcVariables=" + tcVariables +
				", isAdmissionTcBased=" + isAdmissionTcBased +
				", tcNumber='" + tcNumber + '\'' +
				", deviceUserId='" + deviceUserId + '\'' +
				", newAdmission=" + newAdmission +
				", studentTaggedDetailsList=" + studentTaggedDetailsList +
				'}';
	}

}
