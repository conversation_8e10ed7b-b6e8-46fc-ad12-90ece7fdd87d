package com.lernen.cloud.core.api.audit.log;

import java.util.UUID;

import com.lernen.cloud.core.api.user.User;

/**
 * 
 * <AUTHOR>
 *
 */
public class AuditLog {

	private final int instituteId;

	private final UUID logId;

	private final User user;

	private final AuditLogActionData audiLogActionData;

	private final String miniLogStatement;

	private String detailedLogStatement;

	private final Integer logTime;

	public AuditLog(int instituteId, UUID logId, User user,
			AuditLogActionData audiLogActionData, String miniLogStatement,
			String detailedLogStatement, Integer logTime) {
		this.instituteId = instituteId;
		this.logId = logId;
		this.user = user;
		this.audiLogActionData = audiLogActionData;
		this.miniLogStatement = miniLogStatement;
		this.detailedLogStatement = detailedLogStatement;
		this.logTime = logTime;
	}

	public int getInstituteId() {
		return instituteId;
	}

	public UUID getLogId() {
		return logId;
	}

	public User getUser() {
		return user;
	}

	public AuditLogActionData getAudiLogActionData() {
		return audiLogActionData;
	}

	public String getMiniLogStatement() {
		return miniLogStatement;
	}

	public String getDetailedLogStatement() {
		return detailedLogStatement;
	}

	public void setDetailedLogStatement(String detailedLogStatement) {
		this.detailedLogStatement = detailedLogStatement;
	}

	public Integer getLogTime() {
		return logTime;
	}

	@Override
	public String toString() {
		return "AuditLog [instituteId=" + instituteId + ", logId=" + logId
				+ ", user=" + user + ", audiLogActionData=" + audiLogActionData
				+ ", miniLogStatement=" + miniLogStatement
				+ ", detailedLogStatement=" + detailedLogStatement
				+ ", logTime=" + logTime + "]";
	}

}
