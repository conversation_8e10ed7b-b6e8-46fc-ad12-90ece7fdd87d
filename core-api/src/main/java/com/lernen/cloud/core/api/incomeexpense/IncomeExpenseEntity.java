/**
 * 
 */
package com.lernen.cloud.core.api.incomeexpense;

import java.util.UUID;

/**
 * <AUTHOR>
 *
 */
public class IncomeExpenseEntity {
	
	private int instituteId;
	
	private UUID entityId;
	
	private String entityName;
	
	private IncomeExpenseCategory incomeExpenseCategory;
	
	private IncomeExpenseTransactionType incomeExpenseTransactionType;
	
	private String entityDescription;
	
	private Double amount;

	public IncomeExpenseEntity(int instituteId, UUID entityId, String entityName,
			IncomeExpenseCategory incomeExpenseCategory, IncomeExpenseTransactionType incomeExpenseTransactionType,
			String entityDescription, Double amount) {
		this.instituteId = instituteId;
		this.entityId = entityId;
		this.entityName = entityName;
		this.incomeExpenseCategory = incomeExpenseCategory;
		this.incomeExpenseTransactionType = incomeExpenseTransactionType;
		this.entityDescription = entityDescription;
		this.amount = amount;
	}

	public IncomeExpenseEntity() {
		
	}

	public int getInstituteId() {
		return instituteId;
	}

	public void setInstituteId(int instituteId) {
		this.instituteId = instituteId;
	}

	public UUID getEntityId() {
		return entityId;
	}

	public void setEntityId(UUID entityId) {
		this.entityId = entityId;
	}

	public String getEntityName() {
		return entityName;
	}

	public void setEntityName(String entityName) {
		this.entityName = entityName;
	}

	public IncomeExpenseCategory getIncomeExpenseCategory() {
		return incomeExpenseCategory;
	}

	public void setIncomeExpenseCategory(IncomeExpenseCategory incomeExpenseCategory) {
		this.incomeExpenseCategory = incomeExpenseCategory;
	}

	public IncomeExpenseTransactionType getIncomeExpenseTransactionType() {
		return incomeExpenseTransactionType;
	}

	public void setIncomeExpenseTransactionType(IncomeExpenseTransactionType incomeExpenseTransactionType) {
		this.incomeExpenseTransactionType = incomeExpenseTransactionType;
	}

	public String getEntityDescription() {
		return entityDescription;
	}

	public void setEntityDescription(String entityDescription) {
		this.entityDescription = entityDescription;
	}

	public Double getAmount() {
		return amount;
	}

	public void setAmount(Double amount) {
		this.amount = amount;
	}

	@Override
	public String toString() {
		return "IncomeExpenseEntity [instituteId=" + instituteId + ", entityId=" + entityId + ", entityName="
				+ entityName + ", incomeExpenseCategory=" + incomeExpenseCategory + ", incomeExpenseTransactionType="
				+ incomeExpenseTransactionType + ", entityDescription=" + entityDescription + ", amount=" + amount
				+ "]";
	}
	
}
