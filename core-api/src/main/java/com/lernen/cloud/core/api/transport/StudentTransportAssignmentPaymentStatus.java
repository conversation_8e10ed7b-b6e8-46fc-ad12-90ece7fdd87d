package com.lernen.cloud.core.api.transport;

import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class StudentTransportAssignmentPaymentStatus {

	private final UUID transportHistoryId;

	private final UUID studentId;

	private final UUID pickupServiceRouteId;

	private final UUID dropServiceRouteId;

	private final int areaId;

	private final int currentTimestamp;

	private final int startDate;

	private final int endDate;

	private final boolean paymentCollected;

	private final UUID parentId;


	public StudentTransportAssignmentPaymentStatus(UUID transportHistoryId, UUID studentId, UUID pickupServiceRouteId, UUID dropServiceRouteId, int areaId, int currentTimestamp, int startDate, int endDate, boolean paymentCollected, UUID parentId) {
		this.transportHistoryId = transportHistoryId;
		this.studentId = studentId;
		this.pickupServiceRouteId = pickupServiceRouteId;
		this.dropServiceRouteId = dropServiceRouteId;
		this.areaId = areaId;
		this.currentTimestamp = currentTimestamp;
		this.startDate = startDate;
		this.endDate = endDate;
		this.paymentCollected = paymentCollected;
		this.parentId = parentId;
	}

	public UUID getTransportHistoryId() {
		return transportHistoryId;
	}

	public UUID getStudentId() {
		return studentId;
	}

	public UUID getPickupServiceRouteId() {
		return pickupServiceRouteId;
	}

	public UUID getDropServiceRouteId() {
		return dropServiceRouteId;
	}

	public int getAreaId() {
		return areaId;
	}

	public int getCurrentTimestamp() {
		return currentTimestamp;
	}

	public int getStartDate() {
		return startDate;
	}

	public int getEndDate() {
		return endDate;
	}

	public boolean isPaymentCollected() {
		return paymentCollected;
	}

	public UUID getParentId() {
		return parentId;
	}


	@Override
	public String toString() {
		return "StudentTransportAssignmentPaymentStatusRow{" +
				"transportHistoryId=" + transportHistoryId +
				", studentId=" + studentId +
				", pickupServiceRouteId=" + pickupServiceRouteId +
				", dropServiceRouteId=" + dropServiceRouteId +
				", areaId=" + areaId +
				", currentTimestamp=" + currentTimestamp +
				", startDate=" + startDate +
				", endDate=" + endDate +
				", paymentCollected=" + paymentCollected +
				", parentId=" + parentId +
				'}';
	}
}
