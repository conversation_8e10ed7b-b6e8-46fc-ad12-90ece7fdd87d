package com.lernen.cloud.core.api.fees.payment;

import com.lernen.cloud.core.api.student.Student;

/**
 * 
 * <AUTHOR>
 *
 */
public class StudentFeePaymentTransactionDetails {
	
	private final Student student;
	
	private final FeePaymentTransactionDetails feePaymentTransactionDetails;

	public StudentFeePaymentTransactionDetails(Student student,
			FeePaymentTransactionDetails feePaymentTransactionDetails) {
		this.student = student;
		this.feePaymentTransactionDetails = feePaymentTransactionDetails;
	}

	public Student getStudent() {
		return student;
	}

	public FeePaymentTransactionDetails getFeePaymentTransactionDetails() {
		return feePaymentTransactionDetails;
	}

	@Override
	public String toString() {
		return "FeePaymentInvoice [student=" + student + ", feePaymentTransactionDetails="
				+ feePaymentTransactionDetails + "]";
	}
	
	

}
