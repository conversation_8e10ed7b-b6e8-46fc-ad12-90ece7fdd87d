package com.lernen.cloud.core.api.examination;

import com.lernen.cloud.core.api.student.StudentDetailedRow;

/**
 * 
 * <AUTHOR>
 *
 */
public class ExamMarksDetailsRow {

	private final StudentDetailedRow studentDetailedRow;
	private final ExamMetaData examMetaData;
	private final ExamDimensionsRowDetails examDimensionsRowDetails;
	private final ExamCourseMarks examCourseMarks;

	public ExamMarksDetailsRow(StudentDetailedRow studentDetailedRow, ExamMetaData examMetaData,
			ExamDimensionsRowDetails examDimensionsRowDetails, ExamCourseMarks examCourseMarks) {
		this.studentDetailedRow = studentDetailedRow;
		this.examMetaData = examMetaData;
		this.examDimensionsRowDetails = examDimensionsRowDetails;
		this.examCourseMarks = examCourseMarks;
	}

	public StudentDetailedRow getStudentDetailedRow() {
		return studentDetailedRow;
	}

	public ExamMetaData getExamMetaData() {
		return examMetaData;
	}

	public ExamDimensionsRowDetails getExamDimensionsRowDetails() {
		return examDimensionsRowDetails;
	}

	public ExamCourseMarks getExamCourseMarks() {
		return examCourseMarks;
	}

}
