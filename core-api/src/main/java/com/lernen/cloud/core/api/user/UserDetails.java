/**
 * 
 */
package com.lernen.cloud.core.api.user;

import com.lernen.cloud.core.api.permissions.UserRole;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 *
 */
public class UserDetails {

	private final int instituteId;

	private final UUID uuid;

	private final String userInstituteId;

	private final String firstName;

	private final String name;

	private final UserType userType;

	private final List<Module> authorizedModules;

	private List<UserRole> userRoles;

	private final List<Integer> instituteScope;

	private final UserStatus userStatus;

	public UserDetails(int instituteId, UUID uuid, String userInstituteId,
			String firstName,String name, UserType userType, List<Module> authorizedModules,
			List<UserRole> userRoles, List<Integer> instituteScope, UserStatus userStatus) {
		this.instituteId = instituteId;
		this.uuid = uuid;
		this.userInstituteId = userInstituteId;
		this.firstName = firstName;
		this.name = name;
		this.userType = userType;
		this.authorizedModules = authorizedModules;
		this.userRoles = userRoles;
		this.instituteScope = instituteScope;
		this.userStatus = userStatus;
	}

	public int getInstituteId() {
		return instituteId;
	}

	public UUID getUuid() {
		return uuid;
	}

	public String getUserInstituteId() {
		return userInstituteId;
	}

	public String getFirstName() {
		return firstName;
	}

	public String getName() { return name; }

	public UserType getUserType() {
		return userType;
	}

	public List<Module> getAuthorizedModules() {
		return authorizedModules;
	}

	public List<UserRole> getUserRoles() {
		return userRoles;
	}

	public void setUserRoles(List<UserRole> userRoles) {
		this.userRoles = userRoles;
	}

	public List<Integer> getInstituteScope() {
		return instituteScope;
	}

	public UserStatus getUserStatus() {
		return userStatus;
	}

	@Override
	public String toString() {
		return "UserDetails{" +
				"instituteId=" + instituteId +
				", uuid=" + uuid +
				", userInstituteId='" + userInstituteId + '\'' +
				", firstName='" + firstName + '\'' +
				", name='" + name + '\'' +
				", userType=" + userType +
				", authorizedModules=" + authorizedModules +
				", userRoles=" + userRoles +
				", instituteScope=" + instituteScope +
				", userStatus=" + userStatus +
				'}';
	}

}
