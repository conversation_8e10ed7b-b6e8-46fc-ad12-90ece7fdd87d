package com.lernen.cloud.core.api.report;

import com.lernen.cloud.core.api.institute.Institute;

import java.util.List;

/**
 * <AUTHOR>
 */

public class HTMLReportTable {

    private final String reportName;
    private final boolean showDateAndTime;
    private final List<List<HTMLReportTableCol>> headerRows;
    private final List<List<HTMLReportTableCol>> rows;
    private final boolean showInstituteName;
    private final boolean showInstituteLetterHead;
    private final Institute institute;
    private final Integer instituteHeaderColSpan;

    public HTMLReportTable(String reportName, boolean showDateAndTime, List<List<HTMLReportTableCol>> headerRows, List<List<HTMLReportTableCol>> rows, boolean showInstituteName, boolean showInstituteLetterHead, Institute institute, Integer instituteHeaderColSpan) {
        this.reportName = reportName;
        this.showDateAndTime = showDateAndTime;
        this.headerRows = headerRows;
        this.rows = rows;
        this.showInstituteName = showInstituteName;
        this.showInstituteLetterHead = showInstituteLetterHead;
        this.institute = institute;
        this.instituteHeaderColSpan = instituteHeaderColSpan;
    }

    public String getReportName() {
        return reportName;
    }

    public boolean isShowDateAndTime() {
        return showDateAndTime;
    }

    public List<List<HTMLReportTableCol>> getHeaderRows() {
        return headerRows;
    }

    public List<List<HTMLReportTableCol>> getRows() {
        return rows;
    }

    public boolean isShowInstituteName() {
        return showInstituteName;
    }

    public boolean isShowInstituteLetterHead() {
        return showInstituteLetterHead;
    }

    public Institute getInstitute() {
        return institute;
    }

    public Integer getInstituteHeaderColSpan() {
        return instituteHeaderColSpan;
    }

    @Override
    public String toString() {
        return "HTMLReportTable{" +
                "reportName='" + reportName + '\'' +
                ", showDateAndTime=" + showDateAndTime +
                ", rows=" + rows +
                ", showInstituteName=" + showInstituteName +
                ", showInstituteLetterHead=" + showInstituteLetterHead +
                ", institute=" + institute +
                ", instituteHeaderColSpan=" + instituteHeaderColSpan +
                '}';
    }
}
