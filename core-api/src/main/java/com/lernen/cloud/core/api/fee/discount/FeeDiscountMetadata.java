package com.lernen.cloud.core.api.fee.discount;

import java.util.Map;
import java.util.UUID;

/**
 *
 * <AUTHOR>
 *
 */
public class FeeDiscountMetadata {

	private final int instituteId;

	private final int academicSessionId;

	private final UUID discountStructureId;

	private final String name;

	private final DiscountStructureType discountStructureType;

	private final String description;

	private final Map<String, String> metadata;

	public FeeDiscountMetadata(int instituteId, int academicSessionId, UUID discountStructureId, String name, DiscountStructureType discountStructureType, String description, Map<String, String> metadata) {
		this.instituteId = instituteId;
		this.academicSessionId = academicSessionId;
		this.discountStructureId = discountStructureId;
		this.name = name;
		this.discountStructureType = discountStructureType;
		this.description = description;
		this.metadata = metadata;
	}

	public int getInstituteId() {
		return instituteId;
	}

	public int getAcademicSessionId() {
		return academicSessionId;
	}

	public UUID getDiscountStructureId() {
		return discountStructureId;
	}

	public String getName() {
		return name;
	}

	public String getDescription() {
		return description;
	}

	public DiscountStructureType getDiscountStructureType() {
		return discountStructureType;
	}

	public Map<String, String> getMetadata() {
		return metadata;
	}

	@Override
	public String toString() {
		return "FeeDiscountMetadata{" +
				"instituteId=" + instituteId +
				", academicSessionId=" + academicSessionId +
				", discountStructureId=" + discountStructureId +
				", name='" + name + '\'' +
				", discountStructureType=" + discountStructureType +
				", description='" + description + '\'' +
				", metadata=" + metadata +
				'}';
	}

}
