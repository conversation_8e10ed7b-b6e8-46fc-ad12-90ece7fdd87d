package com.lernen.cloud.core.api.notification;

import java.util.Map;
import java.util.UUID;

import com.lernen.cloud.core.api.common.DeliveryMode;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.user.UserType;

/**
 * 
 * <AUTHOR>
 *
 */
public class StaffNotificationDetails extends NotificationDetails {

	private final Staff staff;

	public StaffNotificationDetails(Staff staff, UUID notificationId, int instituteId, UUID userId,
			Integer academicSessionId, NotificationType notificationType, DeliveryMode deliveryMode,
			String deliveryDestination, UUID batchId, String batchName, String notificationTitle,
			String notificationContent, NotificationStatus notificationStatus, Integer deliveredTime,
			Integer generatedTime, Integer lastOpenedTime, Integer lastClickedTime, String externalUniqueId,
			Map<String, Object> metaData, Integer creditsUsed, UUID creditTransactionId, Integer refundCredits) {
		/**
		 * SMS provider is set as null since it is not supposed to be exposed to user
		 */
		super(notificationId, instituteId, userId, UserType.STAFF, academicSessionId, null, notificationType, deliveryMode,
				deliveryDestination, batchId, batchName, notificationTitle, notificationContent, notificationStatus,
				deliveredTime, generatedTime, lastOpenedTime, lastClickedTime, externalUniqueId, metaData, creditsUsed, creditTransactionId, refundCredits);
		this.staff = staff;
	}

	public Staff getStaff() {
		return staff;
	}

	

}
