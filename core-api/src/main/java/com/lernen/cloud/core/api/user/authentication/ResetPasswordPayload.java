/**
 * 
 */
package com.lernen.cloud.core.api.user.authentication;

import java.util.UUID;

/**
 * <AUTHOR>
 *
 */
public class ResetPasswordPayload {
	
	private UUID userId;

	private String userName;

	private String password;

	private String confirmPassword;
	
	/**
	 * 
	 */
	public ResetPasswordPayload() {
	}

	/**
	 * @param userId
	 * @param userName
	 * @param password
	 * @param confirmPassword
	 */
	public ResetPasswordPayload(UUID userId, String userName, String password, String confirmPassword) {
		this.userId = userId;
		this.userName = userName;
		this.password = password;
		this.confirmPassword = confirmPassword;
	}

	/**
	 * @return the userId
	 */
	public UUID getUserId() {
		return userId;
	}

	/**
	 * @param userId the userId to set
	 */
	public void setUserId(UUID userId) {
		this.userId = userId;
	}

	/**
	 * @return the userName
	 */
	public String getUserName() {
		return userName;
	}

	/**
	 * @param userName the userName to set
	 */
	public void setUserName(String userName) {
		this.userName = userName;
	}

	/**
	 * @return the password
	 */
	public String getPassword() {
		return password;
	}

	/**
	 * @param password the password to set
	 */
	public void setPassword(String password) {
		this.password = password;
	}

	/**
	 * @return the confirmPassword
	 */
	public String getConfirmPassword() {
		return confirmPassword;
	}

	/**
	 * @param confirmPassword the confirmPassword to set
	 */
	public void setConfirmPassword(String confirmPassword) {
		this.confirmPassword = confirmPassword;
	}

	@Override
	public String toString() {
		return "ResetPasswordPayload [userId=" + userId + ", userName=" + userName + ", password=" + password
				+ ", confirmPassword=" + confirmPassword + "]";
	}

}
