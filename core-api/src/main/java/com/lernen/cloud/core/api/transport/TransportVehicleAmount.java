/**
 * 
 */
package com.lernen.cloud.core.api.transport;

/**
 * <AUTHOR>
 *
 */
public class TransportVehicleAmount {

	private final VehicleType vehicleType;

	private final Double amount;

	public TransportVehicleAmount(VehicleType vehicleType, Double amount) {
		this.vehicleType = vehicleType;
		this.amount = amount;
	}

	public VehicleType getVehicleType() {
		return vehicleType;
	}

	public Double getAmount() {
		return amount;
	}

	@Override
	public String toString() {
		return "TransportVehicleAmount{" +
				"vehicleType=" + vehicleType +
				", amount=" + amount +
				'}';
	}
}
