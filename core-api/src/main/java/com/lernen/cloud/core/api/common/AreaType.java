package com.lernen.cloud.core.api.common;

import org.apache.commons.lang3.StringUtils;

/**
 * 
 * <AUTHOR>
 *
 */
public enum AreaType {
	RURAL, URBAN, OTHER;

	public static AreaType getAreaType(String areaType) {
			if (StringUtils.isBlank(areaType)) {
				return null;
			}
			for (AreaType areaTypeEnum : AreaType.values()) {
				if (areaTypeEnum.name().equalsIgnoreCase(areaType)) {
					return areaTypeEnum;
				}
			}
			return null;
		}
	
	
}
