package com.lernen.cloud.core.api.fees;

import java.util.UUID;

import com.lernen.cloud.core.api.institute.AcademicSession;

/**
 * 
 * <AUTHOR>
 *
 */
public class StudentDiscountConfigurationRow {

	private final int instituteId;

	private final UUID studentId;

	private final DiscountConfigurationDetailedRow discountConfigurationDetailedRow;

	private final AcademicSession academicSession;

	public StudentDiscountConfigurationRow(int instituteId, UUID studentId,
			DiscountConfigurationDetailedRow discountConfigurationDetailedRow, AcademicSession academicSession) {
		this.instituteId = instituteId;
		this.studentId = studentId;
		this.discountConfigurationDetailedRow = discountConfigurationDetailedRow;
		this.academicSession = academicSession;
	}

	public int getInstituteId() {
		return instituteId;
	}

	public UUID getStudentId() {
		return studentId;
	}

	public DiscountConfigurationDetailedRow getDiscountConfigurationDetailedRow() {
		return discountConfigurationDetailedRow;
	}

	public AcademicSession getAcademicSession() {
		return academicSession;
	}

	@Override
	public String toString() {
		return "StudentDiscountConfiguration [instituteId=" + instituteId + ", studentId=" + studentId
				+ ", discountConfigurationDetailedRow=" + discountConfigurationDetailedRow + ", academicSession="
				+ academicSession + "]";
	}

}
