package com.lernen.cloud.core.api.transport;

import com.lernen.cloud.core.api.fees.FeeConfigurationBasicInfo;

public class StudentTransportFeeAmount {

	private FeeConfigurationBasicInfo feeConfigurationBasicInfo;
	
	private double amount;
	
	public StudentTransportFeeAmount(FeeConfigurationBasicInfo feeConfigurationBasicInfo, double amount) {
		this.feeConfigurationBasicInfo = feeConfigurationBasicInfo;
		this.amount = amount;
	}

	public FeeConfigurationBasicInfo getFeeConfigurationBasicInfo() {
		return feeConfigurationBasicInfo;
	}

	public double getAmount() {
		return amount;
	}
}
