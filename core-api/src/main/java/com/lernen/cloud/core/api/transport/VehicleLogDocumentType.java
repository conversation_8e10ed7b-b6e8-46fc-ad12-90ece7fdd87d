package com.lernen.cloud.core.api.transport;

import com.lernen.cloud.core.api.common.DocumentType;
import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.Set;

public enum VehicleLogDocumentType implements DocumentType {
    FUEL_EXPENSE_TRANSACTIONS("Fuel Expense Transaction", "Fuel Expense Transaction"),
    SERVICES_EXPENSE_TRANSACTIONS("Service Expense Transaction", "Service Expense Transaction"),
    MAINTENANCE_EXPENSE_TRANSACTIONS("Maintenance Expense Transaction", "Maintenance Expense Transaction"),
    REPAIR_EXPENSE_TRANSACTIONS("Repair Expense Transaction", "Repair Expense Transaction"),
    OTHER_EXPENSE_TRANSACTIONS("Other Expense Transaction", "Other Expense Transaction");


    private String documentName;
    private String displayName;

    VehicleLogDocumentType(String documentName, String displayName) {
        this.documentName = documentName;
        this.displayName = displayName;
    }

    public static VehicleLogDocumentType getVehicleLogDocumentType(String documentType) {
        if (StringUtils.isBlank(documentType)) {
            return null;
        }
        for (VehicleLogDocumentType documentTypeEnum : VehicleLogDocumentType.values()) {
            if (documentTypeEnum.name().equalsIgnoreCase(documentType)) {
                return documentTypeEnum;
            }
        }
        return null;
    }

    public static Set<VehicleLogDocumentType> getDocumentTypes(String documentsTypeStr){
        Set<VehicleLogDocumentType> documentTypes = new HashSet<VehicleLogDocumentType>();
        if(!StringUtils.isEmpty(documentsTypeStr)) {
            String[] types = documentsTypeStr.split(",");
            for(String stats : types) {
                if (VehicleLogDocumentType.getVehicleLogDocumentType(stats) == null) {
                    continue;
                }
                documentTypes.add(VehicleLogDocumentType.getVehicleLogDocumentType(stats));
            }
            return documentTypes;
        }

        VehicleLogDocumentType[] types = VehicleLogDocumentType.values();
        for(VehicleLogDocumentType documentsType: types){
            documentTypes.add(documentsType);
        }
        return documentTypes;
    }

    @Override
    public String getDocumentName() {
        return documentName;
    }

    @Override
    public String getDisplayName() {
        return displayName;
    }

    @Override
    public boolean isThumbnail() { return false;}

}