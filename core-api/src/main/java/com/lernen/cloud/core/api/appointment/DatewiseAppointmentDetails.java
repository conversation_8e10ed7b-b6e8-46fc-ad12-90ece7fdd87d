package com.lernen.cloud.core.api.appointment;

import java.util.List;

public class DatewiseAppointmentDetails implements  Comparable<DatewiseAppointmentDetails>{
    private final int date;
    private final List<StudentAppointmentDetails> studentAppointmentDetailsList;

    public DatewiseAppointmentDetails(int date, List<StudentAppointmentDetails> studentAppointmentDetailsList) {
        this.date = date;
        this.studentAppointmentDetailsList = studentAppointmentDetailsList;
    }

    public int getDate() {
        return date;
    }

    public List<StudentAppointmentDetails> getAppointmentDetailsList() {
        return studentAppointmentDetailsList;
    }

    @Override
    public String toString() {
        return "DatewiseAppointmentDetails{" +
                "date=" + date +
                ", appointmentDetailsList=" + studentAppointmentDetailsList +
                '}';
    }

    @Override
    public int compareTo(DatewiseAppointmentDetails o) {
        return Integer.compare(o.date, this.date);
    }
}
