package com.lernen.cloud.core.api.fees.payment;

/**
 * 
 * <AUTHOR>
 *
 */
public class FeeHeadTransactionAmounts {

	private int feeHeadId;

	private double paidAmount;

	private double instantDiscount;

	private double fineAmount;

	public FeeHeadTransactionAmounts() {
	}

	public FeeHeadTransactionAmounts(int feeHeadId, double paidAmount, double instantDiscount, double fineAmount) {
		this.feeHeadId = feeHeadId;
		this.paidAmount = paidAmount;
		this.instantDiscount = instantDiscount;
		this.fineAmount = fineAmount;
	}

	public int getFeeHeadId() {
		return feeHeadId;
	}

	public void setFeeHeadId(int feeHeadId) {
		this.feeHeadId = feeHeadId;
	}

	public double getPaidAmount() {
		return paidAmount;
	}

	public void setPaidAmount(double paidAmount) {
		this.paidAmount = paidAmount;
	}

	public double getInstantDiscount() {
		return instantDiscount;
	}

	public void setInstantDiscount(double instantDiscount) {
		this.instantDiscount = instantDiscount;
	}

	public double getFineAmount() {
		return fineAmount;
	}

	public void setFineAmount(double fineAmount) {
		this.fineAmount = fineAmount;
	}

	@Override
	public String toString() {
		return "FeeHeadTransactionAmounts [feeHeadId=" + feeHeadId + ", paidAmount=" + paidAmount + ", instantDiscount="
				+ instantDiscount + ", fineAmount=" + fineAmount + "]";
	}

}