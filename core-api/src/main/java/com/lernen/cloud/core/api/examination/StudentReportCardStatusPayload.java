package com.lernen.cloud.core.api.examination;

import java.util.Set;
import java.util.UUID;

/**
 * <AUTHOR>
 * @created_at 28/08/24 : 19:47
 **/
public class StudentReportCardStatusPayload {
    private int instituteId;
    private int academicSessionId;
    private UUID reportCardId;
    private StudentExamDisplayDataStatus studentExamDisplayDataStatus;
    private Set<UUID> studentIdSet;

    public StudentReportCardStatusPayload() {
    }

    public StudentReportCardStatusPayload(int instituteId, int academicSessionId, UUID reportCardId, StudentExamDisplayDataStatus studentExamDisplayDataStatus, Set<UUID> studentIdSet) {
        this.instituteId = instituteId;
        this.academicSessionId = academicSessionId;
        this.reportCardId = reportCardId;
        this.studentExamDisplayDataStatus = studentExamDisplayDataStatus;
        this.studentIdSet = studentIdSet;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public void setInstituteId(int instituteId) {
        this.instituteId = instituteId;
    }

    public int getAcademicSessionId() {
        return academicSessionId;
    }

    public void setAcademicSessionId(int academicSessionId) {
        this.academicSessionId = academicSessionId;
    }

    public UUID getReportCardId() {
        return reportCardId;
    }

    public void setReportCardId(UUID reportCardId) {
        this.reportCardId = reportCardId;
    }

    public StudentExamDisplayDataStatus getStudentExamDisplayDataStatus() {
        return studentExamDisplayDataStatus;
    }

    public void setStudentExamDisplayDataStatus(StudentExamDisplayDataStatus studentExamDisplayDataStatus) {
        this.studentExamDisplayDataStatus = studentExamDisplayDataStatus;
    }

    public Set<UUID> getStudentIdSet() {
        return studentIdSet;
    }

    public void setStudentIdSet(Set<UUID> studentIdSet) {
        this.studentIdSet = studentIdSet;
    }

    @Override
    public String toString() {
        return "StudentReportCardStatusPayload{" +
                "instituteId=" + instituteId +
                ", academicSessionId=" + academicSessionId +
                ", reportCardId=" + reportCardId +
                ", studentExamDisplayDataStatus=" + studentExamDisplayDataStatus +
                ", studentIdSet=" + studentIdSet +
                '}';
    }
}
