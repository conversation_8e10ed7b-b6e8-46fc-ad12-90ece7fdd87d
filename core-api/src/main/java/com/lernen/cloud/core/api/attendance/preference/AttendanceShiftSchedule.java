package com.lernen.cloud.core.api.attendance.preference;

import com.lernen.cloud.core.api.common.EHourMinute;
import com.lernen.cloud.core.api.common.EMonthDay;
import com.lernen.cloud.core.api.common.HourMinuteRange;

import java.time.LocalTime;
import java.time.MonthDay;

/**
 * <AUTHOR>
 */

public class AttendanceShiftSchedule {

    private final EMonthDay startDate;

    private final EMonthDay endDate;

    private final EHourMinute startCutOffTime;

    private final EHourMinute endCutOffTime;

    public AttendanceShiftSchedule(EMonthDay startDate, EMonthDay endDate, EHourMinute startCutOffTime, EHourMinute endCutOffTime) {
        this.startDate = startDate;
        this.endDate = endDate;
        this.startCutOffTime = startCutOffTime;
        this.endCutOffTime = endCutOffTime;
    }

    public EMonthDay getStartDate() {
        return startDate;
    }

    public EMonthDay getEndDate() {
        return endDate;
    }

    public EHourMinute getStartCutOffTime() {
        return startCutOffTime;
    }

    public EHourMinute getEndCutOffTime() {
        return endCutOffTime;
    }

    @Override
    public String toString() {
        return "AttendanceShiftSchedule{" +
                "startDate=" + startDate +
                ", endDate=" + endDate +
                ", startCutOffTime=" + startCutOffTime +
                ", endCutOffTime=" + endCutOffTime +
                '}';
    }
}
