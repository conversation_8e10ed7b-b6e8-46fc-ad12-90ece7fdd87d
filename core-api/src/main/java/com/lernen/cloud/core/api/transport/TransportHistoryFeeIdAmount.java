package com.lernen.cloud.core.api.transport;

import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class TransportHistoryFeeIdAmount {

	private UUID feeId;

	private Double amount;

	public UUID getFeeId() {
		return feeId;
	}

	public void setFeeId(UUID feeId) {
		this.feeId = feeId;
	}

	public Double getAmount() {
		return amount;
	}

	public void setAmount(Double amount) {
		this.amount = amount;
	}

	@Override
	public String toString() {
		return "TransportHistoryFeeIdAmount [feeId=" + feeId + ", amount=" + amount + "]";
	}

}
