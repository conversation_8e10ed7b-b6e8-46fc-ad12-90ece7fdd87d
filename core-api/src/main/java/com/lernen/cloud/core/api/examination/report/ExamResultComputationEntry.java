package com.lernen.cloud.core.api.examination.report;

import java.util.List;
import java.util.Set;
import java.util.UUID;

/**
 * <AUTHOR>
 */

public class ExamResultComputationEntry {

    private UUID examId;

    private boolean totalDimension;

    private Set<Integer> dimensions;

    public ExamResultComputationEntry() {
    }

    public ExamResultComputationEntry(UUID examId, boolean totalDimension, Set<Integer> dimensions) {
        this.examId = examId;
        this.totalDimension = totalDimension;
        this.dimensions = dimensions;
    }

    public UUID getExamId() {
        return examId;
    }

    public void setExamId(UUID examId) {
        this.examId = examId;
    }

    public boolean isTotalDimension() {
        return totalDimension;
    }

    public void setTotalDimension(boolean totalDimension) {
        this.totalDimension = totalDimension;
    }

    public Set<Integer> getDimensions() {
        return dimensions;
    }

    public void setDimensions(Set<Integer> dimensions) {
        this.dimensions = dimensions;
    }

    @Override
    public String toString() {
        return "ExamResultComputationEntry{" +
                "examId=" + examId +
                ", totalDimension=" + totalDimension +
                ", dimensions=" + dimensions +
                '}';
    }
}

