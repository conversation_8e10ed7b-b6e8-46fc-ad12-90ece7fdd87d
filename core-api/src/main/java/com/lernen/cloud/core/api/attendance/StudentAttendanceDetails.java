package com.lernen.cloud.core.api.attendance;

import java.util.List;

/**
 * <AUTHOR>
 * @created_at 01/06/23 : 19:35
 **/
public class StudentAttendanceDetails {

    private final int instituteId;

    private final String admissionNumber;

    private final String studentName;

    private final String rollNumber;


    private final String standardId;

    private final Integer sectionId;

    private final String standardName;

    private final int level;

    private String primaryContactNumber;

    private final List<StudentAttendanceRegisterPayload> studentAttendanceRegisterPayloadList;

    public StudentAttendanceDetails(int instituteId, String admissionNumber, String studentName, String rollNumber, String standardId, Integer sectionId, String standardName, int level, String primaryContactNumber, List<StudentAttendanceRegisterPayload> studentAttendanceRegisterPayloadList) {
        this.instituteId = instituteId;
        this.admissionNumber = admissionNumber;
        this.studentName = studentName;
        this.rollNumber = rollNumber;
        this.standardId = standardId;
        this.sectionId = sectionId;
        this.standardName = standardName;
        this.level = level;
        this.primaryContactNumber = primaryContactNumber;
        this.studentAttendanceRegisterPayloadList = studentAttendanceRegisterPayloadList;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public String getAdmissionNumber() {
        return admissionNumber;
    }

    public String getStudentName() {
        return studentName;
    }

    public String getRollNumber() {
        return rollNumber;
    }

    public String getStandardId() {
        return standardId;
    }

    public Integer getSectionId() {
        return sectionId;
    }

    public String getStandardName() {
        return standardName;
    }

    public int getLevel() {
        return level;
    }

    public String getPrimaryContactNumber() {
        return primaryContactNumber;
    }

    public void setPrimaryContactNumber(String primaryContactNumber) {
        this.primaryContactNumber = primaryContactNumber;
    }

    public List<StudentAttendanceRegisterPayload> getStudentAttendanceRegisterPayloadList() {
        return studentAttendanceRegisterPayloadList;
    }

    @Override
    public String toString() {
        return "StudentAttendanceDetails{" +
                "instituteId=" + instituteId +
                ", admissionNumber='" + admissionNumber + '\'' +
                ", studentName='" + studentName + '\'' +
                ", rollNumber='" + rollNumber + '\'' +
                ", standardId='" + standardId + '\'' +
                ", sectionId=" + sectionId +
                ", standardName='" + standardName + '\'' +
                ", level=" + level +
                ", studentAttendanceRegisterPayloadList=" + studentAttendanceRegisterPayloadList +
                '}';
    }
}
