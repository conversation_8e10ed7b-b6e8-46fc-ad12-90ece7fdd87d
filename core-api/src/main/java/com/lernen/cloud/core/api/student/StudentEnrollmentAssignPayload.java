package com.lernen.cloud.core.api.student;

import com.embrate.cloud.core.api.fee.discount.structure.FeeDiscountStructure;
import com.lernen.cloud.core.api.course.Course;
import com.lernen.cloud.core.api.fees.ResolvedDefaultEntityFeeAssignmentStructure;
import com.lernen.cloud.core.api.institute.InstituteMetaData;
import com.lernen.cloud.core.api.sms.SMSPreferences;
import com.lernen.cloud.core.api.transport.TransportDetails;

import java.util.List;

/**
 * <AUTHOR>
 */
public class StudentEnrollmentAssignPayload {

    private Student student;

    private List<ResolvedDefaultEntityFeeAssignmentStructure> feeAssignmentStructures;

    private List<Course> optionalCourses;

    private SMSPreferences smsPreferences;

    private List<FeeDiscountStructure> defaultEntityDiscountAssignmentStructures;

    private TransportDetails transportDetails;

    private InstituteMetaData instituteMetaData;

    public StudentEnrollmentAssignPayload() {
    }

    public StudentEnrollmentAssignPayload(Student student, List<ResolvedDefaultEntityFeeAssignmentStructure> feeAssignmentStructures, List<Course> optionalCourses, SMSPreferences smsPreferences, List<FeeDiscountStructure> defaultEntityDiscountAssignmentStructures, TransportDetails transportDetails,
                                          InstituteMetaData instituteMetaData) {
        this.student = student;
        this.feeAssignmentStructures = feeAssignmentStructures;
        this.optionalCourses = Course.sortCoursesBySequence(optionalCourses);
        this.smsPreferences = smsPreferences;
        this.defaultEntityDiscountAssignmentStructures = defaultEntityDiscountAssignmentStructures;
        this.transportDetails = transportDetails;
        this.instituteMetaData = instituteMetaData;
    }

    public Student getStudent() {
        return student;
    }

    public void setStudent(Student student) {
        this.student = student;
    }

    public List<ResolvedDefaultEntityFeeAssignmentStructure> getFeeAssignmentStructures() {
        return feeAssignmentStructures;
    }

    public void setFeeAssignmentStructures(List<ResolvedDefaultEntityFeeAssignmentStructure> feeAssignmentStructures) {
        this.feeAssignmentStructures = feeAssignmentStructures;
    }

    public List<Course> getOptionalCourses() {
        return optionalCourses;
    }

    public void setOptionalCourses(List<Course> optionalCourses) {
        this.optionalCourses = optionalCourses;
    }

    public SMSPreferences getSmsPreferences() {
        return smsPreferences;
    }

    public void setSmsPreferences(SMSPreferences smsPreferences) {
        this.smsPreferences = smsPreferences;
    }

    public List<FeeDiscountStructure> getDefaultEntityDiscountAssignmentStructures() {
        return defaultEntityDiscountAssignmentStructures;
    }

    public void setDefaultEntityDiscountAssignmentStructures(List<FeeDiscountStructure> defaultEntityDiscountAssignmentStructures) {
        this.defaultEntityDiscountAssignmentStructures = defaultEntityDiscountAssignmentStructures;
    }

    public TransportDetails getTransportDetails() {
        return transportDetails;
    }

    public void setTransportDetails(TransportDetails transportDetails) {
        this.transportDetails = transportDetails;
    }

    public InstituteMetaData getInstituteMetaData() {
        return instituteMetaData;
    }

    public void setInstituteMetaData(InstituteMetaData instituteMetaData) {
        this.instituteMetaData = instituteMetaData;
    }

    @Override
    public String toString() {
        return "StudentEnrollmentAssignPayload{" +
                "student=" + student +
                ", feeAssignmentStructures=" + feeAssignmentStructures +
                ", optionalCourses=" + optionalCourses +
                ", smsPreferences=" + smsPreferences +
                ", defaultEntityDiscountAssignmentStructures=" + defaultEntityDiscountAssignmentStructures +
                ", transportDetails=" + transportDetails +
                ", instituteMetaData=" + instituteMetaData +
                '}';
    }
}
