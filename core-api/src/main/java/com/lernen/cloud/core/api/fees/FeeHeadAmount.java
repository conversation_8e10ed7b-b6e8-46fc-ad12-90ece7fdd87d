package com.lernen.cloud.core.api.fees;

/**
 * 
 * <AUTHOR>
 *
 */
public class FeeHeadAmount {

	private int feeHeadId;

	private Double amount;
	
	private Boolean isPercentage;

	private FeeEntity feeEntity;

	public FeeHeadAmount() {
	}

	public FeeHeadAmount(int feeHeadId, Double amount) {
		this.feeHeadId = feeHeadId;
		this.amount = amount;
	}
	
	public FeeHeadAmount(int feeHeadId, Double amount, FeeEntity feeEntity) {
		this.feeHeadId = feeHeadId;
		this.amount = amount;
		this.feeEntity = feeEntity;
	}

	/**
	 * @param feeHeadId
	 * @param amount
	 * @param isPercentage
	 * @param feeEntity
	 */
	public FeeHeadAmount(int feeHeadId, Double amount, Boolean isPercentage, FeeEntity feeEntity) {
		this.feeHeadId = feeHeadId;
		this.amount = amount;
		this.isPercentage = isPercentage;
		this.feeEntity = feeEntity;
	}

	public int getFeeHeadId() {
		return feeHeadId;
	}

	public void setFeeHeadId(int feeHeadId) {
		this.feeHeadId = feeHeadId;
	}

	public Double getAmount() {
		return amount;
	}

	public void setAmount(Double amount) {
		this.amount = amount;
	}

	public FeeEntity getFeeEntity() {
		return feeEntity;
	}

	public void setFeeEntity(FeeEntity feeEntity) {
		this.feeEntity = feeEntity;
	}
	
	public Boolean getIsPercentage() {
		return isPercentage;
	}

	public void setIsPercentage(Boolean isPercentage) {
		this.isPercentage = isPercentage;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + feeHeadId;
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		FeeHeadAmount other = (FeeHeadAmount) obj;
		if (feeHeadId != other.feeHeadId)
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "FeeHeadAmount [feeHeadId=" + feeHeadId + ", amount=" + amount + ", feeEntity=" + feeEntity + "]";
	}

}
