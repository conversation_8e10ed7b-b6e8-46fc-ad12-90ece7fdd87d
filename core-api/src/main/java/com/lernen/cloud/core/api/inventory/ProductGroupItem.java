package com.lernen.cloud.core.api.inventory;

import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class ProductGroupItem {

	private UUID skuId;

	private double quantity;

	private Double discount;

	public UUID getSkuId() {
		return skuId;
	}

	public void setSkuId(UUID skuId) {
		this.skuId = skuId;
	}

	public double getQuantity() {
		return quantity;
	}

	public void setQuantity(double quantity) {
		this.quantity = quantity;
	}

	public Double getDiscount() {
		return discount;
	}

	public void setDiscount(Double discount) {
		this.discount = discount;
	}

	@Override
	public String toString() {
		return "ProductGroupItem [skuId=" + skuId + ", quantity=" + quantity + ", discount=" + discount + "]";
	}

}
