package com.lernen.cloud.core.api.examination;

import java.util.*;
import java.util.Map.Entry;

import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.institute.Standard;
import com.embrate.cloud.core.api.institute.StandardMetadata;
import org.springframework.util.CollectionUtils;

/**
 * 
 * <AUTHOR>
 *
 */
public class ExamDetails {

	private final AcademicSession academicSession;

	private final Standard standard;

	private final ExamMetaData examMetaData;

	private final ExamMetaData parentExamMetaData;

	private final Map<CourseType, List<ExamDimensionValues>> examDimensionValues;

	private final List<ExamCourse> examCourses;

	private final List<ExamCourse> examCoursesAllDimensions;

	private final Map<CourseType, List<ExamCourse>> courseMarksMatrix;

	private final List<ExamMetaData> childExams;

	private final StandardMetadata standardsMetaData;

	public ExamDetails(AcademicSession academicSession, Standard standard,
			ExamMetaData examMetaData, ExamMetaData parentExamMetaData,
			Map<CourseType, List<ExamDimensionValues>> examDimensionValues,
			List<ExamCourse> examCourses, List<ExamMetaData> childExams,
			StandardMetadata standardsMetaData) {
		this.academicSession = academicSession;
		this.standard = standard;
		this.examMetaData = examMetaData;
		this.parentExamMetaData = parentExamMetaData;
		this.examDimensionValues = examDimensionValues;
		this.examCourses = sortExamCoursesBySequence(examCourses);
		this.examCoursesAllDimensions = computeCourseMarksAllDimensions();
		this.courseMarksMatrix = computeCourseMarksMatrix();
		this.childExams = sortExamByName(childExams);
		this.standardsMetaData = standardsMetaData;
	}

	private List<ExamCourse> computeCourseMarksAllDimensions() {
		List<ExamCourse> examCoursesList = new ArrayList<>();
		for (Entry<CourseType, List<ExamDimensionValues>> entry : examDimensionValues
				.entrySet()) {
			CourseType courseType = entry.getKey();

			for (ExamCourse examCourse : examCourses) {
				if (courseType != examCourse.getCourse().getCourseType()) {
					continue;
				}
				List<ExamDimensionValues> allExamDimensionValues = new ArrayList<>();
				Map<Integer, ExamDimensionValues> examDimensionValuesMap = new HashMap<>();
				List<ExamDimensionValues> examDimensionValuesList = examCourse
						.getExamDimensionValues();

				double totalMaxMarks = 0d;
				double totalMinMarks = 0d;
				double totalOriginalMaxMarks = 0d;
				ExamDimensionValues totalExamDimensionValues = null;
				for (final ExamDimensionValues examDimensionValues : examDimensionValuesList) {
					examDimensionValuesMap.put(examDimensionValues
							.getExamDimension().getDimensionId(),
							examDimensionValues);
					if (examDimensionValues.getExamDimension()
							.getExamEvaluationType() == ExamEvaluationType.NUMBER
							&& examDimensionValues.getExamDimension()
									.isTotal()) {
						totalExamDimensionValues = examDimensionValues;
						continue;
					}
					totalOriginalMaxMarks += examDimensionValues.getOriginalMaxMarks() == null
							? 0d
							: examDimensionValues.getOriginalMaxMarks();

					totalMaxMarks += examDimensionValues.getMaxMarks() == null
							? 0d
							: examDimensionValues.getMaxMarks();

					totalMinMarks +=  examDimensionValues.getMinMarks() == null
							? 0d
							: examDimensionValues.getMinMarks();
				}
				/**
				 * Summing up the total max marks in total dimension
				 */
				if (totalExamDimensionValues != null) {
					totalExamDimensionValues.setMaxMarks(totalOriginalMaxMarks, totalMaxMarks);
					totalExamDimensionValues.setMinMarks(totalMinMarks);
				}

				for (ExamDimensionValues examDimensionValues : entry
						.getValue()) {
					Integer dimensoinId = examDimensionValues.getExamDimension()
							.getDimensionId();
					if (examDimensionValuesMap.containsKey(dimensoinId)) {
						allExamDimensionValues
								.add(examDimensionValuesMap.get(dimensoinId));
						continue;
					}
					allExamDimensionValues.add(examDimensionValues);
				}
				examCoursesList.add(new ExamCourse(examCourse.getCourse(),
						allExamDimensionValues));
			}
		}
		return sortExamCoursesBySequence(examCoursesList);
	}

	private Map<CourseType, List<ExamCourse>> computeCourseMarksMatrix() {
		Map<CourseType, List<ExamCourse>> courseMarksMatrix = new HashMap<>();
		for (ExamCourse examCourse : examCoursesAllDimensions) {
			CourseType courseType = examCourse.getCourse().getCourseType();
			if (!courseMarksMatrix.containsKey(courseType)) {
				courseMarksMatrix.put(courseType, new ArrayList<>());
			}
			courseMarksMatrix.get(courseType).add(examCourse);
		}
		for(Entry<CourseType, List<ExamCourse>> entry : courseMarksMatrix.entrySet()) {
			entry.setValue(sortExamCoursesBySequence(entry.getValue()));
		}
		return courseMarksMatrix;
	}

	public AcademicSession getAcademicSession() {
		return academicSession;
	}

	public Standard getStandard() {
		return standard;
	}

	public ExamMetaData getExamMetaData() {
		return examMetaData;
	}

	public ExamMetaData getParentExamMetaData() {
		return parentExamMetaData;
	}

	public Map<CourseType, List<ExamDimensionValues>> getExamDimensionValues() {
		return examDimensionValues;
	}

	public List<ExamCourse> getExamCourses() {
		return examCourses;
	}

	public List<ExamCourse> getExamCoursesAllDimensions() {
		return examCoursesAllDimensions;
	}

	public Map<CourseType, List<ExamCourse>> getCourseMarksMatrix() {
		return courseMarksMatrix;
	}

	public List<ExamMetaData> getChildExams() {
		return childExams;
	}

	public StandardMetadata getStandardsMetaData() {
		return standardsMetaData;
	}

	public static List<ExamCourse> sortExamCoursesBySequence(List<ExamCourse> examCourseList) {
		if(CollectionUtils.isEmpty(examCourseList)) {
			return new ArrayList<>();
		}
		Collections.sort(examCourseList, new Comparator<ExamCourse>() {
			@Override
			public int compare(ExamCourse e1, ExamCourse e2) {
				return e1.getCourse().compareTo(e2.getCourse());
			}
		});
		return examCourseList;
	}

	public static List<ExamMetaData> sortExamByName(List<ExamMetaData> examMetaDataList) {
		if(CollectionUtils.isEmpty(examMetaDataList)) {
			return null;
		}
		Collections.sort(examMetaDataList, new Comparator<ExamMetaData>() {
			@Override
			public int compare(ExamMetaData e1, ExamMetaData e2) {
				return e1.getExamName().compareToIgnoreCase(e2.getExamName());
			}
		});
		return examMetaDataList;
	}

}
