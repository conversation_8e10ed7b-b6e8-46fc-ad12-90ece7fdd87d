package com.lernen.cloud.core.api.examination;

import com.lernen.cloud.core.api.institute.AcademicSession;
import com.embrate.cloud.core.api.institute.StandardMetadata;
import com.lernen.cloud.core.api.institute.StandardRowDetails;

/**
 * 
 * <AUTHOR>
 *
 */
public class ExamDetailsRow {

	private final AcademicSession academicSession;
	private final StandardRowDetails standardRowDetails;
	private final ExamMetaData examMetaData;
	private final ExamDimensionsRowDetails examDimensionsRowDetails;
	private final ExamCourse examCourse;
	private final StandardMetadata standardsMetaData;

	public ExamDetailsRow(AcademicSession academicSession,
			StandardRowDetails standardRowDetails, ExamMetaData examMetaData,
			ExamDimensionsRowDetails examDimensionsRowDetails,
			ExamCourse examCourse, StandardMetadata standardsMetaData) {
		this.academicSession = academicSession;
		this.standardRowDetails = standardRowDetails;
		this.examMetaData = examMetaData;
		this.examDimensionsRowDetails = examDimensionsRowDetails;
		this.examCourse = examCourse;
		this.standardsMetaData = standardsMetaData;
	}

	public AcademicSession getAcademicSession() {
		return academicSession;
	}

	public StandardRowDetails getStandardRowDetails() {
		return standardRowDetails;
	}

	public ExamMetaData getExamMetaData() {
		return examMetaData;
	}

	public ExamDimensionsRowDetails getExamDimensionsRowDetails() {
		return examDimensionsRowDetails;
	}

	public ExamCourse getExamCourse() {
		return examCourse;
	}

	public StandardMetadata getStandardsMetaData() {
		return standardsMetaData;
	}

}
