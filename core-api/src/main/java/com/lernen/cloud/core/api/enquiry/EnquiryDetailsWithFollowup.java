package com.lernen.cloud.core.api.enquiry;

import com.lernen.cloud.core.api.followup.FollowUpPayload;
import com.lernen.cloud.core.api.student.StudentDetailedRow;
import com.lernen.cloud.core.api.user.UserLite;

import java.util.List;
import java.util.UUID;

public class EnquiryDetailsWithFollowup {
    private final int instituteId;

    private final int academicSessionId;

    private final UUID enquiryId;

    private final String trackingId;

    private final String guardianName;

    private final String guardianContactInfo;

    private final String guardianEmailId;

    private final String childName;

    private final String standardName;

    private final int enquiryDate;

    private final Integer updateDate;

    private final String message;

    private final EnquiryStatus enquiryStatus;

    private final boolean isSuccessful;

    private final StudentDetailedRow studentDetailedRow;

    private final List<FollowUpPayload> followUpPayloadList;

    private final Integer acceptedDate;

    private final UserLite acceptedUserLite;

    private final Integer rejectedDate;

    private final UserLite rejectedUserLite;

    private final String reason;

    private final Integer closedDate;

    private final UserLite closedUserLite;

    private final String outcome;

    public EnquiryDetailsWithFollowup(int instituteId, int academicSessionId, UUID enquiryId, String trackingId, String guardianName, String guardianContactInfo, String guardianEmailId, String childName, String standardName, int enquiryDate, Integer updateDate, String message, EnquiryStatus enquiryStatus, boolean isSuccessful, StudentDetailedRow studentDetailedRow, List<FollowUpPayload> followUpPayloadList, Integer acceptedDate, UserLite acceptedUserLite, Integer rejectedDate, UserLite rejectedUserLite, String reason, Integer closedDate, UserLite closedUserLite, String outcome) {
        this.instituteId = instituteId;
        this.academicSessionId = academicSessionId;
        this.enquiryId = enquiryId;
        this.trackingId = trackingId;
        this.guardianName = guardianName;
        this.guardianContactInfo = guardianContactInfo;
        this.guardianEmailId = guardianEmailId;
        this.childName = childName;
        this.standardName = standardName;
        this.enquiryDate = enquiryDate;
        this.updateDate = updateDate;
        this.message = message;
        this.enquiryStatus = enquiryStatus;
        this.isSuccessful = isSuccessful;
        this.studentDetailedRow = studentDetailedRow;
        this.followUpPayloadList = followUpPayloadList;
        this.acceptedDate = acceptedDate;
        this.acceptedUserLite = acceptedUserLite;
        this.rejectedDate = rejectedDate;
        this.rejectedUserLite = rejectedUserLite;
        this.reason = reason;
        this.closedDate = closedDate;
        this.closedUserLite = closedUserLite;
        this.outcome = outcome;
    }

    public String getTrackingId() {
        return trackingId;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public int getAcademicSessionId() {
        return academicSessionId;
    }

    public UUID getEnquiryId() {
        return enquiryId;
    }

    public String getGuardianName() {
        return guardianName;
    }

    public String getGuardianContactInfo() {
        return guardianContactInfo;
    }

    public String getGuardianEmailId() {
        return guardianEmailId;
    }

    public String getChildName() {
        return childName;
    }

    public String getStandardName() {
        return standardName;
    }

    public int getEnquiryDate() {
        return enquiryDate;
    }

    public Integer getUpdateDate() {
        return updateDate;
    }

    public String getMessage() {
        return message;
    }

    public EnquiryStatus getEnquiryStatus() {
        return enquiryStatus;
    }

    public StudentDetailedRow getStudentDetailedRow() {
        return studentDetailedRow;
    }

    public List<FollowUpPayload> getFollowUpPayloadList() {
        return followUpPayloadList;
    }

    public Integer getAcceptedDate() {
        return acceptedDate;
    }

    public UserLite getAcceptedUserLite() {
        return acceptedUserLite;
    }

    public Integer getRejectedDate() {
        return rejectedDate;
    }

    public UserLite getRejectedUserLite() {
        return rejectedUserLite;
    }

    public String getReason() {
        return reason;
    }

    public Integer getClosedDate() {
        return closedDate;
    }

    public UserLite getClosedUserLite() {
        return closedUserLite;
    }

    public String getOutcome() {
        return outcome;
    }

    public boolean isSuccessful() {
        return isSuccessful;
    }

    @Override
    public String toString() {
        return "EnquiryDetailsWithFollowup{" +
                "instituteId=" + instituteId +
                ", academicSessionId=" + academicSessionId +
                ", enquiryId=" + enquiryId +
                ", trackingId='" + trackingId + '\'' +
                ", guardianName='" + guardianName + '\'' +
                ", guardianContactInfo='" + guardianContactInfo + '\'' +
                ", guardianEmailId='" + guardianEmailId + '\'' +
                ", childName='" + childName + '\'' +
                ", standardName='" + standardName + '\'' +
                ", enquiryDate=" + enquiryDate +
                ", updateDate=" + updateDate +
                ", message='" + message + '\'' +
                ", enquiryStatus=" + enquiryStatus +
                ", isSuccessful=" + isSuccessful +
                ", studentDetailedRow=" + studentDetailedRow +
                ", followUpPayloadList=" + followUpPayloadList +
                ", acceptedDate=" + acceptedDate +
                ", acceptedUserLite=" + acceptedUserLite +
                ", rejectedDate=" + rejectedDate +
                ", rejectedUserLite=" + rejectedUserLite +
                ", reason='" + reason + '\'' +
                ", closedDate=" + closedDate +
                ", closedUserLite=" + closedUserLite +
                ", outcome='" + outcome + '\'' +
                '}';
    }
}
