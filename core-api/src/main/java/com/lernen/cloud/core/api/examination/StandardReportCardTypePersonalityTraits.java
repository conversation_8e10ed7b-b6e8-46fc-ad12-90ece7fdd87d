package com.lernen.cloud.core.api.examination;

import com.lernen.cloud.core.api.configurations.ExaminationPreferences;

import java.util.List;

public class StandardReportCardTypePersonalityTraits {

    private final List<ExamReportCardMetadata> examReportCardMetadataList;

    private final List<PersonalityTraitsDetails> personalityTraitsDetailsList;

    private final ExaminationPreferences examinationPreferences;

    public StandardReportCardTypePersonalityTraits(List<ExamReportCardMetadata> examReportCardMetadataList, List<PersonalityTraitsDetails> personalityTraitsDetailsList, ExaminationPreferences examinationPreferences) {
        this.examReportCardMetadataList = examReportCardMetadataList;
        this.personalityTraitsDetailsList = personalityTraitsDetailsList;
        this.examinationPreferences = examinationPreferences;
    }

    public List<ExamReportCardMetadata> getExamReportCardMetadataList() {
        return examReportCardMetadataList;
    }

    public List<PersonalityTraitsDetails> getPersonalityTraitsDetailsList() {
        return personalityTraitsDetailsList;
    }

    public ExaminationPreferences getExaminationPreferences() {
        return examinationPreferences;
    }

    @Override
    public String toString() {
        return "StandardReportCardTypePersonalityTraits{" +
                "examReportCardMetadataList=" + examReportCardMetadataList +
                ", personalityTraitsDetailsList=" + personalityTraitsDetailsList +
                ", examinationPreferences=" + examinationPreferences +
                '}';
    }
}
