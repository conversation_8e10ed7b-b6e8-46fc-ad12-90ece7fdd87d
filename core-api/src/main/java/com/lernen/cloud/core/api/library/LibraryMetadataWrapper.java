package com.lernen.cloud.core.api.library;

import java.util.List;

public class LibraryMetadataWrapper {

    private final List<Publication> publications;
    private final List<Publisher> publishers;
    private final List<Genre> genres;
    private final List<Vendor> vendors;
    private final List<Author> authors;
    private final List<LibraryType> libraryTypes;
    
    public LibraryMetadataWrapper(List<Publication> publications, List<Publisher> publishers, List<Genre> genres,
            List<Vendor> vendors, List<Author> authors, List<LibraryType> libraryTypes) {
        this.publications = publications;
        this.publishers = publishers;
        this.genres = genres;
        this.vendors = vendors;
        this.authors = authors;
        this.libraryTypes = libraryTypes;
    }

    public List<Publication> getPublications() {
        return publications;
    }

    public List<Publisher> getPublishers() {
        return publishers;
    }

    public List<Genre> getGenres() {
        return genres;
    }

    public List<Vendor> getVendors() {
        return vendors;
    }

    public List<Author> getAuthors() {
        return authors;
    }

    public List<LibraryType> getLibraryTypes() {
        return libraryTypes;
    }

    @Override
    public String toString() {
        return "LibraryMetadataWrapper [publications=" + publications + ", publishers=" + publishers + ", genres=" + genres
                + ", vendors=" + vendors + ", authors=" + authors + ", libraryTypes=" + libraryTypes + "]";
    }

    
}
