package com.lernen.cloud.core.api.examination;

import com.embrate.cloud.core.api.examination.utility.ExamGridRowAttribute;
import com.lernen.cloud.core.api.pdf.CellData;

import java.util.List;

/**
 * <AUTHOR>
 */

public class ExamGridAdditionalAttributeRow {

    private final ExamGridRowAttribute attribute;
    private final List<CellData> columns;

    public ExamGridAdditionalAttributeRow(ExamGridRowAttribute attribute, List<CellData> columns) {
        this.attribute = attribute;
        this.columns = columns;
    }

    public ExamGridRowAttribute getAttribute() {
        return attribute;
    }

    public List<CellData> getColumns() {
        return columns;
    }

    @Override
    public String toString() {
        return "ExamGridAdditionalAttributeRow{" +
                "attribute=" + attribute +
                ", columns=" + columns +
                '}';
    }
}
