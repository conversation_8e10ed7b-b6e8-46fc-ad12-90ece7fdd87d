/**
 * 
 */
package com.embrate.cloud.push.notifications.handler;

import com.embrate.cloud.core.api.homework.HomeworkDetails;
import com.embrate.cloud.core.api.homework.StudentHomeworkSubmissionPayload;
import com.embrate.cloud.core.api.service.notification.BellNotificationPayload;
import com.embrate.cloud.core.api.service.notification.NotificationEntity;
import com.embrate.cloud.core.api.service.notification.PushNotificationContent;
import com.embrate.cloud.core.lib.homework.HomeworkManager;
import com.embrate.cloud.core.lib.push.notification.PushNotificationManager;
import com.embrate.cloud.push.notifications.content.builder.HomeworkMarkingUpdatePushNotificationContentBuilder;
import com.google.gson.Gson;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserStatus;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.managers.UserManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.utils.SharedConstants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

/**
 * <AUTHOR>
 *
 */
public class HomeworkMarkingUpdatePushNotificationHandler extends AbstractPushNotificationHandler {

	private static final Logger logger = LogManager.getLogger(HomeworkMarkingUpdatePushNotificationHandler.class);

	private static final String STUDENT_HOMEWORK_MARKING_UPDATE = "studentHomeworkMarkingUpdate";
	private static final String HOMEWORK_ID = "homeworkId";
	private static final String HOMEWORK_DETAILS = "homeworkDetails";

	private static final Gson GSON = SharedConstants.GSON;
	private final PushNotificationManager pushNotificationManager;
	private final HomeworkMarkingUpdatePushNotificationContentBuilder homeworkMarkingUpdatePushNotificationContentBuilder;
	private final HomeworkManager homeworkManager;
	private final UserManager userManager;
	private final UserPreferenceSettings userPreferenceSettings;
	private final UserPermissionManager userPermissionManager;

	public HomeworkMarkingUpdatePushNotificationHandler(PushNotificationManager pushNotificationManager,
														HomeworkMarkingUpdatePushNotificationContentBuilder homeworkMarkingUpdatePushNotificationContentBuilder,
                                                        HomeworkManager homeworkManager,
														UserManager userManager,
														UserPreferenceSettings userPreferenceSettings,
                                                        UserPermissionManager userPermissionManager) {
		super(pushNotificationManager);
		this.pushNotificationManager = pushNotificationManager;
		this.homeworkMarkingUpdatePushNotificationContentBuilder = homeworkMarkingUpdatePushNotificationContentBuilder;
		this.homeworkManager = homeworkManager;
		this.userManager = userManager;
		this.userPreferenceSettings = userPreferenceSettings;
		this.userPermissionManager = userPermissionManager;
	}

	public void sendHomeworkMarkingUpdateBroadcastNotificationsAsync(
			int instituteId, UUID homeworkId, UUID studentId, UUID facultyId) {
		Thread t = new Thread(new Runnable() {
			@Override
			public void run() {
				sendHomeworkMarkingUpdateBroadcastNotifications(instituteId, homeworkId, studentId, facultyId);
			}
		});
		t.start();
	}

	public void sendHomeworkMarkingUpdateBroadcastNotifications(int instituteId, UUID homeworkId, UUID studentId, UUID facultyId) {
		if (instituteId <= 0 || homeworkId == null) {
			logger.error("Invalid institute {} or homeworks {} ", instituteId, homeworkId);
			return;
		}

		Set<UUID> userIdSet = new HashSet<>();
		userIdSet.add(studentId);
		userIdSet.add(facultyId);
		List<User> users = userManager.getUsers(instituteId, userIdSet);
		if (CollectionUtils.isEmpty(users)) {
			logger.error("No user found for notification for institute {}, studentId {}, facultyId {} ", instituteId, studentId, facultyId);
			return;
		}

		String facultyName = "";
		List<User> finalUserList = new ArrayList<User>();
		for(User user : users) {
			if(!user.getUserStatus().equals(UserStatus.ENABLED)) {
				continue;
			}
			if(!user.getUuid().equals(facultyId)) {
				finalUserList.add(user);
			} else {
				facultyName = user.getFullName();
			}
		}

		Map<String, String> metaData = new HashMap<String, String>();
		metaData.put(HOMEWORK_ID, homeworkId == null ? null : homeworkId.toString());

		HomeworkDetails homeworkDetails = homeworkManager.getHomeworkDetails(instituteId, homeworkId);
		String homeworkTitleName = homeworkDetails.getTitle();
		PushNotificationContent pushNotificationContent = homeworkMarkingUpdatePushNotificationContentBuilder
				.getPushNotificationContent(homeworkTitleName);

		boolean notificationAdded = pushNotificationManager.addNotification(new BellNotificationPayload(
				instituteId, finalUserList, pushNotificationContent.getTitle(), pushNotificationContent.getBody(), 
				NotificationEntity.HOMEWORK_MARKING, homeworkId, metaData));
		
		if(!notificationAdded) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_BELL_NOTIFICATION_DETAILS,
							"Invalid bell notification details."));
		}


		prepareAndSendBulkNotificationAsync(instituteId, finalUserList, pushNotificationContent, new IUpdateNotificationPayload() {
			@Override
			public void update(Map<String, String> dataPayload, User user) {
				dataPayload.put(PushNotificationManager.CLICK_ACTION, STUDENT_HOMEWORK_MARKING_UPDATE);
				dataPayload.put(HOMEWORK_DETAILS, GSON.toJson(homeworkDetails));
			}
		});

		// Multicast
//		List<UserNotificationDeviceData> userNotificationDeviceDataList = new ArrayList<>();
//
//		for (User user : finalUserList) {
//			if (CollectionUtils.isEmpty(user.getUserAppTokens())) {
//				continue;
//			}
//			userNotificationDeviceDataList.add(new UserNotificationDeviceData(user.getUuid(), user.getUserAppTokens()));
//		}
//
//		if (CollectionUtils.isEmpty(userNotificationDeviceDataList)) {
//			logger.error("No registered users found for notification for institute {} , studentId {} ", instituteId,
//					studentId);
//			return;
//		}
//
//		logger.info("Sending push notifications for registered users {} for institute {} , studentId {}",
//				userNotificationDeviceDataList.size(), instituteId, studentId);
//
//
//
//		pushNotificationManager.sendMultiCastNotification(instituteId,
//				new MulticastPushNotificationMessage(pushNotificationContent.getTitle(),
//						pushNotificationContent.getBody(), pushNotificationContent.getImageURL(),
//						userNotificationDeviceDataList, dataPayload));
	}

	public void sendBulkHomeworkMarkingUpdateBroadcastNotificationsAsync(
			int instituteId, UUID homeworkId, List<StudentHomeworkSubmissionPayload> studentHomeworkSubmissionPayloadList, UUID facultyId) {
		Thread t = new Thread(new Runnable() {
			@Override
			public void run() {
				sendBulkHomeworkMarkingUpdateBroadcastNotifications(instituteId, homeworkId, studentHomeworkSubmissionPayloadList, facultyId);
			}
		});
		t.start();
	}

	public void sendBulkHomeworkMarkingUpdateBroadcastNotifications(int instituteId, UUID homeworkId,
					List<StudentHomeworkSubmissionPayload> studentHomeworkSubmissionPayloadList, UUID facultyId) {
		if (instituteId <= 0 || homeworkId == null) {
			logger.error("Invalid institute {} or homeworks {} ", instituteId, homeworkId);
			return;
		}

		Set<UUID> userIdSet = new HashSet<>();
		for(StudentHomeworkSubmissionPayload studentHomeworkSubmissionPayload : studentHomeworkSubmissionPayloadList) {
			userIdSet.add(studentHomeworkSubmissionPayload.getStudentId());
		}
		userIdSet.add(facultyId);
		List<User> users = userManager.getUsers(instituteId, userIdSet);
		if (CollectionUtils.isEmpty(users)) {
			logger.error("No user found for notification for institute {}, facultyId {} ", instituteId, facultyId);
			return;
		}

		String facultyName = "";
		List<User> finalUserList = new ArrayList<User>();
		for (User user : users) {
			if (!user.getUserStatus().equals(UserStatus.ENABLED)) {
				continue;
			}
			if (!user.getUuid().equals(facultyId)) {
				finalUserList.add(user);
			} else {
				facultyName = user.getFullName();
			}
		}

		Map<String, String> metaData = new HashMap<String, String>();
		metaData.put(HOMEWORK_ID, homeworkId == null ? null : homeworkId.toString());

		HomeworkDetails homeworkDetails = homeworkManager.getHomeworkDetails(instituteId, homeworkId);
		String homeworkTitleName = homeworkDetails.getTitle();
		PushNotificationContent pushNotificationContent = homeworkMarkingUpdatePushNotificationContentBuilder
				.getPushNotificationContent(homeworkTitleName);

		boolean notificationAdded = pushNotificationManager.addNotification(new BellNotificationPayload(
				instituteId, finalUserList, pushNotificationContent.getTitle(), pushNotificationContent.getBody(),
				NotificationEntity.HOMEWORK_MARKING, homeworkId, metaData));

		if (!notificationAdded) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_BELL_NOTIFICATION_DETAILS,
							"Invalid bell notification details."));
		}


		prepareAndSendBulkNotificationAsync(instituteId, finalUserList, pushNotificationContent, new IUpdateNotificationPayload() {
			@Override
			public void update(Map<String, String> dataPayload, User user) {
				dataPayload.put(PushNotificationManager.CLICK_ACTION, STUDENT_HOMEWORK_MARKING_UPDATE);
				dataPayload.put(HOMEWORK_DETAILS, GSON.toJson(homeworkDetails));
			}
		});
	}
}
