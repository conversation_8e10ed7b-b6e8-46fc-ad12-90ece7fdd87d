/**
 * 
 */
package com.embrate.cloud.push.notifications.content.builder;

import com.embrate.cloud.core.api.service.notification.PushNotificationContent;
import com.itextpdf.styledxmlparser.jsoup.internal.StringUtil;
import com.lernen.cloud.core.api.user.UserType;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 *
 */
public class HomeworkBroadcastPushNotificationContentBuilder {
	
	public PushNotificationContent getPushNotificationContent(boolean isSingleHomework, String standardName, String courseName, UserType userType) {
		switch(userType) {
			case STUDENT:
				return getStudentPushNotificationContent(isSingleHomework, standardName, courseName);
			case ADMIN:
				return getAdminPushNotificationContent(isSingleHomework, standardName, courseName);
			default:
				return null;
		}
	}

	private PushNotificationContent getStudentPushNotificationContent(boolean isSingleHomework, String standardName, String courseName) {
		return new PushNotificationContent("New Homework.",
				"You have received new homework. Click here to see it.", null);
	}

	private PushNotificationContent getAdminPushNotificationContent(boolean isSingleHomework, String standardName, String courseName) {
		if(isSingleHomework) {
			return new PushNotificationContent("New Homework.",
					"Homework sent for class " + standardName + (StringUtils.isBlank(courseName) ? "" : " of course " + courseName)  + ".", null);
		}
		return new PushNotificationContent("New Homeworks.",
				"Homework sent for multiple classes. Click here to see them." , null);
	}
}