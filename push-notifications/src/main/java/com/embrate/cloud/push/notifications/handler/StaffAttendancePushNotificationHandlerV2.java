package com.embrate.cloud.push.notifications.handler;

import com.embrate.cloud.core.api.attendance.AttendanceInputType;
import com.embrate.cloud.core.api.attendance.DeviceAttendanceResult;
import com.embrate.cloud.core.api.service.notification.BellNotificationPayload;
import com.embrate.cloud.core.api.service.notification.NotificationEntity;
import com.embrate.cloud.core.api.service.notification.PushNotificationContent;
import com.embrate.cloud.core.api.service.notification.PushNotificationPreferences;
import com.embrate.cloud.core.lib.push.notification.PushNotificationManager;
import com.embrate.cloud.push.notifications.content.builder.StaffAttendancePushNotificationContentBuilder;
import com.embrate.cloud.push.notifications.content.builder.StaffAttendancePushNotificationContentBuilderV2;
import com.lernen.cloud.core.api.attendance.staff.StaffAttendanceType;
import com.lernen.cloud.core.api.attendance.staff.v2.StaffAttendanceInput;
import com.lernen.cloud.core.api.attendance.staff.v2.StaffAttendancePayloadV2;
import com.lernen.cloud.core.api.attendance.staff.v2.StaffAttendanceRegisterData;
import com.lernen.cloud.core.api.attendance.staff.v3.StaffAttendanceInputV3;
import com.lernen.cloud.core.api.attendance.staff.v3.StaffAttendancePayloadV3;
import com.lernen.cloud.core.api.institute.Time;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.staff.StaffTimingDetails;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserStatus;
import com.lernen.cloud.core.lib.attendance.staff.StaffAttendanceManager;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.managers.UserManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.lib.staff.StaffManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.attendance.staff.StaffAttendanceUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

public class StaffAttendancePushNotificationHandlerV2 extends AbstractPushNotificationHandler{
    private static final Logger logger = LogManager.getLogger(StaffAttendancePushNotificationHandlerV2.class);

    public static final String STAFF_ATTENDANCE = "staffAttendance";
    public static final String ADMIN_STAFF_ATTENDANCE = "adminStaffAttendance";
    private final PushNotificationManager pushNotificationManager;
    private final UserManager userManager;
    private final UserPreferenceSettings userPreferenceSettings;
    private final StaffManager staffManager;
    private final StaffAttendancePushNotificationContentBuilderV2 staffAttendancePushNotificationContentBuilderV2;

    public StaffAttendancePushNotificationHandlerV2(PushNotificationManager pushNotificationManager,
                                                  UserManager userManager,
                                                  UserPreferenceSettings userPreferenceSettings, StaffManager staffManager, StaffAttendancePushNotificationContentBuilderV2 staffAttendancePushNotificationContentBuilderV2) {
        super(pushNotificationManager);
        this.pushNotificationManager = pushNotificationManager;
        this.userManager = userManager;
        this.userPreferenceSettings = userPreferenceSettings;
        this.staffManager = staffManager;
        this.staffAttendancePushNotificationContentBuilderV2 = staffAttendancePushNotificationContentBuilderV2;
    }

    public void sendAttendanceUpdateNotificationsAsync(int instituteId, AttendanceInputType attendanceInputType, StaffAttendancePayloadV3 staffAttendancePayload) {

        if (instituteId <= 0 || staffAttendancePayload == null || staffAttendancePayload.getAttendanceDate() <= 0 || CollectionUtils.isEmpty(staffAttendancePayload.getStaffAttendanceInputList())) {
            logger.error("Invalid institute {} or payload {} to send notifications.", instituteId, staffAttendancePayload);
            return;
        }

        PushNotificationPreferences pushNotificationPreferences = userPreferenceSettings.getPushNotificationPreferences(instituteId);
        boolean isStaffAttendanceMobileNotificationEnabled = pushNotificationPreferences.isStaffAttendanceMobileNotificationEnabled();
        if (!isStaffAttendanceMobileNotificationEnabled) {
            logger.info("Not sending the notification as staff attendance notification is not enable for institute {}", instituteId);
            return;
        }
        int attendanceDate = staffAttendancePayload.getAttendanceDate();
        int currentDate = DateUtils.now();
        if(DateUtils.getDayStart(attendanceDate, DateUtils.DEFAULT_TIMEZONE) != DateUtils.getDayStart(currentDate, DateUtils.DEFAULT_TIMEZONE)){
            logger.warn("Not sending the notification as notification date {} is not the current date {}", attendanceDate, currentDate);
            return;
        }

        for(StaffAttendanceInputV3 staffAttendanceInputV3 : staffAttendancePayload.getStaffAttendanceInputList()) {
            UUID staffId = staffAttendanceInputV3.getStaffId();
            sendAttendanceUpdateNotificationsAsync(instituteId, attendanceDate, new HashSet<>(Collections.singletonList(staffId)), attendanceInputType, staffAttendanceInputV3);
        }
    }


    public void sendAttendanceUpdateNotificationsAsync(int instituteId, int attendanceDate, Set<UUID> staffIds, AttendanceInputType attendanceInputType, StaffAttendanceInputV3 staffAttendanceInput) {
        Thread t = new Thread(new Runnable() {
            @Override
            public void run() {
                sendAttendanceUpdateNotifications(instituteId, attendanceDate, staffIds, attendanceInputType, staffAttendanceInput);
            }
        });
        t.start();
    }

    public void sendAttendanceUpdateNotifications(int instituteId, int attendanceDate, Set<UUID> staffIds, AttendanceInputType attendanceInputType, StaffAttendanceInputV3 staffAttendanceInput) {

        if(instituteId <= 0 || CollectionUtils.isEmpty(staffIds)){
            logger.error("Invalid input institute {} or staffIds is empty", instituteId);
            return;
        }


        List<User> userList = userManager.getUsers(instituteId, staffIds);
        Map<UUID, User> userMap = getUserMap(userList);
        if(userMap == null || CollectionUtils.isEmpty(userMap.entrySet())) {
            logger.warn("No staff found to send notifications");
            return;
        }

        /**
         * combing data based on staffAttendanceType as they will have same content
         */

        PushNotificationPreferences pushNotificationPreferences = userPreferenceSettings.getPushNotificationPreferences(instituteId);
        Set<String> userNames = pushNotificationPreferences.getAdminStaffAttendanceNotificationUserNames();
        List<User> adminUserList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(userNames)) {
            adminUserList = userManager.getUsersByUserNames(userNames);
        }

        String staffName = userList.get(0).getName();
        String staffId = userList.get(0).getUserInstituteId();
        Staff staff = staffManager.getStaff(staffAttendanceInput.getStaffId());

        boolean lateEntry = false;
        boolean earlyExit = false;
        if (staff != null && staff.getStaffTimingDetails() != null) {
            StaffTimingDetails staffTimingDetails = staff.getStaffTimingDetails();
            Time inTime = staffTimingDetails.getInTime();
            Time outTime = staffTimingDetails.getOutTime();
            Time entryTime = staffAttendanceInput.getStaffTimeDurationList().get(0).getInTime();
            Time exitTime = staffAttendanceInput.getStaffTimeDurationList().get(staffAttendanceInput.getStaffTimeDurationList().size() - 1).getOutTime();

            if (entryTime != null && inTime != null && entryTime.isAfter(inTime)) {
                lateEntry = true;
            }

            if (outTime != null && exitTime != null && outTime.isAfter(exitTime)) {
                earlyExit = true;
            }

        }

        sendStaffMobileAppNotification(instituteId, attendanceDate, staffAttendanceInput, Collections.singletonList(userList.get(0)), attendanceInputType, lateEntry, earlyExit);

        sendAdminMobileAppNotification(instituteId, attendanceDate, staffId, staffName, staffAttendanceInput, adminUserList, attendanceInputType, lateEntry, earlyExit);

    }

    private Map<UUID, User> getUserMap(List<User> userList) {
        if(CollectionUtils.isEmpty(userList)) {
            return null;
        }
        Map<UUID, User> userLiteMap = new HashMap<>();
        for(User user : userList) {
            if(user.getUserStatus() != UserStatus.ENABLED) {
                continue;
            }
            userLiteMap.put(user.getUuid(), user);
        }
        return userLiteMap;
    }

    private void sendStaffMobileAppNotification(int instituteId, int attendanceDate, StaffAttendanceInputV3 staffAttendanceInput, List<User> userList, AttendanceInputType attendanceInputType,
                                                boolean lateEntry, boolean earlyExit) {

        String attendanceDateStr = DateUtils.getFormattedDate(attendanceDate, DateUtils.DEFAULT_DATE_FORMAT, DateUtils.DEFAULT_TIMEZONE);

        PushNotificationContent pushNotificationContent = staffAttendancePushNotificationContentBuilderV2
                .getStaffPushNotificationContent(attendanceDateStr, attendanceInputType, staffAttendanceInput,
        lateEntry, earlyExit);

        if(pushNotificationContent == null) {
            logger.error("No able to create proper content for notification for institute {}, " +
                    "attendanceInputType {}, staffAttendanceInput {}", instituteId, attendanceInputType, staffAttendanceInput);
            return;
        }
        Map<String, String> metaData = new HashMap<String, String>();
        boolean notificationAdded = pushNotificationManager.addNotification(new BellNotificationPayload(
                instituteId, userList, pushNotificationContent.getTitle(), pushNotificationContent.getBody(),
                NotificationEntity.STAFF_ATTENDANCE, null, metaData));

        if (!notificationAdded) {
            logger.error("Unable to add bell notification details for attendance date {}, staffAttendanceType {}", attendanceDate, staffAttendanceInput);
            return;
        }
        prepareAndSendBulkNotificationAsync(instituteId, userList, pushNotificationContent, new IUpdateNotificationPayload() {
            @Override
            public void update(Map<String, String> dataPayload, User user) {
                dataPayload.put(PushNotificationManager.CLICK_ACTION, STAFF_ATTENDANCE);
            }
        });
    }

    private void sendAdminMobileAppNotification(int instituteId, int attendanceDate, String staffId, String staffName, StaffAttendanceInputV3 staffAttendanceInput,
                                                List<User> userList, AttendanceInputType attendanceInputType,  boolean lateEntry,
                                                boolean earlyExit) {

        String attendanceDateStr = DateUtils.getFormattedDate(attendanceDate, DateUtils.DEFAULT_DATE_FORMAT, DateUtils.DEFAULT_TIMEZONE);
        PushNotificationContent pushNotificationContent = staffAttendancePushNotificationContentBuilderV2
                .getAdminPushNotificationContent(staffId, staffName, attendanceInputType, staffAttendanceInput, attendanceDateStr, lateEntry, earlyExit);
        if(pushNotificationContent == null) {
            logger.error("No able to create proper content for notification for institute {}, " +
                    "attendanceInputType {}, staffAttendanceInput {}", instituteId, attendanceInputType, staffAttendanceInput);
            return;
        }
        Map<String, String> metaData = new HashMap<String, String>();
        boolean notificationAdded = pushNotificationManager.addNotification(new BellNotificationPayload(
                instituteId, userList, pushNotificationContent.getTitle(), pushNotificationContent.getBody(),
                NotificationEntity.ADMIN_STAFF_ATTENDANCE, null, metaData));

        if (!notificationAdded) {
            logger.error("Unable to add bell notification details for attendance date {}, staffAttendanceType {}", attendanceDate, staffAttendanceInput);
            return;
        }
        prepareAndSendBulkNotificationAsync(instituteId, userList, pushNotificationContent, new IUpdateNotificationPayload() {
            @Override
            public void update(Map<String, String> dataPayload, User user) {
                dataPayload.put(PushNotificationManager.CLICK_ACTION, ADMIN_STAFF_ATTENDANCE);
            }
        });
    }
}

