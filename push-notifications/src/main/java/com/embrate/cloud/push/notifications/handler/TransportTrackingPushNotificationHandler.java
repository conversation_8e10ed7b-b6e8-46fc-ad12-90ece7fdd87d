package com.embrate.cloud.push.notifications.handler;

import com.embrate.cloud.core.api.service.notification.BellNotificationPayload;
import com.embrate.cloud.core.api.service.notification.NotificationEntity;
import com.embrate.cloud.core.api.service.notification.PushNotificationContent;
import com.embrate.cloud.core.api.transport.tracking.TransportTrackingData;
import com.embrate.cloud.core.lib.push.notification.PushNotificationManager;
import com.embrate.cloud.core.lib.transport.tracking.TransportTrackingManger;
import com.embrate.cloud.push.notifications.content.builder.TransportTrackingPushNotificationContentBuilder;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.student.StudentAreaWrapper;
import com.lernen.cloud.core.api.student.TransportTripStudentDetails;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserStatus;
import com.lernen.cloud.core.lib.managers.UserManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

/**
 * 
 * <AUTHOR>
 *
 */
public class TransportTrackingPushNotificationHandler extends AbstractPushNotificationHandler {

	public static final String STUDENT_TRANSPORT_TRACKING = "studentTransportTracking";
	public static final String TRANSPORT_TRIP_ID = "transportTripId";
	public static final String TRANSPORT_SERVICE_ROUTE_ID = "transportServiceRouteId";

	private static final Logger logger = LogManager.getLogger(TransportTrackingPushNotificationHandler.class);
	private final PushNotificationManager pushNotificationManager;
	private final TransportTrackingPushNotificationContentBuilder transportTrackingPushNotificationContentBuilder;

	private final TransportTrackingManger transportTrackingManger;

	private final UserManager userManager;

	public TransportTrackingPushNotificationHandler(PushNotificationManager pushNotificationManager,
													TransportTrackingPushNotificationContentBuilder transportTrackingPushNotificationContentBuilder,
													TransportTrackingManger transportTrackingManger, UserManager userManager) {
		super(pushNotificationManager);
		this.pushNotificationManager = pushNotificationManager;
		this.transportTrackingPushNotificationContentBuilder = transportTrackingPushNotificationContentBuilder;
		this.transportTrackingManger = transportTrackingManger;
		this.userManager = userManager;
	}

	public void sendTransportTrackingNotificationAsync(int instituteId, UUID transportTripId) {
		Thread t = new Thread(new Runnable() {
			@Override
			public void run() {
				sendTransportTrackingNotification(instituteId, transportTripId);
			}
		});
		t.start();
	}

	public void sendTransportTrackingNotification(int instituteId, UUID transportTripId) {
		if (instituteId <= 0 || transportTripId == null) {
			logger.error("Invalid institute {} or transportTripId {} ", instituteId, transportTripId);
			return;
		}

		PushNotificationContent pushNotificationContent = transportTrackingPushNotificationContentBuilder
				.getPushNotificationContent();

		TransportTrackingData transportTrackingData = transportTrackingManger.getTransportTrackingDetails(instituteId, transportTripId);
		if(transportTrackingData == null){
			logger.error("Invalid transportTripId {}, {} ", instituteId, transportTripId);
			return;
		}

		TransportTripStudentDetails transportTripStudentDetails = transportTrackingManger.getTodayTransportTripStudentDetails(instituteId, transportTrackingData.getServiceRouteId());
		Set<UUID> studentIds = new HashSet<>();
		if(transportTripStudentDetails != null && transportTripStudentDetails.getTransportServiceRouteDetails() != null &&
				CollectionUtils.isNotEmpty(transportTripStudentDetails.getTransportServiceRouteDetails().getStudents())){
			for(StudentAreaWrapper studentAreaWrapper : transportTripStudentDetails.getTransportServiceRouteDetails().getStudents()){
				studentIds.add(studentAreaWrapper.getStudentLite().getStudentId());
			}
		}

		if(CollectionUtils.isEmpty(studentIds)){
			logger.error("No students are assigned to transportTripId {}, {}, service route {} ", instituteId, transportTripId, transportTrackingData.getServiceRouteId());
			return;
		}

		List<User> studentUsers =  userManager.getUsers(instituteId, studentIds);
		if(CollectionUtils.isEmpty(studentIds)){
			logger.error("No students are found for transportTripId {}, {}, service route {} ", instituteId, transportTripId, transportTrackingData.getServiceRouteId());
			return;
		}

		List<User> finalUserList = new ArrayList<>();
		for(User user : studentUsers) {
			if(!user.getUserStatus().equals(UserStatus.ENABLED)) {
				continue;
			}
			finalUserList.add(user);
		}


		Map<String, String> metaData = new HashMap<String, String>();
		metaData.put(TRANSPORT_TRIP_ID, transportTripId == null ? null : transportTripId.toString());
		metaData.put(TRANSPORT_SERVICE_ROUTE_ID, transportTrackingData.getServiceRouteId() == null ? null : transportTrackingData.getServiceRouteId().toString());

		boolean notificationAdded = pushNotificationManager.addNotification(new BellNotificationPayload(
				instituteId, finalUserList, pushNotificationContent.getTitle(), pushNotificationContent.getBody(), 
				NotificationEntity.STUDENT_TRANSPORT_TRACKING, transportTripId, metaData));
		
		if(!notificationAdded) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_BELL_NOTIFICATION_DETAILS,
							"Invalid bell notification details."));
		}


		prepareAndSendBulkNotificationAsync(instituteId, finalUserList, pushNotificationContent, new IUpdateNotificationPayload() {
			@Override
			public void update(Map<String, String> dataPayload, User user) {
				dataPayload.put(PushNotificationManager.CLICK_ACTION, STUDENT_TRANSPORT_TRACKING);
			}
		});

		//Multicast
//		List<UserNotificationDeviceData> userNotificationDeviceDataList = new ArrayList<>();
//
//		for (User user : finalUserList) {
//			if (CollectionUtils.isEmpty(user.getUserAppTokens())) {
//				continue;
//			}
//			userNotificationDeviceDataList.add(new UserNotificationDeviceData(user.getUuid(), user.getUserAppTokens()));
//		}
//
//		if (CollectionUtils.isEmpty(userNotificationDeviceDataList)) {
//			logger.error("No registered users found for notification for institute {} , lectures {} ", instituteId,
//					lectureIds);
//			return;
//		}
//
//		logger.info("Sending push notifications for registered users {} for institute {} , lectures {}",
//				userNotificationDeviceDataList.size(), instituteId, lectureIds);
//
//		Map<String, String> dataPayload = new HashMap<>();
//		dataPayload.put(PushNotificationManager.CLICK_ACTION, STUDENT_NEW_LECTURE_UPLOAD);
//		dataPayload.put(PushNotificationManager.FLUTTER_REQUIRED_CLICK_ACTION_KEY, FLUTTER_REQUIRED_CLICK_ACTION_VAL);
//
//		pushNotificationManager.sendMultiCastNotification(instituteId,
//				new MulticastPushNotificationMessage(pushNotificationContent.getTitle(),
//						pushNotificationContent.getBody(), pushNotificationContent.getImageURL(),
//						userNotificationDeviceDataList, dataPayload));

	}
}
