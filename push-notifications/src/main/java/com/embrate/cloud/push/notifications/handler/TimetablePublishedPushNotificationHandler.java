/**
 * 
 */
package com.embrate.cloud.push.notifications.handler;

import com.embrate.cloud.core.api.service.notification.BellNotificationPayload;
import com.embrate.cloud.core.api.service.notification.NotificationEntity;
import com.embrate.cloud.core.api.service.notification.PushNotificationContent;
import com.embrate.cloud.core.lib.push.notification.PushNotificationManager;
import com.embrate.cloud.core.lib.timetable.TimetableManager;
import com.embrate.cloud.push.notifications.content.builder.TimetablePublishedPushNotificationContentBuilder;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserStatus;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

/**
 * <AUTHOR>
 *
 */
public class TimetablePublishedPushNotificationHandler extends AbstractPushNotificationHandler {

	private static final Logger logger = LogManager.getLogger(TimetablePublishedPushNotificationHandler.class);

	public static final String STUDENT_TIMETABLE_PUBLISHED = "studentTimetablePublished";
	public static final String TIMETABLE_ID = "timetableId";

	private final PushNotificationManager pushNotificationManager;
	private final TimetablePublishedPushNotificationContentBuilder timetablePublishedPushNotificationContentBuilder;
	private final TimetableManager timetableManager;
	private final UserPreferenceSettings userPreferenceSettings;
	private final UserPermissionManager userPermissionManager;

	public TimetablePublishedPushNotificationHandler(PushNotificationManager pushNotificationManager,
													 TimetablePublishedPushNotificationContentBuilder timetablePublishedPushNotificationContentBuilder,
													 TimetableManager timetableManager, UserPreferenceSettings userPreferenceSettings,
													 UserPermissionManager userPermissionManager) {
		super(pushNotificationManager);
		this.pushNotificationManager = pushNotificationManager;
		this.timetablePublishedPushNotificationContentBuilder = timetablePublishedPushNotificationContentBuilder;
		this.timetableManager = timetableManager;
		this.userPreferenceSettings = userPreferenceSettings;
		this.userPermissionManager = userPermissionManager;
	}

	public void sendTimetablePublishedNotificationsAsync(int instituteId, int academicSessionId, UUID timetableId, UUID createdUserId) {
		Thread t = new Thread(new Runnable() {
			@Override
			public void run() {
				sendTimetablePublishedNotifications(instituteId, academicSessionId, timetableId, createdUserId);
			}
		});
		t.start();
	}

	public void sendTimetablePublishedNotifications(int instituteId, int academicSessionId, UUID timetableId, UUID createdUserId) {
		if (instituteId <= 0 || timetableId == null) {
			logger.error("Invalid institute {} or timetable {} ", instituteId, timetableId);
			return;
		}

		PushNotificationContent pushNotificationContent = timetablePublishedPushNotificationContentBuilder
				.getPushNotificationContent();

		List<User> users = timetableManager.getTimetableUsers(instituteId, academicSessionId, timetableId);
		if (CollectionUtils.isEmpty(users)) {
			logger.error("No user found for notification for institute {} ,timetable {} ", instituteId, timetableId);
			return;
		}

		List<User> finalUserList = new ArrayList<User>();
		for(User user : users) {
			if(!user.getUserStatus().equals(UserStatus.ENABLED)) {
				continue;
			}
			if(!user.getUuid().equals(createdUserId)) {
				finalUserList.add(user);
			}
		}

		Map<String, String> metaData = new HashMap<String, String>();
		metaData.put(TIMETABLE_ID, timetableId == null ? null : timetableId.toString());
		
		boolean notificationAdded = pushNotificationManager.addNotification(new BellNotificationPayload(
				instituteId, finalUserList, pushNotificationContent.getTitle(), pushNotificationContent.getBody(), 
				NotificationEntity.TIMETABLE_PUBLISHED, timetableId, metaData));
		
		if(!notificationAdded) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_BELL_NOTIFICATION_DETAILS,
							"Invalid bell notification details."));
		}

		prepareAndSendBulkNotificationAsync(instituteId, finalUserList, pushNotificationContent, new IUpdateNotificationPayload() {
			@Override
			public void update(Map<String, String> dataPayload, User user) {
				dataPayload.put(PushNotificationManager.CLICK_ACTION, STUDENT_TIMETABLE_PUBLISHED);
			}
		});
	}
}
