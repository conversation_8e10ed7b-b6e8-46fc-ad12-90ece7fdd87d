/**
 * 
 */
package com.embrate.cloud.push.notifications.content.builder;

import com.embrate.cloud.core.api.service.notification.PushNotificationContent;
import com.lernen.cloud.core.api.user.UserType;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 *
 */
public class NoticeBoardBroadcastPushNotificationContentBuilder {
//	public PushNotificationContent getPushNotificationContent() {
//		return new PushNotificationContent("New Notice",
//				"Your school has sent a new notice. Check it out!.", null);
//	}

	public PushNotificationContent getPushNotificationContent(boolean isSingleNotice, String title, UserType userType) {
		switch(userType) {
			case STUDENT:
				return getStudentPushNotificationContent(isSingleNotice, title);
			case ADMIN:
				return getAdminPushNotificationContent(isSingleNotice, title);
			default:
				return null;
		}
	}

	private PushNotificationContent getStudentPushNotificationContent(boolean isSingleHomework, String title) {
		return new PushNotificationContent("New Notice.",
				"Your school has sent a new notice. Check it out.", null);
	}

	private PushNotificationContent getAdminPushNotificationContent(boolean isSingleHomework, String title) {
		if(isSingleHomework) {
			return new PushNotificationContent("New Notice.",
					"A notice '" + title + "' has been sent." , null);
		}
		return new PushNotificationContent("New Notices.",
				"Multiple notices sent. Click here to see them." , null);
	}
}
