package com.embrate.cloud.push.notifications.content.builder;

import com.embrate.cloud.core.api.service.notification.PushNotificationContent;
import com.embrate.cloud.core.api.service.notification.PushNotificationPreferences;
import com.embrate.cloud.core.lib.templates.notification.NotificationTemplateManager;
import com.embrate.cloud.push.notifications.handler.AbstractPushNotificationHandler;

import java.util.UUID;

/**
 * <AUTHOR>
 * @created_at 25/08/23 : 17:15
 **/
public class FeePaymentReminderPushNotificationContentBuilder extends PushNotificationContentBuilder{

    public FeePaymentReminderPushNotificationContentBuilder(NotificationTemplateManager notificationTemplateManager) {
        super(notificationTemplateManager);
    }

    public PushNotificationContent getPushNotificationContent(AbstractPushNotificationHandler.PushNotificationUserMetadata pushNotificationUserMetadata,
                                                              PushNotificationPreferences pushNotificationPreferences) {
        UUID templateId = pushNotificationPreferences.getDueFeeReminderPushNotificationTemplateId();
        if(templateId == null) {
            return null;
        }
        return new PushNotificationContent("Fee Payment Reminder",
                generateTextContent(templateId, pushNotificationUserMetadata), null);
    }
}
