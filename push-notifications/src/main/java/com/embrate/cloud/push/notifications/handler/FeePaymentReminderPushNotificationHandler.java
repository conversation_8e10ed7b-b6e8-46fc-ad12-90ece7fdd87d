/**
 *
 */
package com.embrate.cloud.push.notifications.handler;

import com.embrate.cloud.core.api.service.notification.BellNotificationPayload;
import com.embrate.cloud.core.api.service.notification.NotificationEntity;
import com.embrate.cloud.core.api.service.notification.PushNotificationContent;
import com.embrate.cloud.core.api.service.notification.PushNotificationPreferences;
import com.embrate.cloud.core.lib.push.notification.PushNotificationManager;
import com.embrate.cloud.push.notifications.content.builder.FeePaymentPushNotificationContentBuilder;
import com.embrate.cloud.push.notifications.content.builder.FeePaymentReminderPushNotificationContentBuilder;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.fees.payment.FeePaymentResponse;
import com.lernen.cloud.core.api.fees.payment.FeePaymentTransactionDetails;
import com.lernen.cloud.core.api.fees.payment.StudentDueFeesData;
import com.lernen.cloud.core.api.fees.payment.reminders.FeePaymentReminderPayload;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.fees.payment.FeePaymentManager;
import com.lernen.cloud.core.lib.managers.UserManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.utils.DateUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 */
public class FeePaymentReminderPushNotificationHandler extends AbstractPushNotificationHandler {

    private static final Logger logger = LogManager.getLogger(FeePaymentReminderPushNotificationHandler.class);
    public static final String FEE_PAYMENT_REMINDER = "feePaymentReminder";
    private final PushNotificationManager pushNotificationManager;
    private final FeePaymentReminderPushNotificationContentBuilder feePaymentReminderPushNotificationContentBuilder;
    private final UserManager userManager;
    private final UserPreferenceSettings userPreferenceSettings;
    private final UserPermissionManager userPermissionManager;
	private final FeePaymentManager feePaymentManager;

    public FeePaymentReminderPushNotificationHandler(PushNotificationManager pushNotificationManager,
													 FeePaymentReminderPushNotificationContentBuilder feePaymentReminderPushNotificationContentBuilder,
                                                     UserManager userManager,
                                                     UserPreferenceSettings userPreferenceSettings,
                                                     UserPermissionManager userPermissionManager, FeePaymentManager feePaymentManager) {
        super(pushNotificationManager);
        this.pushNotificationManager = pushNotificationManager;
        this.feePaymentReminderPushNotificationContentBuilder = feePaymentReminderPushNotificationContentBuilder;
        this.userManager = userManager;
        this.userPreferenceSettings = userPreferenceSettings;
        this.userPermissionManager = userPermissionManager;
		this.feePaymentManager = feePaymentManager;
	}

	public void sendFeePaymentReminderNotificationsAsync(int instituteId, FeePaymentReminderPayload feePaymentReminderPayload, UUID userId) {
		Thread t = new Thread(new Runnable() {
			@Override
			public void run() {
				sendFeePaymentReminderNotifications(instituteId, feePaymentReminderPayload, userId);
			}
		});
		t.start();
	}

	public void sendFeePaymentReminderNotifications(int instituteId, FeePaymentReminderPayload feePaymentReminderPayload, UUID userId) {

		if (instituteId <= 0 || userId == null) {
			logger.error("Invalid details send notifications institute {}, user {} ", instituteId, userId);
			return;
		}
		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.SEND_DUE_FEE_REMINDERS);

		if(feePaymentReminderPayload == null ||feePaymentReminderPayload.getAcademicSessionId() <= 0
				|| CollectionUtils.isEmpty(feePaymentReminderPayload.getStudentIds()) ||
				feePaymentReminderPayload.getDueDate() <= 0) {
			logger.error("Invalid details send notifications {} ", feePaymentReminderPayload);
			return;
		}

		int academicSessionId = feePaymentReminderPayload.getAcademicSessionId();
		List<UUID> studentIds = feePaymentReminderPayload.getStudentIds();
		int dueFeesDate = feePaymentReminderPayload.getDueDate();

		final List<StudentDueFeesData> studentDueFeesDetailsList = feePaymentManager.getDueFeesStudents(instituteId,
				academicSessionId, dueFeesDate, feePaymentReminderPayload.isComputeFine(),
				feePaymentReminderPayload.getRequiredStanardsCSV(), feePaymentReminderPayload.getTotalSessionCount());

		for(StudentDueFeesData studentDueFeesData : studentDueFeesDetailsList) {
			sendPushNotification(instituteId, studentDueFeesData, studentIds, feePaymentReminderPayload.isComputeFine());
		}
	}

	private void sendPushNotification(int instituteId, StudentDueFeesData studentDueFeesData, List<UUID> studentIds,
									  boolean includeFine) {
		Student student = studentDueFeesData.getStudent();
		if(student == null || student.getStudentId() == null) {
			return;
		}
		UUID studentId = student.getStudentId();
		if(!studentIds.contains(studentId)) {
			return;
		}
		String admissionNumber = student.getStudentBasicInfo().getAdmissionNumber();
		String studentName = student.getStudentBasicInfo().getName();
		String standardName = student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayNameWithSection();
		double dueAmount = includeFine ? studentDueFeesData.getTotalDueAmountWithFine()
				: studentDueFeesData.getTotalDueAmount();
		StudentUserData studentUserData = new StudentUserData(admissionNumber, studentName, standardName, dueAmount);
		AbstractPushNotificationHandler.PushNotificationUserMetadata pushNotificationUserMetadata =
				new PushNotificationUserMetadata(studentId, UserType.STUDENT, studentUserData);
		PushNotificationPreferences pushNotificationPreferences = userPreferenceSettings.getPushNotificationPreferences(instituteId);
		PushNotificationContent pushNotificationContent = feePaymentReminderPushNotificationContentBuilder
				.getPushNotificationContent(pushNotificationUserMetadata, pushNotificationPreferences);

		User user = userManager.getUser(studentId);
		if(user == null || user.getUuid() == null) {
			logger.error("No user found for notification for institute {},student {} ", instituteId, studentId);
			return;
		}

		List<User> finalUserList = Arrays.asList(user);
		Map<String, String> metaData = new HashMap<String, String>();

		boolean notificationAdded = pushNotificationManager.addNotification(new BellNotificationPayload(
				instituteId, finalUserList, pushNotificationContent.getTitle(), pushNotificationContent.getBody(),
				NotificationEntity.FEE_PAYMENT_REMINDER, null, metaData));

		if (!notificationAdded) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_BELL_NOTIFICATION_DETAILS,
							"Invalid bell notification details."));
		}


		prepareAndSendBulkNotificationAsync(instituteId, finalUserList, pushNotificationContent, new IUpdateNotificationPayload() {
			@Override
			public void update(Map<String, String> dataPayload, User user) {
				dataPayload.put(PushNotificationManager.CLICK_ACTION, FEE_PAYMENT_REMINDER);
			}
		});
	}
}
