package com.embrate.cloud.push.notifications.handler;

import com.embrate.cloud.core.api.service.notification.CustomBellNotification;
import com.embrate.cloud.core.api.service.notification.MulticastPushNotificationMessage;
import com.embrate.cloud.core.api.service.notification.PushNotificationContent;
import com.embrate.cloud.core.lib.push.notification.PushNotificationManager;
import com.embrate.cloud.push.notifications.content.builder.CustomPushNotificationContentBuilder;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserNotificationDeviceData;
import com.lernen.cloud.core.api.user.UserStatus;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.managers.UserManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

import static com.embrate.cloud.core.lib.push.notification.PushNotificationManager.FLUTTER_REQUIRED_CLICK_ACTION_VAL;

/**
 * 
 * <AUTHOR>
 *
 */
public class CustomPushNotificationHandler extends AbstractPushNotificationHandler {

	public static final String CUSTOM = "custom";
	public static final String NOTIFICATION_ID = "notificationId";

	private static final Logger logger = LogManager.getLogger(CustomPushNotificationHandler.class);
	private final PushNotificationManager pushNotificationManager;
	private final CustomPushNotificationContentBuilder customPushNotificationContentBuilder;
	private final UserManager userManager;
	private final UserPreferenceSettings userPreferenceSettings;
	private final UserPermissionManager userPermissionManager;



	public CustomPushNotificationHandler(PushNotificationManager pushNotificationManager,
										 CustomPushNotificationContentBuilder customPushNotificationContentBuilder,
										 UserManager userManager, UserPreferenceSettings userPreferenceSettings,
										 UserPermissionManager userPermissionManager) {
		super(pushNotificationManager);
		this.pushNotificationManager = pushNotificationManager;
		this.customPushNotificationContentBuilder = customPushNotificationContentBuilder;
		this.userManager = userManager;
		this.userPreferenceSettings = userPreferenceSettings;
		this.userPermissionManager = userPermissionManager;
	}

	public void sendCustomNotificationsAsync(CustomBellNotification customBellNotification, UUID createdUserId,
											 Map<UUID, UUID> studentNotificationMap) {
		Thread t = new Thread(new Runnable() {
			@Override
			public void run() {
				sendCustomNotifications(customBellNotification, createdUserId, studentNotificationMap);
			}
		});
		t.start();
	}

	public void sendCustomNotifications(CustomBellNotification customBellNotification, UUID createdUserId, Map<UUID, UUID> studentNotificationMap) {
		if (customBellNotification.getInstituteId() <= 0 || CollectionUtils.isEmpty(customBellNotification.getUserIds())
				|| StringUtils.isBlank(customBellNotification.getTitle()) || studentNotificationMap == null ||
				CollectionUtils.isEmpty(studentNotificationMap.entrySet())) {
			logger.error("Invalid institute {} or users {}, or title {}, or student notification map {}",
					customBellNotification.getInstituteId(), customBellNotification.getUserIds(),
					customBellNotification.getTitle(), studentNotificationMap);
			return;
		}

		PushNotificationContent customPushNotificationContent = customPushNotificationContentBuilder
				.getPushNotificationContent(customBellNotification.getTitle(),
						customBellNotification.getBody());

		List<User> users = userManager.getUsers(customBellNotification.getInstituteId(),
				new HashSet<UUID>(customBellNotification.getUserIds()));
		if (CollectionUtils.isEmpty(users)) {
			logger.error("No user found for notification for institute {} ", customBellNotification.getInstituteId());
			return;
		}
		
		List<User> finalUserList = new ArrayList<>();

		for(User user : users) {
			if(!user.getUserStatus().equals(UserStatus.ENABLED)) {
				continue;
			}
			finalUserList.add(user);
		}


		prepareAndSendBulkNotificationAsync(customBellNotification.getInstituteId(), finalUserList, customPushNotificationContent, new IUpdateNotificationPayload() {
			@Override
			public void update(Map<String, String> dataPayload, User user) {
				UUID notificationId = studentNotificationMap.get(user.getUuid());
				dataPayload.put(PushNotificationManager.CLICK_ACTION, CUSTOM);
				dataPayload.put(NOTIFICATION_ID, notificationId.toString());
			}
		});


		// Multicast
//
//		List<UserNotificationDeviceData> userNotificationDeviceDataList = new ArrayList<>();
//
//		for (User user : finalUserList) {
//			if (CollectionUtils.isEmpty(user.getUserAppTokens())) {
//				continue;
//			}
//			userNotificationDeviceDataList.add(new UserNotificationDeviceData(user.getUuid(), user.getUserAppTokens()));
//		}
//
//		if (CollectionUtils.isEmpty(userNotificationDeviceDataList)) {
//			logger.error("No registered users found for notification for institute {}", customBellNotification.getInstituteId());
//			return;
//		}
//
//		logger.info("Sending push notifications for registered users {} for institute {} for users {} ",
//				userNotificationDeviceDataList.size(), customBellNotification.getInstituteId(), customBellNotification.getUserIds());
//
//		/**
//		 * sendMultiCastNotification is handled for multi-user single notification
//		 * using for loop for multi-user single multi-notification
//		 */
//		for(UserNotificationDeviceData userNotificationDeviceData : userNotificationDeviceDataList) {
//			List<UserNotificationDeviceData> singleUserNotificationDeviceDataList = new ArrayList<>();
//			singleUserNotificationDeviceDataList.add(userNotificationDeviceData);
//			UUID userId = userNotificationDeviceData.getUserId();
//			if(userId == null || studentNotificationMap == null || CollectionUtils.isEmpty(studentNotificationMap.entrySet()) ||
//					studentNotificationMap.get(userId) == null) {
//				continue;
//			}
//			UUID notificationId = studentNotificationMap.get(userId);
//			Map<String, String> dataPayload = new HashMap<>();
//			dataPayload.put(PushNotificationManager.CLICK_ACTION, CUSTOM);
//			dataPayload.put(NOTIFICATION_ID, notificationId.toString());
//			dataPayload.put(PushNotificationManager.FLUTTER_REQUIRED_CLICK_ACTION_KEY, FLUTTER_REQUIRED_CLICK_ACTION_VAL);
//			pushNotificationManager.sendMultiCastNotification(customBellNotification.getInstituteId(),
//					new MulticastPushNotificationMessage(customPushNotificationContent.getTitle(),
//							customPushNotificationContent.getBody(), customPushNotificationContent.getImageURL(),
//							singleUserNotificationDeviceDataList, dataPayload));
//		}

	}
}
