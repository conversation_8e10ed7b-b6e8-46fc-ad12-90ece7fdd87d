package com.embrate.cloud.push.notifications.handler;

import com.embrate.cloud.core.api.service.notification.BellNotificationPayload;
import com.embrate.cloud.core.api.service.notification.NotificationEntity;
import com.embrate.cloud.core.api.service.notification.PushNotificationContent;
import com.embrate.cloud.core.lib.push.notification.PushNotificationManager;
import com.embrate.cloud.push.notifications.content.builder.VisitorStatusUpdatePushNotificationContentBuilder;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserStatus;
import com.lernen.cloud.core.api.visitor.VisitorDetails;
import com.lernen.cloud.core.api.visitor.VisitorStatus;
import com.lernen.cloud.core.lib.managers.UserManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.lib.visitor.VisitorDetailsManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

public class VisitorStatusUpdatePushNotificationHandler extends AbstractPushNotificationHandler {
	public static final String VISITOR_ID = "visitorId";
	public static final String ADMIN_VISITOR_APPOINTMENT_STATUS_UPDATE = "adminVisitorAppointmentStatusUpdate";
	private static final Logger logger = LogManager.getLogger(VisitorStatusUpdatePushNotificationHandler.class);
	private final UserManager userManager;
	private final PushNotificationManager pushNotificationManager;
	private final VisitorDetailsManager visitorDetailsManager;
	private final UserPermissionManager userPermissionManager;
	private final VisitorStatusUpdatePushNotificationContentBuilder visitorStatusUpdatePushNotificationContentBuilder;

	public VisitorStatusUpdatePushNotificationHandler(UserManager userManager, PushNotificationManager pushNotificationManager, VisitorDetailsManager visitorDetailsManager, UserPermissionManager userPermissionManager, VisitorStatusUpdatePushNotificationContentBuilder visitorStatusUpdatePushNotificationContentBuilder) {
		super(pushNotificationManager);
		this.userManager = userManager;
		this.pushNotificationManager = pushNotificationManager;
		this.visitorDetailsManager = visitorDetailsManager;
		this.userPermissionManager = userPermissionManager;
		this.visitorStatusUpdatePushNotificationContentBuilder = visitorStatusUpdatePushNotificationContentBuilder;
	}

	public void sendVisitorStatusUpdateNotificationsAsync(int instituteId, UUID visitorId, int academicSessionId, UUID userId) {
		Thread t = new Thread(new Runnable() {
			@Override
			public void run() {
				sendVisitorStatusUpdateNotifications(instituteId, visitorId, academicSessionId, userId);
			}
		});
		t.start();
	}

	public void sendVisitorStatusUpdateNotifications(int instituteId, UUID visitorId, int academicSessionId, UUID userId) {
		if (instituteId <= 0) {
			logger.error("Invalid institute {} ", instituteId);
			return;
		}


		List<BellNotificationPayload> bellNotificationPayloadList = new ArrayList<>();

		VisitorDetails visitorDetails = visitorDetailsManager.getVisitorDetailsByVisitorId(instituteId, academicSessionId, visitorId);
		if (visitorDetails == null) {
			return;
		}
		/**
		 * admin notification
		 */
		sendAdminStatusUpdateNotifications(instituteId, userId, visitorId, visitorDetails, bellNotificationPayloadList);

		if (CollectionUtils.isEmpty(bellNotificationPayloadList)) {
			return;
		}

		boolean notificationAdded = pushNotificationManager.addBulkNotification(bellNotificationPayloadList);

		if (!notificationAdded) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_BELL_NOTIFICATION_DETAILS,
							"Invalid bell notification details."));
		}
	}

	private void sendAdminStatusUpdateNotifications(int instituteId, UUID userId, UUID visitorId, VisitorDetails visitorDetails, List<BellNotificationPayload> bellNotificationPayloadList) {
		Set<User> finalUserSet = new HashSet<>();
		if(visitorDetails.getStatus() == VisitorStatus.STAFF_APPROVAL_PENDING) {
			User raisedFor = userManager.getUser(visitorDetails.getStaff().getStaffId());
			if (raisedFor != null) {
				finalUserSet.add(raisedFor);
			}
		}
		if(visitorDetails.getStatus() == VisitorStatus.APPROVED || visitorDetails.getStatus() == VisitorStatus.REJECTED){
			List<User> adminUserList = userPermissionManager.getUserByUserPermission(instituteId, new HashSet<>(Arrays.asList(AuthorisationRequiredAction.ALL_NOTIFICATION_PERMISSION)));
			for (User user : adminUserList) {
				if (!user.getUserStatus().equals(UserStatus.ENABLED)) {
					continue;
				}
				if (!user.getUuid().equals(userId)) {
					finalUserSet.add(user);
				}
			}
		}


		if (CollectionUtils.isEmpty(finalUserSet)) {
			return;
		}

		Map<String, String> metaData = new HashMap<String, String>();
		metaData.put(VISITOR_ID, visitorId == null ? null : visitorId.toString());

		PushNotificationContent adminPushNotificationContent = visitorStatusUpdatePushNotificationContentBuilder.getPushNotificationContent();
		bellNotificationPayloadList.add(new BellNotificationPayload(
				instituteId, new ArrayList<>(finalUserSet), adminPushNotificationContent.getTitle(), adminPushNotificationContent.getBody(),
				NotificationEntity.VISITOR_APPOINTMENT_STATUS_UPDATE, visitorId, metaData));

		UUID finalVisitorId = visitorId;
		prepareAndSendBulkNotificationAsync(instituteId, new ArrayList<>(finalUserSet), adminPushNotificationContent, new IUpdateNotificationPayload() {
			@Override
			public void update(Map<String, String> dataPayload, User user) {
				dataPayload.put(PushNotificationManager.CLICK_ACTION, ADMIN_VISITOR_APPOINTMENT_STATUS_UPDATE);
				dataPayload.put(VISITOR_ID, finalVisitorId == null ? "" : finalVisitorId.toString());
			}
		});


	}
}
