package com.lernen.cloud.dev.tools.templates;

import java.io.IOException;
import java.nio.charset.Charset;

import org.apache.commons.io.IOUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import com.lernen.cloud.core.api.common.DeliveryMode;
import com.embrate.cloud.core.api.service.communication.templates.TemplateType;
import com.lernen.cloud.core.lib.templates.VelocityTemplateVersionManager;
import com.lernen.cloud.core.utils.spring.SpringAppContextProvider;

/**
 * 
 * <AUTHOR>
 *
 */
public class TemplateVersionGenerator {

	final VelocityTemplateVersionManager velocityTemplateVersionManager;

	public TemplateVersionGenerator(VelocityTemplateVersionManager velocityTemplateVersionManager) {
		this.velocityTemplateVersionManager = velocityTemplateVersionManager;
	}

	public static void main(String[] args) throws IOException {
		final ApplicationContext context = SpringAppContextProvider.getApplicationContext("dev-tools.xml");
		final TemplateVersionGenerator versionGenerator = context.getBean(TemplateVersionGenerator.class);
		versionGenerator.generateVersions();
	}

	protected void generateVersions() throws IOException {

		final PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
		final Resource[] templates = resolver.getResources("classpath*:templates/**/*.vm");
		for (final Resource resource : templates) {
			final String fileLocation = resource.getURI().toString();
			final String[] fileNameSplits = fileLocation.split("/");
			final String templateName = fileNameSplits[fileNameSplits.length - 1];
			final String templateType = fileNameSplits[fileNameSplits.length - 2];
			final String templateLocale = fileNameSplits[fileNameSplits.length - 3];
			final String templateEntityID = fileNameSplits[fileNameSplits.length - 4];
			final String deliveryModeString = fileNameSplits[fileNameSplits.length - 5];
			try {
				final DeliveryMode deliveryMode = DeliveryMode.valueOf(deliveryModeString);
				
				final String emailTemplate = IOUtils.toString(resource.getInputStream(), Charset.defaultCharset());
				final boolean updated = velocityTemplateVersionManager.updateTemplates(TemplateType.valueOf(templateType), templateName,
						templateEntityID, templateLocale, emailTemplate, deliveryMode);
				if (updated) {
					System.out.println("Updated Template Successfully!!! -  " + fileLocation);
				} else {
					System.out.println("Already Upto date !!! -  " + fileLocation);
				}
			} catch (final Exception e) {
				System.err.println("No Delivery mode is present for template : " + fileLocation);
			}

		}
		System.out.println("Script ran successfully!!!");
	}
}
