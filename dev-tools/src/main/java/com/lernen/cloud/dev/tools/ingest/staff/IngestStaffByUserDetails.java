/**
 * 
 */
package com.lernen.cloud.dev.tools.ingest.staff;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

import org.apache.commons.cli.CommandLine;
import org.apache.commons.cli.CommandLineParser;
import org.apache.commons.cli.DefaultParser;
import org.apache.commons.cli.HelpFormatter;
import org.apache.commons.cli.Options;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.ApplicationContext;

import com.lernen.cloud.core.api.staff.RegisterStaffPayload;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.staff.StaffBasicInfo;
import com.lernen.cloud.core.api.staff.StaffCategory;
import com.lernen.cloud.core.api.staff.StaffDepartment;
import com.lernen.cloud.core.api.staff.StaffDesignation;
import com.lernen.cloud.core.api.staff.StaffStatus;
import com.lernen.cloud.core.api.user.Gender;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.lib.managers.UserManager;
import com.lernen.cloud.core.lib.staff.StaffManager;
import com.lernen.cloud.core.utils.spring.SpringAppContextProvider;

/**
 * <AUTHOR>
 *
 */
public class IngestStaffByUserDetails {
	
	private static final Logger logger = LogManager.getLogger(IngestStaffByUserDetails.class);

	private static final String INSTITUTE_ID = "i";
	private static final String USER_ID = "ui";

	private final StaffManager staffManager;
	private final UserManager userManager;

	public IngestStaffByUserDetails(StaffManager staffManager, UserManager userManager) {
		this.staffManager = staffManager;
		this.userManager = userManager;
	}

	public static void main(String args[]) {
		final ApplicationContext context = SpringAppContextProvider.getApplicationContext("dev-tools.xml");
		final IngestStaffByUserDetails ingestStaffByUserDetails = context.getBean(IngestStaffByUserDetails.class);
		Options options = buildOptions();
		CommandLine cmdLine = null;
		try {
			final CommandLineParser parser = new DefaultParser();
			cmdLine = parser.parse(options, args);
		} catch (final Exception pex) {
			logger.error("Unable to parse arguments.");
			pex.printStackTrace();
			final HelpFormatter formatter = new HelpFormatter();
			formatter.printHelp("UserCreation ", options);
			return;
		}

		if (cmdLine == null) {
			logger.error("Unable to parse arguments from cmdline.");
			return;
		}

		int instituteId = 0;
		if (cmdLine.hasOption(INSTITUTE_ID)) {
			instituteId = Integer.parseInt(cmdLine.getOptionValue(INSTITUTE_ID));
		} else {
			logger.error("No institute id passed. Exitting");
			return;
		}

		UUID userId = null;
		if (cmdLine.hasOption(USER_ID)) {
			userId = UUID.fromString(cmdLine.getOptionValue(USER_ID));
		} else {
			logger.error("No userId passed. Exitting");
			return;
		}
		
		ingestStaffByUserDetails.run(instituteId, userId);
	}

	private void run(int instituteId, UUID userId) {
		
		UUID staffCategoryId = staffManager.addStaffCategory(new StaffCategory(instituteId, null, "General Staff"), userId);
		UUID staffDepartmentId = staffManager.addStaffDepartment(new StaffDepartment(instituteId, null, "Staff"), userId);
		UUID staffDesignationId = staffManager.addStaffDesignation(new StaffDesignation(instituteId, null, "Staff"), userId);
		HashMap<UUID, UUID> departmentDesignationMapping = new HashMap<UUID, UUID>();
		departmentDesignationMapping.put(staffDepartmentId, staffDesignationId);
		
		List<UserType> userTypeList = new ArrayList<UserType>();
		userTypeList.add(UserType.ADMIN);
		userTypeList.add(UserType.FACULTY);
		userTypeList.add(UserType.STAFF);		
		List<User> userList = userManager.getUserByUserType(instituteId, userTypeList);
		
		int successCount = 0;
		int failureCount = 0;
		int exceptionCount = 0;
		int alreadyExistsCount = 0;
		int count = 0;
		int staffInstiId = 1;
		for (User user : userList) {
			logger.info("Count {}", ++count);
			
			try {
				logger.info("Creating staff for user {}", user.getUuid());
				
				UUID staffUserId = user.getUuid();
				Staff staff = staffManager.getStaff(staffUserId);
				if(staff == null) {
					String staffInstituteId = "S" + String.valueOf(staffInstiId);
					String name = user.getFirstName() + (user.getLastName() == null ? "" : " "+user.getLastName());
					Gender gender = Gender.MALE;
					String primaryContactNumber = user.getPhoneNumber() == null ? "" : user.getPhoneNumber();
					String primaryEmail = user.getEmail() == null ? "" : user.getEmail();
					
					StaffBasicInfo staffBasicInfo = new StaffBasicInfo(staffInstituteId, name, null, gender, null,
							staffCategoryId, departmentDesignationMapping, null, null, null, null, null, null,
							null, null, null, null, primaryContactNumber, null, primaryEmail, null, null, null, null);
					
					final RegisterStaffPayload registerStaffPaylaod = new RegisterStaffPayload(instituteId, staffUserId,
							null, null, StaffStatus.ONBOARD, staffBasicInfo, null, null, null, null, null, null, false, null);
					
					final UUID staffId = staffManager.addStaff(registerStaffPaylaod, instituteId, null, null, true);
					if (staffId == null) {
						logger.error("{} failed in creation", user.getUuid());
						failureCount++;
					} else {
						logger.error("{} successful in creation", user.getUuid());
						staffInstiId++;
						successCount++;
					}
				} else {
					logger.error("{} already exists", user.getUuid());
					alreadyExistsCount++;
				}
				
			} catch (Exception e) {
				logger.error("Error while creating user {}", user.getUuid(), e);
				exceptionCount++;
			}
			
		}
		
		logger.info("Total success users for institute {} = {}", instituteId, successCount);
		logger.info("Total failure users for institute {} = {}", instituteId, failureCount);
		logger.info("Total exception users for institute {} = {}", instituteId, exceptionCount);
		logger.info("Total already exists users for institute {} = {}", instituteId, alreadyExistsCount);
	}

	private static Options buildOptions() {
		final Options options = new Options();
		options.addOption(INSTITUTE_ID, true, "specify the institute id");
		options.addOption(USER_ID, true, "user id of user for institute");
		return options;
	}

}
