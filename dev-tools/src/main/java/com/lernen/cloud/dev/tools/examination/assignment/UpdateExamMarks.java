package com.lernen.cloud.dev.tools.examination.assignment;

import java.io.BufferedReader;
import java.io.FileReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.UUID;

import org.apache.commons.cli.CommandLine;
import org.apache.commons.cli.CommandLineParser;
import org.apache.commons.cli.DefaultParser;
import org.apache.commons.cli.HelpFormatter;
import org.apache.commons.cli.Options;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.ApplicationContext;
import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.course.Course;
import com.lernen.cloud.core.api.examination.ExamDimension;
import com.lernen.cloud.core.api.examination.ExamDimensionObtainedValues;
import com.lernen.cloud.core.api.examination.ExamNode;
import com.lernen.cloud.core.api.examination.MarksFeedData;
import com.lernen.cloud.core.api.examination.MarksFeedStatus;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.lib.examination.ExaminationManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.spring.SpringAppContextProvider;

/**
 * 
 * <AUTHOR>
 *
 */
public class UpdateExamMarks {
	private static final Logger logger = LogManager.getLogger(UpdateExamMarks.class);

	private static final String INSTITUTE_ID = "i";
	private static final String ACADEMIC_SESSION_ID = "s";
	private static final String FILE_PATH = "f";
	private static final String CLASS_NAME = "c";
	private static final String DIMENSION_ID = "d";
	private static final String ORAL_DIMENSION_ID = "o";
	private static final String USER_ID = "u";
	private static final String UPDATE = "um";
	private static final String FILE_DELIMITER = "\\|";

	private static final Map<String, String> COURSE_MAP = new HashMap<String, String>();
	private static final Map<String, Boolean> SUBJECT_MAP = new HashMap<String, Boolean>();

	static {
		COURSE_MAP.put("Hindi".toLowerCase(), "Hindi");
		COURSE_MAP.put("Eng.".toLowerCase(), "English");
		COURSE_MAP.put("Eng".toLowerCase(), "English");
		COURSE_MAP.put("Math".toLowerCase(), "Math");
		COURSE_MAP.put("e.v.s".toLowerCase(), "E.V.S");
		COURSE_MAP.put("g.k".toLowerCase(), "General Knowledge");
		COURSE_MAP.put("Dra.".toLowerCase(), "Drawing");
		COURSE_MAP.put("Draw".toLowerCase(), "Drawing");
		COURSE_MAP.put("Com.".toLowerCase(), "Computer");
		COURSE_MAP.put("Sans.".toLowerCase(), "Sanskrit");
		COURSE_MAP.put("Sans".toLowerCase(), "Sanskrit");
		COURSE_MAP.put("conv.".toLowerCase(), "Conversation");
		COURSE_MAP.put("sc.".toLowerCase(), "Science");
		COURSE_MAP.put("so.sc".toLowerCase(), "Social Science");

	}

	static {
		SUBJECT_MAP.put("Hindi Oral".toLowerCase(), true);
		SUBJECT_MAP.put("English Oral".toLowerCase(), true);
		SUBJECT_MAP.put("Math Oral".toLowerCase(), true);
		SUBJECT_MAP.put("Conversation".toLowerCase(), true);
	}

	private final ExaminationManager examinationManager;
	private final StudentManager studentManager;
	private final InstituteManager instituteManager;

	public UpdateExamMarks(ExaminationManager examinationManager, StudentManager studentManager,
			InstituteManager instituteManager) {
		this.examinationManager = examinationManager;
		this.studentManager = studentManager;
		this.instituteManager = instituteManager;
	}

	private static Options buildOptions() {
		final Options options = new Options();
		options.addOption(INSTITUTE_ID, true, "specify the institute id");
		options.addOption(ACADEMIC_SESSION_ID, true, "Academic session id for institute");
		options.addOption(FILE_PATH, true, "Examination report card variables assignment csv file path");
		options.addOption(CLASS_NAME, true, "Class name");
		options.addOption(DIMENSION_ID, true, "Dimension Id");
		options.addOption(USER_ID, true, "User Id");
		options.addOption(ORAL_DIMENSION_ID, true, "Update the assignments");
		options.addOption(UPDATE, false, "Update the assignments");
		return options;
	}

	public static void main(String args[]) {
		final ApplicationContext context = SpringAppContextProvider.getApplicationContext("dev-tools.xml");
		final UpdateExamMarks updateExamMarks = context.getBean(UpdateExamMarks.class);
		Options options = buildOptions();
		CommandLine cmdLine = null;
		try {
			final CommandLineParser parser = new DefaultParser();
			cmdLine = parser.parse(options, args);
		} catch (final Exception pex) {
			logger.error("Unable to parse arguments.", pex);
			final HelpFormatter formatter = new HelpFormatter();
			formatter.printHelp("ReportCardVariableAssignmentCreator ", options);
			return;
		}

		if (cmdLine == null) {
			logger.error("Unable to parse arguments from cmdline.");
			return;
		}

		int instituteId = 0;
		if (cmdLine.hasOption(INSTITUTE_ID)) {
			instituteId = Integer.parseInt(cmdLine.getOptionValue(INSTITUTE_ID));
		} else {
			logger.error("No institute id passed. Exitting");
			return;
		}
		int academicSessionId = 0;
		if (cmdLine.hasOption(ACADEMIC_SESSION_ID)) {
			academicSessionId = Integer.parseInt(cmdLine.getOptionValue(ACADEMIC_SESSION_ID));
		} else {
			logger.error("No academicSessionId passed. Exitting");
			return;
		}

		String className = null;
		if (cmdLine.hasOption(CLASS_NAME)) {
			className = cmdLine.getOptionValue(CLASS_NAME);
		} else {
			logger.error("No class passed. Exitting");
			return;
		}

		int dimensionId = 0;
		if (cmdLine.hasOption(DIMENSION_ID)) {
			dimensionId = Integer.parseInt(cmdLine.getOptionValue(DIMENSION_ID));
		} else {
			logger.error("No dimensionId passed. Exitting");
			return;
		}

		Integer oralDimensionId = null;
		if (cmdLine.hasOption(ORAL_DIMENSION_ID)) {
			oralDimensionId = Integer.parseInt(cmdLine.getOptionValue(ORAL_DIMENSION_ID));
		}

		UUID userId = null;
		if (cmdLine.hasOption(USER_ID)) {
			userId = UUID.fromString(cmdLine.getOptionValue(USER_ID));
		} else {
			logger.error("No userId passed. Exitting");
			return;
		}

		String filePath = null;
		if (cmdLine.hasOption(FILE_PATH)) {
			filePath = cmdLine.getOptionValue(FILE_PATH);
		} else {
			logger.error("No file path passed. Exitting");
			return;
		}

		boolean updateAssignment = false;
		if (cmdLine.hasOption(UPDATE)) {
			updateAssignment = true;
		}

		logger.info(
				"Running for institute {}, academicSessionId {}, className {}, dimensionId {}, userId {}, filePath {} , updateAssignment {} oralDimensionId {}",
				instituteId, academicSessionId, className, dimensionId, userId, filePath, updateAssignment,
				oralDimensionId);
		updateExamMarks.run(instituteId, academicSessionId, className, dimensionId, userId, filePath, updateAssignment,
				oralDimensionId);
	}

	public void run(int instituteId, int academicSessionId, String className, int dimensionId, UUID userId,
			String filePath, boolean updateAssignment, Integer oralDimensionId) {
		try {
			List<MarksFeedData> marksFeedDataList = getPayloadFromFile(instituteId, academicSessionId, className,
					dimensionId, filePath, oralDimensionId);
			if (marksFeedDataList == null) {
				logger.error("Null list while getting exam marks payload");
				return;
			}
			logger.info("Total MarksFeedData in file {}", marksFeedDataList.size());
			Map<UUID, Map<UUID, List<MarksFeedData>>> examCourseMap = new HashMap<UUID, Map<UUID, List<MarksFeedData>>>();
			for (MarksFeedData marksFeedData : marksFeedDataList) {
				if (!examCourseMap.containsKey(marksFeedData.getExamId())) {
					examCourseMap.put(marksFeedData.getExamId(), new HashMap<UUID, List<MarksFeedData>>());
				}
				if (!examCourseMap.get(marksFeedData.getExamId()).containsKey(marksFeedData.getCourseId())) {
					examCourseMap.get(marksFeedData.getExamId()).put(marksFeedData.getCourseId(), new ArrayList<>());
				}
				examCourseMap.get(marksFeedData.getExamId()).get(marksFeedData.getCourseId()).add(marksFeedData);
			}
			if (updateAssignment) {
				boolean totalSuccess = true;
				for (Entry<UUID, Map<UUID, List<MarksFeedData>>> entry : examCourseMap.entrySet()) {
					for (Entry<UUID, List<MarksFeedData>> courseEntry : entry.getValue().entrySet()) {
						boolean success = examinationManager.feedMarks(instituteId, 0, courseEntry.getValue(),
								MarksFeedStatus.SAVED, null, null, userId);
						totalSuccess &= success;
					}
				}

				if (totalSuccess) {
					logger.info("Successfully updated all the exam marks");
				} else {
					logger.error("Error while updating all the exam marks");
				}
			}

		} catch (Exception e) {
			logger.error("Error while getting exam marks payload", e);
		}
	}

	private List<MarksFeedData> getPayloadFromFile(int instituteId, int academicSessionId, String className,
			int dimensionId, String fileName, Integer oralDimensionId) {
		List<String> examList = new ArrayList<>();
		List<String> subjectList = new ArrayList<>();
		Map<String, String[]> studentMarks = new HashMap<>();
		int rowCount = -1;
		try (final BufferedReader br = new BufferedReader(new FileReader(fileName))) {
			String line = null;
			while ((line = br.readLine()) != null) {
				if (StringUtils.isBlank(line)) {
					continue;
				}
				String[] columns = line.split(FILE_DELIMITER, -1);
				rowCount++;

				if (rowCount == 0) {
					String latestExam = "";
					for (String col : columns) {
						if (StringUtils.isNotBlank(col)) {
							latestExam = col.trim().toLowerCase();
						}
						examList.add(latestExam);
					}
				} else if (rowCount == 1) {
					for (String col : columns) {
						subjectList.add(col.trim().toLowerCase());
					}
				} else if (rowCount == 2) {
					// skip
				} else {
					String admissionNumber = columns[1].trim();
					if (StringUtils.isNotBlank(admissionNumber)) {
						studentMarks.put(admissionNumber, columns);
					}
				}
			}
			return getMarksFeedData(instituteId, academicSessionId, className, dimensionId, examList, subjectList,
					studentMarks, oralDimensionId);
		} catch (final Exception e) {
			logger.error("Error while reading file", e);
		}
		return null;
	}

	private List<MarksFeedData> getMarksFeedData(int instituteId, int academicSessionId, String className,
			int dimensionId, List<String> examList, List<String> subjectList, Map<String, String[]> studentMarks,
			Integer oralDimensionId) {

		Standard standard = getStandard(instituteId, academicSessionId, className);
		if (standard == null) {
			logger.error("Invalid standard name {}. Skipping.", className);
			return null;
		}
		Map<String, ExamNode> examNameMap = getExams(instituteId, standard.getStandardId(), academicSessionId);
		Map<UUID, Map<String, Course>> examCourseMap = getExamCourseMap(examNameMap);

		Map<String, Student> studentMap = getStudents(instituteId, academicSessionId, standard.getStandardId());

		logger.info("examNameMap  {}", examNameMap);
		logger.info("examCourseMap  {}", examCourseMap);
		logger.info("examList  {}", examList);
		logger.info("subjectList  {}", subjectList);

		List<MarksFeedData> marksFeedDatas = new ArrayList<MarksFeedData>();
		for (Entry<String, String[]> studentEntry : studentMarks.entrySet()) {
			String admissionNumber = studentEntry.getKey();
			if (!studentMap.containsKey(admissionNumber)) {
				logger.error("Invalid admission number {}. Skipping.", admissionNumber);
				return null;
			}

			Student student = studentMap.get(admissionNumber);

			for (int i = 4; i < studentEntry.getValue().length; i++) {
				String value = studentEntry.getValue()[i];
				String exam = examList.get(i);
				String inputCourseName = subjectList.get(i);

				if (StringUtils.isBlank(value) || value.trim().toLowerCase().equalsIgnoreCase("A".toLowerCase())) {
					continue;
				}

				try {
					Double.parseDouble(value);
				} catch (Exception e) {
					continue;
				}
				if (StringUtils.isNotBlank(exam) && StringUtils.isNotBlank(inputCourseName)) {
					ExamNode examNode = examNameMap.get(exam);
					if (examNode == null) {
						logger.error("Invalid exam {}. Skipping.", exam);
						throw new EmbrateRunTimeException("Invalid exam");
					}
					String courseName = getCourseName(inputCourseName);
					if (StringUtils.isBlank(courseName)) {
						logger.error("Invalid course input {} . Skipping.", inputCourseName);
						throw new EmbrateRunTimeException("Invalid course mapping");
					}
					Course course = examCourseMap.get(examNode.getExamMetaData().getExamId())
							.get(courseName.toLowerCase());
					if (course == null) {
						logger.error("Invalid course input {}, mapped course {} . Skipping.", inputCourseName,
								courseName);
						throw new EmbrateRunTimeException("Invalid course");
					}
					MarksFeedData marksFeedData = new MarksFeedData();
					marksFeedData.setStudentId(student.getStudentId());
					marksFeedData.setExamId(examNode.getExamMetaData().getExamId());
					marksFeedData.setCourseId(course.getCourseId());
					ExamDimensionObtainedValues examDimensionObtainedValues = null;
					if (oralDimensionId == null) {
						examDimensionObtainedValues = new ExamDimensionObtainedValues(
								new ExamDimension(dimensionId, null, null, null, false));
					} else {
						if (SUBJECT_MAP.containsKey(courseName.toLowerCase())) {
							examDimensionObtainedValues = new ExamDimensionObtainedValues(
									new ExamDimension(oralDimensionId, null, null, null, false));
						} else {
							examDimensionObtainedValues = new ExamDimensionObtainedValues(
									new ExamDimension(dimensionId, null, null, null, false));
						}
					}

					examDimensionObtainedValues.setObtainedMarks(Double.parseDouble(value));
					marksFeedData.setExamDimensionObtainedValues(Arrays.asList(examDimensionObtainedValues));

					marksFeedDatas.add(marksFeedData);
				}

			}
		}
		return marksFeedDatas;
	}

	private String getCourseName(String inputCourseName) {
//		return COURSE_MAP.get(inputCourseName.toLowerCase());
		return inputCourseName.toLowerCase();
	}

	private Map<String, Student> getStudents(int instituteId, int academicSessionId, UUID standardId) {
		List<Student> students = studentManager.getClassStudents(instituteId, academicSessionId, standardId);
		Map<String, Student> studentMap = new HashMap<String, Student>();
		for (Student student : students) {
			studentMap.put(student.getStudentBasicInfo().getAdmissionNumber(), student);
		}
		return studentMap;
	}

	private Standard getStandard(int instituteId, int academicSessionId, String className) {
		List<Standard> standards = instituteManager.getInstituteStandardList(instituteId, academicSessionId);
		for (Standard standard : standards) {
			if (standard.getStandardName().trim().toLowerCase().equalsIgnoreCase(className.trim().toLowerCase())) {
				return standard;
			}
		}
		return null;
	}

	private Map<String, ExamNode> getExams(int instituteId, UUID standardId, int academicSessionId) {
		List<ExamNode> examNodes = examinationManager.getClassExamsForest(standardId, academicSessionId, instituteId,
				true);
		List<ExamNode> allExams = new ArrayList<>();
		populateExams(examNodes.get(0), allExams);

		Map<String, ExamNode> examNameMap = new HashMap<String, ExamNode>();
		for (ExamNode examNode : allExams) {
			examNameMap.put(examNode.getExamMetaData().getExamName().toLowerCase(), examNode);
		}

		return examNameMap;
	}

	private Map<UUID, Map<String, Course>> getExamCourseMap(Map<String, ExamNode> examNameMap) {
		Map<UUID, Map<String, Course>> examCourseMap = new HashMap<UUID, Map<String, Course>>();
		for (Entry<String, ExamNode> entry : examNameMap.entrySet()) {
			ExamNode examNode = entry.getValue();
			Map<String, Course> courseMap = new HashMap<String, Course>();
			for (Course course : examNode.getCourses()) {
				courseMap.put(course.getCourseName().trim().toLowerCase(), course);
			}
			examCourseMap.put(examNode.getExamMetaData().getExamId(), courseMap);
		}
		return examCourseMap;
	}

	private void populateExams(ExamNode root, List<ExamNode> allExams) {
		allExams.add(root);
		if (CollectionUtils.isEmpty(root.getChildren())) {
			return;
		}
		for (ExamNode examNode : root.getChildren()) {
			populateExams(examNode, allExams);
		}
	}

}
