package com.lernen.cloud.dev.tools.ingest.student;

import java.io.BufferedReader;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import com.lernen.cloud.core.lib.student.StudentAdmissionManager;
import org.apache.commons.cli.CommandLine;
import org.apache.commons.cli.CommandLineParser;
import org.apache.commons.cli.DefaultParser;
import org.apache.commons.cli.HelpFormatter;
import org.apache.commons.cli.Option;
import org.apache.commons.cli.Options;
import org.apache.commons.cli.ParseException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.institute.Stream;
import com.lernen.cloud.core.api.student.StudentBasicInfo;
import com.lernen.cloud.core.api.student.StudentFamilyInfo;
import com.lernen.cloud.core.api.student.StudentPayload;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserCategory;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.Pair;
import com.lernen.cloud.core.utils.spring.SpringAppContextProvider;

public class IngestRaghunathStudents {

	private static final String FILE_PATH = "f";
	private static final String CURRENT_ACADEMIC_SESSION_ID = "cs";
	private static final String CSV_DELIMITER = "\\|";
	private static final String SPACE_DELIMITER = " ";
	public static final String DATE_FORMAT = "dd/MM/yyyy";
	private static final String TEMP_REGISTRATION_NUMBER_SUFFIX = "-TEMP";

	private static final int INSTITUTE_ID = 10015;

	public static final String LKG = "LKG";
	public static final String UKG = "UKG";
	public static final String FIRST = "1";
	public static final String SECOND = "2";
	public static final String THIRD = "3";
	public static final String FOURTH = "4";
	public static final String FIFTH = "5";
	public static final String SIXTH = "6";
	public static final String SEVENTH = "7";
	public static final String EIGHTH = "8";
	public static final String NINTH = "9";
	public static final String TENTH = "10";
	public static final String ELEVENTH = "11";
	public static final String TWELFTH = "12";

	private final InstituteManager instituteManager;
	private final StudentManager studentManager;
	private final StudentAdmissionManager studentAdmissionManager;

	public IngestRaghunathStudents(InstituteManager instituteManager, StudentManager studentManager, StudentAdmissionManager studentAdmissionManager) {
		this.instituteManager = instituteManager;
		this.studentManager = studentManager;
		this.studentAdmissionManager = studentAdmissionManager;
	}

	public static void main(String[] args) {
		final ApplicationContext ctx = SpringAppContextProvider.getApplicationContext("dev-tools.xml");
		final IngestRaghunathStudents ingestRaghunathStudents = ctx.getBean(IngestRaghunathStudents.class);
		final Options options = buildOptions();

		CommandLine cmdLine = null;
		try {
			final CommandLineParser parser = new DefaultParser();
			cmdLine = parser.parse(options, args);
		} catch (final ParseException pex) {
			final HelpFormatter formatter = new HelpFormatter();
			formatter.printHelp("IngestRaghunathStudents", options);
			System.exit(2);
		}

		if (!cmdLine.hasOption(FILE_PATH)) {
			final HelpFormatter formatter = new HelpFormatter();
			formatter.printHelp("File path required", options);
		}
		
		int currentAcademicSessionId = 0;
		if (cmdLine.hasOption(CURRENT_ACADEMIC_SESSION_ID)) {
			currentAcademicSessionId = Integer.parseInt(cmdLine.getOptionValue(CURRENT_ACADEMIC_SESSION_ID));
		} else {
			System.out.println("No current academicSessionId passed. Exitting");
			return;
		}
		
		String filePath = cmdLine.getOptionValue(FILE_PATH);
		System.out.println("Reading student data from file: " + filePath);
		ingestRaghunathStudents.ingestStudents(filePath, currentAcademicSessionId);
	}

	private static Options buildOptions() {
		final Options options = new Options();
		options.addOption(new Option(FILE_PATH, true, "File Path"));
		options.addOption(CURRENT_ACADEMIC_SESSION_ID, true, "Current academic session id for institute");
		return options;
	}

	public boolean ingestStudents(String filePath, int academicSession) {
		System.out.println("Reading student data from file: " + filePath);
		Set<String> classes = new HashSet<>();
		Map<String, Integer> counts = new HashMap<>();
		try (BufferedReader br = new BufferedReader(new FileReader(filePath))) {
			String line = "";
			int count = 0;
			while ((line = br.readLine()) != null) {
				// System.out.println(line);
				String[] params = line.split(CSV_DELIMITER, -1);
				// System.out.println(params.length);
				classes.add(params[6].trim());
				if (counts.get(params[6].trim()) == null) {
					counts.put(params[6].trim(), 1);
				} else {
					counts.put(params[6].trim(), counts.get(params[6].trim()) + 1);
				}
				count++;
				// if (params.length != 10) {
				// System.out.println("Invalid csv format. Expected 10
				// columns");
				// return false;
				// }
				StudentPayload studentPayload = getStudentPayload(params, academicSession);
				if (studentPayload == null) {
					System.out.println("Invalid Student Info. Skipping...");
					continue;
				}
				UUID studentId = studentManager.addStudent(studentPayload);
				if (studentId == null) {
					System.out.println("Unable to create Student. Skipping...");
					continue;
				}
				if(!studentAdmissionManager.admitStudent(INSTITUTE_ID, studentId,
						studentPayload.getStudentBasicInfo().getAdmissionNumber())){
					System.out.println("Unable to admit Student...");
					continue;
				}
				
			}
			System.out.println(counts);
			System.out.println(classes);
			System.out.println(count);
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

	private StudentPayload getStudentPayload(String[] params, int academicSession) {
		StudentPayload studentPayload = new StudentPayload();
		StudentBasicInfo studentBasicInfo = new StudentBasicInfo();
		
		String admissionNumber = params[0];
		if (StringUtils.isBlank(admissionNumber)) {
			System.out.println("Invalid admissionNumber");
			return null;
		}
		studentBasicInfo.setRegistrationNumber(admissionNumber.trim() + TEMP_REGISTRATION_NUMBER_SUFFIX);
		studentBasicInfo.setAdmissionNumber(admissionNumber.trim());

		
		String name = params[1];
		if (StringUtils.isBlank(name)) {
			System.out.println("Invalid name");
			return null;
		}
		
		studentBasicInfo.setName(name.toString().trim());
		System.out.println("Student Name - "+ name);
		
		String admissionDate = params[6];
		if (!StringUtils.isBlank(admissionDate)) {
			int admissionDateTimestamp = DateUtils.getTimestampFromDate(admissionDate.trim(), User.DFAULT_TIMEZONE, DATE_FORMAT);
			studentBasicInfo.setAdmissionDate(admissionDateTimestamp);
		}
		
		String dob = params[2];
		if (!StringUtils.isBlank(dob)) {
			int dobTimestamp = DateUtils.getTimestampFromDate(dob.trim(), User.DFAULT_TIMEZONE, DATE_FORMAT);
			studentBasicInfo.setDateOfBirth(dobTimestamp);
		}
		
		String category = params[8];		
		if (!StringUtils.isBlank(category)) {
			if (category.equalsIgnoreCase("gen")) {
				studentBasicInfo.setUserCategory(UserCategory.GENERAL);
			} else if (category.equalsIgnoreCase("obc")) {
				studentBasicInfo.setUserCategory(UserCategory.OBC);;
			} else if (category.equalsIgnoreCase("sc")) {
				studentBasicInfo.setUserCategory(UserCategory.SC);;
			} else if (category.equalsIgnoreCase("st")) {
				studentBasicInfo.setUserCategory(UserCategory.ST);;
			} else if (category.equalsIgnoreCase("min")) {
				studentBasicInfo.setUserCategory(UserCategory.MIN);;
			} else if (category.equalsIgnoreCase("sbc")) {
				studentBasicInfo.setUserCategory(UserCategory.SBC);;
			} else {
				studentBasicInfo.setUserCategory(UserCategory.OTHER);;
			}
		}
		
		String primaryContactNumber = params[7];
		if("NO".equals(primaryContactNumber) || StringUtils.isBlank(primaryContactNumber)) {
			studentBasicInfo.setPrimaryContactNumber(null);
		} else {
			studentBasicInfo.setPrimaryContactNumber(primaryContactNumber);
		}
		
		String religion = params[9];
		if (!StringUtils.isBlank(religion)) {
			studentBasicInfo.setReligion(StringUtils.capitalize(religion.trim()));
		}
		

		StudentFamilyInfo studentFamilyInfo = new StudentFamilyInfo();
		String motherName = params[4];
		String fatherName = params[3];

		if (!StringUtils.isBlank(motherName)) {
			studentFamilyInfo.setMothersName(motherName);
		}

		if (!StringUtils.isBlank(fatherName)) {
			studentFamilyInfo.setFathersName(fatherName);
		}

		String className = params[5];
		if (StringUtils.isBlank(className)) {
			System.out.println("Invalid standard");
			return null;
		}
		Pair<Standard, String> standardPair = getStandardId(className.trim(), academicSession);
		if (standardPair == null) {
			System.out.println("No standard with given name");
			return null;
		}
		Integer sectionId = null;
		if (StringUtils.isNotBlank(standardPair.getSecond())) {
			for (StandardSections standardSection : standardPair.getFirst().getStandardSectionList()) {
				if (standardSection.getSectionName().equals(standardPair.getSecond())) {
					sectionId = standardSection.getSectionId();
					break;
				}
			}
		}

		studentPayload.setStudentBasicInfo(studentBasicInfo);
		studentPayload.setStudentFamilyInfo(studentFamilyInfo);
		studentPayload.setStandardId(standardPair.getFirst().getStandardId());
		studentPayload.setSectionId(sectionId);
		studentPayload.setAdmissionAcademicSession(academicSession);
		studentPayload.setInstituteId(INSTITUTE_ID);
		return studentPayload;
	}

	private Pair<Standard, String> getStandardId(String className, int academicSession) {
		List<Standard> standards = instituteManager.getInstituteStandardList(INSTITUTE_ID, academicSession);
		if (CollectionUtils.isEmpty(standards)) {
			System.out.println("Invalid standards configured");
		}

		Map<String, Standard> standardNameMap = new HashMap<>();
		for (Standard standard : standards) {
			Stream stream = standard.getStream() == null ? Stream.NA : standard.getStream();
			if (stream == Stream.NA) {
				standardNameMap.put(standard.getStandardName().trim(), standard);
			} else {
				standardNameMap.put(standard.getStandardName().trim() + "|" + stream, standard);
			}
		}

		String[] str = className.split("-");
		String classId = null;
		String stream = null;
		String section = null;
		
		if (str.length == 1) {
			classId = str[0].trim();
		} else if (str.length == 2) {
			classId = str[0].trim();
			section = str[1].trim();
		} else {
			classId = str[0].trim();
			stream = str[1].trim();
			section = str[2].trim();
		}
		
		System.out.println("Class = "+ classId + ", Stream = "+ stream+ ", Section = "+section);
		if (classId.equalsIgnoreCase("LKG")) {
			if (!standardNameMap.containsKey(LKG)) {
				System.out.println("No " + LKG + " standard configured");
				return null;
			}
			return new Pair(standardNameMap.get(LKG), section);
		} else if (classId.equalsIgnoreCase("UKG")) {
			if (!standardNameMap.containsKey(UKG)) {
				System.out.println("No " + UKG + " standard configured");
				return null;
			}
			return new Pair(standardNameMap.get(UKG), section);
		} else if (classId.equalsIgnoreCase("1")) {
			if (!standardNameMap.containsKey(FIRST)) {
				System.out.println("No " + FIRST + " standard configured");
				return null;
			}
			return new Pair(standardNameMap.get(FIRST), section);
		} else if (classId.equalsIgnoreCase("2")) {
			if (!standardNameMap.containsKey(SECOND)) {
				System.out.println("No " + SECOND + " standard configured");
				return null;
			}
			return new Pair(standardNameMap.get(SECOND), section);
		} else if (classId.equalsIgnoreCase("3")) {
			if (!standardNameMap.containsKey(THIRD)) {
				System.out.println("No " + THIRD + " standard configured");
				return null;
			}
			return new Pair(standardNameMap.get(THIRD), section);
		} else if (classId.equalsIgnoreCase("4")) {
			if (!standardNameMap.containsKey(FOURTH)) {
				System.out.println("No " + FOURTH + " standard configured");
				return null;
			}
			return new Pair(standardNameMap.get(FOURTH), section);
		} else if (classId.equalsIgnoreCase("5")) {
			if (!standardNameMap.containsKey(FIFTH)) {
				System.out.println("No " + FIFTH + " standard configured");
				return null;
			}
			return new Pair(standardNameMap.get(FIFTH), section);
		} else if (classId.equalsIgnoreCase("6")) {
			if (!standardNameMap.containsKey(SIXTH)) {
				System.out.println("No " + SIXTH + " standard configured");
				return null;
			}
			return new Pair(standardNameMap.get(SIXTH), section);
		} else if (classId.equalsIgnoreCase("7")) {
			if (!standardNameMap.containsKey(SEVENTH)) {
				System.out.println("No " + SEVENTH + " standard configured");
				return null;
			}
			return new Pair(standardNameMap.get(SEVENTH), section);
		} else if (classId.equalsIgnoreCase("8")) {
			if (!standardNameMap.containsKey(EIGHTH)) {
				System.out.println("No " + EIGHTH + " standard configured");
				return null;
			}
			return new Pair(standardNameMap.get(EIGHTH), section);
		} else if (classId.equalsIgnoreCase("9")) {
			if (!standardNameMap.containsKey(NINTH)) {
				System.out.println("No " + NINTH + " standard configured");
				return null;
			}
			return new Pair(standardNameMap.get(NINTH), section);
		} else if (classId.equalsIgnoreCase("10")) {
			if (!standardNameMap.containsKey(TENTH)) {
				System.out.println("No " + TENTH + " standard configured");
				return null;
			}
			return new Pair(standardNameMap.get(TENTH), section);
		} else if (classId.equalsIgnoreCase("11")) {
			String standardName = ELEVENTH;
			System.out.println("Standard Name - "+ standardName);
			if ("Sci".equalsIgnoreCase(stream)) {
				System.out.println("In loop stream = "+ stream);
				standardName = ELEVENTH + "|" + "SCIENCE";
			} else if ("Com".equalsIgnoreCase(stream)) {
				standardName = ELEVENTH + "|" + "COMMERCE";
			} else if ("Arts".equalsIgnoreCase(stream)) {
				standardName = ELEVENTH + "|" + "ARTS";
			}
			if (!standardNameMap.containsKey(standardName)) {
				System.out.println("Standard name 11th= "+standardNameMap.toString());
				System.out.println("No " + standardName + " standard configured");
				return null;
			}
			return new Pair(standardNameMap.get(standardName), section);
		} else if (classId.equalsIgnoreCase("12")) {
			String standardName = TWELFTH;
			if ("Sci".equalsIgnoreCase(stream)) {
				standardName = TWELFTH + "|" + "SCIENCE";
			} else if ("Com".equalsIgnoreCase(stream)) {
				standardName = TWELFTH + "|" + "COMMERCE";
			} else if ("Arts".equalsIgnoreCase(stream)) {
				standardName = TWELFTH + "|" + "ARTS";
			}
			if (!standardNameMap.containsKey(standardName)) {
				System.out.println("Standard name 12th= "+standardNameMap.toString());
				System.out.println("No " + standardName + " standard configured");
				return null;
			}
			return new Pair(standardNameMap.get(standardName), section);
		}
		return null;
	}

}
