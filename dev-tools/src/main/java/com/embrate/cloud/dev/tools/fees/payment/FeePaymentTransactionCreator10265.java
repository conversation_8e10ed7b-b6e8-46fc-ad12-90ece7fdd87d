package com.embrate.cloud.dev.tools.fees.payment;

import com.lernen.cloud.core.api.common.TransactionMode;
import com.lernen.cloud.core.api.fees.payment.*;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentStatus;
import com.lernen.cloud.core.lib.fees.payment.FeePaymentManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.spring.SpringAppContextProvider;
import org.apache.commons.cli.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.ApplicationContext;
import org.springframework.util.CollectionUtils;

import java.io.BufferedReader;
import java.io.FileReader;
import java.util.*;

/**
 * <AUTHOR>
 */
public class FeePaymentTransactionCreator10265 {

    private static final Logger logger = LogManager.getLogger(FeePaymentTransactionCreator10265.class);

    private static final String INSTITUTE_ID = "i";
    private static final String ACADEMIC_SESSION_ID = "s";
    private static final String USER = "user";
    private static final String FILE_PATH = "f";
    private static final String FILE_HEADER = "h";
    private static final String UPDATE = "u";
    private static final String FILE_DELIMITER = ",";

    private final StudentManager studentManager;
    private final FeePaymentManager feePaymentManager;

    public FeePaymentTransactionCreator10265(StudentManager studentManager, FeePaymentManager feePaymentManager) {
        this.studentManager = studentManager;
        this.feePaymentManager = feePaymentManager;
    }

    public static void main(String args[]) {
        final ApplicationContext context = SpringAppContextProvider.getApplicationContext("dev-tools.xml");
        final StudentManager studentManager = context.getBean(StudentManager.class);
        final FeePaymentManager feePaymentManager = context.getBean(FeePaymentManager.class);
        FeePaymentTransactionCreator10265 feePaymentTransactionCreator10265 = new FeePaymentTransactionCreator10265(
                studentManager, feePaymentManager);
        Options options = buildOptions();
        CommandLine cmdLine = null;
        try {
            final CommandLineParser parser = new DefaultParser();
            cmdLine = parser.parse(options, args);
        } catch (final Exception pex) {
            System.err.print("Unable to parse arguments.");
            pex.printStackTrace();
            final HelpFormatter formatter = new HelpFormatter();
            formatter.printHelp("TransportAssignmentCreator ", options);
            return;
        }

        if (cmdLine == null) {
            logger.error("Unable to parse arguments from cmdline.");
            return;
        }

        int instituteId = 0;
        if (cmdLine.hasOption(INSTITUTE_ID)) {
            instituteId = Integer.parseInt(cmdLine.getOptionValue(INSTITUTE_ID));
        } else {
            logger.error("No institute id passed. Exitting");
            return;
        }
        int academicSessionId = 0;
        if (cmdLine.hasOption(ACADEMIC_SESSION_ID)) {
            academicSessionId = Integer.parseInt(cmdLine.getOptionValue(ACADEMIC_SESSION_ID));
        } else {
            logger.error("No academicSessionId passed. Exitting");
            return;
        }

        UUID userId = null;
        if (cmdLine.hasOption(USER)) {
            userId = UUID.fromString(cmdLine.getOptionValue(USER));
        } else {
            logger.error("No userId passed. Exitting");
            return;
        }

        String filePath = null;
        if (cmdLine.hasOption(FILE_PATH)) {
            filePath = cmdLine.getOptionValue(FILE_PATH);
        } else {
            logger.error("No file path passed. Exitting");
            return;
        }
        boolean fileHeader = false;
        if (cmdLine.hasOption(FILE_HEADER)) {
            fileHeader = true;
        }
        boolean updatePayment = false;
        if (cmdLine.hasOption(UPDATE)) {
            updatePayment = true;
        }
        logger.info("Running for institute = " + instituteId + " , session = " + academicSessionId + ", header = "
                + fileHeader + " , update2 = " + updatePayment);
        feePaymentTransactionCreator10265.run(instituteId, academicSessionId, filePath, fileHeader, updatePayment,
                userId);
    }

    public void run(int instituteId, int academicSessionId, String filePath, boolean fileHeader, boolean updatePayment,
                    UUID userId) {
        List<String> errors = new ArrayList<>();
        List<FileData> fileDataList = readPaymentFile(filePath, fileHeader, errors);
        logger.info("Total records = {}", fileDataList.size());
        int success = 0;
        int failure = 0;
        int noTransactions = 0;
        int noAssignment = 0;
        int count = 1;

        Map<String, Student> studentMap = new HashMap<>();
        List<Student> enrolledStudents = studentManager.getStudentsInAcademicSession(instituteId, academicSessionId,
                Arrays.asList(StudentStatus.ENROLLED, StudentStatus.ENROLMENT_PENDING, StudentStatus.NSO, StudentStatus.RELIEVED));
        for (Student studentResponse : enrolledStudents) {
            studentMap.put(studentResponse.getStudentBasicInfo().getAdmissionNumber().toLowerCase(), studentResponse);
        }

        for (FileData fileData : fileDataList) {
            try {
                logger.info("{} Adding payment for entry {}", count++, fileData);
                Student student = studentMap.get(fileData.getAdmissionNumber().toLowerCase());
                if (student == null) {
                    logger.error("Student not found for entry {}", fileData);
                    errors.add("Student not found for entry : " + fileData);
                    failure++;
                    continue;
                }

                if (fileData.getDiscountAmount() < 0 || fileData.getTotalCollectedAmount() < 0) {
                    logger.error("Invalid discount or paid amount {}", fileData);
                    errors.add("Invalid discount or paid amount : " + fileData);
                    failure++;
                    continue;
                }

                StudentFeesDetails studentFeesDetails = feePaymentManager.getPaymentDetails(instituteId,
                        academicSessionId, student.getStudentId());

                Collections.sort(studentFeesDetails.getFeePaymentDetailsList(), new Comparator<FeePaymentDetails>() {
                    @Override
                    public int compare(FeePaymentDetails o1, FeePaymentDetails o2) {
                        Integer d1 = o1.getFeeConfigurationBasicInfo().getDueDate();
                        Integer d2 = o2.getFeeConfigurationBasicInfo().getDueDate();
                        if (d1 == null && d2 == null) {
                            return 0;
                        }
                        if (d1 == null) {
                            return 1;
                        }
                        if (d2 == null) {
                            return -1;
                        }
                        return d1 - d2;
                    }
                });

                List<FeeIdFeeHeadTransaction> feeIdFeeHeadTransactionList = new ArrayList<FeeIdFeeHeadTransaction>();
                double paidAmount = fileData.getTotalCollectedAmount();
                double discountAmount = fileData.getDiscountAmount();
                for (FeePaymentDetails feePaymentDetails : studentFeesDetails.getFeePaymentDetailsList()) {
                    logger.info("Taking amount in fees {}, {}", feePaymentDetails.getFeeConfigurationBasicInfo().getFeeName(), feePaymentDetails.getFeeConfigurationBasicInfo().getDueDate());
                    UUID feeId = feePaymentDetails.getFeeConfigurationBasicInfo().getFeeId();
                    List<FeeHeadTransactionAmounts> feeHeadTransactionAmountsList = new ArrayList<>();
                    for (FeeHeadPaymentDetails feeHeadPaymentDetails : feePaymentDetails.getFeeHeadPaymentDetails()) {
                        double balanceAmt = feeHeadPaymentDetails.getBalanceAmount();
                        double feeHeadDiscAmt = Math.min(balanceAmt, discountAmount);
                        discountAmount -= feeHeadDiscAmt;
                        balanceAmt -= feeHeadDiscAmt;

                        double feeHeadPaidAmt = Math.min(balanceAmt, paidAmount);
                        paidAmount -= feeHeadPaidAmt;
                        balanceAmt -= feeHeadPaidAmt;

                        if (Double.compare(feeHeadDiscAmt, 0d) == 0 && Double.compare(feeHeadPaidAmt, 0d) == 0) {
                            continue;
                        }

                        FeeHeadTransactionAmounts feeHeadTransactionAmounts = new FeeHeadTransactionAmounts(
                                feeHeadPaymentDetails.getFeeHeadConfiguration().getFeeHeadId(),
                                feeHeadPaidAmt, feeHeadDiscAmt, 0d);
                        feeHeadTransactionAmountsList.add(feeHeadTransactionAmounts);
                    }

                    if(!CollectionUtils.isEmpty(feeHeadTransactionAmountsList)){
                        feeIdFeeHeadTransactionList
                                .add(new FeeIdFeeHeadTransaction(feeId, feeHeadTransactionAmountsList));
                    }
                }

                if (Double.compare(paidAmount, 0d) > 0 || Double.compare(discountAmount, 0d) > 0) {
                    logger.error("Could not assign all paid amount, remaining {} or discount amount, remaining {}", paidAmount, discountAmount);
                    errors.add("Could not assign all paid amount or discount amount : " + fileData);
                    continue;
                }

                if (CollectionUtils.isEmpty(feeIdFeeHeadTransactionList)) {
                    logger.info("No fees transaction for student {}, {}", student.getStudentId(), fileData);
                    errors.add("No fees transaction for : " + fileData);
                    noTransactions++;
                    continue;
                }

                FeePaymentTransactionMetaData feePaymentTransactionMetaData = new FeePaymentTransactionMetaData();
                feePaymentTransactionMetaData.setInstituteId(instituteId);
                feePaymentTransactionMetaData.setAcademicSessionId(academicSessionId);
                feePaymentTransactionMetaData.setStudentId(student.getStudentId());
                feePaymentTransactionMetaData.setFeePaymentTransactionStatus(FeePaymentTransactionStatus.ACTIVE);
                feePaymentTransactionMetaData.setTransactionDate(fileData.getTransactionDate());
                feePaymentTransactionMetaData.setTransactionMode(fileData.getPaymentMode());
                feePaymentTransactionMetaData.setInvoiceId(fileData.getInvoiceId());

                FeePaymentPayload feePaymentPayload = new FeePaymentPayload(feePaymentTransactionMetaData,
                        feeIdFeeHeadTransactionList);
                logger.info("Fee payment payload {}", feePaymentPayload);

                if (!updatePayment) {
                    logger.info("Skipping update Fee payment payload {}", feePaymentPayload);
                    continue;
                }

                FeePaymentResponse feePaymentResponse = feePaymentManager.addFeePayment(feePaymentPayload, false,
                        userId, true);
                if (feePaymentResponse == null) {
                    logger.error("Unable to add payment for student {} ", student.getStudentId());
                    errors.add("Unable to add payment for : " + fileData);
                    failure++;
                } else {
                    logger.info("Successfully added payment for student {}, {}", student.getStudentId(), fileData);
                    success++;
                }
            } catch (Exception e) {
                logger.error("Error while adding payment for entry {}", fileData, e);
                failure++;
            }
        }

        for(String error : errors){
            logger.info("Error : {}", error);
        }

        logger.info("Total errors = {}", errors.size());
        logger.info("Total payment entries {}, success {}, failure {}, no transactions {}, noAssignment {}", count - 1,
                success, failure, noTransactions, noAssignment);
    }

    private List<FileData> readPaymentFile(String fileName, boolean header, List<String> errors) {
        List<FileData> fileDataList = new ArrayList<>();
        try (final BufferedReader br = new BufferedReader(new FileReader(fileName))) {
            String line = null;
            while ((line = br.readLine()) != null) {
                if (header) {
                    header = false;
                    continue;
                }
                if (StringUtils.isBlank(line)) {
                    continue;
                }
                String[] columns = line.split(FILE_DELIMITER, 6);
                if (columns.length < 6) {
                    logger.error("Columns are not " + 6 + ". Skipping it as its not for assignment. Entry = " + line);
                    logger.error("Columns are not 6 :" + line);
                    continue;
                }

                try {
                    String dateStr = columns[0].trim();
                    String admissionNumber = columns[1].trim();
                    String discountAmountStr = columns[2].trim();
                    String mode = columns[3].trim();
                    String amtReceived = columns[4].trim();
                    String invoiceId = columns[5].trim();

                    double discountAmount = 0d;
                    if (!StringUtils.isEmpty(discountAmountStr)) {
                        discountAmount = Double.parseDouble(discountAmountStr);
                    }

                    double paidAmount = 0d;
                    if (!StringUtils.isEmpty(amtReceived)) {
                        paidAmount = Double.parseDouble(amtReceived);
                    }

                    // 2 formats are mixed in file 15-07-2023 & 4/10/2023
                    int transactionDate = 0;
                    if (dateStr.contains("/")) {
                        transactionDate = DateUtils.getTimestampFromDate(dateStr, DateUtils.DEFAULT_TIMEZONE, "dd/MM/yyyy");
                    } else {
                        transactionDate = DateUtils.getTimestampFromDate(dateStr, DateUtils.DEFAULT_TIMEZONE, "dd-MM-yyyy");
                    }

                    TransactionMode transactionMode = TransactionMode.getTransactionMode(mode);


                    fileDataList.add(new FileData(admissionNumber, transactionDate, paidAmount, discountAmount, transactionMode, invoiceId));
                } catch (Exception e) {
                    logger.error("Error while reading line {}", line, e);
                    errors.add("Error Reading Line : " + line);
                }

            }
            return fileDataList;
        } catch (final Exception e) {
            logger.error("Error while reading file", e);
        }
        return null;
    }

    private static Options buildOptions() {
        final Options options = new Options();
        options.addOption(INSTITUTE_ID, true, "specify the institute id");
        options.addOption(ACADEMIC_SESSION_ID, true, "Academic session id for institute");
        options.addOption(FILE_PATH, true, "Transport assignment csv file path");
        options.addOption(FILE_HEADER, false, "File has header");
        options.addOption(UPDATE, false, "Create transaction in DB");
        options.addOption(USER, true, "User id for transaction");
        return options;
    }

    private class FileData {
        private final String admissionNumber;

        private final int transactionDate;
        private final double totalCollectedAmount;
        private final double discountAmount;
        private final TransactionMode paymentMode;
        private final String invoiceId;

        public FileData(String admissionNumber, int transactionDate, double totalCollectedAmount, double discountAmount, TransactionMode paymentMode, String invoiceId) {
            this.admissionNumber = admissionNumber;
            this.transactionDate = transactionDate;
            this.totalCollectedAmount = totalCollectedAmount;
            this.discountAmount = discountAmount;
            this.paymentMode = paymentMode;
            this.invoiceId = invoiceId;
        }

        public String getAdmissionNumber() {
            return admissionNumber;
        }

        public int getTransactionDate() {
            return transactionDate;
        }

        public double getTotalCollectedAmount() {
            return totalCollectedAmount;
        }

        public double getDiscountAmount() {
            return discountAmount;
        }

        public TransactionMode getPaymentMode() {
            return paymentMode;
        }

        public String getInvoiceId() {
            return invoiceId;
        }

        @Override
        public String toString() {
            return "FileData{" +
                    "admissionNumber='" + admissionNumber + '\'' +
                    ", transactionDate=" + transactionDate +
                    ", totalCollectedAmount=" + totalCollectedAmount +
                    ", discountAmount=" + discountAmount +
                    ", paymentMode=" + paymentMode +
                    ", invoiceId='" + invoiceId + '\'' +
                    '}';
        }
    }
}
