package com.embrate.cloud.dev.tools.ingest.user.image;

import com.embrate.cloud.core.api.document.BulkDocumentUploadResponse;
import com.embrate.cloud.core.api.document.BulkUserImageUploadPayload;
import com.embrate.cloud.core.lib.document.BulkDocumentManager;
import com.lernen.cloud.core.lib.student.StudentAdmissionManager;
import com.lernen.cloud.core.utils.SharedConstants;
import com.lernen.cloud.core.utils.spring.SpringAppContextProvider;
import org.apache.commons.cli.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.ApplicationContext;

import java.io.*;

import static com.lernen.cloud.core.utils.SharedConstants.OBJECT_MAPPER;

/**
 * sudo java -Dlernen_env=prod -cp /tmp/dev-tools-1.0.1-SNAPSHOT.jar com.embrate.cloud.dev.tools.ingest.user.image.BulkUserProfileImageUpdater -cf "/tmp/config.json"
 *
 * To generate token
 * https://developers.google.com/oauthplayground/?code=4/0AZEOvhX1HvVZBPWVu-CedOQfK_3dXejI0fo0pqX1FsgCA5W9q4-fiL-M3hVzhW0x5Z_MPg&scope=https://www.googleapis.com/auth/drive
 *
 * select drive api v3
 * auth
 *
 *
 *  {
 *    "instituteId" : 10180,
 *    "userType" : "STUDENT",
 *    "imageFolderPath" : "Nosegay ID Cards 2023-24/Images",
 *    "profileImageFilePath" :"Nosegay ID Cards 2023-24/10180_profle_image.csv",
 *    "googleDriveAccessToken" : "******************************************************************************************************************************************************************************************************************",
 *    "header" : false,
 *    "validationMode" : false,
 *    "execute" : true,
 *    "testAdmissionNumber" : null,
 *    "resizeFactor" : 0.2,
 *    "rotation" : -90
 *  }
 *
 * <AUTHOR>
 */
public class BulkUserProfileImageUpdater {

    private static final Logger logger = LogManager.getLogger(StudentAdmissionManager.class);

    private static final String CONFIG_FILE_PATH = "cf";
    private static final String EXECUTE = "e";
    private final BulkDocumentManager bulkDocumentManager;

    public BulkUserProfileImageUpdater(BulkDocumentManager bulkDocumentManager) {
        this.bulkDocumentManager = bulkDocumentManager;
    }

    public static void main(String args[]) {
        final ApplicationContext context = SpringAppContextProvider.getApplicationContext("dev-tools.xml");
        final BulkDocumentManager bulkDocumentManager = context.getBean(BulkDocumentManager.class);
        BulkUserProfileImageUpdater bulkUserProfileImageUpdater = new BulkUserProfileImageUpdater(bulkDocumentManager);
        Options options = buildOptions();
        CommandLine cmdLine = null;
        try {
            final CommandLineParser parser = new DefaultParser();
            cmdLine = parser.parse(options, args);
        } catch (final Exception pex) {
            logger.error("Unable to parse arguments.");
            pex.printStackTrace();
            final HelpFormatter formatter = new HelpFormatter();
            formatter.printHelp("BulkUserProfileImageUpdater ", options);
            return;
        }

        if (cmdLine == null) {
            logger.error("Unable to parse arguments from cmdline.");
            return;
        }

        String configFilePath = null;
        if (cmdLine.hasOption(CONFIG_FILE_PATH)) {
            configFilePath = cmdLine.getOptionValue(CONFIG_FILE_PATH);
        } else {
            logger.error("Invalid config path. Exiting");
            return;
        }

        boolean execute = false;
        if (cmdLine.hasOption(EXECUTE)) {
            execute = true;
        }

        BulkUserImageUploadPayload bulkUserImageUploadPayload = bulkUserProfileImageUpdater.loadJsonResource(configFilePath);
        logger.info("Running with execute = {}, configuration file {}", execute, bulkUserImageUploadPayload);

        try {
            bulkUserProfileImageUpdater.run(bulkUserImageUploadPayload, execute);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private BulkUserImageUploadPayload loadJsonResource(String resourcePath) {
        try (InputStream inputStream = new FileInputStream(resourcePath)) {
            if (inputStream == null) {
                logger.error("Null input stream for schema path {}", resourcePath);
                return null;
            }
            return OBJECT_MAPPER.convertValue(OBJECT_MAPPER.readTree(inputStream), BulkUserImageUploadPayload.class);

        } catch (final Exception e) {
            logger.error("Unable to read json file - {}", resourcePath, e);
        }
        return null;
    }

    private void run(BulkUserImageUploadPayload payload, boolean execute) throws IOException {
        BulkDocumentUploadResponse bulkDocumentUploadResponse = bulkDocumentManager.validateStudentProfileImageFile(payload.getInstituteId(), payload.getUserType(), payload.getImageFolderPath(), payload.getProfileImageFilePath()
                , payload.getGoogleDriveAccessToken(), payload.isHeader(), payload.isValidationMode(), payload.isExecute() && execute, payload.getTestAdmissionNumber(),
                payload.getResizeFactor(), payload.getRotation());
        logger.info("Response {}", bulkDocumentUploadResponse);
        try {
            FileWriter myWriter = new FileWriter("/tmp/bulkDocumentUploadResponse-" + System.currentTimeMillis() + ".json");
            myWriter.write(SharedConstants.GSON.toJson(bulkDocumentUploadResponse));
            myWriter.close();
        }catch (Exception e){
            logger.error("Error while writing file for payload {}", payload);
        }
    }

    private static Options buildOptions() {
        final Options options = new Options();
        options.addOption(CONFIG_FILE_PATH, true, "config file path");
        options.addOption(EXECUTE, false, "execute");
        return options;
    }

}
