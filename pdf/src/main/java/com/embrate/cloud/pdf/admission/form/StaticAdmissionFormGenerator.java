package com.embrate.cloud.pdf.admission.form;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.pdf.base.PDFGenerator;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.WordUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public abstract class StaticAdmissionFormGenerator  extends PDFGenerator {

    public StaticAdmissionFormGenerator(AssetProvider assetProvider) {
                super(assetProvider);
                //TODO Auto-generated constructor stub
        }

private static final Logger logger = LogManager.getLogger(StaticAdmissionFormGenerator.class);
    protected static final String EMPTY_VALUE = "";

    public abstract DocumentOutput generateStaticAdmissionForm(Institute institute, AcademicSession academicSession, String documentName);

    protected DocumentLayoutData generateStaticAdmissionFormLayoutData(Institute institute, DocumentOutput documentOutput,
                                                                       float logoWidth, float logoHeight) throws IOException {
        float contentFontSize = 11f;
        return generateStaticAdmissionFormLayoutData(institute, documentOutput, logoWidth, logoHeight, contentFontSize);
    }
    protected DocumentLayoutData generateStaticAdmissionFormLayoutData(Institute institute, DocumentOutput documentOutput,
                                                                 float logoWidth, float logoHeight, float contentFontSize) throws IOException {

        DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false, PageSize.A4, 5f, 5f, 20f, 0f);
        float defaultBorderWidth = 0.5f;
        Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

        int instituteId = institute.getInstituteId();
        DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getRegularFont(),
                getRegularBoldFont(), contentFontSize, defaultBorderWidth, logoWidth, logoHeight,
                LogoProvider.INSTANCE.getLogo(instituteId), null, null);
        documentLayoutData.setImageFrame(ImageProvider.INSTANCE.getImage(ImageProvider.IMAGE_FRAME));
        return documentLayoutData;
    }

    protected void generateMetadata(DocumentLayoutData documentLayoutData, float squareBorderMargin, float innerGap, int pageNumber) {
        generateBorderLayout(documentLayoutData.getDocument(), documentLayoutData.getDocumentLayoutSetup(), pageNumber,
                squareBorderMargin, innerGap);
    }

    protected void generatePageHeader(DocumentLayoutData documentLayoutData, Institute institute,
                                      AcademicSession academicSession) throws IOException {
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
        generatePageHeader(documentLayoutData, institute, academicSession,
                20f, documentLayoutSetup.getPageSize().getHeight() * 0.91f, 22f);
    }

    protected void generatePageHeader(DocumentLayoutData documentLayoutData, Institute institute,
                                      AcademicSession academicSession, float logoOffsetX, float logoOffsetY,
                                      float instituteNameFontSize) throws IOException {
        PdfFont boldFont = documentLayoutData.getBoldFont();
        List<TextDetails> headingTextDetailsList = new ArrayList<>();
        headingTextDetailsList.add(new TextDetails(institute.getInstituteName().toUpperCase(),
                instituteNameFontSize, 102, 51, 0, boldFont, TextAlignment.CENTER));
        headingTextDetailsList.add(new TextDetails(institute.getLetterHeadLine1(),
                12f, 0, 0, 0, boldFont, TextAlignment.CENTER));
        headingTextDetailsList.add(new TextDetails(institute.getLetterHeadLine2(),
                12f, 0, 0, 0, boldFont, TextAlignment.CENTER));
        generatePageHeader(documentLayoutData, institute,
                academicSession, logoOffsetX, logoOffsetY, headingTextDetailsList);
    }

    protected void generatePageHeader(DocumentLayoutData documentLayoutData, Institute institute,
                                      AcademicSession academicSession, float logoOffsetX, float logoOffsetY,
                                      List<TextDetails> headingTextDetailsList) throws IOException {
        Document document = documentLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
        PdfFont boldFont = documentLayoutData.getBoldFont();

        generateDynamicImageProvider(documentLayoutData, logoOffsetX, logoOffsetY, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

        int singleContentColumn = 1;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        for(TextDetails textDetails : headingTextDetailsList) {
            addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(textDetails.getText(),
                            textDetails.getTextRColor(), textDetails.getTextGColor(), textDetails.getTextBColor())),
                cellLayoutSetup.copy()
                        .setFontSize(textDetails.getTextSize())
                        .setPdfFont(textDetails.getPdfFont())
                        .setTextAlignment(textDetails.getTextAlignment())
            );
        }

        document.add(table);

        table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup singleLineBorderLayout = new CellLayoutSetup();
        singleLineBorderLayout.setPdfFont(boldFont).setFontSize(14f).setTextAlignment(TextAlignment.CENTER)
                .setBorderBottom(new SolidBorder(2f));

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("")), singleLineBorderLayout);
        document.add(table);

        addBlankLine(document, false, 1);

        table = getPDFTable(documentLayoutSetup, new float[] { 0.3f, 0.4f, 0.3f });
        addRow(table, documentLayoutSetup, Arrays.asList(new CellData(EMPTY_TEXT, cellLayoutSetup),
                new CellData(getParagraph("ADMISSION FORM (" + academicSession.getShortYearDisplayName() + ")", 255,
                        255, 255), cellLayoutSetup.copy().setFontSize(14f).setBackgroundColor(EColorUtils.BLACK_COLOR_HEX_CODE)),
                new CellData(EMPTY_TEXT, cellLayoutSetup)));

        document.add(table);

    }

    protected int generateBasicInformationPage(DocumentLayoutData documentLayoutData, int index) throws IOException {
        Document document = documentLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
        PdfFont boldFont = documentLayoutData.getBoldFont();

        byte[]  image = documentLayoutData.getImageFrame();
        generateImage(document, documentLayoutSetup, image, 80, 90,
                documentLayoutData.getDocumentLayoutSetup().getPageSize().getWidth() - 120f,
                documentLayoutSetup.getPageSize().getHeight() * 0.70f);

        int singleContentColumn = 1;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup singleCellLayoutSetup = new CellLayoutSetup();
        singleCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.LEFT);

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("BASIC INFORMATION")),
                singleCellLayoutSetup.copy().setFontSize(12f).setPdfFont(boldFont));

        document.add(table);
        addBlankLine(document, false, 1);

        table = getPDFTable(documentLayoutSetup, singleContentColumn);

        addRow(table, documentLayoutSetup,
                Arrays.asList(
                        getAdmissionFormKeyValueParagraph(index++, "Student Name : ", EMPTY_VALUE)),
                singleCellLayoutSetup);

        addRow(table, documentLayoutSetup,
                Arrays.asList(getAdmissionFormKeyValueParagraph(index++, "Class : ", EMPTY_VALUE)),
                singleCellLayoutSetup);

        addRow(table, documentLayoutSetup, Arrays.asList(getAdmissionFormKeyValueParagraph(index++,
                "Admission Number : ", EMPTY_VALUE, false)), singleCellLayoutSetup);

        document.add(table);
        table = getPDFTable(documentLayoutSetup, 2);

        addRow(table, documentLayoutSetup, Arrays.asList(
                getAdmissionFormKeyValueParagraph(index++, "Admission Date : ", EMPTY_VALUE),
                    getAdmissionFormKeyValueParagraph(index++, "PEN Number : ", EMPTY_VALUE)),
                singleCellLayoutSetup);

        document.add(table);

        CellLayoutSetup threeCellLayoutSetup = new CellLayoutSetup();
        threeCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.LEFT);

        table = getPDFTable(documentLayoutSetup, 3);

        addRow(table, documentLayoutSetup,
                Arrays.asList(
                        getAdmissionFormKeyValueParagraph(index++, "Gender : ", EMPTY_VALUE),
                        getAdmissionFormKeyValueParagraph(index++, "Date Of Birth : ", EMPTY_VALUE),
                        getAdmissionFormKeyValueParagraph(index++, "Birth Place : ", EMPTY_VALUE)),
                threeCellLayoutSetup);

        addRow(table, documentLayoutSetup,
                Arrays.asList(getAdmissionFormKeyValueParagraph(index++, "Religion : ", EMPTY_VALUE),
                        getAdmissionFormKeyValueParagraph(index++, "Category : ", EMPTY_VALUE),
                        getAdmissionFormKeyValueParagraph(index++, "Specially Abled : ", EMPTY_VALUE)),
                threeCellLayoutSetup);

        document.add(table);

        table = getPDFTable(documentLayoutSetup, 3);

        addRow(table, documentLayoutSetup, Arrays.asList(
                getAdmissionFormKeyValueParagraph(index++, "Permanent Address : ", EMPTY_VALUE),
                new Paragraph(""),
                new Paragraph("")), singleCellLayoutSetup);

        document.add(table);
        table = getPDFTable(documentLayoutSetup, 3);

        addRow(table, documentLayoutSetup,
                Arrays.asList(getAdmissionFormKeyValueParagraph(index++, "City : ", EMPTY_VALUE),
                        getAdmissionFormKeyValueParagraph(index++, "State : ", EMPTY_VALUE),
                        getAdmissionFormKeyValueParagraph(index++, "Pincode : ", EMPTY_VALUE)),
                threeCellLayoutSetup);

        document.add(table);
        table = getPDFTable(documentLayoutSetup, 2);

        addRow(table, documentLayoutSetup, Arrays.asList(
                getAdmissionFormKeyValueParagraph(index++, "Aadhar Number : ", EMPTY_VALUE),
                getAdmissionFormKeyValueParagraph(index++, "Whatsapp Number : ", EMPTY_VALUE)),
                threeCellLayoutSetup);

        document.add(table);
        table = getPDFTable(documentLayoutSetup, 3);

        addRow(table, documentLayoutSetup, Arrays.asList(
                getAdmissionFormKeyValueParagraph(index++, "Student Sponsored :", EMPTY_VALUE),
                getAdmissionFormKeyValueParagraph(index++, "Primary Email : ", EMPTY_VALUE),
                getAdmissionFormKeyValueParagraph(index++, "Primary Contact :  ", EMPTY_VALUE)), threeCellLayoutSetup);
        document.add(table);

        return index;
    }

    protected int generateFamilyInformationPage(DocumentLayoutData documentLayoutData,
                                                int index) throws IOException {
        Document document = documentLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
        PdfFont boldFont = documentLayoutData.getBoldFont();

        int singleContentColumn = 1;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup singleCellLayoutSetup = new CellLayoutSetup();
        singleCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.LEFT);

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("FAMILY INFORMATION")),
                singleCellLayoutSetup.copy().setFontSize(12f).setPdfFont(boldFont));

        document.add(table);
        addBlankLine(document, false, 1);

        table = getPDFTable(documentLayoutSetup, 2);

        CellLayoutSetup threeCellLayoutSetup = new CellLayoutSetup();
        threeCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.LEFT);

        table = getPDFTable(documentLayoutSetup, 2);

        addRow(table, documentLayoutSetup,
                Arrays.asList(
                        getAdmissionFormKeyValueParagraph(index++, "Father's Name : ", EMPTY_VALUE),
                        getAdmissionFormKeyValueParagraph(index++, "Mother's Name : ", EMPTY_VALUE)),
                threeCellLayoutSetup);

        addRow(table, documentLayoutSetup,
                Arrays.asList(
                        getAdmissionFormKeyValueParagraph(index++, "Father's Contact : ", EMPTY_VALUE),
                        getAdmissionFormKeyValueParagraph(index++, "Mother's Contact : ", EMPTY_VALUE)),
                threeCellLayoutSetup);

        addRow(table, documentLayoutSetup,
                Arrays.asList(
                        getAdmissionFormKeyValueParagraph(index++, "Father's Occupation : ", EMPTY_VALUE),
                        getAdmissionFormKeyValueParagraph(index++, "Mother's Occupation : ", EMPTY_VALUE)),
                threeCellLayoutSetup);

        addRow(table, documentLayoutSetup,
                Arrays.asList(
                        getAdmissionFormKeyValueParagraph(index++, "Father's Aadhar Number : ", EMPTY_VALUE),
                        getAdmissionFormKeyValueParagraph(index++, "Mother's Aadhar Number : ", EMPTY_VALUE)),
                threeCellLayoutSetup);

        document.add(table);

        return index;
    }

    protected int generatePrevSchoolInformationPage(DocumentLayoutData documentLayoutData, int index) throws IOException {
        Document document = documentLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
        PdfFont boldFont = documentLayoutData.getBoldFont();

        int singleContentColumn = 1;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup singleCellLayoutSetup = new CellLayoutSetup();
        singleCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.LEFT);

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("PREVIOUS SCHOOL INFORMATION")),
                singleCellLayoutSetup.copy().setFontSize(12f).setPdfFont(boldFont));

        document.add(table);
        addBlankLine(document, false, 1);

        table = getPDFTable(documentLayoutSetup, 2);

        CellLayoutSetup threeCellLayoutSetup = new CellLayoutSetup();
        threeCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.LEFT);

        table = getPDFTable(documentLayoutSetup, 2);

        addRow(table, documentLayoutSetup,
                Arrays.asList(
                        getAdmissionFormKeyValueParagraph(index++, "School Name : ", EMPTY_VALUE),
                        getAdmissionFormKeyValueParagraph(index++, "Class Attended : ", EMPTY_VALUE)),
                threeCellLayoutSetup);

        addRow(table, documentLayoutSetup,
                Arrays.asList(
                        getAdmissionFormKeyValueParagraph(index++, "Medium : ", EMPTY_VALUE),
                        getAdmissionFormKeyValueParagraph(index++, "Percentage/Grade : ", EMPTY_VALUE)),
                threeCellLayoutSetup);

        document.add(table);

        return index;
    }

    protected int generateMedicalInformationPage(DocumentLayoutData documentLayoutData, int index) throws IOException {
        Document document = documentLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
        PdfFont boldFont = documentLayoutData.getBoldFont();

        int singleContentColumn = 1;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup singleCellLayoutSetup = new CellLayoutSetup();
        singleCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.LEFT);

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("MEDICAL INFORMATION")),
                singleCellLayoutSetup.copy().setFontSize(12f).setPdfFont(boldFont));

        document.add(table);
        addBlankLine(document, false, 1);

        table = getPDFTable(documentLayoutSetup, 2);

        CellLayoutSetup threeCellLayoutSetup = new CellLayoutSetup();
        threeCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.LEFT);

        table = getPDFTable(documentLayoutSetup, 2);

        addRow(table, documentLayoutSetup, Arrays.asList(
                        getAdmissionFormKeyValueParagraph(index++, "Blood Group : ", EMPTY_VALUE),
                        getAdmissionFormKeyValueParagraph(index++, "Blood Pressure : ", EMPTY_VALUE)),
                threeCellLayoutSetup);

        addRow(table, documentLayoutSetup,
                Arrays.asList(getAdmissionFormKeyValueParagraph(index++, "Height : ", EMPTY_VALUE),
                        getAdmissionFormKeyValueParagraph(index++, "Weight : ", EMPTY_VALUE)),
                threeCellLayoutSetup);

        document.add(table);

        return index;
    }

    protected int generateGardianInformationPage(DocumentLayoutData documentLayoutData, int index) throws IOException {
        Document document = documentLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
        PdfFont boldFont = documentLayoutData.getBoldFont();

        int singleContentColumn = 1;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup singleCellLayoutSetup = new CellLayoutSetup();
        singleCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.LEFT);

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("GUARDIAN INFORMATION")),
                singleCellLayoutSetup.copy().setFontSize(12f).setPdfFont(boldFont));

        document.add(table);
        addBlankLine(document, false, 1);

        table = getPDFTable(documentLayoutSetup, 3);

        CellLayoutSetup threeCellLayoutSetup = new CellLayoutSetup();
        threeCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.LEFT);

        table = getPDFTable(documentLayoutSetup, 3);
        addRow(table, documentLayoutSetup, Arrays.asList(
                        getAdmissionFormKeyValueParagraph(index++, "Guardian Name : ", EMPTY_VALUE),
                        getAdmissionFormKeyValueParagraph(index++, "Relation : ", EMPTY_VALUE),
                        new Paragraph("")),
                threeCellLayoutSetup);

        addRow(table, documentLayoutSetup,
                Arrays.asList(getAdmissionFormKeyValueParagraph(index++, "Age : ", EMPTY_VALUE),
                        getAdmissionFormKeyValueParagraph(index++, "Contact Number : ", EMPTY_VALUE),
                        getAdmissionFormKeyValueParagraph(index++, "Gender : ", EMPTY_VALUE)),
                threeCellLayoutSetup);

        addRow(table, documentLayoutSetup,
                        Arrays.asList(getAdmissionFormKeyValueParagraph(index++, "Occupation : ", EMPTY_VALUE),
                                getAdmissionFormKeyValueParagraph(index++, "Email : ", EMPTY_VALUE),
                                new Paragraph("")),
                threeCellLayoutSetup);

        addRow(table, documentLayoutSetup,
                Arrays.asList(getAdmissionFormKeyValueParagraph(index++, "Address : ", EMPTY_VALUE),
                        new Paragraph(""), new Paragraph("")),
                threeCellLayoutSetup);


        addRow(table, documentLayoutSetup,
                Arrays.asList(getAdmissionFormKeyValueParagraph(index++, "City : ", EMPTY_VALUE),
                        getAdmissionFormKeyValueParagraph(index++, "State : ", EMPTY_VALUE),
                        getAdmissionFormKeyValueParagraph(index++, "Pincode : ", EMPTY_VALUE)),
                threeCellLayoutSetup);

        document.add(table);

        return index;
    }

    protected int generateDocumentsInformationPage(DocumentLayoutData documentLayoutData, int index) throws IOException {
        Document document = documentLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
        PdfFont boldFont = documentLayoutData.getBoldFont();

        int singleContentColumn = 1;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup singleCellLayoutSetup = new CellLayoutSetup();
        singleCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.LEFT);

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("DOCUMENT INFORMATION")),
                singleCellLayoutSetup.copy().setFontSize(12f).setPdfFont(boldFont));

        document.add(table);
        addBlankLine(document, false, 1);

        table = getPDFTable(documentLayoutSetup, 2);

        CellLayoutSetup threeCellLayoutSetup = new CellLayoutSetup();
        threeCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.LEFT);

        table = getPDFTable(documentLayoutSetup, 2);

        addRow(table, documentLayoutSetup, Arrays.asList(
                        getAdmissionFormKeyValueParagraph(index++, "Original TC from previous school : ", "Yes/No", false),
                        getAdmissionFormKeyValueParagraph(index++, "Previous class original marksheet : ", "Yes/No", false)),
                threeCellLayoutSetup);

        addRow(table, documentLayoutSetup,
                Arrays.asList(getAdmissionFormKeyValueParagraph(index++, "Aadhar Card : ", "Yes/No", false),
                        getAdmissionFormKeyValueParagraph(index++, "Birth Certificate : ", "Yes/No", false)),
                threeCellLayoutSetup);

        addRow(table, documentLayoutSetup,
                Arrays.asList(getAdmissionFormKeyValueParagraph(index++, "Income Certificate : ", "Yes/No", false),
                        getAdmissionFormKeyValueParagraph(index++, "Caste Certificate : ", "Yes/No", false)),
                threeCellLayoutSetup);

        document.add(table);

        return index;
    }

    protected void generateSignatureBox(DocumentLayoutData documentLayoutData, float squareBorderMargin, float innerGap)
            throws IOException {

        Document document = documentLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();

        int singleContentColumn = 4;

        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
        signatureCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(documentLayoutData.getContentFontSize())
                .setTextAlignment(TextAlignment.CENTER);

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Admission Incharge"), getParagraph("Student"),
                getParagraph("Parents/Guardians"), getParagraph("Principal")), signatureCellLayoutSetup);

        table.setFixedPosition(3, squareBorderMargin + innerGap + 3, documentLayoutSetup.getPageSize().getWidth());
        document.add(table);
    }

    protected Paragraph getAdmissionFormKeyValueParagraph(int index, String key, String value) {
        return getAdmissionFormKeyValueParagraph(index, true, key, value, true);
    }

    protected Paragraph getAdmissionFormKeyValueParagraph(int index, String key, String value, boolean capitalize) {
        return getAdmissionFormKeyValueParagraph(index, true, key, value, capitalize);
    }

    private Paragraph getAdmissionFormKeyValueParagraph(int index, boolean useIndex, String key, String value,
                                                        boolean capitalize) {
        try {
            return getKeyValueParagraph((useIndex ? index + ". " : "") + key, getAdmissionFormValue(value, capitalize));
        } catch (Exception e) {
            logger.error("Error while getting paragraph with key {}", key);
        }
        return new Paragraph(EMPTY_VALUE);
    }

    protected Paragraph getAdmissionFormParagraph(int index, String value, PdfFont pdfFont) {
        return getAdmissionFormParagraph(index, true, value, true, pdfFont);
    }

    private Paragraph getAdmissionFormParagraph(int index, boolean useIndex, String value,
                                                        boolean capitalize, PdfFont pdfFont) {
        try {
            return getParagraph((useIndex ? index + ". " : "") + getAdmissionFormValue(value, capitalize),
                    0,0,0, pdfFont);
        } catch (Exception e) {
            logger.error("Error while getting paragraph with value {}", value);
        }
        return new Paragraph(EMPTY_VALUE);
    }

    private String getAdmissionFormValue(String value, boolean capitalize) {
        if (StringUtils.isBlank(value)) {
            return EMPTY_VALUE;
        }
        return capitalize ? WordUtils.capitalizeFully(value) : value;
    }

    protected void generateBackgroundImage(Document document, DocumentLayoutSetup documentLayoutSetup, byte[] image, float width,
                                         float height, float offsetX, float offsetY) {
        try {
            generateImage(document, documentLayoutSetup, image, width, height, offsetX, offsetY);
        } catch (Exception e) {
            logger.error("Exception while adding background image", e);
        }

    }

    class TextDetails {

        private final String text;

        private final float textSize;

        private final int textRColor;

        private final int textGColor;

        private final int textBColor;

        private final PdfFont pdfFont;

        private final TextAlignment textAlignment;

        public TextDetails(String text, float textSize, int textRColor, int textGColor, int textBColor, PdfFont pdfFont,
                           TextAlignment textAlignment) {
            this.text = text;
            this.textSize = textSize;
            this.textRColor = textRColor;
            this.textGColor = textGColor;
            this.textBColor = textBColor;
            this.pdfFont = pdfFont;
            this.textAlignment = textAlignment;
        }

        public String getText() {
            return text;
        }

        public float getTextSize() {
            return textSize;
        }

        public int getTextRColor() {
            return textRColor;
        }

        public int getTextGColor() {
            return textGColor;
        }

        public int getTextBColor() {
            return textBColor;
        }

        public PdfFont getPdfFont() {
            return pdfFont;
        }

        public TextAlignment getTextAlignment() {
            return textAlignment;
        }

        @Override
        public String toString() {
            return "TextDetails{" +
                    "text='" + text + '\'' +
                    ", textSize=" + textSize +
                    ", textRColor=" + textRColor +
                    ", textGColor=" + textGColor +
                    ", textBColor=" + textBColor +
                    ", pdfFont=" + pdfFont +
                    ", textAlignment=" + textAlignment +
                    '}';
        }
    }
}
