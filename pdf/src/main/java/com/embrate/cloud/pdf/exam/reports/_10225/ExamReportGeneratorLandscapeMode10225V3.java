package com.embrate.cloud.pdf.exam.reports._10225;

import com.embrate.cloud.core.api.examination.utility.ExamGridRowAttribute;
import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Image;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.ExamDimensionObtainedValues;
import com.lernen.cloud.core.api.examination.ExamGridAdditionalAttributeRow;
import com.lernen.cloud.core.api.examination.report.*;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.examination.report.configs.GridHeaderConfigs;
import com.lernen.cloud.core.api.examination.report.configs.GridTotalMarksRowConfigs;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.NumberUtils;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.core.utils.images.WatermarkProvider;
import com.lernen.cloud.pdf.exam.reports.ExamReportGenerator;
import com.lernen.cloud.pdf.exam.reports.IExamReportCardGenerator;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;

public class ExamReportGeneratorLandscapeMode10225V3 extends ExamReportGenerator implements IExamReportCardGenerator {

    public ExamReportGeneratorLandscapeMode10225V3(AssetProvider assetProvider) {
        super(assetProvider);
        //TODO Auto-generated constructor stub
    }

    private static final Logger logger = LogManager.getLogger(ExamReportGeneratorLandscapeMode10225V3.class);
    public static final float DEFAULT_PAGE_SIDE_MARGIN = 25f;
    public static final float DEFAULT_PAGE_TOP_MARGIN = 10f;
    public static final float DEFAULT_PAGE_BOTTOM_MARGIN = 20f;
    public static final float SQUARE_BORDER_MARGIN = 12f;
    public static final float SQUARE_BORDER_TOP_BOTTOM_MARGIN = 20f;
    public static final float LOGO_WIDTH = 85f;
    public static final float LOGO_HEIGHT = 85f;
    private static final String DISTINCTION = "DISTINCTION";
    private static final String DIVISION_I = "I";
    private static final String DIVISION_II = "II";
    private static final String DIVISION_III = "III";
    private static final String DIVISION_IV = "IV";

    @Override
    public DocumentOutput generateReport(Institute institute, Student student, String reportType,
                                         ExamReportData examReportData, String documentName, StudentManager studentManager) {
        try {

            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

            ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);
            PdfFont regularFont = getCambriaFont();
            PdfFont boldFont = getCambriaBoldFont();

            int noOfCourses = 0;
            if(examReportData.getCourseTypeExamReportMarksGrid() != null &&
                    examReportData.getCourseTypeExamReportMarksGrid().get(CourseType.SCHOLASTIC) != null &&
                    !CollectionUtils.isEmpty(examReportData.getCourseTypeExamReportMarksGrid().get(CourseType.SCHOLASTIC).getExamReportCourseMarksRows())) {
                noOfCourses += examReportData.getCourseTypeExamReportMarksGrid().get(CourseType.SCHOLASTIC).getExamReportCourseMarksRows().size();
            }
            if(examReportData.getCourseTypeExamReportMarksGrid() != null &&
                    examReportData.getCourseTypeExamReportMarksGrid().get(CourseType.COSCHOLASTIC) != null &&
                    !CollectionUtils.isEmpty(examReportData.getCourseTypeExamReportMarksGrid().get(CourseType.COSCHOLASTIC).getExamReportCourseMarksRows())) {
                noOfCourses += examReportData.getCourseTypeExamReportMarksGrid().get(CourseType.COSCHOLASTIC).getExamReportCourseMarksRows().size();
            }

            generateStudentReportCard(examReportCardLayoutData, institute, examReportData, studentManager, reportType, 1,
                    boldFont, regularFont, noOfCourses);

            examReportCardLayoutData.getDocument().close();
            return documentOutput;
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("Error while generating report card institute {}, student {}, reportType {} ",
                    institute.getInstituteId(), student.getStudentId(), reportType, e);
        }
        return null;
    }

    protected float getScholasticMarksGridSubjectWidth(String reportType) {
        // return 0.2f;
        return 0.15f;
    }

    protected ExamReportCardLayoutData generateReportCardLayoutData(Institute institute, DocumentOutput documentOutput)
            throws IOException {

        DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4.rotate());
        // float contentFontSize = 12f;
        float contentFontSize = 10f;
        float defaultBorderWidth = 0.5f;
        Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

        int instituteId = institute.getInstituteId();
        return new ExamReportCardLayoutData(document, documentLayoutSetup, null, null, contentFontSize,
                defaultBorderWidth, LOGO_WIDTH, LOGO_HEIGHT, LogoProvider.INSTANCE.getLogo(instituteId));
    }

    private void generateBackgroundImage(Document document, DocumentLayoutSetup documentLayoutSetup, byte[] image, float width,
                                         float height, float offsetX, float offsetY) {
        try {
            generateImage(document, documentLayoutSetup, image, width, height, offsetX, offsetY);
        } catch (Exception e) {
            logger.error("Exception while adding background image", e);
        }

    }

    protected void generateMetaDataLayout(ExamReportCardLayoutData examReportCardLayoutData, Institute institute,
                                          StudentLite studentLite, StudentManager studentManager,
                                          String reportType, ExamReportData examReportData, int pageNumber,
                                          PdfFont boldFont, PdfFont regularFont, int noOfCourses) throws IOException {

        //Institute watermark
        int instituteId = institute.getInstituteId();
        float bgImageHeightWidth = 400f;
        generateWatermark(examReportCardLayoutData, instituteId, bgImageHeightWidth, bgImageHeightWidth, bgImageHeightWidth / 2,
                bgImageHeightWidth / 5 + 20);
        generateHeader(examReportCardLayoutData, studentLite, institute, reportType, 40f,
                examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.82f, boldFont, regularFont, studentManager, noOfCourses);
        generateStudentInformation(examReportCardLayoutData, institute, studentLite, examReportData, boldFont, regularFont, noOfCourses);
        generateBorderLayout(examReportCardLayoutData, pageNumber);
    }

    @Override
    public DocumentLayoutSetup initDocumentLayoutSetup(boolean officeCopy) {
        return initDocumentLayoutSetup(officeCopy, officeCopy ? DEFAULT_PAGE_SIZE.rotate() : DEFAULT_PAGE_SIZE,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN : DEFAULT_PAGE_TOP_MARGIN,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN : DEFAULT_PAGE_BOTTOM_MARGIN,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_SIDE_MARGIN : DEFAULT_PAGE_SIDE_MARGIN,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_MIDDLE_MARGIN : 0f);
    }

    protected void generateHeader(ExamReportCardLayoutData examReportCardLayoutData, StudentLite studentLite,
                                  Institute institute, String reportType, float offsetX, float offsetY,
                                  PdfFont boldFont, PdfFont regularFont,
                                  StudentManager studentManager, int noOfCourses) throws IOException {

        generateDynamicImageProvider(examReportCardLayoutData, offsetX, offsetY, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

        int singleContentColumn = 1;
        Table table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(boldFont).setFontSize(16f).setTextAlignment(TextAlignment.CENTER);

        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);

        String instituteName = institute.getInstituteName();
        String letterHead1 = institute.getLetterHeadLine1();
        String letterHead2 = institute.getLetterHeadLine2();
        String letterHead = letterHead1;
        if(StringUtils.isBlank(letterHead)) {
            letterHead = letterHead2;
        } else {
            letterHead += ", " + letterHead2;
        }
        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(instituteName).setMultipliedLeading(1.0f).setPaddingTop(5f)
                        .setFontColor(Color.convertRgbToCmyk(new DeviceRgb(EColorUtils.blackR, EColorUtils.blackG, EColorUtils.blackB)))),
                cellLayoutSetup.copy().setFontSize(18f));

        int textColorR = EColorUtils._10225IntR;
        int textColorG = EColorUtils._10225IntG;
        int textColorB = EColorUtils._10225IntB;
        switch (institute.getInstituteId()) {
            case 10225:
                textColorR = EColorUtils._10225IntR;
                textColorG = EColorUtils._10225IntG;
                textColorB = EColorUtils._10225IntB;
                break;
            case 10226:
                textColorR = EColorUtils._10226IntR;
                textColorG = EColorUtils._10226IntG;
                textColorB = EColorUtils._10226IntB;
                break;
            case 10227:
                textColorR = EColorUtils._10227IntR;
                textColorG = EColorUtils._10227IntG;
                textColorB = EColorUtils._10227IntB;
                break;
            case 10228:
                textColorR = EColorUtils._10228IntR;
                textColorG = EColorUtils._10228IntG;
                textColorB = EColorUtils._10228IntB;
                break;
            default:
        }

        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(letterHead)
                        .setFontColor(new DeviceRgb(textColorR, textColorG, textColorB))),
                cellLayoutSetup.copy().setFontSize(8f));
        examReportCardLayoutData.getDocument().add(table);
        if(noOfCourses < 11 && noOfCourses >= 8) {
            addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
        }

        if(noOfCourses < 8) {
            addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
        }

        table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);
        String headerExamTitle = "PROGRESS REPORT FOR THE ACADEMIC YEAR ";
        headerExamTitle +=  studentLite.getStudentSessionData().getShortYearDisplayName();
        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(headerExamTitle).setUnderline()),
                cellLayoutSetup.copy().setFontSize(11f).setPdfFont(boldFont));
        examReportCardLayoutData.getDocument().add(table);
        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);

        if(noOfCourses < 14 && noOfCourses >= 11) {
            addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
        }

        if(noOfCourses < 11 && noOfCourses >= 8) {
            addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
        }

        if(noOfCourses < 8) {
            addBlankLine(examReportCardLayoutData.getDocument(), false, 2);
        }
    }

    protected void generateStudentInformation(ExamReportCardLayoutData examReportCardLayoutData,
                                              Institute institute, StudentLite studentLite, ExamReportData examReportData,
                                              PdfFont boldFont, PdfFont regularFont, int noOfCourses) throws IOException {

        Document document = examReportCardLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
        float contentFontSize = examReportCardLayoutData.getContentFontSize() - 1f;

        Table table = getPDFTable(documentLayoutSetup, new float[] { 0.104f, 0.104f, 0.104f, 0.16f, 0.16f, 0.16f, 0.104f, 0.104f });

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize).setTextAlignment(
                TextAlignment.CENTER).setBorder(new SolidBorder(examReportCardLayoutData.getDefaultBorderWidth()));

        // int textColorR = EColorUtils._10225IntR;
        // int textColorG = EColorUtils._10225IntG;
        // int textColorB = EColorUtils._10225IntB;
        // switch (institute.getInstituteId()) {
        //     case 10225:
        //         textColorR = EColorUtils._10225IntR;
        //         textColorG = EColorUtils._10225IntG;
        //         textColorB = EColorUtils._10225IntB;
        //         break;
        //     case 10226:
        //         textColorR = EColorUtils._10226IntR;
        //         textColorG = EColorUtils._10226IntG;
        //         textColorB = EColorUtils._10226IntB;
        //         break;
        //     case 10227:
        //         textColorR = EColorUtils._10227IntR;
        //         textColorG = EColorUtils._10227IntG;
        //         textColorB = EColorUtils._10227IntB;
        //         break;
        //     case 10228:
        //         textColorR = EColorUtils._10228IntR;
        //         textColorG = EColorUtils._10228IntG;
        //         textColorB = EColorUtils._10228IntB;
        //         break;
        //     default:
        // }

//        Paragraph studentName = getKeyValueParagraph("Student Name : ", studentLite.getName(), boldFont, boldFont);
//        Paragraph fatherName = getKeyValueParagraph("Father Name : ", studentLite.getFathersName(), boldFont, boldFont);
//        Paragraph dob = getKeyValueParagraph("DOB : ",
//                studentLite.getDateOfBirth() == null || studentLite.getDateOfBirth() <= 0 ? ""
//                        : DateUtils.getFormattedDate(studentLite.getDateOfBirth(), DATE_FORMAT, User.DFAULT_TIMEZONE), boldFont, boldFont);
//        Paragraph motherName = getKeyValueParagraph("Mother Name : ", studentLite.getMothersName(), boldFont, boldFont);
//        Paragraph admissionNumber = getKeyValueParagraph("Admission No. : ", studentLite.getAdmissionNumber(), boldFont, boldFont);
//        Paragraph classValue = getKeyValueParagraph("Class : ",
//                studentLite.getStudentSessionData().getStandardNameWithSection(), boldFont, boldFont);
//        Paragraph rollNumber = getKeyValueParagraph("Roll Number : ", studentLite.getStudentSessionData().getRollNumber(),  boldFont, boldFont);
//        Paragraph house = getKeyValueParagraph("House : ",
//                examReportData.getStudentLite().getInstituteHouse() == null ? "" : examReportData.getStudentLite().getInstituteHouse().getHouseName(),
//                boldFont, boldFont);

        addRow(table, documentLayoutSetup,
                Arrays.asList(
                        new CellData(getParagraph("CLASS").setMultipliedLeading(0.85f), cellLayoutSetup),
                        new CellData(getParagraph("ROLL NO.").setMultipliedLeading(0.85f), cellLayoutSetup),
                        new CellData(getParagraph("REG NO.").setMultipliedLeading(0.85f), cellLayoutSetup),
                        new CellData(getParagraph("STUDENT'S NAME").setMultipliedLeading(0.85f), cellLayoutSetup),
                        new CellData(getParagraph("FATHER'S NAME").setMultipliedLeading(0.85f), cellLayoutSetup),
                        new CellData(getParagraph("MOTHER'S NAME").setMultipliedLeading(0.85f), cellLayoutSetup),
                        new CellData(getParagraph("BIRTH DATE").setMultipliedLeading(0.85f), cellLayoutSetup),
                        new CellData(getParagraph("HOUSE").setMultipliedLeading(0.85f), cellLayoutSetup)));

        cellLayoutSetup.setPdfFont(regularFont);
        addRow(table, documentLayoutSetup,
                Arrays.asList(
                        new CellData(getParagraph(studentLite.getStudentSessionData().getStandardNameWithSection()).setMultipliedLeading(0.85f), cellLayoutSetup),
                        new CellData(getParagraph(StringUtils.isBlank(studentLite.getStudentSessionData().getRollNumber()) ? EMPTY_TEXT : studentLite.getStudentSessionData().getRollNumber()).setMultipliedLeading(0.85f), cellLayoutSetup),
                        new CellData(getParagraph(studentLite.getAdmissionNumber()).setMultipliedLeading(0.85f), cellLayoutSetup),
                        new CellData(getParagraph(studentLite.getName()).setMultipliedLeading(0.85f), cellLayoutSetup),
                        new CellData(getParagraph(StringUtils.isBlank(studentLite.getFathersName()) ? EMPTY_TEXT : studentLite.getFathersName()).setMultipliedLeading(0.85f), cellLayoutSetup),
                        new CellData(getParagraph(StringUtils.isBlank(studentLite.getMothersName()) ? EMPTY_TEXT : studentLite.getMothersName()).setMultipliedLeading(0.85f), cellLayoutSetup),
                        new CellData(getParagraph(studentLite.getDateOfBirth() == null || studentLite.getDateOfBirth() <= 0 ? ""
                                : DateUtils.getFormattedDate(studentLite.getDateOfBirth(), DATE_FORMAT_DOT, User.DFAULT_TIMEZONE)).setMultipliedLeading(0.85f),
                                cellLayoutSetup),
                        new CellData(getParagraph(examReportData.getStudentLite().getInstituteHouse() == null ? ""
                                : examReportData.getStudentLite().getInstituteHouse().getHouseName()).setMultipliedLeading(0.85f), cellLayoutSetup)));

        document.add(table);

        if(noOfCourses < 8) {
            addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
        }

    }

    protected void generateBorderLayout(ExamReportCardLayoutData examReportCardLayoutData, int pageNumber) {
        DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
        PdfCanvas canvas = new PdfCanvas(examReportCardLayoutData.getDocument().getPdfDocument(), pageNumber);
        canvas.setLineWidth(1.5f);
        canvas.rectangle(SQUARE_BORDER_MARGIN - 2, SQUARE_BORDER_TOP_BOTTOM_MARGIN - 2,
                documentLayoutSetup.getPageSize().getWidth() - (SQUARE_BORDER_MARGIN - 2) * 2,
                documentLayoutSetup.getPageSize().getHeight() - (SQUARE_BORDER_TOP_BOTTOM_MARGIN - 2) * 2);
        canvas.stroke();
        canvas.rectangle(SQUARE_BORDER_MARGIN, SQUARE_BORDER_TOP_BOTTOM_MARGIN,
                documentLayoutSetup.getPageSize().getWidth() - SQUARE_BORDER_MARGIN * 2,
                documentLayoutSetup.getPageSize().getHeight() - SQUARE_BORDER_TOP_BOTTOM_MARGIN * 2);
        canvas.stroke();
    }

    protected void generateRemarksSection(ExamReportCardLayoutData examReportCardLayoutData,
                                          ExamReportData examReportData, PdfFont boldFont,
                                          PdfFont regularFont) throws IOException {

        StudentLite studentLite = examReportData.getStudentLite();
        String heightStr = StringUtils.isBlank(examReportData.getHeight())
                ? StringUtils.isBlank(studentLite.getStudentSessionData().getHeight()) ? EMPTY_TEXT
                : studentLite.getStudentSessionData().getHeight() :
                examReportData.getHeight();
        String weightStr = StringUtils.isBlank(examReportData.getWeight())
                ? StringUtils.isBlank(studentLite.getStudentSessionData().getWeight()) ? EMPTY_TEXT
                : studentLite.getStudentSessionData().getWeight() :
                examReportData.getWeight();

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(boldFont).setFontSize(examReportCardLayoutData.getContentFontSize() - 1f)
                .setTextAlignment(TextAlignment.LEFT).setBorder(new SolidBorder(examReportCardLayoutData.getDefaultBorderWidth()));

        CellLayoutSetup emptyCellLayoutSetup = new CellLayoutSetup();

        Table remarksTable = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), new float[] { 0.54f, 0.01f, 0.12f, 0.10f, 0.01f, 0.12f });

        Paragraph remarkTitle = getParagraph("REMARKS", boldFont).setMultipliedLeading(0.85f);
        Paragraph physicalReport = getParagraph("PHYSICAL REPORT", boldFont).setMultipliedLeading(0.85f);
        Paragraph headingSignatureSection = getParagraph("Signature with Date", boldFont).setMultipliedLeading(0.85f);


        Paragraph remarks = getKeyValueParagraph("Class Teacher Remarks  :  ", examReportData.getRemarks(),
                regularFont, regularFont).setMultipliedLeading(0.85f);
        Paragraph height = getKeyValueParagraph("Height(cm)  :  ", heightStr, regularFont, regularFont).setMultipliedLeading(0.85f);
        Paragraph classTeacherSignature = getParagraph("Class Teacher Signature", regularFont).setMultipliedLeading(0.85f);


        Paragraph principalRemarks = getKeyValueParagraph("Principal Remarks  :  ", examReportData.getPrincipalRemarks(),
                regularFont, regularFont).setMultipliedLeading(0.85f);
        Paragraph weight = getKeyValueParagraph("Weight(kg)  :  ", weightStr, regularFont, regularFont).setMultipliedLeading(0.85f);
        Paragraph principalSignature = getParagraph("Principal Signature", regularFont).setMultipliedLeading(0.85f);


        addRow(remarksTable, examReportCardLayoutData.getDocumentLayoutSetup(),
                Arrays.asList(new CellData(remarkTitle, cellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER)),
                        new CellData(new Paragraph(), emptyCellLayoutSetup),
                        new CellData(headingSignatureSection, cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)),
                        new CellData(new Paragraph(), cellLayoutSetup),
                        new CellData(new Paragraph(), emptyCellLayoutSetup),
                        new CellData(physicalReport, cellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER))));

        addRow(remarksTable, examReportCardLayoutData.getDocumentLayoutSetup(),
                Arrays.asList(new CellData(remarks, cellLayoutSetup),
                        new CellData(new Paragraph(), emptyCellLayoutSetup),
                        new CellData(classTeacherSignature, cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)),
                        new CellData(new Paragraph(), cellLayoutSetup),
                        new CellData(new Paragraph(), emptyCellLayoutSetup),
                        new CellData(height, cellLayoutSetup)));

        addRow(remarksTable, examReportCardLayoutData.getDocumentLayoutSetup(),
                Arrays.asList(new CellData(principalRemarks, cellLayoutSetup),
                        new CellData(new Paragraph(), emptyCellLayoutSetup),
                        new CellData(principalSignature, cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)),
                        new CellData(new Paragraph(), cellLayoutSetup),
                        new CellData(new Paragraph(), emptyCellLayoutSetup),
                        new CellData(weight, cellLayoutSetup)));
        examReportCardLayoutData.getDocument().add(remarksTable);

    }

//    protected void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup,
//                                        float contentFontSize, float defaultBorderWidth, int pageNumber,
//                                        PdfFont boldFont, PdfFont regularFont) throws IOException {
//        int singleContentColumn = 3;
//        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);
//        CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
//        signatureCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize)
//                .setTextAlignment(TextAlignment.LEFT);
//
//        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Class Teacher"),
//                getParagraph("Principal"), getParagraph("Parent")), signatureCellLayoutSetup);
//        table.setFixedPosition(30f, 16f, documentLayoutSetup.getPageSize().getWidth() - 100f);
//        document.add(table);
//    }

    protected void generateStudentAddressBox(Document document, DocumentLayoutSetup documentLayoutSetup,
                                        float contentFontSize, float defaultBorderWidth, int pageNumber,
                                        PdfFont boldFont, PdfFont regularFont, StudentLite studentLite) throws IOException {
        Table table = getPDFTable(documentLayoutSetup, new float[] { 0.1f, 0.4f, 0.01f, 0.2f, 0.01f, 0.2f, 0.08f });
        CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
        signatureCellLayoutSetup.setPdfFont(regularFont).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.LEFT).setBorder(new SolidBorder(defaultBorderWidth));


        String fatherName = (StringUtils.isBlank(studentLite.getFathersName()) ? EMPTY_TEXT : studentLite.getFathersName());
        String address = (StringUtils.isBlank(studentLite.getStudentFullAddress()) ? EMPTY_TEXT : studentLite.getStudentFullAddress());
        String fatherAndAddress = (StringUtils.isBlank(fatherName) ? EMPTY_TEXT : fatherName) + "\n" + (StringUtils.isBlank(address) ? EMPTY_TEXT : address);

        addRow(table, documentLayoutSetup, Arrays.asList(
                new CellData(getParagraph(EMPTY_TEXT), signatureCellLayoutSetup.copy().setBorder(null)),
                new CellData(getParagraph(fatherAndAddress), StringUtils.isBlank(fatherAndAddress) ? signatureCellLayoutSetup.copy().setBorder(null) : signatureCellLayoutSetup.copy().setBorder(new SolidBorder(defaultBorderWidth))),
                new CellData(getParagraph(EMPTY_TEXT), signatureCellLayoutSetup.setBorder(null)),
                new CellData(getParagraph("\n" + "Parent's Signature with Date").setMultipliedLeading(0.85f), signatureCellLayoutSetup.copy().setBorder(new SolidBorder(defaultBorderWidth))),
                new CellData(getParagraph(EMPTY_TEXT), signatureCellLayoutSetup.setBorder(null)),
                new CellData(getParagraph("Copyrights reserved with\nJivem Education Pvt. Ltd. :\nEmbrate Technologies Pvt. Ltd.").setMultipliedLeading(0.85f), signatureCellLayoutSetup.copy().setBorder(null)),
                new CellData(getParagraph(EMPTY_TEXT), signatureCellLayoutSetup.copy().setBorder(null))));
        table.setFixedPosition(30f, 26f, documentLayoutSetup.getPageSize().getWidth() - 100f);
        document.add(table);
    }

    protected Set<UUID> getNonAdditionalSubjects(ExamReportData examReportData) {
        Set<UUID> nonAdditionalSubjects = new HashSet<UUID>();
        for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportData.getCourseTypeExamReportMarksGrid()
                .get(CourseType.SCHOLASTIC).getExamReportCourseMarksRows()) {
            if (!isAdditionalSubject(examReportCourseMarksRow.getCourse(), examReportData.getExamReportStructure())) {
                nonAdditionalSubjects.add(examReportCourseMarksRow.getCourse().getCourseId());
            }
        }
        return nonAdditionalSubjects;
    }

    protected void sortClassExamReports(List<ExamReportData> examReportDataList) {
        Collections.sort(examReportDataList, new Comparator<ExamReportData>() {

            @Override
            public int compare(ExamReportData e1, ExamReportData e2) {
                StandardSections section1 = e1.getStudentLite().getStudentSessionData().getStandardSection();

                StandardSections section2 = e2.getStudentLite().getStudentSessionData().getStandardSection();

                if (section1 != null && section2 != null) {
                    int sectionCompare = section1.getSectionName().compareToIgnoreCase(section2.getSectionName());
                    if (sectionCompare != 0) {
                        return sectionCompare;
                    }
                }

                return e1.getStudentLite().getName().compareToIgnoreCase(e2.getStudentLite().getName());
            }
        });
    }

    @Override
    public DocumentOutput generateClassReport(Institute institute, String reportType,
                                              List<ExamReportData> examReportDataList, String documentName, StudentManager studentManager) {

        try {
            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);

            sortClassExamReports(examReportDataList);

            int pageNumber = 1;
            PdfFont regularFont = getCambriaFont();
            PdfFont boldFont = getCambriaBoldFont();

            for (ExamReportData examReportData : examReportDataList) {
                int noOfCourses = 0;
                if(examReportData.getCourseTypeExamReportMarksGrid() != null &&
                        examReportData.getCourseTypeExamReportMarksGrid().get(CourseType.SCHOLASTIC) != null &&
                        !CollectionUtils.isEmpty(examReportData.getCourseTypeExamReportMarksGrid().get(CourseType.SCHOLASTIC).getExamReportCourseMarksRows())) {
                    noOfCourses += examReportData.getCourseTypeExamReportMarksGrid().get(CourseType.SCHOLASTIC).getExamReportCourseMarksRows().size();
                }
                if(examReportData.getCourseTypeExamReportMarksGrid() != null &&
                        examReportData.getCourseTypeExamReportMarksGrid().get(CourseType.COSCHOLASTIC) != null &&
                        !CollectionUtils.isEmpty(examReportData.getCourseTypeExamReportMarksGrid().get(CourseType.COSCHOLASTIC).getExamReportCourseMarksRows())) {
                    noOfCourses += examReportData.getCourseTypeExamReportMarksGrid().get(CourseType.COSCHOLASTIC).getExamReportCourseMarksRows().size();
                }
                generateStudentReportCard(examReportCardLayoutData, institute, examReportData, studentManager, reportType,
                        pageNumber, boldFont, regularFont, noOfCourses);

                if (pageNumber != examReportDataList.size()) {
                    examReportCardLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                }
                pageNumber++;
            }

            examReportCardLayoutData.getDocument().close();
            return documentOutput;

        } catch (IOException e) {
            e.printStackTrace();
        }

        return null;

    }

    private void generateStudentReportCard(ExamReportCardLayoutData examReportCardLayoutData,
                                           Institute institute, ExamReportData examReportData,
                                           StudentManager studentManager, String reportType, int pageNumber,
                                           PdfFont boldFont, PdfFont regularFont, int noOfCourses) throws IOException {

        generateMetaDataLayout(examReportCardLayoutData, institute, examReportData.getStudentLite(), studentManager, reportType,
                examReportData, pageNumber, boldFont, regularFont, noOfCourses);

        GridConfigs gridConfigs = new GridConfigs(new GridHeaderConfigs(false), new GridTotalMarksRowConfigs(
                true, false, true, true, false,true, true,
                true, true, true, true, "GRAND TOTAL", "", "PERCENTAGE (%)", "TEST DATE",
                "CUML ATTENDANCE", "RESULT DATE", "DIVISION", "RANK/TOTAL STUDENTS", "RESULT"), false);

        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);

        int totalStudents = getStudentCountInClass(institute.getInstituteId(), examReportData.getStudentLite(), studentManager);
        generateCustomScholasticMarksGrid(examReportCardLayoutData, examReportData, reportType, gridConfigs,
                "SUBJECT", getScholasticMarksGridSubjectWidth(reportType), institute.getInstituteId(),
                examReportData.getStudentLite().getStudentId(), studentManager, noOfCourses, totalStudents);

        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);

        if(noOfCourses < 8) {
            addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
        }

        generateRemarksSection(examReportCardLayoutData, examReportData, boldFont, regularFont);

//        generateSignatureBox(examReportCardLayoutData.getDocument(),
//                examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 2f,
//                examReportCardLayoutData.getDefaultBorderWidth(), pageNumber, boldFont, regularFont);

        generateStudentAddressBox(examReportCardLayoutData.getDocument(),
                examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 2f,
                examReportCardLayoutData.getDefaultBorderWidth(), pageNumber, boldFont, regularFont, examReportData.getStudentLite());
    }

    private int getStudentCountInClass(int instituteId, StudentLite studentLite, StudentManager studentManager) {

        int academicSessionId = studentLite.getStudentSessionData().getAcademicSessionId();
        UUID standardId = studentLite.getStudentSessionData().getStandardId();
        Integer sectionId = studentLite.getStudentSessionData().getStandardSection() == null ? null :
                studentLite.getStudentSessionData().getStandardSection().getSectionId();

        List<Student> studentList = studentManager.getClassStudents(instituteId, academicSessionId,
                standardId, new HashSet<>(Collections.singletonList(sectionId)));

        return CollectionUtils.isEmpty(studentList) ? 0 : studentList.size();

    }

    protected String getDivision(Double percentage) {
        if (percentage == null) {
            return "";
        }
        // if (percentage >= 75.0 && percentage <= 100) {
        //     return DISTINCTION;
        //}
        if (percentage >= 60.0 && percentage <= 100) {
            return DIVISION_I;
        } else if (percentage >= 45.0 && percentage < 60.0) {
            return DIVISION_II;
        } else if (percentage >= 36.0 && percentage < 45.0) {
            return DIVISION_III;
        }
        return DIVISION_IV;
    }
    private void generateCustomScholasticMarksGrid(ExamReportCardLayoutData examReportCardLayoutData,
                   ExamReportData examReportData, String reportType, GridConfigs gridConfigs, String subjectColumnTitle,
                   float subjectColumnWidth, int instituteId, UUID studentId, StudentManager studentManager, int noOfCourses,
                   int totalStudentCount) throws IOException {

        Set<UUID> nonAdditionalSubjects = getNonAdditionalSubjects(examReportData);
        CourseType courseType = CourseType.SCHOLASTIC;
        DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
        Document document = examReportCardLayoutData.getDocument();
        float defaultBorderWidth = examReportCardLayoutData.getDefaultBorderWidth();
        PdfFont regularFont = getCambriaFont();
        PdfFont boldFont = getCambriaBoldFont();
//        float contentFontSize = examReportCardLayoutData.getContentFontSize();
        float examTitleFontSize = examReportCardLayoutData.getContentFontSize() - 1.5f;
        boolean showGrade = false;
        String maxMarksTitle = "OUT OF";
        String marksObtainedTitle = "OBT";
        List<Integer> rgbGridHeadingColorCode = null;
        List<Integer> rgbCourseNameColorCode = null;
        float subjectTitleFontSize = examReportCardLayoutData.getContentFontSize() - 1f;
        if(noOfCourses >= 14) {
            subjectTitleFontSize = examReportCardLayoutData.getContentFontSize() - 1.5f;
        } else if(noOfCourses < 14 && noOfCourses >= 11) {
            subjectTitleFontSize = examReportCardLayoutData.getContentFontSize() - 0.5f;
        }

        boolean rowWiseMaxMarksTitle = false;
        int dimensionColumnHeight = 1;
        Map<CourseType, ExamReportMarksGrid> courseTypeExamReportMarksGrid = examReportData
                .getCourseTypeExamReportMarksGrid();

        ExamReportMarksGrid examReportMarksGrid = courseTypeExamReportMarksGrid.get(courseType);
        if (examReportMarksGrid == null) {
            logger.error("examReportMarksGrid is not available, Skipping...");
            return;
        }

        /**
         * counting only exams column (not cumulative columns) as they are dynamic
         */
        /**
         * for subject column starting from 1
         */
        int examCount = 0;
        int actualExamCount = 0;
        for (ExamReportCourseMarksColumn examReportMarksColumn : examReportMarksGrid.getExamReportTotalMarksColumns()) {
            if(examReportMarksColumn.isHide()) {
                continue;
            }
            int examNameColSpan = CollectionUtils.isEmpty(examReportMarksColumn.getExamDimensionObtainedValuesList()) ?
                    2 : examReportMarksColumn.getExamDimensionObtainedValuesList().size();
            if(examReportMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.EXAM ||
                    examReportMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.EXAM_GRADE) {
                /**
                 * adding 2 as there are obtained and out of columns for each exam
                 */
                examCount = examCount + (examNameColSpan * 2);
                actualExamCount = actualExamCount + 2;
            }
        }

        System.out.println(examCount);

        float[] columnWidths = getColumnWidth(examReportMarksGrid, subjectColumnWidth, examCount);

        ExamReportStructureMetaData examReportStructureMetaData = examReportData.getExamReportStructure()
                .getExamReportStructureMetaData();

        int maxHeight = getHeaderColumnMaxHeight(examReportMarksGrid);

        CellLayoutSetup headerCellLayoutSetup = new CellLayoutSetup();
        headerCellLayoutSetup.setPdfFont(boldFont).setFontSize(examTitleFontSize).setBorder(
                new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);

        Table headerTable = getPDFTable(documentLayoutSetup, columnWidths);
        /**
         * code to generate grid header
         */
        addRow(headerTable, documentLayoutSetup, generateGridHeader(examReportStructureMetaData, examReportMarksGrid, courseType,
                headerCellLayoutSetup, subjectColumnTitle, subjectTitleFontSize, maxHeight, subjectColumnWidth,
                showGrade, examTitleFontSize, maxMarksTitle, marksObtainedTitle));


        CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
        marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(subjectTitleFontSize)
                .setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);

        CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
                .setTextAlignment(TextAlignment.LEFT).setFontSize(subjectTitleFontSize);

        ExamReportCourseStructure examReportCourseStructure = examReportData.getExamReportStructure().getExamReportCourseStructure() == null ? null :
                CollectionUtils.isEmpty(examReportData.getExamReportStructure().getExamReportCourseStructure()) ? null :
                        examReportData.getExamReportStructure().getExamReportCourseStructure().get(courseType);


        /**
         * code to generate grid header
         */
        generateGridScholasticContent(documentLayoutSetup, examReportMarksGrid, examReportData
                .getExamReportStructure().getExamReportStructureMetaData(), courseType, headerTable,
                marksCellLayoutSetup, courseCellLayoutSetup, showGrade, nonAdditionalSubjects,
                rgbCourseNameColorCode, EMPTY_TEXT, examReportCourseStructure, examCount);

        generateGridCoScholasticContent(documentLayoutSetup, courseTypeExamReportMarksGrid,
                examReportData, headerTable, marksCellLayoutSetup, courseCellLayoutSetup, showGrade,
                rgbCourseNameColorCode, examCount, boldFont);

        if (gridConfigs != null && gridConfigs.getGridTotalMarksRowConfigs() != null && gridConfigs.getGridTotalMarksRowConfigs().isAddTotalRow()) {
            GridTotalMarksRowConfigs gridTotalMarksRowConfigs = gridConfigs.getGridTotalMarksRowConfigs();
            boolean addMaxRowInTotalRow = gridTotalMarksRowConfigs.isAddMaxRowInTotalRow();
            boolean addPercentRowInTotalRow =  gridTotalMarksRowConfigs.isAddPercentRowInTotalRow();

            CourseTotalMarksRows courseTotalMarksRows = generateCourseTotalMarksInGrid(examReportMarksGrid, examReportStructureMetaData,
                    courseType, marksCellLayoutSetup, courseCellLayoutSetup, showGrade, null, "-",
                    rgbCourseNameColorCode, examReportCourseStructure, gridConfigs, examCount, boldFont);

            if(addMaxRowInTotalRow){
                addRow(headerTable, documentLayoutSetup, courseTotalMarksRows.getMaxMarksRow());
            }

            addRow(headerTable, documentLayoutSetup, courseTotalMarksRows.getObtainedMarksRow());

            if(addPercentRowInTotalRow){
                addRow(headerTable, documentLayoutSetup, courseTotalMarksRows.getPercentMarksRow());
            }
        }

        byte[] studentImage = getStudentImage(instituteId, studentId, studentManager);

        String rankString = "Rank  :  " + (examReportData.getRank() == null ? "" : examReportData.getRank().toString());
        String divisionString = "Division  :  " + (examReportData.getExamResultStatus() == null ||
                examReportData.getExamResultStatus() == ExamResultStatus.FAIL ? "NA" : getDivision(
                examReportData.getPercentage()) + (examReportData.getPercentage() == null ? "" :
                " [" +  (Math.round(examReportData.getPercentage() * 100) / 100d) + "%]"));
        String resultString = "Result  :  " + (examReportData.getExamResultStatus() == null ?
                EMPTY_TEXT : examReportData.getExamResultStatus().getDisplayName());

        String resultText = rankString + "\n" + divisionString + "\n" + resultString + "\n";

        String resultTextWithOneLineSpace = rankString + "\n" + divisionString + "\n" + resultString;

        if (gridConfigs != null) {
            List<ExamGridAdditionalAttributeRow> examGridAdditionalAttributeRowList = generateAdditionalAttributeRowsInGrid(
                    examReportMarksGrid, examReportStructureMetaData,
                    courseType, marksCellLayoutSetup, courseCellLayoutSetup, showGrade, null,
                    "-", null, examReportCourseStructure, gridConfigs, actualExamCount,
                    studentImage, marksCellLayoutSetup, 0, resultText, boldFont, totalStudentCount);
            if(!CollectionUtils.isEmpty(examGridAdditionalAttributeRowList)){
                for(ExamGridAdditionalAttributeRow examGridAdditionalAttributeRow : examGridAdditionalAttributeRowList){
                    addRow(headerTable, documentLayoutSetup, examGridAdditionalAttributeRow.getColumns());
                }
            }
        }

        document.add(headerTable);
    }

    public List<ExamGridAdditionalAttributeRow> generateAdditionalAttributeRowsInGrid(ExamReportMarksGrid examReportMarksGrid,
                              ExamReportStructureMetaData examReportStructureMetaData, CourseType courseType,
                              CellLayoutSetup totalMarksRowCellLayoutSetup, CellLayoutSetup courseCellLayoutSetup,
                              boolean showGrade, String totalRowValuesHexColor, String emptyColumnValue,
                              List<Integer> rgbCourseNameColorCode, ExamReportCourseStructure examReportCourseStructure,
                              GridConfigs gridConfigs, int examCount,
                              byte[] studentImage, CellLayoutSetup marksCellLayoutSetup,
                              int coscholasticSubjectCount, String resultText, PdfFont boldFont, int totalStudentCount) {

        if(CollectionUtils.isEmpty(examReportMarksGrid.getExamReportAdditionalAttributesRows())){
            return new ArrayList<>();
        }
        GridTotalMarksRowConfigs gridTotalMarksRowConfigs = gridConfigs.getGridTotalMarksRowConfigs();

        String gridCoursesBackgroundColor = examReportCourseStructure == null ? null : examReportCourseStructure.getGridCoursesBackgroundColor();
        String gridCoursesTextColor = examReportCourseStructure == null ? null : examReportCourseStructure.getGridCoursesTextColor();

        if(!StringUtils.isBlank(gridCoursesBackgroundColor)) {
            courseCellLayoutSetup.setBackgroundColor(gridCoursesBackgroundColor);
        }

        if(!StringUtils.isBlank(gridCoursesTextColor)) {
            rgbCourseNameColorCode = EColorUtils.hex2Rgb(gridCoursesTextColor);
        }

        List<ExamGridAdditionalAttributeRow> examGridAdditionalAttributeRowList = new ArrayList<>();
        int index = 1;
        for(ExamReportAdditionalAttributesRow examReportAdditionalAttributesRow : examReportMarksGrid.getExamReportAdditionalAttributesRows()){
            List<CellData> columns = new ArrayList<>();
            String title = null;
            switch (examReportAdditionalAttributesRow.getAttribute()){
                case EXAM_DATE:
                    title = gridTotalMarksRowConfigs.getExamDateTitle();
                    break;
                case RESULT_DATE:
                    title = gridTotalMarksRowConfigs.getResultDateTitle();
                    break;
                case ATTENDANCE:
                    title = gridTotalMarksRowConfigs.getAttendanceTitle();
                    break;
                case DIVISION:
                    title = gridTotalMarksRowConfigs.getDivisionTitle();
                    break;
                case RANK:
                    title = gridTotalMarksRowConfigs.getRankTitle();
                    break;
                case RESULT:
                    title = gridTotalMarksRowConfigs.getResultTitle();
                    break;
            }
            if(!CollectionUtils.isEmpty(rgbCourseNameColorCode)) {
                columns.add(new CellData(getParagraph(title, rgbCourseNameColorCode.get(0), rgbCourseNameColorCode.get(1), rgbCourseNameColorCode.get(2)).setMultipliedLeading(1.0f), courseCellLayoutSetup.copy().setPaddingLeft(5f)));
            } else {
                columns.add(new CellData(getParagraph(title).setMultipliedLeading(1.0f), courseCellLayoutSetup.copy().setPaddingLeft(5f)));
            }

            int columnAdded = 0;
            for (ExamReportMarksColumn examReportMarksColumn : examReportAdditionalAttributesRow
                    .getExamReportMarksColumns()) {
                if(examReportMarksColumn.isHide()) {
                    continue;
                }
                int examNameColSpan = CollectionUtils.isEmpty(examReportMarksColumn.getExamDimensionValues()) ?
                        2 : (examReportMarksColumn.getExamDimensionValues().size() * 2);
                int dimensionMultiplier = getDimensionMultiplier(examReportStructureMetaData,
                        examReportMarksColumn.getExamReportGridColumnType(), courseType, showGrade);

                String columnValue = examReportMarksColumn.getColumnValue();
                if (columnValue == null) {
                    columnValue = emptyColumnValue;
                }

                if(columnValue != null && !columnValue.equalsIgnoreCase("-") && examReportAdditionalAttributesRow.getAttribute() == ExamGridRowAttribute.RANK) {
                    columnValue += "/" + totalStudentCount;
                }

                if (dimensionMultiplier == 2) {
                    columns.add(new CellData(getParagraph(columnValue).setMultipliedLeading(1.0f), totalMarksRowCellLayoutSetup, 1, examNameColSpan));
                } else {
                    columns.add(new CellData(getParagraph(columnValue).setMultipliedLeading(1.0f), totalMarksRowCellLayoutSetup));
                }
                columnAdded++;

                if(columnAdded == (examCount / 2)) {
                    columns.add(new CellData(getParagraph(EMPTY_TEXT).setMultipliedLeading(1.0f), totalMarksRowCellLayoutSetup.copy().setBorderTop(null).setBorderBottom(null), 1, 1));

                    if(index == 1) {
                        columns = generateResultPortion(CourseType.COSCHOLASTIC, index, studentImage, marksCellLayoutSetup,
                                columns, coscholasticSubjectCount, resultText, boldFont);
                    }

                    break;
                }
            }
            examGridAdditionalAttributeRowList.add(new ExamGridAdditionalAttributeRow(examReportAdditionalAttributesRow.getAttribute(), columns));
            index++;
        }

        return examGridAdditionalAttributeRowList;
    }
    public CourseTotalMarksRows generateCourseTotalMarksInGrid(ExamReportMarksGrid examReportMarksGrid,
                                                               ExamReportStructureMetaData examReportStructureMetaData, CourseType courseType,
                                                               CellLayoutSetup totalMarksRowCellLayoutSetup, CellLayoutSetup courseCellLayoutSetup,
                                                               boolean showGrade, String totalRowValuesHexColor, String emptyColumnValue,
                                                               List<Integer> rgbCourseNameColorCode, ExamReportCourseStructure examReportCourseStructure,
                                                               GridConfigs gridConfigs, int examCount, PdfFont boldFont) throws IOException {
        List<CellData> maxMarksRow = new ArrayList<>();
        List<CellData> obtainedMarksRow = new ArrayList<>();
        List<CellData> percentMarksRow = new ArrayList<>();
        GridTotalMarksRowConfigs gridTotalMarksRowConfigs = gridConfigs.getGridTotalMarksRowConfigs();

        String gridCoursesBackgroundColor = examReportCourseStructure == null ? null : examReportCourseStructure.getGridCoursesBackgroundColor();
        String gridCoursesTextColor = examReportCourseStructure == null ? null : examReportCourseStructure.getGridCoursesTextColor();

        if(!StringUtils.isBlank(gridCoursesBackgroundColor)) {
            courseCellLayoutSetup.setBackgroundColor(gridCoursesBackgroundColor);
        }

        if(!StringUtils.isBlank(gridCoursesTextColor)) {
            rgbCourseNameColorCode = EColorUtils.hex2Rgb(gridCoursesTextColor);
        }

        String totalRowBackgroundColor = examReportCourseStructure == null ? null : examReportCourseStructure.getTotalRowBackgroundColor();
        String totalRowTextColor = examReportCourseStructure == null ? null : examReportCourseStructure.getTotalRowTextColor();

        if(!StringUtils.isBlank(totalRowBackgroundColor)) {
            totalMarksRowCellLayoutSetup.setBackgroundColor(totalRowBackgroundColor);
        }

        if(!StringUtils.isBlank(totalRowTextColor)) {
            totalRowValuesHexColor = totalRowTextColor;
        }

        if(!CollectionUtils.isEmpty(rgbCourseNameColorCode)) {
            maxMarksRow.add(new CellData(getParagraph(gridTotalMarksRowConfigs.getMaxRowTitle(), rgbCourseNameColorCode.get(0), rgbCourseNameColorCode.get(1), rgbCourseNameColorCode.get(2)).setMultipliedLeading(0.85f), courseCellLayoutSetup.copy().setPaddingLeft(5f)));
            obtainedMarksRow.add(new CellData(getParagraph(gridTotalMarksRowConfigs.getObtainedRowTitle(), rgbCourseNameColorCode.get(0), rgbCourseNameColorCode.get(1), rgbCourseNameColorCode.get(2)).setMultipliedLeading(0.85f), courseCellLayoutSetup.copy().setPaddingLeft(5f)));
            percentMarksRow.add(new CellData(getParagraph(gridTotalMarksRowConfigs.getPercentRowTitle(), rgbCourseNameColorCode.get(0), rgbCourseNameColorCode.get(1), rgbCourseNameColorCode.get(2)).setMultipliedLeading(0.85f), courseCellLayoutSetup.copy().setPaddingLeft(5f)));
        } else {
            maxMarksRow.add(new CellData(getParagraph(gridTotalMarksRowConfigs.getMaxRowTitle()).setMultipliedLeading(0.85f), courseCellLayoutSetup.copy().setPaddingLeft(5f)));
            obtainedMarksRow.add(new CellData(getParagraph(gridTotalMarksRowConfigs.getObtainedRowTitle()).setMultipliedLeading(0.85f), courseCellLayoutSetup.copy().setPaddingLeft(5f)));
            percentMarksRow.add(new CellData(getParagraph(gridTotalMarksRowConfigs.getPercentRowTitle()).setMultipliedLeading(0.85f), courseCellLayoutSetup.copy().setPaddingLeft(5f)));
        }

        totalMarksRowCellLayoutSetup.setPdfFont(boldFont);
        int columnAdded = 0;
        int index = 0;
        for (ExamReportCourseMarksColumn examReportCourseMarksColumn : examReportMarksGrid
                .getExamReportTotalMarksColumns()) {

            if(examReportCourseMarksColumn.isHide()) {
                continue;
            }
            int dimensionMultiplier = getDimensionMultiplier(examReportStructureMetaData,
                    examReportCourseMarksColumn.getExamReportGridColumnType(), courseType, showGrade);

            for (ExamDimensionObtainedValues examDimensionObtainedValues : examReportCourseMarksColumn
                    .getExamDimensionObtainedValuesList()) {

                if (dimensionMultiplier == 2) {

                    String marks = examDimensionObtainedValues.getMaxMarks() == null ? NULL_MAX_MARKS_VALUE
                            : String.valueOf(Math.round(examDimensionObtainedValues.getMaxMarks()));
                    
                    String maxPercentage = examDimensionObtainedValues.getMaxMarks() == null ? NULL_MAX_MARKS_VALUE: MAX_PERCENTAGE;
                    
                    List<Integer> rgb = EColorUtils.hex2Rgb(totalRowValuesHexColor);
                    if (CollectionUtils.isEmpty(rgb)) {
                        maxMarksRow.add(new CellData(getParagraph(marks).setMultipliedLeading(0.85f), totalMarksRowCellLayoutSetup));
                        obtainedMarksRow.add(new CellData(getParagraph(marks).setMultipliedLeading(0.85f), totalMarksRowCellLayoutSetup));
                        percentMarksRow.add(new CellData(getParagraph(maxPercentage).setMultipliedLeading(0.85f), totalMarksRowCellLayoutSetup));
                    } else {
                        maxMarksRow.add(new CellData(getParagraph(marks, rgb.get(0), rgb.get(1), rgb.get(2)).setMultipliedLeading(0.85f),
                                totalMarksRowCellLayoutSetup));
                        obtainedMarksRow.add(new CellData(getParagraph(marks, rgb.get(0), rgb.get(1), rgb.get(2)).setMultipliedLeading(0.85f),
                                totalMarksRowCellLayoutSetup));
                        percentMarksRow.add(new CellData(getParagraph(maxPercentage, rgb.get(0), rgb.get(1), rgb.get(2)).setMultipliedLeading(0.85f),
                                totalMarksRowCellLayoutSetup));
                    }
                    columnAdded++;
                }

                String maxValue = "";
                String obtainedValue = "";
                String percentValue = "";
                if (showGrade
                        || examReportCourseMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.GRADE
                        || examReportCourseMarksColumn
                        .getExamReportGridColumnType() == ExamReportGridColumnType.EXAM_GRADE
                        || examReportCourseMarksColumn
                        .getExamReportGridColumnType() == ExamReportGridColumnType.SHOW_GRADE_WITH_MARKS_GRID) {
                    String finalGrade = ExamDimensionObtainedValues.getFinalObtainedValueDisplay(examDimensionObtainedValues.getObtainedGrade() == null
                                    ? null : examDimensionObtainedValues.getObtainedGrade().getGradeName(),
                            examDimensionObtainedValues.getAttendanceStatus(), false);
                    obtainedValue =  finalGrade == null ? emptyColumnValue : finalGrade;
                } else if (examReportCourseMarksColumn
                        .getExamReportGridColumnType() == ExamReportGridColumnType.PERCENT) {
                    maxValue = MAX_PERCENTAGE; // As percent max value should be 100?
                    String finalPercentValue = ExamDimensionObtainedValues.getFinalPercentageValueDisplay(examDimensionObtainedValues.getObtainedMarksFraction() == null
                                    ? null : Math.round(examDimensionObtainedValues.getObtainedMarksFraction() * 10000) / 100d,
                            examDimensionObtainedValues.getAttendanceStatus(), false, false);
                    obtainedValue = finalPercentValue == null ? emptyColumnValue : finalPercentValue;
                    percentValue = obtainedValue;
                } else {
                    /**
                     * By default, rounding off marks, only removing round if specified specially
                     */
                    final Double maxMarks = examDimensionObtainedValues.getMaxMarks();
                    final Double minMarks =  examDimensionObtainedValues.getMinMarks();
                    if(examReportCourseMarksColumn.getRoundOff() != null && !examReportCourseMarksColumn.getRoundOff()) {
                        maxValue = examDimensionObtainedValues.getMaxMarks() == null ? NULL_MAX_MARKS_VALUE

                                : String.valueOf(NumberUtils.formatDouble(examDimensionObtainedValues.getMaxMarks(), 1));

                        String finalObtainedValue = ExamDimensionObtainedValues.getFinalObtainedValueDisplay(examDimensionObtainedValues.getObtainedMarks() == null
                                        ? null : String.valueOf(NumberUtils.formatDouble(examDimensionObtainedValues.getObtainedMarks(), 2)),
                                examDimensionObtainedValues.getAttendanceStatus(), minMarks, true, " (F)", maxMarks, "", false);

                        obtainedValue = finalObtainedValue == null ? emptyColumnValue : finalObtainedValue;

                        String finalPercentValue = ExamDimensionObtainedValues.getFinalPercentageValueDisplay(examDimensionObtainedValues.getObtainedMarksFraction() == null
                                        ? null : NumberUtils.formatDouble((examDimensionObtainedValues.getObtainedMarksFraction() * 100d), 2),
                                examDimensionObtainedValues.getAttendanceStatus(), false, false);
                        percentValue = finalPercentValue == null ? emptyColumnValue : finalPercentValue;

                    } else {
                        maxValue = examDimensionObtainedValues.getMaxMarks() == null ? NULL_MAX_MARKS_VALUE
                                : String.valueOf(Math.round(examDimensionObtainedValues.getMaxMarks()));
                        String finalObtainedValue = ExamDimensionObtainedValues.getFinalObtainedValueDisplay(examDimensionObtainedValues.getObtainedMarks() == null
                                        ? null : String.valueOf(Math.round(examDimensionObtainedValues.getObtainedMarks())),
                                examDimensionObtainedValues.getAttendanceStatus(), minMarks, true, " (F)", maxMarks, "", false);

                        obtainedValue = finalObtainedValue == null ? examDimensionObtainedValues.getMaxMarks() == null ? NULL_MAX_MARKS_VALUE :
                                emptyColumnValue : finalObtainedValue;

                        String finalPercentValue = ExamDimensionObtainedValues.getFinalPercentageValueDisplay(examDimensionObtainedValues.getObtainedMarksFraction() == null
                                        ? null : Math.round(examDimensionObtainedValues.getObtainedMarksFraction() * 10000) / 100d,
                                examDimensionObtainedValues.getAttendanceStatus(), false, false);
                        percentValue = finalPercentValue == null ? emptyColumnValue : finalPercentValue;

                    }
                }

                if (maxValue == null) {
                    maxValue = emptyColumnValue;
                }
                if (obtainedValue == null) {
                    obtainedValue = emptyColumnValue;
                }
                if (percentValue == null) {
                    percentValue = emptyColumnValue;
                }

                List<Integer> rgb = EColorUtils.hex2Rgb(totalRowValuesHexColor);
                if (CollectionUtils.isEmpty(rgb)) {
                    maxMarksRow.add(new CellData(getParagraph(maxValue).setMultipliedLeading(0.85f), totalMarksRowCellLayoutSetup));
                    obtainedMarksRow.add(new CellData(getParagraph(obtainedValue).setMultipliedLeading(0.85f), totalMarksRowCellLayoutSetup));
                    percentMarksRow.add(new CellData(getParagraph(percentValue).setMultipliedLeading(0.85f), totalMarksRowCellLayoutSetup));
                } else {
                    maxMarksRow.add(new CellData(getParagraph(maxValue, rgb.get(0), rgb.get(1), rgb.get(2)).setMultipliedLeading(0.85f),
                            totalMarksRowCellLayoutSetup));
                    obtainedMarksRow.add(new CellData(getParagraph(obtainedValue, rgb.get(0), rgb.get(1), rgb.get(2)).setMultipliedLeading(0.85f),
                            totalMarksRowCellLayoutSetup));
                    percentMarksRow.add(new CellData(getParagraph(percentValue, rgb.get(0), rgb.get(1), rgb.get(2)).setMultipliedLeading(0.85f),
                            totalMarksRowCellLayoutSetup));
                }
                columnAdded++;
            }

            if(columnAdded == examCount) {
                maxMarksRow.add(new CellData(getParagraph(EMPTY_TEXT).setMultipliedLeading(0.85f), totalMarksRowCellLayoutSetup.copy().setBorderTop(null).setBorderBottom(null), 1, 1));
                obtainedMarksRow.add(new CellData(getParagraph(EMPTY_TEXT).setMultipliedLeading(0.85f), totalMarksRowCellLayoutSetup.copy().setBorderTop(null).setBorderBottom(null), 1, 1));
                percentMarksRow.add(new CellData(getParagraph(EMPTY_TEXT).setMultipliedLeading(0.85f), totalMarksRowCellLayoutSetup.copy().setBorderTop(null).setBorderBottom(null), 1, 1));
                columnAdded++;
                if(courseType == CourseType.COSCHOLASTIC) {
                    break;
                }
            }

        }
        return new CourseTotalMarksRows(maxMarksRow, obtainedMarksRow, percentMarksRow, null);
    }
    private boolean generateGridCoScholasticContent(DocumentLayoutSetup documentLayoutSetup, Map<CourseType, ExamReportMarksGrid> courseTypeExamReportMarksGrid,
             ExamReportData examReportData, Table headerTable, CellLayoutSetup marksCellLayoutSetup,
             CellLayoutSetup courseCellLayoutSetup, boolean showGrade, List<Integer> rgbCourseNameColorCode,
             int examCount, PdfFont boldFont) {

        CourseType courseType = CourseType.COSCHOLASTIC;
        ExamReportMarksGrid examReportMarksGrid = courseTypeExamReportMarksGrid.get(courseType);

        if (examReportMarksGrid != null) {
            ExamReportCourseStructure examReportCourseStructure = examReportData.getExamReportStructure().getExamReportCourseStructure() == null ? null :
                    CollectionUtils.isEmpty(examReportData.getExamReportStructure().getExamReportCourseStructure()) ? null :
                            examReportData.getExamReportStructure().getExamReportCourseStructure().get(courseType);
            /**
             * code to generate grid header
             */
            return generateGridScholasticContent(documentLayoutSetup, examReportMarksGrid, examReportData
                            .getExamReportStructure().getExamReportStructureMetaData(), courseType, headerTable,
                    marksCellLayoutSetup, courseCellLayoutSetup, showGrade, null,
                    rgbCourseNameColorCode, EMPTY_TEXT, examReportCourseStructure, examCount);

        }
        return false;
    }

    private boolean generateGridScholasticContent(DocumentLayoutSetup documentLayoutSetup,
               ExamReportMarksGrid examReportMarksGrid, ExamReportStructureMetaData examReportStructureMetaData, CourseType courseType,
               Table headerTable, CellLayoutSetup marksCellLayoutSetup, CellLayoutSetup courseCellLayoutSetup,
               boolean showGrade, Set<UUID> coursesToDisplay, List<Integer> rgbCourseNameColorCode, String emptyColumnValue,
               ExamReportCourseStructure examReportCourseStructure, int examCount) {

        if(!CollectionUtils.isEmpty(examReportMarksGrid.getExamReportCourseMarksRows())) {
            for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportMarksGrid.getExamReportCourseMarksRows()) {
                /**
                 * Filtering subjects if not required
                 */
                if (!CollectionUtils.isEmpty(coursesToDisplay)
                        && !coursesToDisplay.contains(examReportCourseMarksRow.getCourse().getCourseId())) {
                    continue;
                }
                addExamCourseRow(documentLayoutSetup, examReportStructureMetaData, courseType, headerTable, marksCellLayoutSetup,
                        courseCellLayoutSetup, examReportCourseMarksRow, showGrade, emptyColumnValue, rgbCourseNameColorCode,
                        examReportCourseStructure, examCount);
            }
            return true;
        }
        return false;
    }

    public void addExamCourseRow(DocumentLayoutSetup documentLayoutSetup,
                                 ExamReportStructureMetaData examReportStructureMetaData, CourseType courseType, Table headerTable,
                                 CellLayoutSetup marksCellLayoutSetup, CellLayoutSetup courseCellLayoutSetup,
                                 ExamReportCourseMarksRow examReportCourseMarksRow, boolean showGrade, String emptyColumnValue,
                                 List<Integer> rgbCourseNameColorCode, ExamReportCourseStructure examReportCourseStructure,
                                 int examCount) {
        List<CellData> row = new ArrayList<>();

        String gridHeadingBackgroundColor = examReportCourseStructure == null ? null : examReportCourseStructure.getGridCoursesBackgroundColor();
        String gridHeadingTextColor = examReportCourseStructure == null ? null : examReportCourseStructure.getGridCoursesTextColor();

        if(!StringUtils.isBlank(gridHeadingBackgroundColor)) {
            courseCellLayoutSetup.setBackgroundColor(gridHeadingBackgroundColor);
        }

        if(!StringUtils.isBlank(gridHeadingTextColor)) {
            rgbCourseNameColorCode = EColorUtils.hex2Rgb(gridHeadingTextColor);
        }

        if(!CollectionUtils.isEmpty(rgbCourseNameColorCode)) {
            row.add(new CellData(getParagraph(examReportCourseMarksRow.getCourse().getCourseName(),
                    rgbCourseNameColorCode.get(0), rgbCourseNameColorCode.get(1), rgbCourseNameColorCode.get(2))
                    .setMultipliedLeading(0.85f), courseCellLayoutSetup.copy().setPaddingLeft(5f)));
        } else {
            row.add(new CellData(getParagraph(examReportCourseMarksRow.getCourse().getCourseName())
                    .setMultipliedLeading(0.85f), courseCellLayoutSetup.copy().setPaddingLeft(5f)));
        }

        int columnAdded = 0;
        for (ExamReportCourseMarksColumn examReportCourseMarksColumn : examReportCourseMarksRow
                .getExamReportCourseMarksColumns()) {
            if(examReportCourseMarksColumn.isHide()) {
                continue;
            }

            int dimensionMultiplier = getDimensionMultiplier(examReportStructureMetaData,
                    examReportCourseMarksColumn.getExamReportGridColumnType(), courseType, showGrade);

            for (ExamDimensionObtainedValues examDimensionObtainedValues : examReportCourseMarksColumn
                    .getExamDimensionObtainedValuesList()) {

                if (dimensionMultiplier == 2) {
                    String marks = courseType == CourseType.SCHOLASTIC ? examDimensionObtainedValues.getMaxMarks() == null ? NULL_MAX_MARKS_VALUE
                            : String.valueOf(Math.round(examDimensionObtainedValues.getMaxMarks())) :
                            "";

                    List<Integer> rgb = EColorUtils.hex2Rgb(examReportCourseMarksColumn.getColorHexCode());
                    if (CollectionUtils.isEmpty(rgb)) {
                        row.add(new CellData(getParagraph(marks).setMultipliedLeading(0.85f), marksCellLayoutSetup));
                    } else {
                        row.add(new CellData(getParagraph(marks, rgb.get(0), rgb.get(1), rgb.get(2)).setMultipliedLeading(0.85f),
                                marksCellLayoutSetup));
                    }
                    columnAdded++;
                }

                String marks;
                if (showGrade
                        || examReportCourseMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.GRADE
                        || examReportCourseMarksColumn
                        .getExamReportGridColumnType() == ExamReportGridColumnType.EXAM_GRADE
                        || examReportCourseMarksColumn
                        .getExamReportGridColumnType() == ExamReportGridColumnType.SHOW_GRADE_WITH_MARKS_GRID) {
                    String finalGrade = ExamDimensionObtainedValues.getFinalObtainedValueDisplay(examDimensionObtainedValues.getObtainedGrade() == null
                                    ? null : examDimensionObtainedValues.getObtainedGrade().getGradeName(),
                            examDimensionObtainedValues.getAttendanceStatus(),
                            examReportCourseMarksColumn.getExamReportGridColumnType() != ExamReportGridColumnType.GRADE);
                    marks =  finalGrade == null ? emptyColumnValue : finalGrade;
                    if(courseType == CourseType.COSCHOLASTIC && examReportCourseMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.GRADE) {
                        marks = null;
                    }
                } else if (examReportCourseMarksColumn
                        .getExamReportGridColumnType() == ExamReportGridColumnType.PERCENT) {
                    String finalPercentValue = ExamDimensionObtainedValues.getFinalPercentageValueDisplay(examDimensionObtainedValues.getObtainedMarksFraction() == null
                                    ? null : Math.round(examDimensionObtainedValues.getObtainedMarksFraction() * 10000) / 100d,
                            examDimensionObtainedValues.getAttendanceStatus(), false, false);
                    marks = finalPercentValue == null ? emptyColumnValue : finalPercentValue;
                    if(courseType == CourseType.COSCHOLASTIC && examReportCourseMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.PERCENT) {
                        marks = null;
                    }
                } else if (examReportCourseMarksColumn
                        .getExamReportGridColumnType() == ExamReportGridColumnType.STATIC || examReportCourseMarksColumn
                        .getExamReportGridColumnType() == ExamReportGridColumnType.COURSE_RANK || examReportCourseMarksColumn
                        .getExamReportGridColumnType() == ExamReportGridColumnType.COURSE_GAIN || examReportCourseMarksColumn
                        .getExamReportGridColumnType() == ExamReportGridColumnType.CLASS_COURSE_AVG || examReportCourseMarksColumn
                        .getExamReportGridColumnType() == ExamReportGridColumnType.CLASS_COURSE_HIGH) {
                    marks = examReportCourseMarksColumn.getColumnValue();
                    if(courseType == CourseType.COSCHOLASTIC) {
                        marks = null;
                    }
                } else if(examReportCourseMarksColumn
                        .getExamReportGridColumnType() == ExamReportGridColumnType.MIN_MARKS){

                    String finalMinValue = examDimensionObtainedValues.getMinMarks() == null
                            ? null : NumberUtils.formatDouble(Math.ceil(examDimensionObtainedValues.getMinMarks()));

                    if(courseType == CourseType.COSCHOLASTIC) {
                        finalMinValue = null;
                    }

                    marks = finalMinValue == null ? emptyColumnValue : finalMinValue;
                }
                else {
                    /**
                     * By default, rounding off marks, only removing round if specified specially
                     */
                    final Double maxMarks = examDimensionObtainedValues.getMaxMarks();
                    final Double minMarks =  examDimensionObtainedValues.getMinMarks();
                    String finalObtainedValue;
                    if(examReportCourseMarksColumn.getRoundOff() != null && !examReportCourseMarksColumn.getRoundOff()) {
                        finalObtainedValue = ExamDimensionObtainedValues.getFinalObtainedValueDisplay(examDimensionObtainedValues.getObtainedMarks() == null
                                        ? null : String.valueOf(NumberUtils.formatDouble(examDimensionObtainedValues.getObtainedMarks(), 2)),
                                examDimensionObtainedValues.getAttendanceStatus(), minMarks, true, " (F)", maxMarks,
                                "", examReportCourseMarksColumn.getExamReportGridColumnType() != ExamReportGridColumnType.SUM);
                    } else {
                        finalObtainedValue = ExamDimensionObtainedValues.getFinalObtainedValueDisplay(examDimensionObtainedValues.getObtainedMarks() == null
                                        ? null : String.valueOf(Math.round(examDimensionObtainedValues.getObtainedMarks())),
                                examDimensionObtainedValues.getAttendanceStatus(), minMarks, true, " (F)", maxMarks,
                                "", examReportCourseMarksColumn.getExamReportGridColumnType() != ExamReportGridColumnType.SUM);

                    }
                    marks = finalObtainedValue == null ? examDimensionObtainedValues.getMaxMarks() == null ? NULL_MAX_MARKS_VALUE :
                            emptyColumnValue : finalObtainedValue;
                    if(courseType == CourseType.COSCHOLASTIC && examReportCourseMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.SUM) {
                        marks = null;
                    }
                }

                if (marks == null) {
                    marks = emptyColumnValue;
                }
                List<Integer> rgb = EColorUtils.hex2Rgb(examReportCourseMarksColumn.getColorHexCode());
                if (CollectionUtils.isEmpty(rgb)) {
                    row.add(new CellData(getParagraph(marks).setMultipliedLeading(0.85f), marksCellLayoutSetup));
                } else {
                    row.add(new CellData(getParagraph(marks, rgb.get(0), rgb.get(1), rgb.get(2)).setMultipliedLeading(0.85f),
                            marksCellLayoutSetup));
                }
                columnAdded++;
            }

            if(columnAdded == examCount) {
                row.add(new CellData(getParagraph(EMPTY_TEXT).setMultipliedLeading(0.85f), marksCellLayoutSetup.copy().setBorderTop(null).setBorderBottom(null), 1, 1));
                columnAdded++;
            }

        }

        addRow(headerTable, documentLayoutSetup, row);
    }

    public List<CellData> generateResultPortion(CourseType courseType, int index, byte[] studentImage, CellLayoutSetup marksCellLayoutSetup,
                                    List<CellData> row, int coscholasticSubjectCount, String resultText, PdfFont boldFont) {
//        if(courseType == CourseType.COSCHOLASTIC && index == 1) {
            Paragraph paragraph = new Paragraph();
            if (studentImage != null) {
                Image img = new Image(ImageDataFactory.create(studentImage));
                img.scaleAbsolute(LOGO_WIDTH, LOGO_HEIGHT);
                paragraph.add(img);
            }
            row.add(new CellData(paragraph, marksCellLayoutSetup.copy(), 6, 3));
            row.add(new CellData(StringUtils.isBlank(resultText) ? EMPTY_TEXT : resultText, marksCellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER).setPdfFont(boldFont).setFontSize(12f),
                    6, 6));
//        }
        return row;
    }

    public int getDimensionMultiplier(ExamReportStructureMetaData examReportStructureMetaData,
                                      ExamReportGridColumnType examReportGridColumnType, CourseType courseType, boolean showGradeOnly) {
        boolean showCourseMaxMarks = courseType == CourseType.SCHOLASTIC
                ? examReportStructureMetaData.isShowScholasticCourseMaxMarks()
                : examReportStructureMetaData.isShowCoScholasticCourseMaxMarks();

        int dimensionMultiplier = showCourseMaxMarks ? 2 : 1;
        if (showGradeOnly
                || examReportGridColumnType == ExamReportGridColumnType.GRADE
                || examReportGridColumnType == ExamReportGridColumnType.PERCENT
                || examReportGridColumnType == ExamReportGridColumnType.STATIC
                || examReportGridColumnType == ExamReportGridColumnType.MIN_MARKS
                || examReportGridColumnType == ExamReportGridColumnType.COURSE_RANK
                || examReportGridColumnType == ExamReportGridColumnType.COURSE_GAIN
                || examReportGridColumnType == ExamReportGridColumnType.CLASS_COURSE_AVG
                || examReportGridColumnType == ExamReportGridColumnType.CLASS_COURSE_HIGH) {
            dimensionMultiplier = 1;
        }
        return dimensionMultiplier;
    }
    private List<CellData> generateGridHeader(ExamReportStructureMetaData examReportStructureMetaData, ExamReportMarksGrid examReportMarksGrid,
            CourseType courseType, CellLayoutSetup headerCellLayoutSetup,
            String subjectColumnTitle, float subjectTitleFontSize, int maxHeight, float subjectColumnWidth,
            boolean showGrade, float examTitleFontSize, String maxMarksTitle, String marksObtainedTitle) {

        List<CellData> firstHeaderCells = new ArrayList<>();
        List<CellData> secondHeaderCells = new ArrayList<>();

        firstHeaderCells.add(new CellData(subjectColumnTitle, headerCellLayoutSetup.copy().setFontSize(subjectTitleFontSize),
                2, 1));

        List<ExamReportMarksColumn<ExamDimensionObtainedValues>> orderedExamReportMarksColumns = getOrderedHeaderColumn(
                examReportMarksGrid, courseType, maxHeight, examReportStructureMetaData, subjectColumnWidth,
                showGrade);

        for (ExamReportMarksColumn<ExamDimensionObtainedValues> examReportMarksColumn : orderedExamReportMarksColumns) {
            int examNameColSpan = CollectionUtils.isEmpty(examReportMarksColumn.getExamDimensionValues()) ?
                    2 : (examReportMarksColumn.getExamDimensionValues().size() * 2);
            if(examReportMarksColumn.isHide()) {
                continue;
            }
            if(!(examReportMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.EXAM ||
                    examReportMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.EXAM_GRADE)) {
                continue;
            }

            String title = examReportMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.EXAM
                    || examReportMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.EXAM_GRADE
                    || examReportMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.SHOW_GRADE_WITH_MARKS_GRID
                    ? StringUtils.isBlank(examReportMarksColumn.getTitle())
                    ? examReportMarksColumn.getExamMetaData().getExamName()
                    : examReportMarksColumn.getTitle()
                    : examReportMarksColumn.getTitle();

            firstHeaderCells.add(new CellData(title, headerCellLayoutSetup.copy().setFontSize(examTitleFontSize - 1f), 1, examNameColSpan));
            for(int i = 0; i < examNameColSpan / 2; i++) {
                secondHeaderCells.add(new CellData(maxMarksTitle, headerCellLayoutSetup.copy().setFontSize(examTitleFontSize - 1.5f), 1, 1));
                secondHeaderCells.add(new CellData(marksObtainedTitle, headerCellLayoutSetup.copy().setFontSize(examTitleFontSize - 1.5f), 1, 1));
            }
        }

        firstHeaderCells.add(new CellData(EMPTY_TEXT, headerCellLayoutSetup.copy().setFontSize(examTitleFontSize - 1f)
                .setBorderTop(null).setBorderBottom(null), 1, 1));

        secondHeaderCells.add(new CellData(EMPTY_TEXT, headerCellLayoutSetup.copy().setFontSize(examTitleFontSize - 1.5f)
                .setBorderTop(null).setBorderBottom(null), 1, 1));


        firstHeaderCells.add(new CellData("CUMULATIVE PERFORMANCE", headerCellLayoutSetup.copy().setFontSize(examTitleFontSize - 1f), 1, 9));

        secondHeaderCells.add(new CellData("MAX", headerCellLayoutSetup.copy().setFontSize(examTitleFontSize - 1.5f), 1, 1));
        secondHeaderCells.add(new CellData("OBT", headerCellLayoutSetup.copy().setFontSize(examTitleFontSize - 1.5f), 1, 1));
        secondHeaderCells.add(new CellData("MIN", headerCellLayoutSetup.copy().setFontSize(examTitleFontSize - 1.5f), 1, 1));
        secondHeaderCells.add(new CellData("GRADE", headerCellLayoutSetup.copy().setFontSize(examTitleFontSize - 1.5f), 1, 1));
        secondHeaderCells.add(new CellData("%", headerCellLayoutSetup.copy().setFontSize(examTitleFontSize - 1.5f), 1, 1));
        secondHeaderCells.add(new CellData("RANK", headerCellLayoutSetup.copy().setFontSize(examTitleFontSize - 1.5f), 1, 1));
        secondHeaderCells.add(new CellData("GAIN %", headerCellLayoutSetup.copy().setFontSize(examTitleFontSize - 1.5f), 1, 1));
        secondHeaderCells.add(new CellData("CL AVG", headerCellLayoutSetup.copy().setFontSize(examTitleFontSize - 1.5f), 1, 1));
        secondHeaderCells.add(new CellData("CL HIGH", headerCellLayoutSetup.copy().setFontSize(examTitleFontSize - 1.5f), 1, 1));


        List<CellData> cells = new ArrayList<>();
        cells.addAll(firstHeaderCells);
        cells.addAll(secondHeaderCells);
        return cells;
    }

    public float[] getColumnWidth(ExamReportMarksGrid examReportMarksGrid, float subjectColumnWidth, int examCount) {
        List<Float> columnWidthsArrayList = new ArrayList<>();

        float widthOfEmptyPart = 0.005f;
        float remainingPart = 1 - widthOfEmptyPart - subjectColumnWidth;
        int noOFCumulativeColumns = 9;
        int totalColumns = examCount + noOFCumulativeColumns;//No including empty and course name column
        float widthOfEachColumn = remainingPart / (totalColumns * 1f);
        /**
         * course Column
         */
        columnWidthsArrayList.add(subjectColumnWidth);
        for (int i = 0; i < examCount; i++) {
            columnWidthsArrayList.add(widthOfEachColumn);
        }
        /**
         * empty part
         */
        columnWidthsArrayList.add(widthOfEmptyPart);
        /**
         * total &  CumulativeColumns
         */
        for (int i = 0; i < noOFCumulativeColumns; i++) {
            columnWidthsArrayList.add(widthOfEachColumn);
        }
        float[] columnWidths = new float[columnWidthsArrayList.size()];

        // ArrayList to Array Conversion
        for (int i = 0; i < columnWidthsArrayList.size(); i++) {
            columnWidths[i] = columnWidthsArrayList.get(i);
        }
        return columnWidths;
    }

    @Override
    public DocumentOutput generateClassResultReport(Institute institute, String reportType, List<ExamReportData> examReportDataList,
                                                    String documentName, StudentManager studentManager, int studentPerPage) {
        try {
            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4);
            Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
            PdfFont regularFont = getCambriaFont();
            PdfFont boldFont = getCambriaBoldFont();

            int instituteId = institute.getInstituteId();
            ExamReportCardLayoutData examReportCardLayoutData = new ExamReportCardLayoutData(document, documentLayoutSetup, null, null, DEFAULT_FONT_SIZE,
                    DEFAULT_BORDER_WIDTH, LOGO_WIDTH, LOGO_HEIGHT, LogoProvider.INSTANCE.getLogo(instituteId));

            CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
            marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(DEFAULT_FONT_SIZE)
                    .setBorder(new SolidBorder(DEFAULT_BORDER_WIDTH)).setTextAlignment(TextAlignment.CENTER);

            CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
                    .setTextAlignment(TextAlignment.LEFT);

            String instituteName = institute.getInstituteName().toUpperCase();
            String sessionName = "";
            if(!CollectionUtils.isEmpty(examReportDataList)) {
                sessionName = "Session : " + examReportDataList.get(0).getStudentLite().getStudentSessionData().getShortYearDisplayName();
            }
            generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName,
                    sessionName, marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));

            GridConfigs gridConfigs = new GridConfigs(new GridHeaderConfigs(false), new GridTotalMarksRowConfigs(
                    true, false, true, true, false,true, true,
                    true, true, true, true, "GRAND TOTAL", "", "PERCENTAGE (%)", "TEST DATE",
                    "CUML ATTENDANCE", "RESULT DATE", "DIVISION", "RANK", "RESULT"), false);

            sortClassExamReports(examReportDataList);
            int pageNumber = 1;
            int totalStudents = getStudentCountInClass(institute.getInstituteId(), examReportDataList.get(0).getStudentLite(), studentManager);
            for (ExamReportData examReportData : examReportDataList) {
                int noOfCourses = 0;
                if(examReportData.getCourseTypeExamReportMarksGrid() != null &&
                        examReportData.getCourseTypeExamReportMarksGrid().get(CourseType.SCHOLASTIC) != null &&
                        !CollectionUtils.isEmpty(examReportData.getCourseTypeExamReportMarksGrid().get(CourseType.SCHOLASTIC).getExamReportCourseMarksRows())) {
                    noOfCourses += examReportData.getCourseTypeExamReportMarksGrid().get(CourseType.SCHOLASTIC).getExamReportCourseMarksRows().size();
                }
                if(examReportData.getCourseTypeExamReportMarksGrid() != null &&
                        examReportData.getCourseTypeExamReportMarksGrid().get(CourseType.COSCHOLASTIC) != null &&
                        !CollectionUtils.isEmpty(examReportData.getCourseTypeExamReportMarksGrid().get(CourseType.COSCHOLASTIC).getExamReportCourseMarksRows())) {
                    noOfCourses += examReportData.getCourseTypeExamReportMarksGrid().get(CourseType.COSCHOLASTIC).getExamReportCourseMarksRows().size();
                }

                generateClassReportDataStudentDetails(documentLayoutSetup, document, courseCellLayoutSetup, examReportData.getStudentLite());
                generateCustomScholasticMarksGrid(examReportCardLayoutData, examReportData, reportType, gridConfigs,
                        "Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType),
                        instituteId, examReportData.getStudentLite().getStudentId(), studentManager, noOfCourses, totalStudents);
                generateClassReportDataStudentResult(documentLayoutSetup, document, examReportData, courseCellLayoutSetup);
                if (pageNumber % studentPerPage == 0) {
                    document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                    generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName, sessionName,
                            marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));
                } else {
                    addBlankLine(document, false, 1);
                }
                pageNumber++;
            }
            document.close();
            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
                    reportType, e);
        }
        return null;
    }
}
