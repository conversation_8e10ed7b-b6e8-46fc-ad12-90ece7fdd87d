package com.embrate.cloud.pdf.admission.form;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;

/**
 * <AUTHOR>
 */
public class StaticAdmissionFormGenerator10170_10171 extends GlobalStaticAdmissionFormGenerator {

    public StaticAdmissionFormGenerator10170_10171(AssetProvider assetProvider) {
                super(assetProvider);
                //TODO Auto-generated constructor stub
        }

private static final Logger logger = LogManager.getLogger(StaticAdmissionFormGenerator10170_10171.class);

    @Override
    public DocumentOutput generateStaticAdmissionForm(Institute institute, AcademicSession academicSession, String documentName) {
        try {
            float squareBorderMargin = 8f;
            float borderInnerGap = 2f;

            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

            DocumentLayoutData admissionFormLayoutData = generateStaticAdmissionFormLayoutData(institute, documentOutput, 70f,
                    70f);
            Document document = admissionFormLayoutData.getDocument();
            int index = 1;

            addBlankLine(document, false, 1);
            generatePageHeader(admissionFormLayoutData, institute, academicSession, 20f,
                    admissionFormLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.90f,
                    18f);
            generateMetadata(admissionFormLayoutData, squareBorderMargin, borderInnerGap, 1);

            addBlankLine(document, true, 1);

            index = generateBasicInformationPage(admissionFormLayoutData, index);

            addBlankLine(document, true, 1);

            index = generateFamilyInformationPage(admissionFormLayoutData, index);

            addBlankLine(document, true, 1);

            index = generatePrevSchoolInformationPage(admissionFormLayoutData, index);

            addBlankLine(document, true, 1);

            index = generateTransportInformationPage(admissionFormLayoutData, index);

            generateSignatureBox(admissionFormLayoutData, squareBorderMargin, borderInnerGap);

            document.close();

            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating admission form for institute {} ",
                    institute.getInstituteId(), e);
        }
        return null;
    }

    protected int generateBasicInformationPage(DocumentLayoutData documentLayoutData, int index) throws IOException {
        Document document = documentLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
        PdfFont boldFont = documentLayoutData.getBoldFont();

        byte[]  image = documentLayoutData.getImageFrame();
        generateImage(document, documentLayoutSetup, image, 120, 130,
                documentLayoutData.getDocumentLayoutSetup().getPageSize().getWidth() - 140f,
                documentLayoutSetup.getPageSize().getHeight() * 0.70f);

        int singleContentColumn = 1;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup singleCellLayoutSetup = new CellLayoutSetup();
        singleCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.LEFT);

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("BASIC INFORMATION").setUnderline()),
                singleCellLayoutSetup.copy().setFontSize(12f).setPdfFont(boldFont));

        document.add(table);
        addBlankLine(document, false, 1);

        table = getPDFTable(documentLayoutSetup, singleContentColumn);

        addRow(table, documentLayoutSetup,
                Arrays.asList(
                        getAdmissionFormKeyValueParagraph(index++, "Student Name : ", EMPTY_VALUE)),
                singleCellLayoutSetup);

        addRow(table, documentLayoutSetup,
                Arrays.asList(getAdmissionFormKeyValueParagraph(index++, "Admission in Class : ", EMPTY_VALUE)),
                singleCellLayoutSetup);

        addRow(table, documentLayoutSetup, Arrays.asList(getAdmissionFormKeyValueParagraph(index++,
                "SR No. : ", EMPTY_VALUE, false)), singleCellLayoutSetup);

        addRow(table, documentLayoutSetup,
                Arrays.asList(getAdmissionFormKeyValueParagraph(index++, "Admission Date : ", EMPTY_VALUE)),
                singleCellLayoutSetup);

        document.add(table);

        CellLayoutSetup threeCellLayoutSetup = new CellLayoutSetup();
        threeCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.LEFT);

        table = getPDFTable(documentLayoutSetup, 3);

        addRow(table, documentLayoutSetup,
                Arrays.asList(
                        getAdmissionFormKeyValueParagraph(index++, "Gender : ", EMPTY_VALUE),
                        getAdmissionFormKeyValueParagraph(index++, "Religion : ", EMPTY_VALUE),
                        getAdmissionFormKeyValueParagraph(index++, "Category : ", EMPTY_VALUE)),
                threeCellLayoutSetup);
        document.add(table);

        table = getPDFTable(documentLayoutSetup, 2);

        addRow(table, documentLayoutSetup,
                Arrays.asList(
                        getAdmissionFormKeyValueParagraph(index++, "Date Of Birth : ", EMPTY_VALUE),
                        getAdmissionFormKeyValueParagraph(index++, "In Words : ", EMPTY_VALUE)),
                threeCellLayoutSetup);

        document.add(table);

        table = getPDFTable(documentLayoutSetup, 3);

        addRow(table, documentLayoutSetup, Arrays.asList(
                getAdmissionFormKeyValueParagraph(index++, "Permanent Address : ", EMPTY_VALUE),
                new Paragraph(""),
                new Paragraph("")), singleCellLayoutSetup);

        document.add(table);
        table = getPDFTable(documentLayoutSetup, 3);

        addRow(table, documentLayoutSetup,
                Arrays.asList(getAdmissionFormKeyValueParagraph(index++, "City : ", EMPTY_VALUE),
                        getAdmissionFormKeyValueParagraph(index++, "State : ", EMPTY_VALUE),
                        getAdmissionFormKeyValueParagraph(index++, "Pincode : ", EMPTY_VALUE)),
                threeCellLayoutSetup);

        addRow(table, documentLayoutSetup, Arrays.asList(
                        getAdmissionFormKeyValueParagraph(index++, "Aadhar Number : ", EMPTY_VALUE),
                        getAdmissionFormKeyValueParagraph(index++, "Whatsapp Number : ", EMPTY_VALUE),
                        getAdmissionFormKeyValueParagraph(index++, "Mobile Number :  ", EMPTY_VALUE)),
                threeCellLayoutSetup);

        document.add(table);

        return index;
    }

    protected int generateFamilyInformationPage(DocumentLayoutData documentLayoutData,
                                                int index) throws IOException {
        Document document = documentLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
        PdfFont boldFont = documentLayoutData.getBoldFont();

        int singleContentColumn = 1;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup singleCellLayoutSetup = new CellLayoutSetup();
        singleCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.LEFT);

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("FAMILY INFORMATION").setUnderline()),
                singleCellLayoutSetup.copy().setFontSize(12f).setPdfFont(boldFont));

        document.add(table);
        addBlankLine(document, false, 1);

        table = getPDFTable(documentLayoutSetup, 2);

        CellLayoutSetup threeCellLayoutSetup = new CellLayoutSetup();
        threeCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.LEFT);

        table = getPDFTable(documentLayoutSetup, 2);

        addRow(table, documentLayoutSetup,
                Arrays.asList(
                        getAdmissionFormKeyValueParagraph(index++, "Father's Name : ", EMPTY_VALUE),
                        getAdmissionFormKeyValueParagraph(index++, "Mother's Name : ", EMPTY_VALUE)),
                threeCellLayoutSetup);

        addRow(table, documentLayoutSetup,
                Arrays.asList(
                        getAdmissionFormKeyValueParagraph(index++, "Father's Contact : ", EMPTY_VALUE),
                        getAdmissionFormKeyValueParagraph(index++, "Mother's Contact : ", EMPTY_VALUE)),
                threeCellLayoutSetup);

        document.add(table);

        return index;
    }

    protected int generatePrevSchoolInformationPage(DocumentLayoutData documentLayoutData, int index) throws IOException {
        Document document = documentLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
        PdfFont boldFont = documentLayoutData.getBoldFont();

        int singleContentColumn = 1;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup singleCellLayoutSetup = new CellLayoutSetup();
        singleCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.LEFT);

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("PREVIOUS SCHOOL INFORMATION").setUnderline()),
                singleCellLayoutSetup.copy().setFontSize(12f).setPdfFont(boldFont));

        document.add(table);
        addBlankLine(document, false, 1);

        table = getPDFTable(documentLayoutSetup, 2);

        CellLayoutSetup threeCellLayoutSetup = new CellLayoutSetup();
        threeCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.LEFT);

        table = getPDFTable(documentLayoutSetup, 2);

        addRow(table, documentLayoutSetup,
                Arrays.asList(
                        getAdmissionFormKeyValueParagraph(index++, "School Name : ", EMPTY_VALUE),
                        getAdmissionFormKeyValueParagraph(index++, "Previous Class : ", EMPTY_VALUE)),
                threeCellLayoutSetup);

        addRow(table, documentLayoutSetup,
                Arrays.asList(
                        getAdmissionFormKeyValueParagraph(index++, "Medium : ", EMPTY_VALUE),
                        getAdmissionFormKeyValueParagraph(index++, "Result : ", EMPTY_VALUE)),
                threeCellLayoutSetup);

        document.add(table);

        return index;
    }

    protected int generateTransportInformationPage(DocumentLayoutData documentLayoutData, int index) throws IOException {
        Document document = documentLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
        PdfFont boldFont = documentLayoutData.getBoldFont();

        int singleContentColumn = 1;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup singleCellLayoutSetup = new CellLayoutSetup();
        singleCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.LEFT);

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("TRANSPORT INFORMATION").setUnderline()),
                singleCellLayoutSetup.copy().setFontSize(12f).setPdfFont(boldFont));

        document.add(table);
        addBlankLine(document, false, 1);

        table = getPDFTable(documentLayoutSetup, 2);

        CellLayoutSetup threeCellLayoutSetup = new CellLayoutSetup();
        threeCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.LEFT);

        table = getPDFTable(documentLayoutSetup, 2);

        addRow(table, documentLayoutSetup, Arrays.asList(
                        getAdmissionFormKeyValueParagraph(index++, "Bus/VAN Facility : ", EMPTY_VALUE),
                        getAdmissionFormKeyValueParagraph(index++, "Station/Stop : ", EMPTY_VALUE)),
                threeCellLayoutSetup);

        document.add(table);

        table = getPDFTable(documentLayoutSetup, 1);

        addRow(table, documentLayoutSetup,
                Arrays.asList(getAdmissionFormKeyValueParagraph(index++, "Route : ", EMPTY_VALUE)),
                threeCellLayoutSetup);

        document.add(table);

        return index;
    }

}
