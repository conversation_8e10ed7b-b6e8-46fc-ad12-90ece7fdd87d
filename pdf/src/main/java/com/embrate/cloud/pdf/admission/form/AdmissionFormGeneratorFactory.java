package com.embrate.cloud.pdf.admission.form;

import com.embrate.cloud.core.lib.utility.AssetProvider;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 *
 * <AUTHOR>
 *
 */
public class AdmissionFormGeneratorFactory {

	private static final Map<Integer, AdmissionFormGenerator> INSTITUTE_ADMISSION_FORM_GENERATOR = new HashMap<>();
	private static final Map<Integer, StaticAdmissionFormGenerator> INSTITUTE_STATIC_ADMISSION_FORM_GENERATOR = new HashMap<>();
	private static final Set<Integer> INSTITUTE_SAME_STATIC_ADMISSION_FORM_GENERATOR = new HashSet<>();
	private final AssetProvider assetProvider;

	public  AdmissionFormGeneratorFactory(AssetProvider assetProvider) {
		this.assetProvider = assetProvider;
		initializeGenerators();
	}


	private void initializeGenerators() {
		//For prod 702 institute (ghy distributor)
		INSTITUTE_ADMISSION_FORM_GENERATOR.put(702, new AdmissionFormGenerator10115(assetProvider));
		INSTITUTE_ADMISSION_FORM_GENERATOR.put(10010, new GlobalTwoPageAdmissionFormGenerator(assetProvider));
		INSTITUTE_ADMISSION_FORM_GENERATOR.put(10030, new AdmissionFormGenerator10030(assetProvider));
		INSTITUTE_ADMISSION_FORM_GENERATOR.put(10031, new AdmissionFormGenerator10030(assetProvider));
		INSTITUTE_ADMISSION_FORM_GENERATOR.put(10115, new AdmissionFormGenerator10115(assetProvider));
		INSTITUTE_ADMISSION_FORM_GENERATOR.put(10130, new AdmissionFormGenerator10130(assetProvider));
		INSTITUTE_ADMISSION_FORM_GENERATOR.put(10131, new AdmissionFormGenerator10130(assetProvider));
		INSTITUTE_ADMISSION_FORM_GENERATOR.put(10135, new AdmissionFormGenerator10115(assetProvider));
		INSTITUTE_ADMISSION_FORM_GENERATOR.put(10145, new AdmissionFormGenerator10130(assetProvider));
		INSTITUTE_ADMISSION_FORM_GENERATOR.put(10146, new AdmissionFormGenerator10130(assetProvider));
		INSTITUTE_ADMISSION_FORM_GENERATOR.put(10150, new AdmissionFormGenerator10130(assetProvider));
		INSTITUTE_ADMISSION_FORM_GENERATOR.put(10180, new AdmissionFormGenerator10180(assetProvider));
		INSTITUTE_ADMISSION_FORM_GENERATOR.put(10190, new AdmissionFormGenerator10190(assetProvider));
		INSTITUTE_ADMISSION_FORM_GENERATOR.put(10230, new AdmissionFormGenerator10230(assetProvider));
		INSTITUTE_ADMISSION_FORM_GENERATOR.put(10231, new AdmissionFormGenerator10230(assetProvider));
		INSTITUTE_ADMISSION_FORM_GENERATOR.put(10232, new AdmissionFormGenerator10230(assetProvider));
		INSTITUTE_ADMISSION_FORM_GENERATOR.put(10240, new AdmissionFormGenerator10240(assetProvider));
		INSTITUTE_ADMISSION_FORM_GENERATOR.put(10241, new AdmissionFormGenerator10240(assetProvider));

		//For prod 702 institute (ghy distributor)
		INSTITUTE_STATIC_ADMISSION_FORM_GENERATOR.put(702, new StaticAdmissionFormGenerator10135(assetProvider));
		INSTITUTE_STATIC_ADMISSION_FORM_GENERATOR.put(10030, new StaticAdmissionFormGenerator10030(assetProvider));
		INSTITUTE_STATIC_ADMISSION_FORM_GENERATOR.put(10031, new StaticAdmissionFormGenerator10030(assetProvider));
		INSTITUTE_STATIC_ADMISSION_FORM_GENERATOR.put(10115, new StaticAdmissionFormGenerator10115(assetProvider));
		INSTITUTE_STATIC_ADMISSION_FORM_GENERATOR.put(10130, new StaticAdmissionFormGenerator10130(assetProvider));
		INSTITUTE_STATIC_ADMISSION_FORM_GENERATOR.put(10131, new StaticAdmissionFormGenerator10130(assetProvider));
		INSTITUTE_STATIC_ADMISSION_FORM_GENERATOR.put(10135, new StaticAdmissionFormGenerator10135(assetProvider));
		INSTITUTE_STATIC_ADMISSION_FORM_GENERATOR.put(10145, new StaticAdmissionFormGenerator10130(assetProvider));
		INSTITUTE_STATIC_ADMISSION_FORM_GENERATOR.put(10146, new StaticAdmissionFormGenerator10130(assetProvider));
		INSTITUTE_STATIC_ADMISSION_FORM_GENERATOR.put(10150, new StaticAdmissionFormGenerator10130(assetProvider));
		INSTITUTE_STATIC_ADMISSION_FORM_GENERATOR.put(10170, new StaticAdmissionFormGenerator10170_10171(assetProvider));
		INSTITUTE_STATIC_ADMISSION_FORM_GENERATOR.put(10171, new StaticAdmissionFormGenerator10170_10171(assetProvider));
		INSTITUTE_STATIC_ADMISSION_FORM_GENERATOR.put(10190, new StaticAdmissionFormGenerator10190(assetProvider));
		INSTITUTE_STATIC_ADMISSION_FORM_GENERATOR.put(10240, new StaticAdmissionFormGenerator10240(assetProvider));
		INSTITUTE_STATIC_ADMISSION_FORM_GENERATOR.put(10241, new StaticAdmissionFormGenerator10240(assetProvider));

		INSTITUTE_SAME_STATIC_ADMISSION_FORM_GENERATOR.add(10010);
		INSTITUTE_SAME_STATIC_ADMISSION_FORM_GENERATOR.add(10180);
		INSTITUTE_SAME_STATIC_ADMISSION_FORM_GENERATOR.add(10230);
		INSTITUTE_SAME_STATIC_ADMISSION_FORM_GENERATOR.add(10231);
		INSTITUTE_SAME_STATIC_ADMISSION_FORM_GENERATOR.add(10232);

	}

	/**
	 * If static form layout is different from corresponding filled admission form then use static form
	 * generator else just use regular admission form generator with flag to control to dislay data.
	 *
	 * @param instituteId
	 * @return
	 */
	public boolean generateSpecificStaticForm(int instituteId){
		return !INSTITUTE_SAME_STATIC_ADMISSION_FORM_GENERATOR.contains(instituteId);
	}

	public AdmissionFormGenerator getAdmissionFormGenerator(int instituteId) {
		if (!INSTITUTE_ADMISSION_FORM_GENERATOR.containsKey(instituteId)) {
			return new GlobalAdmissionFormGenerator(assetProvider);
		}
		return INSTITUTE_ADMISSION_FORM_GENERATOR.get(instituteId);
	}

	public StaticAdmissionFormGenerator getStaticAdmissionFormGenerator(int instituteId) {
		if (!INSTITUTE_STATIC_ADMISSION_FORM_GENERATOR.containsKey(instituteId)) {
			return new GlobalStaticAdmissionFormGenerator(assetProvider);
		}
		return INSTITUTE_STATIC_ADMISSION_FORM_GENERATOR.get(instituteId);
	}

}
