package com.embrate.cloud.pdf.admission.form;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import org.apache.commons.text.WordUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentBasicInfo;
import com.lernen.cloud.core.api.transport.StudentTransportDetails;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;

import com.lernen.cloud.core.utils.EColorUtils;

/**
 * 
 * <AUTHOR>
 *
 */
public class AdmissionFormGenerator10030 extends AdmissionFormGenerator {

	public AdmissionFormGenerator10030(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(AdmissionFormGenerator10030.class);

	@Override
	public DocumentOutput generateAdmissionForm(StudentManager studentManager, Institute institute, AcademicSession academicSession, StudentTransportDetails studentTransportDetails, boolean publishStudentData, Student student,
                                                String documentName) {
		try {
			float squareBorderMargin = 8f;
			float borderInnerGap = 2f;

			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

			DocumentLayoutData admissionFormLayoutData = generateAdmissionFormLayoutData(institute, documentOutput, 60f,
					60f);
			Document document = admissionFormLayoutData.getDocument();
			int index = 1;

			addBlankLine(document, false, 1);
			generatePageHeader(admissionFormLayoutData, institute,
					student.getStudentAcademicSessionInfoResponse().getAcademicSession());
			generateMetadata(admissionFormLayoutData, squareBorderMargin, borderInnerGap);
			addBlankLine(document, true, 1);

			index = generateBasicInformationPage(admissionFormLayoutData, studentManager, institute, student, index);

			addBlankLine(document, true, 1);

			index = generateFamilyInformationPage(admissionFormLayoutData, institute, student, index, true);

			addBlankLine(document, true, 1);

			index = generatePrevSchoolInformationPage(admissionFormLayoutData, institute, student, index);

			addBlankLine(document, true, 1);

			index = generateDocumentsInformationPage(admissionFormLayoutData, institute, student, index);

			generateSignatureBox(admissionFormLayoutData, squareBorderMargin, borderInnerGap);

			document.close();

			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating admission form for institute {}, student id {}",
					institute.getInstituteId(), student.getStudentId(), e);
		}
		return null;
	}

	protected void generatePageHeader(DocumentLayoutData documentLayoutData, Institute institute,
			AcademicSession academicSession) throws IOException {
		Document document = documentLayoutData.getDocument();
		DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
		PdfFont boldFont = documentLayoutData.getBoldFont();

		generateDynamicImageProvider(documentLayoutData, 20f, documentLayoutSetup.getPageSize().getHeight() * 0.91f, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

		int singleContentColumn = 1;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(documentLayoutData.getRegularFont()).setFontSize(12f).setTextAlignment(TextAlignment.CENTER);

		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph(institute.getInstituteName().toUpperCase(), 102, 51, 0)),
				cellLayoutSetup.copy().setFontSize(25f).setPdfFont(boldFont));
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Vill & P.O - Piyawali Tajpur, Dadri, Gautam Buddh Nagar")),
				cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Phone : **********/**********, Email : <EMAIL>")),
				cellLayoutSetup);
		document.add(table);

		table = getPDFTable(documentLayoutSetup, singleContentColumn);

		CellLayoutSetup singleLineBorderLayout = new CellLayoutSetup();
		singleLineBorderLayout.setPdfFont(boldFont).setFontSize(14f).setTextAlignment(TextAlignment.CENTER)
				.setBorderBottom(new SolidBorder(2f));

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("")), singleLineBorderLayout);
		document.add(table);

		addBlankLine(document, false, 1);

		table = getPDFTable(documentLayoutSetup, new float[] { 0.3f, 0.4f, 0.3f });
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(EMPTY_TEXT, cellLayoutSetup),
				new CellData(getParagraph("ADMISSION FORM (" + academicSession.getShortYearDisplayName() + ")", 255,
						255, 255), cellLayoutSetup.copy().setFontSize(14f).setBackgroundColor(EColorUtils.BLACK_COLOR_HEX_CODE).setPdfFont(boldFont)),
				new CellData(EMPTY_TEXT, cellLayoutSetup)));

		document.add(table);

//		addBlankLine(document, true, 1);

	}

	protected int generateBasicInformationPage(DocumentLayoutData documentLayoutData, StudentManager studentManager,
			Institute institute, Student student, int index) throws IOException {
		Document document = documentLayoutData.getDocument();
		DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
		PdfFont boldFont = documentLayoutData.getBoldFont();
		StudentBasicInfo studentBasicInfo = student.getStudentBasicInfo();

		byte[] image = getStudentImage(institute.getInstituteId(), student, studentManager);
		if (image == null) {
			image = documentLayoutData.getImageFrame();
		}
		generateImage(document, documentLayoutSetup, image, 80, 90,
				documentLayoutData.getDocumentLayoutSetup().getPageSize().getWidth() - 120f,
				documentLayoutSetup.getPageSize().getHeight() * 0.70f);

		int singleContentColumn = 1;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

		CellLayoutSetup singleCellLayoutSetup = new CellLayoutSetup();
		singleCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.LEFT);

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("BASIC INFORMATION")),
				singleCellLayoutSetup.copy().setFontSize(14f).setPdfFont(boldFont));

		document.add(table);
		addBlankLine(document, false, 1);

		table = getPDFTable(documentLayoutSetup, singleContentColumn);

		addRow(table, documentLayoutSetup,
				Arrays.asList(
						getAdmissionFormKeyValueParagraph(index++, "Student Name : ", studentBasicInfo.getName())),
				singleCellLayoutSetup);

		addRow(table, documentLayoutSetup,
				Arrays.asList(getAdmissionFormKeyValueParagraph(index++, "Class : ",
						student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayName())),
				singleCellLayoutSetup);

		addRow(table, documentLayoutSetup, Arrays.asList(getAdmissionFormKeyValueParagraph(index++,
				"Admission Number : ", studentBasicInfo.getAdmissionNumber(), false)), singleCellLayoutSetup);

		addRow(table, documentLayoutSetup,
				Arrays.asList(getAdmissionFormKeyValueParagraph(index++, "Admission Date : ",
						studentBasicInfo.getAdmissionDate() == null || studentBasicInfo.getAdmissionDate() <= 0 ? ""
								: DateUtils.getFormattedDate(studentBasicInfo.getAdmissionDate()))),
				singleCellLayoutSetup);

		document.add(table);

		CellLayoutSetup threeCellLayoutSetup = new CellLayoutSetup();
		threeCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.LEFT);

		table = getPDFTable(documentLayoutSetup, 3);

		addRow(table, documentLayoutSetup,
				Arrays.asList(
						getAdmissionFormKeyValueParagraph(index++, "Gender : ",
								studentBasicInfo.getGender() == null ? EMPTY_VALUE
										: WordUtils.capitalizeFully(studentBasicInfo.getGender().name())),
						getAdmissionFormKeyValueParagraph(index++, "Date Of Birth : ",
								studentBasicInfo.getDateOfBirth() == null || studentBasicInfo.getDateOfBirth() <= 0
										? EMPTY_VALUE
										: DateUtils.getFormattedDate(studentBasicInfo.getDateOfBirth())),
						getAdmissionFormKeyValueParagraph(index++, "Birth Place : ", studentBasicInfo.getBirthPlace())),
				threeCellLayoutSetup);

		addRow(table, documentLayoutSetup,
				Arrays.asList(getAdmissionFormKeyValueParagraph(index++, "Religion : ", studentBasicInfo.getReligion()),
						getAdmissionFormKeyValueParagraph(index++, "Category : ",
								studentBasicInfo.getUserCategory() == null ? EMPTY_VALUE
										: studentBasicInfo.getUserCategory().name()),
						getAdmissionFormKeyValueParagraph(index++, "Specially Abled : ",
								studentBasicInfo.getSpeciallyAbledDisplay())),
				threeCellLayoutSetup);

		document.add(table);

		table = getPDFTable(documentLayoutSetup, 1);

		addRow(table, documentLayoutSetup, Arrays.asList(getAdmissionFormKeyValueParagraph(index++,
				"Permanent Address : ", studentBasicInfo.getPermanentAddress())), singleCellLayoutSetup);
		document.add(table);

		table = getPDFTable(documentLayoutSetup, 3);
		addRow(table, documentLayoutSetup,
				Arrays.asList(getAdmissionFormKeyValueParagraph(index++, "City : ", studentBasicInfo.getPermanentCity()),
						getAdmissionFormKeyValueParagraph(index++, "State : ", studentBasicInfo.getPermanentState()),
						getAdmissionFormKeyValueParagraph(index++, "Zipcode : ", studentBasicInfo.getPermanentZipcode())),
				threeCellLayoutSetup);

		document.add(table);
		table = getPDFTable(documentLayoutSetup, 2);

		addRow(table, documentLayoutSetup, Arrays.asList(
				getAdmissionFormKeyValueParagraph(index++, "Primary Contact : ",
						studentBasicInfo.getPrimaryContactNumber()),
				getAdmissionFormKeyValueParagraph(index++, "Aadhar Number : ", studentBasicInfo.getAadharNumber())),
				threeCellLayoutSetup);

		document.add(table);

		return index;
	}

	@Override
	public DocumentOutput generateBulkAdmissionForm(StudentManager studentManager, Institute institute, AcademicSession academicSession, Map<UUID, StudentTransportDetails> studentTransportDetailsMap, boolean publishStudentData, List<Student> studentList, String documentName) {
		return null;
	}
}
