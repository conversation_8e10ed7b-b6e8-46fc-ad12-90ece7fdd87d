package com.embrate.cloud.pdf.admission.form;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.*;
import com.lernen.cloud.core.api.transport.StudentTransportDetails;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.StringConstants;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.pdf.base.PDFGenerator;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.WordUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public abstract class AdmissionFormGenerator extends PDFGenerator {
    public AdmissionFormGenerator(AssetProvider assetProvider) {
                super(assetProvider);
                //TODO Auto-generated constructor stub
        }

protected static final String EMPTY_VALUE = "";
    private static final Logger logger = LogManager.getLogger(AdmissionFormGenerator.class);

    public abstract DocumentOutput generateAdmissionForm(StudentManager studentManager, Institute institute,
                                                         AcademicSession academicSession, StudentTransportDetails studentTransportDetails, boolean publishStudentData, Student student, String documentName);

    public abstract DocumentOutput generateBulkAdmissionForm(StudentManager studentManager, Institute institute,
                                                             AcademicSession academicSession, Map<UUID, StudentTransportDetails> studentTransportDetailsMap,
                                                             boolean publishStudentData, List<Student> studentList, String documentName);

    protected DocumentLayoutData generateAdmissionFormLayoutData(Institute institute, DocumentOutput documentOutput,
                                                                 float logoWidth, float logoHeight) throws IOException {
        return generateAdmissionFormLayoutData(institute, documentOutput, logoWidth, logoHeight, 12f);
    }

    protected DocumentLayoutData generateAdmissionFormLayoutData(Institute institute, DocumentOutput documentOutput,
                                                                 float logoWidth, float logoHeight, float contentFontSize) throws IOException {

        DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false, PageSize.A4, 5f, 5f, 20f, 0f);
        float defaultBorderWidth = 0.5f;
        Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

        int instituteId = institute.getInstituteId();
        DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getRegularFont(),
                getRegularBoldFont(), contentFontSize, defaultBorderWidth, logoWidth, logoHeight,
                LogoProvider.INSTANCE.getLogo(instituteId), null, null);
        documentLayoutData.setImageFrame(ImageProvider.INSTANCE.getImage(ImageProvider.IMAGE_FRAME));
        return documentLayoutData;
    }

    protected void generateMetadata(DocumentLayoutData documentLayoutData, float squareBorderMargin, float innerGap) {
        generateMetadata(documentLayoutData,squareBorderMargin, innerGap,1);
    }

    protected void generateMetadata(DocumentLayoutData documentLayoutData, float squareBorderMargin, float innerGap, int pageNumber) {
        generateBorderLayout(documentLayoutData.getDocument(), documentLayoutData.getDocumentLayoutSetup(), pageNumber,
                squareBorderMargin, innerGap);
    }

    protected void generatePageHeader(DocumentLayoutData documentLayoutData, Institute institute,
                                      AcademicSession academicSession) throws IOException {
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
        generatePageHeader(documentLayoutData, institute, academicSession,
                20f, documentLayoutSetup.getPageSize().getHeight() * 0.90f, 22f);
    }

    protected void generatePageHeader(DocumentLayoutData documentLayoutData, Institute institute,
                                      AcademicSession academicSession, float logoOffsetX, float logoOffsetY,
                                      float instituteNameFontSize) throws IOException {
        Document document = documentLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
        PdfFont boldFont = documentLayoutData.getBoldFont();

        generateDynamicImageProvider(documentLayoutData, logoOffsetX, logoOffsetY, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

        int singleContentColumn = 1;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(boldFont).setFontSize(12f).setTextAlignment(TextAlignment.CENTER);

        addRow(table, documentLayoutSetup,
                Arrays.asList(getParagraph(institute.getInstituteName().toUpperCase(), 102, 51, 0)),
                cellLayoutSetup.copy().setFontSize(instituteNameFontSize));
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine1())),
                cellLayoutSetup);
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine2())),
                cellLayoutSetup);
        document.add(table);

        table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup singleLineBorderLayout = new CellLayoutSetup();
        singleLineBorderLayout.setPdfFont(boldFont).setFontSize(14f).setTextAlignment(TextAlignment.CENTER)
                .setBorderBottom(new SolidBorder(2f));

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("")), singleLineBorderLayout);
        document.add(table);

        addBlankLine(document, false, 1);

        table = getPDFTable(documentLayoutSetup, new float[]{0.3f, 0.4f, 0.3f});
        addRow(table, documentLayoutSetup, Arrays.asList(new CellData(EMPTY_TEXT, cellLayoutSetup),
                new CellData(getParagraph("ADMISSION FORM (" + academicSession.getShortYearDisplayName() + ")", 255,
                        255, 255), cellLayoutSetup.copy().setFontSize(14f).setBackgroundColor(EColorUtils.BLACK_COLOR_HEX_CODE)),
                new CellData(EMPTY_TEXT, cellLayoutSetup)));

        document.add(table);

//		addBlankLine(document, true, 1);

    }

    protected int generateBasicInformationPage(DocumentLayoutData documentLayoutData, StudentManager studentManager,  StudentTransportDetails studentTransportDetails,
                                               Institute institute, Student student, boolean fillAdmissionDate, int index) throws IOException {
        Document document = documentLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
        PdfFont boldFont = documentLayoutData.getBoldFont();
        StudentBasicInfo studentBasicInfo = student == null ? new StudentBasicInfo() : student.getStudentBasicInfo();

        byte[] image = getStudentImage(institute.getInstituteId(), student, studentManager);
        if (image == null) {
            image = documentLayoutData.getImageFrame();
        }
        generateImage(document, documentLayoutSetup, image, 80, 90,
                documentLayoutData.getDocumentLayoutSetup().getPageSize().getWidth() - 120f,
                documentLayoutSetup.getPageSize().getHeight() * 0.70f);

        int singleContentColumn = 1;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup singleCellLayoutSetup = new CellLayoutSetup();
        singleCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.LEFT);

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("BASIC INFORMATION")),
                singleCellLayoutSetup.copy().setFontSize(14f).setPdfFont(boldFont));

        document.add(table);
        addBlankLine(document, false, 1);

        table = getPDFTable(documentLayoutSetup, singleContentColumn);

        addRow(table, documentLayoutSetup,
                Arrays.asList(
                        getAdmissionFormKeyValueParagraph(index++, "Student Name : ", studentBasicInfo.getName())),
                singleCellLayoutSetup);

        addRow(table, documentLayoutSetup,
                Arrays.asList(getAdmissionFormKeyValueParagraph(index++, "Class : ",
                        student == null ? "" : student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayName())),
                singleCellLayoutSetup);

        addRow(table, documentLayoutSetup, Arrays.asList(getAdmissionFormKeyValueParagraph(index++,
                "Admission Number : ", studentBasicInfo.getAdmissionNumber(), false)), singleCellLayoutSetup);

        document.add(table);

        table = getPDFTable(documentLayoutSetup, 2);

        addRow(table, documentLayoutSetup,
                Arrays.asList(
                        getAdmissionFormKeyValueParagraph(index++, "Admission Date : ", !fillAdmissionDate ? ""
                        : studentBasicInfo.getAdmissionDate() == null || studentBasicInfo.getAdmissionDate() <= 0 ? ""
                        : DateUtils.getFormattedDate(studentBasicInfo.getAdmissionDate())),
                        getAdmissionFormKeyValueParagraph(index++, "PEN Number : ", studentBasicInfo.getPenNumber())),
                singleCellLayoutSetup);

        document.add(table);

        CellLayoutSetup threeCellLayoutSetup = new CellLayoutSetup();
        threeCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.LEFT);

        table = getPDFTable(documentLayoutSetup, 3);

        addRow(table, documentLayoutSetup,
                Arrays.asList(
                        getAdmissionFormKeyValueParagraph(index++, "Gender : ",
                                studentBasicInfo.getGender() == null ? EMPTY_VALUE
                                        : WordUtils.capitalizeFully(studentBasicInfo.getGender().name())),
                        getAdmissionFormKeyValueParagraph(index++, "Date Of Birth : ",
                                studentBasicInfo.getDateOfBirth() == null || studentBasicInfo.getDateOfBirth() <= 0
                                        ? EMPTY_VALUE
                                        : DateUtils.getFormattedDate(studentBasicInfo.getDateOfBirth())),
                        getAdmissionFormKeyValueParagraph(index++, "Birth Place : ", studentBasicInfo.getBirthPlace())),
                threeCellLayoutSetup);

        addRow(table, documentLayoutSetup,
                Arrays.asList(getAdmissionFormKeyValueParagraph(index++, "Religion : ", studentBasicInfo.getReligion()),
                        getAdmissionFormKeyValueParagraph(index++, "Category : ",
                                studentBasicInfo.getUserCategory() == null ? EMPTY_VALUE
                                        : studentBasicInfo.getUserCategory().name()),
                        getAdmissionFormKeyValueParagraph(index++, "Specially Abled : ",
                                studentBasicInfo.getSpeciallyAbledDisplay())),
                threeCellLayoutSetup);

        document.add(table);

        table = getPDFTable(documentLayoutSetup, 1);

        addRow(table, documentLayoutSetup, Arrays.asList(getAdmissionFormKeyValueParagraph(index++,
                "Permanent Address : ", studentBasicInfo.getPermanentAddress())), singleCellLayoutSetup);
        document.add(table);

        table = getPDFTable(documentLayoutSetup, 3);
        addRow(table, documentLayoutSetup,
                Arrays.asList(getAdmissionFormKeyValueParagraph(index++, "City : ", studentBasicInfo.getPermanentCity()),
                        getAdmissionFormKeyValueParagraph(index++, "State : ", studentBasicInfo.getPermanentState()),
                        getAdmissionFormKeyValueParagraph(index++, "Zipcode : ", studentBasicInfo.getPermanentZipcode())),
                threeCellLayoutSetup);

        document.add(table);
        table = getPDFTable(documentLayoutSetup, 2);

        addRow(table, documentLayoutSetup, Arrays.asList(getAdmissionFormKeyValueParagraph(index++,
                        "Primary Contact : ", studentBasicInfo.getPrimaryContactNumber()), new Paragraph("")),
                threeCellLayoutSetup);

        addRow(table, documentLayoutSetup, Arrays.asList(
                getAdmissionFormKeyValueParagraph(index++, "Primary Email : ", studentBasicInfo.getPrimaryEmail()),
                new Paragraph("")), threeCellLayoutSetup);

        document.add(table);

        table = getPDFTable(documentLayoutSetup, 1);

        addRow(table, documentLayoutSetup, Arrays.asList(getAdmissionFormKeyValueParagraph(index++,
                "House : ", studentBasicInfo.getInstituteHouse() == null ? EMPTY_TEXT
                        : StringUtils.isBlank(studentBasicInfo.getInstituteHouse().getHouseName()) ? EMPTY_TEXT
                        : studentBasicInfo.getInstituteHouse().getHouseName())), singleCellLayoutSetup);
        
        document.add(table);

        return index;
    }

    protected int generateFamilyInformationPage(DocumentLayoutData documentLayoutData, Institute institute,
                                                Student student, int index, boolean includeNamePrefix) throws IOException {

        return generateFamilyInformationPage(documentLayoutData, institute, true, student, index, includeNamePrefix);
    }

    protected int generateFamilyInformationPage(DocumentLayoutData documentLayoutData, Institute institute,
                                                boolean publishStudentData, Student student, int index, boolean includeNamePrefix) throws IOException {

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.LEFT);

        return generateFamilyInformationPage(documentLayoutData, institute, publishStudentData, student, index, includeNamePrefix,
                cellLayoutSetup);
    }

    protected int generateFamilyInformationPage(DocumentLayoutData documentLayoutData, Institute institute, boolean publishStudentData,
                                                Student student, int index, boolean includeNamePrefix,
                                                CellLayoutSetup cellLayoutSetup) throws IOException {

        Document document = documentLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
        PdfFont boldFont = documentLayoutData.getBoldFont();
        StudentFamilyInfo studentFamilyInfo = student == null ? new StudentFamilyInfo() : student.getStudentFamilyInfo();

        addSingleRowParagraphTable(document, documentLayoutSetup, cellLayoutSetup.copy().setFontSize(14f).setPdfFont(boldFont),
                getList(getParagraph("FAMILY INFORMATION")));

        addBlankLine(document, false, 1);

        List<List<Paragraph>> content = getList(
                getList(getAdmissionFormKeyValueParagraph(index++, "Father's Name : ",
                                publishStudentData,
                                includeNamePrefix
                                        ? MALE_NAME_GREET_PREFIX + StringConstants.SPACE
                                        + studentFamilyInfo.getFathersName()
                                        : studentFamilyInfo.getFathersName()),
                        getAdmissionFormKeyValueParagraph(index++, "Mother's Name : ",
                                publishStudentData,
                                includeNamePrefix
                                        ? FEMALE_NAME_GREET_PREFIX + StringConstants.SPACE
                                        + studentFamilyInfo.getMothersName()
                                        : studentFamilyInfo.getMothersName())),
                getList(getAdmissionFormKeyValueParagraph(index++, "Father's Contact : ",
                                publishStudentData,
                                studentFamilyInfo.getFathersContactNumber()),
                        getAdmissionFormKeyValueParagraph(index++, "Mother's Contact : ",
                                publishStudentData,
                                studentFamilyInfo.getMothersContactNumber())),
                getList(getAdmissionFormKeyValueParagraph(index++, "Father's Occupation : ",
                                publishStudentData,
                                studentFamilyInfo.getFathersOccupation()),
                        getAdmissionFormKeyValueParagraph(index++, "Mother's Occupation : ",
                                publishStudentData,
                                studentFamilyInfo.getMothersOccupation()))
        );

        addParagraphTable(document, documentLayoutSetup, cellLayoutSetup, content);
        return index;
    }

    protected int generatePrevSchoolInformationPage(DocumentLayoutData documentLayoutData, Institute institute,
                                                    Student student, int index) throws IOException {
        return generatePrevSchoolInformationPage(documentLayoutData, institute, true, student, index);
    }

    protected int generatePrevSchoolInformationPage(DocumentLayoutData documentLayoutData, Institute institute,
                                                    boolean publishStudentData,
                                                    Student student, int index) throws IOException {
        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.LEFT);

        return generatePrevSchoolInformationPage(documentLayoutData, institute, publishStudentData, student, index,
                cellLayoutSetup);
    }

    protected int generatePrevSchoolInformationPage(DocumentLayoutData documentLayoutData, Institute institute,
                                                    boolean publishStudentData,
                                                    Student student, int index, CellLayoutSetup cellLayoutSetup) throws IOException {
        Document document = documentLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
        PdfFont boldFont = documentLayoutData.getBoldFont();
        StudentPreviousSchoolInfo studentPreviousSchoolInfo = student == null ? new StudentPreviousSchoolInfo() : student.getStudentPreviousSchoolInfo();

        addSingleRowParagraphTable(document, documentLayoutSetup, cellLayoutSetup.copy().setFontSize(14f).setPdfFont(boldFont),
                getList(getParagraph("PREVIOUS SCHOOL INFORMATION")));

        addBlankLine(document, false, 1);

        List<List<Paragraph>> content = getList(
                getList(
                        getAdmissionFormKeyValueParagraph(index++, "School Name : ",
                                publishStudentData,
                                studentPreviousSchoolInfo.getSchoolName()),
                        getAdmissionFormKeyValueParagraph(index++, "Class Attended : ",
                                publishStudentData,
                                studentPreviousSchoolInfo.getClassPassed())),
                getList(
                        getAdmissionFormKeyValueParagraph(index++, "Medium : ", publishStudentData,
                                studentPreviousSchoolInfo.getMedium()),
                        getAdmissionFormKeyValueParagraph(index++, "Percentage/Grade : ", publishStudentData,
                                studentPreviousSchoolInfo.getPercentage()))
        );

        addParagraphTable(document, documentLayoutSetup, cellLayoutSetup, content);
        return index;
    }

    protected int generateMedicalInformationPage(DocumentLayoutData documentLayoutData, Institute institute,
                                                 Student student, int index) throws IOException {
        return generateMedicalInformationPage(documentLayoutData, institute, true, student, index);
    }

    protected int generateMedicalInformationPage(DocumentLayoutData documentLayoutData, Institute institute,
                                                 boolean publishStudentData, Student student, int index) throws IOException {
        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.LEFT);

        return generateMedicalInformationPage(documentLayoutData, institute, publishStudentData, student, index,
                cellLayoutSetup);
    }

    protected int generateMedicalInformationPage(DocumentLayoutData documentLayoutData, Institute institute,
                                                 boolean publishStudentData, Student student, int index, CellLayoutSetup cellLayoutSetup) throws IOException {
        Document document = documentLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
        PdfFont boldFont = documentLayoutData.getBoldFont();
        StudentMedicalInfo studentMedicalInfo = student == null ? new StudentMedicalInfo() : student.getStudentMedicalInfo();

        addSingleRowParagraphTable(document, documentLayoutSetup, cellLayoutSetup.copy().setFontSize(14f).setPdfFont(boldFont),
                getList(getParagraph("MEDICAL INFORMATION")));

        addBlankLine(document, false, 1);

        List<List<Paragraph>> content = getList(
                getList(
                        getAdmissionFormKeyValueParagraph(index++, "Blood Group : ", publishStudentData,
                                studentMedicalInfo.getBloodGroup() == null ? EMPTY_TEXT
                                        : studentMedicalInfo.getBloodGroup().getDisplayName()),
                        getAdmissionFormKeyValueParagraph(index++, "Blood Pressure : ", publishStudentData,studentMedicalInfo.getBloodPressure())),
                getList(getAdmissionFormKeyValueParagraph(index++, "Height : ", publishStudentData, studentMedicalInfo.getHeight()),
                        getAdmissionFormKeyValueParagraph(index++, "Weight : ", publishStudentData, studentMedicalInfo.getWeight()))
        );

        addParagraphTable(document, documentLayoutSetup, cellLayoutSetup, content);
        return index;

    }

    protected int generateDocumentsInformationPage(DocumentLayoutData documentLayoutData, Institute institute,
                                                   Student student, int index) throws IOException {
        Document document = documentLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
        PdfFont boldFont = documentLayoutData.getBoldFont();

        int singleContentColumn = 1;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup singleCellLayoutSetup = new CellLayoutSetup();
        singleCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.LEFT);

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("DOCUMENT INFORMATION")),
                singleCellLayoutSetup.copy().setFontSize(14f).setPdfFont(boldFont));

        document.add(table);
        addBlankLine(document, false, 1);

        table = getPDFTable(documentLayoutSetup, 2);

        CellLayoutSetup threeCellLayoutSetup = new CellLayoutSetup();
        threeCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.LEFT);

        table = getPDFTable(documentLayoutSetup, 2);

        addRow(table, documentLayoutSetup, Arrays.asList(
                        getAdmissionFormKeyValueParagraph(index++, "Original TC from previous school : ", "Yes/No", false),
                        getAdmissionFormKeyValueParagraph(index++, "Previous class original marksheet : ", "Yes/No", false)),
                threeCellLayoutSetup);

        addRow(table, documentLayoutSetup,
                Arrays.asList(getAdmissionFormKeyValueParagraph(index++, "Aadhar Card : ", "Yes/No", false),
                        getAdmissionFormKeyValueParagraph(index++, "Birth Certificate : ", "Yes/No", false)),
                threeCellLayoutSetup);

        addRow(table, documentLayoutSetup,
                Arrays.asList(getAdmissionFormKeyValueParagraph(index++, "Income Certificate : ", "Yes/No", false),
                        getAdmissionFormKeyValueParagraph(index++, "Caste Certificate : ", "Yes/No", false)),
                threeCellLayoutSetup);

        document.add(table);

        return index;
    }

    protected void generateSignatureBox(DocumentLayoutData documentLayoutData, float squareBorderMargin, float innerGap)
            throws IOException {

        Document document = documentLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();

        int singleContentColumn = 4;

        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
        signatureCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(documentLayoutData.getContentFontSize())
                .setTextAlignment(TextAlignment.CENTER);

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Admission Incharge"), getParagraph("Student"),
                getParagraph("Parents/Guardians"), getParagraph("Principal")), signatureCellLayoutSetup);

        table.setFixedPosition(3, squareBorderMargin + innerGap + 3, documentLayoutSetup.getPageSize().getWidth());
        document.add(table);
    }

    protected Paragraph getAdmissionFormKeyValueParagraph(int index, String key, String value) {
        return getAdmissionFormKeyValueParagraph(index,  key, true, value, false);
    }

    protected Paragraph getAdmissionFormKeyValueParagraph(int index, String key, boolean publishValue, String value) {
        return getAdmissionFormKeyValueParagraph(index, key, publishValue, value, false);
    }

    protected Paragraph getAdmissionFormKeyValueParagraph(int index, String key, String value, boolean capitalize) {
        return getAdmissionFormKeyValueParagraph(index,  key, true, value, capitalize);
    }

    protected Paragraph getAdmissionFormKeyValueParagraph(int index, String key, boolean publishValue, String value, boolean capitalize) {
        return getAdmissionFormKeyValueParagraph(index, true, key, publishValue ? value : null, capitalize);
    }

    private Paragraph getAdmissionFormKeyValueParagraph(int index, boolean useIndex, String key, String value,
                                                        boolean capitalize) {
        try {
            return getKeyValueParagraph((useIndex ? index + ". " : "") + key, getAdmissionFormValue(value, capitalize));
        } catch (Exception e) {
            logger.error("Error while getting paragraph with key {}", key);
        }
        return new Paragraph(EMPTY_VALUE);
    }

    private String getAdmissionFormValue(String value, boolean capitalize) {
        if (StringUtils.isBlank(value)) {
            return EMPTY_VALUE;
        }
        return capitalize ? WordUtils.capitalizeFully(value) : value;
    }

    protected void generateBackgroundImage(Document document, DocumentLayoutSetup documentLayoutSetup, byte[] image, float width,
                                           float height, float offsetX, float offsetY) {
        try {
            generateImage(document, documentLayoutSetup, image, width, height, offsetX, offsetY);
        } catch (Exception e) {
            logger.error("Exception while adding background image", e);
        }

    }
}
