package com.embrate.cloud.pdf.exam.document.studentmarks;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.google.common.collect.Lists;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.*;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.utils.NumberUtils;
import com.lernen.cloud.core.lib.examination.ExamMarksUtils;
import com.lernen.cloud.core.utils.images.LogoProvider;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.Map.Entry;

/**
 * this report have grade column and global one does not.
 */
public class StudentMarksFeedDocument10390 extends StudentMarksFeedDocument {
	public StudentMarksFeedDocument10390(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(StudentMarksFeedDocument10390.class);
	public static final float DEFAULT_LOGO_WIDTH = 40f;
	public static final float DEFAULT_LOGO_HEIGHT = 40f;
	public static final String GRADE = "Grade";

	public DocumentOutput generateStudentMarksFeedDocument(Institute institute, Standard standard, Integer sectionId,
			String documentName, ExamMetaData examMetaData, List<StudentExamMarksDetails> studentExamMarksDetailsList,
			int singleColumnStudentCount, Set<UUID> userAccessCourses, Map<CourseType, List<ExamGrade>> examGradeMap) {
		int instituteId = institute.getInstituteId();
		try {
			DocumentOutput invoice = new DocumentOutput(documentName, new ByteArrayOutputStream());
			/**
			 * 	aadhar card size - 10.5 x 29.7 cm
			 * 	in inches - 3.93701x11.69291
			 * 	1 inch  = 72 points
			 * 	3.93701*72 & 11.69291*72
			 * 	283.46472f + 20f (side margins) X 841.88952f
			 */
			PageSize pageSize = new PageSize(303f, 842f);
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(pageSize,
					DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN, DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN, 10f);

			documentLayoutSetup.setNoDoubleContentBorder(true);
			float contentFontSize = 8f;
			float headerFontSize = 8f;
			float defaultBorderWidth = 0.1f;
			Document document = initDocument(invoice.getContent(), documentLayoutSetup, false);
			DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, null, null, contentFontSize,
					defaultBorderWidth, DEFAULT_LOGO_WIDTH, DEFAULT_LOGO_HEIGHT, LogoProvider.INSTANCE.getLogo(institute.getInstituteId()), null, null);


			float offsetX = documentLayoutSetup.getSideMargin()
					+ (documentLayoutSetup.isOfficeCopy() ? DEFAULT_LOGO_DOUBLE_CONTENT_SIDE_MARGIN
							: DEFAULT_LOGO_SINGLE_CONTENT_SIDE_MARGIN);
			float offsetY = documentLayoutSetup.getPageSize().getHeight() - DEFAULT_LOGO_HEIGHT
					- documentLayoutSetup.getTopMargin() + 5f;

//			LinkedHashMap<UUID, ExamCourseMarks> examCourseMarksMap = new LinkedHashMap<>();
//			for (StudentExamMarksDetails studentExamMarksDetails : studentExamMarksDetailsList) {
//				for (ExamCourseMarks examCourseMarks : studentExamMarksDetails.getExamCoursesAllDimensionsMarks()) {
//					if (!examCourseMarksMap.containsKey(examCourseMarks.getCourse().getCourseId())) {
//						examCourseMarksMap.put(examCourseMarks.getCourse().getCourseId(), examCourseMarks);
//					}
//				}
//
//			}

			LinkedHashMap<UUID, ExamCourseMarks> examCourseMarksMap = new LinkedHashMap<>();
			Map<UUID, Map<UUID, Double>> studentCourseWiseClassObtainedMarksMap = new HashMap<>();
			Map<UUID, Double> courseTotalMarksMap = new HashMap<>();
			HashMap<UUID, Double> classMaxMarksMap = new HashMap<>();
			HashMap<UUID, Double> classMinMarksMap = new HashMap<>();
			HashMap<UUID, Integer> totalStudentsMap = new HashMap<>();
			HashMap<UUID, Integer> presentStudentsMap = new HashMap<>();
			HashMap<UUID, Integer> passedStudentsMap = new HashMap<>();
			HashMap<UUID, Integer> failedStudentsMap = new HashMap<>();
			//StudentId, CourseId, DimensionId, ExamDimensionObtainedValues
			Map<UUID, Map<UUID, Map<Integer, ExamDimensionObtainedValues>>> studentExamCourseMarksMap = new HashMap<>();
			HashMap<UUID, Double> classAvgMarksMap = new HashMap<>();
			HashMap<UUID, Double> classAvgPercentageMap = new HashMap<>();

			getStudentMarksDataMaps(studentExamMarksDetailsList, studentExamCourseMarksMap, examCourseMarksMap, studentCourseWiseClassObtainedMarksMap, courseTotalMarksMap, classMaxMarksMap, classMinMarksMap,
					totalStudentsMap, presentStudentsMap, passedStudentsMap, failedStudentsMap, classAvgMarksMap, classAvgPercentageMap);


			int count = 0;

			List<List<StudentExamMarksDetails>> studentRowList = Lists.partition(studentExamMarksDetailsList,
					singleColumnStudentCount);
			int lastPageIndex = examCourseMarksMap.keySet().size() * studentRowList.size();
			for (Entry<UUID, ExamCourseMarks> examCourseMarksMapEntry : examCourseMarksMap.entrySet()) {
				ExamCourseMarks examCourseMarks = examCourseMarksMapEntry.getValue();
				/**
				 * filtering courses of which user don't have access to generate report
				 */
				UUID courseId = examCourseMarks.getCourse().getCourseId();
				CourseType courseType = examCourseMarks.getCourse().getCourseType();
				if(!CollectionUtils.isEmpty(userAccessCourses) && !userAccessCourses.contains(courseId)) {
					continue;
				}
				List<ExamGrade> examGradeList = examGradeMap.get(courseType);
				StudentMarksFeedDocumentSummary studentMarksFeedDocumentSummary = new StudentMarksFeedDocumentSummary(classAvgMarksMap.get(courseId),
						classAvgPercentageMap.get(courseId), classMaxMarksMap.get(courseId), classMinMarksMap.get(courseId), totalStudentsMap.get(courseId),
						presentStudentsMap.get(courseId), passedStudentsMap.get(courseId),
						NumberUtils.subtractValues(totalStudentsMap.get(courseId), passedStudentsMap.get(courseId))
//						failedStudentsMap.get(courseId)
				);

				int studentIndex = 1;
				for (List<StudentExamMarksDetails> pageStudents : studentRowList) {
					generateDynamicImageProvider(documentLayoutData, offsetX, offsetY, instituteId, InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

					generateHeader(document, documentLayoutSetup, standard, sectionId, institute, headerFontSize,
							defaultBorderWidth, examMetaData, examCourseMarks);
					generateStudentInformation(document, documentLayoutSetup, contentFontSize, defaultBorderWidth,
							pageStudents, singleColumnStudentCount, studentIndex, examCourseMarks, studentExamCourseMarksMap, examGradeList);
					studentIndex += singleColumnStudentCount;
					generateSignatureBox(document, documentLayoutSetup, contentFontSize, count + 1, studentMarksFeedDocumentSummary);
					if (count != lastPageIndex - 1) {
						document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
					}
					count++;
				}
			}

			document.close();
			return invoice;
		} catch (Exception e) {
			logger.error("Error while crating exam marks feed sheet for institute {}, standard {} ",
					institute.getInstituteId(), standard.getStandardId(), e);
		}
		return null;
	}

	private void getStudentMarksDataMaps(List<StudentExamMarksDetails> studentExamMarksDetailsList, Map<UUID,
			Map<UUID, Map<Integer, ExamDimensionObtainedValues>>> studentExamCourseMarksMap,
			 LinkedHashMap<UUID, ExamCourseMarks> examCourseMarksMap,
			 Map<UUID, Map<UUID, Double>> studentCourseWiseClassObtainedMarksMap,
			 Map<UUID, Double> courseTotalMarksMap, HashMap<UUID, Double> classMaxMarksMap,
			 HashMap<UUID, Double> classMinMarksMap, HashMap<UUID, Integer> totalStudentsMap, HashMap<UUID, Integer> presentStudentsMap,
			 HashMap<UUID, Integer> passedStudentsMap, HashMap<UUID, Integer> failedStudentsMap,
			 HashMap<UUID, Double> classAvgMarksMap, HashMap<UUID, Double> classAvgPercentageMap) {

		for (StudentExamMarksDetails studentExamMarksDetails : studentExamMarksDetailsList) {
			UUID studentId = studentExamMarksDetails.getStudent().getStudentId();
			if(!studentExamCourseMarksMap.containsKey(studentId)) {
				studentExamCourseMarksMap.put(studentId, new HashMap<>());
			}
			Map<UUID, Map<Integer, ExamDimensionObtainedValues>> courseDimensionObtainedValuesMap = studentExamCourseMarksMap.get(studentId);
			for (ExamCourseMarks examCourseMarks : studentExamMarksDetails.getExamCoursesAllDimensionsMarks()) {
				UUID courseId = examCourseMarks.getCourse().getCourseId();

				if(!totalStudentsMap.containsKey(courseId)) {
					totalStudentsMap.put(courseId, 0);
				}
				totalStudentsMap.put(courseId, totalStudentsMap.get(courseId) + 1);


				if(!studentCourseWiseClassObtainedMarksMap.containsKey(courseId)) {
					studentCourseWiseClassObtainedMarksMap.put(courseId, new HashMap<>());
				}

				if(!courseDimensionObtainedValuesMap.containsKey(courseId)) {
					courseDimensionObtainedValuesMap.put(courseId, new HashMap<>());
				}
				if (!examCourseMarksMap.containsKey(courseId)) {
					examCourseMarksMap.put(courseId, examCourseMarks);
				}
				if (!presentStudentsMap.containsKey(courseId)) {
					presentStudentsMap.put(courseId, 0);
				}
				if (!passedStudentsMap.containsKey(courseId)) {
					passedStudentsMap.put(courseId, 0);
				}
				if (!failedStudentsMap.containsKey(courseId)) {
					failedStudentsMap.put(courseId, 0);
				}
				Map<Integer, ExamDimensionObtainedValues> dimensionObtainedValuesMap = courseDimensionObtainedValuesMap.get(courseId);
				for(ExamDimensionObtainedValues examDimensionObtainedValues : examCourseMarks.getExamDimensionObtainedValues()) {
					if(!dimensionObtainedValuesMap.containsKey(examDimensionObtainedValues.getExamDimension().getDimensionId())) {
						dimensionObtainedValuesMap.put(examDimensionObtainedValues.getExamDimension().getDimensionId(), examDimensionObtainedValues);
					}
					if(examDimensionObtainedValues.getExamDimension().isTotal()) {
						Double obtainedMarks = examDimensionObtainedValues.getObtainedMarks();
						Double maxMarks = examDimensionObtainedValues.getMaxMarks();
						Double minMarks = examDimensionObtainedValues.getMinMarks();
						courseTotalMarksMap.put(courseId, maxMarks);

						/**
						 * if no min marks is set then setting min marks as
						 * 0.33 of max marks
						 */
						minMarks = minMarks == null ? maxMarks == null ? null : Math.round(PASSING_THRESHOLD * maxMarks) * 1d : minMarks;
						if(obtainedMarks != null) {

							studentCourseWiseClassObtainedMarksMap.get(courseId).put(studentId, obtainedMarks);
							presentStudentsMap.put(courseId, presentStudentsMap.get(courseId) + 1);
							if(minMarks == null || obtainedMarks >= minMarks) {
								passedStudentsMap.put(courseId, passedStudentsMap.get(courseId) + 1);
							}

							if(minMarks != null && obtainedMarks < minMarks) {
								failedStudentsMap.put(courseId, failedStudentsMap.get(courseId) + 1);
							}
						}
					}
				}
			}
		}

		for(Entry<UUID, Map<UUID, Double>> studentCourseWiseClassObtainedMarksEntry : studentCourseWiseClassObtainedMarksMap.entrySet()) {
			UUID courseId = studentCourseWiseClassObtainedMarksEntry.getKey();
			Map<UUID, Double> courseClassObtainedMarksMap = studentCourseWiseClassObtainedMarksEntry.getValue();

			if (courseClassObtainedMarksMap == null || CollectionUtils.isEmpty(courseClassObtainedMarksMap.entrySet())) {
				continue;
			}

			Double maxMarks = courseTotalMarksMap.get(courseId);

			Double courseClassAvg = MapUtils.isEmpty(courseClassObtainedMarksMap) ? null : ExamMarksUtils.getObtainedMarksAvg(courseClassObtainedMarksMap);
			classAvgMarksMap.put(courseId, courseClassAvg);

			Double courseClassAvgPercentage = courseClassAvg == null || maxMarks == null || maxMarks == 0 ? null : (courseClassAvg / maxMarks) * 100;
			classAvgPercentageMap.put(courseId, courseClassAvgPercentage);

			Double courseClassMax = MapUtils.isEmpty(courseClassObtainedMarksMap) ? null : ExamMarksUtils.getMaxObtainedMarks(courseClassObtainedMarksMap);
			classMaxMarksMap.put(courseId, courseClassMax);

			Double courseClassMin = MapUtils.isEmpty(courseClassObtainedMarksMap) ? null : ExamMarksUtils.getMinObtainedMarks(courseClassObtainedMarksMap);
			classMinMarksMap.put(courseId, courseClassMin);
		}

	}

	public void generateStudentInformation(Document document, DocumentLayoutSetup documentLayoutSetup,
										   float contentFontSize, float defaultBorderWidth, List<StudentExamMarksDetails> studentExamMarksDetails,
										   int singleColumnStudentCount, int studentIndex, ExamCourseMarks examCourseMarks,
										   Map<UUID, Map<UUID, Map<Integer, ExamDimensionObtainedValues>>> studentExamCourseMarksMap,
										   List<ExamGrade> examGradeList) throws IOException {

		int dimensionSize = 0;

		for (ExamDimensionObtainedValues examDimensionObtainedValues : examCourseMarks
				.getExamDimensionObtainedValues()) {
			if(examDimensionObtainedValues.getOriginalMaxMarks() == null) {
				continue;
			}
			dimensionSize++;
		}

		int dimensionSizePlusGradeColumn = dimensionSize + 1;
		float[] singleContentColumnWidths = new float[3 + dimensionSizePlusGradeColumn];
		singleContentColumnWidths[0] = 0.10f;
		singleContentColumnWidths[1] = 0.14f;
		singleContentColumnWidths[2] = 0.33f;

		for (int i = 0; i < dimensionSizePlusGradeColumn; i++) {
			singleContentColumnWidths[i + 3] = 0.43f / dimensionSizePlusGradeColumn;
		}

		Table table = getPDFTable(documentLayoutSetup, singleContentColumnWidths);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize);

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)
				.setBorder(new SolidBorder(defaultBorderWidth)).setPaddingTop(3.5f).setPaddingBottom(3.5f);

		List<CellData> headerCellDataList = new ArrayList<CellData>();
		headerCellDataList.add(new CellData(getParagraph("Roll No.").setMultipliedLeading(0.95f), firstCellLayoutSetup.copy().setFontSize(contentFontSize - 1), 2, 1));
		headerCellDataList.add(new CellData(getParagraph("Ad. No.").setMultipliedLeading(0.95f), firstCellLayoutSetup.copy().setFontSize(contentFontSize - 1), 2, 1));
		headerCellDataList.add(new CellData(getParagraph("Student Name").setMultipliedLeading(0.95f), firstCellLayoutSetup.copy().setFontSize(contentFontSize - 1), 2, 1));

		List<CellData> rightHeaderCellDataList = new ArrayList<CellData>(headerCellDataList);

		List<CellData> leftDimensionMarks = new ArrayList<>();

		for (ExamDimensionObtainedValues examDimensionObtainedValues : examCourseMarks
				.getExamDimensionObtainedValues()) {

			if(examDimensionObtainedValues.getOriginalMaxMarks() == null) {
				continue;
			}

			headerCellDataList.add(new CellData(getParagraph(examDimensionObtainedValues.getExamDimension().getDimensionName()).setMultipliedLeading(0.95f),
					firstCellLayoutSetup));
			rightHeaderCellDataList.add(new CellData(getParagraph(examDimensionObtainedValues.getExamDimension().getDimensionName()).setMultipliedLeading(0.95f),
					firstCellLayoutSetup));

			leftDimensionMarks.add(
					new CellData(getParagraph(examDimensionObtainedValues.getOriginalMaxMarks() == null ? EMPTY_TEXT :
							String.valueOf(Math.round(examDimensionObtainedValues.getOriginalMaxMarks()))).setMultipliedLeading(0.95f),
							firstCellLayoutSetup));

			/**
			 * adding grade column only when exam dimension is number and is of total dimension
			 */
			if(examDimensionObtainedValues.getExamDimension().getExamEvaluationType() == ExamEvaluationType.NUMBER &&
					examDimensionObtainedValues.getExamDimension().isTotal()) {
				headerCellDataList.add(new CellData(getParagraph(GRADE).setMultipliedLeading(0.95f),
						firstCellLayoutSetup));
				rightHeaderCellDataList.add(new CellData(getParagraph(GRADE).setMultipliedLeading(0.95f),
						firstCellLayoutSetup));

				leftDimensionMarks.add(new CellData(getParagraph(EMPTY_TEXT).setMultipliedLeading(0.95f), firstCellLayoutSetup));
			}

		}
		List<CellData> rightDimensionMarks = new ArrayList<>(leftDimensionMarks);

		addRow(table, documentLayoutSetup, headerCellDataList, rightHeaderCellDataList);
		addRow(table, documentLayoutSetup, leftDimensionMarks, rightDimensionMarks);

		for (int i = 0; i < singleColumnStudentCount; i++) {
			Student leftStudent = null;
			if (i < studentExamMarksDetails.size()) {
				leftStudent = studentExamMarksDetails.get(i).getStudent();
			}
			List<CellData> leftStudentRow = new ArrayList<CellData>();

			Map<Integer, ExamDimensionObtainedValues> examDimensionObtainedValuesMap = new HashMap<>();
			if (leftStudent != null) {
				examDimensionObtainedValuesMap = studentExamCourseMarksMap.get(leftStudent.getStudentId()).get(examCourseMarks.getCourse().getCourseId());
				String rollNumber = leftStudent.getStudentAcademicSessionInfoResponse() == null ?
						EMPTY_TEXT : leftStudent.getStudentAcademicSessionInfoResponse().getRollNumber() == null ?
						EMPTY_TEXT : leftStudent.getStudentAcademicSessionInfoResponse().getRollNumber();
				leftStudentRow.add(new CellData(getParagraph(rollNumber).setMultipliedLeading(0.95f), firstCellLayoutSetup));
				leftStudentRow.add(
						new CellData(getParagraph(leftStudent.getStudentBasicInfo().getAdmissionNumber()).setMultipliedLeading(0.95f), firstCellLayoutSetup));
				leftStudentRow.add(new CellData(getParagraph(leftStudent.getStudentBasicInfo().getName()).setMultipliedLeading(0.95f), firstCellLayoutSetup));
			} else {
				leftStudentRow.add(new CellData(getParagraph(EMPTY_TEXT).setMultipliedLeading(0.95f), firstCellLayoutSetup.copy().setBorder(null)));
				leftStudentRow.add(new CellData(getParagraph(EMPTY_TEXT).setMultipliedLeading(0.95f), firstCellLayoutSetup.copy().setBorder(null)));
				leftStudentRow.add(new CellData(getParagraph(EMPTY_TEXT).setMultipliedLeading(0.95f), firstCellLayoutSetup.copy().setBorder(null)));
				leftStudentRow.add(new CellData(getParagraph(EMPTY_TEXT).setMultipliedLeading(0.95f), firstCellLayoutSetup.copy().setBorder(null)));
			}

			for (ExamDimensionObtainedValues examDimensionObtainedValues : examCourseMarks
					.getExamDimensionObtainedValues()) {
				if(examDimensionObtainedValues.getOriginalMaxMarks() == null) {
					continue;
				}

				ExamDimensionObtainedValues studentExamDimensionObtainedValues = examDimensionObtainedValuesMap.get(examDimensionObtainedValues.getExamDimension().getDimensionId());

				String obtainedMarksValue = EMPTY_TEXT;
				String obtainedMarksGrade = EMPTY_TEXT;
				if(studentExamDimensionObtainedValues != null) {
					Double obtainedMarks = studentExamDimensionObtainedValues.getObtainedMarks();
					Double maxMarks = studentExamDimensionObtainedValues.getMaxMarks();
					if (studentExamDimensionObtainedValues.getExamDimension().getExamEvaluationType() == ExamEvaluationType.NUMBER) {
						obtainedMarksValue = ExamDimensionObtainedValues.getFinalObtainedValueDisplay(
								obtainedMarks == null ? null : NumberUtils.getRoundValueString(obtainedMarks),
								studentExamDimensionObtainedValues.getAttendanceStatus(), studentExamDimensionObtainedValues.getMinMarks(), false,
								EMPTY_TEXT, maxMarks, EMPTY_TEXT, true);
						Double percentage = ExamMarksUtils.getPercentage(obtainedMarks, maxMarks);
						obtainedMarksGrade = ExamMarksUtils.computeScholasticGradeByPercentage(examGradeList, percentage, maxMarks,
								studentExamDimensionObtainedValues.getAttendanceStatus(), false, EMPTY_TEXT, true);
					} else if (studentExamDimensionObtainedValues.getExamDimension().getExamEvaluationType() == ExamEvaluationType.GRADE) {
						obtainedMarksValue = ExamDimensionObtainedValues.getFinalObtainedValueDisplay(studentExamDimensionObtainedValues.getObtainedGrade(),
								studentExamDimensionObtainedValues.getAttendanceStatus(), studentExamDimensionObtainedValues.getMinMarks(), false,
								EMPTY_TEXT, maxMarks, EMPTY_TEXT, true);
					}
				}

				if (leftStudent != null) {
					leftStudentRow.add(new CellData(getParagraph(obtainedMarksValue).setMultipliedLeading(0.95f), firstCellLayoutSetup));
					/**
					 * adding grade column only when exam dimension is number and is of total dimension
					 */
					if(examDimensionObtainedValues.getExamDimension().getExamEvaluationType() == ExamEvaluationType.NUMBER &&
							examDimensionObtainedValues.getExamDimension().isTotal()) {
						leftStudentRow.add(new CellData(getParagraph(obtainedMarksGrade).setMultipliedLeading(0.95f), firstCellLayoutSetup));
					}
				} else {
					leftStudentRow.add(new CellData(getParagraph(EMPTY_TEXT).setMultipliedLeading(0.95f), firstCellLayoutSetup.copy().setBorder(null)));
					/**
					 * adding grade column only when exam dimension is number and is of total dimension
					 */
					if(examDimensionObtainedValues.getExamDimension().getExamEvaluationType() == ExamEvaluationType.NUMBER &&
							examDimensionObtainedValues.getExamDimension().isTotal()) {

						leftStudentRow.add(new CellData(getParagraph(EMPTY_TEXT).setMultipliedLeading(0.95f), firstCellLayoutSetup.copy().setBorder(null)));
					}
				}
			}
			addRow(table, documentLayoutSetup, leftStudentRow, leftStudentRow);
		}
		document.add(table);
	}

	protected void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup,
										float contentFontSize, int pageNumber, StudentMarksFeedDocumentSummary studentMarksFeedDocumentSummary) throws IOException {
		int singleContentColumn = 2;

		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.LEFT);

		String classAverageMarks = studentMarksFeedDocumentSummary.getClassAvgMarks() == null ? EMPTY_TEXT : NumberUtils.formatDouble(studentMarksFeedDocumentSummary.getClassAvgMarks());
		String classAveragePercentage = studentMarksFeedDocumentSummary.getClassAvgPercentage() == null ? EMPTY_TEXT : NumberUtils.formatDouble(studentMarksFeedDocumentSummary.getClassAvgPercentage());
		addRow(table, documentLayoutSetup, Arrays.asList(getKeyValueParagraph("Class Avg Marks : ", classAverageMarks),
				getKeyValueParagraph("Class Avg % : ", StringUtils.isBlank(classAveragePercentage) ? EMPTY_TEXT : classAveragePercentage + "%")),
				signatureCellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)), signatureCellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(
				getKeyValueParagraph("Class Max : ", studentMarksFeedDocumentSummary.getClassMaxMarks() == null ? EMPTY_TEXT : NumberUtils.formatDouble(studentMarksFeedDocumentSummary.getClassMaxMarks())),
				getKeyValueParagraph("Class Min : ", studentMarksFeedDocumentSummary.getClassMinMarks() == null ? EMPTY_TEXT : NumberUtils.formatDouble(studentMarksFeedDocumentSummary.getClassMinMarks()))),
				signatureCellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)), signatureCellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(
						getKeyValueParagraph("Total Students : ", studentMarksFeedDocumentSummary.getTotalStudents() == null ? EMPTY_TEXT : studentMarksFeedDocumentSummary.getTotalStudents().toString()),
						getKeyValueParagraph("Present Students : ", studentMarksFeedDocumentSummary.getPresentStudents() == null ? EMPTY_TEXT : studentMarksFeedDocumentSummary.getPresentStudents().toString())),
				signatureCellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)), signatureCellLayoutSetup);
//		addRow(table, documentLayoutSetup, Arrays.asList(
//						getKeyValueParagraph("Passed: ", studentMarksFeedDocumentSummary.getPassedStudents() == null ? EMPTY_TEXT : studentMarksFeedDocumentSummary.getPassedStudents().toString()),
//						getKeyValueParagraph("Failed : ", studentMarksFeedDocumentSummary.getFailedStudents() == null ? EMPTY_TEXT : studentMarksFeedDocumentSummary.getFailedStudents().toString())),
//				signatureCellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)), signatureCellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getKeyValueParagraph("Signature Examiner: ", EMPTY_TEXT), getKeyValueParagraph(EMPTY_TEXT, EMPTY_TEXT)),
				signatureCellLayoutSetup);

		float contentWidth = documentLayoutSetup.getPageSize().getWidth() - 2 * documentLayoutSetup.getSideMargin();
		table.setFixedPosition(pageNumber, documentLayoutSetup.getSideMargin(), 8, contentWidth);
		document.add(table);
	}
}

class StudentMarksFeedDocumentSummary {

	private final Double classAvgMarks;

	private final Double classAvgPercentage;

	private final Double classMaxMarks;

	private final Double classMinMarks;

	private final Integer totalStudents;

	private final Integer presentStudents;

	private final Integer passedStudents;

	private final Integer failedStudents;

	public StudentMarksFeedDocumentSummary(Double classAvgMarks, Double classAvgPercentage, Double classMaxMarks, Double classMinMarks, Integer totalStudents, Integer presentStudents, Integer passedStudents, Integer failedStudents) {
		this.classAvgMarks = classAvgMarks;
		this.classAvgPercentage = classAvgPercentage;
		this.classMaxMarks = classMaxMarks;
		this.classMinMarks = classMinMarks;
		this.totalStudents = totalStudents;
		this.presentStudents = presentStudents;
		this.passedStudents = passedStudents;
		this.failedStudents = failedStudents;
	}

	public Double getClassAvgMarks() {
		return classAvgMarks;
	}

	public Double getClassAvgPercentage() {
		return classAvgPercentage;
	}

	public Double getClassMaxMarks() {
		return classMaxMarks;
	}

	public Double getClassMinMarks() {
		return classMinMarks;
	}

	public Integer getTotalStudents() {
		return totalStudents;
	}

	public Integer getPresentStudents() {
		return presentStudents;
	}

	public Integer getPassedStudents() {
		return passedStudents;
	}

	public Integer getFailedStudents() {
		return failedStudents;
	}

	@Override
	public String toString() {
		return "StudentMarksFeedDocumentSummary{" +
				"classAvgMarks=" + classAvgMarks +
				", classAvgPercentage=" + classAvgPercentage +
				", classMaxMarks=" + classMaxMarks +
				", classMinMarks=" + classMinMarks +
				", totalStudents=" + totalStudents +
				", presentStudents=" + presentStudents +
				", passedStudents=" + passedStudents +
				", failedStudents=" + failedStudents +
				'}';
	}
}