package com.embrate.cloud.pdf.studentfeeschart;

import java.util.HashMap;
import java.util.Map;



public class StudentFeesChartGeneratorFactory {

    private static final Map<Integer, StudentFeesChartGenerator> STUDENT_FEES_CHART_GENERATOR = new HashMap<>();
    
    public StudentFeesChartGenerator getStudentFeesChartGenerator(int instituteId) {

        if (!STUDENT_FEES_CHART_GENERATOR.containsKey(instituteId)) {
				return new GlobalStudentFeeChartGenerator(null);
			}
			return STUDENT_FEES_CHART_GENERATOR.get(instituteId);

    }

}
