/**
 * 
 */
package com.embrate.cloud.pdf.exam.reports._10045;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.kernel.pdf.canvas.draw.SolidLine;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.LineSeparator;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.ExamDimensionObtainedValues;
import com.lernen.cloud.core.api.examination.ExamGrade;
import com.lernen.cloud.core.api.examination.report.*;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.NumberUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.pdf.exam.reports.ExamReportGenerator;
import com.lernen.cloud.pdf.exam.reports.IExamReportCardGenerator;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.*;
import java.util.Map.Entry;

/**
 * <AUTHOR>
 *
 */
public class ExamReportGenerator10045  extends ExamReportGenerator implements IExamReportCardGenerator {
	
	public ExamReportGenerator10045(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(ExamReportGenerator10045.class);

	public static final float DEFAULT_PAGE_SIDE_MARGIN = 25f;
	public static final float DEFAULT_PAGE_TOP_MARGIN = 10f;
	public static final float DEFAULT_PAGE_BOTTOM_MARGIN = 10f;
	public static final float SQUARE_BORDER_MARGIN = 12f;

	private static final Map<String, String> MARKS_GRADE_MAP = new LinkedHashMap<>();

	private static final String SCHOOL_NAME_1 = "Sree Jain Swetamber Terapanthi Vidyalaya";
	private static final String SCHOOL_NAME_2  = "(Boys' H.S. School)";
	private static final String SCHOOL_NAME_3  = "(UNDER WBBSE & WBCHSE)";
	private static final String SCHOOL_NAME_4  = "Governed by : ";
	private static final String SCHOOL_NAME_5  = "Sree Jain Swetamber Terapanthi Vidyalaya (Society)";
	private static final String SCHOOL_NAME_6  = "AN ISO 9001 : 2015 Certified Organization";
	private static final String SCHOOL_NAME_7  = "3, Sahid Nityananda Saha Sarani";
	private static final String SCHOOL_NAME_8  = "(Portuguese Church Street)";
	private static final String SCHOOL_NAME_9  = "Kolkata - 700 001";
	
	private static final String NOTICE  = "Parents are asked to acknowledge the receipt of the report by signing and returning it within the specified time. However, after the Annual Examination it may be retained.";

	private static final String POINT_1 = "1. Reports will not be issued if fees are in arrears.";
	private static final String POINT_2 = "2. Children who are ill or recovering from illness should not be sent for an examination.";
	private static final String POINT_3 = "3. If a child is absent for an examination he may not appear for it at a later date. His general performance in the subject will be taken into account.";
	private static final String POINT_4 = "4. Promotion depends on the student's performance throughout the year not merely on marks scored in the final examination.";
	private static final String POINT_5 = "5. A student who fails two years in succession in the same class may not continue in the school.";
	private static final String POINT_6 = "6. The Headmistress's decision with regards to promotion if final.";
	private static final String POINT_7 = "7. The school assists parents in their primary role as educators. These term reports allow us to share with parents our observations of their ward's progress.";
	private static final String POINT_8 = "8. Criterion for promotion is 25% marks in individual subject.";
	
	
	static {
		MARKS_GRADE_MAP.put("91-100", "A+");
		MARKS_GRADE_MAP.put("76-90", "A");
		MARKS_GRADE_MAP.put("61-75", "B");
		MARKS_GRADE_MAP.put("41-60", "C");
		MARKS_GRADE_MAP.put("0-40", "D");
	}
	
	@Override
	public DocumentOutput generateReport(Institute institute, Student student, String reportType,
			ExamReportData examReportData, String documentName, StudentManager studentManager) {
		try {

			float contentFontSize = 12f;
			float defaultBorderWidth = 0.5f;
			
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput,
					contentFontSize, defaultBorderWidth);

			PdfFont boldFont = getRegularBoldFont();
			PdfFont regularFont = getRegularFont();
			
			Document document = examReportCardLayoutData.getDocument();
			DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
			StudentLite studentLite = Student.getStudentLite(student);
			generateFirstPageLayout(examReportCardLayoutData, document, documentLayoutSetup, institute, boldFont, regularFont,
					studentLite, studentManager, reportType, 1);
			
			document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
			
			generateSecondPageLayout(document, documentLayoutSetup, institute, boldFont, regularFont, contentFontSize,
					defaultBorderWidth, studentLite, studentManager, reportType, examReportData, 2);
			
			examReportCardLayoutData.getDocument().close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, student {}, reportType {} ",
					institute.getInstituteId(), student.getStudentId(), reportType, e);
		}
		return null;
	}
	
	protected void generateSecondPageLayout(Document document, DocumentLayoutSetup documentLayoutSetup,
										  Institute institute, PdfFont boldFont, PdfFont regularFont, float contentFontSize, float defaultBorderWidth,
										  StudentLite student, StudentManager studentManager, String reportType, ExamReportData examReportData, int pageNumber) throws IOException {
		
		generateHeader(document, documentLayoutSetup, boldFont, regularFont, student, institute);
		generateStudentInformation(document, documentLayoutSetup, contentFontSize, boldFont, student);
		
		addBlankLine(document, false, 1);
		if (examReportData.getCourseTypeExamReportMarksGrid().containsKey(CourseType.SCHOLASTIC)) {
			generateCustomScholasticMarksGrid(document, documentLayoutSetup, contentFontSize - 3.5f, defaultBorderWidth,
					examReportData, reportType, GridConfigs.forSkipTotalRow(), "Scholastic Subjects", 0.14f);
			
			addBlankLine(document, false, 2);
			
			if(!CollectionUtils.isEmpty(examReportData.getExamReportStructure()
					.getExamReportCourseStructure().get(CourseType.SCHOLASTIC).getAdditionalCourses())) {				
				generateAdditionalCustomScholasticMarksGrid(document, documentLayoutSetup, contentFontSize - 3.5f, defaultBorderWidth,
						examReportData, reportType, false, "Additional Subjects", 0.14f);
				addBlankLine(document, false, 1);
			}
		}
		
//		if (examReportData.getCourseTypeExamReportMarksGrid().containsKey(CourseType.COSCHOLASTIC)) {
//			generateCoScholasticMarksGridSection(document, documentLayoutSetup, contentFontSize, defaultBorderWidth,
//					examReportData.getCourseTypeExamReportMarksGrid().get(CourseType.COSCHOLASTIC), false, examReportData);
//			addBlankLine(document, false, 1);
//		}
		
		generateResultSummary(document, documentLayoutSetup, contentFontSize, boldFont, examReportData);
		generateSignatureBox(document, documentLayoutSetup, contentFontSize, boldFont, student);
		
		PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);
		canvas.rectangle(SQUARE_BORDER_MARGIN - 2, SQUARE_BORDER_MARGIN - 2,
				documentLayoutSetup.getPageSize().getWidth() - (SQUARE_BORDER_MARGIN - 2) * 2,
				documentLayoutSetup.getPageSize().getHeight() - (SQUARE_BORDER_MARGIN - 2) * 2);
		canvas.stroke();
		canvas.setLineWidth(1f);
		canvas.rectangle(SQUARE_BORDER_MARGIN, SQUARE_BORDER_MARGIN,
				documentLayoutSetup.getPageSize().getWidth() - SQUARE_BORDER_MARGIN * 2,
				documentLayoutSetup.getPageSize().getHeight() - SQUARE_BORDER_MARGIN * 2);
		canvas.stroke();
		canvas.setLineWidth(1f);
		
	}
	
	private void generateCoScholasticMarksGridSection(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize, float defaultBorderWidth, ExamReportMarksGrid examReportMarksGrid, boolean showTotal,
			ExamReportData examReportData) throws IOException {

		float subjectColumnWidth = 0.24f;
		CourseType courseType = CourseType.COSCHOLASTIC;

		float[] columnWidths = new float[] { subjectColumnWidth, 0.24f, 0.04f, 0.24f, 0.24f };
		Table headerTable = getPDFTable(documentLayoutSetup, columnWidths);

		CellLayoutSetup emptyCellLayoutSetup = new CellLayoutSetup();
		CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
		marksCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize)
				.setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);

		CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(getRegularBoldFont())
				.setTextAlignment(TextAlignment.LEFT);

		addBlankLine(document, false, 1);

		List<CellData> headerList = new ArrayList<>();
		headerList.addAll(getHeaderCells(examReportMarksGrid,
				examReportData, courseType,
				defaultBorderWidth, getRegularFont(), getRegularBoldFont(), contentFontSize, contentFontSize,
				subjectColumnWidth, "Subject", true, "MM", "MO", false));

		headerList.add(new CellData("", emptyCellLayoutSetup));
		headerList.add(new CellData("Marks", marksCellLayoutSetup));
		headerList.add(new CellData("Grade", marksCellLayoutSetup));
		addRow(headerTable, documentLayoutSetup, headerList);

		List<Entry<String, String>> gradesList = new ArrayList<>(MARKS_GRADE_MAP.entrySet());

		Collections.sort(examReportMarksGrid.getExamReportCourseMarksRows(),
				new Comparator<ExamReportCourseMarksRow>() {

					@Override
					public int compare(ExamReportCourseMarksRow o1, ExamReportCourseMarksRow o2) {

						return o1.getCourse().compareTo(o2.getCourse());
					}
				});
		int index = 0;
		for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportMarksGrid.getExamReportCourseMarksRows()) {
			if (CollectionUtils.isEmpty(examReportCourseMarksRow.getExamReportCourseMarksColumns())) {
				continue;
			}
			List<CellData> row = new ArrayList<>();
			row.add(new CellData(examReportCourseMarksRow.getCourse().getCourseName(), courseCellLayoutSetup));
			for (ExamReportCourseMarksColumn examReportCourseMarksColumn : examReportCourseMarksRow
					.getExamReportCourseMarksColumns()) {
				/**
				 * Assuming grades only for coscholatic rows
				 */
				if (CollectionUtils.isEmpty(examReportCourseMarksColumn.getExamDimensionObtainedValuesList())) {
					continue;
				}
				ExamDimensionObtainedValues examDimensionObtainedValues = examReportCourseMarksColumn
						.getExamDimensionObtainedValuesList().get(0);
				String value = ExamGrade.emptyGrade(examDimensionObtainedValues.getObtainedGrade()) ? "-"
						: examDimensionObtainedValues.getObtainedGrade().getGradeName();
				row.add(new CellData(value, marksCellLayoutSetup));
			}

			row.add(new CellData("", emptyCellLayoutSetup));
			if (index < gradesList.size()) {
				row.add(new CellData(gradesList.get(index).getKey(), marksCellLayoutSetup));
				row.add(new CellData(gradesList.get(index++).getValue(), marksCellLayoutSetup));
			} else {
				row.add(new CellData("", emptyCellLayoutSetup));
				row.add(new CellData("", emptyCellLayoutSetup));
			}

			addRow(headerTable, documentLayoutSetup, row);
		}
		List<CellData> attendanceRow1 = new ArrayList<>();
		attendanceRow1
				.add(new CellData("Attendance", emptyCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)));
		attendanceRow1.add(new CellData("", emptyCellLayoutSetup));
		attendanceRow1.add(new CellData("", emptyCellLayoutSetup));
		if (index < gradesList.size()) {
			attendanceRow1.add(new CellData(gradesList.get(index).getKey(), marksCellLayoutSetup));
			attendanceRow1.add(new CellData(gradesList.get(index++).getValue(), marksCellLayoutSetup));
		} else {
			attendanceRow1.add(new CellData("", emptyCellLayoutSetup));
			attendanceRow1.add(new CellData("", emptyCellLayoutSetup));
		}
		addRow(headerTable, documentLayoutSetup, attendanceRow1);

		List<CellData> attendanceRow2 = new ArrayList<>();
		attendanceRow2.add(
				new CellData("Total Working Days", marksCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)));
		attendanceRow2.add(new CellData(examReportData.getTotalWorkingDays() == null ? "-"
				: String.valueOf(examReportData.getTotalWorkingDays()), marksCellLayoutSetup));
		attendanceRow2.add(new CellData("", emptyCellLayoutSetup));

		if (index < gradesList.size()) {
			attendanceRow2.add(new CellData(gradesList.get(index).getKey(), marksCellLayoutSetup));
			attendanceRow2.add(new CellData(gradesList.get(index++).getValue(), marksCellLayoutSetup));
		} else {
			attendanceRow2.add(new CellData("", emptyCellLayoutSetup));
			attendanceRow2.add(new CellData("", emptyCellLayoutSetup));
		}

		addRow(headerTable, documentLayoutSetup, attendanceRow2);

		List<CellData> attendanceRow3 = new ArrayList<>();
		attendanceRow3.add(
				new CellData("Total Attended Days", marksCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)));
		attendanceRow3.add(new CellData(examReportData.getTotalAttendedDays() == null ? "-"
				: String.valueOf(examReportData.getTotalAttendedDays()), marksCellLayoutSetup));
		attendanceRow3.add(new CellData("", emptyCellLayoutSetup));

		if (index < gradesList.size()) {
			attendanceRow3.add(new CellData(gradesList.get(index).getKey(), marksCellLayoutSetup));
			attendanceRow3.add(new CellData(gradesList.get(index++).getValue(), marksCellLayoutSetup));
		} else {
			attendanceRow3.add(new CellData("", emptyCellLayoutSetup));
			attendanceRow3.add(new CellData("", emptyCellLayoutSetup));
		}

		addRow(headerTable, documentLayoutSetup, attendanceRow3);

		if (index < gradesList.size()) {
			while (index < gradesList.size()) {
				List<CellData> reminingRows = new ArrayList<>();
				reminingRows.add(new CellData("", emptyCellLayoutSetup));
				reminingRows.add(new CellData("", emptyCellLayoutSetup));
				reminingRows.add(new CellData("", emptyCellLayoutSetup));
				reminingRows.add(new CellData(gradesList.get(index).getKey(), marksCellLayoutSetup));
				reminingRows.add(new CellData(gradesList.get(index++).getValue(), marksCellLayoutSetup));
				addRow(headerTable, documentLayoutSetup, reminingRows);
			}
		}

		/**
		 * only showing sum in last column
		 */
		if (showTotal) {
			List<CellData> row = new ArrayList<>();
			row.add(new CellData("Total", courseCellLayoutSetup, 1,
					columnWidths.length - examReportMarksGrid.getExamReportTotalMarksColumns()
							.get(examReportMarksGrid.getExamReportTotalMarksColumns().size() - 1)
							.getExamDimensionObtainedValuesList().size()));

			for (ExamDimensionObtainedValues examDimensionObtainedValues : examReportMarksGrid
					.getExamReportTotalMarksColumns()
					.get(examReportMarksGrid.getExamReportTotalMarksColumns().size() - 1)
					.getExamDimensionObtainedValuesList()) {
				String value = ExamGrade.emptyGrade(examDimensionObtainedValues.getObtainedGrade()) ? "-"
						: examDimensionObtainedValues.getObtainedGrade().getGradeName();
				row.add(new CellData(value, marksCellLayoutSetup));
			}

			addRow(headerTable, documentLayoutSetup, row);
		}

		document.add(headerTable);

	}
	
	protected void generateAdditionalCustomScholasticMarksGrid(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize, float defaultBorderWidth,
			ExamReportData examReportData, String reportType, boolean addTotalRow, String subjectColumnTitle,
			float subjectColumnWidth) throws IOException {
		ExamReportStructure examReportStructure = examReportData.getExamReportStructure();
		if (examReportStructure == null || CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure())
				|| examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC) == null
				|| CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC)
						.getAdditionalCourses())) {
			return;
		}
//		generateScholasticMarksGrid(document, documentLayoutSetup, contentFontSize, defaultBorderWidth,
//				examReportData, GridConfigs.forSkipTotalRow(), subjectColumnTitle, subjectColumnWidth,
//				examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC).getAdditionalCourses(),
//				null);

		generateScholasticMarksGrid(document, documentLayoutSetup,
				getRegularFont(), getRegularBoldFont(), contentFontSize, defaultBorderWidth, examReportData,
				GridConfigs.forSkipTotalRow(), subjectColumnTitle, subjectColumnWidth,
				examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC).getAdditionalCourses(),
				"MM", "MO", null, "-");

	}
	
	protected Set<UUID> getNonAdditionalSubjets(ExamReportData examReportData) {
		Set<UUID> nonAdditionalSubjects = new HashSet<UUID>();
		for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportData.getCourseTypeExamReportMarksGrid()
				.get(CourseType.SCHOLASTIC).getExamReportCourseMarksRows()) {
			if (!isAdditionalSubject(examReportCourseMarksRow.getCourse(), examReportData.getExamReportStructure())) {
				nonAdditionalSubjects.add(examReportCourseMarksRow.getCourse().getCourseId());
			}
		}
		return nonAdditionalSubjects;
	}
	
	private void generateCustomScholasticMarksGrid(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize, float defaultBorderWidth,
			ExamReportData examReportData, String reportType, GridConfigs gridConfigs, String subjectColumnTitle,
			float subjectColumnWidth) throws IOException {
		Set<UUID> nonAdditionalSubjects = getNonAdditionalSubjets(examReportData);
//		generateScholasticMarksGrid(document, documentLayoutSetup, contentFontSize, defaultBorderWidth, examReportData,
//				gridConfigs, subjectColumnTitle, subjectColumnWidth, nonAdditionalSubjects, null);
		generateScholasticMarksGrid(document, documentLayoutSetup,
				getRegularFont(), getRegularBoldFont(), contentFontSize, defaultBorderWidth, examReportData, gridConfigs,
				subjectColumnTitle, subjectColumnWidth, nonAdditionalSubjects, "MM",
				"MO", null, "-");
	}
	
	protected void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
			PdfFont boldFont, StudentLite student) {
		int singleContentColumn = 3;

		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.CENTER);

		int academicSessionId = student.getStudentSessionData().getAcademicSessionId();
		String thirdSignature = "Head of the Institution";
		if(academicSessionId >= 38) {
			thirdSignature = "Headmistress";
		}
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Exam Controller"), getParagraph("Class Teacher"),
				getParagraph(thirdSignature)), signatureCellLayoutSetup);
		table.setFixedPosition(SQUARE_BORDER_MARGIN, SQUARE_BORDER_MARGIN + 3,
				documentLayoutSetup.getPageSize().getWidth());
		document.add(table);
	}

	private void generateResultSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize, PdfFont boldFont, ExamReportData examReportData) {
		
		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.4f, 0.2f, 0.4f });

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.LEFT);

		addBlankLine(document, false, 1);
		addBlankLine(document, true, 1);
		Paragraph promotedClass = getKeyValueParagraph("Promoted to: ",
				examReportData.getPromotedTo() == null ? "-" : examReportData.getPromotedTo().getStandardName());
		Paragraph obtainedMarks = getKeyValueParagraph("Obtained marks : ",
				examReportData.getTotalObtainedMarks() == null ? "-"
						: examReportData.getTotalObtainedMarks() * 10 % 10 == 0
								? String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10)
								: String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10d));
		Paragraph totalMarks = getKeyValueParagraph("Total marks : ", examReportData.getTotalMaxMarks() == null ? "-"
				: String.valueOf(Math.round(examReportData.getTotalMaxMarks() * 10) / 10));
		Paragraph percentage = getKeyValueParagraph("Percentage : ", examReportData.getPercentage() == null ? "-"
				: String.valueOf(Math.round(examReportData.getPercentage() * 100) / 100d) + "%");

		int totalWorkingDays=(examReportData.getTotalWorkingDays()==null?0:examReportData.getTotalWorkingDays());
		int presentCount=(examReportData.getTotalAttendedDays()==null?0:examReportData.getTotalAttendedDays());
		double presentPercentage = (totalWorkingDays == 0 ? 0 : NumberUtils.formatDoubleNumber((((presentCount * 1d)/totalWorkingDays) * 100d), 2));
		Paragraph attendance= getKeyValueParagraph("Attendant Percentage :",presentPercentage + "%");
		Paragraph grade = getKeyValueParagraph("Grade : ",
				examReportData.getTotalGrade() == null ? "-" : examReportData.getTotalGrade().getGradeName());
//		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(promotedClass, cellLayoutSetup),
//				new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(EMPTY_TEXT, cellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(obtainedMarks, cellLayoutSetup),
				new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(totalMarks, cellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(percentage, cellLayoutSetup),
				new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(grade, cellLayoutSetup)));

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(attendance, cellLayoutSetup),
				new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(EMPTY_TEXT, cellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph(EMPTY_TEXT), getParagraph(EMPTY_TEXT), getParagraph(EMPTY_TEXT)),
				cellLayoutSetup);
		document.add(table);

		Table remarksTable = getPDFTable(documentLayoutSetup, 1);
		Paragraph remarks = getKeyValueParagraph("Remarks: ",
				examReportData.getRemarks() == null ? "" : examReportData.getRemarks());
		addRow(remarksTable, documentLayoutSetup, Arrays.asList(remarks), cellLayoutSetup);
		document.add(remarksTable);
		
	}

	private void generateStudentInformation(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize, PdfFont boldFont, StudentLite student) {
		
		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.45f, 0.2f, 0.35f });

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize);

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);

		Paragraph admissionNumber = getKeyValueParagraph("Roll No. ",
				StringUtils.isEmpty(student.getStudentSessionData().getRollNumber()) ? "" :
				student.getStudentSessionData().getRollNumber());

		Paragraph studentName = getKeyValueParagraph("Student's Name : ", student.getName());

		Paragraph classValue = getKeyValueParagraph("Class : ",
				student.getStudentSessionData().getStandardDisplayName());

		Paragraph dob = getKeyValueParagraph("DOB : ",
				student.getDateOfBirth() == null
						|| student.getDateOfBirth() <= 0 ? ""
								: DateUtils.getFormattedDate(student.getDateOfBirth(),
										DATE_FORMAT, User.DFAULT_TIMEZONE));

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(studentName, firstCellLayoutSetup),
						new CellData(new Paragraph(""), secondCellLayoutSetup),
						new CellData(classValue, thirdCellLayoutSetup)));

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(dob, firstCellLayoutSetup),
						new CellData(new Paragraph(""), secondCellLayoutSetup),
						new CellData(admissionNumber, thirdCellLayoutSetup)));

		document.add(table);
		
	}

	private void generateHeader(Document document, DocumentLayoutSetup documentLayoutSetup, PdfFont boldFont,
			PdfFont regularFont, StudentLite student, Institute institute) {
		
		int singleContentColumn = 1;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(14f).setTextAlignment(TextAlignment.CENTER);

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getInstituteName().toUpperCase())),
				cellLayoutSetup.copy().setFontSize(24f));
		document.add(table);

		addBlankLine(document, true, 1);
		addBlankLine(document, false, 1);

		table = getPDFTable(documentLayoutSetup, singleContentColumn);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Session : "
				+ student.getStudentSessionData().getAcademicSessionName())),
				cellLayoutSetup);
		document.add(table);
		addBlankLine(document, true, 1);
		
	}

	protected void generateFirstPageLayout(ExamReportCardLayoutData examReportCardLayoutData, Document document, DocumentLayoutSetup documentLayoutSetup, Institute institute,
			PdfFont boldFont, PdfFont regularFont, StudentLite student, StudentManager studentManager,
			String reportType, int pageNumber) throws MalformedURLException, IOException {
		float logoWidth = 70f;
		float logoHeight = 70f;

		generateImage(document, documentLayoutSetup, ImageProvider.INSTANCE.getImage(ImageProvider._10045_LOGO),
				logoWidth, logoHeight, 265, documentLayoutSetup.getPageSize().getHeight() * 0.555f);

		generateFirstPageHeader(document, documentLayoutSetup, boldFont, regularFont, student, institute);
		
		PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);
		canvas.rectangle(SQUARE_BORDER_MARGIN - 2, SQUARE_BORDER_MARGIN - 2,
				documentLayoutSetup.getPageSize().getWidth() - (SQUARE_BORDER_MARGIN - 2) * 2,
				documentLayoutSetup.getPageSize().getHeight() - (SQUARE_BORDER_MARGIN - 2) * 2);
		canvas.stroke();
		canvas.setLineWidth(1f);
		canvas.rectangle(SQUARE_BORDER_MARGIN, SQUARE_BORDER_MARGIN,
				documentLayoutSetup.getPageSize().getWidth() - (SQUARE_BORDER_MARGIN) * 2,
				documentLayoutSetup.getPageSize().getHeight() - (SQUARE_BORDER_MARGIN) * 2);
		canvas.stroke();
		canvas.setLineWidth(1f);
	}

	private void generateFirstPageHeader(Document document, DocumentLayoutSetup documentLayoutSetup,
			PdfFont boldFont, PdfFont regularFont, StudentLite student, Institute institute) {
		
		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(14f).setTextAlignment(TextAlignment.CENTER);
		
		CellLayoutSetup regularCellLayoutSetup = new CellLayoutSetup();
		regularCellLayoutSetup.setPdfFont(regularFont).setFontSize(13f).setTextAlignment(TextAlignment.CENTER);
		
		generateIndexPhoneNumberTable(document, documentLayoutSetup, boldFont, regularFont, student, institute);
		
		generateSchoolNameDetailTable(document, documentLayoutSetup, boldFont, regularFont, cellLayoutSetup, 
				regularCellLayoutSetup, student, institute);
		
		generateProgressReportTextTable(document, documentLayoutSetup, boldFont, regularFont, cellLayoutSetup, 
				regularCellLayoutSetup, student, institute);
		
		generateStudentInfoFirstPageTable(document, documentLayoutSetup, boldFont, regularFont, cellLayoutSetup, 
				regularCellLayoutSetup, student, institute);
		
		generatePointsDetailsTable(document, documentLayoutSetup, boldFont, regularFont, cellLayoutSetup, 
				regularCellLayoutSetup, student, institute);
		
	}

	private void generatePointsDetailsTable(Document document, DocumentLayoutSetup documentLayoutSetup,
			PdfFont boldFont, PdfFont regularFont, CellLayoutSetup cellLayoutSetup,
			CellLayoutSetup regularCellLayoutSetup, StudentLite student, Institute institute) {
		
		int singleContentColumn = 1;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);
		
		regularCellLayoutSetup.setFontSize(10f);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(NOTICE)), 
				regularCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT));
		
		document.add(table);
		
		SolidLine line = new SolidLine();
		document.add(new LineSeparator(line).setTextAlignment(TextAlignment.JUSTIFIED));
	
		
		table = getPDFTable(documentLayoutSetup, singleContentColumn);
		addBlankLine(document, false, 1);
		addBlankLine(document, false, 1);
		regularCellLayoutSetup.setFontSize(11f);
		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph(POINT_1)), 
				regularCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT));
		
		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph(POINT_2)), 
				regularCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT));
		
		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph(POINT_3)), 
				regularCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT));
		
		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph(POINT_4)), 
				regularCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT));
		
		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph(POINT_5)), 
				regularCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT));
		
		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph(POINT_6)), 
				regularCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT));
		
		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph(POINT_7)), 
				regularCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT));
		
		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph(POINT_8)), 
				regularCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT));
		
		document.add(table);
		
	}

	private void generateStudentInfoFirstPageTable(Document document, DocumentLayoutSetup documentLayoutSetup,
			PdfFont boldFont, PdfFont regularFont, CellLayoutSetup cellLayoutSetup, CellLayoutSetup regularCellLayoutSetup,
												   StudentLite student, Institute institute) {
		
		int singleContentColumn = 1;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);
		
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData("Name : " + student.getName(),
		regularCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT))));
		document.add(table);

		int threeContentColumn = 3;
		table = getPDFTable(documentLayoutSetup, threeContentColumn);
		String standard = student.getStudentSessionData().getStandardName();
		String section = student.getStudentSessionData().getStandardSection() == null ? "" : student.getStudentSessionData().getStandardSection().getSectionName();
		String rollNumber = StringUtils.isEmpty(student.getStudentSessionData().getRollNumber()) ? "" : student.getStudentSessionData().getRollNumber();
		addRow(table, documentLayoutSetup, Arrays.asList(
		new CellData("Class : " + standard, regularCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)),
		new CellData("Section : " + section, regularCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)),
		new CellData("Roll No : " + rollNumber, regularCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT))));		
		document.add(table);
		
		addBlankLine(document, false, 1);
		
	}

	private void generateProgressReportTextTable(Document document, DocumentLayoutSetup documentLayoutSetup,
			PdfFont boldFont, PdfFont regularFont, CellLayoutSetup cellLayoutSetup, CellLayoutSetup regularCellLayoutSetup,
			StudentLite student, Institute institute) {
		int singleContentColumn = 1;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);
		addBlankLine(document, true, 1);
		addBlankLine(document, true, 1);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("PROGRESS REPORT")),
				cellLayoutSetup.copy().setFontSize(18f));
		
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("V - X")), cellLayoutSetup);
		
		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph(student.getStudentSessionData().getAcademicSessionName())), cellLayoutSetup);
		addBlankLine(document, true, 1);
		document.add(table);
		
	}

	private void generateSchoolNameDetailTable(Document document, DocumentLayoutSetup documentLayoutSetup,
			PdfFont boldFont, PdfFont regularFont, CellLayoutSetup cellLayoutSetup, 
			CellLayoutSetup regularCellLayoutSetup, StudentLite student, Institute institute) {
		
		int singleContentColumn = 1;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

		addBlankLine(document, true, 1);

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(SCHOOL_NAME_1)),
				cellLayoutSetup.copy().setFontSize(24f));
		
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(SCHOOL_NAME_2)),
				regularCellLayoutSetup);
		
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(SCHOOL_NAME_3)),
				regularCellLayoutSetup);
		
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(SCHOOL_NAME_4)),
				regularCellLayoutSetup);
		
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(SCHOOL_NAME_5)),
				cellLayoutSetup);
		
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(SCHOOL_NAME_6)),
				regularCellLayoutSetup);
		
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(SCHOOL_NAME_7)),
				regularCellLayoutSetup);
		
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(SCHOOL_NAME_8)),
				regularCellLayoutSetup);
		
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(SCHOOL_NAME_9)),
				regularCellLayoutSetup);
		
		document.add(table);
		
	}

	private void generateIndexPhoneNumberTable(Document document, DocumentLayoutSetup documentLayoutSetup,
			PdfFont boldFont, PdfFont regularFont,
											   StudentLite student, Institute institute) {
		int headlineContentColumn = 2;

		Table headlineTable = getPDFTable(documentLayoutSetup, headlineContentColumn);
		headlineTable.setFixedPosition(SQUARE_BORDER_MARGIN + 6,
				documentLayoutSetup.getPageSize().getHeight() - (SQUARE_BORDER_MARGIN * 2 + 30),
				documentLayoutSetup.getPageSize().getWidth() - documentLayoutSetup.getSideMargin() - 17f);

		CellLayoutSetup headlineCellLayoutSetup = new CellLayoutSetup();
		headlineCellLayoutSetup.setPdfFont(boldFont).setFontSize(11f).setTextAlignment(TextAlignment.LEFT);
		
		CellLayoutSetup regularHeadlineCellLayoutSetup = new CellLayoutSetup();
		regularHeadlineCellLayoutSetup.setPdfFont(regularFont).setFontSize(10f).setTextAlignment(TextAlignment.LEFT);

		addBlankLine(document, true, 1);
		addRow(headlineTable, documentLayoutSetup,
				Arrays.asList(new CellData("Index No. : A2-068", regularHeadlineCellLayoutSetup),
						new CellData("Code No.101320 (H.S.)",
								regularHeadlineCellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))));
		addRow(headlineTable, documentLayoutSetup,
				Arrays.asList(new CellData("", regularHeadlineCellLayoutSetup),
						new CellData("Phone : 2235-9336",
								regularHeadlineCellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))));

		document.add(headlineTable);
		
	}

	protected ExamReportCardLayoutData generateReportCardLayoutData(Institute institute, DocumentOutput documentOutput,
			float contentFontSize, float defaultBorderWidth)
			throws IOException {

		DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false);
		Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

		float logoWidth = 70f;
		float logoHeight = 70f;
		
		return new ExamReportCardLayoutData(document, documentLayoutSetup, null, null, contentFontSize,
				defaultBorderWidth, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(institute.getInstituteId()));
	}
	
	@Override
	public DocumentLayoutSetup initDocumentLayoutSetup(boolean officeCopy) {
		return initDocumentLayoutSetup(officeCopy, officeCopy ? DEFAULT_PAGE_SIZE.rotate() : DEFAULT_PAGE_SIZE,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN : DEFAULT_PAGE_TOP_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN : DEFAULT_PAGE_BOTTOM_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_SIDE_MARGIN : DEFAULT_PAGE_SIDE_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_MIDDLE_MARGIN : 0f);
	}
	
	@Override
	public DocumentOutput generateClassReport(Institute institute, String reportType,
													List<ExamReportData> examReportDataList, String documentName, StudentManager studentManager) {
		try {

			PdfFont regularFont = getCambriaFont();
			PdfFont boldFont = getCambriaBoldFont();

			float contentFontSize = 12f;
			float defaultBorderWidth = 0.5f;

			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput,
					contentFontSize, defaultBorderWidth);


			Document document = examReportCardLayoutData.getDocument();
			DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();

			sortClassExamReports(examReportDataList);

			int pageNumber = 1;
			for (ExamReportData examReportData : examReportDataList) {

				generateFirstPageLayout(examReportCardLayoutData, document, documentLayoutSetup, institute, boldFont, regularFont,
						examReportData.getStudentLite(), studentManager, reportType, pageNumber);

				document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				pageNumber++;

				generateSecondPageLayout(document, documentLayoutSetup, institute, boldFont, regularFont,
						contentFontSize, defaultBorderWidth, examReportData.getStudentLite(),
						studentManager, reportType, examReportData, pageNumber);

				if (pageNumber != examReportDataList.size() * 2) {
					document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				pageNumber++;

			}

			document.close();
			return documentOutput;

		} catch (IOException e) {
			e.printStackTrace();
		}

		return null;
	}

	@Override
	public DocumentOutput generateClassResultReport(Institute institute, String reportType, List<ExamReportData> examReportDataList,
													String documentName, StudentManager studentManager, int studentPerPage) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4);
			Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

			float logoWidth = 75f;
			float logoHeight = 75f;
			PdfFont boldFont = getRegularBoldFont();
			PdfFont regularFont = getRegularFont();
			int instituteId = institute.getInstituteId();
			ExamReportCardLayoutData examReportCardLayoutData = new ExamReportCardLayoutData(document, documentLayoutSetup, null, null, DEFAULT_FONT_SIZE,
					DEFAULT_BORDER_WIDTH, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId));

			CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
			marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(DEFAULT_FONT_SIZE)
					.setBorder(new SolidBorder(DEFAULT_BORDER_WIDTH)).setTextAlignment(TextAlignment.CENTER);

			CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
					.setTextAlignment(TextAlignment.LEFT);

			String instituteName = institute.getInstituteName().toUpperCase();
			String sessionName = "";
			if(!CollectionUtils.isEmpty(examReportDataList)) {
				sessionName = "Session : " + examReportDataList.get(0).getStudentLite().getStudentSessionData().getShortYearDisplayName();
			}
			generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName,
					sessionName, marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));

			sortClassExamReports(examReportDataList);
			int pageNumber = 1;
			for (ExamReportData examReportData : examReportDataList) {
				generateClassReportDataStudentDetails(documentLayoutSetup, document, courseCellLayoutSetup, examReportData.getStudentLite());
				generateCustomScholasticMarksGrid(document, documentLayoutSetup, DEFAULT_FONT_SIZE - 2,
						DEFAULT_BORDER_WIDTH, examReportData, reportType, GridConfigs.forOnlyObtainedTotalRow(), "Scholastic Subjects",
						0.2f);
				generateClassReportDataStudentResult(documentLayoutSetup, document, examReportData, courseCellLayoutSetup);
				if (pageNumber % studentPerPage == 0) {
					document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
					generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName, sessionName,
							marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));
				} else {
					addBlankLine(document, false, 1);
				}
				pageNumber++;
			}
			document.close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
					reportType, e);
		}
		return null;
	}
}
