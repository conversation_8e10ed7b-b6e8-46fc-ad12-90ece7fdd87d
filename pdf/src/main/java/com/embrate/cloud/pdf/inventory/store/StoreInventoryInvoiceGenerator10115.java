package com.embrate.cloud.pdf.inventory.store;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.layout.LayoutArea;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.inventory.InventoryTransactionType;
import com.lernen.cloud.core.api.inventory.InventoryUserType;
import com.lernen.cloud.core.api.inventory.PurchasedProduct;
import com.lernen.cloud.core.api.inventory.TransactionSummary;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.StringHelper;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.core.utils.images.WatermarkProvider;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.Arrays;

import static com.lernen.cloud.core.utils.NumberUtils.*;

/**
 * <AUTHOR>
 */
public class StoreInventoryInvoiceGenerator10115  extends StoreInventoryInvoiceGenerator {

    public StoreInventoryInvoiceGenerator10115(AssetProvider assetProvider) {
                super(assetProvider);
                //TODO Auto-generated constructor stub
        }

private static final Logger logger = LogManager.getLogger(StoreInventoryInvoiceGenerator10115.class);

    @Override
    public DocumentOutput generateInvoice(Institute institute, TransactionSummary transactionSummary,
                                          String documentName, boolean officeCopy) {
        try {

            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            DocumentLayoutData documentLayoutData = generateStoreInvoiceLayoutData(institute, documentOutput);
            DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
            Document document = documentLayoutData.getDocument();

            int pageNumber = 1;
            boolean addWaterMark = true;
            generateInvoice(documentLayoutData, documentLayoutSetup, document, institute, transactionSummary, pageNumber, addWaterMark, false);
            addWaterMark = false;
            LayoutArea currentArea = document.getRenderer().getCurrentArea();
            Rectangle rectangle = currentArea.getBBox();

            /**
             *  rectangle.getHeight() - gives position of last rectangle from bottom,
             *  so if position of last rectangle is below than the middle of the page + 20f
             *  adding next page
             */
            if(rectangle.getHeight() < (documentLayoutSetup.getPageSize().getHeight() / 2) + 20) {
                document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                addWaterMark = true;
                pageNumber++;
            } else {
                /**
                 *  adding newline for second slip
                 */
                while(rectangle.getHeight() > ((documentLayoutSetup.getPageSize().getHeight() / 2) - 20)) {
                    addBlankLine(document, false, 1);
                    currentArea = document.getRenderer().getCurrentArea();
                    rectangle = currentArea.getBBox();
                }
            }

            generateInvoice(documentLayoutData, documentLayoutSetup, document, institute, transactionSummary, pageNumber, addWaterMark, true);
            document.close();
            return documentOutput;

        } catch (Exception e) {
            logger.error("Unable to create invoice for transaction {}", transactionSummary.getTransactionId(), e);
        }
        return null;
    }

    private void generateInvoice(DocumentLayoutData documentLayoutData, DocumentLayoutSetup documentLayoutSetup, Document document, Institute institute,
                                 TransactionSummary transactionSummary, int pageNumber, boolean addWaterMark, boolean isOfficeCopy) throws IOException {

        float contentFontSize = 11f;
        float defaultBorderWidth = 0.1f;
        float bgImageHeightWidth = 300f;
        float headerFontSize = 11f;
        boolean includeInstituteName = true;

        float pageHeight = documentLayoutSetup.getPageSize().getHeight();
        if(addWaterMark) {
//            if(institute.getInstituteId() == 10115) {
//                generateBackgroundImage(document, documentLayoutSetup, ImageProvider.INSTANCE.getImage(
//                                ImageProvider._10115_BG_LOGO), bgImageHeightWidth, bgImageHeightWidth, bgImageHeightWidth - 150,
//                        (bgImageHeightWidth - 240) + (pageHeight / 2));
//
//                generateBackgroundImage(document, documentLayoutSetup, ImageProvider.INSTANCE.getImage(
//                                ImageProvider._10115_BG_LOGO), bgImageHeightWidth, bgImageHeightWidth, bgImageHeightWidth - 150,
//                        (bgImageHeightWidth - 240));
//            } else {
//                generateBackgroundImage(document, documentLayoutSetup, ImageProvider.INSTANCE.getImage(
//                                ImageProvider._702_BG_LOGO), bgImageHeightWidth, bgImageHeightWidth, bgImageHeightWidth - 150,
//                        (bgImageHeightWidth - 240) + (pageHeight / 2));
//
//                generateBackgroundImage(document, documentLayoutSetup, ImageProvider.INSTANCE.getImage(
//                                ImageProvider._702_BG_LOGO), bgImageHeightWidth, bgImageHeightWidth, bgImageHeightWidth - 150,
//                        (bgImageHeightWidth - 240));
//            }
                generateWatermark(documentLayoutData, institute.getInstituteId(), bgImageHeightWidth, bgImageHeightWidth, bgImageHeightWidth - 150,
                        (bgImageHeightWidth - 240) + (pageHeight / 2));

                generateWatermark(documentLayoutData, institute.getInstituteId(), bgImageHeightWidth, bgImageHeightWidth, bgImageHeightWidth - 150,
                        (bgImageHeightWidth - 240));
        }

        generateHeader(documentLayoutData, documentLayoutSetup, document, 45f, documentLayoutSetup.getPageSize().getHeight() * 0.87f + 10f,
                defaultBorderWidth, pageNumber, isOfficeCopy, institute);


        switch (transactionSummary.getTransactionType()) {
            case SALE:
            case SALES_RETURN:
                generateSalesInvoice(document, documentLayoutSetup, institute, headerFontSize, contentFontSize, transactionSummary,
                        defaultBorderWidth, includeInstituteName, pageNumber, isOfficeCopy);
                break;
            case PURCHASE:
            case RETURN:
                generatePurchaseInvoice(document, documentLayoutSetup, institute, headerFontSize, contentFontSize, transactionSummary,
                        defaultBorderWidth, includeInstituteName);
                break;
            case ISSUE:
                generateIssueInvoice(document, documentLayoutSetup, institute, headerFontSize, contentFontSize, transactionSummary,
                        defaultBorderWidth, includeInstituteName);
                break;
            default:
                break;
        }
    }

    public void generatePaymentSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
                                       float contentFontSize, TransactionSummary transactionSummary, float defaultBorderWidth)
            throws IOException {
        InventoryUserType userType = transactionSummary.getInventoryUserType();
        int singleContentColumn = 2;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup keyCellLayoutSetup = new CellLayoutSetup();
        keyCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.LEFT).setBorderLeft(new SolidBorder(defaultBorderWidth));

        CellLayoutSetup valueCellLayoutSetup = keyCellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT)
                .setBorderRight(new SolidBorder(defaultBorderWidth)).setBorderLeft(null);

        CellLayoutSetup keyBoldCellLayoutSetup = keyCellLayoutSetup.copy().setPdfFont(getRegularBoldFont());
        CellLayoutSetup valueBoldCellLayoutSetup = valueCellLayoutSetup.copy().setPdfFont(getRegularBoldFont());

        int totalUnits = 0;
        double totalPrice = 0;
        double totalDiscount = 0;
        double totalTax = 0;
        for (PurchasedProduct purchasedProduct : transactionSummary.getPurchasedProducts()) {
            totalUnits += purchasedProduct.getQuantity();
            totalPrice += purchasedProduct.getTotalPrice();
            totalDiscount += purchasedProduct.getTotalDiscount();
            totalTax += purchasedProduct.getTotalTax();
        }

        addRow(table, documentLayoutSetup, Arrays.asList(new CellData("Total Units:", keyCellLayoutSetup),
                new CellData(String.valueOf(totalUnits), valueCellLayoutSetup)));

        if(transactionSummary.getTransactionType() == InventoryTransactionType.ISSUE){
            document.add(table);
            addRemarkSection(document, documentLayoutSetup, contentFontSize, transactionSummary, defaultBorderWidth);
            return;
        }

        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData("Sub Total:", keyCellLayoutSetup), new CellData(
                        formatToRupees(totalPrice), valueCellLayoutSetup)));
        if (Double.compare(totalDiscount, 0d) > 0) {
            addRow(table, documentLayoutSetup,
                    Arrays.asList(new CellData("Total Discount:", keyCellLayoutSetup), new CellData(
                            formatToRupees(totalDiscount), valueCellLayoutSetup)));
        }

        if (Double.compare(totalTax, 0d) > 0) {
            addRow(table, documentLayoutSetup,
                    Arrays.asList(new CellData("Total Tax:", keyCellLayoutSetup), new CellData(
                            formatToRupees(totalTax), valueCellLayoutSetup)));
        }


        if (Double.compare(transactionSummary.getAdditionalCost(), 0d) > 0) {
            addRow(table, documentLayoutSetup,
                    Arrays.asList(new CellData("Additional Cost:", keyCellLayoutSetup), new CellData(
                            formatToRupees(transactionSummary.getAdditionalCost()), valueCellLayoutSetup)));
        }

        if (Double.compare(transactionSummary.getAdditionalDiscount(), 0d) > 0) {
            addRow(table, documentLayoutSetup,
                    Arrays.asList(new CellData("Additional Discount:", keyCellLayoutSetup), new CellData(
                            formatToRupees(transactionSummary.getAdditionalDiscount()), valueCellLayoutSetup)));
        }

        double netAdditionalAmount = subtractValues(transactionSummary.getAdditionalCost(), transactionSummary.getAdditionalDiscount());
        double netAmount = addValues(subtractValues(addValues(totalPrice, totalTax), totalDiscount), netAdditionalAmount);
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData("Net Amount:", keyBoldCellLayoutSetup),
                        new CellData(formatToRupees(netAmount),
                                valueBoldCellLayoutSetup)));

        if(transactionSummary.getTransactionType() == InventoryTransactionType.SALES_RETURN){
            if(userType == InventoryUserType.STUDENT ){
                if(transactionSummary.getWalletCreditAmount() != null && transactionSummary.getWalletCreditAmount() > 0){
                    addRow(table, documentLayoutSetup,
                            Arrays.asList(new CellData("Refund To Wallet:", keyBoldCellLayoutSetup), new CellData(
                                    formatToRupees(transactionSummary.getWalletCreditAmount()), valueBoldCellLayoutSetup)));
                }
            }
            if(lteZeroOrNull(transactionSummary.getPaidAmount())){
                addRow(table, documentLayoutSetup,
                        Arrays.asList(new CellData("Refund ("+ transactionSummary.getTransactionMode().getDisplayName() +"):", keyBoldCellLayoutSetup),
                                new CellData(formatToRupees(transactionSummary.getPaidAmount()),
                                        valueBoldCellLayoutSetup)));
            }
        }else{
            if(userType == InventoryUserType.STUDENT ){
                if(transactionSummary.getUsedWalletAmount() != null && transactionSummary.getUsedWalletAmount() > 0){
                    addRow(table, documentLayoutSetup,
                            Arrays.asList(new CellData("Paid From Wallet:", keyBoldCellLayoutSetup), new CellData(
                                    formatToRupees(transactionSummary.getUsedWalletAmount()), valueBoldCellLayoutSetup)));
                }
                if(transactionSummary.getWalletCreditAmount() != null && transactionSummary.getWalletCreditAmount() > 0){
                    addRow(table, documentLayoutSetup,
                            Arrays.asList(new CellData("Credit Amount:", keyBoldCellLayoutSetup), new CellData(
                                    formatToRupees(transactionSummary.getWalletCreditAmount()), valueBoldCellLayoutSetup)));
                }
            }

            addRow(table, documentLayoutSetup,
                    Arrays.asList(new CellData("Paid ("+ transactionSummary.getTransactionMode().getDisplayName() +"):", keyBoldCellLayoutSetup),
                            new CellData(formatToRupees(transactionSummary.getPaidAmount()),
                                    valueBoldCellLayoutSetup)));
        }

        document.add(table);

        addRemarkSection(document, documentLayoutSetup, contentFontSize, transactionSummary, defaultBorderWidth);

    }

    private void addRemarkSection(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
                                  TransactionSummary transactionSummary, float defaultBorderWidth) throws IOException {

        int singleContentColumn = 1;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
        signatureCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.RIGHT).setBorder(new SolidBorder(defaultBorderWidth))
                .setBorderBottom(null).setPaddingBottom(10f);

        addRow(table, documentLayoutSetup, Arrays.asList(new Paragraph(EMPTY_TEXT)), signatureCellLayoutSetup);

        document.add(table);

        singleContentColumn = 1;
        table = getPDFTable(documentLayoutSetup, singleContentColumn);
        Paragraph remarks = getKeyValueParagraph("Remarks: ",
                transactionSummary.getDescription());

        CellLayoutSetup remarksCellLayoutSetup = new CellLayoutSetup();
        remarksCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.LEFT).setBorder(new SolidBorder(defaultBorderWidth)).setBorderTop(null);


        if(StringUtils.isNotBlank(transactionSummary.getReference())){
            CellLayoutSetup refCellLayoutSetup = new CellLayoutSetup();
            refCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize)
                    .setTextAlignment(TextAlignment.LEFT).setBorder(new SolidBorder(defaultBorderWidth)).setBorderBottom(null).setBorderTop(null);
            addRow(table, documentLayoutSetup, Arrays.asList(getKeyValueParagraph("Reference: ",
                    transactionSummary.getReference())), refCellLayoutSetup);

        }
        addRow(table, documentLayoutSetup, Arrays.asList(remarks), remarksCellLayoutSetup);
        document.add(table);
    }

    public void generateSalesInvoice(Document document, DocumentLayoutSetup documentLayoutSetup,Institute institute, float headerFontSize,
                                     float contentFontSize, TransactionSummary transactionSummary, float defaultBorderWidth, boolean includeInstituteName,
                                     int pageNumber, boolean isOfficeCopy)
            throws IOException {
        generateSalesInvoiceHeader(document, documentLayoutSetup, transactionSummary, institute, headerFontSize, contentFontSize,
                defaultBorderWidth, includeInstituteName);
        addBlankLine(document, false, 1);
        generateItemsContent(document, documentLayoutSetup, contentFontSize, transactionSummary,
                defaultBorderWidth);
        generatePaymentSummary(document, documentLayoutSetup, contentFontSize, transactionSummary,
                defaultBorderWidth);
        if (transactionSummary.getTransactionType()
                == InventoryTransactionType.SALES_RETURN) {
            generateReturnedWatermark(documentLayoutSetup, document, pageNumber, isOfficeCopy);

        }
    }

    public void generateIssueInvoice(Document document, DocumentLayoutSetup documentLayoutSetup,Institute institute, float headerFontSize,
                                     float contentFontSize, TransactionSummary transactionSummary, float defaultBorderWidth, boolean includeInstituteName)
            throws IOException {
        generateSalesInvoiceHeader(document, documentLayoutSetup, transactionSummary, institute, headerFontSize, contentFontSize,
                defaultBorderWidth, includeInstituteName);
        addBlankLine(document, false, 1);
        generateItemsContent(document, documentLayoutSetup, contentFontSize, transactionSummary,
                defaultBorderWidth);
        generatePaymentSummary(document, documentLayoutSetup, contentFontSize, transactionSummary,
                defaultBorderWidth);
    }

    public void generatePurchaseInvoice(Document document, DocumentLayoutSetup documentLayoutSetup,Institute institute, float headerFontSize,
                                        float contentFontSize, TransactionSummary transactionSummary, float defaultBorderWidth, boolean includeInstituteName)
            throws IOException {
        generatePurchaseInvoiceHeader(document, documentLayoutSetup, transactionSummary, institute, headerFontSize, contentFontSize,
                defaultBorderWidth, includeInstituteName);
//			generateCustomerInformation(document, documentLayoutSetup, contentFontSize, transactionSummary,
//					defaultBorderWidth);
        addBlankLine(document, false, 1);
        generateItemsContent(document, documentLayoutSetup, contentFontSize, transactionSummary,
                defaultBorderWidth);
        generatePaymentSummary(document, documentLayoutSetup, contentFontSize, transactionSummary,
                defaultBorderWidth);
    }

    public void generateSalesInvoiceHeader(Document document, DocumentLayoutSetup documentLayoutSetup,
                                           TransactionSummary transactionSummary, Institute institute, float headerFontSize,
                                           float contentFontSize, float defaultBorderWidth, boolean includeInstituteName) throws IOException {

        float instituteFontSize = headerFontSize + 1;
        int singleContentColumn = 1;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();

        CellLayoutSetup headerCellLayoutSetup = new CellLayoutSetup();
        headerCellLayoutSetup.setPdfFont(getRegularBoldFont()).setTextAlignment(TextAlignment.CENTER);

        if(transactionSummary.getTransactionType() == InventoryTransactionType.ISSUE){
            addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Issue Receipt")),
                    headerCellLayoutSetup.setFontSize(headerFontSize));
        }else{
            addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Sales Order Receipt")),
                    headerCellLayoutSetup.setFontSize(headerFontSize));
        }

        document.add(table);

        table = getPDFTable(documentLayoutSetup, singleContentColumn);
        cellLayoutSetup.setTextAlignment(TextAlignment.RIGHT);
        String customerName = transactionSummary.getInventoryUserType() == InventoryUserType.STUDENT ?
                transactionSummary.getStudentLite().getName() + " (" + transactionSummary.getStudentLite().getAdmissionNumber() + ")"
                : transactionSummary.getBuyerName();

        addBlankLine(document, false, 1);
        addRow(table, documentLayoutSetup, Arrays.asList(getKeyValueParagraph("Invoice: ", transactionSummary.getTransactionId().toString())),
                cellLayoutSetup.setFontSize(contentFontSize));
        addRow(table, documentLayoutSetup, Arrays.asList(getKeyValueParagraph("Customer's Name: ", customerName)),
                cellLayoutSetup.setFontSize(contentFontSize));
        addRow(table, documentLayoutSetup, Arrays.asList(getKeyValueParagraph("Date: ", DateUtils.getFormattedDate((int) (transactionSummary.getTransactionDate()/1000l)))),
                cellLayoutSetup.setFontSize(contentFontSize));

        document.add(table);

    }

    public void generatePurchaseInvoiceHeader(Document document, DocumentLayoutSetup documentLayoutSetup,
                                              TransactionSummary transactionSummary, Institute institute, float headerFontSize,
                                              float contentFontSize, float defaultBorderWidth, boolean includeInstituteName) throws IOException {

        float instituteFontSize = headerFontSize + 1;
        int singleContentColumn = 1;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup headerCellLayoutSetup = new CellLayoutSetup();
        headerCellLayoutSetup.setPdfFont(getRegularBoldFont()).setTextAlignment(TextAlignment.CENTER);

        String purchaseReturnKey = "";
        if(transactionSummary.getTransactionType() == InventoryTransactionType.RETURN){
            addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Seller Return Order Receipt")),
                    headerCellLayoutSetup.setFontSize(headerFontSize));
            purchaseReturnKey = "Returned To";
        }else{
            addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Purchase Order Receipt")),
                    headerCellLayoutSetup.setFontSize(headerFontSize));
            purchaseReturnKey = "Purchased From";
        }

        document.add(table);
        addBlankLine(document, false, 1);

        singleContentColumn = 2;
        table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize);

        CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
        CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);

        addRow(table, documentLayoutSetup, Arrays.asList(new CellData(purchaseReturnKey, firstCellLayoutSetup.copy().setPdfFont(getRegularBoldFont())),
                new CellData(getKeyValueParagraph("Invoice: ", transactionSummary.getTransactionId().toString()), thirdCellLayoutSetup)));
        addRow(table, documentLayoutSetup, Arrays.asList(new CellData(transactionSummary.getVendor().getVendorName(), firstCellLayoutSetup),
                new CellData(getKeyValueParagraph("Payment Mode: ", transactionSummary.getTransactionMode().getDisplayName()), thirdCellLayoutSetup)));
        addRow(table, documentLayoutSetup, Arrays.asList(new CellData(transactionSummary.getVendor().getAddress(), firstCellLayoutSetup),
                new CellData(getKeyValueParagraph("Date: ", DateUtils.getFormattedDate((int) (transactionSummary.getTransactionDate()/1000l))), thirdCellLayoutSetup)));
        addRow(table, documentLayoutSetup, Arrays.asList(new CellData(StringHelper.getAddress(transactionSummary.getVendor().getCity(),transactionSummary.getVendor().getState(), transactionSummary.getVendor().getZipcode()), firstCellLayoutSetup),
                new CellData(EMPTY_TEXT, thirdCellLayoutSetup)));

        document.add(table);

    }

    private void generateReturnedWatermark(DocumentLayoutSetup documentLayoutSetup, Document document,
                                            int pageNumber, boolean isOfficeCopy) throws IOException {

        PdfPage pdfPage = document.getPdfDocument().getPage(pageNumber);
        Rectangle pagesize = pdfPage.getPageSizeWithRotation();

        float x = documentLayoutSetup.isOfficeCopy() ? pagesize.getWidth() / 8 - 50 : pagesize.getWidth() / 4 + 30;
        float y = documentLayoutSetup.isOfficeCopy() ? pagesize.getHeight() * 0.75f : pagesize.getHeight() * 0.70f;

        if(isOfficeCopy) {
            if(pageNumber == 1) {
                y -= (pagesize.getHeight() / 2);
            }
        }
        addWaterMark(document, ImageProvider.INSTANCE.getImage(ImageProvider.RETURNED_TEXT_IMAGE), x, y, pageNumber);
    }

    protected void generateHeader(DocumentLayoutData documentLayoutData, DocumentLayoutSetup documentLayoutSetup, Document document,
                                  float offsetX, float offsetY, float defaultBorderWidth, int pageNumber, boolean isOfficeCopy,
                                  Institute institute) throws IOException {
        if(isOfficeCopy) {
            if(pageNumber == 1) {
                LayoutArea currentArea = document.getRenderer().getCurrentArea();
                Rectangle rectangle = currentArea.getBBox();
                generateDynamicImageProvider(documentLayoutData, offsetX,
                        rectangle.getHeight() - (documentLayoutData.getLogoHeight() * 0.75f), institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);
            } else {
                generateDynamicImageProvider(documentLayoutData, offsetX, offsetY, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);
            }
        } else {
            generateDynamicImageProvider(documentLayoutData, offsetX, offsetY, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);
        }

        int singleContentColumn = 1;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(12f).setTextAlignment(TextAlignment.CENTER);

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getInstituteName().toUpperCase())),
                cellLayoutSetup.copy().setFontSize(13f));
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine1())),
                cellLayoutSetup.copy().setFontSize(11f).setPdfFont(getRegularFont()));
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine2())),
                cellLayoutSetup.copy().setFontSize(11f).setPdfFont(getRegularFont()));

        document.add(table);
    }

    protected void generateLogo(DocumentLayoutData documentLayoutData, DocumentLayoutSetup documentLayoutSetup, Document document,
                                byte[] logo, float offsetX, float offsetY) throws MalformedURLException, IOException {

        generateImage(document, documentLayoutSetup, logo,
                documentLayoutData.getLogoWidth(), documentLayoutData.getLogoHeight(), offsetX, offsetY);
    }

    protected void generateBackgroundImage(Document document, DocumentLayoutSetup documentLayoutSetup, byte[] image, float width,
                                           float height, float offsetX, float offsetY)  throws IOException {
        generateImage(document, documentLayoutSetup, image, width, height, offsetX, offsetY);
    }

    protected DocumentLayoutData generateStoreInvoiceLayoutData(Institute institute, DocumentOutput documentOutput)
            throws IOException {

        DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false);
        float contentFontSize = 12f;
        float defaultBorderWidth = 0.5f;
        Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
        float logoWidth = 80f;
        float logoHeight = 80f;
        int instituteId = institute.getInstituteId();
        return new DocumentLayoutData(document, documentLayoutSetup, null, null, contentFontSize,
                defaultBorderWidth, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId), null, null);
    }

}
