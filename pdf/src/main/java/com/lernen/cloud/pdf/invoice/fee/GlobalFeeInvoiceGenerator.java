package com.lernen.cloud.pdf.invoice.fee;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.configurations.FeeInvoicePreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.fees.payment.*;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.transport.StudentTransportDetails;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class GlobalFeeInvoiceGenerator extends FeeInvoiceGenerator {

	public GlobalFeeInvoiceGenerator(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	@Override
	public DocumentOutput generateInvoice(Institute institute, FeePaymentInvoiceSummary feePaymentInvoiceSummary,
										  StudentTransportDetails studentTransportDetails, String documentName, boolean officeCopy, UserType userType, StudentFeesDetailsLite studentFeesDetailsLite, FeeInvoicePreferences feeInvoicePreferences) {
		try {
			DocumentOutput invoice = new DocumentOutput(documentName, new ByteArrayOutputStream());
			if(userType == UserType.STUDENT) {
				officeCopy = false;
			}
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(officeCopy);
			float contentFontSize = 9f;

			Document document = initDocument(invoice.getContent(), documentLayoutSetup);
			DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getNunitoRegularFont(), getAleoBoldFont(), null, null, DEFAULT_LOGO_WIDTH, DEFAULT_LOGO_HEIGHT, LogoProvider.INSTANCE.getLogo(institute.getInstituteId()), getGlacialRegularFont(), getHindiBoldFont());
			
			generateFeeInvoice(institute, documentLayoutData, feePaymentInvoiceSummary,
					contentFontSize, 1, STUDENT_COPY_TEXT, userType);
			document.close();
			return invoice;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	private void generateFeeInvoice(Institute institute, DocumentLayoutData documentLayoutData,
									FeePaymentInvoiceSummary feePaymentInvoiceSummary,
									float contentFontSize, int pageNumber, String studentCopyText, UserType userType) throws IOException {
		
		Document document = documentLayoutData.getDocument();
		DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
		
		//generateLogo(document, documentLayoutSetup, LogoProvider.INSTANCE.getLogo(institute.getInstituteId()));
		generateHeader(document, documentLayoutSetup, feePaymentInvoiceSummary, institute, studentCopyText, userType);
		generateStudentInformation(document, documentLayoutSetup, contentFontSize, feePaymentInvoiceSummary);
		generateFeeContent(document, documentLayoutSetup, contentFontSize, feePaymentInvoiceSummary);
		generateFeePaymentSummary(document, documentLayoutSetup, contentFontSize, feePaymentInvoiceSummary);
		generateSignatureBox(document, documentLayoutSetup, contentFontSize);

		if (feePaymentInvoiceSummary.getFeePaymentTransactionMetaData()
				.getFeePaymentTransactionStatus() == FeePaymentTransactionStatus.CANCELLED) {
			// addWaterMark(document, documentLayoutSetup, "Cancelled");
			PdfPage pdfPage = document.getPdfDocument().getPage(pageNumber);
			Rectangle pagesize = pdfPage.getPageSizeWithRotation();

			float x = documentLayoutSetup.isOfficeCopy() ? pagesize.getWidth() / 8 - 50 : pagesize.getWidth() / 4 + 30;
			float y = documentLayoutSetup.isOfficeCopy() ? pagesize.getHeight() * 0.75f : pagesize.getHeight() * 0.70f;

			addWaterMark(document, ImageProvider.INSTANCE.getImage(ImageProvider.CANCELLED_TEXT_IMAGE2), x, y, pageNumber);
		}
	}


	private void generateHeader(Document document, DocumentLayoutSetup documentLayoutSetup,
			FeePaymentInvoiceSummary feePaymentInvoiceSummary, Institute institute, String studentCopyText, UserType userType) throws IOException {
		int singleContentColumn = 1;
		int columnCount = getColumnCount(documentLayoutSetup.isOfficeCopy(), singleContentColumn);
		float contentWidth = getContentWidth(documentLayoutSetup);
		Table table = new Table(columnCount);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setWidth(contentWidth).setPdfFont(getRegularBoldFont()).setTextAlignment(TextAlignment.CENTER);

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getInstituteName())), cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine1())),
				cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine2())),
				cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(EMPTY_TEXT)), cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(EMPTY_TEXT)), cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(studentCopyText)),
				Arrays.asList(getParagraph(userType == UserType.STUDENT ? STUDENT_COPY_TEXT : OFFICE_COPY_TEXT)), cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(EMPTY_TEXT)), cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(EMPTY_TEXT)), cellLayoutSetup);
		document.add(table);

		// Student copy section
		singleContentColumn = 3;
		columnCount = getColumnCount(documentLayoutSetup.isOfficeCopy(), singleContentColumn);
		contentWidth = getContentWidth(documentLayoutSetup);
		table = new Table(columnCount);

		cellLayoutSetup.setWidth(contentWidth / singleContentColumn).setBorderBottom(new SolidBorder(1));

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData("", cellLayoutSetup), new CellData("School Fee Receipt", cellLayoutSetup),
						new CellData(
								feePaymentInvoiceSummary.getStudent().getStudentAcademicSessionInfoResponse()
										.getAcademicSession().getYearDisplayName(),
								cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))));
		document.add(table);

	}

	private void generateStudentInformation(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize, FeePaymentInvoiceSummary feePaymentInvoiceSummary) throws IOException {
		int singleContentColumn = 3;
		int columnCount = getColumnCount(documentLayoutSetup.isOfficeCopy(), singleContentColumn);
		float contentWidth = getContentWidth(documentLayoutSetup);
		Table table = new Table(columnCount);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setWidth(contentWidth / singleContentColumn).setPdfFont(getRegularBoldFont())
				.setFontSize(contentFontSize);

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)
				.setBorderLeft(new SolidBorder(1));
		CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER);
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT)
				.setBorderRight(new SolidBorder(1));

		Paragraph receipt = getKeyValueParagraph("Receipt No. ",
				feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getInvoiceId());

		Text classValue = new Text(feePaymentInvoiceSummary.getStudent().getStudentAcademicSessionInfoResponse()
				.getStandard().getDisplayNameWithSection()).setFont(getRegularFont());
		Paragraph classContent = new Paragraph();
		classContent.add(classValue);

		Paragraph date = getKeyValueParagraph("Date: ",
				DateUtils.getFormattedDate(
						feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getTransactionDate(), DATE_FORMAT,
						User.DFAULT_TIMEZONE));

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(receipt, firstCellLayoutSetup),
				new CellData(classContent, secondCellLayoutSetup), new CellData(date, thirdCellLayoutSetup)));

		document.add(table);

		// Single column content
		singleContentColumn = 1;
		columnCount = getColumnCount(documentLayoutSetup.isOfficeCopy(), singleContentColumn);
		cellLayoutSetup.setWidth(contentWidth / singleContentColumn).setTextAlignment(TextAlignment.LEFT)
				.setBorderLeft(new SolidBorder(1)).setBorderRight(new SolidBorder(1));

		table = new Table(columnCount);
		Paragraph admissionNumber = getKeyValueParagraph("Admission No. ",
				feePaymentInvoiceSummary.getStudent().getStudentBasicInfo().getAdmissionNumber());

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(admissionNumber, cellLayoutSetup)));

		Paragraph studentName = getKeyValueParagraph("Student Name : ",
				feePaymentInvoiceSummary.getStudent().getStudentBasicInfo().getName());
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(studentName, cellLayoutSetup)));

		Paragraph fatherName = getKeyValueParagraph("Father Name : ",
				feePaymentInvoiceSummary.getStudent().getStudentFamilyInfo().getFathersName());

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(fatherName, cellLayoutSetup)));

		Paragraph motherName = getKeyValueParagraph("Mother Name : ",
				feePaymentInvoiceSummary.getStudent().getStudentFamilyInfo().getMothersName());
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(motherName, cellLayoutSetup.setBorderBottom(new SolidBorder(1)))));

		document.add(table);
	}

	private void generateFeeContent(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
			FeePaymentInvoiceSummary feePaymentInvoiceSummary) throws IOException {

		if (feePaymentInvoiceSummary.getFeePaymentTransactionMetaData()
				.getFeePaymentTransactionStatus() == FeePaymentTransactionStatus.ACTIVE) {
			generateActiveFeeContent(document, documentLayoutSetup, contentFontSize, feePaymentInvoiceSummary);
		} else {
			generateCancelledFeeContent(document, documentLayoutSetup, contentFontSize, feePaymentInvoiceSummary);
		}
	}

	private void generateCancelledFeeContent(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize, FeePaymentInvoiceSummary feePaymentInvoiceSummary) throws IOException {

		if (feePaymentInvoiceSummary.getFeePaymentTransactionDetails() == null || CollectionUtils.isEmpty(
				feePaymentInvoiceSummary.getFeePaymentTransactionDetails().getFeeIdFeeHeadTransactionDetails())) {
			return;
		}

		int singleFeeContentColumn = 1;
		int columnCount = getColumnCount(documentLayoutSetup.isOfficeCopy(), singleFeeContentColumn);
		float contentWidth = getContentWidth(documentLayoutSetup);
		CellLayoutSetup feeCellLayoutSetup = new CellLayoutSetup();
		feeCellLayoutSetup.setWidth(contentWidth / singleFeeContentColumn).setPdfFont(getRegularBoldFont())
				.setFontSize(contentFontSize).setTextAlignment(TextAlignment.LEFT).setBorderLeft(new SolidBorder(1))
				.setBorderRight(new SolidBorder(1));

		Table feeTable = new Table(columnCount);
		int count = 0;
		for (FeeIdFeeHeadTransactionDetails feeIdFeeHeadTransactionDetails : feePaymentInvoiceSummary
				.getFeePaymentTransactionDetails().getFeeIdFeeHeadTransactionDetails()) {
			count++;
			feeTable = new Table(columnCount);
			addRow(feeTable, documentLayoutSetup,
					Arrays.asList(
							getParagraph(feeIdFeeHeadTransactionDetails.getFeeConfigurationBasicInfo().getFeeName())),
					feeCellLayoutSetup);
			document.add(feeTable);

			int singleFeeHeadContentColumn = 3;
			int feeHeadcolumnCount = getColumnCount(documentLayoutSetup.isOfficeCopy(), singleFeeHeadContentColumn);
			Table feeHeadTable = new Table(feeHeadcolumnCount);

			CellLayoutSetup feeHeadCellLayoutSetup = new CellLayoutSetup();
			feeHeadCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
					.setBorder(new SolidBorder(1)).setTextAlignment(TextAlignment.LEFT);

			CellLayoutSetup firstFeeHeadCellLayoutSetup = feeHeadCellLayoutSetup.copy()
					.setWidth(contentWidth * DEFAULT_CANCELLED_FEE_HEAD_HEADER_WIDTH[0]);
			CellLayoutSetup secondFeeHeadCellLayoutSetup = feeHeadCellLayoutSetup.copy()
					.setWidth(contentWidth * DEFAULT_CANCELLED_FEE_HEAD_HEADER_WIDTH[1]);
			CellLayoutSetup thirdFeeHeadCellLayoutSetup = feeHeadCellLayoutSetup.copy()
					.setWidth(contentWidth * DEFAULT_CANCELLED_FEE_HEAD_HEADER_WIDTH[2])
					.setPdfFont(getRegularBoldFont());
			// CellLayoutSetup fourthFeeHeadCellLayoutSetup =
			// feeHeadCellLayoutSetup.copy()
			// .setWidth(contentWidth *
			// DEFAULT_FEE_HEAD_HEADER_WIDTH[3]).setPdfFont(getRegularBoldFont());

			if (count == 1) {
				addRow(feeHeadTable, documentLayoutSetup,
						Arrays.asList(new CellData("#", firstFeeHeadCellLayoutSetup),
								new CellData("Particulars", secondFeeHeadCellLayoutSetup),
								new CellData("Paid Amount(INR)", thirdFeeHeadCellLayoutSetup)));
			}
			int feeHeadCount = 1;
			for (FeeHeadTransactionAmountsDetails feeHeadTransactionAmountsDetails : feeIdFeeHeadTransactionDetails
					.getFeeHeadTransactionAmountsDetails()) {
				addRow(feeHeadTable, documentLayoutSetup,
						Arrays.asList(
								new CellData(String.valueOf(feeHeadCount++),
										firstFeeHeadCellLayoutSetup.setPdfFont(getRegularFont())),
								new CellData(feeHeadTransactionAmountsDetails.getFeeHeadConfiguration().getFeeHead(),
										secondFeeHeadCellLayoutSetup.setPdfFont(getRegularFont())),
								new CellData(String.valueOf(feeHeadTransactionAmountsDetails.getPaidAmount()),
										thirdFeeHeadCellLayoutSetup.setPdfFont(getRegularFont()))));
			}
			document.add(feeHeadTable);
		}
	}

	private void generateActiveFeeContent(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize, FeePaymentInvoiceSummary feePaymentInvoiceSummary) throws IOException {

		if (CollectionUtils.isEmpty(feePaymentInvoiceSummary.getFeeIdInvoices())) {
			return;
		}

		int singleFeeContentColumn = 1;
		int columnCount = getColumnCount(documentLayoutSetup.isOfficeCopy(), singleFeeContentColumn);
		float contentWidth = getContentWidth(documentLayoutSetup);
		CellLayoutSetup feeCellLayoutSetup = new CellLayoutSetup();
		feeCellLayoutSetup.setWidth(contentWidth / singleFeeContentColumn).setPdfFont(getRegularBoldFont())
				.setFontSize(contentFontSize).setTextAlignment(TextAlignment.LEFT).setBorderLeft(new SolidBorder(1))
				.setBorderRight(new SolidBorder(1));

		Table feeTable = new Table(columnCount);
		int count = 0;
		for (FeeIdInvoice feeIdInvoice : feePaymentInvoiceSummary.getFeeIdInvoices()) {
			count++;
			feeTable = new Table(columnCount);
			addRow(feeTable, documentLayoutSetup,
					Arrays.asList(getParagraph(feeIdInvoice.getFeeConfigurationBasicInfo().getFeeName())),
					feeCellLayoutSetup);
			document.add(feeTable);

			int singleFeeHeadContentColumn = 4;
			int feeHeadcolumnCount = getColumnCount(documentLayoutSetup.isOfficeCopy(), singleFeeHeadContentColumn);
			Table feeHeadTable = new Table(feeHeadcolumnCount);

			CellLayoutSetup feeHeadCellLayoutSetup = new CellLayoutSetup();
			feeHeadCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
					.setBorder(new SolidBorder(1)).setTextAlignment(TextAlignment.LEFT);

			CellLayoutSetup firstFeeHeadCellLayoutSetup = feeHeadCellLayoutSetup.copy()
					.setWidth(contentWidth * DEFAULT_ACTIVE_FEE_HEAD_HEADER_WIDTH[0]);
			CellLayoutSetup secondFeeHeadCellLayoutSetup = feeHeadCellLayoutSetup.copy()
					.setWidth(contentWidth * DEFAULT_ACTIVE_FEE_HEAD_HEADER_WIDTH[1]);
			CellLayoutSetup thirdFeeHeadCellLayoutSetup = feeHeadCellLayoutSetup.copy()
					.setWidth(contentWidth * DEFAULT_ACTIVE_FEE_HEAD_HEADER_WIDTH[2]).setPdfFont(getRegularBoldFont());
			CellLayoutSetup fourthFeeHeadCellLayoutSetup = feeHeadCellLayoutSetup.copy()
					.setWidth(contentWidth * DEFAULT_ACTIVE_FEE_HEAD_HEADER_WIDTH[3]).setPdfFont(getRegularBoldFont());

			if (count == 1) {
				addRow(feeHeadTable, documentLayoutSetup,
						Arrays.asList(new CellData("#", firstFeeHeadCellLayoutSetup),
								new CellData("Particulars", secondFeeHeadCellLayoutSetup),
								new CellData("Amount(INR)", thirdFeeHeadCellLayoutSetup),
								new CellData("Discount", fourthFeeHeadCellLayoutSetup)));
			}
			int feeHeadCount = 1;
			for (FeeHeadInvoice feeHeadInvoice : feeIdInvoice.getFeeHeadInvoices()) {
				addRow(feeHeadTable, documentLayoutSetup, Arrays.asList(
						new CellData(String.valueOf(feeHeadCount++),
								firstFeeHeadCellLayoutSetup.setPdfFont(getRegularFont())),
						new CellData(feeHeadInvoice.getFeeHeadPaymentDetails().getFeeHeadConfiguration().getFeeHead(),
								secondFeeHeadCellLayoutSetup.setPdfFont(getRegularFont())),
						new CellData(String.valueOf(feeHeadInvoice.getFeeHeadPaymentDetails().getAssignedAmount()),
								thirdFeeHeadCellLayoutSetup.setPdfFont(getRegularFont())),
						new CellData(String.valueOf(feeHeadInvoice.getFeeHeadPaymentDetails().getTotalDiscount()),
								fourthFeeHeadCellLayoutSetup.setPdfFont(getRegularFont()))));
			}
			document.add(feeHeadTable);
		}

	}

	private void generateFeePaymentSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize, FeePaymentInvoiceSummary feePaymentInvoiceSummary) throws IOException {

		if (feePaymentInvoiceSummary.getFeePaymentTransactionMetaData()
				.getFeePaymentTransactionStatus() == FeePaymentTransactionStatus.ACTIVE) {
			generateActiveFeePaymentSummary(document, documentLayoutSetup, contentFontSize, feePaymentInvoiceSummary);
		} else {
			generateCancelledFeePaymentSummary(document, documentLayoutSetup, contentFontSize,
					feePaymentInvoiceSummary);
		}
	}

	private void generateCancelledFeePaymentSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize, FeePaymentInvoiceSummary feePaymentInvoiceSummary) throws IOException {
		int singleContentColumn = 2;
		int columnCount = getColumnCount(documentLayoutSetup.isOfficeCopy(), singleContentColumn);
		float contentWidth = getContentWidth(documentLayoutSetup);
		Table table = new Table(columnCount);

		CellLayoutSetup keyCellLayoutSetup = new CellLayoutSetup();
		keyCellLayoutSetup.setWidth(contentWidth / singleContentColumn).setPdfFont(getRegularBoldFont())
				.setFontSize(contentFontSize).setTextAlignment(TextAlignment.LEFT).setBorderLeft(new SolidBorder(1));

		CellLayoutSetup valueCellLayoutSetup = keyCellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT)
				.setBorderRight(new SolidBorder(1)).setBorderLeft(null);

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData("Total Paid Amount (INR):", keyCellLayoutSetup),
						new CellData(String.valueOf(
								feePaymentInvoiceSummary.getFeePaymentTransactionDetails().getTotalPaidAmount()),
								valueCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData("Total Fine Paid (INR):", keyCellLayoutSetup),
						new CellData(String.valueOf(
								feePaymentInvoiceSummary.getFeePaymentTransactionDetails().getTotalFineAmount()),
								valueCellLayoutSetup)));
		// addRow(table, documentLayoutSetup, Arrays.asList(new
		// CellData("Discount Amount (INR):", keyCellLayoutSetup),
		// new
		// CellData(String.valueOf(feePaymentInvoiceSummary.getTotalDiscontAmount()),
		// valueCellLayoutSetup)));

		if (Double.compare(feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getDebitWalletAmount(),
				0d) > 0) {
			addRow(table, documentLayoutSetup,
					Arrays.asList(new CellData("Used Wallet Amount (INR):", keyCellLayoutSetup),
							new CellData(String.valueOf(
									feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getDebitWalletAmount()),
									valueCellLayoutSetup)));
		}
		if (Double.compare(feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getCreditWalletAmount(),
				0d) > 0) {
			addRow(table,
					documentLayoutSetup, Arrays
							.asList(new CellData("Credit Wallet Amount (INR):", keyCellLayoutSetup),
									new CellData(String.valueOf(feePaymentInvoiceSummary
											.getFeePaymentTransactionMetaData().getCreditWalletAmount()),
											valueCellLayoutSetup)));
		}

		document.add(table);

		// Amount in words
		singleContentColumn = 1;
		columnCount = getColumnCount(documentLayoutSetup.isOfficeCopy(), singleContentColumn);
		table = new Table(columnCount);
		Paragraph amountsInWord = getKeyValueParagraph("(In Words): ",
				feePaymentInvoiceSummary.getTotalCurrentTransactionPaidAmountInWords() + " Only.");

		CellLayoutSetup amountInWordsCellLayoutSetup = new CellLayoutSetup();
		amountInWordsCellLayoutSetup.setWidth(contentWidth / singleContentColumn).setPdfFont(getRegularFont())
				.setFontSize(contentFontSize).setTextAlignment(TextAlignment.LEFT).setBorder(new SolidBorder(1))
				.setBorderBottom(null);

		addRow(table, documentLayoutSetup, Arrays.asList(amountsInWord), amountInWordsCellLayoutSetup);
		document.add(table);
	}

	private void generateActiveFeePaymentSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize, FeePaymentInvoiceSummary feePaymentInvoiceSummary) throws IOException {
		int singleContentColumn = 2;
		int columnCount = getColumnCount(documentLayoutSetup.isOfficeCopy(), singleContentColumn);
		float contentWidth = getContentWidth(documentLayoutSetup);
		Table table = new Table(columnCount);

		CellLayoutSetup keyCellLayoutSetup = new CellLayoutSetup();
		keyCellLayoutSetup.setWidth(contentWidth / singleContentColumn).setPdfFont(getRegularBoldFont())
				.setFontSize(contentFontSize).setTextAlignment(TextAlignment.LEFT).setBorderLeft(new SolidBorder(1));

		CellLayoutSetup valueCellLayoutSetup = keyCellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT)
				.setBorderRight(new SolidBorder(1)).setBorderLeft(null);

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData("Total Fees (INR):", keyCellLayoutSetup),
				new CellData(String.valueOf(feePaymentInvoiceSummary.getTotalAssignedAmount()), valueCellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData("Discount Amount (INR):", keyCellLayoutSetup),
				new CellData(String.valueOf(feePaymentInvoiceSummary.getTotalDiscontAmount()), valueCellLayoutSetup)));

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData("Prev Balance (INR):", keyCellLayoutSetup),
						new CellData(
								String.valueOf(feePaymentInvoiceSummary.getTotalCollectedAmountBeforeTransaction()),
								valueCellLayoutSetup)));

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData("Paid Amount (INR):", keyCellLayoutSetup),
						new CellData(String.valueOf(feePaymentInvoiceSummary.getTotalCurrentTransactionPaidAmount()),
								valueCellLayoutSetup)));
		if (Double.compare(feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getDebitWalletAmount(),
				0d) > 0) {
			addRow(table, documentLayoutSetup,
					Arrays.asList(new CellData("Used Wallet Amount (INR):", keyCellLayoutSetup),
							new CellData(String.valueOf(
									feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getDebitWalletAmount()),
									valueCellLayoutSetup)));
		}
		if (Double.compare(feePaymentInvoiceSummary.getFeePaymentTransactionMetaData().getCreditWalletAmount(),
				0d) > 0) {
			addRow(table,
					documentLayoutSetup, Arrays
							.asList(new CellData("Credit Wallet Amount (INR):", keyCellLayoutSetup),
									new CellData(String.valueOf(feePaymentInvoiceSummary
											.getFeePaymentTransactionMetaData().getCreditWalletAmount()),
											valueCellLayoutSetup)));
		}
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData("Fine Amount (INR):", keyCellLayoutSetup),
				new CellData(String.valueOf(feePaymentInvoiceSummary.getTotalFineAmount()), valueCellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData("Due Amount (INR):", keyCellLayoutSetup),
				new CellData(String.valueOf(feePaymentInvoiceSummary.getTotalDueAmount()), valueCellLayoutSetup)));

		document.add(table);

		// Amount in words
		singleContentColumn = 1;
		columnCount = getColumnCount(documentLayoutSetup.isOfficeCopy(), singleContentColumn);
		table = new Table(columnCount);
		Paragraph amountsInWord = getKeyValueParagraph("(In Words): ",
				feePaymentInvoiceSummary.getTotalCurrentTransactionPaidAmountInWords() + " Only.");

		CellLayoutSetup amountInWordsCellLayoutSetup = new CellLayoutSetup();
		amountInWordsCellLayoutSetup.setWidth(contentWidth / singleContentColumn).setPdfFont(getRegularFont())
				.setFontSize(contentFontSize).setTextAlignment(TextAlignment.LEFT).setBorder(new SolidBorder(1))
				.setBorderBottom(null);

		addRow(table, documentLayoutSetup, Arrays.asList(amountsInWord), amountInWordsCellLayoutSetup);
		document.add(table);

	}

	private void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize)
			throws IOException {
		int singleContentColumn = 1;
		int columnCount = getColumnCount(documentLayoutSetup.isOfficeCopy(), singleContentColumn);
		float contentWidth = getContentWidth(documentLayoutSetup);
		Table table = new Table(columnCount);

		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setWidth(contentWidth / singleContentColumn).setPdfFont(getRegularBoldFont())
				.setFontSize(contentFontSize).setTextAlignment(TextAlignment.RIGHT).setBorder(new SolidBorder(1))
				.setBorderTop(null);

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(EMPTY_TEXT)),
				signatureCellLayoutSetup.copy().setBorderBottom(null));
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(EMPTY_TEXT)),
				signatureCellLayoutSetup.copy().setBorderBottom(null));
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Signature")), signatureCellLayoutSetup);
		document.add(table);
	}

	@Override
	public DocumentOutput generateBulkInvoices(Institute institute, List<FeePaymentInvoiceSummary>
			feePaymentInvoiceSummaryList, String documentName, boolean doubleCopy, FeeInvoicePreferences feeInvoicePreferences) {
		try {
			DocumentOutput invoice = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(doubleCopy);
			float contentFontSize = 9f;

			Document document = initDocument(invoice.getContent(), documentLayoutSetup);
			DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getNunitoRegularFont(), getAleoBoldFont(), null, null, DEFAULT_LOGO_WIDTH, DEFAULT_LOGO_HEIGHT, LogoProvider.INSTANCE.getLogo(institute.getInstituteId()), getGlacialRegularFont(), getHindiBoldFont());
			
			if(CollectionUtils.isEmpty(feePaymentInvoiceSummaryList)) {
				document.close();
				return invoice;
			}

			int pageNumber = 1;
			for (FeePaymentInvoiceSummary feePaymentInvoiceSummary : feePaymentInvoiceSummaryList) {
				generateFeeInvoice(institute, documentLayoutData, feePaymentInvoiceSummary,
						contentFontSize, pageNumber, OFFICE_COPY_TEXT, null);

				if (pageNumber != feePaymentInvoiceSummaryList.size()) {
					document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				pageNumber++;

			}
			document.close();
			return invoice;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
}
