
package com.lernen.cloud.pdf.exam.reports._10240;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.*;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.ExamGrade;
import com.lernen.cloud.core.api.examination.StudentPersonalityTraitsResponse;
import com.lernen.cloud.core.api.examination.report.ExamReportCardLayoutData;
import com.lernen.cloud.core.api.examination.report.ExamReportCourseMarksRow;
import com.lernen.cloud.core.api.examination.report.ExamReportData;
import com.lernen.cloud.core.api.examination.report.ExamReportStructure;
import com.lernen.cloud.core.api.examination.report.ExamResultStatus;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.core.utils.images.WatermarkProvider;
import com.lernen.cloud.pdf.exam.reports.ExamReportGenerator;
import com.lernen.cloud.pdf.exam.reports.IExamReportCardGenerator;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.List;

/**
 *
 * <AUTHOR>
 *
 */
public class ExamReportGenerator10240 extends ExamReportGenerator implements IExamReportCardGenerator {

    public ExamReportGenerator10240(AssetProvider assetProvider) {
        super(assetProvider);
        //TODO Auto-generated constructor stub
    }

    private static final Logger logger = LogManager.getLogger(ExamReportGenerator10240.class);
    public static final float DEFAULT_PAGE_SIDE_MARGIN = 25f;
    public static final float DEFAULT_PAGE_TOP_MARGIN = 10f;
    public static final float DEFAULT_PAGE_BOTTOM_MARGIN = 10f;
    public static final float SQUARE_BORDER_MARGIN = 12f;



    protected static final String ANNUAL_REPORT_TYPE = "ANNUAL";
    protected static final String COSCHOLASTIC_EXAM_DESCRIPTION = "A= Excellent, B= Very Good, C= Keep it up, D = Poor, E= Fail";
    protected static final String SCHOLASTIC_EXAM_DESCRIPTION = "MM= Max Marks, MO= Marks Obtained";

    private static Map<UUID, String> PROMOTE_CLASS_MAP = new HashMap<>();

    static{
        PROMOTE_CLASS_MAP.put(UUID.fromString("4a951522-eff2-11ee-bcc8-124ce6a1ab45"), "KG(H)");
        PROMOTE_CLASS_MAP.put(UUID.fromString("61fa7da9-4f68-40dd-b301-8f30caef11c5"), "KG(E)");
        PROMOTE_CLASS_MAP.put(UUID.fromString("a05bea98-b986-4c6e-b7ec-230a0f5562be"), "I(H)");
        PROMOTE_CLASS_MAP.put(UUID.fromString("d3610b35-a1f9-4cb1-8e65-c5f1b3fb3b6b"), "I(E)");
        PROMOTE_CLASS_MAP.put(UUID.fromString("ae005c26-ed14-45eb-8586-5c4753c31879"), "II(H)");
        PROMOTE_CLASS_MAP.put(UUID.fromString("3d878148-f427-416f-a59b-0ba9016a59db"), "II(E)");
        PROMOTE_CLASS_MAP.put(UUID.fromString("ca11c94e-1d8b-414d-81ba-522f5d0bb39b"), "III(H)");
        PROMOTE_CLASS_MAP.put(UUID.fromString("a05759c4-cdda-4e9c-8700-73abb4168948"), "III(E)");
        PROMOTE_CLASS_MAP.put(UUID.fromString("c4214565-eb43-4268-972c-da85ed40b68f"), "IV(H)");
        PROMOTE_CLASS_MAP.put(UUID.fromString("b33a2205-bf53-4971-aeee-76d014777fd8"), "IV(E)");
        PROMOTE_CLASS_MAP.put(UUID.fromString("fb6f3a3e-88de-43f2-a48c-191fd5252e30"), "V(H)");
        PROMOTE_CLASS_MAP.put(UUID.fromString("3e4eee3e-07d3-4496-8905-78ca358bd566"), "V(E)");
        PROMOTE_CLASS_MAP.put(UUID.fromString("4c2d4418-b3c9-4b32-b980-b7e14dc2026b"), "VI(H)");
        PROMOTE_CLASS_MAP.put(UUID.fromString("ef51cc31-9464-448e-a154-bc007537eee1"), "VI(E)");
        PROMOTE_CLASS_MAP.put(UUID.fromString("89977312-33c5-4bea-83ba-10a77291ac2b"), "VII(H)");
        PROMOTE_CLASS_MAP.put(UUID.fromString("8a4024bc-7c32-4a77-9c55-e5e272c14f18"), "VII(E)");
        PROMOTE_CLASS_MAP.put(UUID.fromString("17c81c65-b401-494c-89a6-01a9e887f7a1"), "VIII(H)");
        PROMOTE_CLASS_MAP.put(UUID.fromString("e71e8ca7-abe3-4600-9de2-6d6d8701d299"), "VIII(E)");
        PROMOTE_CLASS_MAP.put(UUID.fromString("e5a41134-fcbd-4732-a50e-2797597c76d9"), "IX(E)");
        PROMOTE_CLASS_MAP.put(UUID.fromString("40752b05-595f-4561-bbb3-628dd093c118"), "IX(H)");
        PROMOTE_CLASS_MAP.put(UUID.fromString("37b5c211-ae96-4ed2-9c5f-05a80807750a"), "X(E)");
        PROMOTE_CLASS_MAP.put(UUID.fromString("abb20c37-8e15-47fc-a7c1-f205b6f3e2fd"), "X(H)");
        PROMOTE_CLASS_MAP.put(UUID.fromString("35d9e273-32e9-4973-9ff8-a3ab79c36986"), "XI(E)");
        PROMOTE_CLASS_MAP.put(UUID.fromString("76db69dc-fb64-40d4-bdb3-d88ab3f3ca0c"), "XI(H)");
        PROMOTE_CLASS_MAP.put(UUID.fromString("b9bc88aa-69fe-4171-9627-a7869832d2c0"), "XII(E)");
        PROMOTE_CLASS_MAP.put(UUID.fromString("5e1bfbe3-ec31-46ed-ad38-e553a1a284bb"), "XII(H)");
        PROMOTE_CLASS_MAP.put(UUID.fromString("2b9edfa4-8bdf-4a68-941d-927160c007a4"), "XII(H)");
        PROMOTE_CLASS_MAP.put(UUID.fromString("6df72e06-8117-4428-bdc8-387b9d444f92"), "-");
        PROMOTE_CLASS_MAP.put(UUID.fromString("4905c458-4344-4068-92e5-6eea0cac6ec8"), "-");
        PROMOTE_CLASS_MAP.put(UUID.fromString("2ab8d5e7-1390-4a69-8cd6-bd374527c7d0"), "-");
    }

    @Override
    public DocumentOutput generateReport(Institute institute, Student student, String reportType,
                                         ExamReportData examReportData, String documentName, StudentManager studentManager) {
        try {

            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

            ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);

            PdfFont regularFont = getCambriaFont();
            PdfFont boldFont = getCambriaBoldFont();

            generateStudentReportCard(examReportCardLayoutData, institute, examReportData, studentManager, reportType,
                    1, boldFont, regularFont);

            examReportCardLayoutData.getDocument().close();
            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating report card institute {}, student {}, reportType {} ",
                    institute.getInstituteId(), student.getStudentId(), reportType, e);
        }
        return null;
    }

    private void generateStudentReportCard(ExamReportCardLayoutData examReportCardLayoutData,
                                           Institute institute, ExamReportData examReportData,
                                           StudentManager studentManager, String reportType,
                                           int pageNumber, PdfFont boldFont, PdfFont regularFont) throws IOException {

        generateMetaDataLayout(examReportCardLayoutData, institute, examReportData.getStudentLite(), studentManager, reportType,
                examReportData, pageNumber, boldFont, regularFont);


        Set<UUID> nonAdditionalSubjects = getNonAdditionalSubjects(examReportData);
        GridConfigs gridConfigs = GridConfigs.forOnlyObtainedTotalRow();
        gridConfigs.setMaxMarksColorHexCode("#980000");
        generateScholasticMarksGrid(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
                getRegularFont(), getRegularBoldFont(), examReportCardLayoutData.getContentFontSize(),
                examReportCardLayoutData.getDefaultBorderWidth(), examReportData, gridConfigs,
                "Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType),
                nonAdditionalSubjects, "MM", "MO", null,
                "-");
        generateScholasticExamDescription(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize());


        if(!CollectionUtils.isEmpty(examReportData.getExamReportStructure().getExamReportCourseStructure()
                .get(CourseType.SCHOLASTIC).getAdditionalCourses())) {
            generateAdditionalCustomScholasticMarksGrid(examReportCardLayoutData, examReportData, GridConfigs.forOnlyObtainedTotalRow(),
                    "Additional Subjects", getScholasticMarksGridSubjectWidth(reportType));
        }

        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);

        if (examReportData.getCourseTypeExamReportMarksGrid().containsKey(CourseType.COSCHOLASTIC)) {
            generateCoScholasticMarksGrid(examReportCardLayoutData.getDocument(),
                    examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
                    examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(),
                    "Particulars Details", getCoScholasticMarksGridSubjectWidth(reportType), "-");
            generateCoscholasticExamDescription(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize());
        }

        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
        generateResultSummary(examReportCardLayoutData.getDocument(),
                examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
                examReportData, reportType, boldFont, regularFont);

        generateRemarksSection(examReportCardLayoutData, examReportData, boldFont, regularFont);

        generateSignatureBox(examReportCardLayoutData.getDocument(),
                examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
                examReportCardLayoutData.getDefaultBorderWidth(), pageNumber, boldFont, regularFont, reportType);
    }

    protected void generateScholasticExamDescription(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize) throws IOException {
		Text descTitle = new Text("Description: ").setFont(getRegularBoldFont()).setFontSize(contentFontSize - 2);
		Text descText = new Text(SCHOLASTIC_EXAM_DESCRIPTION)
				.setFontSize(contentFontSize - 4);
		Paragraph desc = new Paragraph();
		desc.add(descTitle).add(descText);
		document.add(desc);
	}

    protected void generateCoscholasticExamDescription(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize) throws IOException {
		Text descTitle = new Text("Description: ").setFont(getRegularBoldFont()).setFontSize(contentFontSize - 2);
		Text descText = new Text(COSCHOLASTIC_EXAM_DESCRIPTION)
				.setFontSize(contentFontSize - 4);
		Paragraph desc = new Paragraph();
		desc.add(descTitle).add(descText);
		document.add(desc);
	}

    protected void generateAdditionalCustomScholasticMarksGrid(ExamReportCardLayoutData examReportCardLayoutData,
                                                               ExamReportData examReportData, GridConfigs gridConfigs, String subjectColumnTitle, float subjectColumnWidth) throws IOException {
        ExamReportStructure examReportStructure = examReportData.getExamReportStructure();
        if (examReportStructure == null || CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure())
                || examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC) == null
                || CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC)
                .getAdditionalCourses())) {
            return;
        }

        generateScholasticMarksGrid(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
                getRegularFont(), getRegularBoldFont(), examReportCardLayoutData.getContentFontSize() - 1,
                examReportCardLayoutData.getDefaultBorderWidth(), examReportData, gridConfigs,
                subjectColumnTitle, subjectColumnWidth,
                examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC).getAdditionalCourses(),
                "MM", "MO", null, "-");
    }

    protected float getScholasticMarksGridSubjectWidth(String reportType) {
        return 0.2f;
    }

    protected float getCoScholasticMarksGridSubjectWidth(String reportType) {
        return 0.4f;
    }

    protected ExamReportCardLayoutData generateReportCardLayoutData(Institute institute, DocumentOutput documentOutput)
            throws IOException {

        DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false);
        float contentFontSize = 12f;
        float defaultBorderWidth = 0.5f;
        Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
        float logoWidth = 90f;
        float logoHeight = 85f;

        int instituteId = institute.getInstituteId();

        return new ExamReportCardLayoutData(document, documentLayoutSetup, null, null, contentFontSize,
                defaultBorderWidth, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId));
    }

    protected void generateMetaDataLayout(ExamReportCardLayoutData examReportCardLayoutData, Institute institute,
                                          StudentLite studentLite, StudentManager studentManager, String reportType, ExamReportData examReportData,
                                          int pageNumber, PdfFont boldFont, PdfFont regularFont) throws IOException {

        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
        //Institute watermark
        int instituteId = institute.getInstituteId();
        float watermarkImageHeightWidth = 400f;
        generateWatermark(examReportCardLayoutData,
                instituteId, watermarkImageHeightWidth, watermarkImageHeightWidth, watermarkImageHeightWidth / 5 + 20,
                watermarkImageHeightWidth / 2);
        generateHeader(examReportCardLayoutData.getDocumentLayoutSetup(),
                examReportCardLayoutData, studentLite, institute, reportType, 40f,
                examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.87f, boldFont, regularFont, studentManager);
        generateStudentInformation(examReportCardLayoutData, studentLite, examReportData, boldFont, regularFont);
        generateBorderLayout(examReportCardLayoutData, pageNumber);
    }

    @Override
    public DocumentLayoutSetup initDocumentLayoutSetup(boolean officeCopy) {
        return initDocumentLayoutSetup(officeCopy, officeCopy ? DEFAULT_PAGE_SIZE.rotate() : DEFAULT_PAGE_SIZE,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN : DEFAULT_PAGE_TOP_MARGIN,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN : DEFAULT_PAGE_BOTTOM_MARGIN,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_SIDE_MARGIN : DEFAULT_PAGE_SIDE_MARGIN,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_MIDDLE_MARGIN : 0f);
    }

    protected void generateHeader(DocumentLayoutSetup documentLayoutSetup, ExamReportCardLayoutData examReportCardLayoutData, StudentLite studentLite,
                                  Institute institute, String reportType, float offsetX, float offsetY,
                                  PdfFont boldFont, PdfFont regularFont, StudentManager studentManager) throws IOException {

        generateDynamicImageProvider(examReportCardLayoutData, offsetX, offsetY, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

        int singleContentColumn = 1;


       /*  byte[] studentImage = getStudentImage(institute.getInstituteId(), studentLite.getStudentId(), studentManager);
        if (studentImage != null) {
            generateImage(examReportCardLayoutData.getDocument(), documentLayoutSetup, studentImage, examReportCardLayoutData.getLogoWidth(), examReportCardLayoutData.getLogoHeight(),
                    documentLayoutSetup.getPageSize().getWidth() - 125f, offsetY);
        } */
        Table table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(boldFont).setFontSize(examReportCardLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.CENTER);

        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getInstituteName().toUpperCase())),
                cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() + 3));
        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getLetterHeadLine1())),
                cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() - 2));
        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getLetterHeadLine2())),
                cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() - 2).setPdfFont(regularFont));

        examReportCardLayoutData.getDocument().add(table);
        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
        table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);
        String headerExamTitle = "PROGRESS REPORT ";
        headerExamTitle += "(" + studentLite.getStudentSessionData().getShortYearDisplayName() + ")";
        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(headerExamTitle).setUnderline()),
                cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize()).setPdfFont(boldFont));
        examReportCardLayoutData.getDocument().add(table);
        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
    }

    protected void generateStudentInformation(ExamReportCardLayoutData examReportCardLayoutData,
                                              StudentLite studentLite, ExamReportData examReportData,
                                              PdfFont boldFont, PdfFont regularFont) throws IOException {

        Document document = examReportCardLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
        float contentFontSize = examReportCardLayoutData.getContentFontSize() - 1;

        Table table = getPDFTable(documentLayoutSetup, new float[] { 0.4f, 0.2f, 0.4f });

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize);

        CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
        CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);
        CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);

        Paragraph studentName = getKeyValueParagraph("Student Name : ", studentLite.getName(), boldFont, boldFont);
        Paragraph fatherName = getKeyValueParagraph("Father Name : ", studentLite.getFathersName(), boldFont, boldFont);
        Paragraph dob = getKeyValueParagraph("DOB : ",
                studentLite.getDateOfBirth() == null || studentLite.getDateOfBirth() <= 0 ? ""
                        : DateUtils.getFormattedDate(studentLite.getDateOfBirth(), DATE_FORMAT, User.DFAULT_TIMEZONE),  boldFont, boldFont);
        Paragraph motherName = getKeyValueParagraph("Mother Name : ", studentLite.getMothersName(), boldFont, boldFont);
        Paragraph admissionNumber = getKeyValueParagraph("Admission No. : ", studentLite.getAdmissionNumber(), boldFont, boldFont);
        Paragraph classValue = getKeyValueParagraph("Class : ",
                studentLite.getStudentSessionData().getStandardNameWithSection(), boldFont, boldFont);
        Paragraph rollNumber = getKeyValueParagraph("Roll Number : ", studentLite.getStudentSessionData().getRollNumber(), boldFont, boldFont);
        Paragraph dateOfResultDeclaration = getKeyValueParagraph("Date Of Result Declaration : ",
                examReportData.getDateOfResultDeclaration() == null ? "-" : DateUtils.getFormattedDate(examReportData.getDateOfResultDeclaration()),
                boldFont, boldFont);


        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(studentName, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
                        new CellData(fatherName, thirdCellLayoutSetup)));
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(dob, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
                        new CellData(motherName, thirdCellLayoutSetup)));
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(admissionNumber, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
                        new CellData(classValue, thirdCellLayoutSetup)));
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(rollNumber, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
                        new CellData(dateOfResultDeclaration, thirdCellLayoutSetup)));

        document.add(table);

        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
    }

    protected void generateBorderLayout(ExamReportCardLayoutData examReportCardLayoutData, int pageNumber) {
        DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
        PdfCanvas canvas = new PdfCanvas(examReportCardLayoutData.getDocument().getPdfDocument(), pageNumber);
        canvas.rectangle(SQUARE_BORDER_MARGIN - 2, SQUARE_BORDER_MARGIN - 2,
                documentLayoutSetup.getPageSize().getWidth() - (SQUARE_BORDER_MARGIN - 2) * 2,
                documentLayoutSetup.getPageSize().getHeight() - (SQUARE_BORDER_MARGIN - 2) * 2);
        canvas.stroke();
        canvas.setLineWidth(1f);
        canvas.rectangle(SQUARE_BORDER_MARGIN, SQUARE_BORDER_MARGIN,
                documentLayoutSetup.getPageSize().getWidth() - SQUARE_BORDER_MARGIN * 2,
                documentLayoutSetup.getPageSize().getHeight() - SQUARE_BORDER_MARGIN * 2);
        canvas.stroke();
        canvas.setLineWidth(1f);
    }

    protected void generateScholasticExamDescription(Document document, DocumentLayoutSetup documentLayoutSetup,
                                                     float contentFontSize, ExamReportData examReportData, PdfFont boldFont, PdfFont regularFont, String descriptionText) throws IOException {
        Text descTitle = new Text("Description: " + descriptionText).setFontSize(contentFontSize - 4);
        Paragraph desc = new Paragraph();
        desc.add(descTitle);
        document.add(desc);
    }

    protected void generateResultSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
                                         float contentFontSize, ExamReportData examReportData, String reportType, PdfFont boldFont, PdfFont regularFont) throws IOException {

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.LEFT).setPaddingTop(2f).setPaddingBottom(2f);

        Table table = getPDFTable(documentLayoutSetup, new float[] { 0.4f, 0.2f, 0.4f });
        String hexColorCode = "#072b69";
        Paragraph result = getKeyValueParagraph("Result : ", "-", EColorUtils.hex2Rgb(hexColorCode), boldFont, regularFont);
        if(examReportData.getExamResultStatus() != null){
                result = getKeyValueParagraph("Result : ", examReportData.getExamResultStatus().getDisplayName(),
                                EColorUtils.hex2Rgb(hexColorCode), boldFont, boldFont);
        }

        String promotedClassText = examReportData.getPromotedTo() == null ? "-" : examReportData.getPromotedTo().getStandardName();
        if(PROMOTE_CLASS_MAP.containsKey(examReportData.getStandardMetaData().getStandardId()) && examReportData.getExamResultStatus() != null && (examReportData.getExamResultStatus() == ExamResultStatus.PASS || examReportData.getExamResultStatus() == ExamResultStatus.PASS_WITH_GRACE)){
                promotedClassText = PROMOTE_CLASS_MAP.get(examReportData.getStandardMetaData().getStandardId());
        }

        Paragraph promotedClass = getKeyValueParagraph("Promoted to: ",
                        promotedClassText, EColorUtils.hex2Rgb(hexColorCode), boldFont, boldFont);
        Paragraph obtainedMarks = getKeyValueParagraph("Obtained marks : ",
                examReportData.getTotalObtainedMarks() == null ? "-"
                        : examReportData.getTotalObtainedMarks() * 10 % 10 == 0
                        ? String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10)
                        : String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10d), EColorUtils.hex2Rgb(hexColorCode), boldFont, boldFont);
        Paragraph totalMarks = getKeyValueParagraph("Total marks : ", examReportData.getTotalMaxMarks() == null ? "-"
                : String.valueOf(Math.round(examReportData.getTotalMaxMarks() * 10) / 10), EColorUtils.hex2Rgb(hexColorCode), boldFont, boldFont);
        Paragraph percentage = getKeyValueParagraph("Percentage : ", examReportData.getPercentage() == null ? "-"
                : Math.round(examReportData.getPercentage() * 100) / 100d + "%", EColorUtils.hex2Rgb(hexColorCode), boldFont, boldFont);
        Paragraph grade = getKeyValueParagraph("Grade : ",
                examReportData.getTotalGrade() == null ? "-" : examReportData.getTotalGrade().getGradeName(), EColorUtils.hex2Rgb(hexColorCode), boldFont, boldFont);

        Paragraph totalWorkingDays = getKeyValueParagraph("Total Working Days : ",
                examReportData.getTotalWorkingDays() == null ? ""
                        : String.valueOf(examReportData.getTotalWorkingDays()), EColorUtils.hex2Rgb(hexColorCode), boldFont, boldFont);

        Paragraph totalAttendedDays = getKeyValueParagraph("Total Attended Days : ",
                examReportData.getTotalAttendedDays() == null ? ""
                        : String.valueOf(examReportData.getTotalAttendedDays()), EColorUtils.hex2Rgb(hexColorCode), boldFont, boldFont);

        if (reportType.equalsIgnoreCase(ANNUAL_REPORT_TYPE)) {
            addRow(table, documentLayoutSetup, Arrays.asList(new CellData(result, cellLayoutSetup),
                    new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(promotedClass, cellLayoutSetup)));
        }

        addRow(table, documentLayoutSetup, Arrays.asList(new CellData(obtainedMarks, cellLayoutSetup),
                new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(totalMarks, cellLayoutSetup)));
        addRow(table, documentLayoutSetup, Arrays.asList(new CellData(percentage, cellLayoutSetup),
                new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(grade, cellLayoutSetup)));
        addRow(table, documentLayoutSetup, Arrays.asList(new CellData(totalWorkingDays, cellLayoutSetup),
                new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(totalAttendedDays, cellLayoutSetup)));

        document.add(table);

    }

    protected void generateRemarksSection(ExamReportCardLayoutData examReportCardLayoutData,
                                          ExamReportData examReportData, PdfFont boldFont, PdfFont regularFont) throws IOException {

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(boldFont).setFontSize(examReportCardLayoutData.getContentFontSize() - 1)
                .setTextAlignment(TextAlignment.LEFT);

        Table remarksTable = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), new float[] { 0.4f, 0.2f, 0.4f });
        Paragraph rank = null;
	if (examReportData.getRank() != null && examReportData.getRank() > 0 && examReportData.getRank() <= 10) {
		rank = getKeyValueParagraph("Position In Class : ", String.valueOf(examReportData.getRank()), EColorUtils.hex2Rgb("#072b69"), boldFont, boldFont);
	}
        Paragraph remarks = getKeyValueParagraph("Remarks: ", examReportData.getRemarks(), EColorUtils.hex2Rgb("#072b69"), boldFont, boldFont);

        if(rank != null) {
		addRow(remarksTable, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(new CellData(remarks, cellLayoutSetup),
			new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(rank, cellLayoutSetup)));
	}else{
                addRow(remarksTable, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(new CellData(remarks, cellLayoutSetup),
                        new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(EMPTY_TEXT, cellLayoutSetup)));
        }
        examReportCardLayoutData.getDocument().add(remarksTable);
    }

    protected void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup,
                                        float contentFontSize, float defaultBorderWidth, int pageNumber, PdfFont boldFont, PdfFont regularFont,
                                        String reportType) throws IOException {
        PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);
        int singleContentColumn = 3;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);
        CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
        signatureCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 1)
                .setTextAlignment(TextAlignment.CENTER);

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Class Teacher"), getParagraph(""),
                getParagraph("Principal")), signatureCellLayoutSetup);
        table.setFixedPosition(30f, 10f, documentLayoutSetup.getPageSize().getWidth() - 100f);
        document.add(table);


//        if (reportType.equalsIgnoreCase(TERM_I_REPORT_TYPE) || reportType.equalsIgnoreCase(HALF_YEARLY_REPORT_TYPE)) {
//            generateImage(document, documentLayoutSetup, ImageProvider.INSTANCE.getImage(ImageProvider._10130_PRINCIPAL_SIGNATURE),
//                    50f, 50f, documentLayoutSetup.getPageSize().getWidth() - 130f, 110f);
//        }

    }

    private void generateGradeBox(Document document, DocumentLayoutSetup documentLayoutSetup, ExamReportData examReportData, float contentFontSize, 
                                  float defaultBorderWidth, PdfFont boldFont, PdfFont regularFont) throws IOException {

		Map<CourseType, List<ExamGrade>> gradesList = examReportData.getCourseTypeExamGrades();
		List<ExamGrade> scholasticGrades = gradesList.get(CourseType.SCHOLASTIC);
//		List<ExamGrade> coScholasticGrades = gradesList.get(CourseType.COSCHOLASTIC);

		boolean hasScholastic = !CollectionUtils.isEmpty(scholasticGrades);
		boolean hasCoScholastic = false;

		if (!hasScholastic && !hasCoScholastic) {
			return;
		}
		float yAxisforMarksRange = hasScholastic && hasCoScholastic ? 130f : 74f;

		float yOffset = hasCoScholastic ? 109f : 53f;
		Table table = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 1)
				.setTextAlignment(TextAlignment.CENTER);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("MARKS RANGE")), signatureCellLayoutSetup);
		table.setFixedPosition(10f, yAxisforMarksRange, documentLayoutSetup.getPageSize().getWidth());
		document.add(table);

		CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
		marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(contentFontSize - 2)
				.setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);
		

		if (hasScholastic) {
			generateGradeTable(document, documentLayoutSetup, "Scholastic Grading", scholasticGrades, marksCellLayoutSetup, 
							contentFontSize, defaultBorderWidth, boldFont, regularFont, yOffset, CourseType.SCHOLASTIC);
		}

//		if (hasCoScholastic) {
//			float coScholasticPositionY = hasScholastic ? 57f : 53f;
//			generateGradeTable(document, documentLayoutSetup, "Co-Scholastic Grading", coScholasticGrades, marksCellLayoutSetup,
//							contentFontSize, defaultBorderWidth, boldFont, regularFont, coScholasticPositionY, CourseType.COSCHOLASTIC);
//		}
	}

    private void generateGradeTable(Document document, DocumentLayoutSetup documentLayoutSetup, String title, List<ExamGrade> grades, CellLayoutSetup marksCellLayoutSetup,
									float contentFontSize, float defaultBorderWidth, PdfFont boldFont, PdfFont regularFont, float yPosition, CourseType courseType) throws IOException {

		Table table = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup titleHeadLayoutsSetup = new CellLayoutSetup();
		
		if(CourseType.SCHOLASTIC == courseType){
			titleHeadLayoutsSetup.setPdfFont(regularFont).setFontSize(contentFontSize - 2)
				.setBorderTop(new SolidBorder(defaultBorderWidth)).setBorderRight(new SolidBorder(defaultBorderWidth)).setBorderLeft(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);
		}
		if(CourseType.COSCHOLASTIC == courseType){
			titleHeadLayoutsSetup.setPdfFont(regularFont).setFontSize(contentFontSize - 2)
				.setBorderRight(new SolidBorder(defaultBorderWidth)).setBorderLeft(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);
		}

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(title)), titleHeadLayoutsSetup);
		table.setFixedPosition(25f, yPosition, documentLayoutSetup.getPageSize().getWidth() - 100f);
		document.add(table);

		int columnCount = grades.size() + 1;
		float firstColumnWidth = 0.13f;
		float remainingWidth = 1f - firstColumnWidth;
		float columnWidth = remainingWidth / (columnCount - 1);

		float[] columnWidths = new float[columnCount];
		columnWidths[0] = firstColumnWidth;
		Arrays.fill(columnWidths, 1, columnWidths.length, columnWidth);

		Table headerTable = getPDFTable(documentLayoutSetup, columnWidths);
		List<CellData> headerList = new ArrayList<>();
		List<CellData> gradeList = new ArrayList<>();

		headerList.add(new CellData("MARKS RANGE", marksCellLayoutSetup));
		gradeList.add(new CellData("GRADE", marksCellLayoutSetup));

		for (ExamGrade grade : grades) {
			headerList.add(new CellData(grade.getRangeDisplayName(), marksCellLayoutSetup));
			gradeList.add(new CellData(grade.getGradeName(), marksCellLayoutSetup));
		}

		addRow(headerTable, documentLayoutSetup, headerList);
		addRow(headerTable, documentLayoutSetup, gradeList);
		headerTable.setFixedPosition(25f, yPosition - 37f, documentLayoutSetup.getPageSize().getWidth() - 100f);
		document.add(headerTable);
	}

    protected void generatePersonalityTraits(ExamReportCardLayoutData examReportCardLayoutData, ExamReportData examReportData,
                                             PdfFont boldFont, PdfFont regularFont) {


        Map<String, String> personalityTraitKeyValueMap = getPersonalityTraitKeyValueMap(examReportData.getStudentPersonalityTraitsResponseList());
        List<Map.Entry<String, String>> gradesList = new ArrayList<>(personalityTraitKeyValueMap.entrySet());

        Table table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), new float[] { 0.6f, 0.4f });

        Paragraph disciplineHeader = getParagraph("Personality Development", boldFont);
        Paragraph gradeHeader = getParagraph("Grade", boldFont);

        Cell disciplineHeaderCell = new Cell().add(disciplineHeader).setTextAlignment(TextAlignment.CENTER).setFontSize(examReportCardLayoutData.getContentFontSize());
        Cell gradeHeaderCell = new Cell().add(gradeHeader).setTextAlignment(TextAlignment.CENTER).setFontSize(examReportCardLayoutData.getContentFontSize());

        table.addCell(disciplineHeaderCell);
        table.addCell(gradeHeaderCell);

        for (Map.Entry<String, String> entry : gradesList) {
            String key = entry.getKey();
            String value = entry.getValue();

            Paragraph disciplineParagraph = getParagraph(key, boldFont);
            Paragraph gradeParagraph = getParagraph(value, regularFont);

            Cell disciplineCell = new Cell().add(disciplineParagraph).setTextAlignment(TextAlignment.LEFT).setFontSize(examReportCardLayoutData.getContentFontSize());
            Cell gradeCell = new Cell().add(gradeParagraph).setTextAlignment(TextAlignment.CENTER).setFontSize(examReportCardLayoutData.getContentFontSize());

            table.addCell(disciplineCell);
            table.addCell(gradeCell);
        }

        examReportCardLayoutData.getDocument().add(table);
    }

    private Map<String, String> getPersonalityTraitKeyValueMap(List<StudentPersonalityTraitsResponse> studentPersonalityTraitsResponseList) {
        Map<String, String> personalityTraitKeyValueMap = new HashMap<>();
        if(CollectionUtils.isEmpty(studentPersonalityTraitsResponseList)) {
            return personalityTraitKeyValueMap;
        }

        for(StudentPersonalityTraitsResponse studentPersonalityTraitsResponse : studentPersonalityTraitsResponseList) {
            if(studentPersonalityTraitsResponse.getPersonalityTraitsDetails() == null) {
                continue;
            }
            personalityTraitKeyValueMap.put(studentPersonalityTraitsResponse.getPersonalityTraitsDetails().getPersonalityTraitName(),
                    studentPersonalityTraitsResponse.getResponse());
        }
        return personalityTraitKeyValueMap;
    }

    protected Set<UUID> getNonAdditionalSubjects(ExamReportData examReportData) {
        Set<UUID> nonAdditionalSubjects = new HashSet<UUID>();
        for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportData.getCourseTypeExamReportMarksGrid()
                .get(CourseType.SCHOLASTIC).getExamReportCourseMarksRows()) {
            if (!isAdditionalSubject(examReportCourseMarksRow.getCourse(), examReportData.getExamReportStructure())) {
                nonAdditionalSubjects.add(examReportCourseMarksRow.getCourse().getCourseId());
            }
        }
        return nonAdditionalSubjects;
    }

    private void generateBackgroundImage(Document document, DocumentLayoutSetup documentLayoutSetup, byte[] image, float width,
                                         float height, float offsetX, float offsetY) {
        try {
            generateImage(document, documentLayoutSetup, image, width, height, offsetX, offsetY);
        } catch (Exception e) {
            logger.error("Exception while adding background image", e);
        }

    }

    protected void sortClassExamReports(List<ExamReportData> examReportDataList) {
        Collections.sort(examReportDataList, new Comparator<ExamReportData>() {

            @Override
            public int compare(ExamReportData e1, ExamReportData e2) {
                StandardSections section1 = e1.getStudentLite().getStudentSessionData().getStandardSection();

                StandardSections section2 = e2.getStudentLite().getStudentSessionData().getStandardSection();

                if (section1 != null && section2 != null) {
                    int sectionCompare = section1.getSectionName().compareToIgnoreCase(section2.getSectionName());
                    if (sectionCompare != 0) {
                        return sectionCompare;
                    }
                }

                return e1.getStudentLite().getName().compareToIgnoreCase(e2.getStudentLite().getName());
            }
        });
    }

    @Override
    public DocumentOutput generateClassReport(Institute institute, String reportType,
                                              List<ExamReportData> examReportDataList, String documentName, StudentManager studentManager) {

        try {
            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);

            sortClassExamReports(examReportDataList);

            int pageNumber = 1;
            PdfFont regularFont = getCambriaFont();
            PdfFont boldFont = getCambriaBoldFont();
            for (ExamReportData examReportData : examReportDataList) {
                generateStudentReportCard(examReportCardLayoutData, institute, examReportData, studentManager, reportType,
                        pageNumber, boldFont, regularFont);

                if (pageNumber != examReportDataList.size()) {
                    examReportCardLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                }
                pageNumber++;
            }

            examReportCardLayoutData.getDocument().close();
            return documentOutput;

        } catch (IOException e) {
            e.printStackTrace();
        }

        return null;
    }

    @Override
    public DocumentOutput generateClassResultReport(Institute institute, String reportType, List<ExamReportData> examReportDataList,
                                                    String documentName, StudentManager studentManager, int studentPerPage) {
        try {
            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4);
            Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

            PdfFont regularFont = getRegularFont();
            PdfFont boldFont = getRegularBoldFont();

            CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
            marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(DEFAULT_FONT_SIZE)
                    .setBorder(new SolidBorder(DEFAULT_BORDER_WIDTH)).setTextAlignment(TextAlignment.CENTER);

            CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
                    .setTextAlignment(TextAlignment.LEFT);

            String instituteName = institute.getInstituteName().toUpperCase();
            String sessionName = "";
            if(!CollectionUtils.isEmpty(examReportDataList)) {
                sessionName = "Session : " + examReportDataList.get(0).getStudentLite().getStudentSessionData().getShortYearDisplayName();
            }
            generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName,
                    sessionName, marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));

            sortClassExamReports(examReportDataList);
            int pageNumber = 1;
            for (ExamReportData examReportData : examReportDataList) {
                generateClassReportDataStudentDetails(documentLayoutSetup, document, courseCellLayoutSetup, examReportData.getStudentLite());
                generateScholasticMarksGrid(document,
                        documentLayoutSetup, DEFAULT_FONT_SIZE, DEFAULT_BORDER_WIDTH, examReportData, GridConfigs.forOnlyObtainedTotalRow(),
                        "Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType),
                        null, null);
                generateClassReportDataStudentResult(documentLayoutSetup, document, examReportData, courseCellLayoutSetup);
                if (pageNumber % studentPerPage == 0) {
                    document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                    generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName, sessionName,
                            marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));
                } else {
                    addBlankLine(document, false, 1);
                }
                pageNumber++;
            }
            document.close();
            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
                    reportType, e);
        }
        return null;
    }
}
