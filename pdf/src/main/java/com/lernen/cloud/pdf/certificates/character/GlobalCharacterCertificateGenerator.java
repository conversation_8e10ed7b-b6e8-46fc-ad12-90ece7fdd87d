/**
 * 
 */
package com.lernen.cloud.pdf.certificates.character;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.user.Gender;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.PronounUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.core.utils.images.WatermarkProvider;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.Arrays;

/**
 * <AUTHOR>
 *
 */
public class GlobalCharacterCertificateGenerator extends CharacterCertificateGenerator {
	
	public GlobalCharacterCertificateGenerator(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}


	private static final Logger logger = LogManager.getLogger(GlobalCharacterCertificateGenerator.class);

	public static final float DEFAULT_PAGE_SIDE_MARGIN = 50f;
	public static final float DEFAULT_TABLE_BORDER_WIDTH = 0.5f;
	public static final float LOGO_WIDTH = 75f;
	public static final float LOGO_HEIGHT = 75f;
	public static final String NA = "NA";

	private static final String CONTENT_1 = "This is to certify that Mr/Miss ";
	private static final String CONTENT_2 = "%s ";//STUDENT NAME
	private static final String CONTENT_3 = "%s";//(S/D/)
	private static final String CONTENT_4 = "o ";
	private static final String CONTENT_5 = "Shri %s";//FATHER NAME
	private static final String CONTENT_6 = " Admission Number : ";
	private static final String CONTENT_7 = "%s";//Admission Number
	private static final String CONTENT_8 = "";
	private static final String CONTENT_9 = " Date of Birth : ";
	private static final String CONTENT_10 = "%s ";//Date of birth
	private static final String CONTENT_9_1 = "Date of Birth (In Words) : ";
	private static final String CONTENT_10_1 = "%s ";//Date of birth in words
	private static final String CONTENT_11 = "has been a bonafide student of this school during the period ";
	private static final String CONTENT_12 = "%s ";//Admission Date
	private static final String CONTENT_13 = " to ";
	private static final String CONTENT_14 = "%s. ";//Relieved Date
	private static final String CONTENT_15 = "During the period ";//Relieved Date
	private static final String CONTENT_16 = "%s ";//his/her
	private static final String CONTENT_17 = "behaviour has been found good. ";
	//	private static final String CONTENT_17 = "behaviour has been found good. He/She is declared ";
//	private static final String CONTENT_18 = "%s ";//pass
//	private static final String CONTENT_19 = "in the exam ";
//	private static final String CONTENT_20 = "%s. ";//BSER
	private static final String CONTENT_21 = "We wish him/her good luck. ";

	private static final String SIGNATURE_1 = "Issue Date : ";
	private static final String SIGNATURE_2 = "%s ";//current date
	private static final String SIGNATURE_3 = "Authorised Signatory";

	@Override
	public DocumentOutput generateCharacterCertificate(Institute institute, Student student,
													   String documentName, StudentManager studentManager) {
		try {
			float squareBorderMargin = 8f;
			float borderInnerGap = 2f;
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

			DocumentLayoutData documentLayoutData = generateCharacterCertificateLayoutData(institute, documentOutput, 70f,
					70f);
			DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
			Document document = documentLayoutData.getDocument();
			PdfFont boldFont = getCambriaBoldFont();
			PdfFont regularFont = getCambriaFont();
			float contentFontSize = documentLayoutData.getContentFontSize();

			generateImages(document, documentLayoutSetup, documentLayoutData, institute, student, studentManager);
			generateHeader(document, documentLayoutSetup, contentFontSize, boldFont, regularFont, student, institute);
			generateContent(document, documentLayoutSetup, contentFontSize, student, boldFont, regularFont, NA, NA);
			generateMetadata(documentLayoutData, squareBorderMargin, borderInnerGap);
			generateSignatureBox(document, documentLayoutSetup, contentFontSize, boldFont, regularFont);

			document.close();
			return documentOutput;
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("Error while generate character certificate for student {} ", student.getStudentBasicInfo().getAdmissionNumber(), e);
		}
		return null;
	}

	@Override
	public DocumentLayoutSetup initDocumentLayoutSetup(boolean officeCopy) {
		return initDocumentLayoutSetup(officeCopy, officeCopy ? DEFAULT_PAGE_SIZE : DEFAULT_PAGE_SIZE,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN : DEFAULT_PAGE_TOP_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN : DEFAULT_PAGE_BOTTOM_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_SIDE_MARGIN : DEFAULT_PAGE_SIDE_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_MIDDLE_MARGIN : 0f);
	}

	protected void generateImages(Document document, DocumentLayoutSetup documentLayoutSetup,
								  DocumentLayoutData documentLayoutData, Institute institute, Student student,
								  StudentManager studentManager) throws IOException {
		int instituteId = institute.getInstituteId();
		float bgImageHeightWidth = 500f;

		//Watermark
		generateWatermark(documentLayoutData, institute.getInstituteId(), bgImageHeightWidth, bgImageHeightWidth, bgImageHeightWidth / 5 - 50, bgImageHeightWidth / 2 - 80);

		//LOGO
		generateDynamicImageProvider(documentLayoutData, 30f, documentLayoutSetup.getPageSize().getHeight() * 0.75f, instituteId, InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

		//Student Image
		byte[] image = getStudentImage(institute.getInstituteId(), student, studentManager);
		if (image == null) {
			image = documentLayoutData.getImageFrame();
		}
		generateImage(document, documentLayoutSetup, image, 80, 90,
				documentLayoutData.getDocumentLayoutSetup().getPageSize().getWidth() - 120f,
				documentLayoutSetup.getPageSize().getHeight() * 0.75f);
	}

	protected void generateLogo(Document document, DocumentLayoutSetup documentLayoutSetup, DocumentLayoutData documentLayoutData, Institute institute, byte[] logo,
								float offsetX, float offsetY) throws MalformedURLException, IOException {

		generateImage(document, documentLayoutSetup, logo,
				documentLayoutData.getLogoWidth(), documentLayoutData.getLogoHeight(), offsetX, offsetY);
	}

	protected DocumentLayoutData generateCharacterCertificateLayoutData(Institute institute, DocumentOutput documentOutput,
																		float logoWidth, float logoHeight) throws IOException {

		DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false,  DEFAULT_PAGE_SIZE, DEFAULT_PAGE_TOP_MARGIN, DEFAULT_PAGE_BOTTOM_MARGIN, DEFAULT_PAGE_SIDE_MARGIN,
				0f);

		float contentFontSize = 14f;
		float defaultBorderWidth = 0.5f;
		Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

		DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getRegularFont(),
				getRegularBoldFont(), contentFontSize, defaultBorderWidth, logoWidth, logoHeight,
				LogoProvider.INSTANCE.getLogo(institute.getInstituteId()), null, null);
		documentLayoutData.setImageFrame(ImageProvider.INSTANCE.getImage(ImageProvider.IMAGE_FRAME));
		return documentLayoutData;
	}

	protected void generateHeader(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
								  PdfFont cambriaBoldFont, PdfFont cambriaFont, Student student, Institute institute) throws IOException {

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.CENTER).setFontSize(contentFontSize);

		Table table = getPDFTable(documentLayoutSetup, 1).setTextAlignment(TextAlignment.CENTER);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getInstituteName())),
				cellLayoutSetup.copy().setFontSize(contentFontSize + 2));
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine1())),
				cellLayoutSetup.copy().setFontSize(contentFontSize - 3));
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine2())),
				cellLayoutSetup.setFontSize(contentFontSize - 3));
		document.add(table);

		addBlankLine(document, true, 2);

		table = getPDFTable(documentLayoutSetup, 1).setTextAlignment(TextAlignment.CENTER);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Session : " +
						student.getStudentAcademicSessionInfoResponse().getAcademicSession().getShortYearDisplayName())),
				cellLayoutSetup.copy().setFontSize(contentFontSize));
		document.add(table);
		table = getPDFTable(documentLayoutSetup, 1).setUnderline().setTextAlignment(TextAlignment.CENTER);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("CHARACTER CERTIFICATE")),
				cellLayoutSetup.copy().setFontSize(contentFontSize + 2));
		document.add(table);

		addBlankLine(document, true, 3);

	}

	protected void generateContent(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
								   Student student, PdfFont cambriaBoldFont, PdfFont cambriaFont,
								   String status, String board) throws IOException {

		Table table = getPDFTable(documentLayoutSetup, 1);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setFontSize(contentFontSize - 1).setTextAlignment(TextAlignment.JUSTIFIED);

		String studentName = student.getStudentBasicInfo().getName().toUpperCase();
		String sonDaughter = PronounUtils.getSonDaughter(student.getStudentBasicInfo().getGender(), true);
		String fatherName = student.getStudentFamilyInfo() == null ? NA :
				StringUtils.isBlank(student.getStudentFamilyInfo().getFathersName()) ? NA :
						student.getStudentFamilyInfo().getFathersName().toUpperCase();
		String srNumber = student.getStudentBasicInfo().getAdmissionNumber();
		String dob = student.getStudentBasicInfo().getDateOfBirth() == null ? NA :
				DateUtils.getFormattedDate(student.getStudentBasicInfo().getDateOfBirth());
		String dobInWords = student.getStudentBasicInfo().getDateOfBirth() == null ? NA :
				DateUtils.getDateInWords(student.getStudentBasicInfo().getDateOfBirth());
		String admissionDate = student.getStudentBasicInfo().getAdmissionDate() == null ? NA :
				DateUtils.getFormattedDate(student.getStudentBasicInfo().getAdmissionDate());
		String relievedDate = student.getStudentBasicInfo().getRelieveDate() == null ? NA :
				DateUtils.getFormattedDate(student.getStudentBasicInfo().getRelieveDate());
		String hisHer = PronounUtils.getHisHer(student.getStudentBasicInfo().getGender(), false);
		String statusValue = status;
		String boardValue = board;
		Text t1 = new Text(String.format(CONTENT_1)).setFont(cambriaFont);
		Text t2 = new Text(String.format(CONTENT_2, studentName)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
		Text t3 = new Text(String.format(CONTENT_3, sonDaughter)).setFont(cambriaFont);
		Text t4 = new Text(String.format(CONTENT_4)).setFont(cambriaFont);
		Text t5 = new Text(String.format(CONTENT_5, fatherName)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
		Text t6 = new Text(String.format(CONTENT_6)).setFont(cambriaFont);
		Text t7 = new Text(String.format(CONTENT_7, srNumber)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
		Text t8 = new Text(String.format(CONTENT_8)).setFont(cambriaFont);
		Text t9 = new Text(String.format(CONTENT_9)).setFont(cambriaFont);
		Text t10 = new Text(String.format(CONTENT_10, dob)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
		Text t9_1 = new Text(String.format(CONTENT_9_1)).setFont(cambriaFont);
		Text t10_1 = new Text(String.format(CONTENT_10_1, dobInWords)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
		Text t11 = new Text(String.format(CONTENT_11)).setFont(cambriaFont);
		Text t12 = new Text(String.format(CONTENT_12, admissionDate)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
		Text t13 = new Text(String.format(CONTENT_13)).setFont(cambriaFont);
		Text t14 = new Text(String.format(CONTENT_14, relievedDate)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
		Text t15 = new Text(String.format(CONTENT_15)).setFont(cambriaFont);
		Text t16 = new Text(String.format(CONTENT_16, hisHer)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
		Text t17 = new Text(String.format(CONTENT_17)).setFont(cambriaFont);
//		Text t18 = new Text(String.format(CONTENT_18, statusValue)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
//		Text t19 = new Text(String.format(CONTENT_19)).setFont(cambriaFont);
//		Text t20 = new Text(String.format(CONTENT_20, boardValue)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
		Text t21 = new Text(String.format(CONTENT_21)).setFont(cambriaFont);


		Paragraph p1 = new Paragraph();
		p1.add(t1).add(t2).add(t3).add(t4).add(t5).add(t6).add(t7).add(t8).add(t9).add(t10).add(t9_1).add(t10_1).add(t11).add(t12)
				.add(t13).add(t14).add(t15).add(t16).add(t17).add(t21);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(p1, cellLayoutSetup)));
		document.add(table);

		addBlankLine(document, true, 3);
	}


	protected void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
										PdfFont cambriaBoldFont, PdfFont cambriaFont) throws IOException {
		Table table = getPDFTable(documentLayoutSetup, 2);
		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(cambriaFont).setFontSize(contentFontSize).setTextAlignment(TextAlignment.LEFT);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(getParagraph(""), signatureCellLayoutSetup),
				new CellData(getParagraph(""), signatureCellLayoutSetup)));
		document.add(table);
		addBlankLine(document, true, 2);
		table = getPDFTable(documentLayoutSetup, 2);
		signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup signatureRightCellLayoutSetup = new CellLayoutSetup();
		signatureRightCellLayoutSetup.setTextAlignment(TextAlignment.RIGHT);
		String currentDate = DateUtils.getFormattedDate(DateUtils.now());
		Text t1 = new Text(SIGNATURE_1).setFont(cambriaFont).setFontSize(contentFontSize);
		Text t2 = new Text(String.format(SIGNATURE_2, currentDate)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
		Text t3 = new Text(SIGNATURE_3).setFont(cambriaFont).setFontSize(contentFontSize);
		Paragraph paragraph1 = new Paragraph();
		paragraph1.add(t1).add(t2);
		Paragraph paragraph2 = new Paragraph();
		paragraph2.add(t3);
		addRow(table, documentLayoutSetup, Arrays.asList(
				new CellData(paragraph1, signatureCellLayoutSetup),
				new CellData(paragraph2, signatureRightCellLayoutSetup)));
		document.add(table);
	}
}
