package com.lernen.cloud.pdf.certificates.transfer;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.*;
import com.lernen.cloud.core.api.user.UserCategory;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;

public class TransferCertificateGenerator10275 extends CBSETransferCertificateGenerator {

    public TransferCertificateGenerator10275(AssetProvider assetProvider) {
                super(assetProvider);
                //TODO Auto-generated constructor stub
        }

private static final Logger logger = LogManager.getLogger(TransferCertificateGenerator10275.class);

    @Override
    public DocumentOutput generateTransferCertificate(StudentManager studentManager, Institute institute, Student student,
                                                      String documentName) {
        try {
            float squareBorderMargin = 8f;
            float borderInnerGap = 2f;

            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

            DocumentLayoutData admissionFormLayoutData = generateTransferCertificateLayoutData(institute, documentOutput,
                    60f, 60f);
            Document document = admissionFormLayoutData.getDocument();
            int index = 1;

            addBlankLine(document, false, 1);
            StudentTransferCertificateDetails studentTransferCertificateDetails = student.getStudentTransferCertificateDetails();
            generatePageHeader(admissionFormLayoutData, institute, studentTransferCertificateDetails);

            addBlankLine(document, false, 1);

            index = generateBasicInformationPage(admissionFormLayoutData, studentManager, institute,
                    studentTransferCertificateDetails, index, true);

            generateDeclarationPage(admissionFormLayoutData, document, squareBorderMargin, borderInnerGap);

            generateSignatureBox(admissionFormLayoutData, squareBorderMargin, borderInnerGap);

            generateMetadata(admissionFormLayoutData, squareBorderMargin, borderInnerGap);

            document.close();

            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating admission form for institute {}, student id {}",
                    institute.getInstituteId(), student.getStudentId(), e);
        }
        return null;
    }

    private void generateDeclarationPage(DocumentLayoutData documentLayoutData, Document document, float squareBorderMargin, float borderInnerGap) {
        PdfFont boldFont = documentLayoutData.getBoldFont();
        PdfFont regularFont = documentLayoutData.getRegularFont();

        addBlankLine(document, false, 1);

        Table table = getPDFTable(documentLayoutData.getDocumentLayoutSetup(), 1);
        CellLayoutSetup headlineCellLayoutSetup = new CellLayoutSetup();
        headlineCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize())
                .setTextAlignment(TextAlignment.LEFT).setPdfFont(regularFont);
        addRow(table, documentLayoutData.getDocumentLayoutSetup(), Collections.singletonList(
                new CellData(getParagraph("I hereby declare that the above information including name of the candidate, father’s name, mother’s name and date of birth furnished above is correct as per school records.", regularFont),
                        headlineCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT))));
        documentLayoutData.getDocument().add(table);

        addBlankLine(document, false, 1);

        table = getPDFTable(documentLayoutData.getDocumentLayoutSetup(), 1);
        addRow(table, documentLayoutData.getDocumentLayoutSetup(), Collections.singletonList(
                new CellData(getParagraph("Note: If this T.C. is issued by the officiating/Incharge Principal, invariably counter signed by the Manager-S.M.C.", regularFont),
                        headlineCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT))));

        documentLayoutData.getDocument().add(table);
    }

    protected DocumentLayoutData generateTransferCertificateLayoutData(Institute institute, DocumentOutput documentOutput,
                                                                       float logoWidth, float logoHeight) throws IOException {

        DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false, PageSize.A4, 5f, 5f,
                20f, 0f);
        float contentFontSize = 12f;
        float defaultBorderWidth = 0.5f;
        Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
        int instituteId = institute.getInstituteId();
        instituteId = 10275;
        DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getCambriaFont(),
                getCambriaBoldFont(), contentFontSize, defaultBorderWidth, logoWidth, logoHeight,
                LogoProvider.INSTANCE.getLogo(instituteId), null, null);
        documentLayoutData.setImageFrame(ImageProvider.INSTANCE.getImage(ImageProvider.IMAGE_FRAME));
        return documentLayoutData;
    }

    protected void generatePageHeader(DocumentLayoutData documentLayoutData, Institute institute,
                                      StudentTransferCertificateDetails studentTransferCertificateDetails) throws IOException {
        Document document = documentLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
        PdfFont boldFont = documentLayoutData.getBoldFont();
        PdfFont regularFont = documentLayoutData.getRegularFont();

//        generateImage(document, documentLayoutSetup, documentLayoutData.getLogo(), documentLayoutData.getLogoWidth(),
//                documentLayoutData.getLogoHeight(), 20f,
//                documentLayoutSetup.getPageSize().getHeight() * 0.87f, 1.5f, 1.5f);

        generateDynamicImageProvider(documentLayoutData, 20f, documentLayoutSetup.getPageSize().getHeight() * 0.87f,
                1.5f, 1.5f, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

        float bgImageHeightWidth = 500f;
        //Institute watermark
        int instituteId = institute.getInstituteId();
        generateWatermark(documentLayoutData, instituteId, bgImageHeightWidth, bgImageHeightWidth, bgImageHeightWidth / 5 - 50, bgImageHeightWidth / 2 - 80);


        int singleContentColumn = 1;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(regularFont).setFontSize(
                documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.CENTER);


        addRow(table, documentLayoutSetup, Arrays.asList(
            getParagraph(institute.getInstituteName().toUpperCase(), EColorUtils.purpleR, EColorUtils.purpleG, EColorUtils.purpleB)),
                cellLayoutSetup.copy().setFontSize(documentLayoutData.getContentFontSize() + 2).setPdfFont(boldFont));

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine1())),
                cellLayoutSetup.copy().setFontSize(documentLayoutData.getContentFontSize() - 2));

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine2())),
                cellLayoutSetup.copy().setFontSize(documentLayoutData.getContentFontSize() - 2));

        document.add(table);

        addBlankLine(document, false, 1);

        table = getPDFTable(documentLayoutSetup, singleContentColumn);

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("TRANSFER CERTIFICATE").setUnderline()),
                cellLayoutSetup.copy().setPdfFont(boldFont));

        document.add(table);

    }

    protected int generateBasicInformationPage(DocumentLayoutData documentLayoutData, StudentManager studentManager,
            Institute institute, StudentTransferCertificateDetails studentTransferCertificateDetails, int index,
                                               boolean addSchoolInfoData) throws IOException {

        PdfFont boldFont = documentLayoutData.getBoldFont();
        PdfFont regularFont = documentLayoutData.getRegularFont();

        int headlineContentColumn = 3;
        Table headlineTable = getPDFTable(documentLayoutData.getDocumentLayoutSetup(), headlineContentColumn);
        CellLayoutSetup headlineCellLayoutSetup = new CellLayoutSetup();
        headlineCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize())
                .setTextAlignment(TextAlignment.LEFT).setPdfFont(regularFont);

        TCStudentDetails tcStudentDetails = studentTransferCertificateDetails.getTcStudentDetails();
        TCSchoolDetails tcSchoolDetails = studentTransferCertificateDetails.getTcSchoolDetails();
        if(addSchoolInfoData) {

            addRow(headlineTable, documentLayoutData.getDocumentLayoutSetup(), Arrays.asList(
                    new CellData(getKeyValueParagraph("Affiliation No.: ", tcSchoolDetails.getAffiliationNumber(), regularFont, regularFont),
                            headlineCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)),
                    new CellData(getKeyValueParagraph("Book No.:", "", regularFont, regularFont),
                            headlineCellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER)),
                    new CellData(getKeyValueParagraph("Admission No.: ", tcStudentDetails.getAdmissionNumber(), regularFont, regularFont),
                            headlineCellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))
                    ));
            documentLayoutData.getDocument().add(headlineTable);
        }

        headlineContentColumn = 3;
        headlineTable = getPDFTable(documentLayoutData.getDocumentLayoutSetup(), headlineContentColumn);

        headlineCellLayoutSetup = new CellLayoutSetup();
        headlineCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.LEFT).setPdfFont(regularFont);


        addRow(headlineTable, documentLayoutData.getDocumentLayoutSetup(), Arrays.asList(
                new CellData(getKeyValueParagraph("TC Number: " , studentTransferCertificateDetails.getTcNumber(), regularFont, regularFont),
                        headlineCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)),
                new CellData(getKeyValueParagraph("School Code:", tcSchoolDetails.getSchoolCode(), regularFont, regularFont),
                        headlineCellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER)),
                new CellData(getKeyValueParagraph("Status Of School: ", "Sr. Secondary", regularFont, regularFont),
                        headlineCellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))
        ));

        documentLayoutData.getDocument().add(headlineTable);

        Document document = documentLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();

        int singleContentColumn = 1;

        CellLayoutSetup singleCellLayoutSetup = new CellLayoutSetup();
        singleCellLayoutSetup.setFontSize(documentLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.LEFT);

        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Name of Student: ", tcStudentDetails.getStudentName(), false, regularFont, regularFont, false, true)),
                singleCellLayoutSetup);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                        index++, "Mother's Name: ", tcStudentDetails.getMotherName(), false, regularFont, regularFont, false, true)),
                singleCellLayoutSetup);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Father's Name: ", tcStudentDetails.getFatherGuardianName(), false, regularFont, regularFont, false, true)),
                singleCellLayoutSetup);

        document.add(table);

        table = getPDFTable(documentLayoutSetup, 1);
        String dobInFigures = tcStudentDetails.getDob() == null || tcStudentDetails.getDob() <= 0
                ? EMPTY_VALUE : DateUtils.getFormattedDate(tcStudentDetails.getDob());
        String dobInWords = tcStudentDetails.getDob() == null || tcStudentDetails.getDob() <= 0
                ? EMPTY_VALUE : DateUtils.getDateInWords(tcStudentDetails.getDob());
        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Date Of Birth according to Admission & Withdrawal Register (in figures): ", dobInFigures, false, regularFont, regularFont, false, true)), singleCellLayoutSetup);
        addRow(table, documentLayoutSetup, Arrays.asList(getKeyValueParagraph("(in words) ", dobInWords, regularFont, regularFont, false, true).setPaddingLeft(20f)), singleCellLayoutSetup);
        document.add(table);

        table = getPDFTable(documentLayoutSetup, 1);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Nationality: ", tcStudentDetails.getNationality(), false, regularFont, regularFont, false, true)),
        singleCellLayoutSetup);

        boolean isScStObc = false;
        if(tcStudentDetails.getCategory() != null &&
                (tcStudentDetails.getCategory() == UserCategory.SC || tcStudentDetails.getCategory() == UserCategory.ST || tcStudentDetails.getCategory() == UserCategory.OBC)) {
            isScStObc = true;
        }
        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Whether the candidate belongs to ST/SC or OBC: ",
                isScStObc ? "Yes" : "No", false, regularFont, regularFont, false, true)), singleCellLayoutSetup);

        document.add(table);

        table = getPDFTable(documentLayoutSetup, singleContentColumn);

//        document.add(table);
//
//        table = getPDFTable(documentLayoutSetup, 2);
//        String dobInFigures = tcStudentDetails.getDob() == null || tcStudentDetails.getDob() <= 0
//                ? EMPTY_VALUE : DateUtils.getFormattedDate(tcStudentDetails.getDob());
//        String dobInWords = tcStudentDetails.getDob() == null || tcStudentDetails.getDob() <= 0
//                ? EMPTY_VALUE : DateUtils.getDateInWords(tcStudentDetails.getDob());
//        addRow(table, documentLayoutSetup, Arrays.asList(
//                getTransferCertificateKeyValueParagraph(index++, "Date Of Birth (in figures): ", dobInFigures),
//                getKeyValueParagraph("(in words): ", dobInWords)),
//                singleCellLayoutSetup);
//        document.add(table);
//
//        table = getPDFTable(documentLayoutSetup, singleContentColumn);

        String dateOfFirstAdmissionWithClass = tcStudentDetails.getAdmissionDate() == null || tcStudentDetails.getAdmissionDate() <= 0
                ? EMPTY_VALUE : DateUtils.getFormattedDate(tcStudentDetails.getAdmissionDate())
                + (StringUtils.isBlank(tcStudentDetails.getAdmissionClass()) ? EMPTY_VALUE : ", Class: " + tcStudentDetails.getAdmissionClass());

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                        index++, "Date of first admission in school with class: ", dateOfFirstAdmissionWithClass, false, regularFont, regularFont, false, true)), singleCellLayoutSetup);

        TCStudentLastActiveSessionDetails tcStudentLastActiveSessionDetails =
                studentTransferCertificateDetails.getTcStudentLastActiveSessionDetails();
        document.add(table);

        table = getPDFTable(documentLayoutSetup, 2);
        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Class in which pupil last studied: ",
                tcStudentLastActiveSessionDetails.getLastActiveSessionClass(), false, regularFont, regularFont, false, true)),
                singleCellLayoutSetup);
        document.add(table);

        table = getPDFTable(documentLayoutSetup, singleContentColumn);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "School/Board Annual examination last taken with result: ",
                tcStudentLastActiveSessionDetails.getLastExamTakenWithResult(), false, regularFont, regularFont, false, true)), singleCellLayoutSetup);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Whether failed, if so once/twice in the same class: ",
                tcStudentLastActiveSessionDetails.getNumberOfTimeExamFailed(), false, regularFont, regularFont, false, true)), singleCellLayoutSetup);

        String subjects = CollectionUtils.isEmpty(tcStudentLastActiveSessionDetails.getScholasticCoursesLastActiveSession()) ? EMPTY_TEXT :
                String.join(", " , tcStudentLastActiveSessionDetails.getScholasticCoursesLastActiveSession());
        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Subjects Studied: ", subjects, false, regularFont, regularFont, false, true)), singleCellLayoutSetup);
//        document.add(table);
//
//        table = getPDFTable(documentLayoutSetup, 2);
//
//        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
//                index++, "Whether qualified for promotion in higher class: ",
//                tcStudentLastActiveSessionDetails.getPromotionToHigherClass()),
//                getTransferCertificateKeyValueParagraph(index++, "If so in which class: ",
//                tcStudentLastActiveSessionDetails.getPromotingClassName())),
//                singleCellLayoutSetup);
//
//        document.add(table);
//
//        table = getPDFTable(documentLayoutSetup, singleContentColumn);

//        document.add(table);
//
//        table = getPDFTable(documentLayoutSetup, 1);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Whether qualified for promotion in higher class: ",
                tcStudentLastActiveSessionDetails.getPromotionToHigherClass(), false, regularFont, regularFont, false, true)), singleCellLayoutSetup);

        addRow(table, documentLayoutSetup, Arrays.asList(getKeyValueParagraph("If so in which class: ",
                tcStudentLastActiveSessionDetails.getPromotingClassName(), regularFont, regularFont, false, true).setPaddingLeft(20f)), singleCellLayoutSetup);

//        document.add(table);
//
//        table = getPDFTable(documentLayoutSetup, singleContentColumn);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Total No Of Working Days in Academic Session: ",
                tcStudentLastActiveSessionDetails.getTotalWorkingDays(), false, regularFont, regularFont, false, true)), singleCellLayoutSetup);

//        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
//                index++, "Month up to which (pupil had paid) school dues paid: ",
//                tcStudentLastActiveSessionDetails.getLastFeesPaid(), false, regularFont, regularFont, false, true)), singleCellLayoutSetup);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Any fees concession availed of: If so, the nature of such concession: ",
                tcStudentLastActiveSessionDetails.getDiscountWithNature(), false, regularFont, regularFont, false, true)), singleCellLayoutSetup);

//        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
//                index++, "Total No. of working days: ",
//                tcStudentLastActiveSessionDetails.getTotalWorkingDays(), false, regularFont, regularFont, false, true)), singleCellLayoutSetup);

//        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
//                index++, "Total No. of working days present: ",
//                tcStudentLastActiveSessionDetails.getTotalAttendedDays(), false, regularFont, regularFont, false, true)), singleCellLayoutSetup);

        TCOtherDetails tcOtherDetails = studentTransferCertificateDetails.getTcOtherDetails();

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Whether NCC Cadet/Boy Scout/Girl Guide (details may be given): ",
                tcOtherDetails.getNccCadetBoyScoutGirlGuideWithDetails(), false, regularFont, regularFont, false, true)), singleCellLayoutSetup);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Whether School Is Under Govt/Minority/Independent Category: ",
                "Independent", false, regularFont, regularFont, false, true)), singleCellLayoutSetup);

//        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
//                index++, "Games played extra curricular activities in which the pupil usually took part (mention achievement level therein): ",
//                tcOtherDetails.getCoCurricularActivities(), false, regularFont, regularFont, false, true)), singleCellLayoutSetup);


        TCStudentRelievingDetails tcStudentRelievingDetails = studentTransferCertificateDetails.getTcStudentRelievingDetails();

//        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
//                index++, "General Conduct: ", tcStudentRelievingDetails.getCodeOfConduct(), false, regularFont, regularFont, false, true)), singleCellLayoutSetup);

        String relieveDate = tcStudentRelievingDetails.getRelieveDate() == null || tcStudentRelievingDetails.getRelieveDate() <= 0 ?
                EMPTY_VALUE : DateUtils.getFormattedDate(tcStudentRelievingDetails.getRelieveDate());
        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Date on which pupils name was stuck off the rolls of the school: ", relieveDate, false, regularFont, regularFont, false, true)), singleCellLayoutSetup);
        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Date of application of Certificate: ", relieveDate, false, regularFont, regularFont, false, true)), singleCellLayoutSetup);

        String tcGenerationDate = studentTransferCertificateDetails.getTcGenerationDate() == null || studentTransferCertificateDetails.getTcGenerationDate() <= 0 ?
                EMPTY_VALUE : DateUtils.getFormattedDate(studentTransferCertificateDetails.getTcGenerationDate());
        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Date of issue of Certificate: ", tcGenerationDate, false, regularFont, regularFont, false, true)), singleCellLayoutSetup);

//        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
//                index++, "Reason for leaving the school: ", tcStudentRelievingDetails.getRelieveReason(), false, regularFont, regularFont, false, true)),
//                singleCellLayoutSetup);

        addRow(table, documentLayoutSetup, Arrays.asList(getTransferCertificateKeyValueParagraph(
                index++, "Any other remarks: ", tcStudentRelievingDetails.getRemarks(), false, regularFont, regularFont, false, true)), singleCellLayoutSetup);

        document.add(table);

        return index;
    }

    protected void generateSignatureBox(DocumentLayoutData documentLayoutData, float squareBorderMargin, float innerGap)
            throws IOException {

        Document document = documentLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
        PageSize pageSize = documentLayoutSetup.getPageSize();

        int singleContentColumn = 3;

        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
        signatureCellLayoutSetup.setPdfFont(documentLayoutData.getBoldFont())
                .setFontSize(documentLayoutData.getContentFontSize())
                .setTextAlignment(TextAlignment.LEFT);

        addRow(table, documentLayoutSetup, Arrays.asList(
                new CellData("Prepared by", signatureCellLayoutSetup),
                new CellData("Checked by", signatureCellLayoutSetup.setTextAlignment(TextAlignment.CENTER)),
                new CellData("Sign. Of Principal With Seal", signatureCellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))));


        table.setFixedPosition(document.getLeftMargin(), document.getBottomMargin() + 5, pageSize.getWidth() - document.getLeftMargin() - document.getRightMargin());

        document.add(table);
    }
}
