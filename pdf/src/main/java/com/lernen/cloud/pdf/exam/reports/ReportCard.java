package com.lernen.cloud.pdf.exam.reports;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;

import org.springframework.util.CollectionUtils;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.fees.payment.FeeHeadInvoice;
import com.lernen.cloud.core.api.fees.payment.FeeIdInvoice;
import com.lernen.cloud.core.api.fees.payment.FeePaymentInvoiceSummary;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.pdf.base.PDFGenerator;

/**
 * 
 * <AUTHOR>
 *
 */
public class ReportCard extends PDFGenerator {

	public ReportCard(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	public static final float DEFAULT_PAGE_SIDE_MARGIN = 40f;
	// public static final float DEFAULT_PAGE_RIGHT_MARGIN = 5f;
	public static final float DEFAULT_PAGE_TOP_MARGIN = 20f;
	public static final float DEFAULT_PAGE_BOTTOM_MARGIN = 20f;

	public DocumentOutput generateReportCard(Institute institute, Student student, String documentName) {
		int instituteId = institute.getInstituteId();
		try {
			DocumentOutput invoice = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false);
			float contentFontSize = 12f;

			Document document = initDocument(invoice.getContent(), documentLayoutSetup);

			generateLogo(document, documentLayoutSetup, LogoProvider.INSTANCE.getLogo(instituteId));
			generateHeader(document, documentLayoutSetup, student, institute);
			generateStudentInformation(document, documentLayoutSetup, contentFontSize, student);
			generateScholasticMarksGrid(document, documentLayoutSetup, contentFontSize);
			generateCoScholasticMarksGrid(document, documentLayoutSetup, contentFontSize);
			// generateContent(document, documentLayoutSetup, contentFontSize,
			// student, institute);
			generateSignatureBox(document, documentLayoutSetup, contentFontSize);
			PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), 1);
			canvas.rectangle(10, 10, documentLayoutSetup.getPageSize().getWidth() - 20,
					documentLayoutSetup.getPageSize().getHeight() - 20);
			canvas.stroke();
			canvas.setLineWidth(1f);
			canvas.rectangle(12, 12, documentLayoutSetup.getPageSize().getWidth() - 24,
					documentLayoutSetup.getPageSize().getHeight() - 24);
			canvas.stroke();
			canvas.setLineWidth(1f);
			document.close();
			return invoice;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	@Override
	public DocumentLayoutSetup initDocumentLayoutSetup(boolean officeCopy) {
		return initDocumentLayoutSetup(officeCopy, officeCopy ? DEFAULT_PAGE_SIZE.rotate() : DEFAULT_PAGE_SIZE,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN : DEFAULT_PAGE_TOP_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN : DEFAULT_PAGE_BOTTOM_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_SIDE_MARGIN : DEFAULT_PAGE_SIDE_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_MIDDLE_MARGIN : 0f);
	}

	private void generateHeader(Document document, DocumentLayoutSetup documentLayoutSetup, Student student,
			Institute institute) throws IOException {
		int singleContentColumn = 1;
		int columnCount = getColumnCount(documentLayoutSetup.isOfficeCopy(), singleContentColumn);
		float contentWidth = getContentWidth(documentLayoutSetup);
		Table table = new Table(columnCount);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setWidth(contentWidth).setPdfFont(getRegularBoldFont()).setFontSize(14f)
				.setTextAlignment(TextAlignment.CENTER);

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getInstituteName())), cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine1())),
				cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine2())),
				cellLayoutSetup);
		// addRow(table, documentLayoutSetup,
		// Arrays.asList(getParagraph(NEXT_LINE)), cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(NEXT_LINE)), cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("SCHOLAR'S ANNUAL REPORT CARD")),
				cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(NEXT_LINE)), cellLayoutSetup);
		// addRow(table, documentLayoutSetup,
		// Arrays.asList(getParagraph(NEXT_LINE)), cellLayoutSetup);
		document.add(table);

		// // Student copy section
		// singleContentColumn = 1;
		// columnCount = getColumnCount(documentLayoutSetup.isOfficeCopy(),
		// singleContentColumn);
		// contentWidth = getContentWidth(documentLayoutSetup);
		// table = new Table(columnCount);
		//
		// cellLayoutSetup.setWidth(contentWidth / singleContentColumn);
		//
		// Paragraph admissionNumber = getKeyValueParagraph("ADMISSION NO. - ",
		// student.getStudentBasicInfo().getAdmissionNumber());
		//
		// addRow(table, documentLayoutSetup, Arrays
		// .asList(new CellData(admissionNumber,
		// cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT))));
		// addRow(table, documentLayoutSetup,
		// Arrays.asList(getParagraph(EMPTY_TEXT)), cellLayoutSetup);
		// addRow(table, documentLayoutSetup,
		// Arrays.asList(getParagraph(EMPTY_TEXT)), cellLayoutSetup);
		// document.add(table);

	}

	private void generateStudentInformation(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize, Student student) throws IOException {
		int singleContentColumn = 3;
		int columnCount = getColumnCount(documentLayoutSetup.isOfficeCopy(), singleContentColumn);
		float contentWidth = getContentWidth(documentLayoutSetup);
		Table table = new Table(columnCount);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setWidth(contentWidth / singleContentColumn).setPdfFont(getRegularBoldFont())
				.setFontSize(contentFontSize);

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setWidth(contentWidth * 0.6f)
				.setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setWidth(contentWidth * 0.2f)
				.setTextAlignment(TextAlignment.RIGHT);
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setWidth(contentWidth * 0.2f)
				.setTextAlignment(TextAlignment.LEFT);

		Paragraph admissionNumber = getKeyValueParagraph("Admission No. ",
				student.getStudentBasicInfo().getAdmissionNumber());

		Paragraph studentName = getKeyValueParagraph("Student Name : ", student.getStudentBasicInfo().getName());

		Paragraph fatherName = getKeyValueParagraph("Father Name : ", student.getStudentFamilyInfo().getFathersName());

		Paragraph motherName = getKeyValueParagraph("Mother Name : ", student.getStudentFamilyInfo().getMothersName());

		Paragraph session = getKeyValueParagraph("Session : ",
				student.getStudentAcademicSessionInfoResponse().getAcademicSession().getYearDisplayName());

		Paragraph classValue = getKeyValueParagraph("Class : ",
				student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayName());

		Paragraph dob = getKeyValueParagraph("DOB : ", DateUtils
				.getFormattedDate(student.getStudentBasicInfo().getDateOfBirth(), DATE_FORMAT, User.DFAULT_TIMEZONE));

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(studentName, firstCellLayoutSetup),
				new CellData(new Paragraph(""), secondCellLayoutSetup), new CellData(session, thirdCellLayoutSetup)));

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(admissionNumber, firstCellLayoutSetup),
						new CellData(new Paragraph(""), secondCellLayoutSetup),
						new CellData(classValue, thirdCellLayoutSetup)));

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(fatherName, firstCellLayoutSetup),
				new CellData(new Paragraph(""), secondCellLayoutSetup), new CellData(dob, thirdCellLayoutSetup)));

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(motherName, firstCellLayoutSetup),
						new CellData(new Paragraph(""), secondCellLayoutSetup),
						new CellData(new Paragraph(""), thirdCellLayoutSetup)));

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(NEXT_LINE)), cellLayoutSetup);

		document.add(table);

	}

	private void generateScholasticMarksGrid(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize) throws IOException {

		int marksGridHeaderCount = 5;
		int marksGridHeaderColumnCount = getColumnCount(documentLayoutSetup.isOfficeCopy(), marksGridHeaderCount);
		Table feeHeadTable = new Table(marksGridHeaderColumnCount);
		float contentWidth = getContentWidth(documentLayoutSetup);

		CellLayoutSetup feeHeadCellLayoutSetup = new CellLayoutSetup();
		feeHeadCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
				.setBorder(new SolidBorder(1)).setTextAlignment(TextAlignment.CENTER);

		CellLayoutSetup firstFeeHeadCellLayoutSetup = feeHeadCellLayoutSetup.copy().setWidth(contentWidth * 0.4f)
				.setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup secondFeeHeadCellLayoutSetup = feeHeadCellLayoutSetup.copy().setWidth(contentWidth * 0.15f);
		CellLayoutSetup thirdFeeHeadCellLayoutSetup = feeHeadCellLayoutSetup.copy().setWidth(contentWidth * 0.15f);
		CellLayoutSetup fourthFeeHeadCellLayoutSetup = feeHeadCellLayoutSetup.copy().setWidth(contentWidth * 0.15f);
		CellLayoutSetup fifthFeeHeadCellLayoutSetup = feeHeadCellLayoutSetup.copy().setWidth(contentWidth * 0.15f)
				.setPdfFont(getRegularBoldFont());

		addRow(feeHeadTable, documentLayoutSetup,
				Arrays.asList(new CellData("Course", firstFeeHeadCellLayoutSetup),
						new CellData("Term I", secondFeeHeadCellLayoutSetup),
						new CellData("Half Yearly", thirdFeeHeadCellLayoutSetup),
						new CellData("Annual Exam", fourthFeeHeadCellLayoutSetup),
						new CellData("Total", fifthFeeHeadCellLayoutSetup)));

		addRow(feeHeadTable, documentLayoutSetup,
				Arrays.asList(new CellData("English", firstFeeHeadCellLayoutSetup.setPdfFont(getRegularFont())),
						new CellData("95", secondFeeHeadCellLayoutSetup.setPdfFont(getRegularFont())),
						new CellData("98", thirdFeeHeadCellLayoutSetup.setPdfFont(getRegularFont())),
						new CellData("96", fourthFeeHeadCellLayoutSetup.setPdfFont(getRegularFont())),
						new CellData("289", fifthFeeHeadCellLayoutSetup.setPdfFont(getRegularFont()))));

		addRow(feeHeadTable, documentLayoutSetup,
				Arrays.asList(new CellData("Hindi", firstFeeHeadCellLayoutSetup.setPdfFont(getRegularFont())),
						new CellData("91", secondFeeHeadCellLayoutSetup.setPdfFont(getRegularFont())),
						new CellData("88", thirdFeeHeadCellLayoutSetup.setPdfFont(getRegularFont())),
						new CellData("86", fourthFeeHeadCellLayoutSetup.setPdfFont(getRegularFont())),
						new CellData("265", fifthFeeHeadCellLayoutSetup.setPdfFont(getRegularFont()))));

		addRow(feeHeadTable, documentLayoutSetup,
				Arrays.asList(new CellData("Maths", firstFeeHeadCellLayoutSetup.setPdfFont(getRegularFont())),
						new CellData("100", secondFeeHeadCellLayoutSetup.setPdfFont(getRegularFont())),
						new CellData("100", thirdFeeHeadCellLayoutSetup.setPdfFont(getRegularFont())),
						new CellData("100", fourthFeeHeadCellLayoutSetup.setPdfFont(getRegularFont())),
						new CellData("300", fifthFeeHeadCellLayoutSetup.setPdfFont(getRegularFont()))));

		addRow(feeHeadTable, documentLayoutSetup,
				Arrays.asList(new CellData("Science", firstFeeHeadCellLayoutSetup.setPdfFont(getRegularFont())),
						new CellData("90", secondFeeHeadCellLayoutSetup.setPdfFont(getRegularFont())),
						new CellData("100", thirdFeeHeadCellLayoutSetup.setPdfFont(getRegularFont())),
						new CellData("99", fourthFeeHeadCellLayoutSetup.setPdfFont(getRegularFont())),
						new CellData("289", fifthFeeHeadCellLayoutSetup.setPdfFont(getRegularFont()))));

		addRow(feeHeadTable, documentLayoutSetup,
				Arrays.asList(new CellData("Sanskrit", firstFeeHeadCellLayoutSetup.setPdfFont(getRegularFont())),
						new CellData("90", secondFeeHeadCellLayoutSetup.setPdfFont(getRegularFont())),
						new CellData("88", thirdFeeHeadCellLayoutSetup.setPdfFont(getRegularFont())),
						new CellData("100", fourthFeeHeadCellLayoutSetup.setPdfFont(getRegularFont())),
						new CellData("278", fifthFeeHeadCellLayoutSetup.setPdfFont(getRegularFont()))));

		addRow(feeHeadTable, documentLayoutSetup, Arrays.asList(getParagraph(NEXT_LINE)), new CellLayoutSetup());

		document.add(feeHeadTable);

	}

	private void generateCoScholasticMarksGrid(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize) throws IOException {

		int marksGridHeaderCount = 2;
		int marksGridHeaderColumnCount = getColumnCount(documentLayoutSetup.isOfficeCopy(), marksGridHeaderCount);
		Table feeHeadTable = new Table(marksGridHeaderColumnCount);
		float contentWidth = getContentWidth(documentLayoutSetup);

		CellLayoutSetup feeHeadCellLayoutSetup = new CellLayoutSetup();
		feeHeadCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
				.setBorder(new SolidBorder(1)).setTextAlignment(TextAlignment.CENTER);

		CellLayoutSetup firstFeeHeadCellLayoutSetup = feeHeadCellLayoutSetup.copy().setWidth(contentWidth * 0.5f)
				.setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup secondFeeHeadCellLayoutSetup = feeHeadCellLayoutSetup.copy().setWidth(contentWidth * 0.5f);
		CellLayoutSetup thirdFeeHeadCellLayoutSetup = feeHeadCellLayoutSetup.copy().setWidth(contentWidth * 0.15f);
		CellLayoutSetup fourthFeeHeadCellLayoutSetup = feeHeadCellLayoutSetup.copy().setWidth(contentWidth * 0.15f);
		CellLayoutSetup fifthFeeHeadCellLayoutSetup = feeHeadCellLayoutSetup.copy().setWidth(contentWidth * 0.15f)
				.setPdfFont(getRegularBoldFont());

		addRow(feeHeadTable, documentLayoutSetup, Arrays.asList(new CellData("Course", firstFeeHeadCellLayoutSetup),
				new CellData("Total Grade", secondFeeHeadCellLayoutSetup)));

		addRow(feeHeadTable, documentLayoutSetup,
				Arrays.asList(new CellData("Arts", firstFeeHeadCellLayoutSetup.setPdfFont(getRegularFont())),
						new CellData("A", secondFeeHeadCellLayoutSetup.setPdfFont(getRegularFont()))));

		addRow(feeHeadTable, documentLayoutSetup,
				Arrays.asList(
						new CellData("Health Education", firstFeeHeadCellLayoutSetup.setPdfFont(getRegularFont())),
						new CellData("B", secondFeeHeadCellLayoutSetup.setPdfFont(getRegularFont()))));

		addRow(feeHeadTable, documentLayoutSetup,
				Arrays.asList(new CellData("Music", firstFeeHeadCellLayoutSetup.setPdfFont(getRegularFont())),
						new CellData("A-", secondFeeHeadCellLayoutSetup.setPdfFont(getRegularFont()))));

		document.add(feeHeadTable);

	}

	private void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize)
			throws IOException {
		int singleContentColumn = 3;
		int columnCount = getColumnCount(documentLayoutSetup.isOfficeCopy(), singleContentColumn);
		float contentWidth = getContentWidth(documentLayoutSetup);
		Table table = new Table(columnCount);

		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setWidth(contentWidth / singleContentColumn).setPdfFont(getRegularBoldFont())
				.setFontSize(contentFontSize).setTextAlignment(TextAlignment.CENTER);

		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
				signatureCellLayoutSetup);
		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
				signatureCellLayoutSetup);
		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
				signatureCellLayoutSetup);
		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
				signatureCellLayoutSetup);
		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
				signatureCellLayoutSetup);
		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
				signatureCellLayoutSetup);

		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
				signatureCellLayoutSetup);
		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
				signatureCellLayoutSetup);

		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
				signatureCellLayoutSetup);
		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
				signatureCellLayoutSetup);

		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
				signatureCellLayoutSetup);
		addRow(table, documentLayoutSetup,
				Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
				signatureCellLayoutSetup);

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Signature Class Teacher"),
				getParagraph("Checked By"), getParagraph("Principal")), signatureCellLayoutSetup);
		document.add(table);
	}

}
