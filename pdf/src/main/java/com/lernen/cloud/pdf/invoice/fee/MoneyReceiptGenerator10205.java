package com.lernen.cloud.pdf.invoice.fee;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceCmyk;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.Border;
import com.itextpdf.layout.borders.RoundDotsBorder;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.*;
import com.itextpdf.layout.element.Image;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.HorizontalAlignment;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.configurations.FeeInvoicePreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.fees.payment.*;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.staff.StaffDocumentType;
import com.lernen.cloud.core.api.transport.StudentTransportDetails;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserAppToken;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.awt.*;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public class MoneyReceiptGenerator10205 extends GlobalShrinkedSizeMoneyReceiptGenerator {

    public MoneyReceiptGenerator10205(AssetProvider assetProvider) {
        super(assetProvider);
        //TODO Auto-generated constructor stub
    }

    public static final float DEFAULT_LOGO_WIDTH = 40f;
    public static final float DEFAULT_LOGO_HEIGHT = 40f;

    @Override
    public DocumentOutput generateInvoice(Institute institute, FeePaymentInvoiceSummary feePaymentInvoiceSummary,
                                          StudentTransportDetails studentTransportDetails, String documentName, boolean officeCopy, UserType userType, StudentFeesDetailsLite studentFeesDetailsLite, FeeInvoicePreferences feeInvoicePreferences) {
        try {
            DocumentOutput invoice = new DocumentOutput(documentName, new ByteArrayOutputStream());
            if(userType == UserType.STUDENT) {
                officeCopy = false;
            }
            DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(officeCopy);
            float contentFontSize = 8f;
            float headerFontSize = 11f;
            float defaultBorderWidth = 0.1f;
            Document document = initDocument(invoice.getContent(), documentLayoutSetup);
            DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getNunitoRegularFont(), getAleoBoldFont(), null, null, DEFAULT_LOGO_WIDTH, DEFAULT_LOGO_HEIGHT, LogoProvider.INSTANCE.getLogo(institute.getInstituteId()), getGlacialRegularFont(), getHindiBoldFont());
            
            generateFeeInvoice(institute, documentLayoutData, feePaymentInvoiceSummary,
                    headerFontSize, defaultBorderWidth, institute.getInstituteId(), contentFontSize,
                    1, STUDENT_COPY_TEXT, userType);

            document.close();
            return invoice;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public void generateFeeInvoice(Institute institute, DocumentLayoutData documentLayoutData,
                                   FeePaymentInvoiceSummary feePaymentInvoiceSummary,
                                   float headerFontSize, float defaultBorderWidth, int instituteId, float contentFontSize, int pageNumber,
                                   String studentCopyText, UserType userType) throws IOException {
        
        Document document = documentLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
        
        generateDynamicImageProvider(documentLayoutData, instituteId, InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);
                                    
        generateHeader(document, documentLayoutSetup, feePaymentInvoiceSummary, institute, headerFontSize,
                defaultBorderWidth, studentCopyText, userType);
        generateStudentInformation(document, documentLayoutSetup, contentFontSize, feePaymentInvoiceSummary,
                defaultBorderWidth);
        generateFeeContent(document, documentLayoutSetup, contentFontSize, feePaymentInvoiceSummary,
                defaultBorderWidth);
        generateFeePaymentSummary(document, documentLayoutSetup, instituteId, contentFontSize, feePaymentInvoiceSummary,
                defaultBorderWidth);
        generateSignatureBox(document, documentLayoutSetup, contentFontSize, defaultBorderWidth);
        if (feePaymentInvoiceSummary.getFeePaymentTransactionMetaData()
                .getFeePaymentTransactionStatus() == FeePaymentTransactionStatus.CANCELLED) {
            PdfPage pdfPage = document.getPdfDocument().getPage(pageNumber);
            Rectangle pagesize = pdfPage.getPageSizeWithRotation();

            float x = documentLayoutSetup.isOfficeCopy() ? pagesize.getWidth() / 8 - 50 : pagesize.getWidth() / 4 + 30;
            float y = documentLayoutSetup.isOfficeCopy() ? pagesize.getHeight() * 0.75f : pagesize.getHeight() * 0.70f;

            addWaterMark(document, ImageProvider.INSTANCE.getImage(ImageProvider.CANCELLED_TEXT_IMAGE2), x, y, pageNumber);
        }
    }

    public void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
                                     float defaultBorderWidth) throws IOException {
        int singleContentColumn = 1;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

        generateImage(table, documentLayoutSetup, defaultBorderWidth, ImageProvider.INSTANCE.getImage(
                ImageProvider._10205_PRINCIPAL_SIGNATURE), 50f, 80f, 5f, HorizontalAlignment.RIGHT);
        document.add(table);

        table = getPDFTable(documentLayoutSetup, singleContentColumn);
        CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
        signatureCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.RIGHT).setBorder(new SolidBorder(defaultBorderWidth))
                .setBorderTop(null);

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Signature")), signatureCellLayoutSetup);
        document.add(table);
    }

    @Override
    public DocumentOutput generateBulkInvoices(Institute institute, List<FeePaymentInvoiceSummary>
            feePaymentInvoiceSummaryList, String documentName, boolean doubleCopy, FeeInvoicePreferences feeInvoicePreferences) {
        try {
            DocumentOutput invoice = new DocumentOutput(documentName, new ByteArrayOutputStream());
            DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(doubleCopy);
            float contentFontSize = 8f;
            float headerFontSize = 11f;
            float defaultBorderWidth = 0.1f;
            Document document = initDocument(invoice.getContent(), documentLayoutSetup);
            DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getNunitoRegularFont(), getAleoBoldFont(), null, null, DEFAULT_LOGO_WIDTH, DEFAULT_LOGO_HEIGHT, LogoProvider.INSTANCE.getLogo(institute.getInstituteId()), getGlacialRegularFont(), getHindiBoldFont());
            
            if (CollectionUtils.isEmpty(feePaymentInvoiceSummaryList)) {
                document.close();
                return invoice;
            }

            int pageNumber = 1;
            for (FeePaymentInvoiceSummary feePaymentInvoiceSummary : feePaymentInvoiceSummaryList) {
                generateFeeInvoice(institute, documentLayoutData, feePaymentInvoiceSummary,
                        headerFontSize, defaultBorderWidth, institute.getInstituteId(), contentFontSize, pageNumber,
                        OFFICE_COPY_TEXT, null);

                if (pageNumber != feePaymentInvoiceSummaryList.size()) {
                    document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                }
                pageNumber++;

            }
            document.close();
            return invoice;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
