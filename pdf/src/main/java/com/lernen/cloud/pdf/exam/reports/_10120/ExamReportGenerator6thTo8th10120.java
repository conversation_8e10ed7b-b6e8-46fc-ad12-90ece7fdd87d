package com.lernen.cloud.pdf.exam.reports._10120;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.properties.AreaBreakType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.report.ExamReportCardLayoutData;
import com.lernen.cloud.core.api.examination.report.ExamReportData;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.pdf.exam.reports.IExamReportCardGenerator;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Set;
import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class ExamReportGenerator6thTo8th10120 extends ExamReportGenerator10120 implements IExamReportCardGenerator {
	public ExamReportGenerator6thTo8th10120(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(ExamReportGenerator6thTo8th10120.class);

	@Override
	public DocumentOutput generateReport(Institute institute, Student student, String reportType,
			ExamReportData examReportData, String documentName, StudentManager studentManager) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);
			Document document = examReportCardLayoutData.getDocument();
			DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
			generateStudentReport(document, documentLayoutSetup, institute, examReportData.getStudentLite(), reportType, examReportData, examReportCardLayoutData, 1);
			examReportCardLayoutData.getDocument().close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, student {}, reportType {} ",
					institute.getInstituteId(), student.getStudentId(), reportType, e);
		}
		return null;
	}

	@Override
	public DocumentOutput generateClassReport(Institute institute, String reportType,
													List<ExamReportData> examReportDataList, String documentName, StudentManager studentManager) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);
			Document document = examReportCardLayoutData.getDocument();
			DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
			sortClassExamReports(examReportDataList);
			int pageNumber = 1;
			for (ExamReportData examReportData : examReportDataList) {
				generateStudentReport(document, documentLayoutSetup, institute, examReportData.getStudentLite(), reportType, examReportData,
						examReportCardLayoutData, pageNumber);

				if (pageNumber != examReportDataList.size()) {
					examReportCardLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				pageNumber++;
			}
			examReportCardLayoutData.getDocument().close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
					reportType, e);
		}
		return null;

	}

	private void generateBackgroundImage(Document document, DocumentLayoutSetup documentLayoutSetup, byte[] image, float width,
										 float height, float offsetX, float offsetY) {
		try {
			generateImage(document, documentLayoutSetup, image, width, height, offsetX, offsetY);
		} catch (Exception e) {
			logger.error("Exception while adding background image", e);
		}

	}

	private void generateStudentReport(Document document, DocumentLayoutSetup documentLayoutSetup, Institute institute, StudentLite studentLite, String reportType,
			ExamReportData examReportData, ExamReportCardLayoutData examReportCardLayoutData, int pageNumber)
			throws IOException {

		float bgImageWidth = documentLayoutSetup.getPageSize().getWidth();
		float bgImageHeight = documentLayoutSetup.getPageSize().getHeight();

		PdfFont nunitoRegular = getNunitoRegularFont();
		PdfFont nunitoBold = getNunitoBoldFont();
		PdfFont bookmanBold = getBookmanOldBoldFont();

		String gridHeadingHexColorCode = "#134f5c";
		List<Integer> rgbGridHeadingColorCode = EColorUtils.hex2Rgb(gridHeadingHexColorCode);
		String courseNameHexColorCode = "#134f5c";
		List<Integer> rgbCourseNameColorCode = EColorUtils.hex2Rgb(courseNameHexColorCode);

		generateBackgroundImage(document, documentLayoutSetup, ImageProvider.INSTANCE.getImage(
						ImageProvider._10120_REPORT_CARD_BG), bgImageWidth, bgImageHeight, 0f, 0f);

//		addBlankLine(examReportCardLayoutData, false, 1);
		generateHeader(examReportCardLayoutData, studentLite, institute, reportType, 40f,
				examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.85f - 10f,
				ImageProvider.INSTANCE.getImage(ImageProvider._CBSE_LOGO), nunitoRegular, nunitoBold, bookmanBold);
//		addBlankLine(examReportCardLayoutData, false, 1);
		generateStudentInformation(examReportCardLayoutData, studentLite, examReportData, nunitoRegular, nunitoBold, false);
		addBlankLine(examReportCardLayoutData, false, 1);
		generateCustomScholasticMarksGrid(examReportCardLayoutData, examReportData, reportType, false,
				"Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType),
				rgbGridHeadingColorCode, rgbCourseNameColorCode, nunitoRegular, nunitoBold);
//		addBlankLine(examReportCardLayoutData, false, 1);
		generateScholasticExamDescription(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
				examReportData, nunitoRegular, nunitoBold, bookmanBold);
//		addBlankLine(examReportCardLayoutData, false, 1);
		generateCoScholasticMarksGrid(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 2,
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(), "Co-Scholastic Subjects",
				getCoScholasticMarksGridSubjectWidth(reportType), rgbGridHeadingColorCode, rgbCourseNameColorCode,
				nunitoRegular, nunitoBold);
		addBlankLine(examReportCardLayoutData, false, 1);
		generateResultSummary(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
				examReportCardLayoutData.getContentFontSize(), examReportData, reportType, nunitoRegular, nunitoBold, bookmanBold);
		generateRemarksSection(examReportCardLayoutData, examReportData, nunitoRegular, nunitoBold, bookmanBold);
		generateSignatureBox(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
				examReportCardLayoutData.getContentFontSize(), examReportCardLayoutData.getDefaultBorderWidth(), pageNumber,
				nunitoRegular, nunitoBold, bookmanBold);
	}

	protected void generateScholasticExamDescription(Document document, DocumentLayoutSetup documentLayoutSetup,
													 float contentFontSize, ExamReportData examReportData,
													 PdfFont nunitoRegular, PdfFont nunitoBold, PdfFont bookmanBold) throws IOException {

		Text descTitle = new Text("Description: " + SCHOLASTIC_EXAM_DESCRIPTION_6th_TO_8th)
				.setFont(nunitoBold).setFontSize(contentFontSize - 4);

//		Text descText = new Text(SCHOLASTIC_EXAM_DESCRIPTION_6th_TO_8th).setFont(nunitoRegular)
//				.setFontSize(contentFontSize - 4);
		Paragraph desc = new Paragraph();
		desc.add(descTitle).setFontColor(Color.convertRgbToCmyk(new DeviceRgb(194,123,160)));
		document.add(desc);
	}

	private void generateCustomScholasticMarksGrid(ExamReportCardLayoutData examReportCardLayoutData,
			ExamReportData examReportData, String reportType, boolean addTotalRow, String subjectColumnTitle,
			float subjectColumnWidth, List<Integer> rgbGridHeadingColorCode, List<Integer> rgbCourseNameColorCode,
												   PdfFont nunitoRegular, PdfFont nunitoBold) throws IOException {
		Set<UUID> nonAdditionalSubjects = getNonAdditionalSubjects(examReportData);
		generateScholasticMarksGrid(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 2,
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(), subjectColumnTitle,
				subjectColumnWidth, nonAdditionalSubjects, null, rgbGridHeadingColorCode, rgbCourseNameColorCode,
				nunitoRegular, nunitoBold);

	}

}
