/**
 * 
 */
package com.lernen.cloud.pdf.certificates.study;

import com.embrate.cloud.core.lib.utility.AssetProvider;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 *
 */
public class StudyCertificateGeneratorFactory {

	private static final Map<Integer, StudyCertificateGenerator> STUDY_CERTIFICATE_GENERATOR = new HashMap<>();
	private final AssetProvider assetProvider;

	public  StudyCertificateGeneratorFactory(AssetProvider assetProvider) {
		this.assetProvider = assetProvider;
		initializeGenerators();
	}

	private void initializeGenerators(){
		STUDY_CERTIFICATE_GENERATOR.put(10005, new StudyCertificateGenerator10005_10006(assetProvider));
		STUDY_CERTIFICATE_GENERATOR.put(10006, new StudyCertificateGenerator10005_10006(assetProvider));
		STUDY_CERTIFICATE_GENERATOR.put(10050, new StudyCertificateGenerator_10050_10051(assetProvider));
		STUDY_CERTIFICATE_GENERATOR.put(10051, new StudyCertificateGenerator_10050_10051(assetProvider));
		STUDY_CERTIFICATE_GENERATOR.put(10071, new StudyCertificateGenerator_10070_10071(assetProvider));
		STUDY_CERTIFICATE_GENERATOR.put(10070, new StudyCertificateGenerator_10070_10071(assetProvider));
		STUDY_CERTIFICATE_GENERATOR.put(10085, new StudyCertificateGenerator10085(assetProvider));
		STUDY_CERTIFICATE_GENERATOR.put(10130, new StudyCertificateGenerator10130(assetProvider));
		STUDY_CERTIFICATE_GENERATOR.put(10190, new StudyCertificateGenerator10190(assetProvider));
		STUDY_CERTIFICATE_GENERATOR.put(10295, new StudyCertificateGenerator10295(assetProvider));
	}

	public StudyCertificateGenerator getStudyCertificateGenerator(int instituteId) {
		if (!STUDY_CERTIFICATE_GENERATOR.containsKey(instituteId)) {
			return new GlobalStudyCertificateGenerator(assetProvider);
		}
		return STUDY_CERTIFICATE_GENERATOR.get(instituteId);
	}

}
