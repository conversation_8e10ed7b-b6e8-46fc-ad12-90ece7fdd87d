/**
 *
 */
package com.lernen.cloud.pdf.exam.reports._10105;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.report.*;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.StringHelper;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.core.utils.images.WatermarkProvider;
import com.lernen.cloud.pdf.exam.reports.ExamReportGenerator;
import com.lernen.cloud.pdf.exam.reports.ExamReportGenerator701;
import com.lernen.cloud.pdf.exam.reports.IExamReportCardGenerator;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.Map.Entry;

/**
 * <AUTHOR>
 *
 */
public class ExamReportGenerator10105v2 extends ExamReportGenerator implements IExamReportCardGenerator {

	public ExamReportGenerator10105v2(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(ExamReportGenerator701.class);
	public static final float DEFAULT_PAGE_SIDE_MARGIN = 25f;
	public static final float DEFAULT_PAGE_TOP_MARGIN = 10f;
	public static final float DEFAULT_PAGE_BOTTOM_MARGIN = 10f;
	public static final float SQUARE_BORDER_MARGIN = 12f;
	public static final float LOGO_WIDTH = 100f;
	public static final float LOGO_HEIGHT = 100f;
	public static final float DEFAULT_TABLE_BORDER_WIDTH = 0.5f;

	private static final Map<String, String> MARKS_GRADE_MAP = new LinkedHashMap<>();

	private static final  String GRADE_A_PLUS = "Outstanding performance and has extraordinary thinking.";
	private static final  String GRADE_A = "Excellent effort, follow deadlines and maintain decency.";
	private static final  String GRADE_B_PLUS = "Gracefully takes the task and has tendency to do better.";
	private static final  String GRADE_B = "Well behaved, has the capacity to work hard in order to reach heights.";
	private static final  String GRADE_C_PLUS = "Well behaved, has the capacity to work hard in order to reach heights.";
	private static final  String GRADE_C = "Average performance but innovative.";
	private static final  String GRADE_D = "Needs to get serious towards studies and maintain sense of humour.";
	private static final  String GRADE_E = "Needs to be very attentive and work hard in order to get promoted.";
	private static final  String SCHOOL_NAME = "Mother's Pride Sec School";
	private static final  String SCHOOL_ADDRESS = "A-27, Bharadwaj Marg, Barkat Nagar, Tonk Phatak,";
	private static final  String SCHOOL_PHONE_NUMBER = "Jaipur - 302015, Phone : 0141 - 2595472, Mob. : 7014465074";
	private static final  String SCHOOL_EMAIL = "Email : <EMAIL>";
	private static final  String SCHOOL_URL = "www.mothersprideschool.co.in";



//	private static final  String FINAL_TERM_ACADEMIC_PERFORMAMCE = "";

	static {
		MARKS_GRADE_MAP.put("91 to 100", "A+");
		MARKS_GRADE_MAP.put("81 to 90", "A");
		MARKS_GRADE_MAP.put("71 to 80", "B+");
		MARKS_GRADE_MAP.put("61 to 70", "B");
		MARKS_GRADE_MAP.put("51 to 60", "C+");
		MARKS_GRADE_MAP.put("41 to 50", "C");
		MARKS_GRADE_MAP.put("33 to 40", "D");
		MARKS_GRADE_MAP.put("Below 33", "E");
	}

	@Override
	public DocumentOutput generateReport(Institute institute, Student student, String reportType,
										 ExamReportData examReportData, String documentName, StudentManager studentManager) {
		try {

			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false);
			float contentFontSize = 11f;
			float defaultBorderWidth = DEFAULT_TABLE_BORDER_WIDTH;
			Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

			ExamReportCardLayoutData examReportCardLayoutData = new ExamReportCardLayoutData(document, documentLayoutSetup, null, null, contentFontSize,
					defaultBorderWidth, LOGO_WIDTH, LOGO_HEIGHT, LogoProvider.INSTANCE.getLogo(institute.getInstituteId()));
			generateStudentReport(institute, document, documentLayoutSetup, examReportData.getStudentLite(), reportType, examReportData,
					examReportCardLayoutData, 1, defaultBorderWidth, reportType, contentFontSize);

			examReportCardLayoutData.getDocument().close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Exception while generating report card institute {}, student {}, reportType {}", institute,
					student.getStudentId(), reportType, e);
		}
		return null;
	}

	private void generateStudentReport(Institute institute, Document document, DocumentLayoutSetup documentLayoutSetup,
									   StudentLite studentLite, String documentName, ExamReportData examReportData,
									   ExamReportCardLayoutData examReportCardLayoutData, int pageNumber, float defaultBorderWidth, String reportType,
									   float contentFontSize) throws IOException {
		float watermarkImageHeightWidth = 400f;
		Table table = getPDFTable(documentLayoutSetup, 1);
		document.add(table);
		PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);
		Color backgroundColor1 = new DeviceRgb(250, 238, 223);
		canvas.saveState()
				.setFillColor(backgroundColor1)
				.rectangle(0, 0, documentLayoutSetup.getPageSize().getWidth(), documentLayoutSetup.getPageSize().getHeight())
				.fill()
				.restoreState();
		Color backgroundColor = new DeviceRgb(255, 251, 246);
		canvas.saveState()
				.setFillColor(backgroundColor)
				.rectangle(0, 0, documentLayoutSetup.getPageSize().getWidth(), documentLayoutSetup.getPageSize().getHeight()-135f)
				.fill()
				.restoreState();

		generateWatermark(examReportCardLayoutData, institute.getInstituteId(), watermarkImageHeightWidth, watermarkImageHeightWidth, watermarkImageHeightWidth / 5,
				watermarkImageHeightWidth / 2);
		generateDynamicImageProvider(examReportCardLayoutData, 5f, documentLayoutSetup.getPageSize().getHeight() - 123f, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);
		generateHeader(document, documentLayoutSetup, studentLite, institute, reportType, contentFontSize);

		generateStudentInformation(document, documentLayoutSetup, contentFontSize, studentLite, examReportData);
		addBlankLine(document, false, 1);

		if (examReportData.getCourseTypeExamReportMarksGrid().containsKey(CourseType.SCHOLASTIC)) {
			generateCustomScholasticMarksGrid(examReportCardLayoutData, examReportData, GridConfigs.forSkipTotalRow(),
					"Subjects", 0.20f);

			generateScholasticResultSummary(document, documentLayoutSetup, contentFontSize, examReportData, reportType);

			addBlankLine(document, false, 1);
		}

		if (examReportData.getCourseTypeExamReportMarksGrid().containsKey(CourseType.COSCHOLASTIC)) {
			generateCoScholasticMarksGrid(examReportCardLayoutData.getDocument(),
					examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
					examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(),
					"Co-Scholastic Subjects", 0.30f, "Ab");
		}

		generateResultSummary(document, documentLayoutSetup, contentFontSize, examReportData, reportType, defaultBorderWidth);

		generateSignatureBox(document, documentLayoutSetup, contentFontSize, defaultBorderWidth, pageNumber);

		canvas.rectangle(SQUARE_BORDER_MARGIN - 2, SQUARE_BORDER_MARGIN - 2,
				documentLayoutSetup.getPageSize().getWidth() - (SQUARE_BORDER_MARGIN - 2) * 2,
				documentLayoutSetup.getPageSize().getHeight() - (SQUARE_BORDER_MARGIN - 2) * 2);
		canvas.stroke();
		canvas.setLineWidth(1f);
		canvas.rectangle(SQUARE_BORDER_MARGIN, SQUARE_BORDER_MARGIN,
				documentLayoutSetup.getPageSize().getWidth() - SQUARE_BORDER_MARGIN * 2,
				documentLayoutSetup.getPageSize().getHeight() - SQUARE_BORDER_MARGIN * 2);
		canvas.stroke();
		canvas.setLineWidth(1f);

	}

	private void generateCustomScholasticMarksGrid(ExamReportCardLayoutData examReportCardLayoutData,
												   ExamReportData examReportData, GridConfigs gridConfigs, String subjectColumnTitle,
												   float subjectColumnWidth) throws IOException {
		Set<UUID> nonAdditionalSubjects = getNonAdditionalSubjets(examReportData);

		generateScholasticMarksGrid(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
				getRegularFont(), getRegularBoldFont(), examReportCardLayoutData.getContentFontSize() - 1,
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, gridConfigs, "Subjects", subjectColumnWidth,
				nonAdditionalSubjects, "Max. Marks", "Marks", null, "Ab");
	}

	protected Set<UUID> getNonAdditionalSubjets(ExamReportData examReportData) {
		Set<UUID> nonAdditionalSubjects = new HashSet<UUID>();
		for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportData.getCourseTypeExamReportMarksGrid()
				.get(CourseType.SCHOLASTIC).getExamReportCourseMarksRows()) {
			if (!isAdditionalSubject(examReportCourseMarksRow.getCourse(), examReportData.getExamReportStructure())) {
				nonAdditionalSubjects.add(examReportCourseMarksRow.getCourse().getCourseId());
			}
		}
		return nonAdditionalSubjects;
	}

	protected void generateAdditionalCustomScholasticMarksGrid(ExamReportCardLayoutData examReportCardLayoutData,
															   ExamReportData examReportData, boolean addTotalRow, String subjectColumnTitle,
															   float subjectColumnWidth) throws IOException {
		ExamReportStructure examReportStructure = examReportData.getExamReportStructure();
		if (examReportStructure == null || CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure())
				|| examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC) == null
				|| CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC)
				.getAdditionalCourses())) {
			return;
		}
		generateScholasticMarksGrid(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
				getRegularFont(), getRegularBoldFont(), examReportCardLayoutData.getContentFontSize(),
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(), subjectColumnTitle, subjectColumnWidth,
				examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC).getAdditionalCourses(),
				"Max. Marks", "Obtained Grade", null);
	}

	@Override
	public DocumentLayoutSetup initDocumentLayoutSetup(boolean officeCopy) {
		return initDocumentLayoutSetup(officeCopy, officeCopy ? DEFAULT_PAGE_SIZE.rotate() : DEFAULT_PAGE_SIZE,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN : DEFAULT_PAGE_TOP_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN : DEFAULT_PAGE_BOTTOM_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_SIDE_MARGIN : DEFAULT_PAGE_SIDE_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_MIDDLE_MARGIN : 0f);
	}

	private void generateHeader(Document document, DocumentLayoutSetup documentLayoutSetup, StudentLite student,
								Institute institute, String reportType, float contentFontSize) throws IOException {

		int singleContentColumn = 1;

		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(14f).setTextAlignment(TextAlignment.CENTER);
		addBlankLine(document, false, 1);

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(SCHOOL_NAME.toUpperCase())),
				cellLayoutSetup.copy().setFontSize(25f));
		document.add(table);


		table = getPDFTable(documentLayoutSetup, singleContentColumn);

		cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setFontSize(contentFontSize).setTextAlignment(TextAlignment.CENTER)
				.setPaddingBottom(0f).setPaddingTop(0f).setPdfFont(getRegularFont());

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(SCHOOL_ADDRESS)),
				cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(SCHOOL_PHONE_NUMBER)),
				cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(SCHOOL_EMAIL)),
				cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(SCHOOL_URL)),
				cellLayoutSetup);
		document.add(table);

		addBlankLine(document, false, 1);

		cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(14f).setTextAlignment(TextAlignment.CENTER);
		table = getPDFTable(documentLayoutSetup, singleContentColumn);


		String examName = "";
		switch (reportType) {
			case "TERM_I":
				examName = "TERM-I";
				break;
			case "TERM_II":
				examName = "TERM-II";
				break;
			case "UNIT_I":
				examName = "UNIT-I";
				break;
			case "UNIT_II":
				examName = "UNIT-II";
				break;
			case "HALF_YEARLY":
				examName = "HALF-YEARLY";
				break;
			case "ANNUAL":
				examName = "ANNUAL";
				break;
		}
		String headerExamTitle = examName + " REPORT CARD ";
		headerExamTitle += "(" + student.getStudentSessionData().getShortYearDisplayName() + ")";
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(headerExamTitle).setUnderline()),
				cellLayoutSetup);
		document.add(table);
		addBlankLine(document, false, 1);
	}

	private void generateStudentInformation (Document document, DocumentLayoutSetup documentLayoutSetup,
											 float contentFontSize, StudentLite student, ExamReportData examReportData) throws IOException {
		Table table = getPDFTable(documentLayoutSetup, new float[]{0.55f, 0.05f, 0.40f});
		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize);
		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);

		Paragraph studentName = getKeyValueParagraph("Name of Student : ", StringHelper.capitalizeFirstOfAllWords(student.getName()));
		String attendanceStr = "";
        if (examReportData.getTotalAttendedDays() != null && (examReportData.getTotalWorkingDays() != null && examReportData.getTotalWorkingDays() != 0)) {
            attendanceStr = examReportData.getTotalAttendedDays() + "/" + examReportData.getTotalWorkingDays();
        }

        Paragraph noOfPresent = getKeyValueParagraph("Attendance : ", attendanceStr);
		Paragraph dob = getKeyValueParagraph("Date of Birth : ",
				student.getDateOfBirth() == null
						|| student.getDateOfBirth() <= 0 ? ""
						: DateUtils.getFormattedDate(student.getDateOfBirth(),
						DATE_FORMAT_NUMBER, User.DFAULT_TIMEZONE));
		Paragraph admissionNumber = getKeyValueParagraph("Scholar No. ",
				student.getAdmissionNumber());
		Paragraph fatherName = getKeyValueParagraph("Father's Name : ", StringHelper.capitalizeFirstOfAllWords(student.getFathersName()));
		Paragraph motherName = getKeyValueParagraph("Mother's Name : ", StringHelper.capitalizeFirstOfAllWords(student.getMothersName()));
		Paragraph classValue = getKeyValueParagraph("Class : ",
				student.getStudentSessionData().getStandardNameWithSection());
		Paragraph rollNumber=getKeyValueParagraph("Roll No. ", student.getStudentSessionData().getRollNumber());
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(studentName, firstCellLayoutSetup),
						new CellData(new Paragraph(""), secondCellLayoutSetup),
						new CellData(admissionNumber, thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(fatherName, firstCellLayoutSetup),

						new CellData(new Paragraph(""), secondCellLayoutSetup),
						new CellData(classValue, thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(motherName, firstCellLayoutSetup),
						new CellData(new Paragraph(""), secondCellLayoutSetup),
						new CellData(rollNumber, thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(dob, firstCellLayoutSetup),
						new CellData(new Paragraph(""), secondCellLayoutSetup),
						new CellData(noOfPresent, thirdCellLayoutSetup)));
		document.add(table);
	}

	private void generateResultSummary (Document document, DocumentLayoutSetup documentLayoutSetup,
										float contentFontSize, ExamReportData examReportData, String reportType, float defaultBorderWidth) throws IOException {
		Table table = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.LEFT);
		addBlankLine(document, false, 1);

		Paragraph grade = getKeyValueParagraph("Grade : ",
				examReportData.getTotalGrade() == null ? "" : examReportData.getTotalGrade().getGradeName());

		String resultAndPromoted = examReportData.getExamResultStatus() == null ? ""
				: examReportData.getExamResultStatus().name();

		if(examReportData.getExamResultStatus() == ExamResultStatus.PASS || examReportData.getExamResultStatus() == ExamResultStatus.PASS_WITH_GRACE) {
			resultAndPromoted = "PASSED & PROMOTED TO CLASS "  + (examReportData.getPromotedTo() == null ?
					"" : examReportData.getPromotedTo().getStandardName());
		}

		Paragraph result = getKeyValueParagraph("Result: ", resultAndPromoted);

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(grade, cellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(result, cellLayoutSetup.copy().setFontSize(contentFontSize + 2))));

		document.add(table);

		Table remarksTable = getPDFTable(documentLayoutSetup, new float[] {0.12f, 0.88f});

		addRow(remarksTable, documentLayoutSetup, Arrays.asList(getParagraph("Remarks: "),
				new Paragraph(
						examReportData.getTotalGrade() == null ? EMPTY_TEXT :
								StringUtils.isEmpty(getRemarks(examReportData.getTotalGrade().getGradeName()))
										? EMPTY_TEXT : getRemarks(examReportData.getTotalGrade().getGradeName())
				).setBorderBottom(new SolidBorder(defaultBorderWidth))),
				cellLayoutSetup.copy().setFontSize(contentFontSize + 2));
		document.add(remarksTable);
		
		Paragraph resultDate = getKeyValueParagraph("Result Date: ",
				examReportData.getDateOfResultDeclaration() == null
						|| examReportData.getDateOfResultDeclaration() <= 0 ? ""
						: DateUtils.getFormattedDate(examReportData.getDateOfResultDeclaration(),
						DATE_FORMAT_NUMBER, User.DFAULT_TIMEZONE));

		Table resultTable = getPDFTable(documentLayoutSetup, 1);

		addRow(resultTable, documentLayoutSetup, Arrays.asList(new CellData(resultDate, cellLayoutSetup)));

		document.add(resultTable);

	}

	private void generateScholasticResultSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
										float contentFontSize, ExamReportData examReportData, String reportType) throws IOException {
		Table table = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.RIGHT);
		addBlankLine(document, false, 1);

		Paragraph grandTotal = getKeyValueParagraph("Grand Total : ",
				examReportData.getTotalObtainedMarks() == null ? "-"
						: examReportData.getTotalObtainedMarks() * 10 % 10 == 0
						? String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10)
						: String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10d));

		Paragraph percentage = getKeyValueParagraph("Percentage : ", examReportData.getPercentage() == null ? "-"
				: Math.round(examReportData.getPercentage() * 100) / 100d + "%");

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(grandTotal, cellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(percentage, cellLayoutSetup)));


		document.add(table);
	}

	private String getRemarks (String grade){
		switch (grade) {

			case "A+":
				return GRADE_A_PLUS;
			case "A":
				return GRADE_A;
			case "B+":
				return GRADE_B_PLUS;
			case "B":
				return GRADE_B;
			case "C+":
				return GRADE_C_PLUS;
			case "C":
				return GRADE_C;
			case "D":
				return GRADE_D;
			case "E":
				return GRADE_E;
			default:
				return null;

		}
	}

	private void generateSignatureBox (Document document, DocumentLayoutSetup documentLayoutSetup,
									   float contentFontSize,
									   float defaultBorderWidth, int pageNumber)
			throws IOException {
		PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);
		canvas.moveTo(12, 145);
		canvas.lineTo(583, 145);
		canvas.setLineWidth(.5f);
		canvas.closePathStroke();
		int singleContentColumn = 3;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);
		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.CENTER);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Class Teacher"), getParagraph("Exam Controller"),
				getParagraph("Director")), signatureCellLayoutSetup);
		table.setFixedPosition(30f, 145f, documentLayoutSetup.getPageSize().getWidth() - 100f);
		document.add(table);
		generateGradeBox(document, documentLayoutSetup, contentFontSize, defaultBorderWidth);
	}

	private void generateGradeBox (Document document, DocumentLayoutSetup documentLayoutSetup,float contentFontSize,
								   float defaultBorderWidth) throws IOException {
		addBlankLine(document, true, 1);

		Table table = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.CENTER);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Marks Range")), signatureCellLayoutSetup);
		table.setFixedPosition(10f, 117f, documentLayoutSetup.getPageSize().getWidth());
		document.add(table);

		float[] columnWidths = new float[]{0.24f, 0.24f, 0.24f, 0.24f};
		Table headerTable = getPDFTable(documentLayoutSetup, columnWidths);
		CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
		marksCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize - 2)
				.setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);
		addBlankLine(document, false, 1);
		List<CellData> headerList = new ArrayList<>();
		headerList.add(new CellData("Marks", marksCellLayoutSetup));
		headerList.add(new CellData("Grade", marksCellLayoutSetup));
		headerList.add(new CellData("Marks", marksCellLayoutSetup));
		headerList.add(new CellData("Grade", marksCellLayoutSetup));
		addRow(headerTable, documentLayoutSetup, headerList);
		List<Entry<String, String>> gradesList = new ArrayList<>(MARKS_GRADE_MAP.entrySet());
		for (int index = 0; index < gradesList.size() / 2; index++) {
			List<CellData> row = new ArrayList<>();
			row.add(new CellData(gradesList.get(index).getKey(), marksCellLayoutSetup));
			row.add(new CellData(gradesList.get(index).getValue(), marksCellLayoutSetup));
			row.add(new CellData(gradesList.get(gradesList.size() / 2 + index).getKey(), marksCellLayoutSetup));
			row.add(new CellData(gradesList.get(gradesList.size() / 2 + index).getValue(), marksCellLayoutSetup));
			addRow(headerTable, documentLayoutSetup, row);
		}
		headerTable.setFixedPosition(35f, 20f, documentLayoutSetup.getPageSize().getWidth() - 100f);
		document.add(headerTable);
	}

	@Override
	public DocumentOutput generateClassReport (Institute institute, String reportType,
											   List < ExamReportData > examReportDataList, String documentName, StudentManager studentManager){
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false);
			float contentFontSize = 11f;
			float defaultBorderWidth = DEFAULT_TABLE_BORDER_WIDTH;
			Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

			ExamReportCardLayoutData examReportCardLayoutData = new ExamReportCardLayoutData(document, documentLayoutSetup,
					null, null, contentFontSize, defaultBorderWidth,
					LOGO_WIDTH, LOGO_HEIGHT, LogoProvider.INSTANCE.getLogo(institute.getInstituteId()));

			sortClassExamReports(examReportDataList);

			int pageNumber = 1;
			for (ExamReportData examReportData : examReportDataList) {

				generateStudentReport(institute, document, documentLayoutSetup, examReportData.getStudentLite(),
						reportType, examReportData, examReportCardLayoutData, pageNumber, defaultBorderWidth, reportType, contentFontSize);

				if (pageNumber != examReportDataList.size()) {
					examReportCardLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				pageNumber++;

			}

			examReportCardLayoutData.getDocument().close();

			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
					reportType, e);
		}
		return null;
	}

	protected void sortClassExamReports (List < ExamReportData > examReportDataList) {
		Collections.sort(examReportDataList, new Comparator<ExamReportData>() {

			@Override
			public int compare(ExamReportData e1, ExamReportData e2) {
				StandardSections section1 = e1.getStudentLite().getStudentSessionData().getStandardSection();

				StandardSections section2 = e2.getStudentLite().getStudentSessionData().getStandardSection();

				if (section1 != null && section2 != null) {
					int sectionCompare = section1.getSectionName().compareToIgnoreCase(section2.getSectionName());
					if (sectionCompare != 0) {
						return sectionCompare;
					}
				}

				return e1.getStudentLite().getName().compareToIgnoreCase(e2.getStudentLite().getName());
			}
		});
	}

	@Override
	public DocumentOutput generateClassResultReport (Institute institute, String
			reportType, List < ExamReportData > examReportDataList,
													 String documentName, StudentManager studentManager,int studentPerPage){
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4);
			Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

			float logoWidth = 75f;
			float logoHeight = 75f;
			PdfFont boldFont = getRegularBoldFont();
			PdfFont regularFont = getRegularFont();
			int instituteId = institute.getInstituteId();
			ExamReportCardLayoutData examReportCardLayoutData = new ExamReportCardLayoutData(document, documentLayoutSetup, null, null, DEFAULT_FONT_SIZE,
					DEFAULT_BORDER_WIDTH, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId));

			CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
			marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(DEFAULT_FONT_SIZE)
					.setBorder(new SolidBorder(DEFAULT_BORDER_WIDTH)).setTextAlignment(TextAlignment.CENTER);

			CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
					.setTextAlignment(TextAlignment.LEFT);

			String instituteName = institute.getInstituteName().toUpperCase();
			String sessionName = "";
			if (!CollectionUtils.isEmpty(examReportDataList)) {
				sessionName = "Session : " + examReportDataList.get(0).getStudentLite().getStudentSessionData().getShortYearDisplayName();
			}
			generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName,
					sessionName, marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));

			sortClassExamReports(examReportDataList);
			int pageNumber = 1;
			for (ExamReportData examReportData : examReportDataList) {
				generateClassReportDataStudentDetails(documentLayoutSetup, document, courseCellLayoutSetup, examReportData.getStudentLite());
				generateCustomScholasticMarksGrid(examReportCardLayoutData, examReportData, GridConfigs.forOnlyObtainedTotalRow(),
						"Subjects", 0.20f);
				generateClassReportDataStudentResult(documentLayoutSetup, document, examReportData, courseCellLayoutSetup);
				if (pageNumber % studentPerPage == 0) {
					document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
					generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName, sessionName,
							marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));
				} else {
					addBlankLine(document, false, 1);
				}
				pageNumber++;
			}
			document.close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
					reportType, e);
		}
		return null;
	}

}
