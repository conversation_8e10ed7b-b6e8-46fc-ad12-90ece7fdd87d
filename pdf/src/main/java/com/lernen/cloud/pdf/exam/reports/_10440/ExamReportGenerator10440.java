package com.lernen.cloud.pdf.exam.reports._10440;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.WebColors;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.action.PdfAction;
import com.itextpdf.kernel.pdf.annot.PdfLinkAnnotation;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.*;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.ExamGrade;
import com.lernen.cloud.core.api.examination.report.ExamReportCardLayoutData;
import com.lernen.cloud.core.api.examination.report.ExamReportCourseMarksRow;
import com.lernen.cloud.core.api.examination.report.ExamReportData;
import com.lernen.cloud.core.api.examination.report.ExamReportStructure;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.pdf.exam.reports.ExamReportGenerator;
import com.lernen.cloud.pdf.exam.reports.IExamReportCardGenerator;
import com.lernen.cloud.pdf.exam.reports._10400.ExamReportGenerator10400;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.List;

public class ExamReportGenerator10440 extends ExamReportGenerator implements IExamReportCardGenerator {

    public ExamReportGenerator10440(AssetProvider assetProvider) {
        super(assetProvider);
        //TODO Auto-generated constructor stub
    }

    private static final Logger logger = LogManager.getLogger(ExamReportGenerator10440.class);
    public static final float DEFAULT_PAGE_SIDE_MARGIN = 25f;
    public static final float DEFAULT_PAGE_TOP_MARGIN = 10f;
    public static final float DEFAULT_PAGE_BOTTOM_MARGIN = 10f;
    public static final float SQUARE_BORDER_MARGIN = 12f;

    protected static final String TERM_I_REPORT_TYPE = "TERM_I";
    protected static final String TERM_II_REPORT_TYPE = "TERM_II";
    protected static final String ANNUAL_REPORT_TYPE = "ANNUAL";
    protected static final String PRE_BOARD_I = "PRE_BOARD_I";
    protected static final String PRE_BOARD_II = "PRE_BOARD_II";

    protected static final String SCHOLASTIC_EXAM_DESCRIPTION = "PT = Periodic Test, NB = Notebook, SE = Subject Enrichment, HY = Half Yearly, YL = Yearly or Annual Exam";
    protected static final String PRINCIPAL_UUID = "d3cb332c-3501-4ee9-861a-eb21c32e5a8b";
    protected static final String EXAM_INCHARGE_UUID = "edbabc72-4bba-4707-b152-66735c3722a8";

    @Override
    public DocumentOutput generateReport(Institute institute, Student student, String reportType,
                                         ExamReportData examReportData, String documentName, StudentManager studentManager) {
        try {

            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

            ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);
            PdfFont regularFont = getCambriaFont();
            PdfFont boldFont = getCambriaBoldFont();

            generateStudentReportCard(examReportCardLayoutData, institute, examReportData, studentManager, reportType,
                    1, boldFont, regularFont);

            examReportCardLayoutData.getDocument().close();
            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating report card institute {}, student {}, reportType {} ",
                    institute.getInstituteId(), student.getStudentId(), reportType, e);
        }
        return null;
    }

    private void generateStudentReportCard(ExamReportCardLayoutData examReportCardLayoutData,
                                           Institute institute, ExamReportData examReportData,
                                           StudentManager studentManager, String reportType,
                                           int pageNumber, PdfFont boldFont, PdfFont regularFont) throws IOException {

        generateMetaDataLayout(examReportCardLayoutData, institute, examReportData.getStudentLite(), studentManager, reportType,
                examReportData, pageNumber, boldFont, regularFont);


        Set<UUID> nonAdditionalSubjects = getNonAdditionalSubjects(examReportData);
        generateScholasticMarksGrid(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
                getRegularFont(), getRegularBoldFont(), examReportCardLayoutData.getContentFontSize() - 1,
                examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forOnlyObtainedTotalRow(),
                "Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType),
                nonAdditionalSubjects, "MM", "MO", null,
                "-");

        if(!CollectionUtils.isEmpty(examReportData.getExamReportStructure().getExamReportCourseStructure()
                .get(CourseType.SCHOLASTIC).getAdditionalCourses())) {
            addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
            generateAdditionalCustomScholasticMarksGrid(examReportCardLayoutData, examReportData, GridConfigs.forOnlyObtainedTotalRow(),
                    "Additional Subjects", getScholasticMarksGridSubjectWidth(reportType));
        }
        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
        if (examReportData.getCourseTypeExamReportMarksGrid().containsKey(CourseType.COSCHOLASTIC)) {
            generateCoScholasticMarksGrid(examReportCardLayoutData.getDocument(),
                    examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
                    examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(),
                    "Co-Scholastic Subjects", getCoScholasticMarksGridSubjectWidth(reportType), "-");
        }

        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
        generateResultSummary(institute, examReportCardLayoutData.getDocument(),
                examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
                examReportData, reportType, boldFont, regularFont);


        generateRemarksSection(examReportCardLayoutData, examReportData, boldFont, regularFont);

        generateSignatureBox(institute, examReportCardLayoutData.getDocument(),
                examReportCardLayoutData.getDocumentLayoutSetup(), examReportData, examReportCardLayoutData.getContentFontSize(),
                examReportCardLayoutData.getDefaultBorderWidth(), pageNumber, boldFont, regularFont, reportType, studentManager);
    }

    protected void generateAdditionalCustomScholasticMarksGrid(ExamReportCardLayoutData examReportCardLayoutData,
                                                               ExamReportData examReportData, GridConfigs gridConfigs, String subjectColumnTitle, float subjectColumnWidth) throws IOException {
        ExamReportStructure examReportStructure = examReportData.getExamReportStructure();
        if (examReportStructure == null || CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure())
                || examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC) == null
                || CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC)
                .getAdditionalCourses())) {
            return;
        }

        generateScholasticMarksGrid(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
                getRegularFont(), getRegularBoldFont(), examReportCardLayoutData.getContentFontSize() - 1,
                examReportCardLayoutData.getDefaultBorderWidth(), examReportData, gridConfigs,
                subjectColumnTitle, subjectColumnWidth,
                examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC).getAdditionalCourses(),
                "MM", "MO", null, "-");
    }

    protected float getScholasticMarksGridSubjectWidth(String reportType) {
        return 0.2f;
    }

    protected float getCoScholasticMarksGridSubjectWidth(String reportType) {
        if (reportType.equalsIgnoreCase(TERM_I_REPORT_TYPE)) {
            return 0.5f;
        }
        if (reportType.equalsIgnoreCase(TERM_II_REPORT_TYPE)) {
            return 0.5f;
        }
        if (reportType.equalsIgnoreCase(PRE_BOARD_I)) {
            return 0.5f;
        }
        if (reportType.equalsIgnoreCase(PRE_BOARD_II)) {
            return 0.5f;
        }
        if (reportType.equalsIgnoreCase(ANNUAL_REPORT_TYPE)) {
            return 0.4f;
        }
        return 0.3f;
    }

    protected ExamReportCardLayoutData generateReportCardLayoutData(Institute institute, DocumentOutput documentOutput)
            throws IOException {

        DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false);
        float contentFontSize = 11f;
        float defaultBorderWidth = 0.5f;
        Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
        float logoWidth = 75f;
        float logoHeight = 75f;

        int instituteId = institute.getInstituteId();
        return new ExamReportCardLayoutData(document, documentLayoutSetup, null, null, contentFontSize,
                defaultBorderWidth, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId));
    }

    protected void generateMetaDataLayout(ExamReportCardLayoutData examReportCardLayoutData, Institute institute,
                                          StudentLite studentLite, StudentManager studentManager, String reportType, ExamReportData examReportData,
                                          int pageNumber, PdfFont boldFont, PdfFont regularFont) throws IOException {

        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
        //Institute watermark
        int instituteId = institute.getInstituteId();
        float watermarkImageHeightWidth = 400f;
        generateWatermark(examReportCardLayoutData,
                instituteId, watermarkImageHeightWidth, watermarkImageHeightWidth, watermarkImageHeightWidth / 5 + 20,
                watermarkImageHeightWidth / 2);
        generateHeader(examReportCardLayoutData.getDocumentLayoutSetup(),
                examReportCardLayoutData, studentLite, institute, reportType, 40f,
                examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.87f, boldFont, regularFont);
        generateStudentInformation(examReportCardLayoutData, studentLite, examReportData, boldFont, regularFont);
        generateBorderLayout(examReportCardLayoutData, pageNumber);
    }

    @Override
    public DocumentLayoutSetup initDocumentLayoutSetup(boolean officeCopy) {
        return initDocumentLayoutSetup(officeCopy, officeCopy ? DEFAULT_PAGE_SIZE.rotate() : DEFAULT_PAGE_SIZE,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN : DEFAULT_PAGE_TOP_MARGIN,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN : DEFAULT_PAGE_BOTTOM_MARGIN,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_SIDE_MARGIN : DEFAULT_PAGE_SIDE_MARGIN,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_MIDDLE_MARGIN : 0f);
    }

    protected void generateHeader(DocumentLayoutSetup documentLayoutSetup, ExamReportCardLayoutData examReportCardLayoutData, StudentLite studentLite,
                                  Institute institute, String reportType, float offsetX, float offsetY,
                                  PdfFont boldFont, PdfFont regularFont) throws IOException {

        generateDynamicImageProvider(examReportCardLayoutData, offsetX - 10f, offsetY + 10f, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);
        float watermarkImageHeightWidth = 400f;
        //Institute watermark
        generateWatermark(examReportCardLayoutData, institute.getInstituteId(), watermarkImageHeightWidth, watermarkImageHeightWidth, watermarkImageHeightWidth / 5 + 20,
                watermarkImageHeightWidth / 2);
//        generateDynamicImageProvider(examReportCardLayoutData, documentLayoutSetup.getPageSize().getWidth() - (2.5f * offsetX) + 5f, offsetY - 7f, 1f, 1f, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_SECONDARY_LOGO);

        Table table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), 2);

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(examReportCardLayoutData.getRegularFont()).setFontSize(10f);

//        String heading1 = "Regd. No. 40/sikar/1993-94";
//        String heading2 = "Rec. No. DEO/SEC/S/REC/2008-09/205";
//
//        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(
//                new CellData(getParagraph(heading1),
//                        cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT).setPaddingLeft(10f)),
//                new CellData(getParagraph(heading2),
//                        cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT).setPaddingRight(10f))));
//
//        examReportCardLayoutData.getDocument().add(table);

        int singleContentColumn = 1;
        table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);

        cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(boldFont).setFontSize(examReportCardLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.CENTER);

//        Paragraph contactParagraph = new Paragraph("Contact No: - 9610166661, 8094770888").setFont(regularFont).setFontSize(examReportCardLayoutData.getContentFontSize() - 1).setMultipliedLeading(0.88f);
//
//        String url = "www.tagoreedu.in";
//        PdfLinkAnnotation linkAnnotation = new PdfLinkAnnotation(new Rectangle(0, 0, 0, 0));
//        linkAnnotation.setAction(PdfAction.createURI(url));
//        Link link = new Link("Website - "+ url +", Email Id - <EMAIL>" , linkAnnotation);
//        Paragraph EmailAndWebsite = new Paragraph(link).setFont(regularFont).setFontSize(examReportCardLayoutData.getContentFontSize() - 1).setMultipliedLeading(0.88f);

        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getInstituteName().toUpperCase(), EColorUtils.hex2Rgb("#3a3d92"))),
                cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() + 13));
//        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph("A Co-Educational RBSE English Medium School").setMultipliedLeading(0.88f)),
//                cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize()).setPdfFont(regularFont));
//        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph("Kishan Colony, Nawalgarh Road, Sikar, Rajasthan - 332001").setMultipliedLeading(0.88f)),
//                cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize()).setPdfFont(regularFont));
        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getLetterHeadLine1(), EColorUtils.hex2Rgb("#3a3d92")).setMultipliedLeading(0.85f)),
                cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() + 1f).setPdfFont(regularFont));
        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getLetterHeadLine2(), EColorUtils.hex2Rgb("#3a3d92")).setMultipliedLeading(0.85f)),
                cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() + 1f).setPdfFont(regularFont));
        examReportCardLayoutData.getDocument().add(table);
        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
        table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);
//		String headerExamTitle = reportType.equalsIgnoreCase(TERM_I_REPORT_TYPE) ? "TERM-1 EXAMINATION "
//				: reportType.equalsIgnoreCase(PRE_BOARD_I) ? "PRE BOARD I "
//				: reportType.equalsIgnoreCase(PRE_BOARD_II) ? "PRE BOARD II "
//				: reportType.equalsIgnoreCase(TERM_I_REPORT_TYPE) ? "TERM-2 EXAMINATION "
//				: "ANNUAL EXAMINATION ";
//		headerExamTitle += "(" + studentLite.getStudentSessionData().getShortYearDisplayName() + ")";
        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph("Report Card", EColorUtils.hex2Rgb("#3a3d92")).setMultipliedLeading(0.85f)),
                cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() + 3.5f).setPdfFont(boldFont));
        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph("Academic Session : " + studentLite.getStudentSessionData().getShortYearDisplayName(), EColorUtils.hex2Rgb("#3a3d92")).setMultipliedLeading(0.85f)),
                cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() + 1.5f).setPdfFont(boldFont));


        examReportCardLayoutData.getDocument().add(table);
        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
    }

    protected void generateStudentInformation(ExamReportCardLayoutData examReportCardLayoutData,
                                              StudentLite studentLite, ExamReportData examReportData,
                                              PdfFont boldFont, PdfFont regularFont) throws IOException {

        Document document = examReportCardLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
        float contentFontSize = examReportCardLayoutData.getContentFontSize() - 1;

        Table table = getPDFTable(documentLayoutSetup, new float[] { 0.5f, 0.15f, 0.35f });

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize);

        CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
        CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);
        CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);

        Paragraph studentName = getKeyValueParagraph("Student Name : ", studentLite.getName(), boldFont, boldFont);
        Paragraph fatherName = getKeyValueParagraph("Father Name : ", studentLite.getFathersName(), boldFont, boldFont);
        Paragraph dob = getKeyValueParagraph("DOB : ",
                studentLite.getDateOfBirth() == null || studentLite.getDateOfBirth() <= 0 ? ""
                        : DateUtils.getFormattedDate(studentLite.getDateOfBirth(), DATE_FORMAT, User.DFAULT_TIMEZONE),  boldFont, boldFont);
        Paragraph motherName = getKeyValueParagraph("Mother Name : ", studentLite.getMothersName(), boldFont, boldFont);
        Paragraph admissionNumber = getKeyValueParagraph("Admission No. : ", studentLite.getAdmissionNumber(), boldFont, boldFont);
        Paragraph classValue = getKeyValueParagraph("Class : ",
                studentLite.getStudentSessionData().getStandardNameWithSection(), boldFont, boldFont);
        Paragraph rollNumber = getKeyValueParagraph("Roll Number : ", studentLite.getStudentSessionData().getRollNumber(), boldFont, boldFont);
		/* Paragraph dateOfResultDeclaration = getKeyValueParagraph("Date Of Result Declaration : ",
				examReportData.getDateOfResultDeclaration() == null ? "-" : DateUtils.getFormattedDate(examReportData.getDateOfResultDeclaration()),
				boldFont, boldFont); */


        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(studentName, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
                        new CellData(fatherName, thirdCellLayoutSetup)));
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(dob, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
                        new CellData(motherName, thirdCellLayoutSetup)));
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(admissionNumber, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
                        new CellData(classValue, thirdCellLayoutSetup)));
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(rollNumber, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
                        new CellData(EMPTY_TEXT, thirdCellLayoutSetup)));

        document.add(table);

        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
    }

    protected void generateBorderLayout(ExamReportCardLayoutData examReportCardLayoutData, int pageNumber) {
        DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
        PdfCanvas canvas = new PdfCanvas(examReportCardLayoutData.getDocument().getPdfDocument(), pageNumber);
        Color color = WebColors.getRGBColor("#212121");
        canvas.setColor(color, false);
        canvas.setLineWidth(3f);
        canvas.rectangle(SQUARE_BORDER_MARGIN - 2, SQUARE_BORDER_MARGIN - 2,
                documentLayoutSetup.getPageSize().getWidth() - (SQUARE_BORDER_MARGIN - 2) * 2,
                documentLayoutSetup.getPageSize().getHeight() - (SQUARE_BORDER_MARGIN - 2) * 2);
        canvas.stroke();
        canvas.setLineWidth(1f);
        canvas.rectangle(SQUARE_BORDER_MARGIN, SQUARE_BORDER_MARGIN,
                documentLayoutSetup.getPageSize().getWidth() - SQUARE_BORDER_MARGIN * 2,
                documentLayoutSetup.getPageSize().getHeight() - SQUARE_BORDER_MARGIN * 2);
        canvas.stroke();
        canvas.setLineWidth(1f);
    }

    protected void generateScholasticExamDescription(Document document, DocumentLayoutSetup documentLayoutSetup,
                                                     float contentFontSize, ExamReportData examReportData, PdfFont boldFont, PdfFont regularFont, String descriptionText) throws IOException {
        Text descTitle = new Text("Description: " + descriptionText).setFontSize(contentFontSize - 4);
        Paragraph desc = new Paragraph();
        desc.add(descTitle);
        document.add(desc);
    }

    protected void generateResultSummary(Institute institute, Document document, DocumentLayoutSetup documentLayoutSetup,
                                         float contentFontSize, ExamReportData examReportData, String reportType, PdfFont boldFont, PdfFont regularFont) throws IOException {

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.LEFT).setPaddingTop(2f).setPaddingBottom(2f);

        Table table = getPDFTable(documentLayoutSetup, new float[] { 0.4f, 0.2f, 0.4f });

        Paragraph result = getKeyValueParagraph("Result : ", "-", boldFont);

        if (examReportData.getExamResultStatus() != null) {
            switch (examReportData.getExamResultStatus()) {
                case PASS:
                case COMPARTMENT:
                    result = getKeyValueParagraph("Result : ", examReportData.getExamResultStatus().getDisplayName(),
                            0, 0, 0, boldFont, boldFont);
                    break;
                case FAIL:
                    result = getKeyValueParagraph("Result : ", examReportData.getExamResultStatus().getDisplayName(),
                            0, 0, 0, boldFont, boldFont);
                    break;
            }
        }
//        Paragraph rank = null;
//        if (examReportData.getRank() != null && examReportData.getRank() > 0 && examReportData.getRank() <= 5) {
//            rank = getKeyValueParagraph("Rank : ", String.valueOf(examReportData.getRank()), boldFont, boldFont);
//        }
        Paragraph promotedClass = getKeyValueParagraph("Promoted to: ",
                examReportData.getPromotedTo() == null ? "-" : examReportData.getPromotedTo().getStandardName(), boldFont, boldFont);
        Paragraph obtainedMarks = getKeyValueParagraph("Obtained marks : ",
                examReportData.getTotalObtainedMarks() == null ? "-"
                        : examReportData.getTotalObtainedMarks() * 10 % 10 == 0
                        ? String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10)
                        : String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10d), boldFont, boldFont);
        Paragraph totalMarks = getKeyValueParagraph("Total marks : ", examReportData.getTotalMaxMarks() == null ? "-"
                : String.valueOf(Math.round(examReportData.getTotalMaxMarks() * 10) / 10), boldFont, boldFont);
        Paragraph percentage = getKeyValueParagraph("Percentage : ", examReportData.getPercentage() == null ? "-"
                : Math.round(examReportData.getPercentage() * 100) / 100d + "%", boldFont, boldFont);
        Paragraph grade = getKeyValueParagraph("Grade : ",
                examReportData.getTotalGrade() == null ? "-" : examReportData.getTotalGrade().getGradeName(), boldFont, boldFont);

//        Paragraph attendance = getKeyValueParagraph("Attendance : ", examReportData.getTotalWorkingDays() == null ? "-" :
//                examReportData.getTotalAttendedDays() == null ? "-" : String.valueOf(examReportData.getTotalAttendedDays()) + "/" + String.valueOf(examReportData.getTotalWorkingDays()), boldFont, boldFont);

        if (reportType.equalsIgnoreCase(ANNUAL_REPORT_TYPE) || reportType.equalsIgnoreCase(TERM_II_REPORT_TYPE)) {
            addRow(table, documentLayoutSetup, Arrays.asList(new CellData(result, cellLayoutSetup),
                    new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(promotedClass, cellLayoutSetup)));
        }
        addRow(table, documentLayoutSetup, Arrays.asList(new CellData(obtainedMarks, cellLayoutSetup),
                new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(totalMarks, cellLayoutSetup)));
        addRow(table, documentLayoutSetup, Arrays.asList(new CellData(percentage, cellLayoutSetup),
                new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(grade, cellLayoutSetup)));
//        if(rank != null){
//            addRow(table, documentLayoutSetup, Arrays.asList(new CellData(EMPTY_TEXT, cellLayoutSetup),
//                    new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(rank, cellLayoutSetup)));
//        }
//        else{
//            addRow(table, documentLayoutSetup, Arrays.asList(new CellData(EMPTY_TEXT, cellLayoutSetup),
//                    new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(EMPTY_TEXT, cellLayoutSetup)));
//        }

        document.add(table);

    }

    protected void generateRemarksSection(ExamReportCardLayoutData examReportCardLayoutData,
                                          ExamReportData examReportData, PdfFont boldFont, PdfFont regularFont) throws IOException {

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(boldFont).setFontSize(examReportCardLayoutData.getContentFontSize() - 1)
                .setTextAlignment(TextAlignment.LEFT);

        Table remarksTable = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), 1);

        Paragraph remarks = getKeyValueParagraph("Remarks: ", examReportData.getRemarks());

        addRow(remarksTable, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(remarks),
                cellLayoutSetup);
        examReportCardLayoutData.getDocument().add(remarksTable);
    }

    protected void generateSignatureBox(Institute institute, Document document, DocumentLayoutSetup documentLayoutSetup, ExamReportData examReportData,
                                        float contentFontSize, float defaultBorderWidth, int pageNumber, PdfFont boldFont, PdfFont regularFont,
                                        String reportType, StudentManager studentManager) throws IOException {
        PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);
//		Map<CourseType, List<ExamGrade>> gradesList = examReportData.getCourseTypeExamGrades();
//		List<ExamGrade> scholasticGrades = gradesList.get(CourseType.SCHOLASTIC);
//		List<ExamGrade> coScholasticGrades = gradesList.get(CourseType.COSCHOLASTIC);
//		boolean hasScholastic = !CollectionUtils.isEmpty(scholasticGrades);
//		boolean hasCoScholastic = !CollectionUtils.isEmpty(coScholasticGrades);
//		int yAxis = 94;
//		float yOffset = 94f;
//		if (hasScholastic && hasCoScholastic) {
//			yAxis = 150;
//			yOffset = 150f;
//		}

//        canvas.moveTo(12, 94);
//        canvas.lineTo(583, 94);
//        canvas.setLineWidth(.5f);
//        canvas.closePathStroke();
        int singleContentColumn = 3;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);
        CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
        signatureCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 1)
                .setTextAlignment(TextAlignment.CENTER);
        Paragraph dateOfResultDeclaration = getKeyValueParagraph("Date : ",
                examReportData.getDateOfResultDeclaration() == null ? "-" : DateUtils.getFormattedDate(examReportData.getDateOfResultDeclaration()), boldFont, boldFont);
        addRow(table, documentLayoutSetup, Arrays.asList(dateOfResultDeclaration, getParagraph("Exam Incharge"),
                getParagraph("Principal")), signatureCellLayoutSetup);
        table.setFixedPosition(30f, 20, documentLayoutSetup.getPageSize().getWidth() - 100f);
        document.add(table);
        byte[] principalSignatureImage = getStaffSignature(institute.getInstituteId(), studentManager, PRINCIPAL_UUID);
        if(principalSignatureImage != null){
            generateImage(document, documentLayoutSetup,
                    principalSignatureImage,
                    100f, 80f, 420, 25f);
        }

        byte[] examInchargeSignatureImage = getStaffSignature(institute.getInstituteId(), studentManager, EXAM_INCHARGE_UUID);
        if(examInchargeSignatureImage != null){
            generateImage(document, documentLayoutSetup,
                    examInchargeSignatureImage,
                    100f, 80f, 240, 25f);
        }
//        generateGradeBox(institute, document, documentLayoutSetup, examReportData, contentFontSize, defaultBorderWidth, boldFont, regularFont);
    }

    private void generateGradeBox(Institute institute, Document document, DocumentLayoutSetup documentLayoutSetup, ExamReportData examReportData, float contentFontSize,
                                  float defaultBorderWidth, PdfFont boldFont, PdfFont regularFont) throws IOException {

        Map<CourseType, List<ExamGrade>> gradesList = examReportData.getCourseTypeExamGrades();
        List<ExamGrade> scholasticGrades = gradesList.get(CourseType.SCHOLASTIC);
		List<ExamGrade> coScholasticGrades = gradesList.get(CourseType.COSCHOLASTIC);

        boolean hasScholastic = !CollectionUtils.isEmpty(scholasticGrades);
        boolean hasCoScholastic = false;

        if (!hasScholastic && !hasCoScholastic) {
            return;
        }
        float yAxisforMarksRange = hasScholastic && hasCoScholastic ? 130f : 74f;

        float yOffset = hasCoScholastic ? 109f : 53f;
        Table table = getPDFTable(documentLayoutSetup, 1);
        CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
        signatureCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 1)
                .setTextAlignment(TextAlignment.CENTER);
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("MARKS RANGE")), signatureCellLayoutSetup);
        table.setFixedPosition(10f, yAxisforMarksRange, documentLayoutSetup.getPageSize().getWidth());
        document.add(table);

        CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
        marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(contentFontSize - 2)
                .setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);

        String gradingSchemeTitle = "Grading Scheme";

        if (hasScholastic) {
            generateGradeTable(document, documentLayoutSetup, gradingSchemeTitle, scholasticGrades, marksCellLayoutSetup,
                    contentFontSize, defaultBorderWidth, boldFont, regularFont, yOffset, CourseType.SCHOLASTIC);
        }

//		if (hasCoScholastic) {
//			float coScholasticPositionY = hasScholastic ? 57f : 53f;
//			generateGradeTable(document, documentLayoutSetup, "Co-Scholastic Grading", coScholasticGrades, marksCellLayoutSetup,
//							contentFontSize, defaultBorderWidth, boldFont, regularFont, coScholasticPositionY, CourseType.COSCHOLASTIC);
//		}
    }

    private void generateGradeTable(Document document, DocumentLayoutSetup documentLayoutSetup, String title, List<ExamGrade> grades, CellLayoutSetup marksCellLayoutSetup,
                                    float contentFontSize, float defaultBorderWidth, PdfFont boldFont, PdfFont regularFont, float yPosition, CourseType courseType) throws IOException {

        Table table = getPDFTable(documentLayoutSetup, 1);
        CellLayoutSetup titleHeadLayoutsSetup = new CellLayoutSetup();

        if(CourseType.SCHOLASTIC == courseType){
            titleHeadLayoutsSetup.setPdfFont(regularFont).setFontSize(contentFontSize - 2)
                    .setBorderTop(new SolidBorder(defaultBorderWidth)).setBorderRight(new SolidBorder(defaultBorderWidth)).setBorderLeft(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);
        }
        if(CourseType.COSCHOLASTIC == courseType){
            titleHeadLayoutsSetup.setPdfFont(regularFont).setFontSize(contentFontSize - 2)
                    .setBorderRight(new SolidBorder(defaultBorderWidth)).setBorderLeft(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);
        }

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(title)), titleHeadLayoutsSetup);
        table.setFixedPosition(25f, yPosition, documentLayoutSetup.getPageSize().getWidth() - 100f);
        document.add(table);

        int columnCount = grades.size() + 1;
        float firstColumnWidth = 0.13f;
        float remainingWidth = 1f - firstColumnWidth;
        float columnWidth = remainingWidth / (columnCount - 1);

        float[] columnWidths = new float[columnCount];
        columnWidths[0] = firstColumnWidth;
        Arrays.fill(columnWidths, 1, columnWidths.length, columnWidth);

        Table headerTable = getPDFTable(documentLayoutSetup, columnWidths);
        List<CellData> headerList = new ArrayList<>();
        List<CellData> gradeList = new ArrayList<>();

        headerList.add(new CellData("MARKS RANGE", marksCellLayoutSetup));
        gradeList.add(new CellData("GRADE", marksCellLayoutSetup));

        for (ExamGrade grade : grades) {
            headerList.add(new CellData(grade.getRangeDisplayName(), marksCellLayoutSetup));
            gradeList.add(new CellData(grade.getGradeName(), marksCellLayoutSetup));
        }

        addRow(headerTable, documentLayoutSetup, headerList);
        addRow(headerTable, documentLayoutSetup, gradeList);
        headerTable.setFixedPosition(25f, yPosition - 37f, documentLayoutSetup.getPageSize().getWidth() - 100f);
        document.add(headerTable);
    }

    protected Set<UUID> getNonAdditionalSubjects(ExamReportData examReportData) {
        Set<UUID> nonAdditionalSubjects = new HashSet<UUID>();
        for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportData.getCourseTypeExamReportMarksGrid()
                .get(CourseType.SCHOLASTIC).getExamReportCourseMarksRows()) {
            if (!isAdditionalSubject(examReportCourseMarksRow.getCourse(), examReportData.getExamReportStructure())) {
                nonAdditionalSubjects.add(examReportCourseMarksRow.getCourse().getCourseId());
            }
        }
        return nonAdditionalSubjects;
    }

    protected void sortClassExamReports(List<ExamReportData> examReportDataList) {
        Collections.sort(examReportDataList, new Comparator<ExamReportData>() {

            @Override
            public int compare(ExamReportData e1, ExamReportData e2) {
                StandardSections section1 = e1.getStudentLite().getStudentSessionData().getStandardSection();

                StandardSections section2 = e2.getStudentLite().getStudentSessionData().getStandardSection();

                if (section1 != null && section2 != null) {
                    int sectionCompare = section1.getSectionName().compareToIgnoreCase(section2.getSectionName());
                    if (sectionCompare != 0) {
                        return sectionCompare;
                    }
                }

                return e1.getStudentLite().getName().compareToIgnoreCase(e2.getStudentLite().getName());
            }
        });
    }

    @Override
    public DocumentOutput generateClassReport(Institute institute, String reportType,
                                              List<ExamReportData> examReportDataList, String documentName, StudentManager studentManager) {

        try {
            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);

            sortClassExamReports(examReportDataList);

            int pageNumber = 1;
            PdfFont regularFont = getCambriaFont();
            PdfFont boldFont = getCambriaBoldFont();
            for (ExamReportData examReportData : examReportDataList) {
                generateStudentReportCard(examReportCardLayoutData, institute, examReportData, studentManager, reportType,
                        pageNumber, boldFont, regularFont);

                if (pageNumber != examReportDataList.size()) {
                    examReportCardLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                }
                pageNumber++;
            }

            examReportCardLayoutData.getDocument().close();
            return documentOutput;

        } catch (IOException e) {
            e.printStackTrace();
        }

        return null;
    }

    @Override
    public DocumentOutput generateClassResultReport(Institute institute, String reportType, List<ExamReportData> examReportDataList,
                                                    String documentName, StudentManager studentManager, int studentPerPage) {
        try {
            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4);
            Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

            PdfFont regularFont = getRegularFont();
            PdfFont boldFont = getRegularBoldFont();

            CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
            marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(DEFAULT_FONT_SIZE)
                    .setBorder(new SolidBorder(DEFAULT_BORDER_WIDTH)).setTextAlignment(TextAlignment.CENTER);

            CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
                    .setTextAlignment(TextAlignment.LEFT);

            String instituteName = institute.getInstituteName().toUpperCase();
            String sessionName = "";
            if(!CollectionUtils.isEmpty(examReportDataList)) {
                sessionName = "Session : " + examReportDataList.get(0).getStudentLite().getStudentSessionData().getShortYearDisplayName();
            }
            generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName,
                    sessionName, marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));

            sortClassExamReports(examReportDataList);
            int pageNumber = 1;
            for (ExamReportData examReportData : examReportDataList) {
                generateClassReportDataStudentDetails(documentLayoutSetup, document, courseCellLayoutSetup, examReportData.getStudentLite());
                generateScholasticMarksGrid(document,
                        documentLayoutSetup, DEFAULT_FONT_SIZE, DEFAULT_BORDER_WIDTH, examReportData, GridConfigs.forOnlyObtainedTotalRow(),
                        "Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType),
                        null, null);
                generateClassReportDataStudentResult(documentLayoutSetup, document, examReportData, courseCellLayoutSetup);
                if (pageNumber % studentPerPage == 0) {
                    document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                    generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName, sessionName,
                            marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));
                } else {
                    addBlankLine(document, false, 1);
                }
                pageNumber++;
            }
            document.close();
            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
                    reportType, e);
        }
        return null;
    }

    protected byte[] getStaffSignature(int instituteId, StudentManager studentManager, String staffId) throws IOException {
        if(StringUtils.isEmpty(staffId)){
            return null;
        }
        return getStudentSchoolPrincipalSignature(instituteId, UUID.fromString(staffId), studentManager);
    }
}
