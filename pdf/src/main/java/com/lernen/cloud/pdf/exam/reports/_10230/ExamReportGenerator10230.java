package com.lernen.cloud.pdf.exam.reports._10230;


import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.report.ExamReportCardLayoutData;
import com.lernen.cloud.core.api.examination.report.ExamReportCourseMarksRow;
import com.lernen.cloud.core.api.examination.report.ExamReportData;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.core.utils.images.WatermarkProvider;
import com.lernen.cloud.pdf.exam.reports.ExamReportGenerator;
import com.lernen.cloud.pdf.exam.reports.IExamReportCardGenerator;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;

import static com.embrate.cloud.core.api.institute.InstituteDocumentType.*;

public class ExamReportGenerator10230 extends ExamReportGenerator implements IExamReportCardGenerator{

    public ExamReportGenerator10230(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(ExamReportGenerator10230.class);
	public static final float DEFAULT_PAGE_SIDE_MARGIN = 25f;
	public static final float DEFAULT_PAGE_TOP_MARGIN = 10f;
	public static final float DEFAULT_PAGE_BOTTOM_MARGIN = 10f;
	public static final float SQUARE_BORDER_MARGIN = 12f;

	protected static final String HALF_YEARLY_REPORT_TYPE = "HALF_YEARLY";
	protected static final String ANNUAL_REPORT_TYPE = "ANNUAL";
	protected static final String SCHOLASTIC_EXAM_DESCRIPTION = "FA = Formative Assessment, SA = Summative Assessment";
	
	protected static final String _10230_LETTER_HEAD_LINE_1 = "English Medium: Nursery to XII, Hindi Medium: VII to XII";
	protected static final String _10230_LETTER_HEAD_LINE_2 = "Jagdish Puri Road Kanda Colony, Tehsil-Shrimadhopur, Dist. Sikar, Rajasthan";
	protected static final String _10230_LETTER_HEAD_LINE_3 = "Contact On:- 01575222117, ********** <NAME_EMAIL>";
	protected static final String _10230_LETTER_HEAD_LINE_4 = "Powered by Aastha Jan Kalyan Sanstha, Jaipur [NGO]";

	protected static final String _10231_LETTER_HEAD_LINE_1 = "English Medium: Nursery to VIII";
	protected static final String _10231_LETTER_HEAD_LINE_2 = "Let Ka Bas, Shahpura, Teh: Shahpura, Dist: Jaipur";
	protected static final String _10231_LETTER_HEAD_LINE_3 = "Contact On:- **********, 7737378717 <NAME_EMAIL>";
	protected static final String _10231_LETTER_HEAD_LINE_4 = "Powered by Aastha Jan Kalyan Sanstha, Jaipur [NGO]";

	private static final  String GRADE_A1 = "Above 85 percent. All over good efforts of parents & school, very nice achievements. Congratulations! Your child's performance is best. Keep it up.";
	private static final  String GRADE_A2 = "Marks obtained 70% to 84.99%. Need parents more attention on their kid's study to get A+ grade. We suggest you to attend regular meetings held by school.";
	private static final  String GRADE_B1 = "Marks obtained 60 to 74.99%. It's not a very bad position but your child may be up or down from this stage. So please be careful to your kid's study. We strongly suggest you to attend regular meetings held by school.";
	private static final  String GRADE_B2 = "Marks obtained 55 to 59.99%. If your child was new admitted in starting of this session and was poor in study then we may expect that he will grow, but if your child is our school's old student then it is the matter to think for School Management & Parents' also. So please be careful to your kid's study. We strongly suggest you to attend regular meetings held by school.";
	private static final  String GRADE_C1 = "Marks obtained 50 to 54.99%. It is a very poor condition for every type (New or Old) of student. Please look after your child's every activity. We strongly suggest you to attend regular meetings held by school and please inform us about your child's home activities regularly.";
	private static final  String GRADE_C2 = "Below 50%. It's a very critical condition for School Management, child and parents. No doubt your child has potentially to prove himself/herself but either you are very careless or your child or we are on a wrong way. So please think.";

    //	Red : rgb(204, 0, 0)
    //	Purple : rgb(57, 65, 158)
    //	Blue : rgb(1, 155, 248)

	private static final Map<String, String> MARKS_GRADE_MAP = new LinkedHashMap<>();

	private static final Map<Integer, List<String>> HEADER_MAP = new LinkedHashMap<>();
	private static final List<String> _10230_HEADER_LIST = new ArrayList<>();
	private static final List<String> _10231_HEADER_LIST = new ArrayList<>();

	static {
		MARKS_GRADE_MAP.put("85 - 100", "A+");
		MARKS_GRADE_MAP.put("70 - 85", "A");
		MARKS_GRADE_MAP.put("60 - 70", "B+");
		MARKS_GRADE_MAP.put("55 - 60", "B");
		MARKS_GRADE_MAP.put("50 - 55", "C+");
		MARKS_GRADE_MAP.put("0 - 50", "C");

		_10230_HEADER_LIST.add(_10230_LETTER_HEAD_LINE_1);
		_10230_HEADER_LIST.add(_10230_LETTER_HEAD_LINE_2);
		_10230_HEADER_LIST.add(_10230_LETTER_HEAD_LINE_3);
		_10230_HEADER_LIST.add(_10230_LETTER_HEAD_LINE_4);
		HEADER_MAP.put(10230, _10230_HEADER_LIST);
        HEADER_MAP.put(10232, _10230_HEADER_LIST);

		_10231_HEADER_LIST.add(_10231_LETTER_HEAD_LINE_1);
		_10231_HEADER_LIST.add(_10231_LETTER_HEAD_LINE_2);
		_10231_HEADER_LIST.add(_10231_LETTER_HEAD_LINE_3);
		_10231_HEADER_LIST.add(_10231_LETTER_HEAD_LINE_4);
		HEADER_MAP.put(10231, _10231_HEADER_LIST);
	}

	private static final Map<String, String> PROMOTION_CLASS_MAP_10232 = new HashMap<>();

	static {
		PROMOTION_CLASS_MAP_10232.put("11th EM (Biology)", "12th EM (Biology)");
		PROMOTION_CLASS_MAP_10232.put("11th HM (Biology)", "12th HM (Biology)");
		PROMOTION_CLASS_MAP_10232.put("11th EM (Maths)", "12th EM (Maths)");
		PROMOTION_CLASS_MAP_10232.put("11th HM (Maths)", "12th HM (Maths)");
		PROMOTION_CLASS_MAP_10232.put("11th EM (Agriculture)", "12th EM (Agriculture)");
		PROMOTION_CLASS_MAP_10232.put("11th HM (Agriculture)", "12th HM (Agriculture)");
	}

	@Override
	public DocumentOutput generateReport(Institute institute, Student student, String reportType,
										 ExamReportData examReportData, String documentName, StudentManager studentManager) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);
			PdfFont regularFont = getCambriaFont();
			PdfFont boldFont = getCambriaBoldFont();
			Document document = examReportCardLayoutData.getDocument();
			DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
			generateStudentReport(document, documentLayoutSetup, institute, examReportData.getStudentLite(), reportType, examReportData, examReportCardLayoutData, 1,
					regularFont, boldFont, studentManager);
			examReportCardLayoutData.getDocument().close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, student {}, reportType {} ",
					institute.getInstituteId(), student.getStudentId(), reportType, e);
		}
		return null;
	}

	@Override
	public DocumentOutput generateClassReport(Institute institute, String reportType,
													List<ExamReportData> examReportDataList, String documentName, StudentManager studentManager) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);
			Document document = examReportCardLayoutData.getDocument();
			DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
			sortClassExamReports(examReportDataList);
			PdfFont regularFont = getCambriaFont();
			PdfFont boldFont = getCambriaBoldFont();
			int pageNumber = 1;
			for (ExamReportData examReportData : examReportDataList) {
				generateStudentReport(document, documentLayoutSetup, institute, examReportData.getStudentLite(), reportType, examReportData,
						examReportCardLayoutData, pageNumber, regularFont, boldFont, studentManager);

				if (pageNumber != examReportDataList.size()) {
					examReportCardLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				pageNumber++;
			}
			examReportCardLayoutData.getDocument().close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
					reportType, e);
		}
		return null;

	}

	private void generateStudentReport(Document document, DocumentLayoutSetup documentLayoutSetup, Institute institute, StudentLite studentLite, String reportType,
									   ExamReportData examReportData, ExamReportCardLayoutData examReportCardLayoutData, int pageNumber,
			PdfFont regularFont, PdfFont boldFont, StudentManager studentManager) throws IOException {

		float watermarkImageHeightWidth = 400f;
		//Institute watermark
		int instituteId = institute.getInstituteId();
		generateWatermark(examReportCardLayoutData, instituteId, watermarkImageHeightWidth, watermarkImageHeightWidth, watermarkImageHeightWidth / 5 + 20,
				watermarkImageHeightWidth / 2);

		addBlankLine(examReportCardLayoutData, false, 1);
		generateHeader(examReportCardLayoutData, studentLite, institute, reportType, 20f,
				examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.88f, regularFont, boldFont);
		addBlankLine(examReportCardLayoutData, false, 2);
		generateStudentInformation(examReportCardLayoutData, studentLite, examReportData);
		addBlankLine(examReportCardLayoutData, false, 1);
		generateCustomScholasticMarksGrid(examReportCardLayoutData, examReportData, reportType, GridConfigs.forOnlyObtainedTotalRow(),
				"Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType));
		generateScholasticExamDescription(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
				examReportData);
		generateCoScholasticMarksGrid(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(), "Co-Scholastic Subjects",
				getCoScholasticMarksGridSubjectWidth(reportType));
		addBlankLine(examReportCardLayoutData, false, 1);
		generateResultSummary(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
				examReportCardLayoutData.getContentFontSize(), examReportData, reportType, institute.getInstituteId());
		generateRemarksSection(examReportCardLayoutData, examReportData);
		generateSignatureBox(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
				examReportCardLayoutData.getContentFontSize(), examReportCardLayoutData.getDefaultBorderWidth(), pageNumber);
		generateBorderLayout(examReportCardLayoutData, pageNumber);
	}

	protected void generateScholasticExamDescription(Document document, DocumentLayoutSetup documentLayoutSetup,
													 float contentFontSize, ExamReportData examReportData) throws IOException {
		Text descTitle = new Text("Description: ").setFont(getRegularBoldFont()).setFontSize(contentFontSize - 2);

		Text descText = new Text(SCHOLASTIC_EXAM_DESCRIPTION).setFontSize(contentFontSize - 4);
		Paragraph desc = new Paragraph();
		desc.add(descTitle).add(descText);
		document.add(desc);
	}

	private void generateCustomScholasticMarksGrid(ExamReportCardLayoutData examReportCardLayoutData,
												   ExamReportData examReportData, String reportType, GridConfigs gridConfigs, String subjectColumnTitle,
												   float subjectColumnWidth) throws IOException {
		Set<UUID> nonAdditionalSubjects = getNonAdditionalSubjects(examReportData);
		generateScholasticMarksGrid(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, gridConfigs, subjectColumnTitle,
				subjectColumnWidth, nonAdditionalSubjects, null);

	}

	protected float getScholasticMarksGridSubjectWidth(String reportType) {
		if (reportType.equalsIgnoreCase(HALF_YEARLY_REPORT_TYPE)) {
			return 0.2f;
		}
		return 0.2f;
	}

	protected float getCoScholasticMarksGridSubjectWidth(String reportType) {
		if (reportType.equalsIgnoreCase(HALF_YEARLY_REPORT_TYPE)) {
			return 0.5f;
		}
		if (reportType.equalsIgnoreCase(ANNUAL_REPORT_TYPE)) {
			return 0.4f;
		}
		return 0.3f;
	}

	protected ExamReportCardLayoutData generateReportCardLayoutData(Institute institute, DocumentOutput documentOutput)
			throws IOException {

		DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false);
		float contentFontSize = 11f;
		float defaultBorderWidth = 0.5f;
		Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
		float logoWidth = 75f;
		float logoHeight = 75f;

		int instituteId = institute.getInstituteId();

		return new ExamReportCardLayoutData(document, documentLayoutSetup, null, null, contentFontSize,
				defaultBorderWidth, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId));
	}

	@Override
	public DocumentLayoutSetup initDocumentLayoutSetup(boolean officeCopy) {
		return initDocumentLayoutSetup(officeCopy, officeCopy ? DEFAULT_PAGE_SIZE.rotate() : DEFAULT_PAGE_SIZE,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN : DEFAULT_PAGE_TOP_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN : DEFAULT_PAGE_BOTTOM_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_SIDE_MARGIN : DEFAULT_PAGE_SIDE_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_MIDDLE_MARGIN : 0f);
	}

	protected void generateHeader(ExamReportCardLayoutData examReportCardLayoutData, StudentLite studentLite,
			Institute institute, String reportType, float offsetX, float offsetY, PdfFont regularFont, PdfFont boldFont) throws IOException {
		float schoolNameFontSize = 19f;
		float headerFontSize = 11f;
		generateDynamicImageProvider(examReportCardLayoutData, 0, 25, institute.getInstituteId(), INSTITUTE_PRIMARY_LOGO);

		float xOffSet = examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getWidth() - (offsetX + 75f);
		generateSecondaryLogo(examReportCardLayoutData, institute.getInstituteId(), xOffSet, offsetY);

		int singleContentColumn = 1;
		Table table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(headerFontSize).setTextAlignment(TextAlignment.CENTER);

		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getInstituteName())
						.setFontColor(Color.convertRgbToCmyk(new DeviceRgb(57, 65, 158)))),
						cellLayoutSetup.copy().setFontSize(schoolNameFontSize));

		int index = 1;
		List<String> headerList = HEADER_MAP.get(institute.getInstituteId());
		if(!CollectionUtils.isEmpty(headerList)) {
			for(String letterHeadVal : HEADER_MAP.get(institute.getInstituteId())) {
				if(index == 1) {
					addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(letterHeadVal)),
							cellLayoutSetup.copy().setFontSize(12f));
				} else {
					addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(letterHeadVal)),
							cellLayoutSetup.copy().setFontSize(12f).setPdfFont(regularFont));
				}
				index++;
			}
		}

		CellLayoutSetup singleLineBorderLayout = new CellLayoutSetup();
		
		singleLineBorderLayout.setPdfFont(boldFont).setFontSize(2f).setBorderBottom(new SolidBorder(2f));
		
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph("")),singleLineBorderLayout);
				
		String headerExamTitle = "PROGRESS REPORT ";
		headerExamTitle += "(" + studentLite.getStudentSessionData().getShortYearDisplayName() + ")";
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(headerExamTitle).setUnderline()),
				cellLayoutSetup.copy().setFontSize(headerFontSize).setPdfFont(boldFont));
		examReportCardLayoutData.getDocument().add(table);

	}

	protected void generateStudentInformation(ExamReportCardLayoutData examReportCardLayoutData,
			StudentLite studentLite, ExamReportData examReportData) throws IOException {

		PdfFont boldFont = getRegularBoldFont();
		Document document = examReportCardLayoutData.getDocument();
		DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
		float contentFontSize = examReportCardLayoutData.getContentFontSize();

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.4f, 0.2f, 0.4f });

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize);

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);

		Paragraph studentName = getKeyValueParagraph("Student Name : ", studentLite.getName(), 57, 65, 158, boldFont, boldFont);
		Paragraph fatherName = getKeyValueParagraph("Father Name : ", studentLite.getFathersName(), 57, 65, 158, boldFont, boldFont);
		Paragraph dob = getKeyValueParagraph("DOB : ",
				studentLite.getDateOfBirth() == null || studentLite.getDateOfBirth() <= 0 ? ""
						: DateUtils.getFormattedDate(studentLite.getDateOfBirth(), DATE_FORMAT, User.DFAULT_TIMEZONE), 57, 65, 158, boldFont, boldFont);
		Paragraph motherName = getKeyValueParagraph("Mother Name : ", studentLite.getMothersName(), 57, 65, 158, boldFont, boldFont);
		Paragraph admissionNumber = getKeyValueParagraph("Admission No. : ", studentLite.getAdmissionNumber(), 57, 65, 158, boldFont, boldFont);
		Paragraph classValue = getKeyValueParagraph("Class : ",
				studentLite.getStudentSessionData().getStandardNameWithSection(), 57, 65, 158, boldFont, boldFont);
		Paragraph rollNumber = getKeyValueParagraph("Roll Number : ", studentLite.getStudentSessionData().getRollNumber(), 57, 65, 158, boldFont, boldFont);
		Paragraph dateOfResultDeclaration = getKeyValueParagraph("Date Of Result Declaration : ",
				examReportData.getDateOfResultDeclaration() == null ? "-" : DateUtils.getFormattedDate(examReportData.getDateOfResultDeclaration()),
				57, 65, 158, boldFont, boldFont);


		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(studentName, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
				new CellData(fatherName, thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(dob, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
				new CellData(motherName, thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(admissionNumber, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
				new CellData(classValue, thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(rollNumber, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
				new CellData(dateOfResultDeclaration, thirdCellLayoutSetup)));

		document.add(table);

	}

	protected void generateBorderLayout(ExamReportCardLayoutData examReportCardLayoutData) {
		generateBorderLayout(examReportCardLayoutData, 1);
	}

	protected void generateBorderLayout(ExamReportCardLayoutData examReportCardLayoutData, int pageNumber) {
		DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
		PdfCanvas canvas = new PdfCanvas(examReportCardLayoutData.getDocument().getPdfDocument(), pageNumber);
		canvas.rectangle(SQUARE_BORDER_MARGIN - 2, SQUARE_BORDER_MARGIN - 2,
				documentLayoutSetup.getPageSize().getWidth() - (SQUARE_BORDER_MARGIN - 2) * 2,
				documentLayoutSetup.getPageSize().getHeight() - (SQUARE_BORDER_MARGIN - 2) * 2);
		canvas.stroke();
		canvas.setLineWidth(1f);
		canvas.rectangle(SQUARE_BORDER_MARGIN, SQUARE_BORDER_MARGIN,
				documentLayoutSetup.getPageSize().getWidth() - SQUARE_BORDER_MARGIN * 2,
				documentLayoutSetup.getPageSize().getHeight() - SQUARE_BORDER_MARGIN * 2);
		canvas.stroke();
		canvas.setLineWidth(1f);
	}

	protected void generateResultSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize, ExamReportData examReportData, String reportType, int instituteId) throws IOException {

		PdfFont boldFont = getRegularBoldFont();

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.LEFT).setPaddingTop(2f).setPaddingBottom(2f);

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.4f, 0.2f, 0.4f });

		Paragraph noOfMeetings = getKeyValueParagraph("Number of Meetings : ", examReportData.getTotalWorkingDays() == null ? ""
				: String.valueOf(examReportData.getTotalWorkingDays()), 1, 155, 248, boldFont, boldFont);
		Paragraph noOfPresent = getKeyValueParagraph("Number of presents : ", examReportData.getTotalAttendedDays() == null ? ""
				: String.valueOf(examReportData.getTotalAttendedDays()), 1, 155, 248, boldFont, boldFont);
		Paragraph result = getKeyValueParagraph("Result : ", "-", boldFont);
		if(examReportData.getExamResultStatus() != null){
			switch (examReportData.getExamResultStatus()){
				case PASS:
				case PASS_WITH_GRACE:
					result = getKeyValueParagraph("Result : ", examReportData.getExamResultStatus().getDisplayName(),
							76, 142, 48, boldFont, boldFont);
					break;
				case SUPPLEMENTARY:
					result = getKeyValueParagraph("Result : ", examReportData.getExamResultStatus().getDisplayName(),
							255, 165, 0, boldFont, boldFont);
					break;
				case FAIL:
					result = getKeyValueParagraph("Result : ", examReportData.getExamResultStatus().getDisplayName(),
							255, 0, 0, boldFont, boldFont);
					break;
			}
		}
		Paragraph promotedClass = getKeyValueParagraph("Promoted to: ",
					examReportData.getPromotedTo() == null ? "-" : examReportData.getPromotedTo().getStandardName(), 57, 65, 158, boldFont, boldFont);

		if (instituteId == 10232) {
			promotedClass = getKeyValueParagraph("Promoted to: ",
					 PROMOTION_CLASS_MAP_10232.get(examReportData.getStudentLite().getStudentSessionData().getStandardDisplayName()), 57, 65, 158, boldFont, boldFont);
		}
		Paragraph obtainedMarks = getKeyValueParagraph("Obtained marks : ",
				examReportData.getTotalObtainedMarks() == null ? "-"
						: examReportData.getTotalObtainedMarks() * 10 % 10 == 0
								? String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10)
								: String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10d), 57, 65, 158, boldFont, boldFont);
		Paragraph totalMarks = getKeyValueParagraph("Total marks : ", examReportData.getTotalMaxMarks() == null ? "-"
				: String.valueOf(Math.round(examReportData.getTotalMaxMarks() * 10) / 10), 57, 65, 158, boldFont, boldFont);
		Paragraph percentage = getKeyValueParagraph("Percentage : ", examReportData.getPercentage() == null ? "-"
				: String.valueOf(Math.round(examReportData.getPercentage() * 100) / 100d) + "%", 57, 65, 158, boldFont, boldFont);
		Paragraph grade = getKeyValueParagraph("Grade : ",
				examReportData.getTotalGrade() == null ? "-" : examReportData.getTotalGrade().getGradeName(), 57, 65, 158, boldFont, boldFont);

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(noOfMeetings, cellLayoutSetup),
				new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(noOfPresent, cellLayoutSetup)));

		if (reportType.equalsIgnoreCase("ANNUAL")) {
			addRow(table, documentLayoutSetup, Arrays.asList(new CellData(result, cellLayoutSetup),
					new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(promotedClass, cellLayoutSetup)));
		}

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(obtainedMarks, cellLayoutSetup),
				new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(totalMarks, cellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(percentage, cellLayoutSetup),
				new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(grade, cellLayoutSetup)));

		document.add(table);

	}

	protected void generateRemarksSection(ExamReportCardLayoutData examReportCardLayoutData,
			ExamReportData examReportData) throws IOException {

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(examReportCardLayoutData.getContentFontSize())
				.setTextAlignment(TextAlignment.LEFT);

		Table remarksTable = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), 1);
		Paragraph remarks = getKeyValueParagraph("Remarks: ",
		StringUtils.isBlank(examReportData.getRemarks()) ? examReportData.getTotalGrade() == null ? "" :examReportData.getTotalGrade() == null ? "" :
						StringUtils.isEmpty(getRemarks(examReportData.getTotalGrade().getGradeName()))
								? "" : getRemarks(examReportData.getTotalGrade().getGradeName()): examReportData.getRemarks());

		addRow(remarksTable, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(remarks),
				cellLayoutSetup);
		examReportCardLayoutData.getDocument().add(remarksTable);
	}

	private String getRemarks(String grade) {
		switch (grade) {
			case "A+":
				return GRADE_A1;
			case "A":
				return GRADE_A2;
			case "B+":
				return GRADE_B1;
			case "B":
				return GRADE_B2;
			case "C+":
				return GRADE_C1;
			case "C":
				return GRADE_C2;
			default:
				return null;

		}
	}

	protected void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize, float defaultBorderWidth, int pageNumber) throws IOException {
		float bottomLineOffset = 35;
//		PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);
//		canvas.moveTo(12, bottomLineOffset);
//		canvas.lineTo(583, bottomLineOffset);
//		canvas.setLineWidth(.5f);
//		canvas.closePathStroke();
		int singleContentColumn = 3;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);
		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.CENTER);

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Class Teacher"), getParagraph("Exam Incharge"),
				getParagraph("Principal")), signatureCellLayoutSetup);
		table.setFixedPosition(30f, bottomLineOffset, documentLayoutSetup.getPageSize().getWidth() - 100f);
		document.add(table);
//		generateGradeBox(document, documentLayoutSetup, contentFontSize, defaultBorderWidth);
	}

	private void generateGradeBox(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
								  float defaultBorderWidth) throws IOException {
		Table table = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize - 2)
				.setTextAlignment(TextAlignment.CENTER);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("MARKS RANGE")), signatureCellLayoutSetup);
		table.setFixedPosition(10f, 55f, documentLayoutSetup.getPageSize().getWidth());
		document.add(table);

		float[] columnWidths = new float[] { 0.24f, 0.24f, 0.24f, 0.24f };
		Table headerTable = getPDFTable(documentLayoutSetup, columnWidths);
		CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
		marksCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize - 4)
				.setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);
		List<Map.Entry<String, String>> gradesList = new ArrayList<>(MARKS_GRADE_MAP.entrySet());
		for(int index = 0; index < gradesList.size() / 2; index++) {
			List<CellData> row = new ArrayList<>();
			row.add(new CellData(gradesList.get(index).getKey(), marksCellLayoutSetup));
			row.add(new CellData(gradesList.get(index).getValue(), marksCellLayoutSetup));
			row.add(new CellData(gradesList.get(gradesList.size() / 2 + index).getKey(), marksCellLayoutSetup));
			row.add(new CellData(gradesList.get(gradesList.size() / 2 + index).getValue(), marksCellLayoutSetup));
			addRow(headerTable, documentLayoutSetup, row);
		}
		headerTable.setFixedPosition(35f, 15f, documentLayoutSetup.getPageSize().getWidth() - 100f);
		document.add(headerTable);
	}

	protected Set<UUID> getNonAdditionalSubjects(ExamReportData examReportData) {
		Set<UUID> nonAdditionalSubjects = new HashSet<UUID>();
		for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportData.getCourseTypeExamReportMarksGrid()
				.get(CourseType.SCHOLASTIC).getExamReportCourseMarksRows()) {
			if (!isAdditionalSubject(examReportCourseMarksRow.getCourse(), examReportData.getExamReportStructure())) {
				nonAdditionalSubjects.add(examReportCourseMarksRow.getCourse().getCourseId());
			}
		}
		return nonAdditionalSubjects;
	}

	protected void sortClassExamReports(List<ExamReportData> examReportDataList) {
		Collections.sort(examReportDataList, new Comparator<ExamReportData>() {

			@Override
			public int compare(ExamReportData e1, ExamReportData e2) {
				StandardSections section1 = e1.getStudentLite().getStudentSessionData().getStandardSection();

				StandardSections section2 = e2.getStudentLite().getStudentSessionData().getStandardSection();

				if (section1 != null && section2 != null) {
					int sectionCompare = section1.getSectionName().compareToIgnoreCase(section2.getSectionName());
					if (sectionCompare != 0) {
						return sectionCompare;
					}
				}

				return e1.getStudentLite().getName().compareToIgnoreCase(e2.getStudentLite().getName());
			}
		});
	}
													
	@Override
	public DocumentOutput generateClassResultReport(Institute institute, String reportType, List<ExamReportData> examReportDataList,
													String documentName, StudentManager studentManager, int studentPerPage) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4);
			Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

			float logoWidth = 75f;
			float logoHeight = 75f;
			PdfFont boldFont = getRegularBoldFont();
			PdfFont regularFont = getRegularFont();
			int instituteId = institute.getInstituteId();
			ExamReportCardLayoutData examReportCardLayoutData = new ExamReportCardLayoutData(document, documentLayoutSetup, null, null, DEFAULT_FONT_SIZE,
					DEFAULT_BORDER_WIDTH, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId));

			CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
			marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(DEFAULT_FONT_SIZE)
					.setBorder(new SolidBorder(DEFAULT_BORDER_WIDTH)).setTextAlignment(TextAlignment.CENTER);

			CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
					.setTextAlignment(TextAlignment.LEFT);

			String instituteName = institute.getInstituteName().toUpperCase();
			String sessionName = "";
			if(!CollectionUtils.isEmpty(examReportDataList)) {
				sessionName = "Session : " + examReportDataList.get(0).getStudentLite().getStudentSessionData().getShortYearDisplayName();
			}
			generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName,
					sessionName, marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));

			sortClassExamReports(examReportDataList);
			int pageNumber = 1;
			for (ExamReportData examReportData : examReportDataList) {
				generateClassReportDataStudentDetails(documentLayoutSetup, document, courseCellLayoutSetup, examReportData.getStudentLite());
				generateScholasticMarksGrid(document,
						documentLayoutSetup, DEFAULT_FONT_SIZE, DEFAULT_BORDER_WIDTH, examReportData, GridConfigs.forOnlyObtainedTotalRow(),
						"Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType),
						null, "#39419e0");
				generateClassReportDataStudentResult(documentLayoutSetup, document, examReportData, courseCellLayoutSetup);
				if (pageNumber % studentPerPage == 0) {
					document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
					generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName, sessionName,
							marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));
				} else {
					addBlankLine(document, false, 1);
				}
				pageNumber++;
			}
			document.close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
					reportType, e);
		}
		return null;
	}
}
