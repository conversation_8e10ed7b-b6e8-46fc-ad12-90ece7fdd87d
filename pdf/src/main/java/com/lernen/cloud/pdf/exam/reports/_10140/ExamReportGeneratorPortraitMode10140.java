package com.lernen.cloud.pdf.exam.reports._10140;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.report.ExamReportCardLayoutData;
import com.lernen.cloud.core.api.examination.report.ExamReportCourseMarksRow;
import com.lernen.cloud.core.api.examination.report.ExamReportData;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.pdf.exam.reports.ExamReportGenerator;
import com.lernen.cloud.pdf.exam.reports.IExamReportCardGenerator;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;

import static com.lernen.cloud.core.utils.StringHelper.capitalizeFirstOfAllWords;

/**
 *
 * <AUTHOR>
 *
 */
public class ExamReportGeneratorPortraitMode10140 extends ExamReportGenerator implements IExamReportCardGenerator {

	public ExamReportGeneratorPortraitMode10140(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(ExamReportGeneratorPortraitMode10140.class);
	public static final float DEFAULT_PAGE_SIDE_MARGIN = 25f;
	public static final float DEFAULT_PAGE_TOP_MARGIN = 15f;
	public static final float DEFAULT_PAGE_BOTTOM_MARGIN = 15f;
	public static final float SQUARE_BORDER_MARGIN = 18f;

	protected static final String UNIT_I_REPORT_TYPE = "UNIT_I";
	protected static final String UNIT_II_REPORT_TYPE = "UNIT_II";
	protected static final String TERM_I_REPORT_TYPE = "TERM_I";
	protected static final String ANNUAL_REPORT_TYPE = "ANNUAL";

	@Override
	public DocumentOutput generateReport(Institute institute, Student student, String reportType,
			ExamReportData examReportData, String documentName, StudentManager studentManager) {
		try {

			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);

			PdfFont regularFont = getCambriaFont();
			PdfFont boldFont = getCambriaBoldFont();

			PdfFont timesBoldFont = getRegularBoldFont();

			generateStudentReportCard(examReportCardLayoutData, institute, examReportData, studentManager, reportType,
					regularFont, boldFont, 1, timesBoldFont);

			examReportCardLayoutData.getDocument().close();
			return documentOutput;
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("Error while generating report card institute {}, student {}, reportType {} ",
					institute.getInstituteId(), student.getStudentId(), reportType, e);
		}
		return null;
	}

	protected void generateStudentReportCard(ExamReportCardLayoutData examReportCardLayoutData, Institute institute,
											 ExamReportData examReportData, StudentManager studentManager,
											 String reportType, PdfFont regularFont, PdfFont boldFont,
											 int pageNumber, PdfFont timesBoldFont) throws IOException {
		generateMetaDataLayout(examReportCardLayoutData, institute, examReportData.getStudentLite(), studentManager, reportType,
				examReportData, regularFont, boldFont, pageNumber, timesBoldFont);

		addBlankLine(examReportCardLayoutData.getDocument(), false, 2);

		generateScholasticMarksGrid(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(), "Scholastic Subjects",
				getScholasticMarksGridSubjectWidth(reportType), null, "#39419e0");

		addBlankLine(examReportCardLayoutData.getDocument(), false, 2);

		generateCoScholasticMarksGrid(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(), "Co-Scholastic Subjects",
				getCoScholasticMarksGridSubjectWidth(reportType));

		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);

		generateResultSummary(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
				examReportData, reportType, regularFont, boldFont);

		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);

		generateRemarksSection(examReportCardLayoutData, examReportData, regularFont, boldFont);

		generateSignatureBox(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
				examReportCardLayoutData.getDefaultBorderWidth(), pageNumber, regularFont, boldFont);
	}

	protected float getScholasticMarksGridSubjectWidth(String reportType) {
		if (reportType.equalsIgnoreCase(UNIT_I_REPORT_TYPE) || reportType.equalsIgnoreCase(UNIT_II_REPORT_TYPE)) {
			return 0.45f;
		}
		if (reportType.equalsIgnoreCase(TERM_I_REPORT_TYPE)) {
			return 0.3f;
		}
		return 0.2f;
	}

	protected float getCoScholasticMarksGridSubjectWidth(String reportType) {
		if (reportType.equalsIgnoreCase(UNIT_I_REPORT_TYPE) || reportType.equalsIgnoreCase(UNIT_II_REPORT_TYPE) ||
				reportType.equalsIgnoreCase(TERM_I_REPORT_TYPE)) {
			return 0.45f;
		}
		return 0.4f;
	}

	protected ExamReportCardLayoutData generateReportCardLayoutData(Institute institute, DocumentOutput documentOutput)
			throws IOException {

		DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4);
		float contentFontSize = 11f;
		float defaultBorderWidth = 0.5f;
		Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
		float logoWidth = 75f;
		float logoHeight = 75f;

		int instituteId = institute.getInstituteId();
		return new ExamReportCardLayoutData(document, documentLayoutSetup, null, null, contentFontSize,
				defaultBorderWidth, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId));
	}

	protected void generateMetaDataLayout(ExamReportCardLayoutData examReportCardLayoutData, Institute institute,
			StudentLite studentLite, StudentManager studentManager, String reportType, ExamReportData examReportData,
										  PdfFont regularFont, PdfFont boldFont, int pageNumber, PdfFont timesBoldFont) throws IOException {

		generateHeader(examReportCardLayoutData, studentLite, institute, reportType, 40f,
				examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.88f, boldFont, regularFont,
				timesBoldFont, studentManager);
		generateStudentInformation(examReportCardLayoutData, studentLite, examReportData, regularFont, boldFont);
		generateBorderLayout(examReportCardLayoutData, pageNumber);
	}

	public DocumentLayoutSetup initDocumentLayoutSetup(PageSize defaultPageSize) {
		return initDocumentLayoutSetup(false, defaultPageSize,
				DEFAULT_PAGE_TOP_MARGIN, DEFAULT_PAGE_BOTTOM_MARGIN,
				DEFAULT_PAGE_SIDE_MARGIN, 0f);
	}

	protected void generateHeader(ExamReportCardLayoutData examReportCardLayoutData, StudentLite studentLite,
			Institute institute, String reportType, float offsetX, float offsetY, PdfFont regularFont, PdfFont boldFont,
								  PdfFont timesBoldFont, StudentManager studentManager) throws IOException {

		generateDynamicImageProvider(examReportCardLayoutData, offsetX, offsetY, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);
//		byte[] studentImage = getStudentImage(institute.getInstituteId(), studentLite.getStudentId(), studentManager);
//		if (studentImage != null) {
//			generateImage(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(), studentImage, examReportCardLayoutData.getLogoWidth(), examReportCardLayoutData.getLogoHeight(),
//					examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getWidth() - 115f,
//					offsetY);
//		}
		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);

		int singleContentColumn = 1;
		Table table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(14f).setTextAlignment(TextAlignment.CENTER);

		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getInstituteName())
						.setFontColor(Color.convertRgbToCmyk(new DeviceRgb(57, 65, 158)))),
						cellLayoutSetup.copy().setFontSize(15f).setPdfFont(regularFont));
		cellLayoutSetup.setPdfFont(boldFont);
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getLetterHeadLine1())
						.setFontColor(Color.convertRgbToCmyk(new DeviceRgb(204, 0, 0)))),
				cellLayoutSetup.copy().setFontSize(12f));
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getLetterHeadLine2())
						.setFontColor(Color.convertRgbToCmyk(new DeviceRgb(204, 0, 0)))),
				cellLayoutSetup.copy().setFontSize(12f));

		examReportCardLayoutData.getDocument().add(table);

		addBlankLine(examReportCardLayoutData.getDocument(), false, 2);

		table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);
		String headerExamTitle = "PROGRESS REPORT ";
		headerExamTitle += "(" + studentLite.getStudentSessionData().getShortYearDisplayName() + ")";
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(headerExamTitle).setUnderline()),
				cellLayoutSetup.copy().setFontSize(12f).setPdfFont(boldFont));
		examReportCardLayoutData.getDocument().add(table);

		addBlankLine(examReportCardLayoutData.getDocument(), false, 2);

	}

	protected void generateStudentInformation(ExamReportCardLayoutData examReportCardLayoutData,
			StudentLite studentLite, ExamReportData examReportData, PdfFont regularFont, PdfFont boldFont) throws IOException {

		Document document = examReportCardLayoutData.getDocument();
		DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
		float contentFontSize = examReportCardLayoutData.getContentFontSize();

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.4f, 0.2f, 0.4f });

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize);

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);

		Paragraph studentName = getKeyValueParagraph("Student's Name : ", capitalizeFirstOfAllWords(studentLite.getName()), 57, 65, 158, boldFont, boldFont);
		Paragraph fatherName = getKeyValueParagraph("Father's Name : ", capitalizeFirstOfAllWords(studentLite.getFathersName()), 57, 65, 158, boldFont, boldFont);
		Paragraph dob = getKeyValueParagraph("DOB : ",
				studentLite.getDateOfBirth() == null || studentLite.getDateOfBirth() <= 0 ? ""
						: DateUtils.getFormattedDate(studentLite.getDateOfBirth(), DATE_FORMAT, User.DFAULT_TIMEZONE), 57, 65, 158, boldFont, boldFont);
		Paragraph motherName = getKeyValueParagraph("Mother's Name : ", capitalizeFirstOfAllWords(studentLite.getMothersName()), 57, 65, 158, boldFont, boldFont);
		Paragraph admissionNumber = getKeyValueParagraph("S.R. No. : ", studentLite.getAdmissionNumber(), 57, 65, 158, boldFont, boldFont);
		Paragraph classValue = getKeyValueParagraph("Class : ",
				studentLite.getStudentSessionData().getStandardNameWithSection(), 57, 65, 158, boldFont, boldFont);

		Paragraph attendance = getKeyValueParagraph("Attendance : ", String.valueOf((examReportData.getTotalWorkingDays() == null ? "-" : examReportData.getTotalWorkingDays()) + "/" + (examReportData.getTotalAttendedDays() == null ? "-" : examReportData.getTotalAttendedDays())), 57, 65, 158, boldFont, boldFont);

		Paragraph dateOfResultDeclaration = getKeyValueParagraph("Date Of Result Declaration : ",
				examReportData.getDateOfResultDeclaration() == null ? "-" : DateUtils.getFormattedDate(examReportData.getDateOfResultDeclaration()),
				57, 65, 158, boldFont, boldFont);

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(studentName, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
						new CellData(fatherName, thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(dob, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
						new CellData(motherName, thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(admissionNumber, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
						new CellData(classValue, thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(attendance, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
						new CellData(dateOfResultDeclaration, thirdCellLayoutSetup)));
		document.add(table);

	}

	protected void generateBorderLayout(ExamReportCardLayoutData examReportCardLayoutData, int pageNumber) {
		DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
		PdfCanvas canvas = new PdfCanvas(examReportCardLayoutData.getDocument().getPdfDocument(), pageNumber);
		canvas.rectangle(SQUARE_BORDER_MARGIN - 2, SQUARE_BORDER_MARGIN - 2,
				documentLayoutSetup.getPageSize().getWidth() - (SQUARE_BORDER_MARGIN - 2) * 2,
				documentLayoutSetup.getPageSize().getHeight() - (SQUARE_BORDER_MARGIN - 2) * 2);
		canvas.stroke();
		canvas.setLineWidth(1f);
		canvas.rectangle(SQUARE_BORDER_MARGIN, SQUARE_BORDER_MARGIN,
				documentLayoutSetup.getPageSize().getWidth() - SQUARE_BORDER_MARGIN * 2,
				documentLayoutSetup.getPageSize().getHeight() - SQUARE_BORDER_MARGIN * 2);
		canvas.stroke();
		canvas.setLineWidth(1f);
	}

	protected void generateResultSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize, ExamReportData examReportData, String reportType,
										 PdfFont regularFont, PdfFont boldFont) throws IOException {
		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 1)
				.setTextAlignment(TextAlignment.LEFT);

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.4f, 0.2f, 0.4f });

		Paragraph result = getKeyValueParagraph("Result : ", "-", boldFont);
		if(examReportData.getExamResultStatus() != null){
			switch (examReportData.getExamResultStatus()){
				case PASS:
				case PASS_WITH_GRACE:
					result = getKeyValueParagraph("Result : ", examReportData.getExamResultStatus().getDisplayName(),
							76, 142, 48, boldFont, boldFont);
					break;
				case SUPPLEMENTARY:
					result = getKeyValueParagraph("Result : ", examReportData.getExamResultStatus().getDisplayName(),
							255, 165, 0, boldFont, boldFont);
					break;
				case FAIL:
					result = getKeyValueParagraph("Result : ", examReportData.getExamResultStatus().getDisplayName(),
							255, 0, 0, boldFont, boldFont);
					break;
			}
		}

		Paragraph promotedClass = getKeyValueParagraph("Promoted to Class: ",
				examReportData.getPromotedTo() == null ? "-" : examReportData.getPromotedTo().getStandardName(), 57, 65, 158, boldFont, boldFont);
		Paragraph obtainedMarks = getKeyValueParagraph("Marks Obtained : ",
				examReportData.getTotalObtainedMarks() == null ? "-"
						: examReportData.getTotalObtainedMarks() * 10 % 10 == 0
						? String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10)
						: String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10d), 57, 65, 158, boldFont, boldFont);
		Paragraph totalMarks = getKeyValueParagraph("Total marks : ", examReportData.getTotalMaxMarks() == null ? "-"
				: String.valueOf(Math.round(examReportData.getTotalMaxMarks() * 10) / 10), 57, 65, 158, boldFont, boldFont);
		Paragraph percentage = getKeyValueParagraph("Percentage : ", examReportData.getPercentage() == null ? "-"
				: (Math.round(examReportData.getPercentage() * 100) / 100d) + "%", 57, 65, 158, boldFont, boldFont);

		if(reportType.equalsIgnoreCase("ANNUAL")) {
			addRow(table, documentLayoutSetup, Arrays.asList(new CellData(result, cellLayoutSetup),
					new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(promotedClass, cellLayoutSetup)));
		} else {
			addRow(table, documentLayoutSetup, Arrays.asList(new CellData(result, cellLayoutSetup),
					new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(EMPTY_TEXT, cellLayoutSetup)));
		}

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(obtainedMarks, cellLayoutSetup),
				new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(totalMarks, cellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(percentage, cellLayoutSetup),
				new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(EMPTY_TEXT, cellLayoutSetup)));

		document.add(table);

	}

	protected void generateRemarksSection(ExamReportCardLayoutData examReportCardLayoutData,
			ExamReportData examReportData, PdfFont regularFont, PdfFont boldFont) throws IOException {

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(examReportCardLayoutData.getContentFontSize())
				.setTextAlignment(TextAlignment.LEFT);

		Table remarksTable = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), 1);
		Paragraph remarks = getKeyValueParagraph("Remark : ",
				examReportData.getRemarks() == null ? "" : examReportData.getRemarks());
		addRow(remarksTable, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(remarks),
				cellLayoutSetup);
		examReportCardLayoutData.getDocument().add(remarksTable);
	}

	protected void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize, float defaultBorderWidth, int pageNumber, PdfFont regularFont, PdfFont boldFont) throws IOException {
		int singleContentColumn = 3;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);
		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 1)
				.setTextAlignment(TextAlignment.CENTER);

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Class Teacher"), getParagraph("Exam Controller"),
				getParagraph("Principal")), signatureCellLayoutSetup);
		table.setFixedPosition(30f, 20f, documentLayoutSetup.getPageSize().getWidth() - 100f);
		document.add(table);
	}

	protected Set<UUID> getNonAdditionalSubjects(ExamReportData examReportData) {
		Set<UUID> nonAdditionalSubjects = new HashSet<UUID>();
		for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportData.getCourseTypeExamReportMarksGrid()
				.get(CourseType.SCHOLASTIC).getExamReportCourseMarksRows()) {
			if (!isAdditionalSubject(examReportCourseMarksRow.getCourse(), examReportData.getExamReportStructure())) {
				nonAdditionalSubjects.add(examReportCourseMarksRow.getCourse().getCourseId());
			}
		}
		return nonAdditionalSubjects;
	}

	protected void sortClassExamReports(List<ExamReportData> examReportDataList) {
		Collections.sort(examReportDataList, new Comparator<ExamReportData>() {

			@Override
			public int compare(ExamReportData e1, ExamReportData e2) {
				StandardSections section1 = e1.getStudentLite().getStudentSessionData().getStandardSection();

				StandardSections section2 = e2.getStudentLite().getStudentSessionData().getStandardSection();

				if (section1 != null && section2 != null) {
					int sectionCompare = section1.getSectionName().compareToIgnoreCase(section2.getSectionName());
					if (sectionCompare != 0) {
						return sectionCompare;
					}
				}

				return e1.getStudentLite().getName().compareToIgnoreCase(e2.getStudentLite().getName());
			}
		});
	}

	@Override
	public DocumentOutput generateClassReport(Institute institute, String reportType,
													List<ExamReportData> examReportDataList, String documentName, StudentManager studentManager) {

		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);

			sortClassExamReports(examReportDataList);

			PdfFont regularFont = getCambriaFont();
			PdfFont boldFont = getCambriaBoldFont();

			PdfFont timesBoldFont = getRegularBoldFont();

			int pageNumber = 1;
			for (ExamReportData examReportData : examReportDataList) {
				generateStudentReportCard(examReportCardLayoutData, institute, examReportData, studentManager,
						reportType, regularFont, boldFont, pageNumber, timesBoldFont);

				if (pageNumber != examReportDataList.size()) {
					examReportCardLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				pageNumber++;
			}

			examReportCardLayoutData.getDocument().close();
			return documentOutput;

		} catch (IOException e) {
			e.printStackTrace();
		}

		return null;
	}

	@Override
	public DocumentOutput generateClassResultReport(Institute institute, String reportType, List<ExamReportData> examReportDataList,
													String documentName, StudentManager studentManager, int studentPerPage) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4.rotate());
			Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

			PdfFont boldFont = getRegularBoldFont();
			PdfFont regularFont = getRegularFont();
			CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
			marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(DEFAULT_FONT_SIZE)
					.setBorder(new SolidBorder(DEFAULT_BORDER_WIDTH)).setTextAlignment(TextAlignment.CENTER);

			CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
					.setTextAlignment(TextAlignment.LEFT);

			String instituteName = institute.getInstituteName().toUpperCase();
			String sessionName = "";
			if(!CollectionUtils.isEmpty(examReportDataList)) {
				sessionName = "Session : " + examReportDataList.get(0).getStudentLite().getStudentSessionData().getShortYearDisplayName();
			}
			generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName,
					sessionName, marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));

			sortClassExamReports(examReportDataList);
			int pageNumber = 1;
			for (ExamReportData examReportData : examReportDataList) {
				generateClassReportDataStudentDetails(documentLayoutSetup, document, courseCellLayoutSetup, examReportData.getStudentLite());
				generateScholasticMarksGrid(document,
						documentLayoutSetup, DEFAULT_FONT_SIZE, DEFAULT_BORDER_WIDTH, examReportData, GridConfigs.forOnlyObtainedTotalRow(),
						"Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType),
						null, "#39419e0");
				generateClassReportDataStudentResult(documentLayoutSetup, document, examReportData, courseCellLayoutSetup);
				if (pageNumber % studentPerPage == 0) {
					document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
					generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName, sessionName,
							marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));
				} else {
					addBlankLine(document, false, 1);
				}
				pageNumber++;
			}
			document.close();
			return documentOutput;
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
					reportType, e);
		}
		return null;
	}
}
