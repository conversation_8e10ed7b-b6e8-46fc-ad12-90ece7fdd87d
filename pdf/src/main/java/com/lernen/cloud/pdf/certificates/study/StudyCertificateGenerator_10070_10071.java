/**
 * 
 */
package com.lernen.cloud.pdf.certificates.study;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.layout.Document;
import com.lernen.cloud.core.api.configurations.DocumentPropertiesPreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.lib.student.StudentManager;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.ByteArrayOutputStream;

/**
 * <AUTHOR>
 *
 */
public class StudyCertificateGenerator_10070_10071 extends GlobalStudyCertificateGenerator {
	
	public StudyCertificateGenerator_10070_10071(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(StudyCertificateGenerator_10070_10071.class);

	@Override
	public DocumentOutput generateStudyCertificate(Institute institute, Student student, String documentName, StudentManager studentManager, DocumentPropertiesPreferences documentPropertiesPreferences) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

			DocumentLayoutData documentLayoutData = generateStudyCertificateLayoutData(institute, documentOutput, 70f,
					70f, documentPropertiesPreferences);
			DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
			Document document = documentLayoutData.getDocument();
			PdfFont boldFont = getCambriaBoldFont();
			PdfFont regularFont = getCambriaFont();
			float contentFontSize = documentLayoutData.getContentFontSize();

			generateImages(document, documentLayoutSetup, documentLayoutData, institute, student, studentManager, documentPropertiesPreferences);
			generateHeader(document, documentLayoutSetup, contentFontSize, boldFont, regularFont, student, institute, documentPropertiesPreferences);
			generateContent(document, documentLayoutSetup, contentFontSize, student, boldFont, regularFont);
			generateSignatureBox(document, documentLayoutSetup, contentFontSize, boldFont, regularFont);

			document.close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generate study certificate for student {} ", student.getStudentBasicInfo().getAdmissionNumber(), e);
		}
		return null;
	}

}
