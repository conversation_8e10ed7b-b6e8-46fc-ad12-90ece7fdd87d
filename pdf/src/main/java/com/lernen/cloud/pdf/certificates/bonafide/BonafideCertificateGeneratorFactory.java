/**
 * 
 */
package com.lernen.cloud.pdf.certificates.bonafide;

import java.util.HashMap;
import java.util.Map;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.lernen.cloud.pdf.certificates.study.GlobalStudyCertificateGenerator;
import com.lernen.cloud.pdf.certificates.study.StudyCertificateGenerator10005_10006;
import com.lernen.cloud.pdf.certificates.study.StudyCertificateGenerator10085;
import com.lernen.cloud.pdf.certificates.study.StudyCertificateGenerator10130;
import com.lernen.cloud.pdf.certificates.study.StudyCertificateGenerator10190;
import com.lernen.cloud.pdf.certificates.study.StudyCertificateGenerator10295;
import com.lernen.cloud.pdf.certificates.study.StudyCertificateGenerator_10050_10051;
import com.lernen.cloud.pdf.certificates.study.StudyCertificateGenerator_10070_10071;

/**
 * <AUTHOR>
 *
 */

public class BonafideCertificateGeneratorFactory {
    
	private static final Map<Integer, BonafideCertificateGenerator> BONAFIDE_CERTIFICATE_GENERATOR = new HashMap<>();
	private final AssetProvider assetProvider;

	public BonafideCertificateGeneratorFactory(AssetProvider assetProvider) {
        this.assetProvider = assetProvider;
        initializeGenerators();
    }

    private void initializeGenerators() {
		BONAFIDE_CERTIFICATE_GENERATOR.put(10295, new BonafideCertificateGenerator10295(assetProvider));
	}

	public BonafideCertificateGenerator getBonafideCertificateGenerator(int instituteId) {
		if (!BONAFIDE_CERTIFICATE_GENERATOR.containsKey(instituteId)) {
			return new GlobalBonafideCertificateGenerator(assetProvider);
		}
		return BONAFIDE_CERTIFICATE_GENERATOR.get(instituteId);
	}

}

