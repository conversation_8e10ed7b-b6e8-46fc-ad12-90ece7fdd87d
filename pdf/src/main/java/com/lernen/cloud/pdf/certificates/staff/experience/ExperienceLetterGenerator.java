package com.lernen.cloud.pdf.certificates.staff.experience;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.staff.FullStaffDetails;
import com.lernen.cloud.core.lib.staff.StaffManager;
import com.lernen.cloud.pdf.base.PDFGenerator;

public abstract class ExperienceLetterGenerator extends PDFGenerator {
    public ExperienceLetterGenerator(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	public abstract DocumentOutput generateExperienceLetter(Institute institute, FullStaffDetails fullStaffDetails,
															String documentName, StaffManager staffManager);

	protected void generateMetadata(DocumentLayoutData documentLayoutData, float squareBorderMargin, float innerGap) {
		generateMetadata(documentLayoutData,squareBorderMargin, innerGap,1);
	}

	protected void generateMetadata(DocumentLayoutData documentLayoutData, float squareBorderMargin, float innerGap, int pageNumber) {
		generateBorderLayout(documentLayoutData.getDocument(), documentLayoutData.getDocumentLayoutSetup(), pageNumber,
				squareBorderMargin, innerGap);
	}
}
