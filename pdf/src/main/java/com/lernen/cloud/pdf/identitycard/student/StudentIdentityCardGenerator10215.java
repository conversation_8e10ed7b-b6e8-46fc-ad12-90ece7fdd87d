package com.lernen.cloud.pdf.identitycard.student;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.kernel.pdf.PdfReader;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.kernel.pdf.xobject.PdfFormXObject;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.configurations.StudentIdentityCardPreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.transport.StudentTransportDetails;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.staff.StaffManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.AddressUtils;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.Arrays;
import java.util.List;

/**
 *
 * <AUTHOR>
 *
 */
public class StudentIdentityCardGenerator10215 extends GlobalStudentIdentityCardGenerator {

    public StudentIdentityCardGenerator10215(AssetProvider assetProvider) {
                super(assetProvider);
                //TODO Auto-generated constructor stub
        }

private static final Logger logger = LogManager.getLogger(StudentIdentityCardGenerator10215.class);

    @Override
    public DocumentOutput generateIdentityCard(StudentManager studentManager, Institute institute, StudentTransportDetails studentTransportDetails,
                                               StudentIdentityCardPreferences studentIdentityCardPreferences, Student student, String documentName, StaffManager staffManager) {
        try {
            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            DocumentLayoutData documentLayoutData = generateIdentityCardLayoutData(institute,
                    studentIdentityCardPreferences, documentOutput);
            Document document = documentLayoutData.getDocument();

            PdfFont cambriaBoldFont = getCambriaBoldFont();
            PdfFont cambriaFont = getCambriaFont();

            generateIdentityCardDetails(document, documentLayoutData, cambriaBoldFont, cambriaFont,
                    documentLayoutData.getDocumentLayoutSetup(), institute, student, studentIdentityCardPreferences, studentManager, 1);
            document.close();
            return documentOutput;
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("Error while generating idenity cards for institute {}, student {}",
                    institute.getInstituteId(), student.getStudentId(), e);
        }
        return null;
    }

    private void generateIdentityCardDetails(Document document, DocumentLayoutData documentLayoutData,
                        PdfFont cambriaBoldFont, PdfFont cambriaFont, DocumentLayoutSetup documentLayoutSetup, Institute institute, Student student,
                        StudentIdentityCardPreferences studentIdentityCardPreferences, StudentManager studentManager, int pageNumber) throws IOException {

        /**
         * Institute Header
         */
        generateInstituteHeader(document, documentLayoutData, cambriaBoldFont, cambriaFont,
                documentLayoutSetup, institute, studentIdentityCardPreferences);

        /**
         * Student Details
         */
        generateStudentDetails(document, cambriaFont, cambriaBoldFont, documentLayoutSetup, student, studentIdentityCardPreferences);

        /**
         * Student Image
         */
        generateStudentImage(document, documentLayoutSetup, documentLayoutData, institute, studentManager, student, studentIdentityCardPreferences);

        /**
         * Bottom bar
         */
        generateBottomBar(document, cambriaBoldFont, documentLayoutSetup, studentIdentityCardPreferences);
    }

    protected void generateStudentDetails(Document document, PdfFont cambriaFont, PdfFont cambriaBoldFont, DocumentLayoutSetup documentLayoutSetup,
                                        Student student, StudentIdentityCardPreferences studentIdentityCardPreferences) {

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(cambriaBoldFont).setFontSize(7f)
                .setTextAlignment(TextAlignment.LEFT).setPaddingLeft(10f).setPaddingTop(0f).setPaddingBottom(0f);


        Table table = getPDFTable(documentLayoutSetup, new float [] { 0.6f, 0.4f });
        CellLayoutSetup centerCellLayoutSetup = new CellLayoutSetup();

        List<Integer> rgb = studentIdentityCardPreferences != null &&
                !StringUtils.isEmpty(studentIdentityCardPreferences.getiCardDetailsTextColor()) ?
                EColorUtils.hex2Rgb(studentIdentityCardPreferences.getiCardDetailsTextColor()) : null;
        centerCellLayoutSetup.setPdfFont(cambriaFont).setTextAlignment(TextAlignment.RIGHT).setPaddingRight(12f).setFontSize(7f);

        Paragraph session = getParagraph("Session : " + student.getStudentAcademicSessionInfoResponse()
                .getAcademicSession().getShortYearDisplayName(), rgb);

        Paragraph studentName = getKeyValueParagraph("Name : ",
                student.getStudentBasicInfo().getName().toUpperCase(), rgb, cambriaBoldFont, cambriaFont);

        addRow(table, documentLayoutSetup, Arrays.asList(
                new CellData(studentName, cellLayoutSetup.copy().setPaddingTop(2f)),
                new CellData(session, centerCellLayoutSetup.copy().setPaddingTop(2f))));

        document.add(table);

        table = getPDFTable(documentLayoutSetup, new float [] { 0.7f, 0.3f });

        Paragraph fatherName = getKeyValueParagraph("Father's Name : ", student.getStudentFamilyInfo().getFathersName(),
                rgb, cambriaBoldFont, cambriaFont);
        Paragraph standard = getKeyValueParagraph("Class : ", student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayNameWithSection(),
                rgb, cambriaBoldFont, cambriaFont);

//        Paragraph className = getKeyValueParagraph("Class : ",
//                student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayNameWithSection(),
//                rgb, cambriaBoldFont, cambriaFont);
//
//        Paragraph classRollNumber = getKeyValueParagraph("Class Roll Number : ",
//                student.getStudentAcademicSessionInfoResponse().getRollNumber(),
//                rgb, cambriaBoldFont, cambriaFont);
//
//        Paragraph admissionNumber = getKeyValueParagraph("Admission No. : ",
//                student.getStudentBasicInfo().getAdmissionNumber(),
//                rgb, cambriaBoldFont, cambriaFont);

        Paragraph dob = getKeyValueParagraph("DOB : ",
                student.getStudentBasicInfo().getDateOfBirth() == null
                        || student.getStudentBasicInfo().getDateOfBirth() <= 0 ? ""
                        : DateUtils.getFormattedDate(student.getStudentBasicInfo().getDateOfBirth(),
                        DATE_FORMAT_DOT, User.DFAULT_TIMEZONE), rgb, cambriaBoldFont, cambriaFont);

        Paragraph bloodGroup = getKeyValueParagraph("Blood Group : ",
                student.getStudentMedicalInfo() == null ?
                        "" : student.getStudentMedicalInfo().getBloodGroup() == null ?
                        "" : student.getStudentMedicalInfo().getBloodGroup().getDisplayName(),
                rgb, cambriaBoldFont, cambriaFont);

        Paragraph contactNumber = getKeyValueParagraph("Contact Number : ",
                student.getStudentBasicInfo().getPrimaryContactNumber(),
                rgb, cambriaBoldFont, cambriaFont);

        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(standard, cellLayoutSetup.copy()),
                        new CellData(new Paragraph(""), cellLayoutSetup.copy())));
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(fatherName, cellLayoutSetup.copy()),
                        new CellData(new Paragraph(""), cellLayoutSetup.copy())));
//        addRow(table, documentLayoutSetup,
//                Arrays.asList(new CellData(className, cellLayoutSetup.copy()),
//                        new CellData(new Paragraph(""), cellLayoutSetup.copy())));
//        addRow(table, documentLayoutSetup,
//                Arrays.asList(new CellData(classRollNumber, cellLayoutSetup.copy()),
//                        new CellData(new Paragraph(""), cellLayoutSetup.copy())));
//        addRow(table, documentLayoutSetup,
//                Arrays.asList(new CellData(admissionNumber, cellLayoutSetup.copy()),
//                        new CellData(new Paragraph(""), cellLayoutSetup.copy())));
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(dob, cellLayoutSetup.copy()),
                        new CellData(new Paragraph(""), cellLayoutSetup.copy())));
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(bloodGroup, cellLayoutSetup.copy()),
                        new CellData(new Paragraph(""), cellLayoutSetup.copy())));
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(contactNumber, cellLayoutSetup.copy()),
                        new CellData(new Paragraph(""), cellLayoutSetup.copy())));
        document.add(table);

        table = getPDFTable(documentLayoutSetup, 1);
        Paragraph address = getKeyValueParagraph("Address : ", AddressUtils.getStudentAddress(student),
                rgb, cambriaBoldFont, cambriaFont);
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(address, cellLayoutSetup.copy().setPaddingTop(4f))));
        document.add(table);
    }

    @Override
    public DocumentOutput generateIdentityCards(StudentManager studentManager, Institute institute, List<StudentTransportDetails> studentTransportDetails,
                                                StudentIdentityCardPreferences studentIdentityCardPreferences, List<Student> students,
                                                String documentName, StaffManager staffManager) {
        try {

            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            DocumentLayoutData documentLayoutData = generateIdentityCardLayoutData(institute,
                    studentIdentityCardPreferences, documentOutput);
            Document document = documentLayoutData.getDocument();

            PdfFont cambriaBoldFont = getCambriaBoldFont();
            PdfFont cambriaFont = getCambriaFont();

            int pageNumber = 1;
            for (Student student : students) {

                generateIdentityCardDetails(document, documentLayoutData, cambriaBoldFont, cambriaFont,
                        documentLayoutData.getDocumentLayoutSetup(), institute, student, studentIdentityCardPreferences, studentManager,
                        pageNumber);

                if (pageNumber != students.size()) {
                    documentLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                }
                pageNumber++;

            }

            documentLayoutData.getDocument().close();

            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating student identity card institute {}", institute.getInstituteId(), e);
        }
        return null;
    }
}
