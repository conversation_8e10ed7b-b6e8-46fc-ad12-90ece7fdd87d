package com.lernen.cloud.pdf.exam.reports._10150;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.colors.WebColors;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.*;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.report.*;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.pdf.exam.reports.ExamReportGenerator;
import com.lernen.cloud.pdf.exam.reports.IExamReportCardGenerator;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ExamReportGenerator10150 extends ExamReportGenerator implements IExamReportCardGenerator {

    public ExamReportGenerator10150(AssetProvider assetProvider) {
                super(assetProvider);
                //TODO Auto-generated constructor stub
        }

private static final Logger logger = LogManager.getLogger(ExamReportGenerator10150.class);
    public static final float DEFAULT_PAGE_SIDE_MARGIN = 25f;
    public static final float DEFAULT_PAGE_TOP_MARGIN = 10f;
    public static final float DEFAULT_PAGE_BOTTOM_MARGIN = 10f;
    public static final float SQUARE_BORDER_MARGIN = 12f;

    protected static final String HALF_YEARLY_REPORT_TYPE = "HALF_YEARLY";

    protected static final String ANNUAL_REPORT_TYPE = "ANNUAL";

    protected static final String SCHOLASTIC_EXAM_DESCRIPTION = "HY = Half Yearly, YL = Yearly or Annual Exam";
    private static final String GRADE_A = "Excellent. Keep it up.";
    private static final String GRADE_B = "Good, capable of better performance.";
    private static final String GRADE_C = "Fair, needs to put in more effort to perform better.";
    private static final String GRADE_D = "Satisfactory. More practice and sincere effort is required.";
    private static final String GRADE_E = "Unsatisfactory. Needs to work very hard to come up to required standard of the class.";


    private static final Map<String, String> MARKS_GRADE_MAP = new LinkedHashMap<>();

    static {
        MARKS_GRADE_MAP.put("86 - 100", "A");
        MARKS_GRADE_MAP.put("71 - 85", "B");
        MARKS_GRADE_MAP.put("51 - 70", "C");
        MARKS_GRADE_MAP.put("31 - 50", "D");
        MARKS_GRADE_MAP.put("0 - 30", "E");
    }

    @Override
    public DocumentOutput generateReport(Institute institute, Student student, String reportType,
                                         ExamReportData examReportData, String documentName, StudentManager studentManager) {
        try {

            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

            ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);

            PdfFont regularFont = getCambriaFont();
            PdfFont boldFont = getCambriaBoldFont();

            generateStudentReportCard(examReportCardLayoutData, institute, examReportData, studentManager, reportType,
                    1, boldFont, regularFont, "STUDENT");

            examReportCardLayoutData.getDocument().close();
            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating report card institute {}, student {}, reportType {} ",
                    institute.getInstituteId(), student.getStudentId(), reportType, e);
        }
        return null;
    }

    private void generateStudentReportCard(ExamReportCardLayoutData examReportCardLayoutData,
                                           Institute institute, ExamReportData examReportData,
                                           StudentManager studentManager, String reportType,
                                           int pageNumber, PdfFont boldFont, PdfFont regularFont,
                                           String reportCardFor) throws IOException {

        generateMetaDataLayout(examReportCardLayoutData, institute, examReportData.getStudentLite(), studentManager, reportType,
                examReportData, pageNumber, boldFont, regularFont);


        Set<UUID> nonAdditionalSubjects = getNonAdditionalSubjects(examReportData);
        generateScholasticMarksGrid(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
                regularFont, boldFont, examReportCardLayoutData.getContentFontSize() - 1,
                examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(),
                "Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType),
                nonAdditionalSubjects, "MM", "MO", "#ff0000",
                "-");

        if(!CollectionUtils.isEmpty(examReportData.getExamReportStructure().getExamReportCourseStructure()
                .get(CourseType.SCHOLASTIC).getAdditionalCourses())) {
            addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
            generateAdditionalCustomScholasticMarksGrid(examReportCardLayoutData, examReportData, GridConfigs.forSkipTotalRow(),
                    "Additional Subjects", getScholasticMarksGridSubjectWidth(reportType), boldFont, regularFont);
        }

        generateScholasticExamDescription(examReportCardLayoutData.getDocument(),
                examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
                examReportData, boldFont, regularFont);

        if (examReportData.getCourseTypeExamReportMarksGrid().containsKey(CourseType.COSCHOLASTIC)) {
            addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
            generateCoScholasticMarksGrid(examReportCardLayoutData.getDocument(),
                    examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
                    examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(),
                    "Co-Scholastic Subjects", getCoScholasticMarksGridSubjectWidth(reportType), "-");
        }

        addBlankLine(examReportCardLayoutData, false, 1);

        generateResultSummary(examReportCardLayoutData.getDocument(),
            examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
            examReportData, reportType, regularFont, boldFont, reportCardFor);

        generateRemarksSection(examReportCardLayoutData, examReportData, regularFont, boldFont);

        generateSignatureBox(examReportCardLayoutData.getDocument(),
                examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
                examReportCardLayoutData.getDefaultBorderWidth(), pageNumber, regularFont, boldFont);

        generateBorderLayout(examReportCardLayoutData, pageNumber);
    }

    protected void generateAdditionalCustomScholasticMarksGrid(ExamReportCardLayoutData examReportCardLayoutData,
                                                               ExamReportData examReportData, GridConfigs gridConfigs,
                                                               String subjectColumnTitle, float subjectColumnWidth, PdfFont boldFont, PdfFont regularFont) throws IOException {
        ExamReportStructure examReportStructure = examReportData.getExamReportStructure();
        if (examReportStructure == null || CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure())
                || examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC) == null
                || CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC)
                .getAdditionalCourses())) {
            return;
        }

        generateScholasticMarksGrid(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
                regularFont, boldFont, examReportCardLayoutData.getContentFontSize() - 1,
                examReportCardLayoutData.getDefaultBorderWidth(), examReportData, gridConfigs,
                subjectColumnTitle, subjectColumnWidth,
                examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC).getAdditionalCourses(),
                "MM", "MO", "#ff0000", "-");
    }

    protected float getScholasticMarksGridSubjectWidth(String reportType) {
        return 0.2f;
    }

    protected float getCoScholasticMarksGridSubjectWidth(String reportType) {
        return 0.25f;
    }

    protected ExamReportCardLayoutData generateReportCardLayoutData(Institute institute, DocumentOutput documentOutput)
            throws IOException {

        DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false);
        float contentFontSize = 12f;
        float defaultBorderWidth = 0.5f;
        Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
        float logoWidth = 70f;
        float logoHeight = 70f;

        int instituteId = institute.getInstituteId();
        return new ExamReportCardLayoutData(document, documentLayoutSetup, null, null, contentFontSize,
                defaultBorderWidth, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId));
    }

    protected void generateMetaDataLayout(ExamReportCardLayoutData examReportCardLayoutData, Institute institute,
                                          StudentLite studentLite, StudentManager studentManager, String reportType, ExamReportData examReportData,
                                          int pageNumber, PdfFont boldFont, PdfFont regularFont) throws IOException {

        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
        //Institute watermark
        int instituteId = institute.getInstituteId();
//        float bgImageHeightWidth = 400f;
//        generateBackgroundImage(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
//                WatermarkProvider.INSTANCE.getWatermark(
//                        instituteId), bgImageHeightWidth, bgImageHeightWidth, bgImageHeightWidth / 5 + 20,
//                bgImageHeightWidth / 2);
        generateHeader(examReportCardLayoutData, studentLite, institute, reportType, 40f,
                examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.89f,
                boldFont, regularFont);
        generateStudentInformation(examReportCardLayoutData, studentLite, examReportData, boldFont, regularFont);
    }

    @Override
    public DocumentLayoutSetup initDocumentLayoutSetup(boolean officeCopy) {
        return initDocumentLayoutSetup(officeCopy, officeCopy ? DEFAULT_PAGE_SIZE.rotate() : DEFAULT_PAGE_SIZE,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN : DEFAULT_PAGE_TOP_MARGIN,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN : DEFAULT_PAGE_BOTTOM_MARGIN,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_SIDE_MARGIN : DEFAULT_PAGE_SIDE_MARGIN,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_MIDDLE_MARGIN : 0f);
    }

    protected void generateHeader(ExamReportCardLayoutData examReportCardLayoutData, StudentLite studentLite,
                                  Institute institute, String reportType, float offsetX, float offsetY,
                                  PdfFont boldFont, PdfFont regularFont) throws IOException {

        generateDynamicImageProvider(examReportCardLayoutData, offsetX, offsetY, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

//        generateLogo(examReportCardLayoutData, institute, ImageProvider.INSTANCE.getImage(ImageProvider._CBSE_LOGO_2),
//                examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getWidth() - (2.5f * offsetX), offsetY);

        int singleContentColumn = 1;
        Table table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(boldFont).setFontSize(14f).setTextAlignment(TextAlignment.CENTER);

        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(
                institute.getInstituteName().toUpperCase())
                        .setFontColor(Color.convertRgbToCmyk(EColorUtils.redDeviceRgb))),
                cellLayoutSetup.copy().setFontSize(24f));

        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(
                        institute.getLetterHeadLine1())
                        .setFontColor(Color.convertRgbToCmyk(EColorUtils.darkBlueDeviceRgb))),
                cellLayoutSetup.copy().setFontSize(14f).setPdfFont(regularFont));

        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(
                        institute.getLetterHeadLine2())
                        .setFontColor(Color.convertRgbToCmyk(EColorUtils.darkBlueDeviceRgb))),
                cellLayoutSetup.copy().setFontSize(14f).setPdfFont(regularFont));

        examReportCardLayoutData.getDocument().add(table);
        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
        table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);
        String headerExamTitle = reportType.equalsIgnoreCase(HALF_YEARLY_REPORT_TYPE) ? "Half-Yearly Progress Report"
                : "Annual Progress Report";
        headerExamTitle += " -" + studentLite.getStudentSessionData().getShortYearDisplayName() + "";
        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(headerExamTitle).setUnderline()
                        .setFontColor(new DeviceRgb(EColorUtils.purpleR, EColorUtils.purpleG, EColorUtils.purpleB))),
                cellLayoutSetup.copy().setFontSize(11f).setPdfFont(boldFont));
        examReportCardLayoutData.getDocument().add(table);
        addBlankLine(examReportCardLayoutData.getDocument(), false, 2);
    }

    protected void generateStudentInformation(ExamReportCardLayoutData examReportCardLayoutData,
                                              StudentLite studentLite, ExamReportData examReportData,
                                              PdfFont boldFont, PdfFont regularFont) throws IOException {

        Document document = examReportCardLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
        float contentFontSize = examReportCardLayoutData.getContentFontSize() - 1;

        Table table = getPDFTable(documentLayoutSetup, new float[] { 0.4f, 0.2f, 0.4f });

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize);

        CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
        CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);
        CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);

        Paragraph studentName = getKeyValueParagraph("Student Name : ", studentLite.getName(),
                EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);
        Paragraph fatherName = getKeyValueParagraph("Father Name : ", studentLite.getFathersName(),
                EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);
        Paragraph dob = getKeyValueParagraph("DOB : ",
                studentLite.getDateOfBirth() == null || studentLite.getDateOfBirth() <= 0 ? ""
                        : DateUtils.getFormattedDate(studentLite.getDateOfBirth(), DATE_FORMAT, User.DFAULT_TIMEZONE),
                EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);
        Paragraph motherName = getKeyValueParagraph("Mother Name : ", studentLite.getMothersName(),
                EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);
        Paragraph admissionNumber = getKeyValueParagraph("SR No. : ", studentLite.getAdmissionNumber(),
                EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);
        Paragraph classValue = getKeyValueParagraph("Class : ",
                studentLite.getStudentSessionData().getStandardNameWithSection(),
                EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);

        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(studentName, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
                        new CellData(fatherName, thirdCellLayoutSetup)));
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(dob, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
                        new CellData(motherName, thirdCellLayoutSetup)));
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(admissionNumber, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
                        new CellData(classValue, thirdCellLayoutSetup)));
        document.add(table);

        addBlankLine(examReportCardLayoutData.getDocument(), false, 2);
    }

    protected void generateBorderLayout(ExamReportCardLayoutData examReportCardLayoutData, int pageNumber) {
        DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
        PdfCanvas canvas = new PdfCanvas(examReportCardLayoutData.getDocument().getPdfDocument(), pageNumber);
        Color color = WebColors.getRGBColor("#df5143");
        canvas.setColor(color, false);
        canvas.setLineWidth(3f);
        canvas.rectangle(SQUARE_BORDER_MARGIN - 2, SQUARE_BORDER_MARGIN - 2,
                documentLayoutSetup.getPageSize().getWidth() - (SQUARE_BORDER_MARGIN - 2) * 2,
                documentLayoutSetup.getPageSize().getHeight() - (SQUARE_BORDER_MARGIN - 2) * 2);
        canvas.stroke();
        canvas.rectangle(SQUARE_BORDER_MARGIN, SQUARE_BORDER_MARGIN,
                documentLayoutSetup.getPageSize().getWidth() - SQUARE_BORDER_MARGIN * 2,
                documentLayoutSetup.getPageSize().getHeight() - SQUARE_BORDER_MARGIN * 2);
        canvas.stroke();
    }

    protected void generateScholasticExamDescription(Document document, DocumentLayoutSetup documentLayoutSetup,
                                                     float contentFontSize, ExamReportData examReportData, PdfFont boldFont, PdfFont regularFont) throws IOException {

        Text descTitle = new Text("Description: " + SCHOLASTIC_EXAM_DESCRIPTION).setFont(regularFont).setFontSize(contentFontSize - 3);
        Paragraph desc = new Paragraph();
        desc.add(descTitle);
        document.add(desc.setFontColor(new DeviceRgb(EColorUtils.purpleR, EColorUtils.purpleG, EColorUtils.purpleB)));
    }

    protected void generateResultSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
                                         float contentFontSize, ExamReportData examReportData,
                                         String reportType, PdfFont regularFont, PdfFont boldFont,
                                         String reportCardFor) throws IOException {

        addBlankLine(document, false, 1);
        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.LEFT).setPaddingTop(2f).setPaddingBottom(2f);

        Table table = getPDFTable(documentLayoutSetup, new float[]{0.4f, 0.2f, 0.4f});

        String attendanceStr = "-";
        if (examReportData.getTotalAttendedDays() != null && (examReportData.getTotalWorkingDays() != null || examReportData.getTotalWorkingDays() != 0)) {
            attendanceStr = examReportData.getTotalAttendedDays() + "/" + examReportData.getTotalWorkingDays();
        }

        Paragraph noOfPresent = getKeyValueParagraph("Attendance : ", attendanceStr,
                EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);
        Paragraph rank = null;
        if (examReportData.getRank() != null && examReportData.getRank() > 0 && examReportData.getRank() <= 10) {
            rank = getKeyValueParagraph("Rank : ", String.valueOf(examReportData.getRank()),
                    EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);
        }


        Paragraph result = getKeyValueParagraph("Result : ", "-", EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);
        if (examReportData.getExamResultStatus() != null) {
            result = getKeyValueParagraph("Result : ", examReportData.getExamResultStatus().getDisplayName(),
                    EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);
        }

        Paragraph promotedClass = getKeyValueParagraph("Promoted to: ",
                examReportData.getPromotedTo() == null ? "-" : examReportData.getPromotedTo().getStandardName(), EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);
        Paragraph obtainedMarks = getKeyValueParagraph("Obtained marks : ",
                examReportData.getTotalObtainedMarks() == null ? "-"
                        : examReportData.getTotalObtainedMarks() * 10 % 10 == 0
                        ? String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10)
                        : String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10d),
                EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);
        Paragraph totalMarks = getKeyValueParagraph("Total marks : ", examReportData.getTotalMaxMarks() == null ? "-"
                : String.valueOf(Math.round(examReportData.getTotalMaxMarks() * 10) / 10),
                EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);
        Paragraph percentage = getKeyValueParagraph("Percentage : ", examReportData.getPercentage() == null ? "-"
                : String.valueOf(Math.round(examReportData.getPercentage() * 100) / 100d) + "%",
                EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);

        Paragraph grade = getKeyValueParagraph("Grade : ",
                examReportData.getTotalGrade() == null ? "-" : examReportData.getTotalGrade().getGradeName(),
                EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);

        if(reportCardFor.equalsIgnoreCase("CLASS")) {
            addRow(table, documentLayoutSetup, Arrays.asList(new CellData(noOfPresent, cellLayoutSetup),
                    new CellData(EMPTY_TEXT, cellLayoutSetup), rank == null ? new CellData(EMPTY_TEXT, cellLayoutSetup)
                            : new CellData(rank, cellLayoutSetup)));
        } else {
            addRow(table, documentLayoutSetup, Arrays.asList(new CellData(noOfPresent, cellLayoutSetup),
                    new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(EMPTY_TEXT, cellLayoutSetup)));
        }

        if (reportType.equalsIgnoreCase("ANNUAL")) {
            addRow(table, documentLayoutSetup, Arrays.asList(new CellData(result, cellLayoutSetup),
                    new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(promotedClass, cellLayoutSetup)));
        }

        addRow(table, documentLayoutSetup, Arrays.asList(new CellData(obtainedMarks, cellLayoutSetup),
                new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(totalMarks, cellLayoutSetup)));
        addRow(table, documentLayoutSetup, Arrays.asList(new CellData(percentage, cellLayoutSetup),
                new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(grade, cellLayoutSetup)));

        document.add(table);

    }

    protected void generateRemarksSection(ExamReportCardLayoutData examReportCardLayoutData,
            ExamReportData examReportData, PdfFont regularFont, PdfFont boldFont) throws IOException {

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(boldFont).setFontSize(examReportCardLayoutData.getContentFontSize())
                .setTextAlignment(TextAlignment.LEFT);

        Table remarksTable = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), 1);

        Paragraph remarks = getKeyValueParagraph("Remarks: ", !StringUtils.isBlank(examReportData.getRemarks())
                ? examReportData.getRemarks() : examReportData.getTotalGrade() == null ? "" :
                        StringUtils.isEmpty(getRemarks(examReportData.getTotalGrade().getGradeName()))
                                ? "" : getRemarks(examReportData.getTotalGrade().getGradeName()),
                EColorUtils.darkBlueColorList, EColorUtils.darkBlueColorList, boldFont, boldFont);

        addRow(remarksTable, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(remarks),
                cellLayoutSetup);
        examReportCardLayoutData.getDocument().add(remarksTable);
    }

    private String getRemarks(String grade) {
        switch (grade) {
            case "A":
                return GRADE_A;
            case "B":
                return GRADE_B;
            case "C":
                return GRADE_C;
            case "D":
                return GRADE_D;
            case "E":
                return GRADE_E;
            default:
                return null;

        }
    }

    protected void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup,
                                        float contentFontSize, float defaultBorderWidth, int pageNumber, PdfFont regularFont, PdfFont boldFont) throws IOException {
        PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);
        canvas.moveTo(12, 95);
        canvas.lineTo(583, 95);
        canvas.setLineWidth(.5f);
        canvas.closePathStroke();
        int singleContentColumn = 3;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);
        CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
        signatureCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.CENTER);

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Class Teacher", EColorUtils.darkBlueColorList, boldFont),
                getParagraph(EMPTY_TEXT, EColorUtils.darkBlueColorList, boldFont),
                getParagraph("Principal", EColorUtils.darkBlueColorList, boldFont)),
                signatureCellLayoutSetup);
        table.setFixedPosition(30f, 95f, documentLayoutSetup.getPageSize().getWidth() - 100f);
        document.add(table);
        generateGradeBox(document, documentLayoutSetup, contentFontSize, defaultBorderWidth, regularFont, boldFont);
    }

    private void generateGradeBox(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
                                  float defaultBorderWidth, PdfFont regularFont, PdfFont boldFont) throws IOException {

        Table table = getPDFTable(documentLayoutSetup, 1);
        CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
        signatureCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 2)
                .setTextAlignment(TextAlignment.CENTER);
//        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Grading Scheme", EColorUtils.darkBlueColorList, boldFont)), signatureCellLayoutSetup);
        table.setFixedPosition(10f, 75f, documentLayoutSetup.getPageSize().getWidth());
        document.add(table);

        float[] columnWidths = new float[]{0.24f, 0.24f, 0.24f, 0.24f};
        Table headerTable = getPDFTable(documentLayoutSetup, columnWidths);
        CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
        marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(contentFontSize - 4)
                .setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);

        addBlankLine(document, false, 1);

        List<CellData> headerList = new ArrayList<>();
        headerList.add(new CellData(getParagraph("MARKS", EColorUtils.darkBlueColorList, boldFont), marksCellLayoutSetup));
        headerList.add(new CellData(getParagraph("GRADE", EColorUtils.darkBlueColorList, boldFont), marksCellLayoutSetup));
        headerList.add(new CellData(getParagraph("MARKS", EColorUtils.darkBlueColorList, boldFont), marksCellLayoutSetup));
        headerList.add(new CellData(getParagraph("GRADE", EColorUtils.darkBlueColorList, boldFont), marksCellLayoutSetup));
        addRow(headerTable, documentLayoutSetup, headerList);

        List<Map.Entry<String, String>> gradesList = new ArrayList<>(MARKS_GRADE_MAP.entrySet());
        for (int index = 0; index < gradesList.size() / 2; index++) {
            List<CellData> row = new ArrayList<>();
            row.add(new CellData(getParagraph(gradesList.get(index).getKey(), EColorUtils.darkBlueColorList, boldFont), marksCellLayoutSetup));
            row.add(new CellData(getParagraph(gradesList.get(index).getValue(), EColorUtils.darkBlueColorList, boldFont), marksCellLayoutSetup));
            row.add(new CellData(getParagraph(gradesList.get(gradesList.size() / 2 + index).getKey(), EColorUtils.darkBlueColorList, boldFont), marksCellLayoutSetup));
            row.add(new CellData(getParagraph(gradesList.get(gradesList.size() / 2 + index).getValue(), EColorUtils.darkBlueColorList, boldFont), marksCellLayoutSetup));
            addRow(headerTable, documentLayoutSetup, row);
        }
        if(gradesList.size() % 2 != 0) {
            List<CellData> row = new ArrayList<>();
            row.add(new CellData(getParagraph(gradesList.get(gradesList.size() - 1).getKey(), EColorUtils.darkBlueColorList, boldFont), marksCellLayoutSetup));
            row.add(new CellData(getParagraph(gradesList.get(gradesList.size() - 1).getValue(), EColorUtils.darkBlueColorList, boldFont), marksCellLayoutSetup));
            row.add(new CellData(getParagraph(EMPTY_TEXT, EColorUtils.darkBlueColorList, boldFont), marksCellLayoutSetup));
            row.add(new CellData(getParagraph(EMPTY_TEXT, EColorUtils.darkBlueColorList, boldFont), marksCellLayoutSetup));
            addRow(headerTable, documentLayoutSetup, row);
        }
        headerTable.setFixedPosition(35f, 15f, documentLayoutSetup.getPageSize().getWidth() - 100f);
        document.add(headerTable);
    }

    protected Set<UUID> getNonAdditionalSubjects(ExamReportData examReportData) {
        Set<UUID> nonAdditionalSubjects = new HashSet<UUID>();
        for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportData.getCourseTypeExamReportMarksGrid()
                .get(CourseType.SCHOLASTIC).getExamReportCourseMarksRows()) {
            if (!isAdditionalSubject(examReportCourseMarksRow.getCourse(), examReportData.getExamReportStructure())) {
                nonAdditionalSubjects.add(examReportCourseMarksRow.getCourse().getCourseId());
            }
        }
        return nonAdditionalSubjects;
    }

    protected void sortClassExamReports(List<ExamReportData> examReportDataList) {
        Collections.sort(examReportDataList, new Comparator<ExamReportData>() {

            @Override
            public int compare(ExamReportData e1, ExamReportData e2) {
                StandardSections section1 = e1.getStudentLite().getStudentSessionData().getStandardSection();

                StandardSections section2 = e2.getStudentLite().getStudentSessionData().getStandardSection();

                if (section1 != null && section2 != null) {
                    int sectionCompare = section1.getSectionName().compareToIgnoreCase(section2.getSectionName());
                    if (sectionCompare != 0) {
                        return sectionCompare;
                    }
                }

                return e1.getStudentLite().getName().compareToIgnoreCase(e2.getStudentLite().getName());
            }
        });
    }

    @Override
    public DocumentOutput generateClassReport(Institute institute, String reportType,
                                              List<ExamReportData> examReportDataList, String documentName, StudentManager studentManager) {

        try {
            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);

            sortClassExamReports(examReportDataList);

            int pageNumber = 1;
            PdfFont regularFont = getCambriaFont();
            PdfFont boldFont = getCambriaBoldFont();

            for (ExamReportData examReportData : examReportDataList) {
                generateStudentReportCard(examReportCardLayoutData, institute, examReportData, studentManager, reportType,
                        pageNumber, boldFont, regularFont, "CLASS");

                if (pageNumber != examReportDataList.size()) {
                    examReportCardLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                }
                pageNumber++;
            }

            examReportCardLayoutData.getDocument().close();
            return documentOutput;

        } catch (IOException e) {
            e.printStackTrace();
        }

        return null;
    }

    @Override
    public DocumentOutput generateClassResultReport(Institute institute, String reportType, List<ExamReportData> examReportDataList,
                                                    String documentName, StudentManager studentManager, int studentPerPage) {
        try {
            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4);
            Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

            PdfFont regularFont = getRegularFont();
            PdfFont boldFont = getRegularBoldFont();

            CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
            marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(DEFAULT_FONT_SIZE)
                    .setBorder(new SolidBorder(DEFAULT_BORDER_WIDTH)).setTextAlignment(TextAlignment.CENTER);

            CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
                    .setTextAlignment(TextAlignment.LEFT);

            String instituteName = institute.getInstituteName().toUpperCase();
            String sessionName = "";
            if(!CollectionUtils.isEmpty(examReportDataList)) {
                sessionName = "Session : " + examReportDataList.get(0).getStudentLite().getStudentSessionData().getShortYearDisplayName();
            }
            generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName,
                    sessionName, marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));

            sortClassExamReports(examReportDataList);
            int pageNumber = 1;
            for (ExamReportData examReportData : examReportDataList) {
                generateClassReportDataStudentDetails(documentLayoutSetup, document, courseCellLayoutSetup, examReportData.getStudentLite());
                generateScholasticMarksGrid(document,
                        documentLayoutSetup, DEFAULT_FONT_SIZE, DEFAULT_BORDER_WIDTH, examReportData, GridConfigs.forOnlyObtainedTotalRow(),
                        "Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType),
                        null, "#ff0000");
                generateClassReportDataStudentResult(documentLayoutSetup, document, examReportData, courseCellLayoutSetup);
                if (pageNumber % studentPerPage == 0) {
                    document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                    generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName, sessionName,
                            marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));
                } else {
                    addBlankLine(document, false, 1);
                }
                pageNumber++;
            }
            document.close();
            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
                    reportType, e);
        }
        return null;
    }
}
