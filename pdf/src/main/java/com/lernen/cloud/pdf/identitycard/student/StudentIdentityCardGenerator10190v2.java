package com.lernen.cloud.pdf.identitycard.student;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.WebColors;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.VerticalAlignment;
import com.lernen.cloud.core.api.configurations.StudentIdentityCardPreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.transport.StudentTransportDetails;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.staff.StaffManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.AddressUtils;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.Arrays;
import java.util.List;

/**
 *
 * <AUTHOR>
 *
 */
public class StudentIdentityCardGenerator10190v2 extends GlobalStudentIdentityCardGenerator {

    public StudentIdentityCardGenerator10190v2(AssetProvider assetProvider) {
                super(assetProvider);
                //TODO Auto-generated constructor stub
        }

private static final Logger logger = LogManager.getLogger(StudentIdentityCardGenerator10190v2.class);
    private static final String ICARD_PRIMARY_COLOR = "#fce5cd";

    @Override
    public DocumentOutput generateIdentityCard(StudentManager studentManager, Institute institute, StudentTransportDetails studentTransportDetails,
                                               StudentIdentityCardPreferences studentIdentityCardPreferences, Student student, String documentName, StaffManager staffManager) {
        try {
            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            DocumentLayoutData documentLayoutData = generateIdentityCardLayoutData(institute, documentOutput);
            Document document = documentLayoutData.getDocument();

            PdfFont cambriaBoldFont = getCambriaBoldFont();
            PdfFont cambriaFont = getCambriaFont();

            generateIdentityCardDetails(document, documentLayoutData, cambriaBoldFont, cambriaFont,
                    documentLayoutData.getDocumentLayoutSetup(), institute, student, studentManager, 1);
            document.close();
            return documentOutput;
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("Error while generating idenity cards for institute {}, student {}",
                    institute.getInstituteId(), student.getStudentId(), e);
        }
        return null;
    }

    private int generateIdentityCardDetails(Document document, DocumentLayoutData documentLayoutData,
                                            PdfFont cambriaBoldFont, PdfFont cambriaFont, DocumentLayoutSetup documentLayoutSetup, Institute institute,
                                            Student student, StudentManager studentManager, int pageNumber) throws IOException {

        /**
         * Student Details Front
         */
        generateStudentFrontPageDetails(document, documentLayoutData, institute, pageNumber, studentManager,
                cambriaFont, cambriaBoldFont, documentLayoutSetup, student);

//        /**
//         * Add new page
//         */
//        document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
//        pageNumber++;
//
//        /**
//         * Student Details Back
//         */
//        generateStudentBackDetails(document, documentLayoutData, pageNumber,
//                cambriaFont, cambriaBoldFont, documentLayoutSetup, institute, studentManager, student);

        return pageNumber;

    }

    private void generateStudentFrontPageDetails(Document document, DocumentLayoutData documentLayoutData,
                                                 Institute institute, int pageNumber, StudentManager studentManager,
                                                 PdfFont cambriaFont, PdfFont cambriaBoldFont,
                                                 DocumentLayoutSetup documentLayoutSetup, Student student) throws IOException {

        /**
         * Bottom bar, keep this on top as we are
         * using canvas in generateInstituteHeader which
         * require document to be created before it,
         * ow error occurred
         */
        generateBottomBar(document, cambriaBoldFont, documentLayoutSetup);

        /**
         * Institute Header
         */
        generateFrontPageHeader(document, documentLayoutData, cambriaBoldFont, cambriaFont,
                documentLayoutSetup, institute, pageNumber, student, studentManager);

        /**
         * Student basic details
         */
        generateStudentBasicDetails(document, cambriaBoldFont, cambriaFont, documentLayoutSetup, student);

        /**
         * principal signature
         */
        float bgImageHeightWidth = 120f;
        generateImage(document, documentLayoutSetup,
                ImageProvider.INSTANCE.getImage(ImageProvider._10190_PRINCIPAL_SIGNATURE_WITH_TEXT),
                30f, 20f,
                (((bgImageHeightWidth / 5) + 10f) * 3) + 22, 10f);
    }

    private void generateStudentBasicDetails(Document document,
                                             PdfFont cambriaBoldFont, PdfFont cambriaFont, DocumentLayoutSetup documentLayoutSetup,
                                             Student student) {

        String fathersName = student.getStudentFamilyInfo().getFathersName();
        String mothersName = student.getStudentFamilyInfo().getMothersName();
        float bottom = 5;

        Table table = getPDFTable(documentLayoutSetup, 1).setPaddingRight(10f).setPaddingLeft(10f);
        table.setFixedPosition(0, bottom, documentLayoutSetup.getPageSize().getWidth());

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(cambriaFont).setFontSize(6.5f)
                .setTextAlignment(TextAlignment.CENTER).setPaddingTop(0f).setPaddingBottom(0f);

        Paragraph studentNameParagraph = getParagraph(student.getStudentBasicInfo().getName().toUpperCase(), 252,229,205);
        addRow(table, documentLayoutSetup, Arrays.asList(new CellData(studentNameParagraph,
                cellLayoutSetup.copy().setPaddingTop(2f).setFontSize(8f).setPdfFont(cambriaBoldFont))));

        cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(cambriaFont).setFontSize(6.5f)
                .setTextAlignment(TextAlignment.CENTER).setPaddingLeft(10f).setPaddingTop(0f).setPaddingBottom(0f);

        Table table2 = getPDFTable(documentLayoutSetup, new float[] {0.3f, 0.7f}).setPaddingRight(5f).setPaddingLeft(5f);

        Paragraph classKeyParagraph = getParagraph("Class", EColorUtils.blackR, EColorUtils.blackG, EColorUtils.blackB);
        Paragraph classValueParagraph = getParagraph(": " + student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayNameWithSection(),
                EColorUtils.blackR, EColorUtils.blackG, EColorUtils.blackB);
        addRow(table2, documentLayoutSetup, Arrays.asList(new CellData(classKeyParagraph,
                        cellLayoutSetup.copy().setFontSize(6f).setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.LEFT).setVerticalAlignment(VerticalAlignment.TOP)),
                new CellData(classValueParagraph, cellLayoutSetup.copy().setFontSize(6f).setTextAlignment(TextAlignment.LEFT))));

        Paragraph fatherNameKeyParagraph = getParagraph("F/Name", EColorUtils.blackR, EColorUtils.blackG, EColorUtils.blackB);
        Paragraph fathersNameValueParagraph = getParagraph(StringUtils.isBlank(fathersName) ? ": " : ": " + fathersName,
                EColorUtils.blackR, EColorUtils.blackG, EColorUtils.blackB);
        addRow(table2, documentLayoutSetup, Arrays.asList(new CellData(fatherNameKeyParagraph,
                        cellLayoutSetup.copy().setFontSize(6f).setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.LEFT).setVerticalAlignment(VerticalAlignment.TOP)),
                new CellData(fathersNameValueParagraph, cellLayoutSetup.copy().setFontSize(6f).setTextAlignment(TextAlignment.LEFT))));

        Paragraph motherNameKeyParagraph = getParagraph("M/Name", EColorUtils.blackR, EColorUtils.blackG, EColorUtils.blackB);
        Paragraph mothersNameValueParagraph = getParagraph(StringUtils.isBlank(mothersName) ? ": " : ": " + mothersName,
                EColorUtils.blackR, EColorUtils.blackG, EColorUtils.blackB);
        addRow(table2, documentLayoutSetup, Arrays.asList(new CellData(motherNameKeyParagraph,
                        cellLayoutSetup.copy().setFontSize(6f).setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.LEFT).setVerticalAlignment(VerticalAlignment.TOP)),
                new CellData(mothersNameValueParagraph, cellLayoutSetup.copy().setFontSize(6f).setTextAlignment(TextAlignment.LEFT))));

        Paragraph idKeyParagraph = getParagraph("Adm. No.", EColorUtils.blackR, EColorUtils.blackG, EColorUtils.blackB);
        Paragraph idValueParagraph = getParagraph(": " + student.getStudentBasicInfo().getAdmissionNumber(), EColorUtils.blackR, EColorUtils.blackG, EColorUtils.blackB);
        addRow(table2, documentLayoutSetup, Arrays.asList(new CellData(idKeyParagraph,
                        cellLayoutSetup.copy().setFontSize(6f).setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.LEFT).setVerticalAlignment(VerticalAlignment.TOP)),
                new CellData(idValueParagraph, cellLayoutSetup.copy().setFontSize(6f).setTextAlignment(TextAlignment.LEFT))));

        Paragraph dobKeyParagraph = getParagraph("DOB", EColorUtils.blackR, EColorUtils.blackG, EColorUtils.blackB);
        Paragraph dobValueParagraph = getParagraph(student.getStudentBasicInfo().getDateOfBirth() == null
                || student.getStudentBasicInfo().getDateOfBirth() <= 0 ? EMPTY_TEXT
                : ": " + DateUtils.getFormattedDate(student.getStudentBasicInfo().getDateOfBirth(),
                "dd MMM yyyy", User.DFAULT_TIMEZONE), EColorUtils.blackR, EColorUtils.blackG, EColorUtils.blackB);
        addRow(table2, documentLayoutSetup, Arrays.asList(new CellData(dobKeyParagraph,
                        cellLayoutSetup.copy().setFontSize(6f).setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.LEFT).setVerticalAlignment(VerticalAlignment.TOP)),
                new CellData(dobValueParagraph, cellLayoutSetup.copy().setFontSize(6f).setTextAlignment(TextAlignment.LEFT))));

        Paragraph mobileKeyParagraph = getParagraph("Mobile", EColorUtils.blackR, EColorUtils.blackG, EColorUtils.blackB);
        Paragraph mobileValueParagraph = getParagraph(StringUtils.isBlank(student.getStudentBasicInfo().getPrimaryContactNumber())
                        ? ": " : ": " + student.getStudentBasicInfo().getPrimaryContactNumber(),
                EColorUtils.blackR, EColorUtils.blackG, EColorUtils.blackB);
        addRow(table2, documentLayoutSetup, Arrays.asList(new CellData(mobileKeyParagraph,
                        cellLayoutSetup.copy().setFontSize(6f).setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.LEFT).setVerticalAlignment(VerticalAlignment.TOP)),
                new CellData(mobileValueParagraph, cellLayoutSetup.copy().setFontSize(6f).setTextAlignment(TextAlignment.LEFT))));

        String address = AddressUtils.getStudentAddress(student);
        if(StringUtils.isEmpty(address)){
            address = "";
        }
        Paragraph addressKeyParagraph = getParagraph("Address", EColorUtils.blackR, EColorUtils.blackG, EColorUtils.blackB);
        Paragraph addressValueParagraph = getParagraph(": " + address, EColorUtils.blackR, EColorUtils.blackG, EColorUtils.blackB);
        addRow(table2, documentLayoutSetup, Arrays.asList(new CellData(addressKeyParagraph,
                        cellLayoutSetup.copy().setFontSize(6f).setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.LEFT).setVerticalAlignment(VerticalAlignment.TOP)),
                new CellData(addressValueParagraph, cellLayoutSetup.copy().setFontSize(6f).setTextAlignment(TextAlignment.LEFT).setPaddingRight(25f))));

        Cell cell1 = new Cell();
        cell1.setBorder(null);
        table.addCell(cell1);

        Cell cell2 = new Cell();
        cell2.add(table2);
        cell2.setBorder(null);
        table.addCell(cell2);

        document.add(table);
    }

    private void generateBottomBar(Document document, PdfFont cambriaBoldFont, DocumentLayoutSetup documentLayoutSetup) {

        Table table = getPDFTable(documentLayoutSetup, 1);
        CellLayoutSetup bottomBarCellLayoutSetup = new CellLayoutSetup();
        bottomBarCellLayoutSetup.setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.CENTER)
                .setPaddingBottom(2f).setPaddingTop(2f)
                .setBackgroundColor(ICARD_PRIMARY_COLOR);

        table.setFixedPosition(0f, 0f, documentLayoutSetup.getPageSize().getWidth());
        addRow(table, documentLayoutSetup, Arrays.asList(
                new CellData(EMPTY_TEXT, bottomBarCellLayoutSetup.copy())));

        document.add(table);
    }

    private void generateFrontPageHeader(Document document,
                                         DocumentLayoutData documentLayoutData, PdfFont cambriaBoldFont, PdfFont cambriaFont,
                                         DocumentLayoutSetup documentLayoutSetup, Institute institute, int pageNumber,
                                         Student student, StudentManager studentManager) throws IOException {

        generateBackgroundCanvas(document, documentLayoutSetup, pageNumber);

        generateStudentImage(document, documentLayoutSetup, institute, studentManager, student);

        generateInstituteDetails(document, documentLayoutData, cambriaBoldFont, cambriaFont,
                documentLayoutSetup, institute);
    }

    private void generateInstituteDetails(Document document,
                                          DocumentLayoutData documentLayoutData, PdfFont cambriaBoldFont, PdfFont cambriaFont,
                                          DocumentLayoutSetup documentLayoutSetup, Institute institute) throws IOException {

        Table table = getPDFTable(documentLayoutSetup, 1);
        CellLayoutSetup centerCellLayoutSetup = new CellLayoutSetup();
        centerCellLayoutSetup.setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.LEFT)
                .setPaddingLeft(40f).setBackgroundColor(ICARD_PRIMARY_COLOR).setPaddingTop(0f).setPaddingBottom(0f);

        String[] instituteNameArr = institute.getInstituteName().split(" ");
        String firstLineInstituteName = instituteNameArr[0].toUpperCase();
        firstLineInstituteName = "SANSKAR";
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(firstLineInstituteName, EColorUtils.blackR, EColorUtils.blackG, EColorUtils.blackB).setMultipliedLeading(1)),
                centerCellLayoutSetup.copy().setFontSize(11f).setPaddingTop(5f));

        int length = instituteNameArr.length;
        String[] instituteNameRestArr = Arrays.copyOfRange(instituteNameArr, 1, length);
        String instituteNameRest = String.join(" ", instituteNameRestArr).toUpperCase();
        instituteNameRest = "INTERNATIONAL SCHOOL";
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(instituteNameRest, EColorUtils.blackR, EColorUtils.blackG, EColorUtils.blackB)),
                centerCellLayoutSetup.copy().setFontSize(8f));

        CellLayoutSetup addressCellLayoutSetup = centerCellLayoutSetup.copy().setPdfFont(cambriaFont)
                .setFontSize(4f).setTextAlignment(TextAlignment.LEFT);
        String addressLine1 = "AFFILIATED TO C.B.S.E. NEW DELHI (AFF No. 1730661)";
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(addressLine1, EColorUtils.blackR, EColorUtils.blackG, EColorUtils.blackB)), addressCellLayoutSetup.copy().setPaddingTop(.5f));
        document.add(table);

        generateDynamicImageProvider(documentLayoutData, -8, 4, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);
    }

    private void generateBackgroundCanvas(Document document, DocumentLayoutSetup documentLayoutSetup, int pageNumber) {

        generateBackgroundRectangle(document, documentLayoutSetup, pageNumber);

        generateImageBackground(document, documentLayoutSetup, pageNumber);

    }

    private void generateImageBackground(Document document, DocumentLayoutSetup documentLayoutSetup, int pageNumber) {

        PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);
        Color color = WebColors.getRGBColor(ICARD_PRIMARY_COLOR);
        canvas.setFillColor(color);
        canvas.setStrokeColor(color);

        canvas.moveTo(documentLayoutSetup.getPageSize().getWidth() -
                (documentLayoutSetup.getPageSize().getWidth() * (0.75)), documentLayoutSetup.getPageSize().getHeight());
        canvas.lineTo(documentLayoutSetup.getPageSize().getWidth() -
                (documentLayoutSetup.getPageSize().getWidth() * (0.25)), documentLayoutSetup.getPageSize().getHeight());
        canvas.lineTo(documentLayoutSetup.getPageSize().getWidth() -
                (documentLayoutSetup.getPageSize().getWidth() * (0.25)), documentLayoutSetup.getPageSize().getHeight()
                - ((documentLayoutSetup.getPageSize().getHeight() / 2) + 20));
        canvas.lineTo(documentLayoutSetup.getPageSize().getWidth() -
                (documentLayoutSetup.getPageSize().getWidth() * (0.75)), documentLayoutSetup.getPageSize().getHeight()
                - ((documentLayoutSetup.getPageSize().getHeight() / 2) + 20));
        canvas.lineTo(documentLayoutSetup.getPageSize().getWidth() -
                (documentLayoutSetup.getPageSize().getWidth() * (0.75)), documentLayoutSetup.getPageSize().getHeight());

        canvas.setLineWidth(5f);
        canvas.fill();
        canvas.closePathStroke();
    }

    private void generateBackgroundRectangle(Document document, DocumentLayoutSetup documentLayoutSetup, int pageNumber) {
        PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);

        Color color = WebColors.getRGBColor(ICARD_PRIMARY_COLOR);
        canvas.setFillColor(color);
        canvas.setStrokeColor(color);

        canvas.moveTo(0, documentLayoutSetup.getPageSize().getHeight());
        canvas.lineTo(documentLayoutSetup.getPageSize().getWidth(), documentLayoutSetup.getPageSize().getHeight());
        canvas.lineTo(documentLayoutSetup.getPageSize().getWidth(), documentLayoutSetup.getPageSize().getHeight()
                - ((documentLayoutSetup.getPageSize().getHeight() / 2) - 15));
        canvas.lineTo(0, documentLayoutSetup.getPageSize().getHeight()
                - ((documentLayoutSetup.getPageSize().getHeight() / 2) - 15));
        canvas.lineTo(0, documentLayoutSetup.getPageSize().getHeight());
        canvas.setLineWidth(5f);
        canvas.fill();
        canvas.closePathStroke();
    }

    private void generateStudentImage(Document document, DocumentLayoutSetup documentLayoutSetup,
                                      Institute institute, StudentManager studentManager, Student student) throws MalformedURLException {

        byte[] image = getStudentImage(institute.getInstituteId(), student, studentManager);
        if (image == null) {
            return;
        }

        float imageWidth = (documentLayoutSetup.getPageSize().getWidth() / 2) - 10f;
        float imageHeight = (documentLayoutSetup.getPageSize().getWidth() / 2);

        float imageOffsetX = documentLayoutSetup.getPageSize().getWidth()
                - (documentLayoutSetup.getPageSize().getWidth() * (0.75f)) + 5f;
        float imageOffsetY = documentLayoutSetup.getPageSize().getHeight()
                - ((documentLayoutSetup.getPageSize().getHeight() / 2) + 20) + 5f;

        generateImage(document, documentLayoutSetup, image, imageWidth, imageHeight,
                imageOffsetX, imageOffsetY);

    }

    protected DocumentLayoutData generateIdentityCardLayoutData(Institute institute, DocumentOutput documentOutput)
            throws IOException {

        /**
         * 	aadhar card size - 8.5cmx5.5cm
         * 	in inches - 3.34646x2.16535
         * 	1 inch  = 72 points
         * 	3.34646*72 & 2.16535*72
         * 	240.94512f X 155.9052f
         */
        PageSize pageSize = new PageSize(155.9052f, 240.94512f);
        DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(pageSize);
        float contentFontSize = 9f;
        Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

        float logoWidth = 30f;
        float logoHeight = 30f;

        int instituteId = institute.getInstituteId();
        instituteId = 10190;
        DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getRegularFont(),
                getRegularBoldFont(),
                contentFontSize, 0f, logoWidth, logoHeight,
                LogoProvider.INSTANCE.getLogo(instituteId),
                null, null);

        documentLayoutData.setImageFrame(ImageProvider.INSTANCE.getImage(ImageProvider.IMAGE_FRAME));
        return documentLayoutData;
    }

    public DocumentLayoutSetup initDocumentLayoutSetup(PageSize defaultPageSize) {
        return initDocumentLayoutSetup(false, defaultPageSize,
                0f, 0f, 0f, 0f);
    }

    @Override
    public DocumentOutput generateIdentityCards(StudentManager studentManager, Institute institute, List<StudentTransportDetails> studentTransportDetails,
                                                StudentIdentityCardPreferences studentIdentityCardPreferences, List<Student> students,
                                                String documentName, StaffManager staffManager) {
        try {

            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            DocumentLayoutData documentLayoutData = generateIdentityCardLayoutData(institute, documentOutput);
            Document document = documentLayoutData.getDocument();

            PdfFont cambriaBoldFont = getCambriaBoldFont();
            PdfFont cambriaFont = getCambriaFont();

            int pageNumber = 1;
            for (Student student : students) {

                pageNumber = generateIdentityCardDetails(document, documentLayoutData, cambriaBoldFont, cambriaFont,
                        documentLayoutData.getDocumentLayoutSetup(), institute, student, studentManager, pageNumber);

                if (pageNumber != students.size() * 2) {
                    documentLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                }
                pageNumber++;

            }

            documentLayoutData.getDocument().close();

            return getA4PortraitIdentityCard(documentName, documentOutput, PageSize.A4, 3, 3, 10f);
        } catch (Exception e) {
            logger.error("Error while generating student identity card institute {}", institute.getInstituteId(), e);
        }
        return null;
    }

}
