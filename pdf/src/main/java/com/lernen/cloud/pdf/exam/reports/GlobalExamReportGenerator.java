//package com.lernen.cloud.pdf.exam.reports;
//
//import java.io.ByteArrayOutputStream;
//import java.io.IOException;
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.List;
//import java.util.Map;
//
//import org.springframework.util.CollectionUtils;
//
//import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
//import com.itextpdf.layout.Document;
//import com.itextpdf.layout.borders.SolidBorder;
//import com.itextpdf.layout.element.Paragraph;
//import com.itextpdf.layout.element.Table;
//import com.itextpdf.layout.properties.TextAlignment;
//import com.lernen.cloud.core.api.course.CourseType;
//import com.lernen.cloud.core.api.documents.DocumentOutput;
//import com.lernen.cloud.core.api.examination.ExamDimensionObtainedValues;
//import com.lernen.cloud.core.api.examination.report.ExamReportCourseMarksRow;
//import com.lernen.cloud.core.api.examination.report.ExamReportData;
//import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
//import com.lernen.cloud.core.api.examination.report.ExamReportMarksColumn;
//import com.lernen.cloud.core.api.examination.report.ExamReportMarksGrid;
//import com.lernen.cloud.core.api.institute.Institute;
//import com.lernen.cloud.core.api.pdf.CellData;
//import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
//import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
//import com.lernen.cloud.core.api.student.Student;
//import com.lernen.cloud.core.api.user.User;
//import com.lernen.cloud.core.lib.document.DocumentManager;
//import com.lernen.cloud.core.lib.student.StudentManager;
//import com.lernen.cloud.core.utils.DateUtils;
//import com.lernen.cloud.core.utils.images.LogoProvider;
//import com.lernen.cloud.pdf.base.PDFGenerator;
//
///**
// * 
// * <AUTHOR>
// *
// */
//public class GlobalExamReportGenerator extends PDFGenerator implements IExamReportCardGenerator {
//
//	public static final float DEFAULT_PAGE_SIDE_MARGIN = 40f;
//	// public static final float DEFAULT_PAGE_RIGHT_MARGIN = 5f;
//	public static final float DEFAULT_PAGE_TOP_MARGIN = 20f;
//	public static final float DEFAULT_PAGE_BOTTOM_MARGIN = 20f;
//
//	@Override
//	public DocumentOutput generateReport(Institute institute, Student student, String reportType,
//			ExamReportData examReportData, String documentName, StudentManager studentManager) {
//		int instituteId = institute.getInstituteId();
//		try {
//			Map<CourseType, ExamReportMarksGrid> courseTypeExamReportMarksGrid = examReportData
//					.getCourseTypeExamReportMarksGrid();
//			DocumentOutput invoice = new DocumentOutput(documentName, new ByteArrayOutputStream());
//			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false);
//			float contentFontSize = 12f;
//			float defaultBorderWidth = 0.1f;
//
//			Document document = initDocument(invoice.getContent(), documentLayoutSetup);
//
//			generateLogo(document, documentLayoutSetup, LogoProvider.INSTANCE.getLogo(instituteId));
//			generateHeader(document, documentLayoutSetup, student, institute);
//			generateStudentInformation(document, documentLayoutSetup, contentFontSize, student);
//			if (courseTypeExamReportMarksGrid.containsKey(CourseType.SCHOLASTIC)) {
//				generateScholasticMarksGrid(document, documentLayoutSetup, contentFontSize, defaultBorderWidth,
//						courseTypeExamReportMarksGrid.get(CourseType.SCHOLASTIC));
//
//			}
//			if (courseTypeExamReportMarksGrid.containsKey(CourseType.COSCHOLASTIC)) {
//				generateCoScholasticMarksGrid(document, documentLayoutSetup, contentFontSize, defaultBorderWidth,
//						courseTypeExamReportMarksGrid.get(CourseType.COSCHOLASTIC));
//
//			}
//
//			// generateCoScholasticMarksGrid(document, documentLayoutSetup,
//			// contentFontSize, defaultBorderWidth);
//			// generateContent(document, documentLayoutSetup, contentFontSize,
//			// student, institute);
//			generateSignatureBox(document, documentLayoutSetup, contentFontSize);
//			PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), 1);
//			canvas.rectangle(10, 10, documentLayoutSetup.getPageSize().getWidth() - 20,
//					documentLayoutSetup.getPageSize().getHeight() - 20);
//			canvas.stroke();
//			canvas.setLineWidth(1f);
//			canvas.rectangle(12, 12, documentLayoutSetup.getPageSize().getWidth() - 24,
//					documentLayoutSetup.getPageSize().getHeight() - 24);
//			canvas.stroke();
//			canvas.setLineWidth(1f);
//			document.close();
//			return invoice;
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		return null;
//	}
//
//	@Override
//	public DocumentLayoutSetup initDocumentLayoutSetup(boolean officeCopy) {
//		return initDocumentLayoutSetup(officeCopy, officeCopy ? DEFAULT_PAGE_SIZE.rotate() : DEFAULT_PAGE_SIZE,
//				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN : DEFAULT_PAGE_TOP_MARGIN,
//				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN : DEFAULT_PAGE_BOTTOM_MARGIN,
//				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_SIDE_MARGIN : DEFAULT_PAGE_SIDE_MARGIN,
//				officeCopy ? DEFAULT_DOUBLE_CONTENT_MIDDLE_MARGIN : 0f);
//	}
//
//	private void generateHeader(Document document, DocumentLayoutSetup documentLayoutSetup, Student student,
//			Institute institute) throws IOException {
//		int singleContentColumn = 1;
//		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);
//
//		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
//		cellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(14f).setTextAlignment(TextAlignment.CENTER);
//
//		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getInstituteName())), cellLayoutSetup);
//		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine1())),
//				cellLayoutSetup);
//		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getLetterHeadLine2())),
//				cellLayoutSetup);
//		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(NEXT_LINE)), cellLayoutSetup);
//		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("SCHOLAR'S ANNUAL REPORT CARD")),
//				cellLayoutSetup);
//		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(NEXT_LINE)), cellLayoutSetup);
//		document.add(table);
//
//	}
//
//	private void generateStudentInformation(Document document, DocumentLayoutSetup documentLayoutSetup,
//			float contentFontSize, Student student) throws IOException {
//
//		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.6f, 0.15f, 0.25f });
//
//		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
//		cellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize);
//
//		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
//		CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);
//		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
//
//		Paragraph admissionNumber = getKeyValueParagraph("Admission No. ",
//				student.getStudentBasicInfo().getAdmissionNumber());
//
//		Paragraph studentName = getKeyValueParagraph("Student Name : ", student.getStudentBasicInfo().getName());
//
//		Paragraph fatherName = getKeyValueParagraph("Father Name : ", student.getStudentFamilyInfo().getFathersName());
//
//		Paragraph motherName = getKeyValueParagraph("Mother Name : ", student.getStudentFamilyInfo().getMothersName());
//
//		Paragraph session = getKeyValueParagraph("Session : ",
//				student.getStudentAcademicSessionInfoResponse().getAcademicSession().getYearDisplayName());
//
//		Paragraph classValue = getKeyValueParagraph("Class : ",
//				student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayName());
//
//		Paragraph dob = getKeyValueParagraph("DOB : ", DateUtils
//				.getFormattedDate(student.getStudentBasicInfo().getDateOfBirth(), DATE_FORMAT, User.DFAULT_TIMEZONE));
//
//		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(studentName, firstCellLayoutSetup),
//				new CellData(new Paragraph(""), secondCellLayoutSetup), new CellData(session, thirdCellLayoutSetup)));
//
//		addRow(table, documentLayoutSetup,
//				Arrays.asList(new CellData(admissionNumber, firstCellLayoutSetup),
//						new CellData(new Paragraph(""), secondCellLayoutSetup),
//						new CellData(classValue, thirdCellLayoutSetup)));
//
//		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(fatherName, firstCellLayoutSetup),
//				new CellData(new Paragraph(""), secondCellLayoutSetup), new CellData(dob, thirdCellLayoutSetup)));
//
//		addRow(table, documentLayoutSetup,
//				Arrays.asList(new CellData(motherName, firstCellLayoutSetup),
//						new CellData(new Paragraph(""), secondCellLayoutSetup),
//						new CellData(new Paragraph(""), thirdCellLayoutSetup)));
//
//		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(NEXT_LINE)), cellLayoutSetup);
//
//		document.add(table);
//
//	}
//
//	private float[] getMarksGridWidth(ExamReportMarksGrid examReportMarksGrid) {
//		int count = 0;
//		for (ExamReportMarksColumn examReportMarksColumn : examReportMarksGrid.getExamReportMarksHeaderColumns()) {
//			count += examReportMarksColumn.getExamDimensionObtainedValues().size();
//		}
//		float[] widths = new float[count + 1];
//		widths[0] = 0.15f;
//		for (int i = 0; i < count; i++) {
//			widths[i + 1] = (1 - widths[0]) / count;
//		}
//		return widths;
//	}
//
//	private List<CellData> getMarksHeaderRow(ExamReportMarksGrid examReportMarksGrid, float contentFontSize,
//			float defaultBorderWidth) throws IOException {
//		List<CellData> firstRowCellDatas = new ArrayList<>();
//		List<CellData> secondRowCellDatas = new ArrayList<>();
//		List<CellData> thirdRowCellDatas = new ArrayList<>();
//		CellLayoutSetup headerCellLayoutSetup = new CellLayoutSetup();
//		headerCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
//				.setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);
//		CellLayoutSetup firstColumnCellLayoutSetup = headerCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
//
//		boolean dimensionExists = false;
//		for (ExamReportMarksColumn examReportMarksColumn : examReportMarksGrid.getExamReportMarksHeaderColumns()) {
//			if (examReportMarksColumn.getExamDimensionObtainedValues().size() > 1) {
//				dimensionExists = true;
//				break;
//			}
//		}
//		int rowSpan = dimensionExists ? 3 : 2;
//		firstRowCellDatas.add(new CellData("Course", firstColumnCellLayoutSetup, rowSpan, 1));
//
//		for (ExamReportMarksColumn examReportMarksColumn : examReportMarksGrid.getExamReportMarksHeaderColumns()) {
//			boolean dimensionColumn = examReportMarksColumn.getExamDimensionObtainedValues().size() > 1;
//
//			firstRowCellDatas.add(new CellData(
//					examReportMarksColumn.isSumColumn() ? examReportMarksColumn.getSumColumnName()
//							: examReportMarksColumn.getExamMetaData().getExamName(),
//					headerCellLayoutSetup, 1, examReportMarksColumn.getExamDimensionObtainedValues().size()));
//
//			if (!dimensionColumn) {
//				secondRowCellDatas.add(new CellData(
//						examReportMarksColumn.getExamDimensionObtainedValues().get(0).getMaxMarks() == null ? "-"
//								: String.valueOf(Math.round(
//										examReportMarksColumn.getExamDimensionObtainedValues().get(0).getMaxMarks())),
//						headerCellLayoutSetup, dimensionExists ? 2 : 1,
//						examReportMarksColumn.getExamDimensionObtainedValues().size()));
//				continue;
//			}
//			for (ExamDimensionObtainedValues examDimensionObtainedValues : examReportMarksColumn
//					.getExamDimensionObtainedValues()) {
//				secondRowCellDatas.add(new CellData(examDimensionObtainedValues.getExamDimension().getDimensionName(),
//						headerCellLayoutSetup));
//				thirdRowCellDatas.add(new CellData(
//						examDimensionObtainedValues.getMaxMarks() == null ? "-"
//								: String.valueOf(Math.round(examDimensionObtainedValues.getMaxMarks())),
//						headerCellLayoutSetup));
//			}
//		}
//		firstRowCellDatas.addAll(secondRowCellDatas);
//		firstRowCellDatas.addAll(thirdRowCellDatas);
//		return firstRowCellDatas;
//	}
//
//	private void generateScholasticMarksGrid(Document document, DocumentLayoutSetup documentLayoutSetup,
//			float contentFontSize, float defaultBorderWidth, ExamReportMarksGrid examReportMarksGrid)
//			throws IOException {
//
//		Table headerTable = getPDFTable(documentLayoutSetup, getMarksGridWidth(examReportMarksGrid));
//
//		CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
//		marksCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize)
//				.setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);
//
//		CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
//
//		addRow(headerTable, documentLayoutSetup,
//				getMarksHeaderRow(examReportMarksGrid, contentFontSize - 2, defaultBorderWidth));
//
//		for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportMarksGrid.getExamReportCourseMarksRows()) {
//			if (CollectionUtils.isEmpty(examReportCourseMarksRow.getExamReportMarksColumns())) {
//				continue;
//			}
//			List<CellData> row = new ArrayList<>();
//			row.add(new CellData(examReportCourseMarksRow.getCourse().getCourseName(), courseCellLayoutSetup));
//			for (ExamReportMarksColumn examReportMarksColumn : examReportCourseMarksRow.getExamReportMarksColumns()) {
//				for (ExamDimensionObtainedValues examDimensionObtainedValues : examReportMarksColumn
//						.getExamDimensionObtainedValues()) {
//					row.add(new CellData(
//							String.valueOf(Math.round(examDimensionObtainedValues.getObtainedMarks() == null ? 0d
//									: examDimensionObtainedValues.getObtainedMarks())),
//							marksCellLayoutSetup));
//				}
//			}
//			addRow(headerTable, documentLayoutSetup, row);
//		}
//
//		addRow(headerTable, documentLayoutSetup, Arrays.asList(getParagraph(NEXT_LINE)), new CellLayoutSetup());
//
//		document.add(headerTable);
//
//	}
//
//	private void generateCoScholasticMarksGrid(Document document, DocumentLayoutSetup documentLayoutSetup,
//			float contentFontSize, float defaultBorderWidth, ExamReportMarksGrid examReportMarksGrid)
//			throws IOException {
//
//		Table feeHeadTable = getPDFTable(documentLayoutSetup, new float[] { 0.5f, 0.5f });
//
//		CellLayoutSetup feeHeadCellLayoutSetup = new CellLayoutSetup();
//		feeHeadCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
//				.setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);
//
//		CellLayoutSetup firstFeeHeadCellLayoutSetup = feeHeadCellLayoutSetup.copy()
//				.setTextAlignment(TextAlignment.LEFT);
//
//		addRow(feeHeadTable, documentLayoutSetup, Arrays.asList(new CellData("Course", firstFeeHeadCellLayoutSetup),
//				new CellData("Total Grade", feeHeadCellLayoutSetup)));
//
//		addRow(feeHeadTable, documentLayoutSetup,
//				Arrays.asList(new CellData("Arts", firstFeeHeadCellLayoutSetup.setPdfFont(getRegularFont())),
//						new CellData("A", feeHeadCellLayoutSetup.setPdfFont(getRegularFont()))));
//
//		addRow(feeHeadTable, documentLayoutSetup,
//				Arrays.asList(
//						new CellData("Health Education", firstFeeHeadCellLayoutSetup.setPdfFont(getRegularFont())),
//						new CellData("B", feeHeadCellLayoutSetup.setPdfFont(getRegularFont()))));
//
//		addRow(feeHeadTable, documentLayoutSetup,
//				Arrays.asList(new CellData("Music", firstFeeHeadCellLayoutSetup.setPdfFont(getRegularFont())),
//						new CellData("A-", feeHeadCellLayoutSetup.setPdfFont(getRegularFont()))));
//
//		document.add(feeHeadTable);
//
//	}
//
//	private void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize)
//			throws IOException {
//		int singleContentColumn = 3;
//
//		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);
//
//		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
//		signatureCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
//				.setTextAlignment(TextAlignment.CENTER);
//
//		addRow(table, documentLayoutSetup,
//				Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
//				signatureCellLayoutSetup);
//		addRow(table, documentLayoutSetup,
//				Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
//				signatureCellLayoutSetup);
//		addRow(table, documentLayoutSetup,
//				Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
//				signatureCellLayoutSetup);
//		addRow(table, documentLayoutSetup,
//				Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
//				signatureCellLayoutSetup);
//		addRow(table, documentLayoutSetup,
//				Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
//				signatureCellLayoutSetup);
//		addRow(table, documentLayoutSetup,
//				Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
//				signatureCellLayoutSetup);
//
//		addRow(table, documentLayoutSetup,
//				Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
//				signatureCellLayoutSetup);
//		addRow(table, documentLayoutSetup,
//				Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
//				signatureCellLayoutSetup);
//
//		addRow(table, documentLayoutSetup,
//				Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
//				signatureCellLayoutSetup);
//		addRow(table, documentLayoutSetup,
//				Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
//				signatureCellLayoutSetup);
//
//		addRow(table, documentLayoutSetup,
//				Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
//				signatureCellLayoutSetup);
//		addRow(table, documentLayoutSetup,
//				Arrays.asList(getParagraph(NEXT_LINE), getParagraph(NEXT_LINE), getParagraph(NEXT_LINE)),
//				signatureCellLayoutSetup);
//
//		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Signature Class Teacher"),
//				getParagraph("Checked By"), getParagraph("Principal")), signatureCellLayoutSetup);
//		document.add(table);
//	}
//
//}
