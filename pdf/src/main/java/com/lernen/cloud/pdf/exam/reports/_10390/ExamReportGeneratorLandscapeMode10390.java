package com.lernen.cloud.pdf.exam.reports._10390;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.action.PdfAction;
import com.itextpdf.kernel.pdf.annot.PdfLinkAnnotation;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Link;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.ExamGrade;
import com.lernen.cloud.core.api.examination.report.ExamReportCardLayoutData;
import com.lernen.cloud.core.api.examination.report.ExamReportCourseMarksRow;
import com.lernen.cloud.core.api.examination.report.ExamReportData;
import com.lernen.cloud.core.api.examination.report.ExamReportStructure;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.NumberUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.pdf.exam.reports.ExamReportGenerator;
import com.lernen.cloud.pdf.exam.reports.IExamReportCardGenerator;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;

public class ExamReportGeneratorLandscapeMode10390 extends ExamReportGenerator implements IExamReportCardGenerator {
	public static final float DEFAULT_PAGE_SIDE_MARGIN = 25f;
	public static final float DEFAULT_PAGE_TOP_MARGIN = 10f;
	public static final float DEFAULT_PAGE_BOTTOM_MARGIN = 10f;
	public static final float SQUARE_BORDER_MARGIN = 12f;
	protected static final String HALF_YEARLY_REPORT_TYPE = "HALF_YEARLY";
	protected static final String ANNUAL_REPORT_TYPE = "ANNUAL";
	protected static final String PRE_BOARD_1 = "PRE_BOARD_1";
	protected static final String PRE_BOARD_2 = "PRE_BOARD_2";
	protected static final String PERIODIC_TEST_2 = "PERIODIC_TEST_2";
	protected static final String PERIODIC_TEST_3 = "PERIODIC_TEST_3";
	protected static final String UNIT_TEST_2 = "UNIT_TEST_2";
	private static final Logger logger = LogManager.getLogger(ExamReportGenerator10390.class);
	public ExamReportGeneratorLandscapeMode10390(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	@Override
	public DocumentOutput generateReport(Institute institute, Student student, String reportType, ExamReportData examReportData, String documentName, StudentManager studentManager) {
		try {

			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);

			PdfFont regularFont = getCambriaFont();
			PdfFont boldFont = getCambriaBoldFont();

			generateStudentReportCard(examReportCardLayoutData, institute, examReportData, studentManager, reportType, 1, boldFont, regularFont);

			examReportCardLayoutData.getDocument().close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, student {}, reportType {} ", institute.getInstituteId(), student.getStudentId(), reportType, e);
		}
		return null;
	}

	private void generateStudentReportCard(ExamReportCardLayoutData examReportCardLayoutData, Institute institute, ExamReportData examReportData, StudentManager studentManager, String reportType, int pageNumber, PdfFont boldFont, PdfFont regularFont) throws IOException {

		generateMetaDataLayout(examReportCardLayoutData, institute, examReportData.getStudentLite(), studentManager, reportType, examReportData, pageNumber, boldFont, regularFont);
		boolean hideCoscholasticGradingRange = !(examReportData.getCourseTypeExamReportMarksGrid().containsKey(CourseType.COSCHOLASTIC)) && examReportData.getCourseTypeExamGrades() != null && examReportData.getCourseTypeExamGrades().containsKey(CourseType.COSCHOLASTIC) && examReportData.getCourseTypeExamGrades().get(CourseType.COSCHOLASTIC) != null;

		Set<UUID> nonAdditionalSubjects = getNonAdditionalSubjects(examReportData);
		generateScholasticMarksGrid(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(), getRegularFont(), getRegularBoldFont(), examReportCardLayoutData.getContentFontSize() - 1, examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forOnlyObtainedTotalRow(), "SCHOLASTIC SUBJECTS", 0.18f, nonAdditionalSubjects, "MM", "MO", null, "-");


		if (!CollectionUtils.isEmpty(examReportData.getExamReportStructure().getExamReportCourseStructure().get(CourseType.SCHOLASTIC).getAdditionalCourses())) {
			generateAdditionalCustomScholasticMarksGrid(examReportCardLayoutData, examReportData, GridConfigs.forOnlyObtainedTotalRow(), "ADDITIONAL SUBJECTS", 0.18f);
		}

		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);

		if (examReportData.getCourseTypeExamReportMarksGrid().containsKey(CourseType.COSCHOLASTIC)) {
			generateCoScholasticMarksGrid(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1, examReportCardLayoutData.getDefaultBorderWidth(), examReportData, null, "CO-SCHOLASTIC SUBJECTS", getCoScholasticMarksGridSubjectWidth(reportType), "-");
		}

		generateSummarySection(institute, examReportCardLayoutData, examReportData, boldFont, regularFont);

		generateSignatureBox(institute, examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(), examReportCardLayoutData.getDefaultBorderWidth(), pageNumber, boldFont, regularFont, reportType, examReportData, hideCoscholasticGradingRange);
	}

	protected void generateAdditionalCustomScholasticMarksGrid(ExamReportCardLayoutData examReportCardLayoutData, ExamReportData examReportData, GridConfigs gridConfigs, String subjectColumnTitle, float subjectColumnWidth) throws IOException {
		ExamReportStructure examReportStructure = examReportData.getExamReportStructure();
		if (examReportStructure == null || CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure()) || examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC) == null || CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC).getAdditionalCourses())) {
			return;
		}

		generateScholasticMarksGrid(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(), getRegularFont(), getRegularBoldFont(), examReportCardLayoutData.getContentFontSize() - 1, examReportCardLayoutData.getDefaultBorderWidth(), examReportData, gridConfigs, subjectColumnTitle, subjectColumnWidth, examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC).getAdditionalCourses(), "MM", "MO", null, "-");
	}

	protected float getScholasticMarksGridSubjectWidth(String reportType) {
		return 0.2f;
	}

	protected float getCoScholasticMarksGridSubjectWidth(String reportType) {
		if (reportType.equalsIgnoreCase(HALF_YEARLY_REPORT_TYPE)) {
			return 0.5f;
		}
		if (reportType.equalsIgnoreCase(PRE_BOARD_1)) {
			return 0.5f;
		}
		if (reportType.equalsIgnoreCase(PRE_BOARD_2)) {
			return 0.5f;
		}
		if (reportType.equalsIgnoreCase(ANNUAL_REPORT_TYPE)) {
			return 0.4f;
		}
		return 0.3f;
	}

	protected ExamReportCardLayoutData generateReportCardLayoutData(Institute institute, DocumentOutput documentOutput) throws IOException {

		DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4.rotate());
		float contentFontSize = 9f;
		float defaultBorderWidth = 0.5f;
		Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
		float logoWidth = 75f;
		float logoHeight = 75f;

		int instituteId = institute.getInstituteId();
		return new ExamReportCardLayoutData(document, documentLayoutSetup, null, null, contentFontSize, defaultBorderWidth, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId));
	}

	protected void generateMetaDataLayout(ExamReportCardLayoutData examReportCardLayoutData, Institute institute, StudentLite studentLite, StudentManager studentManager, String reportType, ExamReportData examReportData, int pageNumber, PdfFont boldFont, PdfFont regularFont) throws IOException {

		//Institute watermark
		int instituteId = institute.getInstituteId();
		float watermarkImageHeightWidth = 400f;
		generateWatermark(examReportCardLayoutData, instituteId, watermarkImageHeightWidth, watermarkImageHeightWidth, watermarkImageHeightWidth / 2, watermarkImageHeightWidth / 5 + 20);
		generateHeader(examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData, studentLite, institute, reportType, 40f, examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.87f, boldFont, regularFont);
		generateStudentInformation(examReportCardLayoutData, studentLite, examReportData, boldFont, regularFont);
		generateBorderLayout(examReportCardLayoutData, pageNumber);
	}

	protected void generateHeader(DocumentLayoutSetup documentLayoutSetup, ExamReportCardLayoutData examReportCardLayoutData, StudentLite studentLite, Institute institute, String reportType, float offsetX, float offsetY, PdfFont boldFont, PdfFont regularFont) throws IOException {

		generateDynamicImageProvider(examReportCardLayoutData, documentLayoutSetup.getPageSize().getWidth() - (2.5f * offsetX), offsetY - 13f, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

		generateLogo(examReportCardLayoutData, institute, ImageProvider.INSTANCE.getImage(ImageProvider._CBSE_LOGO), offsetX, offsetY - 13f);

		int singleContentColumn = 1;
		Table table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(examReportCardLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.CENTER);


		String url = "http://www.acharyakulam.org";
		PdfLinkAnnotation linkAnnotation = new PdfLinkAnnotation(new Rectangle(0, 0, 0, 0));
		linkAnnotation.setAction(PdfAction.createURI(url));
		Link link = new Link("Website - " + url, linkAnnotation);
		Paragraph website = new Paragraph(link).setFont(regularFont).setFontSize(examReportCardLayoutData.getContentFontSize() - 1);

		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Collections.singletonList(getParagraph(institute.getInstituteName().toUpperCase()).setMultipliedLeading(0.85f)), cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() + 10));
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Collections.singletonList(getParagraph("Affiliation No. - 3530430, School Code No. - 81654,  Acharyakulam, Near Patanjali Yogpeeth Phase 1, Haridwar 249405").setMultipliedLeading(0.85f)), cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() - 1).setPdfFont(regularFont));
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Collections.singletonList(getParagraph("Mob. : 8954890551, 8954890253; Ph. :01334273400;  Email Id - <EMAIL>").setMultipliedLeading(0.85f)), cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() - 1).setPdfFont(regularFont));
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Collections.singletonList(website.setMultipliedLeading(0.85f)), cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() - 1).setPdfFont(regularFont));
		examReportCardLayoutData.getDocument().add(table);
		table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);
		String headerExamTitle = reportType.equalsIgnoreCase(HALF_YEARLY_REPORT_TYPE) ? "HALF YEARLY EXAMINATION " : reportType.equalsIgnoreCase(PRE_BOARD_1) ? "PRE BOARD 1 " : reportType.equalsIgnoreCase(PRE_BOARD_2) ? "PRE BOARD 2 "
				: reportType.equalsIgnoreCase(PERIODIC_TEST_2) ? "PERIODIC TEST 2 ": reportType.equalsIgnoreCase(PERIODIC_TEST_3) ? "PERIODIC TEST 3 ": reportType.equalsIgnoreCase(UNIT_TEST_2) ? "UNIT TEST 2 " : "ANNUAL EXAMINATION ";
		headerExamTitle += "(" + studentLite.getStudentSessionData().getShortYearDisplayName() + ")";
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Collections.singletonList(getParagraph(headerExamTitle).setUnderline()), cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() + 2).setPdfFont(boldFont));
		examReportCardLayoutData.getDocument().add(table);
	}

	protected void generateStudentInformation(ExamReportCardLayoutData examReportCardLayoutData, StudentLite studentLite, ExamReportData examReportData, PdfFont boldFont, PdfFont regularFont) throws IOException {

		Document document = examReportCardLayoutData.getDocument();
		DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
		float contentFontSize = examReportCardLayoutData.getContentFontSize() - 1;

		Table table = getPDFTable(documentLayoutSetup, new float[]{0.10f, 0.15f, 0.10f, 0.15f, 0.10f, 0.15f, 0.10f, 0.15f});

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize).setBorder(new SolidBorder(0.5f));

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT).setBorderRight(null);
		CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT).setBorderLeft(null);
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT).setBorderRight(null);
		CellLayoutSetup forthCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT).setBorderLeft(null);

		Paragraph studentNameKey = getParagraph("STUDENT'S NAME ", boldFont);
		Paragraph studentNameValue = getParagraph(": " + studentLite.getName().toUpperCase(), regularFont);
		Paragraph fatherNameKey = getParagraph("FATHER NAME ", boldFont);
		Paragraph fatherNameValue = getParagraph(StringUtils.isBlank(studentLite.getFathersName()) ? ": " : ": " + studentLite.getFathersName().toUpperCase(), regularFont);
		Paragraph dobKey = getParagraph("DOB ", boldFont);
		Paragraph dobValue = getParagraph(studentLite.getDateOfBirth() == null || studentLite.getDateOfBirth() <= 0 ? ": " : ": " + DateUtils.getFormattedDate(studentLite.getDateOfBirth(), DATE_FORMAT, User.DFAULT_TIMEZONE), regularFont);
		Paragraph motherNameKey = getParagraph("MOTHER NAME ", boldFont);
		Paragraph motherNameValue = getParagraph(StringUtils.isBlank(studentLite.getMothersName()) ? ": " : ": " + studentLite.getMothersName().toUpperCase(), regularFont);
		Paragraph admissionNumberKey = getParagraph("ADMISSION NO. ", boldFont);
		Paragraph admissionNumberValue = getParagraph(": " + studentLite.getAdmissionNumber().toUpperCase(), regularFont);
		Paragraph classKey = getParagraph("CLASS ", boldFont);
		Paragraph classValue = getParagraph(": " + studentLite.getStudentSessionData().getStandardNameWithSection().toUpperCase(), regularFont);
		Paragraph rollNumberKey = getParagraph("ROLL NUMBER ", boldFont);
		Paragraph rollNumberValue = getParagraph(StringUtils.isBlank(studentLite.getStudentSessionData().getRollNumber()) ? ": " : ": " + studentLite.getStudentSessionData().getRollNumber().toUpperCase(), regularFont);
		String attendance = (examReportData.getTotalAttendedDays() == null ? "-" : String.valueOf(examReportData.getTotalAttendedDays()) + " / " +(examReportData.getTotalWorkingDays() == null ? "-" : examReportData.getTotalWorkingDays()));
		Paragraph totalAttendanceDaysKey = getParagraph("ATTENDANCE" ,boldFont);
		Paragraph totalAttendanceDaysValue = getParagraph(StringUtils.isBlank(attendance) ? ": " : ": " + attendance, regularFont);

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(studentNameKey.setMultipliedLeading(0.85f), firstCellLayoutSetup.setBorderRight(null)), new CellData(studentNameValue.setMultipliedLeading(0.85f), secondCellLayoutSetup), new CellData(admissionNumberKey.setMultipliedLeading(0.85f), thirdCellLayoutSetup), new CellData(admissionNumberValue.setMultipliedLeading(0.85f), forthCellLayoutSetup), new CellData(rollNumberKey.setMultipliedLeading(0.85f), thirdCellLayoutSetup), new CellData(rollNumberValue.setMultipliedLeading(0.85f), forthCellLayoutSetup), new CellData(classKey.setMultipliedLeading(0.85f), thirdCellLayoutSetup), new CellData(classValue.setMultipliedLeading(0.85f), forthCellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(fatherNameKey.setMultipliedLeading(0.85f), firstCellLayoutSetup.setBorderRight(null)), new CellData(fatherNameValue.setMultipliedLeading(0.85f), secondCellLayoutSetup), new CellData(motherNameKey.setMultipliedLeading(0.85f), firstCellLayoutSetup.setBorderRight(null)), new CellData(motherNameValue.setMultipliedLeading(0.85f), secondCellLayoutSetup), new CellData(dobKey.setMultipliedLeading(0.85f), firstCellLayoutSetup.setBorderRight(null)), new CellData(dobValue.setMultipliedLeading(0.85f), secondCellLayoutSetup),new CellData(totalAttendanceDaysKey.setMultipliedLeading(0.85f), thirdCellLayoutSetup), new CellData(totalAttendanceDaysValue.setMultipliedLeading(0.85f), forthCellLayoutSetup)));

		document.add(table);

		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
	}

	protected void generateBorderLayout(ExamReportCardLayoutData examReportCardLayoutData, int pageNumber) {
		DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
		PdfCanvas canvas = new PdfCanvas(examReportCardLayoutData.getDocument().getPdfDocument(), pageNumber);
		canvas.rectangle(SQUARE_BORDER_MARGIN - 2, SQUARE_BORDER_MARGIN - 2, documentLayoutSetup.getPageSize().getWidth() - (SQUARE_BORDER_MARGIN - 2) * 2, documentLayoutSetup.getPageSize().getHeight() - (SQUARE_BORDER_MARGIN - 2) * 2);
		canvas.stroke();
		canvas.setLineWidth(1f);
		canvas.rectangle(SQUARE_BORDER_MARGIN, SQUARE_BORDER_MARGIN, documentLayoutSetup.getPageSize().getWidth() - SQUARE_BORDER_MARGIN * 2, documentLayoutSetup.getPageSize().getHeight() - SQUARE_BORDER_MARGIN * 2);
		canvas.stroke();
		canvas.setLineWidth(1f);
	}

	protected void generateSummarySection(Institute institute, ExamReportCardLayoutData examReportCardLayoutData, ExamReportData examReportData, PdfFont boldFont, PdfFont regularFont) throws IOException {

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(examReportCardLayoutData.getContentFontSize() - 1).setTextAlignment(TextAlignment.LEFT);

		Table remarksTable = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), new float[]{0.06f, 0.94f});
		addRow(remarksTable, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph("RESULT : ").setMultipliedLeading(0.85f),
				getParagraph(examReportData.getExamResultStatus() == null ? "" : examReportData.getExamResultStatus().getDisplayName(), regularFont).setMultipliedLeading(0.85f)), cellLayoutSetup);

		addRow(remarksTable, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph("REMARK : ").setMultipliedLeading(0.85f),
				getParagraph(examReportData.getRemarks() == null ? "" : examReportData.getRemarks(), regularFont).setMultipliedLeading(0.85f)), cellLayoutSetup);

		examReportCardLayoutData.getDocument().add(remarksTable);
	}

	protected void generateSignatureBox(Institute institute, Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize, float defaultBorderWidth, int pageNumber, PdfFont boldFont, PdfFont regularFont, String reportType, ExamReportData examReportData, boolean hideCoscholasticGradingRange) throws IOException {
		PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);
		canvas.moveTo(12, hideCoscholasticGradingRange ? 95 : 135);
		canvas.lineTo(830, hideCoscholasticGradingRange ? 95 : 135);
		canvas.setLineWidth(.5f);
		canvas.closePathStroke();
		int singleContentColumn = 5;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);
		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 1).setTextAlignment(TextAlignment.CENTER);
		String dateKey =  "Date : ";
		String dateValue = examReportData.getDateOfResultDeclaration() == null ? EMPTY_TEXT : DateUtils.getFormattedDate(examReportData.getDateOfResultDeclaration());
		Paragraph dateParagraph = getKeyValueParagraph(dateKey, dateValue, boldFont, regularFont);

		String placeValue = StringUtils.isBlank(institute.getCity()) ? EMPTY_TEXT : institute.getCity();
		Paragraph placeParagraph = getKeyValueParagraph("Place : ", placeValue, boldFont, regularFont);


		addRow(table, documentLayoutSetup, Arrays.asList(dateParagraph.setMultipliedLeading(0.85f), placeParagraph.setMultipliedLeading(0.85f), getParagraph("Parent's Sign").setMultipliedLeading(0.85f), getParagraph("Class Teacher's Sign").setMultipliedLeading(0.85f), getParagraph("Principal's Sign").setMultipliedLeading(0.85f)), signatureCellLayoutSetup);
		table.setFixedPosition(30f, hideCoscholasticGradingRange ? 95f : 135f, documentLayoutSetup.getPageSize().getWidth() - 100f);
		document.add(table);

		generateGradeBox(document, documentLayoutSetup, contentFontSize, defaultBorderWidth, boldFont, regularFont, examReportData, hideCoscholasticGradingRange);
	}

	private void generateGradeBox(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize, float defaultBorderWidth, PdfFont boldFont, PdfFont regularFont, ExamReportData examReportData, boolean hideCoscholasticGradingRange) throws IOException {

		Table table = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 1).setTextAlignment(TextAlignment.CENTER);
		addRow(table, documentLayoutSetup, Collections.singletonList(getParagraph("MARKS RANGE")), signatureCellLayoutSetup);

		table.setFixedPosition(10f, hideCoscholasticGradingRange ? 75f : 115f, documentLayoutSetup.getPageSize().getWidth());
		document.add(table);

		if (hideCoscholasticGradingRange) {
			float[] columnWidths = new float[]{0.24f, 0.24f, 0.24f, 0.24f};
			Table headerTable = getPDFTable(documentLayoutSetup, columnWidths);
			CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
			marksCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize - 4).setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);
			addBlankLine(document, false, 1);
			List<CellData> headerList = new ArrayList<>();
			headerList.add(new CellData("Marks Range %", marksCellLayoutSetup));
			headerList.add(new CellData("Grade", marksCellLayoutSetup));
			headerList.add(new CellData("Marks Range %", marksCellLayoutSetup));
			headerList.add(new CellData("Grade", marksCellLayoutSetup));
			addRow(headerTable, documentLayoutSetup, headerList);
			List<ExamGrade> gradesList = examReportData.getCourseTypeExamGrades().get(CourseType.SCHOLASTIC);
			for (int index = 0; index < gradesList.size() / 2; index++) {
				List<CellData> row = new ArrayList<>();
				row.add(new CellData(gradesList.get(index).getRangeDisplayName(), marksCellLayoutSetup));
				row.add(new CellData(gradesList.get(index).getGradeName(), marksCellLayoutSetup));
				row.add(new CellData(gradesList.get(gradesList.size() / 2 + index).getRangeDisplayName(), marksCellLayoutSetup));
				row.add(new CellData(gradesList.get(gradesList.size() / 2 + index).getGradeName(), marksCellLayoutSetup));
				addRow(headerTable, documentLayoutSetup, row);
			}
			if (gradesList.size() % 2 != 0) {
				List<CellData> row = new ArrayList<>();
				row.add(new CellData(gradesList.get(gradesList.size() - 1).getRangeDisplayName(), marksCellLayoutSetup));
				row.add(new CellData(gradesList.get(gradesList.size() - 1).getGradeName(), marksCellLayoutSetup));
				row.add(new CellData(EMPTY_TEXT, marksCellLayoutSetup));
				row.add(new CellData(EMPTY_TEXT, marksCellLayoutSetup));
				addRow(headerTable, documentLayoutSetup, row);
			}
			headerTable.setFixedPosition(35f, 15f, documentLayoutSetup.getPageSize().getWidth() - 100f);
			document.add(headerTable);
			return;
		}
		float cosScholasticBottom = 109f;
		float cosScholasticTableBottom = 72f;
		CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
		marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(contentFontSize - 2).setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);

		if (examReportData.getCourseTypeExamGrades() != null && examReportData.getCourseTypeExamGrades().containsKey(CourseType.SCHOLASTIC) && examReportData.getCourseTypeExamGrades().get(CourseType.SCHOLASTIC) != null) {

			Table headerTable = getPDFTable(documentLayoutSetup, 1);
			List<CellData> headerList = new ArrayList<>();
			headerList.add(new CellData("Scholastic Grading", marksCellLayoutSetup));
			addRow(headerTable, documentLayoutSetup, headerList);

			headerTable.setFixedPosition(25f, 98f, documentLayoutSetup.getPageSize().getWidth() - 100f);
			cosScholasticBottom = 46f;
			document.add(headerTable);
			int scholasticColumnCount = examReportData.getCourseTypeExamGrades().get(CourseType.SCHOLASTIC).size() + 1; // +1 for MARKS RANGE
			float[] scholasticColumnWidths = new float[scholasticColumnCount];
			for (int i = 0; i < scholasticColumnCount; i++) {
				scholasticColumnWidths[i] = 1f / scholasticColumnCount;
			}

			headerTable = getPDFTable(documentLayoutSetup, scholasticColumnWidths);
			headerList = new ArrayList<>();

			headerList.add(new CellData("RANGE", marksCellLayoutSetup));
			for (ExamGrade examGrade : examReportData.getCourseTypeExamGrades().get(CourseType.SCHOLASTIC)) {
				headerList.add(new CellData(examGrade.getRangeDisplayName(), marksCellLayoutSetup));
			}
			addRow(headerTable, documentLayoutSetup, headerList);

			headerList = new ArrayList<>();
			headerList.add(new CellData("GRADE", marksCellLayoutSetup));
			for (ExamGrade examGrade : examReportData.getCourseTypeExamGrades().get(CourseType.SCHOLASTIC)) {
				headerList.add(new CellData(examGrade.getGradeName(), marksCellLayoutSetup));
			}
			addRow(headerTable, documentLayoutSetup, headerList);

			headerTable.setFixedPosition(25f, 67f, documentLayoutSetup.getPageSize().getWidth() - 100f);
			cosScholasticTableBottom = 15f;
			document.add(headerTable);
		}

		if (examReportData.getCourseTypeExamGrades() != null && examReportData.getCourseTypeExamGrades().containsKey(CourseType.COSCHOLASTIC) && examReportData.getCourseTypeExamGrades().get(CourseType.COSCHOLASTIC) != null) {

			Table headerTable = getPDFTable(documentLayoutSetup, 1);
			List<CellData> headerList = new ArrayList<>();
			headerList.add(new CellData("Co-Scholastic Grading", marksCellLayoutSetup));
			addRow(headerTable, documentLayoutSetup, headerList);

			headerTable.setFixedPosition(25f, cosScholasticBottom, documentLayoutSetup.getPageSize().getWidth() - 100f);
			document.add(headerTable);
			int coscholasticColumnCount = examReportData.getCourseTypeExamGrades().get(CourseType.COSCHOLASTIC).size() + 1; // +1 for MARKS RANGE
			float[] coscholasticColumnWidths = new float[coscholasticColumnCount];
			for (int i = 0; i < coscholasticColumnCount; i++) {
				coscholasticColumnWidths[i] = 1f / coscholasticColumnCount;
			}

			headerTable = getPDFTable(documentLayoutSetup, coscholasticColumnWidths);
			headerList = new ArrayList<>();

			headerList.add(new CellData("RANGE", marksCellLayoutSetup));
			for (ExamGrade examGrade : examReportData.getCourseTypeExamGrades().get(CourseType.COSCHOLASTIC)) {
				headerList.add(new CellData(examGrade.getRangeDisplayName(), marksCellLayoutSetup));
			}
			addRow(headerTable, documentLayoutSetup, headerList);

			headerList = new ArrayList<>();
			headerList.add(new CellData("GRADE", marksCellLayoutSetup));
			for (ExamGrade examGrade : examReportData.getCourseTypeExamGrades().get(CourseType.COSCHOLASTIC)) {
				headerList.add(new CellData(examGrade.getGradeName(), marksCellLayoutSetup));
			}
			addRow(headerTable, documentLayoutSetup, headerList);

			headerTable.setFixedPosition(25f, cosScholasticTableBottom, documentLayoutSetup.getPageSize().getWidth() - 100f);
			document.add(headerTable);
		}
	}

	protected Set<UUID> getNonAdditionalSubjects(ExamReportData examReportData) {
		Set<UUID> nonAdditionalSubjects = new HashSet<UUID>();
		for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportData.getCourseTypeExamReportMarksGrid().get(CourseType.SCHOLASTIC).getExamReportCourseMarksRows()) {
			if (!isAdditionalSubject(examReportCourseMarksRow.getCourse(), examReportData.getExamReportStructure())) {
				nonAdditionalSubjects.add(examReportCourseMarksRow.getCourse().getCourseId());
			}
		}
		return nonAdditionalSubjects;
	}


	protected void sortClassExamReports(List<ExamReportData> examReportDataList) {
		Collections.sort(examReportDataList, new Comparator<ExamReportData>() {

			@Override
			public int compare(ExamReportData e1, ExamReportData e2) {
				StandardSections section1 = e1.getStudentLite().getStudentSessionData().getStandardSection();

				StandardSections section2 = e2.getStudentLite().getStudentSessionData().getStandardSection();

				if (section1 != null && section2 != null) {
					int sectionCompare = section1.getSectionName().compareToIgnoreCase(section2.getSectionName());
					if (sectionCompare != 0) {
						return sectionCompare;
					}
				}

				return e1.getStudentLite().getName().compareToIgnoreCase(e2.getStudentLite().getName());
			}
		});
	}

	@Override
	public DocumentOutput generateClassReport(Institute institute, String reportType, List<ExamReportData> examReportDataList, String documentName, StudentManager studentManager) {

		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);

			sortClassExamReports(examReportDataList);

			int pageNumber = 1;
			PdfFont regularFont = getCambriaFont();
			PdfFont boldFont = getCambriaBoldFont();
			for (ExamReportData examReportData : examReportDataList) {
				generateStudentReportCard(examReportCardLayoutData, institute, examReportData, studentManager, reportType, pageNumber, boldFont, regularFont);

				if (pageNumber != examReportDataList.size()) {
					examReportCardLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				pageNumber++;
			}

			examReportCardLayoutData.getDocument().close();
			return documentOutput;

		} catch (IOException e) {
			e.printStackTrace();
		}

		return null;
	}

	@Override
	public DocumentOutput generateClassResultReport(Institute institute, String reportType, List<ExamReportData> examReportDataList, String documentName, StudentManager studentManager, int studentPerPage) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4);
			Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

			PdfFont regularFont = getRegularFont();
			PdfFont boldFont = getRegularBoldFont();

			CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
			marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(DEFAULT_FONT_SIZE).setBorder(new SolidBorder(DEFAULT_BORDER_WIDTH)).setTextAlignment(TextAlignment.CENTER);

			CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont).setTextAlignment(TextAlignment.LEFT);

			String instituteName = institute.getInstituteName().toUpperCase();
			String sessionName = "";
			if (!CollectionUtils.isEmpty(examReportDataList)) {
				sessionName = "Session : " + examReportDataList.get(0).getStudentLite().getStudentSessionData().getShortYearDisplayName();
			}
			generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName, sessionName, marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));

			sortClassExamReports(examReportDataList);
			int pageNumber = 1;
			for (ExamReportData examReportData : examReportDataList) {
				generateClassReportDataStudentDetails(documentLayoutSetup, document, courseCellLayoutSetup, examReportData.getStudentLite());
				generateScholasticMarksGrid(document, documentLayoutSetup, DEFAULT_FONT_SIZE, DEFAULT_BORDER_WIDTH, examReportData, GridConfigs.forOnlyObtainedTotalRow(), "Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType), null, null);
				generateClassReportDataStudentResult(documentLayoutSetup, document, examReportData, courseCellLayoutSetup);
				if (pageNumber % studentPerPage == 0) {
					document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
					generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName, sessionName, marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));
				} else {
					addBlankLine(document, false, 1);
				}
				pageNumber++;
			}
			document.close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(), reportType, e);
		}
		return null;
	}
}
