/**
 * 
 */
package com.lernen.cloud.pdf.certificates.study;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.configurations.DocumentPropertiesPreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.PronounUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.core.utils.images.WatermarkProvider;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.Arrays;
import java.util.Collections;

/**
 * <AUTHOR>
 *
 */
public class GlobalStudyCertificateGenerator extends StudyCertificateGenerator {
	
	public GlobalStudyCertificateGenerator(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(GlobalStudyCertificateGenerator.class);

	public static final float DEFAULT_PAGE_SIDE_MARGIN = 50f;
	public static final float DEFAULT_TABLE_BORDER_WIDTH = 0.5f;
	public static final float LOGO_WIDTH = 75f;
	public static final float LOGO_HEIGHT = 75f;
	public static final String NA = "NA";

	private static final String CONTENT_1 = "This is to certify that ";
	private static final String CONTENT_2 = "%s ";//STUDENT NAME
	private static final String CONTENT_3 = "%s";//(S/D/)
	private static final String CONTENT_4 = "o ";
	private static final String CONTENT_5 = "Shri %s ";//FATHER NAME
	private static final String CONTENT_6 = "%s";//(S/D/)
	private static final String CONTENT_7 = "o ";
	private static final String CONTENT_8 = "Smt %s ";//MOTHER NAME
	private static final String CONTENT_9 = "Date of Birth : ";
	private static final String CONTENT_10 = "%s ";//Date of birth
	private static final String CONTENT_11 = "Category : ";
	private static final String CONTENT_12 = "%s ";//Category
	private static final String CONTENT_13 = "Gender : ";
	private static final String CONTENT_14 = "%s ";//Gender
	private static final String CONTENT_15 = "S.R.No. : ";
	private static final String CONTENT_16 = "%s ";//S.R.No.
	private static final String CONTENT_17 = "Class : ";
	private static final String CONTENT_18 = "%s ";//Class with section
	private static final String CONTENT_19 = "R/o ";
	private static final String CONTENT_20 = "%s";//address
	private static final String CONTENT_21 = ", is currently studying in this school as a regular student.\nThis certificate is signed and the photo of the student is attested by the principal and marked with the seal of the school.\n\n";
	private static final String CONTENT_22 = "Date : ";
	private static final String CONTENT_23 = "%s ";//current date

//	private static final String SIGNATURE_1 = "Signature of Student";
//	private static final String SIGNATURE_2 = "Signature of Class Teacher";
	private static final String SIGNATURE_1 = "";
	private static final String SIGNATURE_2 = "";
	private static final String SIGNATURE_3 = "HeadMaster/Principal";
	private static final String SIGNATURE_4 = "(Seal & Signature)";

	@Override
	public DocumentOutput generateStudyCertificate(Institute institute, Student student,
												   String documentName, StudentManager studentManager, DocumentPropertiesPreferences documentPropertiesPreferences) {
		try {
			float squareBorderMargin = 8f;
			float borderInnerGap = 2f;
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

			float logoWidth = documentPropertiesPreferences != null && documentPropertiesPreferences.getPrimaryLogoWidth() != null ? documentPropertiesPreferences.getPrimaryLogoWidth() : 70f;
			float logoHeight = documentPropertiesPreferences != null && documentPropertiesPreferences.getPrimaryLogoHeight() != null ? documentPropertiesPreferences.getPrimaryLogoHeight() : 70f;
			DocumentLayoutData documentLayoutData = generateStudyCertificateLayoutData(institute, documentOutput, logoWidth, logoHeight, documentPropertiesPreferences);

			DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
			Document document = documentLayoutData.getDocument();
			PdfFont boldFont = getCambriaBoldFont();
			PdfFont regularFont = getCambriaFont();
			float contentFontSize = documentLayoutData.getContentFontSize();

			generateImages(document, documentLayoutSetup, documentLayoutData, institute, student, studentManager, documentPropertiesPreferences);
			generateHeader(document, documentLayoutSetup, contentFontSize, boldFont, regularFont, student, institute, documentPropertiesPreferences);
			generateMetadata(documentLayoutData, squareBorderMargin, borderInnerGap);
			generateContent(document, documentLayoutSetup, contentFontSize, student, boldFont, regularFont);
			generateSignatureBox(document, documentLayoutSetup, contentFontSize, boldFont, regularFont);

			document.close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generate study certificate for student {} ", student.getStudentBasicInfo().getAdmissionNumber(), e);
		}
		return null;
	}

	protected void generateImages(Document document, DocumentLayoutSetup documentLayoutSetup,
								DocumentLayoutData documentLayoutData, Institute institute, Student student,
								StudentManager studentManager, DocumentPropertiesPreferences documentPropertiesPreferences) throws IOException {

		//Watermark
		float watermarkWidth = documentPropertiesPreferences != null && documentPropertiesPreferences.getWatermarkLogoWidth() != null ? documentPropertiesPreferences.getWatermarkLogoWidth() : 500f;
		float watermarkHeight = documentPropertiesPreferences != null && documentPropertiesPreferences.getWatermarkLogoHeight() != null ? documentPropertiesPreferences.getWatermarkLogoHeight() : 500f;
		float watermarkLogoXOffset = documentPropertiesPreferences != null && documentPropertiesPreferences.getWatermarkLogoXOffset() != null ? documentPropertiesPreferences.getWatermarkLogoXOffset() : watermarkWidth / 5 - 50;
		float watermarkLogoYOffset = documentPropertiesPreferences != null && documentPropertiesPreferences.getWatermarkLogoYOffset() != null ? documentPropertiesPreferences.getWatermarkLogoYOffset() : watermarkHeight / 2 - 80;
		generateWatermark(documentLayoutData, institute.getInstituteId(), watermarkWidth, watermarkHeight, watermarkLogoXOffset, watermarkLogoYOffset);

		//LOGO
		float primaryLogoXOffset = documentPropertiesPreferences != null && documentPropertiesPreferences.getPrimaryLogoXOffset() != null ? documentPropertiesPreferences.getPrimaryLogoXOffset() : 30f;
		float primaryLogoYOffset = documentPropertiesPreferences != null && documentPropertiesPreferences.getPrimaryLogoYOffset() != null ? documentPropertiesPreferences.getPrimaryLogoYOffset() : documentLayoutSetup.getPageSize().getHeight() * 0.75f;
		generateDynamicImageProvider(documentLayoutData, primaryLogoXOffset, primaryLogoYOffset, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);


		//Student Image
		byte[] image = getStudentImage(institute.getInstituteId(), student, studentManager);
		if (image == null) {
			image = documentLayoutData.getImageFrame();
		}
		float studentImageWidth = documentPropertiesPreferences != null && documentPropertiesPreferences.getStudentImageWidth() != null ? documentPropertiesPreferences.getStudentImageWidth() : 80f;
		float studentImageHeight = documentPropertiesPreferences != null && documentPropertiesPreferences.getStudentImageHeight() != null ? documentPropertiesPreferences.getStudentImageHeight() : 90f;
		float studentImageXOffset = documentPropertiesPreferences != null && documentPropertiesPreferences.getStudentImageXOffset() != null ? documentPropertiesPreferences.getStudentImageXOffset() : documentLayoutData.getDocumentLayoutSetup().getPageSize().getWidth() - 120f;
		float studentImageYOffset = documentPropertiesPreferences != null && documentPropertiesPreferences.getStudentImageYOffset() != null ? documentPropertiesPreferences.getStudentImageYOffset() : documentLayoutSetup.getPageSize().getHeight() * 0.75f;
		generateImage(document, documentLayoutSetup, image, studentImageWidth, studentImageHeight, studentImageXOffset, studentImageYOffset);
	}

	protected DocumentLayoutData generateStudyCertificateLayoutData(Institute institute, DocumentOutput documentOutput,
																	   float logoWidth, float logoHeight, DocumentPropertiesPreferences documentPropertiesPreferences) throws IOException {


		DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false,  DEFAULT_PAGE_SIZE, DEFAULT_PAGE_TOP_MARGIN, DEFAULT_PAGE_BOTTOM_MARGIN, DEFAULT_PAGE_SIDE_MARGIN,
				0f);
		float contentFontSize = documentPropertiesPreferences != null && documentPropertiesPreferences.getDocumentContentFontSize() != null ? documentPropertiesPreferences.getDocumentContentFontSize() : 14f;
		float defaultBorderWidth = 0.5f;
		Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

		DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getRegularFont(),
				getRegularBoldFont(), contentFontSize, defaultBorderWidth, logoWidth, logoHeight,
				LogoProvider.INSTANCE.getLogo(institute.getInstituteId()), null, null);
		documentLayoutData.setImageFrame(ImageProvider.INSTANCE.getImage(ImageProvider.IMAGE_FRAME));
		return documentLayoutData;
	}

	protected void generateHeader(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
								PdfFont cambriaBoldFont, PdfFont cambriaFont, Student student, Institute institute,
								  DocumentPropertiesPreferences documentPropertiesPreferences) throws IOException {

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.CENTER).setFontSize(contentFontSize);

		float instituteNameFontSize = documentPropertiesPreferences != null && documentPropertiesPreferences.getInstituteNameFontSize() != null ? documentPropertiesPreferences.getInstituteNameFontSize() : contentFontSize + 2;
		float letterHead1FontSize = documentPropertiesPreferences != null && documentPropertiesPreferences.getLetterHead1FontSize() != null ? documentPropertiesPreferences.getLetterHead1FontSize() : contentFontSize - 3;
		float letterHead2FontSize = documentPropertiesPreferences != null && documentPropertiesPreferences.getLetterHead2FontSize() != null ? documentPropertiesPreferences.getLetterHead2FontSize() : contentFontSize - 3;
		Table table = getPDFTable(documentLayoutSetup, 1).setTextAlignment(TextAlignment.CENTER);
		addRow(table, documentLayoutSetup, Collections.singletonList(getParagraph(institute.getInstituteName())),
				cellLayoutSetup.copy().setFontSize(instituteNameFontSize));
		addRow(table, documentLayoutSetup, Collections.singletonList(getParagraph(institute.getLetterHeadLine1())),
				cellLayoutSetup.copy().setFontSize(letterHead1FontSize));
		addRow(table, documentLayoutSetup, Collections.singletonList(getParagraph(institute.getLetterHeadLine2())),
				cellLayoutSetup.setFontSize(letterHead2FontSize));
		document.add(table);

		addBlankLine(document, true, 2);

		float documentHeadingFontSize = documentPropertiesPreferences != null && documentPropertiesPreferences.getDocumentHeadingFontSize() != null ? documentPropertiesPreferences.getDocumentHeadingFontSize() : contentFontSize + 2;
		table = getPDFTable(documentLayoutSetup, 1).setUnderline().setTextAlignment(TextAlignment.CENTER);
		addRow(table, documentLayoutSetup, Collections.singletonList(getParagraph("Student Certificate")),
				cellLayoutSetup.copy().setFontSize(documentHeadingFontSize));
		document.add(table);

		addBlankLine(document, true, 3);


	}

	protected void generateContent(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
								 Student student, PdfFont cambriaBoldFont, PdfFont cambriaFont) throws IOException {

		Table table = getPDFTable(documentLayoutSetup, 1);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setFontSize(contentFontSize).setTextAlignment(TextAlignment.JUSTIFIED);

		String studentName = student.getStudentBasicInfo().getName().toUpperCase();
		String sonDaughter = PronounUtils.getSonDaughter(student.getStudentBasicInfo().getGender(),false);
		String fatherName = student.getStudentFamilyInfo() == null ? NA :
				StringUtils.isBlank(student.getStudentFamilyInfo().getFathersName()) ? NA :
				student.getStudentFamilyInfo().getFathersName().toUpperCase();
		String motherName = student.getStudentFamilyInfo() == null ? NA :
				StringUtils.isBlank(student.getStudentFamilyInfo().getMothersName()) ? NA :
				student.getStudentFamilyInfo().getMothersName().toUpperCase();
		String dob = student.getStudentBasicInfo().getDateOfBirth() == null ? NA :
				DateUtils.getFormattedDate(student.getStudentBasicInfo().getDateOfBirth());
		String category = student.getStudentBasicInfo().getUserCategory() == null ? NA :
				student.getStudentBasicInfo().getUserCategory().name().toUpperCase();
		String gender = PronounUtils.getBoyGirl(student.getStudentBasicInfo().getGender(),false);
		String srNumber = student.getStudentBasicInfo().getAdmissionNumber();
		String displayClassName = student.getStudentAcademicSessionInfoResponse() == null ? NA :
				student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayNameWithSection();
		String address = student.getStudentBasicInfo().getStudentFullAddress();
		String currentDate = DateUtils.getFormattedDate(DateUtils.now());

		Text t1 = new Text(String.format(CONTENT_1)).setFont(cambriaFont);
		Text t2 = new Text(String.format(CONTENT_2, studentName)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
		Text t3 = new Text(String.format(CONTENT_3, sonDaughter)).setFont(cambriaFont);
		Text t4 = new Text(String.format(CONTENT_4)).setFont(cambriaFont);
		Text t5 = new Text(String.format(CONTENT_5, fatherName)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
		Text t6 = new Text(String.format(CONTENT_6, sonDaughter)).setFont(cambriaFont);
		Text t7 = new Text(String.format(CONTENT_7)).setFont(cambriaFont);
		Text t8 = new Text(String.format(CONTENT_8, motherName)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
		Text t9 = new Text(String.format(CONTENT_9)).setFont(cambriaFont);
		Text t10 = new Text(String.format(CONTENT_10, dob)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
		Text t11 = new Text(String.format(CONTENT_11)).setFont(cambriaFont);
		Text t12 = new Text(String.format(CONTENT_12, category)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
		Text t13 = new Text(String.format(CONTENT_13)).setFont(cambriaFont);
		Text t14 = new Text(String.format(CONTENT_14, gender)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
		Text t15 = new Text(String.format(CONTENT_15)).setFont(cambriaFont);
		Text t16 = new Text(String.format(CONTENT_16, srNumber)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
		Text t17 = new Text(String.format(CONTENT_17)).setFont(cambriaFont);
		Text t18 = new Text(String.format(CONTENT_18, displayClassName)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
		Text t19 = new Text(String.format(CONTENT_19)).setFont(cambriaFont);
		Text t20 = new Text(String.format(CONTENT_20, address)).setFont(cambriaBoldFont).setFontSize(contentFontSize);
		Text t21 = new Text(String.format(CONTENT_21)).setFont(cambriaFont);
		Text t22 = new Text(String.format(CONTENT_22)).setFont(cambriaFont);
		Text t23 = new Text(String.format(CONTENT_23, currentDate)).setFont(cambriaBoldFont).setFontSize(contentFontSize);


		Paragraph p1 = new Paragraph();
		p1.add(t1).add(t2).add(t3).add(t4).add(t5).add(t6).add(t7).add(t8).add(t9).add(t10).add(t11).add(t12)
				.add(t13).add(t14).add(t15).add(t16).add(t17).add(t18).add(t19).add(t20).add(t21).add(t22).add(t23);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(p1, cellLayoutSetup)));
		document.add(table);

		addBlankLine(document, true, 3);
	}

	protected void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
									  PdfFont cambriaBoldFont, PdfFont cambriaFont) throws IOException {
		Table table = getPDFTable(documentLayoutSetup, 2);
		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(cambriaFont).setFontSize(contentFontSize).setTextAlignment(TextAlignment.LEFT);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(getParagraph(SIGNATURE_1), signatureCellLayoutSetup),
				new CellData(getParagraph(""), signatureCellLayoutSetup)));
		document.add(table);
		addBlankLine(document, true, 2);
		table = getPDFTable(documentLayoutSetup, 2);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(getParagraph(SIGNATURE_2), signatureCellLayoutSetup),
				new CellData(getParagraph(SIGNATURE_3), signatureCellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER))));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(getParagraph(""), signatureCellLayoutSetup),
				new CellData(getParagraph(SIGNATURE_4), signatureCellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER))));
		document.add(table);
	}
}
