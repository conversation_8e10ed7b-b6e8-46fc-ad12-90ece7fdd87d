package com.lernen.cloud.pdf.exam.reports._10001.v2;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.*;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.report.*;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.images.LogoProvider;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.List;

public class ExamReportGenerator9thTo12th10001 extends ExamReportGenerator10001 {
    public ExamReportGenerator9thTo12th10001(AssetProvider assetProvider) {
        super(assetProvider);
        //TODO Auto-generated constructor stub
    }

    private static final Logger logger = LogManager.getLogger(ExamReportGenerator9thTo12th10001.class);
    protected static final String SCHOLASTIC_EXAM_DESCRIPTION_9th_TO_10th = "HY = Half Yearly, IA = Internal Assessment";


    @Override
    public DocumentOutput generateReport(Institute institute, Student student, String reportType,
                                         ExamReportData examReportData, String documentName, StudentManager studentManager) {
        try {

            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

            ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);

            PdfFont regularFont = getCambriaFont();
            PdfFont boldFont = getCambriaBoldFont();

            Image examInchargeSignatureImage = getExamControllerSignature();

            generateStudentReportCard(examReportCardLayoutData, institute, examReportData, studentManager, reportType,
                    1, boldFont, regularFont, examInchargeSignatureImage);

            examReportCardLayoutData.getDocument().close();
            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating report card institute {}, student {}, reportType {} ",
                    institute.getInstituteId(), student.getStudentId(), reportType, e);
        }
        return null;
    }

    private void generateStudentReportCard(ExamReportCardLayoutData examReportCardLayoutData,
                                           Institute institute, ExamReportData examReportData,
                                           StudentManager studentManager, String reportType,
                                           int pageNumber, PdfFont boldFont, PdfFont regularFont,
                                           Image examControllerSignature) throws IOException {

        generateMetaDataLayout(examReportCardLayoutData, institute, examReportData.getStudentLite(), studentManager, reportType,
                examReportData, pageNumber, boldFont, regularFont);


        Set<UUID> nonAdditionalSubjects = getNonAdditionalSubjects(examReportData);
        generateScholasticMarksGrid(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
                regularFont, boldFont, examReportCardLayoutData.getContentFontSize() - 1,
                examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(),
                "Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType),
                nonAdditionalSubjects, "MM", "MO", "#ff0000",
                "-");

        if(!CollectionUtils.isEmpty(examReportData.getExamReportStructure().getExamReportCourseStructure()
                .get(CourseType.SCHOLASTIC).getAdditionalCourses())) {
            addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
            generateAdditionalCustomScholasticMarksGrid(examReportCardLayoutData, examReportData, GridConfigs.forSkipTotalRow(),
                    "Additional Subjects", getScholasticMarksGridSubjectWidth(reportType), boldFont, regularFont);
        }
        // we are passing description as this is a new template and we do not want to make changes in our previous session report cards.
        generateScholasticExamDescription(examReportCardLayoutData.getDocument(),
                examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() + 1f,
                examReportData, boldFont, regularFont);

        if (examReportData.getCourseTypeExamReportMarksGrid().containsKey(CourseType.COSCHOLASTIC)) {
            addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
            generateCoScholasticMarksGrid(examReportCardLayoutData.getDocument(),
                    examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
                    examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(),
                    "Co-Scholastic Subjects", getCoScholasticMarksGridSubjectWidth(reportType), "-");
        }

        addBlankLine(examReportCardLayoutData, false, 1);
        generateStudentAttributeTable(examReportCardLayoutData, examReportData, boldFont, regularFont);

        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
        generateResultSummary(examReportCardLayoutData.getDocument(),
                examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
                examReportData, reportType, boldFont, regularFont, studentManager);
        generateRemarksSection(examReportCardLayoutData, examReportData, examReportCardLayoutData.getContentFontSize() - 1,
                boldFont, regularFont);
        generateSignatureBox(examReportCardLayoutData.getDocument(),
                examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
                examReportCardLayoutData.getDefaultBorderWidth(), pageNumber, boldFont, regularFont, examControllerSignature);

        generateBorderLayout(examReportCardLayoutData, pageNumber, 3f);
    }

    protected void generateScholasticExamDescription(Document document, DocumentLayoutSetup documentLayoutSetup,
                                                     float contentFontSize, ExamReportData examReportData, PdfFont boldFont, PdfFont regularFont) throws IOException {

        List<UUID> standard9thTo10th = new ArrayList<UUID>();
        standard9thTo10th.add(UUID.fromString("703d8857-3dcc-479c-8d2f-ffbeda238cdd"));//Class IX
        standard9thTo10th.add(UUID.fromString("ef08ce70-7c16-4dfe-a5e6-c60fbc6483f6"));//Class X

        UUID standardId = examReportData.getStandardMetaData().getStandardId();
        String description = standard9thTo10th.contains(standardId) ? SCHOLASTIC_EXAM_DESCRIPTION_9th_TO_10th : "" ;

        if(StringUtils.isBlank(description)) {
            return;
        }
        Text descTitle = new Text("Description: " + description).setFont(regularFont).setFontSize(contentFontSize - 3);
        Paragraph desc = new Paragraph();
        desc.add(descTitle);
        document.add(desc.setFontColor(new DeviceRgb(EColorUtils.purpleR, EColorUtils.purpleG, EColorUtils.purpleB)));
    }


    protected ExamReportCardLayoutData generateReportCardLayoutData(Institute institute, DocumentOutput documentOutput)
            throws IOException {

        DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A5);
        float contentFontSize = 9f;
        float defaultBorderWidth = 0.5f;
        Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
        float logoWidth = 50f;
        float logoHeight = 50f;

        int instituteId = institute.getInstituteId();
        return new ExamReportCardLayoutData(document, documentLayoutSetup, null, null, contentFontSize,
                defaultBorderWidth, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId));
    }

    protected void generateMetaDataLayout(ExamReportCardLayoutData examReportCardLayoutData, Institute institute,
                                          StudentLite studentLite, StudentManager studentManager, String reportType, ExamReportData examReportData,
                                          int pageNumber, PdfFont boldFont, PdfFont regularFont) throws IOException {

        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
        //Institute watermark
        int instituteId = institute.getInstituteId();
        float watermarkImageHeightWidth = 300f;
        generateWatermark(examReportCardLayoutData,
                instituteId, watermarkImageHeightWidth, watermarkImageHeightWidth, watermarkImageHeightWidth / 5 ,
                watermarkImageHeightWidth / 2);
        generateHeader(examReportCardLayoutData, studentLite, institute, reportType, 30f,
                examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.89f,
                boldFont, regularFont);
        generateStudentInformation(examReportCardLayoutData, studentLite, examReportData, boldFont, regularFont);
    }

    @Override
    public DocumentOutput generateClassReport(Institute institute, String reportType,
                                              List<ExamReportData> examReportDataList, String documentName, StudentManager studentManager) {

        try {
            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);

            sortClassExamReports(examReportDataList);

            int pageNumber = 1;
            PdfFont regularFont = getCambriaFont();
            PdfFont boldFont = getCambriaBoldFont();
            Image examInchargeSignatureImage = getExamControllerSignature();

            for (ExamReportData examReportData : examReportDataList) {
                generateStudentReportCard(examReportCardLayoutData, institute, examReportData, studentManager, reportType,
                        pageNumber, boldFont, regularFont, examInchargeSignatureImage);

                if (pageNumber != examReportDataList.size()) {
                    examReportCardLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                }
                pageNumber++;
            }

            examReportCardLayoutData.getDocument().close();
            return documentOutput;

        } catch (IOException e) {
            e.printStackTrace();
        }

        return null;
    }

    @Override
    public DocumentOutput generateClassResultReport(Institute institute, String reportType, List<ExamReportData> examReportDataList,
                                                    String documentName, StudentManager studentManager, int studentPerPage) {
        try {
            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A5);
            Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
            PdfFont regularFont = getRegularFont();
            PdfFont boldFont = getRegularBoldFont();

            CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
            marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(DEFAULT_FONT_SIZE)
                    .setBorder(new SolidBorder(DEFAULT_BORDER_WIDTH)).setTextAlignment(TextAlignment.CENTER);

            CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
                    .setTextAlignment(TextAlignment.LEFT);

            String instituteName = institute.getInstituteName().toUpperCase();
            String sessionName = "";
            if(!CollectionUtils.isEmpty(examReportDataList)) {
                sessionName = "Session : " + examReportDataList.get(0).getStudentLite().getStudentSessionData().getShortYearDisplayName();
            }
            generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName,
                    sessionName, marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));

            sortClassExamReports(examReportDataList);
            int pageNumber = 1;
            for (ExamReportData examReportData : examReportDataList) {
                generateClassReportDataStudentDetails(documentLayoutSetup, document, courseCellLayoutSetup, examReportData.getStudentLite());
                generateScholasticMarksGrid(document,
                        documentLayoutSetup, DEFAULT_FONT_SIZE, DEFAULT_BORDER_WIDTH, examReportData, GridConfigs.forOnlyObtainedTotalRow(),
                        "Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType),
                        null, "#ff0000");
                generateClassReportDataStudentResult(documentLayoutSetup, document, examReportData, courseCellLayoutSetup);
                if (pageNumber % studentPerPage == 0) {
                    document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                    generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName, sessionName,
                            marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));
                } else {
                    addBlankLine(document, false, 1);
                }
                pageNumber++;
            }
            document.close();
            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
                    reportType, e);
        }
        return null;
    }

}

