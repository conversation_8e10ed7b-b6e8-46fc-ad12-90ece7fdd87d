package com.lernen.cloud.pdf.invoice.fee;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.configurations.FeeInvoicePreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.fees.payment.*;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.transport.StudentTransportDetails;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

public class FeeInvoiceGenerator10140_10215 extends GlobalShrinkedSizeFeeInvoiceGenerator {

	public FeeInvoiceGenerator10140_10215(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	public static final float[] DEFAULT_ACTIVE_FEE_HEAD_HEADER_WIDTH = { 0.06f, 0.35f, 0.28f, 0.31f };

	@Override
	public DocumentOutput generateInvoice(Institute institute, FeePaymentInvoiceSummary feePaymentInvoiceSummary,
										  StudentTransportDetails studentTransportDetails, String documentName, boolean officeCopy, UserType userType, StudentFeesDetailsLite studentFeesDetailsLite, FeeInvoicePreferences feeInvoicePreferences) {
		try {
			DocumentOutput invoice = new DocumentOutput(documentName, new ByteArrayOutputStream());
			if(userType == UserType.STUDENT) {
				officeCopy = false;
			}
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(officeCopy);
			float contentFontSize = 8f;
			float headerFontSize = 11f;
			float defaultBorderWidth = 0.1f;
			Document document = initDocument(invoice.getContent(), documentLayoutSetup);
			DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getNunitoRegularFont(), getAleoBoldFont(), null, null, DEFAULT_LOGO_WIDTH, DEFAULT_LOGO_HEIGHT, LogoProvider.INSTANCE.getLogo(institute.getInstituteId()), getGlacialRegularFont(), getHindiBoldFont());
			
			generateFeeInvoice(institute, documentLayoutData, feePaymentInvoiceSummary,
					headerFontSize, defaultBorderWidth, contentFontSize, 1, STUDENT_COPY_TEXT, userType);

			document.close();
			return invoice;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public void generateFeeInvoice(Institute institute, DocumentLayoutData documentLayoutData,
								   FeePaymentInvoiceSummary feePaymentInvoiceSummary,
								   float headerFontSize, float defaultBorderWidth, float contentFontSize, int pageNumber,
								   String studentCopyText, UserType userType) throws IOException {
		
		Document document = documentLayoutData.getDocument();
		DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();

		generateDynamicImageProvider(documentLayoutData, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);
		generateHeader(document, documentLayoutSetup, feePaymentInvoiceSummary, institute, headerFontSize,
				defaultBorderWidth, studentCopyText, userType);
		generateStudentInformation(document, documentLayoutSetup, contentFontSize, feePaymentInvoiceSummary,
				defaultBorderWidth);
		generateFeeContent(document, documentLayoutSetup, contentFontSize, feePaymentInvoiceSummary,
				defaultBorderWidth);
		generateFeePaymentSummary(document, documentLayoutSetup, contentFontSize, feePaymentInvoiceSummary,
				defaultBorderWidth);
		generateSignatureBox(document, documentLayoutSetup, contentFontSize, defaultBorderWidth);
		if (feePaymentInvoiceSummary.getFeePaymentTransactionMetaData()
				.getFeePaymentTransactionStatus() == FeePaymentTransactionStatus.CANCELLED) {
			// addWaterMark(document, documentLayoutSetup, "Cancelled");
			PdfPage pdfPage = document.getPdfDocument().getPage(pageNumber);
			Rectangle pagesize = pdfPage.getPageSizeWithRotation();

			float x = documentLayoutSetup.isOfficeCopy() ? pagesize.getWidth() / 8 - 50 : pagesize.getWidth() / 4 + 30;
			float y = documentLayoutSetup.isOfficeCopy() ? pagesize.getHeight() * 0.75f : pagesize.getHeight() * 0.70f;

			addWaterMark(document, ImageProvider.INSTANCE.getImage(ImageProvider.CANCELLED_TEXT_IMAGE2), x, y, pageNumber);
		}
	}

	public void generateActiveFeeContent(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize, FeePaymentInvoiceSummary feePaymentInvoiceSummary, float defaultBorderWidth)
			throws IOException {

		if (CollectionUtils.isEmpty(feePaymentInvoiceSummary.getFeeIdInvoices())) {
			return;
		}

		int singleFeeContentColumn = 1;
		CellLayoutSetup feeCellLayoutSetup = new CellLayoutSetup();
		feeCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.LEFT).setBorderLeft(new SolidBorder(defaultBorderWidth))
				.setBorderRight(new SolidBorder(defaultBorderWidth));

		int count = 0;
		for (FeeIdInvoice feeIdInvoice : feePaymentInvoiceSummary.getFeeIdInvoices()) {
			count++;
			Table feeHeadTable = getPDFTable(documentLayoutSetup, DEFAULT_ACTIVE_FEE_HEAD_HEADER_WIDTH);

			CellLayoutSetup feeHeadCellLayoutSetup = new CellLayoutSetup();
			feeHeadCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
					.setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.LEFT);

			if (count == 1) {
				addRow(feeHeadTable, documentLayoutSetup,
						Arrays.asList(new CellData("#", feeHeadCellLayoutSetup),
								new CellData("Particulars", feeHeadCellLayoutSetup),
								new CellData("Amount(INR)", feeHeadCellLayoutSetup),
								new CellData("Paid Amount", feeHeadCellLayoutSetup)));
			}
			Table feeTable = getPDFTable(documentLayoutSetup, singleFeeContentColumn);
			addRow(feeTable, documentLayoutSetup,
					Arrays.asList(getParagraph(feeIdInvoice.getFeeConfigurationBasicInfo().getFeeName())),
					feeCellLayoutSetup);
			document.add(feeTable);
			int feeHeadCount = 1;
			feeHeadCellLayoutSetup.setPdfFont(getRegularFont());
			for (FeeHeadInvoice feeHeadInvoice : feeIdInvoice.getFeeHeadInvoices()) {
				addRow(feeHeadTable, documentLayoutSetup, Arrays.asList(
						new CellData(String.valueOf(feeHeadCount++), feeHeadCellLayoutSetup),
						new CellData(feeHeadInvoice.getFeeHeadPaymentDetails().getFeeHeadConfiguration().getFeeHead(),
								feeHeadCellLayoutSetup),
						new CellData(String.valueOf(feeHeadInvoice.getFeeHeadPaymentDetails().getAssignedAmount()),
								feeHeadCellLayoutSetup),
						new CellData(String.valueOf(feeHeadInvoice.getFeeHeadTransactionAmounts().getPaidAmount()),
								feeHeadCellLayoutSetup)));
			}
			document.add(feeHeadTable);
		}

	}

	@Override
	public DocumentOutput generateBulkInvoices(Institute institute, List<FeePaymentInvoiceSummary>
			feePaymentInvoiceSummaryList, String documentName, boolean doubleCopy, FeeInvoicePreferences feeInvoicePreferences) {
		try {
			DocumentOutput invoice = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(doubleCopy);
			float contentFontSize = 8f;
			float headerFontSize = 11f;
			float defaultBorderWidth = 0.1f;
			Document document = initDocument(invoice.getContent(), documentLayoutSetup);
			DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getNunitoRegularFont(), getAleoBoldFont(), null, null, DEFAULT_LOGO_WIDTH, DEFAULT_LOGO_HEIGHT, LogoProvider.INSTANCE.getLogo(institute.getInstituteId()), getGlacialRegularFont(), getHindiBoldFont());

			if(CollectionUtils.isEmpty(feePaymentInvoiceSummaryList)) {
				document.close();
				return invoice;
			}
			
			int pageNumber = 1;
			for (FeePaymentInvoiceSummary feePaymentInvoiceSummary : feePaymentInvoiceSummaryList) {
				generateFeeInvoice(institute, documentLayoutData, feePaymentInvoiceSummary,
						headerFontSize, defaultBorderWidth, contentFontSize, pageNumber, OFFICE_COPY_TEXT, null);

				if (pageNumber != feePaymentInvoiceSummaryList.size()) {
					document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				pageNumber++;

			}
			document.close();
			return invoice;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
}
