package com.lernen.cloud.pdf.certificates.transfer;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Image;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.*;
import com.lernen.cloud.core.api.user.UserCategory;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;

public class TransferCertificateGenerator10228 extends CBSETransferCertificateGenerator {

    public TransferCertificateGenerator10228(AssetProvider assetProvider) {
                super(assetProvider);
                //TODO Auto-generated constructor stub
        }

private static final Logger logger = LogManager.getLogger(TransferCertificateGenerator10228.class);

    public static final float SQUARE_BORDER_MARGIN = 12f;

    @Override
    public DocumentOutput generateTransferCertificate(StudentManager studentManager, Institute institute, Student student,
                                                      String documentName) {
        try {
            float squareBorderMargin = 8f;
            float borderInnerGap = 2f;

            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

            DocumentLayoutData admissionFormLayoutData = generateTransferCertificateLayoutData(institute, documentOutput,
                    60f, 60f);
            Document document = admissionFormLayoutData.getDocument();
            int index = 1;

            addBlankLine(document, false, 1);
            StudentTransferCertificateDetails studentTransferCertificateDetails = student.getStudentTransferCertificateDetails();
            generatePageHeader(admissionFormLayoutData, institute, studentTransferCertificateDetails);

            addBlankLine(document, true, 6);

            index = generateBasicInformationPage(admissionFormLayoutData, studentManager, institute,
                    studentTransferCertificateDetails, index, false, student);

            generateSignatureBox(admissionFormLayoutData, squareBorderMargin, borderInnerGap);

            generateMetadata(admissionFormLayoutData, squareBorderMargin, borderInnerGap);

            document.close();

            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating admission form for institute {}, student id {}",
                    institute.getInstituteId(), student.getStudentId(), e);
        }
        return null;
    }

    protected DocumentLayoutData generateTransferCertificateLayoutData(Institute institute, DocumentOutput documentOutput,
                                                                       float logoWidth, float logoHeight) throws IOException {

        DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false, PageSize.A4, 5f, 5f,
                20f, 0f);
        float contentFontSize = 11f;
        float defaultBorderWidth = 0.5f;
        Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
        int instituteId = institute.getInstituteId();
        DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getCambriaFont(),
                getCambriaBoldFont(), contentFontSize, defaultBorderWidth, logoWidth, logoHeight,
                LogoProvider.INSTANCE.getLogo(instituteId), null, null);
        documentLayoutData.setImageFrame(ImageProvider.INSTANCE.getImage(ImageProvider.IMAGE_FRAME));
        return documentLayoutData;
    }

    protected void generatePageHeader(DocumentLayoutData documentLayoutData, Institute institute,
                                      StudentTransferCertificateDetails studentTransferCertificateDetails) throws IOException {
        Document document = documentLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = documentLayoutData.getDocumentLayoutSetup();
        float headerHeight = 140f;
        generateImage(document, documentLayoutSetup, ImageProvider.INSTANCE.getImage(ImageProvider._10228_TC_HEADER),
                documentLayoutSetup.getPageSize().getWidth() - SQUARE_BORDER_MARGIN * 2, headerHeight,
                SQUARE_BORDER_MARGIN, documentLayoutSetup.getPageSize().getHeight() - SQUARE_BORDER_MARGIN - headerHeight);

    }
}
