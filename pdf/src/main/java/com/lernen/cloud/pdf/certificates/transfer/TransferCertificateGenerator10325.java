package com.lernen.cloud.pdf.certificates.transfer;

import java.io.ByteArrayOutputStream;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.layout.Document;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentTransferCertificateDetails;
import com.lernen.cloud.core.lib.student.StudentManager;

public class TransferCertificateGenerator10325 extends CBSETransferCertificateGenerator{

    public TransferCertificateGenerator10325(AssetProvider assetProvider){
        super(assetProvider);
    }
    
    private static final Logger logger = LogManager.getLogger(TransferCertificateGenerator10325.class);
     public DocumentOutput generateTransferCertificate(Student<PERSON>anager student<PERSON>anager, Institute institute, Student student,
                                                      String documentName) {
        try {
            float squareBorderMargin = 8f;
            float borderInnerGap = 2f;

            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

            DocumentLayoutData admissionFormLayoutData = generateTransferCertificateLayoutData(institute, documentOutput,
                    60f, 60f);
            Document document = admissionFormLayoutData.getDocument();
            int index = 1;

            addBlankLine(document, false, 1);
            StudentTransferCertificateDetails studentTransferCertificateDetails = student.getStudentTransferCertificateDetails();
            generatePageHeader(admissionFormLayoutData, institute, studentTransferCertificateDetails, admissionFormLayoutData.getContentFontSize()+8f);

            addBlankLine(document, false, 1);

            index = generateBasicInformationPage(admissionFormLayoutData, studentManager, institute,
                    studentTransferCertificateDetails, index, true, student);

            generateSignatureBox(admissionFormLayoutData, squareBorderMargin, borderInnerGap);

            generateMetadata(admissionFormLayoutData, squareBorderMargin, borderInnerGap);

            document.close();

            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating admission form for institute {}, student id {}",
                    institute.getInstituteId(), student.getStudentId(), e);
        }
        return null;
    }
}
