package com.lernen.cloud.pdf.exam.reports._10360;


import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.colors.WebColors;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.kernel.pdf.canvas.draw.SolidLine;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.*;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.HorizontalAlignment;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.ExamGrade;
import com.lernen.cloud.core.api.examination.report.*;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.pdf.exam.reports.ExamReportGenerator;
import com.lernen.cloud.pdf.exam.reports.IExamReportCardGenerator;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.*;

/**
 * 
 * <AUTHOR>
 *
 */

 public class ExamReportGenerator10360 extends ExamReportGenerator implements IExamReportCardGenerator {

	public ExamReportGenerator10360(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(ExamReportGenerator10360.class);
	public static final float DEFAULT_PAGE_SIDE_MARGIN = 25f;
	public static final float DEFAULT_PAGE_TOP_MARGIN = 10f;
	public static final float DEFAULT_PAGE_BOTTOM_MARGIN = 10f;
	public static final float SQUARE_BORDER_MARGIN = 12f;

	protected static final String HALF_YEARLY_REPORT_TYPE = "HALF_YEARLY";
	protected static final String ANNUAL_REPORT_TYPE = "ANNUAL";
	protected static final String SCHOLASTIC_EXAM_DESCRIPTION_NURSERY_TO_10th = "PT = Periodic Test, NB = Notebook, SE = Subject Enrichment, HYE = Half Yearly Exam, AE = Yearly or Annual Exam";
	protected static final String COSCHOLASTIC_EXAM_DESCRIPTION_NURSERY_TO_10th = "A = Outstanding, B = Very Good, C = Fair";
	@Override
	public DocumentOutput generateReport(Institute institute, Student student, String reportType,
										 ExamReportData examReportData, String documentName, StudentManager studentManager) {
		try {

			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);

			PdfFont regularFont = examReportCardLayoutData.getRegularFont();
			PdfFont boldFont = examReportCardLayoutData.getBoldFont();

			generateStudentReportCard(examReportCardLayoutData, institute, examReportData, studentManager, reportType,
					1, boldFont, regularFont);

			examReportCardLayoutData.getDocument().close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, student {}, reportType {} ",
					institute.getInstituteId(), student.getStudentId(), reportType, e);
		}
		return null;
	}

	private void generateStudentReportCard(ExamReportCardLayoutData examReportCardLayoutData,
										   Institute institute, ExamReportData examReportData,
										   StudentManager studentManager, String reportType,
										   int pageNumber, PdfFont boldFont, PdfFont regularFont
										   ) throws IOException {

		generateMetaDataLayout(examReportCardLayoutData, institute, examReportData.getStudentLite(), studentManager, reportType,
				examReportData, pageNumber, boldFont, regularFont);


		Set<UUID> nonAdditionalSubjects = getNonAdditionalSubjects(examReportData);
		generateScholasticMarksGrid(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
				regularFont, boldFont, examReportCardLayoutData.getContentFontSize() - 1,
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(),
				"Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType),
				nonAdditionalSubjects, "MM", "MO", "#ff0000",
				"-");

		if(!CollectionUtils.isEmpty(examReportData.getExamReportStructure().getExamReportCourseStructure()
				.get(CourseType.SCHOLASTIC).getAdditionalCourses())) {
			addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
			generateAdditionalCustomScholasticMarksGrid(examReportCardLayoutData, examReportData, GridConfigs.forSkipTotalRow(),
					"Additional Subjects", getScholasticMarksGridSubjectWidth(reportType), boldFont, regularFont);
		}

		generateScholasticExamDescription(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
				examReportData, boldFont, regularFont);

		if (examReportData.getCourseTypeExamReportMarksGrid().containsKey(CourseType.COSCHOLASTIC)) {
			addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
			generateCoScholasticMarksGrid(examReportCardLayoutData.getDocument(),
					examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
					examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(),
					"Co-Scholastic Subjects", getCoScholasticMarksGridSubjectWidth(reportType), "-");
		}
		generateCoScholasticExamDescription(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
				examReportData, boldFont, regularFont);

		addBlankLine(examReportCardLayoutData, false, 1);
		generateResultSummary(examReportCardLayoutData,examReportData, reportType, boldFont, regularFont, studentManager);
		generateRemarksSection(examReportCardLayoutData, examReportData,boldFont, regularFont);

		addBlankLine(examReportCardLayoutData, false, 2);
		generateSignatureBox(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
				examReportCardLayoutData.getDefaultBorderWidth(), pageNumber, boldFont, regularFont, examReportData.getCourseTypeExamGrades());

		generateGradeBox(examReportCardLayoutData.getDocument(),
		examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
		examReportCardLayoutData.getDefaultBorderWidth(), examReportData, examReportCardLayoutData);
		generateBorderLayout(examReportCardLayoutData, pageNumber);
	}

	protected void generateAdditionalCustomScholasticMarksGrid(ExamReportCardLayoutData examReportCardLayoutData,
															   ExamReportData examReportData, GridConfigs gridConfigs,
															   String subjectColumnTitle, float subjectColumnWidth, PdfFont boldFont, PdfFont regularFont) throws IOException {
		ExamReportStructure examReportStructure = examReportData.getExamReportStructure();
		if (examReportStructure == null || CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure())
				|| examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC) == null
				|| CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC)
				.getAdditionalCourses())) {
			return;
		}

		generateScholasticMarksGrid(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
				regularFont, boldFont, examReportCardLayoutData.getContentFontSize() - 1,
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, gridConfigs,
				subjectColumnTitle, subjectColumnWidth,
				examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC).getAdditionalCourses(),
				"MM", "MO", "#ff0000", "-");
	}

	protected float getScholasticMarksGridSubjectWidth(String reportType) {
		return 0.2f;
	}

	protected float getCoScholasticMarksGridSubjectWidth(String reportType) {
		if (reportType.equalsIgnoreCase(HALF_YEARLY_REPORT_TYPE)) {
			return 0.5f;
		}
		if (reportType.equalsIgnoreCase(ANNUAL_REPORT_TYPE)) {
			return 0.4f;
		}
		return 0.3f;
	}

	protected ExamReportCardLayoutData generateReportCardLayoutData(Institute institute, DocumentOutput documentOutput)
			throws IOException {

		DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false);
		float contentFontSize = 12f;
		float defaultBorderWidth = 0.5f;
		Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
		float logoWidth = 70f;
		float logoHeight = 70f;

		int instituteId = institute.getInstituteId();
		return new ExamReportCardLayoutData(document, documentLayoutSetup, getCambriaFont(), getCambriaBoldFont(), contentFontSize,
				defaultBorderWidth, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId));
	}

	protected void generateMetaDataLayout(ExamReportCardLayoutData examReportCardLayoutData, Institute institute,
										  StudentLite studentLite, StudentManager studentManager, String reportType, ExamReportData examReportData,
										  int pageNumber, PdfFont boldFont, PdfFont regularFont) throws IOException {

		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
		//Institute watermark
		int instituteId = institute.getInstituteId();
		float watermarkImageHeightWidth = 400f;
		generateWatermark(examReportCardLayoutData,
				instituteId, watermarkImageHeightWidth, watermarkImageHeightWidth, watermarkImageHeightWidth / 5 + 20,
				watermarkImageHeightWidth / 2);
		generateHeader(examReportCardLayoutData, studentLite, institute, reportType, 40f,
				examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.89f,
				boldFont, regularFont);
		generateStudentInformation(examReportCardLayoutData, studentLite, examReportData, boldFont, regularFont);
	}

	@Override
	public DocumentLayoutSetup initDocumentLayoutSetup(boolean officeCopy) {
		return initDocumentLayoutSetup(officeCopy, officeCopy ? DEFAULT_PAGE_SIZE.rotate() : DEFAULT_PAGE_SIZE,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN : DEFAULT_PAGE_TOP_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN : DEFAULT_PAGE_BOTTOM_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_SIDE_MARGIN : DEFAULT_PAGE_SIDE_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_MIDDLE_MARGIN : 0f);
	}

	protected void generateHeader(ExamReportCardLayoutData examReportCardLayoutData, StudentLite studentLite,
								  Institute institute, String reportType, float offsetX, float offsetY,
								  PdfFont boldFont, PdfFont regularFont) throws IOException {

		/* generateDynamicImageProvider(examReportCardLayoutData, offsetX, offsetY, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO); */
		generateDynamicImageProvider(examReportCardLayoutData, offsetX, offsetY, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

		int singleContentColumn = 1;
		Table table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(examReportCardLayoutData.getContentFontSize()+2f).setTextAlignment(TextAlignment.CENTER);

		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getInstituteName().toUpperCase()).setPaddingLeft(20f).setMarginBottom(-15f)
						.setFontColor(Color.convertRgbToCmyk(EColorUtils.redDeviceRgb))),
				cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize()+10f).setPdfFont(getPoppinsBoldFont()));
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getLetterHeadLine1())),
				cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize()-2f).setPdfFont(regularFont));
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getLetterHeadLine2())),
				cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize()-2f).setPdfFont(regularFont));

		examReportCardLayoutData.getDocument().add(table);
		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
		table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn).setBorder(new SolidBorder(examReportCardLayoutData.getDefaultBorderWidth())).setBackgroundColor(EColorUtils.lightYellowDeviceRgb);
		String headerExamTitle = reportType.equalsIgnoreCase(HALF_YEARLY_REPORT_TYPE) ? "Half-Yearly Progress Report"
				: "REPORT CARD FOR ACADEMIC SESSION";
		headerExamTitle += " (" + studentLite.getStudentSessionData().getShortYearDisplayName() + ")";
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(headerExamTitle).setFontSize(examReportCardLayoutData.getContentFontSize() + 1)
						.setFontColor(new DeviceRgb(EColorUtils.purpleR, EColorUtils.purpleG, EColorUtils.purpleB))),
				cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize()-1f).setPdfFont(boldFont));
		examReportCardLayoutData.getDocument().add(table);
	}

	protected void generateStudentInformation(ExamReportCardLayoutData examReportCardLayoutData,
											  StudentLite studentLite, ExamReportData examReportData,
											  PdfFont boldFont, PdfFont regularFont) throws IOException {

		Document document = examReportCardLayoutData.getDocument();
		DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
		float contentFontSize = examReportCardLayoutData.getContentFontSize() - 1;

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.4f, 0.13f, 0.47f}).setBorder(new SolidBorder(examReportCardLayoutData.getDefaultBorderWidth())).setBorderTop(null);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize).setPaddingLeft(5f);

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);

		Paragraph studentName = getKeyValueParagraph("STUDENT NAME : ", 
				StringUtils.isBlank(studentLite.getName()) ? EMPTY_TEXT:studentLite.getName().toUpperCase(),
				EColorUtils.darkBlueColorList, EColorUtils.lightBlckColorList, boldFont, regularFont);
		Paragraph fatherName = getKeyValueParagraph("FATHER'S NAME : ", 
				StringUtils.isBlank(studentLite.getFathersName()) ? EMPTY_TEXT:studentLite.getFathersName().toUpperCase(),
				EColorUtils.darkBlueColorList, EColorUtils.lightBlckColorList, boldFont,regularFont);
		Paragraph dob = getKeyValueParagraph("DATE OF BIRTH : ",
				studentLite.getDateOfBirth() == null || studentLite.getDateOfBirth() <= 0 ? EMPTY_TEXT
						: DateUtils.getFormattedDate(studentLite.getDateOfBirth(), DATE_FORMAT, User.DFAULT_TIMEZONE),
				EColorUtils.darkBlueColorList, EColorUtils.lightBlckColorList, boldFont, regularFont);
		Paragraph motherName = getKeyValueParagraph("MOTHER'S NAME : ", 
				StringUtils.isBlank(studentLite.getMothersName()) ? EMPTY_TEXT : studentLite.getMothersName().toUpperCase(),
				EColorUtils.darkBlueColorList, EColorUtils.lightBlckColorList, boldFont, regularFont);
		Paragraph admissionNumber = getKeyValueParagraph("ADMISSION NO. : ", studentLite.getAdmissionNumber().toUpperCase(),
				EColorUtils.darkBlueColorList, EColorUtils.lightBlckColorList, boldFont, regularFont);
		Paragraph classValue = getKeyValueParagraph("CLASS : ",
				studentLite.getStudentSessionData().getStandardNameWithSection().toUpperCase(),
				EColorUtils.darkBlueColorList, EColorUtils.lightBlckColorList, boldFont, regularFont);
		Paragraph rollNo = getKeyValueParagraph("ROLL NO : ",
				StringUtils.isBlank(studentLite.getStudentSessionData().getRollNumber()) ? EMPTY_TEXT :studentLite.getStudentSessionData().getRollNumber().toUpperCase(),
				EColorUtils.darkBlueColorList, EColorUtils.lightBlckColorList, boldFont, regularFont);
		Paragraph dord = getKeyValueParagraph("DATE OF RESULT DECLARATION : ",
				examReportData.getDateOfResultDeclaration() == null || examReportData.getDateOfResultDeclaration() <= 0 ? null : DateUtils.getFormattedDate(examReportData.getDateOfResultDeclaration()),
				EColorUtils.darkBlueColorList, EColorUtils.lightBlckColorList, boldFont, regularFont);

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(studentName, firstCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
						new CellData(admissionNumber, thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(fatherName, firstCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
						new CellData(rollNo, thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(motherName, firstCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
						new CellData(dob, thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(classValue, firstCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
						new CellData(dord, thirdCellLayoutSetup)));
		document.add(table);

		addBlankLine(examReportCardLayoutData.getDocument(), false, 2);
	}

	protected void generateBorderLayout(ExamReportCardLayoutData examReportCardLayoutData, int pageNumber) {
		DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
		PdfCanvas canvas = new PdfCanvas(examReportCardLayoutData.getDocument().getPdfDocument(), pageNumber);
		Color color = WebColors.getRGBColor("#df5143");
		canvas.setColor(color, false);
		canvas.setLineWidth(3f);
		canvas.rectangle(SQUARE_BORDER_MARGIN - 2, SQUARE_BORDER_MARGIN - 2,
				documentLayoutSetup.getPageSize().getWidth() - (SQUARE_BORDER_MARGIN - 2) * 2,
				documentLayoutSetup.getPageSize().getHeight() - (SQUARE_BORDER_MARGIN - 2) * 2);
		canvas.stroke();
		canvas.rectangle(SQUARE_BORDER_MARGIN, SQUARE_BORDER_MARGIN,
				documentLayoutSetup.getPageSize().getWidth() - SQUARE_BORDER_MARGIN * 2,
				documentLayoutSetup.getPageSize().getHeight() - SQUARE_BORDER_MARGIN * 2);
		canvas.stroke();
	}

	protected void generateScholasticExamDescription(Document document, DocumentLayoutSetup documentLayoutSetup,
													 float contentFontSize, ExamReportData examReportData, PdfFont boldFont, PdfFont regularFont) throws IOException {

		String description = SCHOLASTIC_EXAM_DESCRIPTION_NURSERY_TO_10th ;

		if(StringUtils.isBlank(description)) {
			return;
		}
		Text descTitle = new Text("Description: " + description).setFont(regularFont).setFontSize(contentFontSize - 3);
		Paragraph desc = new Paragraph();
		desc.add(descTitle);
		document.add(desc.setFontColor(new DeviceRgb(EColorUtils.purpleR, EColorUtils.purpleG, EColorUtils.purpleB)));
	}
	protected void generateCoScholasticExamDescription(Document document, DocumentLayoutSetup documentLayoutSetup,
													 float contentFontSize, ExamReportData examReportData, PdfFont boldFont, PdfFont regularFont) throws IOException {

		String description = COSCHOLASTIC_EXAM_DESCRIPTION_NURSERY_TO_10th ;

		if(StringUtils.isBlank(description)) {
			return;
		}
		Text descTitle = new Text("Description: " + description).setFont(regularFont).setFontSize(contentFontSize - 3);
		Paragraph desc = new Paragraph();
		desc.add(descTitle);
		document.add(desc.setFontColor(new DeviceRgb(EColorUtils.purpleR, EColorUtils.purpleG, EColorUtils.purpleB)));
	}

	protected void generateResultSummary(ExamReportCardLayoutData examReportCardLayoutData, ExamReportData examReportData,
										 String reportType, PdfFont boldFont, PdfFont regularFont,
										 StudentManager studentManager) throws IOException {

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(examReportCardLayoutData.getContentFontSize()-1)
				.setTextAlignment(TextAlignment.CENTER).setPaddingTop(2f).setPaddingBottom(2f).setBorder(new SolidBorder(examReportCardLayoutData.getDefaultBorderWidth()));

		Table table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), new float[] { 0.20f, 0.18f, 0.18f, 0.11f, 0.18f, 0.15f });

		Paragraph result = getParagraph("-",
				EColorUtils.lightBlckColorList);

		if(examReportData.getExamResultStatus() != null){
			result = getParagraph(examReportData.getExamResultStatus().getDisplayName(),
				EColorUtils.lightBlckColorList);
		}

		Paragraph promotedClass = getParagraph(examReportData.getPromotedTo() == null ? "" : examReportData.getPromotedTo().getStandardName(),
				EColorUtils.lightBlckColorList,regularFont);

		Paragraph obtainedMarks = getParagraph(
				examReportData.getTotalObtainedMarks() == null ? "-"
						: examReportData.getTotalObtainedMarks() * 10 % 10 == 0
						? String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10)
						: String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10d),
					EColorUtils.lightBlckColorList,regularFont);
		Paragraph totalMarks = getParagraph( examReportData.getTotalMaxMarks() == null ? "-"
						: String.valueOf(Math.round(examReportData.getTotalMaxMarks() * 10) / 10),
				EColorUtils.lightBlckColorList,regularFont);
		Paragraph grade = getParagraph(examReportData.getTotalGrade() == null ? "-" : examReportData.getTotalGrade().getGradeName(),
				EColorUtils.lightBlckColorList,regularFont);
		
		String attendedDays = "-";
		String totalDays = "-";
		
		if(examReportData.getTotalAttendedDays() != null) {
			attendedDays = String.valueOf(examReportData.getTotalAttendedDays());
		}
		if(examReportData. getTotalWorkingDays() != null) {
			totalDays = String.valueOf(examReportData.getTotalWorkingDays());
		}
		if (reportType.equalsIgnoreCase(ANNUAL_REPORT_TYPE)) {
			addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph("Result  ", EColorUtils.darkBlueColorList), result, getParagraph("Promoted to  ", EColorUtils.darkBlueColorList), promotedClass, getParagraph("Attendance",EColorUtils.darkBlueColorList), getParagraph(attendedDays+"/"+totalDays)), cellLayoutSetup);
		}
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph("Obtained marks  ", EColorUtils.darkBlueColorList), obtainedMarks, getParagraph("Total marks ", EColorUtils.darkBlueColorList), totalMarks, getParagraph("Grade ", EColorUtils.darkBlueColorList),grade), cellLayoutSetup);

		examReportCardLayoutData.getDocument().add(table);

	}

	protected void generateRemarksSection(ExamReportCardLayoutData examReportCardLayoutData,
										  ExamReportData examReportData, PdfFont boldFont, PdfFont regularFont) throws IOException {

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont)
				.setFontSize(examReportCardLayoutData.getContentFontSize()-1).setTextAlignment(TextAlignment.CENTER)
				.setBorder(new SolidBorder(examReportCardLayoutData.getDefaultBorderWidth())).setBorderTop(null);

		Table remarksTable = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(),
				new float[] { 0.3f, 0.7f});

		addRow(remarksTable, examReportCardLayoutData.getDocumentLayoutSetup(),
				Arrays.asList(getParagraph("Class Teacher's Remark : ",EColorUtils.darkBlueColorList), getParagraph(examReportData.getRemarks() == null ? "" : examReportData.getRemarks(), EColorUtils.darkBlueColorList,regularFont)),
								cellLayoutSetup);
		examReportCardLayoutData.getDocument().add(remarksTable);
	}

	protected void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup,
										float contentFontSize, float defaultBorderWidth, int pageNumber,
										PdfFont boldFont, PdfFont regularFont,Map<CourseType, List<ExamGrade>> examReportGradeList) throws IOException {
		int singleContentColumn = 3;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);
		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.CENTER);

		Cell examInchargeSignatureCellImage = new Cell();
		examInchargeSignatureCellImage.setBorder(null);
		

		Cell emptySignatureCell1 = new Cell();
		emptySignatureCell1.setBorder(null);

		Cell emptySignatureCell2 = new Cell();
		emptySignatureCell2.setBorder(null);

		addRow(table, Arrays.asList(examInchargeSignatureCellImage, emptySignatureCell1, emptySignatureCell2));

		addRow(table, documentLayoutSetup, Arrays.asList(
						getParagraph("Exam Controller").setFontColor(new DeviceRgb(EColorUtils.purpleR, EColorUtils.purpleG, EColorUtils.purpleB)),
						getParagraph("Class Teacher").setFontColor(new DeviceRgb(EColorUtils.purpleR, EColorUtils.purpleG, EColorUtils.purpleB)),
						getParagraph("Principal").setFontColor(new DeviceRgb(EColorUtils.purpleR, EColorUtils.purpleG, EColorUtils.purpleB))),
				signatureCellLayoutSetup);
				
		if (CollectionUtils.isEmpty(examReportGradeList.get(CourseType.SCHOLASTIC))) {
			table.setFixedPosition(30f, 15f, documentLayoutSetup.getPageSize().getWidth() - 100f); 
		}
		table.setFixedPosition(30f, 125f, documentLayoutSetup.getPageSize().getWidth() - 100f);
		document.add(table);
	}

	protected Set<UUID> getNonAdditionalSubjects(ExamReportData examReportData) {
		Set<UUID> nonAdditionalSubjects = new HashSet<UUID>();
		for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportData.getCourseTypeExamReportMarksGrid()
				.get(CourseType.SCHOLASTIC).getExamReportCourseMarksRows()) {
			if (!isAdditionalSubject(examReportCourseMarksRow.getCourse(), examReportData.getExamReportStructure())) {
				nonAdditionalSubjects.add(examReportCourseMarksRow.getCourse().getCourseId());
			}
		}
		return nonAdditionalSubjects;
	}

	protected void sortClassExamReports(List<ExamReportData> examReportDataList) {
		Collections.sort(examReportDataList, new Comparator<ExamReportData>() {

			@Override
			public int compare(ExamReportData e1, ExamReportData e2) {
				StandardSections section1 = e1.getStudentLite().getStudentSessionData().getStandardSection();

				StandardSections section2 = e2.getStudentLite().getStudentSessionData().getStandardSection();

				if (section1 != null && section2 != null) {
					int sectionCompare = section1.getSectionName().compareToIgnoreCase(section2.getSectionName());
					if (sectionCompare != 0) {
						return sectionCompare;
					}
				}

				return e1.getStudentLite().getName().compareToIgnoreCase(e2.getStudentLite().getName());
			}
		});
	}

	@Override
	public DocumentOutput generateClassReport(Institute institute, String reportType,
											  List<ExamReportData> examReportDataList, String documentName, StudentManager studentManager) {

		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);

			sortClassExamReports(examReportDataList);

			int pageNumber = 1;
			PdfFont regularFont = examReportCardLayoutData.getRegularFont();
			PdfFont boldFont = examReportCardLayoutData.getBoldFont();

			for (ExamReportData examReportData : examReportDataList) {
				generateStudentReportCard(examReportCardLayoutData, institute, examReportData, studentManager, reportType,
						pageNumber, boldFont, regularFont);

				if (pageNumber != examReportDataList.size()) {
					examReportCardLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				pageNumber++;
			}

			examReportCardLayoutData.getDocument().close();
			return documentOutput;

		} catch (IOException e) {
			e.printStackTrace();
		}

		return null;
	}

	private void generateGradeBox(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
								  float defaultBorderWidth, ExamReportData examReportData, ExamReportCardLayoutData examReportCardLayoutData) throws IOException {

		Map<CourseType, List<ExamGrade>> gradesList = examReportData.getCourseTypeExamGrades();
		List<ExamGrade> scholasticGrades = gradesList.get(CourseType.SCHOLASTIC);
		if (CollectionUtils.isEmpty(scholasticGrades)) {
			return;
		}
			Table table = getPDFTable(documentLayoutSetup, 1);

			CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
			signatureCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize - 2)
					.setTextAlignment(TextAlignment.CENTER).setBorderTop(new SolidBorder(examReportCardLayoutData.getDefaultBorderWidth())).setWidth(documentLayoutSetup.getPageSize().getWidth()-2);

			addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("INSTRUCTIONS \n Grading scale for scholastic areas : Grades are awarded on a 8 point grading scale as follows")), signatureCellLayoutSetup);
			table.setFixedPosition(25f, 95f,documentLayoutSetup.getPageSize().getWidth() - 100f);
			document.add(table);

			float[] columnWidths = new float[] { 0.24f, 0.24f, 0.24f, 0.24f };
			Table headerTable = getPDFTable(documentLayoutSetup, columnWidths);
			CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
			

			marksCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize - 4)
					.setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);
			addBlankLine(document, false, 1);
		
			List<CellData> headerList = new ArrayList<>();
			headerList.add(new CellData("MARKS RANGE",marksCellLayoutSetup));
			headerList.add(new CellData("GRADE", marksCellLayoutSetup));
			headerList.add(new CellData("MARKS RANGE", marksCellLayoutSetup));
			headerList.add(new CellData("GRADE", marksCellLayoutSetup));

			addRow(headerTable, documentLayoutSetup, headerList);

			
			for (int index = 0; index < scholasticGrades.size() / 2; index++) {
				List<CellData> row = new ArrayList<>();

				// First half data
				ExamGrade grade1 = scholasticGrades.size() > index ? scholasticGrades.get(index) : null;
				String marksRange1 = (grade1 != null) ? grade1.getRangeDisplayName() : "-";
				String gradeName1 = (grade1 != null) ? grade1.getGradeName() : "-";

				// Second half data
				int secondHalfIndex = scholasticGrades.size() / 2 + index;
				ExamGrade grade2 = scholasticGrades.size() > secondHalfIndex ? scholasticGrades.get(secondHalfIndex) : null;
				String marksRange2 = (grade2 != null) ? grade2.getRangeDisplayName() : "-";
				String gradeName2 = (grade2 != null) ? grade2.getGradeName() : "-";

				row.add(new CellData(marksRange1, marksCellLayoutSetup));
				row.add(new CellData(gradeName1, marksCellLayoutSetup));
				row.add(new CellData(marksRange2, marksCellLayoutSetup));
				row.add(new CellData(gradeName2, marksCellLayoutSetup));

				addRow(headerTable, documentLayoutSetup, row);
			}
			headerTable.setFixedPosition(35f, 20f, documentLayoutSetup.getPageSize().getWidth() - 100f);
			document.add(headerTable);
	}

	@Override
	public DocumentOutput generateClassResultReport(Institute institute, String reportType, List<ExamReportData> examReportDataList,
													String documentName, StudentManager studentManager, int studentPerPage) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4);
			Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

			PdfFont regularFont = getRegularFont();
			PdfFont boldFont = getRegularBoldFont();

			CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
			marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(DEFAULT_FONT_SIZE)
					.setBorder(new SolidBorder(DEFAULT_BORDER_WIDTH)).setTextAlignment(TextAlignment.CENTER);

			CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
					.setTextAlignment(TextAlignment.LEFT);

			String instituteName = institute.getInstituteName().toUpperCase();
			String sessionName = "";
			if(!CollectionUtils.isEmpty(examReportDataList)) {
				sessionName = "Session : " + examReportDataList.get(0).getStudentLite().getStudentSessionData().getShortYearDisplayName();
			}
			generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName,
					sessionName, marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));

			sortClassExamReports(examReportDataList);
			int pageNumber = 1;
			for (ExamReportData examReportData : examReportDataList) {
				generateClassReportDataStudentDetails(documentLayoutSetup, document, courseCellLayoutSetup, examReportData.getStudentLite());
				generateScholasticMarksGrid(document,
						documentLayoutSetup, DEFAULT_FONT_SIZE, DEFAULT_BORDER_WIDTH, examReportData, GridConfigs.forOnlyObtainedTotalRow(),
						"Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType),
						null, "#ff0000");
				generateClassReportDataStudentResult(documentLayoutSetup, document, examReportData, courseCellLayoutSetup);
				if (pageNumber % studentPerPage == 0) {
					document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
					generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName, sessionName,
							marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));
				} else {
					addBlankLine(document, false, 1);
				}
				pageNumber++;
			}
			document.close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
					reportType, e);
		}
		return null;
	}

}

