/**
 * 
 */
package com.lernen.cloud.pdf.certificates.dynamic;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.pdf.base.PDFGenerator;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
public abstract class DynamicDocumentGenerator extends PDFGenerator {

	public DynamicDocumentGenerator(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	public abstract DocumentOutput generateDynamicDocument(StudentManager studentManager, Institute institute,
														   Student student, String documentName, String documentHeader);

	public abstract DocumentOutput generateDynamicDocuments(StudentManager studentManager, Institute institute,
															List<Student> students, String documentName, String documentHeader);

}
