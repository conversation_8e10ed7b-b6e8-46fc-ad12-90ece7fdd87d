package com.lernen.cloud.pdf.exam.reports._10135;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.ExamDimensionObtainedValues;
import com.lernen.cloud.core.api.examination.ExamGrade;
import com.lernen.cloud.core.api.examination.report.*;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.examination.report.configs.GridHeaderConfigs;
import com.lernen.cloud.core.api.examination.report.configs.GridTotalMarksRowConfigs;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.core.utils.images.WatermarkProvider;
import com.lernen.cloud.pdf.exam.reports.ExamReportGenerator;
import com.lernen.cloud.pdf.exam.reports.IExamReportCardGenerator;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;

/**
 *
 * <AUTHOR>
 *
 */
public class ExamReportGeneratorLandspaceMode10135 extends ExamReportGenerator implements IExamReportCardGenerator {

	public ExamReportGeneratorLandspaceMode10135(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(ExamReportGeneratorLandspaceMode10135.class);
	public static final float DEFAULT_PAGE_SIDE_MARGIN = 25f;
	public static final float DEFAULT_PAGE_TOP_MARGIN = 10f;
	public static final float DEFAULT_PAGE_BOTTOM_MARGIN = 10f;
	public static final float SQUARE_BORDER_MARGIN = 12f;

	protected static final String HALF_YEARLY_REPORT_TYPE = "HALF_YEARLY";
	protected static final String ANNUAL_REPORT_TYPE = "ANNUAL";
	protected static final String SCHOLASTIC_EXAM_DESCRIPTION = "PA : Periodic Assessment, SEA : Subject Enrichment Activity, SP : Student Portfolio";
	protected static final String COSCHOLASTIC_EXAM_GRADING = "A=Excellent; B=Good; C=Average; D=Poor";

	private static final  String GRADE_A_PLUS = "Outstanding performance and has extraordinary thinking.";
	private static final  String GRADE_A = "Excellent effort, follow deadlines and maintain decency.";
	private static final  String GRADE_B_PLUS = "Gracefully takes the task and has tendency to do better.";
	private static final  String GRADE_B = "Well behaved, has the capacity to work hard in order to reach heights.";
	private static final  String GRADE_C_PLUS = "Well behaved, has the capacity to work hard in order to reach heights.";
	private static final  String GRADE_C = "Average performance but innovative.";
	private static final  String GRADE_D = "Needs to get serious towards studies and maintain sense of humour.";
	private static final  String GRADE_F = "Needs to be very attentive and work hard in order to get promoted.";

//	Red : rgb(204, 0, 0)
//	Purple : rgb(57, 65, 158)
//	Blue : rgb(1, 155, 248)

	private static final Map<String, String> MARKS_GRADE_MAP = new LinkedHashMap<>();
	static {
		MARKS_GRADE_MAP.put("91 - 100", "A+");
		MARKS_GRADE_MAP.put("81 - 90", "A");
		MARKS_GRADE_MAP.put("71 - 80", "B+");
		MARKS_GRADE_MAP.put("61 - 70", "B");
		MARKS_GRADE_MAP.put("51 - 60", "C+");
		MARKS_GRADE_MAP.put("41 - 50", "C");
		MARKS_GRADE_MAP.put("33 - 40", "D");
		MARKS_GRADE_MAP.put("00 - 32", "F");
	}

	@Override
	public DocumentOutput generateReport(Institute institute, Student student, String reportType,
			ExamReportData examReportData, String documentName, StudentManager studentManager) {
		try {

			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);

			PdfFont regularFont = getCambriaFont();
			PdfFont boldFont = getCambriaBoldFont();
			generateStudentReportCard(examReportCardLayoutData, institute, examReportData, studentManager, reportType, 1,
					regularFont, boldFont);

			examReportCardLayoutData.getDocument().close();
			return documentOutput;
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("Error while generating report card institute {}, student {}, reportType {} ",
					institute.getInstituteId(), student.getStudentId(), reportType, e);
		}
		return null;
	}

	private void generateStudentReportCard(ExamReportCardLayoutData examReportCardLayoutData, Institute institute,
										   ExamReportData examReportData, StudentManager studentManager, String reportType,
										   int pageNumber, PdfFont regularFont, PdfFont boldFont) throws IOException {

		generateMetaDataLayout(examReportCardLayoutData, institute, examReportData.getStudentLite(), studentManager, reportType,
				examReportData, pageNumber, regularFont, boldFont);

//		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);

		generateCustomScholasticMarksGrid(examReportCardLayoutData, examReportData, reportType,
				new GridConfigs(new GridHeaderConfigs(false), new GridTotalMarksRowConfigs(true, true, true)),
				"Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType));

//		generateScholasticExamDescription(examReportCardLayoutData.getDocument(),
//				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
//				examReportData, regularFont, boldFont);
		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
		if (examReportData.getCourseTypeExamReportMarksGrid().containsKey(CourseType.COSCHOLASTIC)) {
			generateCoScholasticMarksGradeGridSection(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
					examReportCardLayoutData.getDefaultBorderWidth(),
					examReportData.getCourseTypeExamReportMarksGrid().get(CourseType.COSCHOLASTIC), false, examReportData, regularFont, boldFont);
			generateCoScholasticGradingScheme(examReportCardLayoutData.getDocument(),
					examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
					examReportData, reportType, boldFont);
		}

//		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);

		generateResultSummary(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
				examReportData, reportType, regularFont, boldFont);

		generateSignatureBox(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
				examReportCardLayoutData.getDefaultBorderWidth(), pageNumber, regularFont, boldFont);
	}

	private void generateCoScholasticMarksGradeGridSection(Document document, DocumentLayoutSetup documentLayoutSetup,
														   float contentFontSize, float defaultBorderWidth, ExamReportMarksGrid examReportMarksGrid, boolean showTotal,
														   ExamReportData examReportData, PdfFont regularFont, PdfFont boldFont) throws IOException {

//		float subjectColumnWidth = 0.20f;
		float[] columnWidths = new float[] { 0.15f, 0.14f, 0.14f, 0.01f, 0.54f };
		Table headerTable = getPDFTable(documentLayoutSetup, columnWidths);
//		CourseType courseType = CourseType.COSCHOLASTIC;

		CellLayoutSetup emptyCellLayoutSetup = new CellLayoutSetup();
		CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
		marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(contentFontSize - 1)
				.setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);
		CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
				.setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup boldCenterLayoutSetup = courseCellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER);

		List<CellData> headerList = new ArrayList<>();
		headerList.add(new CellData("Co-curricular activities", boldCenterLayoutSetup.copy().setBorderBottom(null)));
		headerList.add(new CellData("TERM 1", boldCenterLayoutSetup.copy().setBorderBottom(null)));
		headerList.add(new CellData("TERM 2", boldCenterLayoutSetup.copy().setBorderBottom(null)));
		headerList.add(new CellData("", emptyCellLayoutSetup));
		headerList.add(new CellData("Grading System", boldCenterLayoutSetup.copy().setBorderBottom(null)));
		addRow(headerTable, documentLayoutSetup, headerList);
		document.add(headerTable);

		Collections.sort(examReportMarksGrid.getExamReportCourseMarksRows(),
			new Comparator<ExamReportCourseMarksRow>() {
				@Override
				public int compare(ExamReportCourseMarksRow o1, ExamReportCourseMarksRow o2) {
					return o1.getCourse().compareTo(o2.getCourse());
				}
			});

		columnWidths = new float[] { 0.15f, 0.14f, 0.14f, 0.01f, 0.12f, 0.07f, 0.05f, 0.05f, 0.05f, 0.05f, 0.05f, 0.05f, 0.05f };
		headerTable = getPDFTable(documentLayoutSetup, columnWidths);
		int index = 1;
		for(ExamReportCourseMarksRow examReportCourseMarksRow : examReportMarksGrid.getExamReportCourseMarksRows()) {
			if (CollectionUtils.isEmpty(examReportCourseMarksRow.getExamReportCourseMarksColumns())) {
				continue;
			}
			headerList = new ArrayList<>();
			String courseName = examReportCourseMarksRow.getCourse().getCourseName();
			headerList.add(new CellData(courseName, courseCellLayoutSetup));
			for (ExamReportCourseMarksColumn examReportCourseMarksColumn : examReportCourseMarksRow
					.getExamReportCourseMarksColumns()) {
				/**
				 * Assuming grades only for coscholatic rows
				 */
				if (CollectionUtils.isEmpty(examReportCourseMarksColumn.getExamDimensionObtainedValuesList())) {
					continue;
				}
				ExamDimensionObtainedValues examDimensionObtainedValues = examReportCourseMarksColumn
						.getExamDimensionObtainedValuesList().get(0);
				String value = ExamGrade.emptyGrade(examDimensionObtainedValues.getObtainedGrade()) ? "-"
						: examDimensionObtainedValues.getObtainedGrade().getGradeName();
				if(StringUtils.isBlank(examReportCourseMarksColumn.getColorHexCode())) {
					headerList.add(new CellData(getParagraph(value), marksCellLayoutSetup));
				} else {
					List<Integer> rgb = EColorUtils.hex2Rgb(examReportCourseMarksColumn.getColorHexCode());
					headerList.add(new CellData(getParagraph(value, rgb), marksCellLayoutSetup));
				}

			}
			headerList.add(new CellData("", emptyCellLayoutSetup));
			if(index == 1) {
				headerList.add(new CellData("% OF MARKS OBTAINED", boldCenterLayoutSetup));
				headerList.add(new CellData("91 - 100", boldCenterLayoutSetup));
				headerList.add(new CellData("81 - 90", boldCenterLayoutSetup));
				headerList.add(new CellData("71 - 80", boldCenterLayoutSetup));
				headerList.add(new CellData("61 - 70", boldCenterLayoutSetup));
				headerList.add(new CellData("51 - 60", boldCenterLayoutSetup));
				headerList.add(new CellData("41 - 50", boldCenterLayoutSetup));
				headerList.add(new CellData("33 - 40", boldCenterLayoutSetup));
				headerList.add(new CellData("00 - 32", boldCenterLayoutSetup));
			} else if(index == 2) {
				headerList.add(new CellData("GRADE", boldCenterLayoutSetup));
				headerList.add(new CellData("A+", boldCenterLayoutSetup));
				headerList.add(new CellData("A", boldCenterLayoutSetup));
				headerList.add(new CellData("B+", boldCenterLayoutSetup));
				headerList.add(new CellData("B", boldCenterLayoutSetup));
				headerList.add(new CellData("C+", boldCenterLayoutSetup));
				headerList.add(new CellData("C", boldCenterLayoutSetup));
				headerList.add(new CellData("D", boldCenterLayoutSetup));
				headerList.add(new CellData("F", boldCenterLayoutSetup));
			} else {
				headerList.add(new CellData("", emptyCellLayoutSetup));
				headerList.add(new CellData("", emptyCellLayoutSetup));
				headerList.add(new CellData("", emptyCellLayoutSetup));
				headerList.add(new CellData("", emptyCellLayoutSetup));
				headerList.add(new CellData("", emptyCellLayoutSetup));
				headerList.add(new CellData("", emptyCellLayoutSetup));
				headerList.add(new CellData("", emptyCellLayoutSetup));
				headerList.add(new CellData("", emptyCellLayoutSetup));
				headerList.add(new CellData("", emptyCellLayoutSetup));
			}
			index++;
			addRow(headerTable, documentLayoutSetup, headerList);
		}
		document.add(headerTable);
	}

	private void generateCustomScholasticMarksGrid(ExamReportCardLayoutData examReportCardLayoutData,
												   ExamReportData examReportData, String reportType, GridConfigs gridConfigs, String subjectColumnTitle,
												   float subjectColumnWidth) throws IOException {
		Set<UUID> nonAdditionalSubjects = getNonAdditionalSubjects(examReportData);
		generateScholasticMarksGrid(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
				getCambriaFont(), getCambriaBoldFont(), examReportCardLayoutData.getContentFontSize(),
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, gridConfigs, subjectColumnTitle,
				subjectColumnWidth, nonAdditionalSubjects, "FM", "MO", "#39419e");

	}

	protected float getScholasticMarksGridSubjectWidth(String reportType) {
		if (reportType.equalsIgnoreCase(HALF_YEARLY_REPORT_TYPE)) {
			return 0.25f;
		}
		return 0.15f;
	}

	protected float getCoScholasticMarksGridSubjectWidth(String reportType) {
		if (reportType.equalsIgnoreCase(HALF_YEARLY_REPORT_TYPE)) {
			return 0.5f;
		}
		return 0.3f;
	}

	protected ExamReportCardLayoutData generateReportCardLayoutData(Institute institute, DocumentOutput documentOutput)
			throws IOException {

		DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4.rotate());
		float contentFontSize = 10f;
		float defaultBorderWidth = 0.5f;
		Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
		float logoWidth = 75f;
		float logoHeight = 75f;

		int instituteId = institute.getInstituteId();
//		instituteId = 10135;
		return new ExamReportCardLayoutData(document, documentLayoutSetup, null, null, contentFontSize,
				defaultBorderWidth, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId));
	}

	protected void generateMetaDataLayout(ExamReportCardLayoutData examReportCardLayoutData, Institute institute,
			StudentLite studentLite, StudentManager studentManager, String reportType, ExamReportData examReportData,
										  int pageNumber, PdfFont regularFont, PdfFont boldFont) throws IOException {

		//Institute watermark
		int instituteId = institute.getInstituteId();
		float watermarkImageHeightWidth = 400f;
		generateWatermark(examReportCardLayoutData, instituteId, watermarkImageHeightWidth, watermarkImageHeightWidth, watermarkImageHeightWidth / 2,
				watermarkImageHeightWidth / 5 + 20);
		generateHeader(examReportCardLayoutData, studentLite, institute, reportType, 40f,
				examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.84f, regularFont, boldFont);
		generateStudentInformation(examReportCardLayoutData, studentLite, examReportData, regularFont, boldFont);
		generateBorderLayout(examReportCardLayoutData, pageNumber);
	}

	@Override
	public DocumentLayoutSetup initDocumentLayoutSetup(boolean officeCopy) {
		return initDocumentLayoutSetup(officeCopy, officeCopy ? DEFAULT_PAGE_SIZE.rotate() : DEFAULT_PAGE_SIZE,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN : DEFAULT_PAGE_TOP_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN : DEFAULT_PAGE_BOTTOM_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_SIDE_MARGIN : DEFAULT_PAGE_SIDE_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_MIDDLE_MARGIN : 0f);
	}

	protected void generateHeader(ExamReportCardLayoutData examReportCardLayoutData, StudentLite studentLite,
			Institute institute, String reportType, float offsetX, float offsetY, PdfFont regularFont, PdfFont boldFont) throws IOException {

		generateDynamicImageProvider(examReportCardLayoutData, offsetX, offsetY, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

		int singleContentColumn = 1;
		Table table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(examReportCardLayoutData.getContentFontSize() + 6).setTextAlignment(TextAlignment.CENTER);

		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);

		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getInstituteName())
						.setFontColor(Color.convertRgbToCmyk(new DeviceRgb(57, 65, 158)))),
						cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() + 5));
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getLetterHeadLine1() + ", " + institute.getLetterHeadLine2())
						.setFontColor(Color.convertRgbToCmyk(new DeviceRgb(204, 0, 0)))),
				cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() + 1));

		examReportCardLayoutData.getDocument().add(table);

		table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);
		
		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);

		examReportCardLayoutData.getDocument().add(table);

		table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);

		String headerExamTitle = "PROGRESS REPORT ";
		headerExamTitle += "(" + studentLite.getStudentSessionData().getShortYearDisplayName() + ")";
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(headerExamTitle).setUnderline()),
				cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() + 1).setPdfFont(boldFont));

		examReportCardLayoutData.getDocument().add(table);

		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);

	}

	protected void generateStudentInformation(ExamReportCardLayoutData examReportCardLayoutData,
			StudentLite studentLite, ExamReportData examReportData, PdfFont regularFont, PdfFont boldFont) throws IOException {

		Document document = examReportCardLayoutData.getDocument();
		DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
		float contentFontSize = examReportCardLayoutData.getContentFontSize() - 1;

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.24f, 0.01f, 0.24f, 0.01f, 0.24f, 0.01f, 0.24f });

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize);

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup fourthCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);

		Paragraph studentName = getKeyValueParagraph("Student's Name : ", studentLite.getName(), 57, 65, 158, boldFont, boldFont);
		Paragraph fatherName = getKeyValueParagraph("Father's Name : ", studentLite.getFathersName(), 57, 65, 158, boldFont, boldFont);
		Paragraph dob = getKeyValueParagraph("DOB : ",
				studentLite.getDateOfBirth() == null || studentLite.getDateOfBirth() <= 0 ? ""
						: DateUtils.getFormattedDate(studentLite.getDateOfBirth(), DATE_FORMAT, User.DFAULT_TIMEZONE), 57, 65, 158, boldFont, boldFont);
		Paragraph motherName = getKeyValueParagraph("Mother's Name : ", studentLite.getMothersName(), 57, 65, 158, boldFont, boldFont);
		Paragraph admissionNumber = getKeyValueParagraph("Admission No. : ", studentLite.getAdmissionNumber(), 57, 65, 158, boldFont, boldFont);
		Paragraph classValue = getKeyValueParagraph("Class : ",
				studentLite.getStudentSessionData().getStandardNameWithSection(), 57, 65, 158, boldFont, boldFont);
		Paragraph rollNumber = getKeyValueParagraph("Roll Number : ", studentLite.getStudentSessionData().getRollNumber(), 57, 65, 158, boldFont, boldFont);
		Paragraph dateOfResultDeclaration = getKeyValueParagraph("Date Of Result Declaration : ",
				examReportData.getDateOfResultDeclaration() == null ? "-" : DateUtils.getFormattedDate(examReportData.getDateOfResultDeclaration()),
				57, 65, 158, boldFont, boldFont);


		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(studentName, firstCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
						new CellData(admissionNumber, secondCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
						new CellData(classValue, thirdCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
						new CellData(rollNumber, fourthCellLayoutSetup)));

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(fatherName, firstCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
						new CellData(motherName, secondCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
						new CellData(dob, thirdCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),
						new CellData(dateOfResultDeclaration, fourthCellLayoutSetup)));

		document.add(table);

	}

	protected void generateBorderLayout(ExamReportCardLayoutData examReportCardLayoutData, int pageNumber) {
		DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
		PdfCanvas canvas = new PdfCanvas(examReportCardLayoutData.getDocument().getPdfDocument(), pageNumber);
		canvas.rectangle(SQUARE_BORDER_MARGIN - 2, SQUARE_BORDER_MARGIN - 2,
				documentLayoutSetup.getPageSize().getWidth() - (SQUARE_BORDER_MARGIN - 2) * 2,
				documentLayoutSetup.getPageSize().getHeight() - (SQUARE_BORDER_MARGIN - 2) * 2);
		canvas.stroke();
		canvas.setLineWidth(1f);
		canvas.rectangle(SQUARE_BORDER_MARGIN, SQUARE_BORDER_MARGIN,
				documentLayoutSetup.getPageSize().getWidth() - SQUARE_BORDER_MARGIN * 2,
				documentLayoutSetup.getPageSize().getHeight() - SQUARE_BORDER_MARGIN * 2);
		canvas.stroke();
		canvas.setLineWidth(1f);
	}

//	protected void generateScholasticExamDescription(Document document, DocumentLayoutSetup documentLayoutSetup,
//			float contentFontSize, ExamReportData examReportData, PdfFont regularFont, PdfFont boldFont) throws IOException {
//		Text descTitle = new Text("Description: ").setFont(boldFont).setFontSize(contentFontSize - 2);
//
//		Text descText = new Text(SCHOLASTIC_EXAM_DESCRIPTION)
//				.setFontSize(contentFontSize - 4);
//		Paragraph desc = new Paragraph();
//		desc.add(descTitle).add(descText);
//		document.add(desc);
//	}

	protected void generateResultSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize, ExamReportData examReportData, String reportType, PdfFont regularFont, PdfFont boldFont) throws IOException {

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 1)
				.setTextAlignment(TextAlignment.LEFT);

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.24f, 0.01f, 0.24f, 0.01f, 0.24f, 0.01f, 0.24f });

//		Paragraph noOfMeetings = getKeyValueParagraph("Number of Meetings : ", examReportData.getTotalWorkingDays() == null ? ""
//				: String.valueOf(examReportData.getTotalWorkingDays()), 1, 155, 248, boldFont, boldFont);
//		Paragraph noOfPresent = getKeyValueParagraph("Number of presents : ", examReportData.getTotalAttendedDays() == null ? ""
//				: String.valueOf(examReportData.getTotalAttendedDays()), 1, 155, 248, boldFont, boldFont);

		Paragraph result = getKeyValueParagraph("Result : ", "", 76, 142, 48, boldFont, boldFont);
//		if(examReportData.getExamResultStatus() != null){
//			switch (examReportData.getExamResultStatus()){
//				case PASS:
//				case PASS_WITH_GRACE:
//					result = getKeyValueParagraph("Result : ", examReportData.getExamResultStatus().getDisplayName(),
//							76, 142, 48, boldFont, boldFont);
//					break;
////				case SUPPLEMENTARY:
////					result = getKeyValueParagraph("Result : ", examReportData.getExamResultStatus().getDisplayName(),
////							255, 165, 0, boldFont, boldFont);
////					break;
////				case FAIL:
////					result = getKeyValueParagraph("Result : ", examReportData.getExamResultStatus().getDisplayName(),
////							255, 0, 0, boldFont, boldFont);
////					break;
//			}
//		}

		Paragraph promotedClass = getKeyValueParagraph("Promoted to: ", "", 57, 65, 158, boldFont, boldFont);
//		Paragraph obtainedMarks = getKeyValueParagraph("Obtained marks : ",
//				examReportData.getTotalObtainedMarks() == null ? "-"
//						: examReportData.getTotalObtainedMarks() * 10 % 10 == 0
//								? String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10)
//								: String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10d), 57, 65, 158, boldFont, boldFont);
//		Paragraph totalMarks = getKeyValueParagraph("Total marks : ", examReportData.getTotalMaxMarks() == null ? "-"
//				: String.valueOf(Math.round(examReportData.getTotalMaxMarks() * 10) / 10), 57, 65, 158, boldFont, boldFont);
//		Paragraph percentage = getKeyValueParagraph("Percentage : ", examReportData.getPercentage() == null ? "-"
//				: (Math.round(examReportData.getPercentage() * 100) / 100d) + "%", 57, 65, 158, boldFont, boldFont);
//		Paragraph grade = getKeyValueParagraph("Grade : ",
//				examReportData.getTotalGrade() == null ? "-" : examReportData.getTotalGrade().getGradeName(), 57, 65, 158, boldFont, boldFont);

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(result, cellLayoutSetup), new CellData(EMPTY_TEXT, cellLayoutSetup),
				new CellData(promotedClass, cellLayoutSetup), new CellData(EMPTY_TEXT, cellLayoutSetup),
				new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(EMPTY_TEXT, cellLayoutSetup),
				new CellData(EMPTY_TEXT, cellLayoutSetup)));
//		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(percentage, cellLayoutSetup), new CellData(EMPTY_TEXT, cellLayoutSetup),
//				new CellData(grade, cellLayoutSetup), new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(EMPTY_TEXT, cellLayoutSetup),
//				new CellData(EMPTY_TEXT, cellLayoutSetup),
//				new CellData(EMPTY_TEXT, cellLayoutSetup)));

		document.add(table);

	}

	protected void generateRemarksSection(ExamReportCardLayoutData examReportCardLayoutData,
			ExamReportData examReportData, PdfFont regularFont, PdfFont boldFont) throws IOException {

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(examReportCardLayoutData.getContentFontSize())
				.setTextAlignment(TextAlignment.LEFT);

		Table remarksTable = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), 1);
//		Paragraph remarks = getKeyValueParagraph("Remark : ",
//				examReportData.getRemarks() == null ? "" : examReportData.getRemarks());

		Paragraph remarks = getKeyValueParagraph("Remarks: ",
				examReportData.getTotalGrade() == null ? "" :
						StringUtils.isEmpty(getRemarks(examReportData.getTotalGrade().getGradeName()))
								? "" : getRemarks(examReportData.getTotalGrade().getGradeName()));

		addRow(remarksTable, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(remarks),
				cellLayoutSetup);
		examReportCardLayoutData.getDocument().add(remarksTable);
	}

	private String getRemarks(String grade) {
		switch (grade) {

			case "A+":
				return GRADE_A_PLUS;
			case "A":
				return GRADE_A;
			case "B+":
				return GRADE_B_PLUS;
			case "B":
				return GRADE_B;
			case "C+":
				return GRADE_C_PLUS;
			case "C":
				return GRADE_C;
			case "D":
				return GRADE_D;
			case "F":
				return GRADE_F;
			default:
				return null;

		}
	}

	protected void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize, float defaultBorderWidth, int pageNumber, PdfFont regularFont, PdfFont boldFont) throws IOException {
//		PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);
//		canvas.moveTo(12, 75);
//		canvas.lineTo(830, 75);
//		canvas.setLineWidth(.5f);
//		canvas.closePathStroke();
		int singleContentColumn = 3;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);
		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 1)
				.setTextAlignment(TextAlignment.CENTER);

		String signatureImageString = ImageProvider._10135_EXAM_INCHARGE_SIGNATURE;
		if(!StringUtils.isBlank(signatureImageString)) {
			generateImage(document, documentLayoutSetup,
					ImageProvider.INSTANCE.getImage(signatureImageString), 40, 40,
					(documentLayoutSetup.getPageSize().getWidth() / 2) - 10f, 25f);
		}

		generateImage(document, documentLayoutSetup,
				ImageProvider.INSTANCE.getImage(ImageProvider._10135_PRINCIPAL_SIGNATURE),
				60, 50, documentLayoutSetup.getPageSize().getWidth() - 170f, 25f);

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(""), getParagraph("Exam Incharge"),
				getParagraph("Principal")), signatureCellLayoutSetup);
		table.setFixedPosition(30f, 10f, documentLayoutSetup.getPageSize().getWidth() - 100f);
		document.add(table);
//		generateGradeBox(document, documentLayoutSetup, contentFontSize, defaultBorderWidth);
	}

	protected Set<UUID> getNonAdditionalSubjects(ExamReportData examReportData) {
		Set<UUID> nonAdditionalSubjects = new HashSet<UUID>();
		for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportData.getCourseTypeExamReportMarksGrid()
				.get(CourseType.SCHOLASTIC).getExamReportCourseMarksRows()) {
			if (!isAdditionalSubject(examReportCourseMarksRow.getCourse(), examReportData.getExamReportStructure())) {
				nonAdditionalSubjects.add(examReportCourseMarksRow.getCourse().getCourseId());
			}
		}
		return nonAdditionalSubjects;
	}

	protected void sortClassExamReports(List<ExamReportData> examReportDataList) {
		Collections.sort(examReportDataList, new Comparator<ExamReportData>() {

			@Override
			public int compare(ExamReportData e1, ExamReportData e2) {
				StandardSections section1 = e1.getStudentLite().getStudentSessionData().getStandardSection();

				StandardSections section2 = e2.getStudentLite().getStudentSessionData().getStandardSection();

				if (section1 != null && section2 != null) {
					int sectionCompare = section1.getSectionName().compareToIgnoreCase(section2.getSectionName());
					if (sectionCompare != 0) {
						return sectionCompare;
					}
				}

				return e1.getStudentLite().getName().compareToIgnoreCase(e2.getStudentLite().getName());
			}
		});
	}

	@Override
	public DocumentOutput generateClassReport(Institute institute, String reportType,
													List<ExamReportData> examReportDataList, String documentName, StudentManager studentManager) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);

			sortClassExamReports(examReportDataList);

			PdfFont regularFont = getCambriaFont();
			PdfFont boldFont = getCambriaBoldFont();

			int pageNumber = 1;
			for (ExamReportData examReportData : examReportDataList) {
				generateStudentReportCard(examReportCardLayoutData, institute, examReportData, studentManager, reportType,
						pageNumber, regularFont, boldFont);

				if (pageNumber != examReportDataList.size()) {
					examReportCardLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				pageNumber++;
			}

			examReportCardLayoutData.getDocument().close();
			return documentOutput;

		} catch (IOException e) {
			e.printStackTrace();
		}

		return null;
	}

	protected void generateCoScholasticGradingScheme(Document document, DocumentLayoutSetup documentLayoutSetup,
													 float contentFontSize, ExamReportData examReportData,
													 String reportType, PdfFont boldFont) throws IOException {
		Text descTitle = new Text("Grading As : ").setFont(boldFont).setFontSize(contentFontSize - 2);
		Text descText = new Text(COSCHOLASTIC_EXAM_GRADING)
				.setFontSize(contentFontSize - 4);
		Paragraph desc = new Paragraph();
		desc.add(descTitle).add(descText);

		Text descTitle2 = new Text(" Description: ").setFont(boldFont).setFontSize(contentFontSize - 2);

		Text descText2 = new Text(SCHOLASTIC_EXAM_DESCRIPTION)
				.setFontSize(contentFontSize - 4);
		desc.add(descTitle2).add(descText2);

		document.add(desc);
	}

	@Override
	public DocumentOutput generateClassResultReport(Institute institute, String reportType, List<ExamReportData> examReportDataList,
													String documentName, StudentManager studentManager, int studentPerPage) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4.rotate());
			Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
			PdfFont regularFont = getCambriaFont();
			PdfFont boldFont = getCambriaBoldFont();

			float logoWidth = 75f;
			float logoHeight = 75f;
			int instituteId = institute.getInstituteId();
			ExamReportCardLayoutData examReportCardLayoutData = new ExamReportCardLayoutData(document, documentLayoutSetup, null, null, DEFAULT_FONT_SIZE,
					DEFAULT_BORDER_WIDTH, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId));

			CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
			marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(DEFAULT_FONT_SIZE)
					.setBorder(new SolidBorder(DEFAULT_BORDER_WIDTH)).setTextAlignment(TextAlignment.CENTER);

			CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
					.setTextAlignment(TextAlignment.LEFT);

			String instituteName = institute.getInstituteName().toUpperCase();
			String sessionName = "";
			if(!CollectionUtils.isEmpty(examReportDataList)) {
				sessionName = "Session : " + examReportDataList.get(0).getStudentLite().getStudentSessionData().getShortYearDisplayName();
			}
			generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName,
					sessionName, marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));

			sortClassExamReports(examReportDataList);
			int pageNumber = 1;
			for (ExamReportData examReportData : examReportDataList) {
				generateClassReportDataStudentDetails(documentLayoutSetup, document, courseCellLayoutSetup, examReportData.getStudentLite());
				generateCustomScholasticMarksGrid(examReportCardLayoutData, examReportData, reportType,
						new GridConfigs(new GridHeaderConfigs(false), new GridTotalMarksRowConfigs(true, true, true)),
						"Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType));
				generateClassReportDataStudentResult(documentLayoutSetup, document, examReportData, courseCellLayoutSetup);
				if (pageNumber % studentPerPage == 0) {
					document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
					generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName, sessionName,
							marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));
				} else {
					addBlankLine(document, false, 1);
				}
				pageNumber++;
			}
			document.close();
			return documentOutput;
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
					reportType, e);
		}
		return null;
	}
}
