package com.lernen.cloud.pdf.exam.reports._10415;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.ExamGrade;
import com.lernen.cloud.core.api.examination.report.ExamReportCardLayoutData;
import com.lernen.cloud.core.api.examination.report.ExamReportCourseMarksRow;
import com.lernen.cloud.core.api.examination.report.ExamReportData;
import com.lernen.cloud.core.api.examination.report.ExamReportStructure;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.pdf.exam.reports.ExamReportGenerator;
import com.lernen.cloud.pdf.exam.reports.IExamReportCardGenerator;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;

public class ExamReportGenerator10415 extends ExamReportGenerator implements IExamReportCardGenerator  {
    public ExamReportGenerator10415(AssetProvider assetProvider) {
        super(assetProvider);
        //TODO Auto-generated constructor stub
    }

    private static final Logger logger = LogManager.getLogger(ExamReportGenerator10415.class);
    public static final float DEFAULT_PAGE_SIDE_MARGIN = 25f;
    public static final float DEFAULT_PAGE_TOP_MARGIN = 10f;
    public static final float DEFAULT_PAGE_BOTTOM_MARGIN = 10f;
    public static final float SQUARE_BORDER_MARGIN = 12f;

    protected static final String TERM_I_REPORT_TYPE = "TERM_I";
    protected static final String TERM_II_REPORT_TYPE = "TERM_II";
    protected static final String ANNUAL_REPORT_TYPE = "ANNUAL";
    protected static final String PRE_BOARD_I = "PRE_BOARD_I";
    protected static final String PRE_BOARD_II = "PRE_BOARD_II";

    protected static final String SCHOLASTIC_EXAM_DESCRIPTION = "PT = Periodic Test, NB = Notebook, SE = Subject Enrichment, HY = Half Yearly, YL = Yearly or Annual Exam";
    protected static final String SCHOLASTIC_EXAM_DESCRIPTION_NUR_TO_UKG = "PT = Periodic Test, NB = Notebook, SE = Subject Enrichment, HY = Half Yearly, YL = Yearly or Annual Exam\nE.V.S. Marks : On both terms, the Viva (Oral Test) scores are listed out of 30.";
    protected static final String SCHOLASTIC_EXAM_DESCRIPTION_I_TO_VIII = "PT = Periodic Test, NB = Notebook, SE = Subject Enrichment, HY = Half Yearly, YL = Yearly or Annual Exam\nComputer Marks: Theory out of 50 & Practical out of 50";
    protected static final String SCHOLASTIC_EXAM_DESCRIPTION_IX = "PT = Periodic Test, NB = Notebook, SE = Subject Enrichment, HY = Half Yearly, YL = Yearly or Annual Exam\nICT Marks : Theory out of 50 & Practical out of 50";
    protected static final String COSCHOLASTIC_EXAM_DESCRIPTION = "A+ : EXCELLENT, A: VERY GOOD, B: GOOD, C: AVERAGE, D : NEEDS ATTENTION";

    private static final  String GRADE_A1 = "Outstanding";
    private static final  String GRADE_A2 = "Excellent";
    private static final  String GRADE_B1 = "Brilliant";
    private static final  String GRADE_B2 = "Brilliant";
    private static final  String GRADE_C1 = "Very Good";
    private static final  String GRADE_C2 = "Good";
    private static final  String GRADE_D = "Adequate";
    private static final  String GRADE_E = "Inadequate";

    @Override
    public DocumentOutput generateReport(Institute institute, Student student, String reportType,
                                         ExamReportData examReportData, String documentName, StudentManager studentManager) {
        try {

            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

            ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);

            PdfFont regularFont = getCambriaFont();
            PdfFont boldFont = getCambriaBoldFont();

            generateStudentReportCard(examReportCardLayoutData, institute, examReportData, studentManager, reportType,
                    1, boldFont, regularFont);

            examReportCardLayoutData.getDocument().close();
            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating report card institute {}, student {}, reportType {} ",
                    institute.getInstituteId(), student.getStudentId(), reportType, e);
        }
        return null;
    }

    private void generateStudentReportCard(ExamReportCardLayoutData examReportCardLayoutData,
                                           Institute institute, ExamReportData examReportData,
                                           StudentManager studentManager, String reportType,
                                           int pageNumber, PdfFont boldFont, PdfFont regularFont) throws IOException {

        generateMetaDataLayout(examReportCardLayoutData, institute, examReportData.getStudentLite(), studentManager, reportType,
                examReportData, pageNumber, boldFont, regularFont);


        Set<UUID> nonAdditionalSubjects = getNonAdditionalSubjects(examReportData);
        generateScholasticMarksGrid(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
                getRegularFont(), getRegularBoldFont(), examReportCardLayoutData.getContentFontSize() - 2,
                examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forOnlyObtainedTotalRow(),
                "Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType),
                nonAdditionalSubjects, "MM", "MO", null,
                "-");

        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);


        if(!CollectionUtils.isEmpty(examReportData.getExamReportStructure().getExamReportCourseStructure()
                .get(CourseType.SCHOLASTIC).getAdditionalCourses())) {
            generateAdditionalCustomScholasticMarksGrid(examReportCardLayoutData, examReportData, GridConfigs.forOnlyObtainedTotalRow(),
                    "Additional Subjects", getScholasticMarksGridSubjectWidth(reportType));
        }

        if (examReportData.getCourseTypeExamReportMarksGrid().containsKey(CourseType.COSCHOLASTIC)) {
            generateCoScholasticMarksGrid(examReportCardLayoutData.getDocument(),
                    examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 2,
                    examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(),
                    "Co-Scholastic Subjects", getCoScholasticMarksGridSubjectWidth(reportType), "-");
//            addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
            generateCoScholasticExamDescription(examReportCardLayoutData.getDocument(),
                    examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
                    examReportData, boldFont, regularFont);
        }

//        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
        generateResultSummary(examReportCardLayoutData.getDocument(),
                examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
                examReportData, reportType, boldFont, regularFont);

        generateRemarksSection(examReportCardLayoutData, examReportData, boldFont, regularFont);

        generateSignatureBox(examReportCardLayoutData.getDocument(),
                examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
                examReportCardLayoutData.getDefaultBorderWidth(), pageNumber, boldFont, regularFont, reportType, examReportData);
    }

    protected void generateAdditionalCustomScholasticMarksGrid(ExamReportCardLayoutData examReportCardLayoutData,
                                                               ExamReportData examReportData, GridConfigs gridConfigs, String subjectColumnTitle, float subjectColumnWidth) throws IOException {
        ExamReportStructure examReportStructure = examReportData.getExamReportStructure();
        if (examReportStructure == null || CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure())
                || examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC) == null
                || CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC)
                .getAdditionalCourses())) {
            return;
        }

        generateScholasticMarksGrid(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
                getRegularFont(), getRegularBoldFont(), examReportCardLayoutData.getContentFontSize() - 1,
                examReportCardLayoutData.getDefaultBorderWidth(), examReportData, gridConfigs,
                subjectColumnTitle, subjectColumnWidth,
                examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC).getAdditionalCourses(),
                "MM", "MO", null, "-");
    }

    protected void generateCoScholasticExamDescription(Document document, DocumentLayoutSetup documentLayoutSetup,
                                                       float contentFontSize, ExamReportData examReportData, PdfFont boldFont, PdfFont regularFont) throws IOException {

        String description = COSCHOLASTIC_EXAM_DESCRIPTION ;

        if(org.apache.commons.lang.StringUtils.isBlank(description)) {
            return;
        }
        Text descTitle = new Text("Description: " + description).setFont(regularFont).setFontSize(contentFontSize - 3);
        Paragraph desc = new Paragraph();
        desc.add(descTitle);
        document.add(desc);
    }

    protected float getScholasticMarksGridSubjectWidth(String reportType) {
        return 0.2f;
    }

    protected float getCoScholasticMarksGridSubjectWidth(String reportType) {
        if (reportType.equalsIgnoreCase(TERM_I_REPORT_TYPE)) {
            return 0.5f;
        }
        if (reportType.equalsIgnoreCase(TERM_II_REPORT_TYPE)) {
            return 0.5f;
        }
        if (reportType.equalsIgnoreCase(PRE_BOARD_I)) {
            return 0.5f;
        }
        if (reportType.equalsIgnoreCase(PRE_BOARD_II)) {
            return 0.5f;
        }
        if (reportType.equalsIgnoreCase(ANNUAL_REPORT_TYPE)) {
            return 0.4f;
        }
        return 0.3f;
    }

    protected ExamReportCardLayoutData generateReportCardLayoutData(Institute institute, DocumentOutput documentOutput)
            throws IOException {

        DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false);
        float contentFontSize = 11f;
        float defaultBorderWidth = 0.5f;
        Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
        float logoWidth = 70f;
        float logoHeight = 70f;

        int instituteId = institute.getInstituteId();
        return new ExamReportCardLayoutData(document, documentLayoutSetup, null, null, contentFontSize,
                defaultBorderWidth, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId));
    }

    protected void generateMetaDataLayout(ExamReportCardLayoutData examReportCardLayoutData, Institute institute,
                                          StudentLite studentLite, StudentManager studentManager, String reportType, ExamReportData examReportData,
                                          int pageNumber, PdfFont boldFont, PdfFont regularFont) throws IOException {

//        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
        //Institute watermark
        int instituteId = institute.getInstituteId();
        float watermarkImageHeightWidth = 400f;
        generateWatermark(examReportCardLayoutData,
                instituteId, watermarkImageHeightWidth, watermarkImageHeightWidth, watermarkImageHeightWidth / 5 + 20,
                watermarkImageHeightWidth / 2);
        generateHeader(examReportCardLayoutData.getDocumentLayoutSetup(),
                examReportCardLayoutData, studentLite, institute, reportType, 40f,
                examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.9f, boldFont, regularFont);
        generateStudentInformation(examReportCardLayoutData, studentLite, examReportData, boldFont, regularFont);
        generateBorderLayout(examReportCardLayoutData, pageNumber);
    }

    @Override
    public DocumentLayoutSetup initDocumentLayoutSetup(boolean officeCopy) {
        return initDocumentLayoutSetup(officeCopy, officeCopy ? DEFAULT_PAGE_SIZE.rotate() : DEFAULT_PAGE_SIZE,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN : DEFAULT_PAGE_TOP_MARGIN,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN : DEFAULT_PAGE_BOTTOM_MARGIN,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_SIDE_MARGIN : DEFAULT_PAGE_SIDE_MARGIN,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_MIDDLE_MARGIN : 0f);
    }

    protected void generateHeader(DocumentLayoutSetup documentLayoutSetup, ExamReportCardLayoutData examReportCardLayoutData, StudentLite studentLite,
                                  Institute institute, String reportType, float offsetX, float offsetY,
                                  PdfFont boldFont, PdfFont regularFont) throws IOException {

        generateDynamicImageProvider(examReportCardLayoutData, offsetX, offsetY, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

        int singleContentColumn = 1;
        Table table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(boldFont).setFontSize(examReportCardLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.CENTER);

        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getInstituteName().toUpperCase())),
                cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() + 3));
        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getLetterHeadLine1()).setMultipliedLeading(0.88f)),
                cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() - 1));
        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getLetterHeadLine2()).setMultipliedLeading(0.88f)),
                cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() - 1).setPdfFont(regularFont));

        examReportCardLayoutData.getDocument().add(table);
//        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
        table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);
        String headerExamTitle = reportType.equalsIgnoreCase(TERM_I_REPORT_TYPE) ? "TERM-1 EXAMINATION "
                : reportType.equalsIgnoreCase(PRE_BOARD_I) ? "PRE BOARD I "
                : reportType.equalsIgnoreCase(PRE_BOARD_II) ? "PRE BOARD II "
                : "PROGRESS REPORT ";
        headerExamTitle += "(" + studentLite.getStudentSessionData().getShortYearDisplayName() + ")";
        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(headerExamTitle).setUnderline()),
                cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize()).setPdfFont(boldFont));
        examReportCardLayoutData.getDocument().add(table);
//        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
    }

    protected void generateStudentInformation(ExamReportCardLayoutData examReportCardLayoutData,
                                              StudentLite studentLite, ExamReportData examReportData,
                                              PdfFont boldFont, PdfFont regularFont) throws IOException {

        Document document = examReportCardLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
        float contentFontSize = examReportCardLayoutData.getContentFontSize() - 1;

        Table table = getPDFTable(documentLayoutSetup, new float[] { 0.4f, 0.2f, 0.4f });

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize);

        CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
        CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);
        CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);

        Paragraph studentName = getKeyValueParagraph("Student Name : ", studentLite.getName(), boldFont, boldFont).setMultipliedLeading(0.88f);
        Paragraph fatherName = getKeyValueParagraph("Father Name : ", studentLite.getFathersName(), boldFont, boldFont).setMultipliedLeading(0.88f);
        Paragraph dob = getKeyValueParagraph("DOB : ",
                studentLite.getDateOfBirth() == null || studentLite.getDateOfBirth() <= 0 ? ""
                        : DateUtils.getFormattedDate(studentLite.getDateOfBirth(), DATE_FORMAT, User.DFAULT_TIMEZONE),  boldFont, boldFont).setMultipliedLeading(0.88f);
        Paragraph motherName = getKeyValueParagraph("Mother Name : ", studentLite.getMothersName(), boldFont, boldFont).setMultipliedLeading(0.88f);
        Paragraph admissionNumber = getKeyValueParagraph("Admission No. : ", studentLite.getAdmissionNumber(), boldFont, boldFont).setMultipliedLeading(0.88f);
        Paragraph classValue = getKeyValueParagraph("Class : ",
                studentLite.getStudentSessionData().getStandardNameWithSection(), boldFont, boldFont).setMultipliedLeading(0.88f);
        Paragraph rollNumber = getKeyValueParagraph("Roll Number : ", studentLite.getStudentSessionData().getRollNumber(), boldFont, boldFont).setMultipliedLeading(0.88f);
//        Paragraph dateOfResultDeclaration = getKeyValueParagraph("Date Of Result Declaration : ",
//                examReportData.getDateOfResultDeclaration() == null ? "-" : DateUtils.getFormattedDate(examReportData.getDateOfResultDeclaration()),
//                boldFont, boldFont);


        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(studentName, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
                        new CellData(fatherName, thirdCellLayoutSetup)));
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(dob, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
                        new CellData(motherName, thirdCellLayoutSetup)));
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(admissionNumber, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
                        new CellData(classValue, thirdCellLayoutSetup)));
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(rollNumber, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
                        new CellData(EMPTY_TEXT, thirdCellLayoutSetup)));

        document.add(table);

//        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
    }

    protected void generateBorderLayout(ExamReportCardLayoutData examReportCardLayoutData, int pageNumber) {
        DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
        PdfCanvas canvas = new PdfCanvas(examReportCardLayoutData.getDocument().getPdfDocument(), pageNumber);
        canvas.rectangle(SQUARE_BORDER_MARGIN - 2, SQUARE_BORDER_MARGIN - 2,
                documentLayoutSetup.getPageSize().getWidth() - (SQUARE_BORDER_MARGIN - 2) * 2,
                documentLayoutSetup.getPageSize().getHeight() - (SQUARE_BORDER_MARGIN - 2) * 2);
        canvas.stroke();
        canvas.setLineWidth(1f);
        canvas.rectangle(SQUARE_BORDER_MARGIN, SQUARE_BORDER_MARGIN,
                documentLayoutSetup.getPageSize().getWidth() - SQUARE_BORDER_MARGIN * 2,
                documentLayoutSetup.getPageSize().getHeight() - SQUARE_BORDER_MARGIN * 2);
        canvas.stroke();
        canvas.setLineWidth(1f);
    }

    protected void generateScholasticExamDescription(Document document, DocumentLayoutSetup documentLayoutSetup,
                                                     float contentFontSize, ExamReportData examReportData, PdfFont boldFont, PdfFont regularFont, String descriptionText) throws IOException {
        Text descTitle = new Text("Description: " + descriptionText).setFontSize(contentFontSize - 4);
        Paragraph desc = new Paragraph();
        desc.add(descTitle);
        document.add(desc);
    }

    protected void generateResultSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
                                         float contentFontSize, ExamReportData examReportData, String reportType, PdfFont boldFont, PdfFont regularFont) throws IOException {

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.LEFT).setPaddingTop(2f).setPaddingBottom(2f);

        Table table = getPDFTable(documentLayoutSetup, new float[] { 0.4f, 0.2f, 0.4f });

        Paragraph result = getKeyValueParagraph("Result : ", "-", boldFont);
        if(examReportData.getExamResultStatus() != null){
            switch (examReportData.getExamResultStatus()){
                case PASS:
                    result = getKeyValueParagraph("Result : ", examReportData.getExamResultStatus().getDisplayName(),
                            0, 0, 0, boldFont, boldFont);
                    break;
                case CONDITIONALLY_PROMOTED:
                    result = getKeyValueParagraph("Result : ", examReportData.getExamResultStatus().getDisplayName(),
                            0, 0, 0, boldFont, boldFont);
                    break;
                case DETAINED:
                    result = getKeyValueParagraph("Result : ", examReportData.getExamResultStatus().getDisplayName(),
                            0, 0, 0, boldFont, boldFont);
                    break;
            }
        }

        Paragraph promotedClass = getKeyValueParagraph("Promoted to: ",
                examReportData.getPromotedTo() == null ? "-" : examReportData.getPromotedTo().getStandardName(), boldFont, boldFont);
        Paragraph obtainedMarks = getKeyValueParagraph("Obtained marks : ",
                examReportData.getTotalObtainedMarks() == null ? "-"
                        : examReportData.getTotalObtainedMarks() * 10 % 10 == 0
                        ? String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10)
                        : String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10d), boldFont, boldFont);
        Paragraph totalMarks = getKeyValueParagraph("Total marks : ", examReportData.getTotalMaxMarks() == null ? "-"
                : String.valueOf(Math.round(examReportData.getTotalMaxMarks() * 10) / 10), boldFont, boldFont);
        Paragraph percentage = getKeyValueParagraph("Percentage : ", examReportData.getPercentage() == null ? "-"
                : Math.round(examReportData.getPercentage() * 100) / 100d + "%", boldFont, boldFont);
        Paragraph grade = getKeyValueParagraph("Grade : ",
                examReportData.getTotalGrade() == null ? "-" : examReportData.getTotalGrade().getGradeName(), boldFont, boldFont);

        Paragraph totalWorkingDays = getKeyValueParagraph("Total Working Days : ",
                examReportData.getTotalWorkingDays() == null ? ""
                        : String.valueOf(examReportData.getTotalWorkingDays()), boldFont, boldFont);

        Paragraph totalAttendedDays = getKeyValueParagraph("Total Attended Days : ",
                examReportData.getTotalAttendedDays() == null ? ""
                        : String.valueOf(examReportData.getTotalAttendedDays()), boldFont, boldFont).setMultipliedLeading(0.77f);

        if (reportType.equalsIgnoreCase(ANNUAL_REPORT_TYPE) || reportType.equalsIgnoreCase(TERM_II_REPORT_TYPE)) {
            addRow(table, documentLayoutSetup, Arrays.asList(new CellData(result.setMultipliedLeading(0.88f), cellLayoutSetup),
                    new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(promotedClass.setMultipliedLeading(0.88f), cellLayoutSetup)));
        }

        addRow(table, documentLayoutSetup, Arrays.asList(new CellData(obtainedMarks.setMultipliedLeading(0.88f), cellLayoutSetup),
                new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(totalMarks.setMultipliedLeading(0.88f), cellLayoutSetup)));
        addRow(table, documentLayoutSetup, Arrays.asList(new CellData(percentage.setMultipliedLeading(0.88f), cellLayoutSetup),
                new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(grade.setMultipliedLeading(0.88f), cellLayoutSetup)));
        addRow(table, documentLayoutSetup, Arrays.asList(new CellData(totalWorkingDays.setMultipliedLeading(0.88f), cellLayoutSetup),
                new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(totalAttendedDays.setMultipliedLeading(0.88f), cellLayoutSetup)));

        document.add(table);

    }

    protected void generateRemarksSection(ExamReportCardLayoutData examReportCardLayoutData,
                                          ExamReportData examReportData, PdfFont boldFont, PdfFont regularFont) throws IOException {

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(boldFont).setFontSize(examReportCardLayoutData.getContentFontSize() - 1)
                .setTextAlignment(TextAlignment.LEFT);

        Table remarksTable = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), 1);

        Paragraph remarks = getKeyValueParagraph("Remarks: ",
                StringUtils.isBlank(examReportData.getRemarks()) ? "" :
                        examReportData.getRemarks(), boldFont, boldFont);

        addRow(remarksTable, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(remarks.setMultipliedLeading(0.88f)),
                cellLayoutSetup);
        examReportCardLayoutData.getDocument().add(remarksTable);
    }

    private String getRemarks(String grade) {
        switch (grade) {

            case "A1":
                return GRADE_A1;
            case "A2":
                return GRADE_A2;
            case "B1":
                return GRADE_B1;
            case "B2":
                return GRADE_B2;
            case "C1":
                return GRADE_C1;
            case "C2":
                return GRADE_C2;
            case "D":
                return GRADE_D;
            case "E":
                return GRADE_E;
            default:
                return null;

        }
    }

    protected void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup,
                                        float contentFontSize, float defaultBorderWidth, int pageNumber, PdfFont boldFont, PdfFont regularFont,
                                        String reportType, ExamReportData examReportData) throws IOException {
        PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);
        canvas.moveTo(12, 77);
        canvas.lineTo(583, 77);
        canvas.setLineWidth(.5f);
        canvas.closePathStroke();
        int singleContentColumn = 3;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);
        CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
        signatureCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 1)
                .setTextAlignment(TextAlignment.CENTER);

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Class Teacher"), getParagraph(""),
                getParagraph("Principal")), signatureCellLayoutSetup);
        table.setFixedPosition(30f, 77f, documentLayoutSetup.getPageSize().getWidth() - 100f);
        document.add(table);


        if (reportType.equalsIgnoreCase(TERM_I_REPORT_TYPE)) {
            generateImage(document, documentLayoutSetup, ImageProvider.INSTANCE.getImage(ImageProvider._10130_PRINCIPAL_SIGNATURE),
                    50f, 50f, documentLayoutSetup.getPageSize().getWidth() - 130f, 170f);
        }

        generateGradeBox(document, documentLayoutSetup, contentFontSize, defaultBorderWidth, boldFont, regularFont, examReportData);
    }

    private void generateGradeBox(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
                                  float defaultBorderWidth, PdfFont boldFont, PdfFont regularFont, ExamReportData examReportData) throws IOException {

        Table table = getPDFTable(documentLayoutSetup, 1);
        CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
        signatureCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 1)
                .setTextAlignment(TextAlignment.CENTER);
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("MARKS GRADE")), signatureCellLayoutSetup);

        table.setFixedPosition(10f, 57f, documentLayoutSetup.getPageSize().getWidth());
        document.add(table);
        float cosScholasticBottom = 109f;
        float cosScholasticTableBottom = 72f;
        CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
        marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(contentFontSize - 2)
                .setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);

        if (examReportData.getCourseTypeExamGrades() != null
                && examReportData.getCourseTypeExamGrades().containsKey(CourseType.SCHOLASTIC)
                && examReportData.getCourseTypeExamGrades().get(CourseType.SCHOLASTIC) != null) {

            Table headerTable = getPDFTable(documentLayoutSetup, 1);
            List<CellData> headerList = new ArrayList<>();
//			headerList.add(new CellData("MARKS GRADE.", marksCellLayoutSetup));
//			addRow(headerTable, documentLayoutSetup, headerList);

//			headerTable.setFixedPosition(25f, 9f, documentLayoutSetup.getPageSize().getWidth() - 100f);
            cosScholasticBottom = 53f;
            document.add(headerTable);
            int scholasticColumnCount = examReportData.getCourseTypeExamGrades().get(CourseType.SCHOLASTIC).size() + 1; // +1 for MARKS RANGE
            float[] scholasticColumnWidths = new float[scholasticColumnCount];
            for (int i = 0; i < scholasticColumnCount; i++) {
                scholasticColumnWidths[i] = 1f / scholasticColumnCount;
            }

            headerTable = getPDFTable(documentLayoutSetup, scholasticColumnWidths);
            headerList = new ArrayList<>();

            headerList.add(new CellData("RANGE", marksCellLayoutSetup));
            for (ExamGrade examGrade : examReportData.getCourseTypeExamGrades().get(CourseType.SCHOLASTIC)) {
                headerList.add(new CellData(examGrade.getRangeDisplayName(), marksCellLayoutSetup));
            }
            addRow(headerTable, documentLayoutSetup, headerList);

            headerList = new ArrayList<>();
            headerList.add(new CellData("GRADE", marksCellLayoutSetup));
            for (ExamGrade examGrade : examReportData.getCourseTypeExamGrades().get(CourseType.SCHOLASTIC)) {
                headerList.add(new CellData(examGrade.getGradeName(), marksCellLayoutSetup));
            }
            addRow(headerTable, documentLayoutSetup, headerList);

            headerTable.setFixedPosition(25f, 18f, documentLayoutSetup.getPageSize().getWidth() - 100f);
            cosScholasticTableBottom = 15f;
            document.add(headerTable);
        }

//		if (examReportData.getCourseTypeExamGrades() != null
//				&& examReportData.getCourseTypeExamGrades().containsKey(CourseType.COSCHOLASTIC)
//				&& examReportData.getCourseTypeExamGrades().get(CourseType.COSCHOLASTIC) != null) {
//
//			Table headerTable = getPDFTable(documentLayoutSetup, 1);
//			List<CellData> headerList = new ArrayList<>();
//			headerList.add(new CellData("Co-Scholastic Grading", marksCellLayoutSetup));
//			addRow(headerTable, documentLayoutSetup, headerList);
//
//			headerTable.setFixedPosition(25f, cosScholasticBottom, documentLayoutSetup.getPageSize().getWidth() - 100f);
//			document.add(headerTable);
//			int coscholasticColumnCount = examReportData.getCourseTypeExamGrades().get(CourseType.COSCHOLASTIC).size() + 1; // +1 for MARKS RANGE
//			float[] coscholasticColumnWidths = new float[coscholasticColumnCount];
//			for (int i = 0; i < coscholasticColumnCount; i++) {
//				coscholasticColumnWidths[i] = 1f / coscholasticColumnCount;
//			}
//
//			headerTable = getPDFTable(documentLayoutSetup, coscholasticColumnWidths);
//			headerList = new ArrayList<>();
//
//			headerList.add(new CellData("RANGE", marksCellLayoutSetup));
//			for (ExamGrade examGrade : examReportData.getCourseTypeExamGrades().get(CourseType.COSCHOLASTIC)) {
//				headerList.add(new CellData(examGrade.getRangeDisplayName(), marksCellLayoutSetup));
//			}
//			addRow(headerTable, documentLayoutSetup, headerList);
//
//			headerList = new ArrayList<>();
//			headerList.add(new CellData("GRADE", marksCellLayoutSetup));
//			for (ExamGrade examGrade : examReportData.getCourseTypeExamGrades().get(CourseType.COSCHOLASTIC)) {
//				headerList.add(new CellData(examGrade.getGradeName(), marksCellLayoutSetup));
//			}
//			addRow(headerTable, documentLayoutSetup, headerList);
//
//			headerTable.setFixedPosition(25f, cosScholasticTableBottom, documentLayoutSetup.getPageSize().getWidth() - 100f);
//			document.add(headerTable);
//		}
    }


    protected Set<UUID> getNonAdditionalSubjects(ExamReportData examReportData) {
        Set<UUID> nonAdditionalSubjects = new HashSet<UUID>();
        for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportData.getCourseTypeExamReportMarksGrid()
                .get(CourseType.SCHOLASTIC).getExamReportCourseMarksRows()) {
            if (!isAdditionalSubject(examReportCourseMarksRow.getCourse(), examReportData.getExamReportStructure())) {
                nonAdditionalSubjects.add(examReportCourseMarksRow.getCourse().getCourseId());
            }
        }
        return nonAdditionalSubjects;
    }

    private void generateBackgroundImage(Document document, DocumentLayoutSetup documentLayoutSetup, byte[] image, float width,
                                         float height, float offsetX, float offsetY) {
        try {
            generateImage(document, documentLayoutSetup, image, width, height, offsetX, offsetY);
        } catch (Exception e) {
            logger.error("Exception while adding background image", e);
        }

    }

    protected void sortClassExamReports(List<ExamReportData> examReportDataList) {
        Collections.sort(examReportDataList, new Comparator<ExamReportData>() {

            @Override
            public int compare(ExamReportData e1, ExamReportData e2) {
                StandardSections section1 = e1.getStudentLite().getStudentSessionData().getStandardSection();

                StandardSections section2 = e2.getStudentLite().getStudentSessionData().getStandardSection();

                if (section1 != null && section2 != null) {
                    int sectionCompare = section1.getSectionName().compareToIgnoreCase(section2.getSectionName());
                    if (sectionCompare != 0) {
                        return sectionCompare;
                    }
                }

                return e1.getStudentLite().getName().compareToIgnoreCase(e2.getStudentLite().getName());
            }
        });
    }

    @Override
    public DocumentOutput generateClassReport(Institute institute, String reportType,
                                              List<ExamReportData> examReportDataList, String documentName, StudentManager studentManager) {

        try {
            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);

            sortClassExamReports(examReportDataList);

            int pageNumber = 1;
            PdfFont regularFont = getCambriaFont();
            PdfFont boldFont = getCambriaBoldFont();
            for (ExamReportData examReportData : examReportDataList) {
                generateStudentReportCard(examReportCardLayoutData, institute, examReportData, studentManager, reportType,
                        pageNumber, boldFont, regularFont);

                if (pageNumber != examReportDataList.size()) {
                    examReportCardLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                }
                pageNumber++;
            }

            examReportCardLayoutData.getDocument().close();
            return documentOutput;

        } catch (IOException e) {
            e.printStackTrace();
        }

        return null;
    }

    @Override
    public DocumentOutput generateClassResultReport(Institute institute, String reportType, List<ExamReportData> examReportDataList,
                                                    String documentName, StudentManager studentManager, int studentPerPage) {
        try {
            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4);
            Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

            PdfFont regularFont = getRegularFont();
            PdfFont boldFont = getRegularBoldFont();

            CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
            marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(DEFAULT_FONT_SIZE)
                    .setBorder(new SolidBorder(DEFAULT_BORDER_WIDTH)).setTextAlignment(TextAlignment.CENTER);

            CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
                    .setTextAlignment(TextAlignment.LEFT);

            String instituteName = institute.getInstituteName().toUpperCase();
            String sessionName = "";
            if(!CollectionUtils.isEmpty(examReportDataList)) {
                sessionName = "Session : " + examReportDataList.get(0).getStudentLite().getStudentSessionData().getShortYearDisplayName();
            }
            generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName,
                    sessionName, marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));

            sortClassExamReports(examReportDataList);
            int pageNumber = 1;
            for (ExamReportData examReportData : examReportDataList) {
                generateClassReportDataStudentDetails(documentLayoutSetup, document, courseCellLayoutSetup, examReportData.getStudentLite());
                generateScholasticMarksGrid(document,
                        documentLayoutSetup, DEFAULT_FONT_SIZE, DEFAULT_BORDER_WIDTH, examReportData, GridConfigs.forOnlyObtainedTotalRow(),
                        "Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType),
                        null, null);
                generateClassReportDataStudentResult(documentLayoutSetup, document, examReportData, courseCellLayoutSetup);
                if (pageNumber % studentPerPage == 0) {
                    document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                    generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName, sessionName,
                            marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));
                } else {
                    addBlankLine(document, false, 1);
                }
                pageNumber++;
            }
            document.close();
            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
                    reportType, e);
        }
        return null;
    }
}
