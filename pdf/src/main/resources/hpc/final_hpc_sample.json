{"name": "Term 1 & Term 2", "applicableExams": ["TERM2", "TERM1"], "sections": [{"id": "s1", "heading": {"id": null, "textType": "CONSTANT", "text": "", "webDisplayText": "About the student", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": null}, "subHeading": null, "associatedUserType": "TEACHER", "container": {"id": "s1.c1", "childAlignment": "VERTICAL", "overrideHeight": null, "associatedExamType": null, "childContainers": [{"id": "s1.c1.c1", "childAlignment": "VERTICAL", "overrideHeight": null, "associatedExamType": null, "childContainers": null, "imageContainer": null, "textElements": [{"id": "s1.c1.c1.heading", "textType": "CONSTANT", "text": "ALL ABOUT ME", "webDisplayText": null, "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 30.0, "bold": true, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": null, "padding": {"allSidePadding": null, "leftPadding": null, "rightPadding": null, "topPadding": 0.0, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"id": "s1.c1.c1.t1", "textType": "NAME", "text": "My name is : %s", "webDisplayText": "My name is", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 20.0, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": null, "padding": {"allSidePadding": null, "leftPadding": 20.0, "rightPadding": 20.0, "topPadding": 10.0, "bottomPadding": null}, "rotationAngleInDegree": 0.0}, "valuePdfAttributes": {"fontSize": 15.0, "bold": false, "underline": true, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": null, "padding": null, "rotationAngleInDegree": 0.0}}, {"id": "s1.c1.c1.t2", "textType": "TEXT", "text": "Things I like : %s", "webDisplayText": "Things I like", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 20.0, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": null, "padding": {"allSidePadding": null, "leftPadding": 20.0, "rightPadding": 20.0, "topPadding": 10.0, "bottomPadding": null}, "rotationAngleInDegree": 0.0}, "valuePdfAttributes": {"fontSize": 15.0, "bold": false, "underline": true, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": null, "padding": null, "rotationAngleInDegree": 0.0}}, {"id": "s1.c1.c1.t3", "textType": "TEXT", "text": "I live in : %s", "webDisplayText": "I live in", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 20.0, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": null, "padding": {"allSidePadding": null, "leftPadding": 20.0, "rightPadding": 20.0, "topPadding": 10.0, "bottomPadding": null}, "rotationAngleInDegree": 0.0}, "valuePdfAttributes": {"fontSize": 15.0, "bold": false, "underline": true, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": null, "padding": null, "rotationAngleInDegree": 0.0}}, {"id": "s1.c1.c1.t4", "textType": "DOB", "text": "My birthday : %s", "webDisplayText": "My birthday", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 20.0, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": null, "padding": {"allSidePadding": null, "leftPadding": 20.0, "rightPadding": 20.0, "topPadding": 10.0, "bottomPadding": null}, "rotationAngleInDegree": 0.0}, "valuePdfAttributes": {"fontSize": 15.0, "bold": false, "underline": true, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": null, "padding": null, "rotationAngleInDegree": 0.0}}, {"id": "s1.c1.c1.t5", "textType": "TEXT", "text": "My friends are : %s", "webDisplayText": "My friends are", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 20.0, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": null, "padding": {"allSidePadding": null, "leftPadding": 20.0, "rightPadding": 20.0, "topPadding": 10.0, "bottomPadding": null}, "rotationAngleInDegree": 0.0}, "valuePdfAttributes": {"fontSize": 15.0, "bold": false, "underline": true, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": null, "padding": null, "rotationAngleInDegree": 0.0}}, {"id": "s1.c1.c1.t6", "textType": "CONSTANT", "text": "My Favorite:", "webDisplayText": "My Favorite:", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 20.0, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": null, "padding": {"allSidePadding": null, "leftPadding": 20.0, "rightPadding": 20.0, "topPadding": 10.0, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"id": "s1.c1.c1.t7", "textType": "TEXT", "text": "Colors : %s", "webDisplayText": "Colors", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 20.0, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": null, "padding": {"allSidePadding": null, "leftPadding": 20.0, "rightPadding": 20.0, "topPadding": 10.0, "bottomPadding": null}, "rotationAngleInDegree": 0.0}, "valuePdfAttributes": {"fontSize": 15.0, "bold": false, "underline": true, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": null, "padding": null, "rotationAngleInDegree": 0.0}}, {"id": "s1.c1.c1.t8", "textType": "TEXT", "text": "Foods : %s", "webDisplayText": "Foods", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 20.0, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": null, "padding": {"allSidePadding": null, "leftPadding": 20.0, "rightPadding": 20.0, "topPadding": 10.0, "bottomPadding": null}, "rotationAngleInDegree": 0.0}, "valuePdfAttributes": {"fontSize": 15.0, "bold": false, "underline": true, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": null, "padding": null, "rotationAngleInDegree": 0.0}}, {"id": "s1.c1.c1.t9", "textType": "TEXT", "text": "Games : %s", "webDisplayText": "Games", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 20.0, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": null, "padding": {"allSidePadding": null, "leftPadding": 20.0, "rightPadding": 20.0, "topPadding": 10.0, "bottomPadding": null}, "rotationAngleInDegree": 0.0}, "valuePdfAttributes": {"fontSize": 15.0, "bold": false, "underline": true, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": null, "padding": null, "rotationAngleInDegree": 0.0}}, {"id": "s1.c1.c1.t10", "textType": "TEXT", "text": "Animals : %s", "webDisplayText": "Animals", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 20.0, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": null, "padding": {"allSidePadding": null, "leftPadding": 20.0, "rightPadding": 20.0, "topPadding": 10.0, "bottomPadding": 20.0}, "rotationAngleInDegree": 0.0}, "valuePdfAttributes": {"fontSize": 15.0, "bold": false, "underline": true, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": null, "padding": null, "rotationAngleInDegree": 0.0}}], "table": null, "pdfAttributes": null}, {"id": "s1.c1.c2", "childAlignment": "HORIZONTAL", "overrideHeight": null, "associatedExamType": null, "childContainers": [{"id": "s1.c1.c2.c1", "childAlignment": "VERTICAL", "overrideHeight": null, "associatedExamType": "TERM1", "childContainers": null, "imageContainer": null, "textElements": [{"id": "s1.c1.c2.c1.t1", "textType": "CONSTANT", "text": "TERM 1", "webDisplayText": null, "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 15.0, "bold": true, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": null, "padding": {"allSidePadding": null, "leftPadding": 20.0, "rightPadding": 20.0, "topPadding": 10.0, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"id": "s1.c1.c2.c1.t2", "textType": "TEXT", "text": "My height is : %s hand spans", "webDisplayText": "My height in hand spans", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 15.0, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": null, "padding": {"allSidePadding": null, "leftPadding": 20.0, "rightPadding": 20.0, "topPadding": 10.0, "bottomPadding": null}, "rotationAngleInDegree": 0.0}, "valuePdfAttributes": {"fontSize": 15.0, "bold": false, "underline": true, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": null, "padding": null, "rotationAngleInDegree": 0.0}}, {"id": "s1.c1.c2.c1.t3", "textType": "TEXT", "text": "My weight is : %s kgs", "webDisplayText": "My weight in kgs", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 15.0, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": null, "padding": {"allSidePadding": null, "leftPadding": 20.0, "rightPadding": 20.0, "topPadding": 10.0, "bottomPadding": 30.0}, "rotationAngleInDegree": 0.0}, "valuePdfAttributes": {"fontSize": 15.0, "bold": false, "underline": true, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": null, "padding": null, "rotationAngleInDegree": 0.0}}], "table": null, "pdfAttributes": {"fontSize": null, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": null, "leftBorderWidth": null, "rightBorderWidth": 2.0, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": null, "rotationAngleInDegree": 0.0}}, {"id": "s1.c1.c2.c2", "childAlignment": "VERTICAL", "overrideHeight": null, "associatedExamType": "TERM2", "childContainers": null, "imageContainer": null, "textElements": [{"id": "s1.c1.c2.c2.t1", "textType": "CONSTANT", "text": "TERM 2", "webDisplayText": null, "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 15.0, "bold": true, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": null, "padding": {"allSidePadding": null, "leftPadding": 20.0, "rightPadding": 20.0, "topPadding": 10.0, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"id": "s1.c1.c2.c2.t2", "textType": "TEXT", "text": "My height is : %s hand spans", "webDisplayText": "My height in hand spans", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 15.0, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": null, "padding": {"allSidePadding": null, "leftPadding": 20.0, "rightPadding": 20.0, "topPadding": 10.0, "bottomPadding": null}, "rotationAngleInDegree": 0.0}, "valuePdfAttributes": {"fontSize": 15.0, "bold": false, "underline": true, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": null, "padding": null, "rotationAngleInDegree": 0.0}}, {"id": "s1.c1.c2.c2.t3", "textType": "TEXT", "text": "My weight is : %s kgs", "webDisplayText": "My weight in kgs", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 15.0, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": null, "padding": {"allSidePadding": null, "leftPadding": 20.0, "rightPadding": 20.0, "topPadding": 10.0, "bottomPadding": 30.0}, "rotationAngleInDegree": 0.0}, "valuePdfAttributes": {"fontSize": 15.0, "bold": false, "underline": true, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": null, "padding": null, "rotationAngleInDegree": 0.0}}], "table": null, "pdfAttributes": null}], "imageContainer": null, "textElements": null, "table": null, "pdfAttributes": {"fontSize": null, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": null, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": 2.0, "bottomBorderWidth": null}, "padding": null, "rotationAngleInDegree": 0.0}}], "imageContainer": null, "textElements": null, "table": null, "pdfAttributes": {"fontSize": null, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.75, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": null, "rotationAngleInDegree": 0.0}}, "pageBreak": true, "blankSpaceLineCount": 0}, {"id": "s2", "heading": {"id": null, "textType": "CONSTANT", "text": "A glimpse of myself", "webDisplayText": "A glimpse of myself", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 30.0, "bold": true, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": null, "padding": {"allSidePadding": null, "leftPadding": null, "rightPadding": null, "topPadding": 0.0, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "subHeading": {"id": null, "textType": "CONSTANT", "text": "", "webDisplayText": null, "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": null}, "associatedUserType": "TEACHER", "container": {"id": "s2.c1", "childAlignment": null, "overrideHeight": null, "associatedExamType": null, "childContainers": null, "imageContainer": {"id": "s2.c1.i1", "imageType": "MYSELF", "imageHeight": 270, "imageWidth": 500}, "textElements": null, "table": null, "pdfAttributes": {"fontSize": null, "bold": false, "overrideHeight": 275.0, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.75, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": null, "rotationAngleInDegree": 0.0}}, "pageBreak": false, "blankSpaceLineCount": 1}, {"id": "s3", "heading": {"id": null, "textType": "CONSTANT", "text": "A glimpse of my family", "webDisplayText": "A glimpse of my family", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 30.0, "bold": true, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": null, "padding": {"allSidePadding": null, "leftPadding": null, "rightPadding": null, "topPadding": 0.0, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "subHeading": {"id": null, "textType": "CONSTANT", "text": "", "webDisplayText": null, "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": null}, "associatedUserType": "TEACHER", "container": {"id": "s3.c1", "childAlignment": null, "overrideHeight": null, "associatedExamType": null, "childContainers": null, "imageContainer": {"id": "s3.c1.i1", "imageType": "MY_FAMILY", "imageHeight": 270, "imageWidth": 500}, "textElements": null, "table": null, "pdfAttributes": {"fontSize": null, "bold": false, "overrideHeight": 275.0, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.75, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": null, "rotationAngleInDegree": 0.0}}, "pageBreak": true, "blankSpaceLineCount": 0}, {"id": "s4", "heading": {"id": null, "textType": "CONSTANT", "text": "", "webDisplayText": "Competencies", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": null}, "subHeading": {"id": null, "textType": "CONSTANT", "text": "", "webDisplayText": null, "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": null}, "associatedUserType": "TEACHER", "container": {"id": "s4.c1", "childAlignment": null, "overrideHeight": null, "associatedExamType": null, "childContainers": null, "imageContainer": null, "textElements": null, "table": {"headerRow": [{"headerType": "CONSTANT", "text": "", "examType": null, "widthRatio": 0.1, "pdfAttributes": {"fontSize": null, "bold": true, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"headerType": "CONSTANT", "text": "Competencies", "examType": null, "widthRatio": 0.54, "pdfAttributes": {"fontSize": null, "bold": true, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"headerType": "EXAM", "text": "Term 1", "examType": "TERM1", "widthRatio": 0.18, "pdfAttributes": {"fontSize": null, "bold": true, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"headerType": "EXAM", "text": "Term 2", "examType": "TERM2", "widthRatio": 0.18, "pdfAttributes": {"fontSize": null, "bold": true, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "childAlignment": "VERTICAL", "cells": [{"textElement": {"id": "s4.c1.t1.ce1.t1", "textType": "CONSTANT", "text": "Physical Development", "webDisplayText": "Physical Development", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": null, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": null, "leftPadding": 6.0, "rightPadding": 6.0, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 90.0}}, "childAlignment": "HORIZONTAL", "childCells": [{"textElement": {"id": "s4.c1.t1.ce1.ce1.t1", "textType": "CONSTANT", "text": "Curriculum Goal 1 - Children develop habits that keep them healthy & safe", "webDisplayText": "Curriculum Goal 1 - Children develop habits that keep them healthy & safe", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 11.5, "bold": true, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": [{"textElement": {"id": "s4.c1.t1.ce1.ce1.ce1.t1", "textType": "TEXT", "text": "C-1.1: Shows a liking for and understanding of nutritious food and does not waste food", "webDisplayText": "C-1.1: Shows a liking for and understanding of nutritious food and does not waste food", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce1.ce1.ce2.t1", "textType": "TEXT", "text": "C-1.2: Practices basic self-care and hygiene", "webDisplayText": "C-1.2: Practices basic self-care and hygiene", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce1.ce1.ce3.t1", "textType": "TEXT", "text": "C-1.6: Understands unsafe situations and asks for help", "webDisplayText": "C-1.6: Understands unsafe situations and asks for help", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}], "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce1.ce2.t1", "textType": "CONSTANT", "text": "Curriculum Goal 2 - Children develop sharpness in sensorial perceptions", "webDisplayText": "Curriculum Goal 2 - Children develop sharpness in sensorial perceptions", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 11.5, "bold": true, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": [{"textElement": {"id": "s4.c1.t1.ce1.ce2.ce1.t1", "textType": "TEXT", "text": "C-2.1: Differentiates between shapes, colours, and their shades", "webDisplayText": "C-2.1: Differentiates between shapes, colours, and their shades", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce1.ce2.ce2.t1", "textType": "TEXT", "text": "C-2.5: <PERSON><PERSON><PERSON> discrimination in the sense of touch", "webDisplayText": "C-2.5: <PERSON><PERSON><PERSON> discrimination in the sense of touch", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce1.ce2.ce3.t1", "textType": "TEXT", "text": "C-2.6: <PERSON><PERSON> integrating sensorial perceptions to get a holistic awareness of experiences", "webDisplayText": "C-2.6: <PERSON><PERSON> integrating sensorial perceptions to get a holistic awareness of experiences", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}], "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce1.ce3.t1", "textType": "CONSTANT", "text": "Curriculum Goal 3 - Children develop a fit and flexible body", "webDisplayText": "Curriculum Goal 3 - Children develop a fit and flexible body", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 11.5, "bold": true, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": [{"textElement": {"id": "s4.c1.t1.ce1.ce3.ce1.t1", "textType": "TEXT", "text": "C-3.2: Shows balance, coordination and flexibility in various physical activities", "webDisplayText": "C-3.2: Shows balance, coordination and flexibility in various physical activities", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce1.ce3.ce2.t1", "textType": "TEXT", "text": "C-3.3: Shows precision and control in working with their hands and fingers", "webDisplayText": "C-3.3: Shows precision and control in working with their hands and fingers", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce1.ce3.ce3.t1", "textType": "TEXT", "text": "C-3.4: Shows strength and endurance in carrying, walking and running", "webDisplayText": "C-3.4: Shows strength and endurance in carrying, walking and running", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}], "pdfAttributes": null}], "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce2.t1", "textType": "CONSTANT", "text": "Socio-Emotional & Ethical Development", "webDisplayText": "Socio-Emotional & Ethical Development", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": null, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": null, "leftPadding": 1.0, "rightPadding": 1.0, "topPadding": 3.0, "bottomPadding": 5.0}, "rotationAngleInDegree": 90.0}}, "childAlignment": "HORIZONTAL", "childCells": [{"textElement": {"id": "s4.c1.t1.ce2.ce1.t1", "textType": "CONSTANT", "text": "Curriculum Goal 4 - Children develop emotional intelligence", "webDisplayText": "Curriculum Goal 4 - Children develop emotional intelligence", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 11.5, "bold": true, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": [{"textElement": {"id": "s4.c1.t1.ce2.ce1.ce1.t1", "textType": "TEXT", "text": "C-4.1: Starts recognising 'self' as an individual belong to a family and community", "webDisplayText": "C-4.1: Starts recognising 'self' as an individual belong to a family and community", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce2.ce1.ce2.t1", "textType": "TEXT", "text": "C-4.2: Recognises different emotions and makes deli- berate effort to regulate them appropriately", "webDisplayText": "C-4.2: Recognises different emotions and makes deli- berate effort to regulate them appropriately", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce2.ce1.ce3.t1", "textType": "TEXT", "text": "C-4.3: Interacts comfortably with other children and adults", "webDisplayText": "C-4.3: Interacts comfortably with other children and adults", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce2.ce1.ce4.t1", "textType": "TEXT", "text": "C-4.6: Shows kindness and helpfulness to others (including animals, plants) when they are in need", "webDisplayText": "C-4.6: Shows kindness and helpfulness to others (including animals, plants) when they are in need", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}], "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce2.ce2.t1", "textType": "CONSTANT", "text": "Curriculum Goal 5 - Children develop a positive attitude towards productive work and service or 'Seva'", "webDisplayText": "Curriculum Goal 5 - Children develop a positive attitude towards productive work and service or 'Seva'", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 11.5, "bold": true, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": [{"textElement": {"id": "s4.c1.t1.ce2.ce2.ce1.t1", "textType": "TEXT", "text": "C-5.1: Demonstrates willingness and participation in age-appropriate physical work towards helping others", "webDisplayText": "C-5.1: Demonstrates willingness and participation in age-appropriate physical work towards helping others", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}], "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce2.ce3.t1", "textType": "CONSTANT", "text": "Curriculum Goal 6 - Children develop a positive regard for the natural environment around them", "webDisplayText": "Curriculum Goal 6 - Children develop a positive regard for the natural environment around them", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 11.5, "bold": true, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": [{"textElement": {"id": "s4.c1.t1.ce2.ce3.ce1.t1", "textType": "TEXT", "text": "C-6.1: Shows care for and joy in engaging with all life forms", "webDisplayText": "C-6.1: Shows care for and joy in engaging with all life forms", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}], "pdfAttributes": null}], "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce3.t1", "textType": "CONSTANT", "text": "Cognitive Development", "webDisplayText": "Cognitive Development", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": null, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": null, "leftPadding": 1.0, "rightPadding": 1.0, "topPadding": 3.0, "bottomPadding": 5.0}, "rotationAngleInDegree": 90.0}}, "childAlignment": "HORIZONTAL", "childCells": [{"textElement": {"id": "s4.c1.t1.ce3.ce1.t1", "textType": "CONSTANT", "text": "Curriculum Goal 7 - Children make sense of world around through observation and logical thinking", "webDisplayText": "Curriculum Goal 7 - Children make sense of world around through observation and logical thinking", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 11.5, "bold": true, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": [{"textElement": {"id": "s4.c1.t1.ce3.ce1.ce1.t1", "textType": "TEXT", "text": "C-7.1: Observes and understands different categories of objects and relationships between them", "webDisplayText": "C-7.1: Observes and understands different categories of objects and relationships between them", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce3.ce1.ce2.t1", "textType": "TEXT", "text": "C-7.2:Observes and understands cause and effect relationships in nature by forming simple hypothesis and uses observations to explain their hypothesis", "webDisplayText": "C-7.2:Observes and understands cause and effect relationships in nature by forming simple hypothesis and uses observations to explain their hypothesis", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}], "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce3.ce2.t1", "textType": "CONSTANT", "text": "Curriculum Goal 8 - Children develop mathematical understanding and abilities to recognise the world through quantities, shapes, and measures", "webDisplayText": "Curriculum Goal 8 - Children develop mathematical understanding and abilities to recognise the world through quantities, shapes, and measures", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 11.5, "bold": true, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": [{"textElement": {"id": "s4.c1.t1.ce3.ce2.ce1.t1", "textType": "TEXT", "text": "C-8.1: Sorts objects into groups and sub-group based on more than one property", "webDisplayText": "C-8.1: Sorts objects into groups and sub-group based on more than one property", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce3.ce2.ce2.t1", "textType": "TEXT", "text": "C-8.2: Identifies and extends simple patterns in their surroundings, shapes, and numbers", "webDisplayText": "C-8.2: Identifies and extends simple patterns in their surroundings, shapes, and numbers", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce3.ce2.ce3.t1", "textType": "TEXT", "text": "C-8.3: Counts up to 99 both forwards and backwards and in groups of 10s and 20s", "webDisplayText": "C-8.3: Counts up to 99 both forwards and backwards and in groups of 10s and 20s", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce3.ce2.ce4.t1", "textType": "TEXT", "text": "C-8.5: Recognises and uses numerals to represent quantities up to 99 with the understanding of decimal place value system", "webDisplayText": "C-8.5: Recognises and uses numerals to represent quantities up to 99 with the understanding of decimal place value system", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce3.ce2.ce5.t1", "textType": "TEXT", "text": "C-8.6: Performs addition and subtraction of 2-digit numbers fluently using flexible strategies of composition and decomposition", "webDisplayText": "C-8.6: Performs addition and subtraction of 2-digit numbers fluently using flexible strategies of composition and decomposition", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce3.ce2.ce6.t1", "textType": "TEXT", "text": "C-8.8: Recognises, makes, and classifies basic geometric shapes and their observable properties, and understands and explains the relative relation of objects in space", "webDisplayText": "C-8.8: Recognises, makes, and classifies basic geometric shapes and their observable properties, and understands and explains the relative relation of objects in space", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce3.ce2.ce7.t1", "textType": "TEXT", "text": "C-8.13: Formulates and solves simple mathematical problems related to quantities, shapes, space, and measurements", "webDisplayText": "C-8.13: Formulates and solves simple mathematical problems related to quantities, shapes, space, and measurements", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}], "pdfAttributes": null}], "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce4.t1", "textType": "CONSTANT", "text": "Language and Literacy Development", "webDisplayText": "Language and Literacy Development", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": null, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": null, "leftPadding": 1.0, "rightPadding": 1.0, "topPadding": 3.0, "bottomPadding": 5.0}, "rotationAngleInDegree": 90.0}}, "childAlignment": "HORIZONTAL", "childCells": [{"textElement": {"id": "s4.c1.t1.ce4.ce1.t1", "textType": "CONSTANT", "text": "Curriculum Goal 9 - Children develop effective communication skills for day-today interactions in two languages", "webDisplayText": "Curriculum Goal 9 - Children develop effective communication skills for day-today interactions in two languages", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 11.5, "bold": true, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": [{"textElement": {"id": "s4.c1.t1.ce4.ce1.ce1.t1", "textType": "TEXT", "text": "C-9.1: Listens to and appreciates simple songs, rhymes, and poems", "webDisplayText": "C-9.1: Listens to and appreciates simple songs, rhymes, and poems", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce4.ce1.ce2.t1", "textType": "TEXT", "text": "C-9.3: Converses fluently and can hold a meaningful conversation", "webDisplayText": "C-9.3: Converses fluently and can hold a meaningful conversation", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce4.ce1.ce3.t1", "textType": "TEXT", "text": "C-9.4: Understands oral instructions for a complex task and gives clear oral instructions for the same to others", "webDisplayText": "C-9.4: Understands oral instructions for a complex task and gives clear oral instructions for the same to others", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce4.ce1.ce4.t1", "textType": "TEXT", "text": "C-9.5: Comprehends narrated/read-out stories and identifies characters, storyline and what the author wants to say", "webDisplayText": "C-9.5: Comprehends narrated/read-out stories and identifies characters, storyline and what the author wants to say", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}], "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce4.ce2.t1", "textType": "CONSTANT", "text": "Curriculum Goal 10 Children develop fluency in reading and writing in Language 1", "webDisplayText": "Curriculum Goal 10 Children develop fluency in reading and writing in Language 1", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 11.5, "bold": true, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": [{"textElement": {"id": "s4.c1.t1.ce4.ce2.ce1.t1", "textType": "TEXT", "text": "C-10.3: Recognises all the letters of the alphabet (forms of akshara) of the script (L1) and uses this knowledge to read and write words", "webDisplayText": "C-10.3: Recognises all the letters of the alphabet (forms of akshara) of the script (L1) and uses this knowledge to read and write words", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce4.ce2.ce2.t1", "textType": "TEXT", "text": "C-10.4: Reads stories and passages (in L1) with accuracy and fluency with appropriate pauses and voice modulation", "webDisplayText": "C-10.4: Reads stories and passages (in L1) with accuracy and fluency with appropriate pauses and voice modulation", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce4.ce2.ce3.t1", "textType": "TEXT", "text": "C-10.5: Reads short stories and comprehends its meaning by identifying characters, storyline and what the author wanted to say on their own (L1)", "webDisplayText": "C-10.5: Reads short stories and comprehends its meaning by identifying characters, storyline and what the author wanted to say on their own (L1)", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce4.ce2.ce4.t1", "textType": "TEXT", "text": "C-10.6: Reads short poems and begins to appreciate the poem for its choice of words and imagination", "webDisplayText": "C-10.6: Reads short poems and begins to appreciate the poem for its choice of words and imagination", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce4.ce2.ce5.t1", "textType": "TEXT", "text": "C-10.7: Reads and comprehends meaning of short news items, instructions and recipes, and publicity material", "webDisplayText": "C-10.7: Reads and comprehends meaning of short news items, instructions and recipes, and publicity material", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}], "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce4.ce3.t1", "textType": "CONSTANT", "text": "Curriculum Goal 11 - Children begin to read and write in Language 2", "webDisplayText": "Curriculum Goal 11 - Children begin to read and write in Language 2", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 11.5, "bold": true, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": [{"textElement": {"id": "s4.c1.t1.ce4.ce3.ce1.t1", "textType": "TEXT", "text": "C-11.2: Recognises most frequently occurring letters of the alphabet (forms of akshara) of the script, and uses this knowledge to read and write simple words and sentences", "webDisplayText": "C-11.2: Recognises most frequently occurring letters of the alphabet (forms of akshara) of the script, and uses this knowledge to read and write simple words and sentences", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}], "pdfAttributes": null}], "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce5.t1", "textType": "CONSTANT", "text": "Aesthetic & Cultural Development", "webDisplayText": "Aesthetic & Cultural Development", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": null, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": null, "leftPadding": 1.0, "rightPadding": 1.0, "topPadding": 3.0, "bottomPadding": 5.0}, "rotationAngleInDegree": 90.0}}, "childAlignment": "HORIZONTAL", "childCells": [{"textElement": {"id": "s4.c1.t1.ce5.ce1.t1", "textType": "CONSTANT", "text": "Curriculum Goal 12 - Children develop abilities and sensibilities in visual and performing arts, and express their emotions through art in meaningful and joyful ways", "webDisplayText": "Curriculum Goal 12 - Children develop abilities and sensibilities in visual and performing arts, and express their emotions through art in meaningful and joyful ways", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 11.5, "bold": true, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": [{"textElement": {"id": "s4.c1.t1.ce5.ce1.ce1.t1", "textType": "TEXT", "text": "C-12.1: Explores and plays with a variety of materials and tools to create two-dimensional and three-dimensional artworks in varying sizes", "webDisplayText": "C-12.1: Explores and plays with a variety of materials and tools to create two-dimensional and three-dimensional artworks in varying sizes", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce5.ce1.ce2.t1", "textType": "TEXT", "text": "C-12.2: Explores and plays with own voice, body, spaces, and a variety of objects to create music, role-play, dance, and movement", "webDisplayText": "C-12.2: Explores and plays with own voice, body, spaces, and a variety of objects to create music, role-play, dance, and movement", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce5.ce1.ce3.t1", "textType": "TEXT", "text": "C-12.3: Innovates and works imaginatively to express ideas and emotions through the arts", "webDisplayText": "C-12.3: Innovates and works imaginatively to express ideas and emotions through the arts", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}], "pdfAttributes": null}], "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce6.t1", "textType": "CONSTANT", "text": "Positive Learning Habits", "webDisplayText": "Positive Learning Habits", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": null, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": null, "leftPadding": 1.0, "rightPadding": 1.0, "topPadding": 3.0, "bottomPadding": 5.0}, "rotationAngleInDegree": 90.0}}, "childAlignment": "HORIZONTAL", "childCells": [{"textElement": {"id": "s4.c1.t1.ce6.ce1.t1", "textType": "CONSTANT", "text": "Curriculum Goal 13- Children develop habits of learning that allow them to engage actively in formal learning environments like a school classroom", "webDisplayText": "Curriculum Goal 13- Children develop habits of learning that allow them to engage actively in formal learning environments like a school classroom", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 11.5, "bold": true, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": [{"textElement": {"id": "s4.c1.t1.ce6.ce1.ce1.t1", "textType": "TEXT", "text": "C-13.1: Attention and intentional action: Acquires skills to plan, focus attention, and direct activities to achieve specific goals", "webDisplayText": "C-13.1: Attention and intentional action: Acquires skills to plan, focus attention, and direct activities to achieve specific goals", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s4.c1.t1.ce6.ce1.ce2.t1", "textType": "TEXT", "text": "C-13.3: Observation, wonder, curiosity, and exploration: Observes minute details of objects, wonders and explores using various senses, tinkers with objects, asks questions", "webDisplayText": "C-13.3: Observation, wonder, curiosity, and exploration: Observes minute details of objects, wonders and explores using various senses, tinkers with objects, asks questions", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["BEGINNER", "PROGRESSING", "PROFICIENT"], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 10.5, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}], "pdfAttributes": null}], "pdfAttributes": null}], "pdfAttributes": null}, "pdfAttributes": null}, "pageBreak": true, "blankSpaceLineCount": 0}, {"id": "s5", "heading": {"id": null, "textType": "CONSTANT", "text": "<PERSON><PERSON>'s profile by the teacher", "webDisplayText": "<PERSON><PERSON>'s profile by the teacher", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 30.0, "bold": true, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": null, "padding": {"allSidePadding": null, "leftPadding": null, "rightPadding": null, "topPadding": 0.0, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "subHeading": {"id": null, "textType": "CONSTANT", "text": "Teacher must present a narrative summary of child, highlighting the strengths, challenges and suggestions for improvement.", "webDisplayText": "Teacher must present a narrative summary of child, highlighting the strengths, challenges and suggestions for improvement.", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 10.0, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "LEFT", "verticalTextAlignment": null, "border": null, "padding": {"allSidePadding": null, "leftPadding": null, "rightPadding": null, "topPadding": 2.0, "bottomPadding": 5.0}, "rotationAngleInDegree": 0.0}}, "associatedUserType": "TEACHER", "container": {"id": "s5.c1", "childAlignment": null, "overrideHeight": null, "associatedExamType": null, "childContainers": null, "imageContainer": null, "textElements": [{"id": "s5.c1.t1", "textType": "TEXT", "text": "%s", "webDisplayText": "Teacher's input", "category": "GENERAL", "generalInputValue": null, "suggestedDropDownValues": null, "examValues": null, "pdfAttributes": {"fontSize": 12.0, "bold": false, "overrideHeight": 320.0, "horizontalTextAlignment": "JUSTIFIED", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.75, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 10, "leftPadding": null, "rightPadding": null, "topPadding": 0.0, "bottomPadding": null}, "rotationAngleInDegree": 0.0}, "valuePdfAttributes": {"fontSize": 12.0, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "JUSTIFIED", "verticalTextAlignment": null, "border": {"allSideBorderWidth": null, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 10, "leftPadding": null, "rightPadding": null, "topPadding": 0.0, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "table": null, "pdfAttributes": null}, "pageBreak": false, "blankSpaceLineCount": 0}, {"id": "s6", "heading": {"id": null, "textType": "CONSTANT", "text": "<PERSON><PERSON>'s <PERSON><PERSON><PERSON>", "webDisplayText": "<PERSON><PERSON>'s <PERSON><PERSON><PERSON>", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 30.0, "bold": true, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": null, "padding": {"allSidePadding": null, "leftPadding": null, "rightPadding": null, "topPadding": 0.0, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "subHeading": {"id": null, "textType": "CONSTANT", "text": "", "webDisplayText": null, "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": null}, "associatedUserType": "PARENT", "container": {"id": "s6.c1", "childAlignment": null, "overrideHeight": null, "associatedExamType": null, "childContainers": null, "imageContainer": null, "textElements": null, "table": {"headerRow": [{"headerType": "CONSTANT", "text": "Aspect", "examType": null, "widthRatio": 0.46, "pdfAttributes": {"fontSize": 14, "bold": true, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"headerType": "EXAM", "text": "Term 1", "examType": "TERM1", "widthRatio": 0.27, "pdfAttributes": {"fontSize": 14, "bold": true, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"headerType": "EXAM", "text": "Term 2", "examType": "TERM2", "widthRatio": 0.27, "pdfAttributes": {"fontSize": 14, "bold": true, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "childAlignment": "HORIZONTAL", "cells": [{"textElement": {"id": "s6.c1.t1.ce1.t1", "textType": "TEXT", "text": "My child enjoys participating in", "webDisplayText": "My child enjoys participating in", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s6.c1.t1.ce2.t1", "textType": "TEXT", "text": "My child can be supported for", "webDisplayText": "My child can be supported for", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s6.c1.t1.ce3.t1", "textType": "TEXT", "text": "I would also like to share", "webDisplayText": "I would also like to share", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s6.c1.t1.ce4.t1", "textType": "TEXT", "text": "Have I completed age appropriate vaccination schedule for my child?", "webDisplayText": "Have I completed age appropriate vaccination schedule for my child?", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}], "pdfAttributes": null}, "pdfAttributes": null}, "pageBreak": true, "blankSpaceLineCount": 0}, {"id": "s7", "heading": {"id": null, "textType": "CONSTANT", "text": "Self Assessment", "webDisplayText": "Self Assessment", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 30.0, "bold": true, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": null, "padding": {"allSidePadding": null, "leftPadding": null, "rightPadding": null, "topPadding": 0.0, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "subHeading": {"id": null, "textType": "CONSTANT", "text": "Self reflection on inter-disciplinary activity done by the child. Example: Clay work, drawing, playing a game, colouring, puppet-making, model making, etc.", "webDisplayText": null, "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 10.0, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "LEFT", "verticalTextAlignment": null, "border": null, "padding": {"allSidePadding": null, "leftPadding": null, "rightPadding": null, "topPadding": 2.0, "bottomPadding": 5.0}, "rotationAngleInDegree": 0.0}}, "associatedUserType": "TEACHER", "container": {"id": "s7.c1", "childAlignment": null, "overrideHeight": null, "associatedExamType": null, "childContainers": null, "imageContainer": null, "textElements": null, "table": {"headerRow": [{"headerType": "CONSTANT", "text": "", "examType": null, "widthRatio": 0.34, "pdfAttributes": {"fontSize": 14, "bold": true, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"headerType": "EXAM", "text": "Term 1", "examType": "TERM1", "widthRatio": 0.32, "pdfAttributes": {"fontSize": 14, "bold": true, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"headerType": "EXAM", "text": "Term 2", "examType": "TERM2", "widthRatio": 0.32, "pdfAttributes": {"fontSize": 14, "bold": true, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "childAlignment": "HORIZONTAL", "cells": [{"textElement": {"id": "s7.c1.t1.ce1.t1", "textType": "TEXT", "text": "Activities that i enjoy the most", "webDisplayText": "Activities that i enjoy the most", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s7.c1.t1.ce2.t1", "textType": "TEXT", "text": "Activities that i find difficult to do", "webDisplayText": "Activities that i find difficult to do", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s7.c1.t1.ce3.t1", "textType": "TEXT", "text": "Activities that i enjoy doing with my friends", "webDisplayText": "Activities that i enjoy doing with my friends", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}], "pdfAttributes": null}, "pdfAttributes": null}, "pageBreak": false, "blankSpaceLineCount": 6}, {"id": "s8", "heading": {"id": null, "textType": "CONSTANT", "text": "Peer Assessment", "webDisplayText": "Peer Assessment", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 30.0, "bold": true, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": null, "padding": {"allSidePadding": null, "leftPadding": null, "rightPadding": null, "topPadding": 0.0, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "subHeading": {"id": null, "textType": "CONSTANT", "text": "", "webDisplayText": null, "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 10.0, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "LEFT", "verticalTextAlignment": null, "border": null, "padding": {"allSidePadding": null, "leftPadding": null, "rightPadding": null, "topPadding": 2.0, "bottomPadding": 5.0}, "rotationAngleInDegree": 0.0}}, "associatedUserType": "TEACHER", "container": {"id": "s8.c1", "childAlignment": null, "overrideHeight": null, "associatedExamType": null, "childContainers": null, "imageContainer": null, "textElements": null, "table": {"headerRow": [{"headerType": "CONSTANT", "text": "", "examType": null, "widthRatio": 0.4, "pdfAttributes": {"fontSize": 14, "bold": true, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"headerType": "EXAM", "text": "Term 1", "examType": "TERM1", "widthRatio": 0.3, "pdfAttributes": {"fontSize": 14, "bold": true, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"headerType": "EXAM", "text": "Term 2", "examType": "TERM2", "widthRatio": 0.3, "pdfAttributes": {"fontSize": 14, "bold": true, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "childAlignment": "HORIZONTAL", "cells": [{"textElement": {"id": "s8.c1.t1.ce1.t1", "textType": "TEXT", "text": "Helps in completing tasks/activity", "webDisplayText": "Helps in completing tasks/activity", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["YES", "NO"], "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["YES", "NO"], "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s8.c1.t1.ce2.t1", "textType": "TEXT", "text": "Likes to play with others", "webDisplayText": "Likes to play with others", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["YES", "NO"], "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["YES", "NO"], "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s8.c1.t1.ce3.t1", "textType": "TEXT", "text": "Shares stationary (crayons/glue/chalk) with classmates", "webDisplayText": "Shares stationary (crayons/glue/chalk) with classmates", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "suggestedDropDownValues": ["YES", "NO"], "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "suggestedDropDownValues": ["YES", "NO"], "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}], "pdfAttributes": null}, "pdfAttributes": null}, "pageBreak": true, "blankSpaceLineCount": 0}, {"id": "s9", "heading": {"id": null, "textType": "CONSTANT", "text": "Learner’s Portfolio", "webDisplayText": "Learner’s Portfolio", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 30.0, "bold": true, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": null, "padding": {"allSidePadding": null, "leftPadding": null, "rightPadding": null, "topPadding": 0.0, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "subHeading": {"id": null, "textType": "CONSTANT", "text": "Paste pictures/ display selected work done by student in various experiential and inter-disciplinary tasks done in class.", "webDisplayText": null, "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 10.0, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "LEFT", "verticalTextAlignment": null, "border": null, "padding": {"allSidePadding": null, "leftPadding": null, "rightPadding": null, "topPadding": 2.0, "bottomPadding": 5.0}, "rotationAngleInDegree": 0.0}}, "associatedUserType": "TEACHER", "container": {"id": "s9.c1", "childAlignment": null, "overrideHeight": null, "associatedExamType": null, "childContainers": null, "imageContainer": {"id": "s9.c1.i1", "imageType": "LEARNERS_PORTFOLIO", "imageHeight": 395, "imageWidth": 500}, "textElements": null, "table": null, "pdfAttributes": {"fontSize": null, "bold": false, "overrideHeight": 400.0, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.75, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": null, "rotationAngleInDegree": 0.0}}, "pageBreak": false, "blankSpaceLineCount": 1}, {"id": "s10", "heading": {"id": null, "textType": "CONSTANT", "text": "Signature With Date", "webDisplayText": "Signature With Date", "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 30.0, "bold": true, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": null, "padding": {"allSidePadding": null, "leftPadding": null, "rightPadding": null, "topPadding": 0.0, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "subHeading": {"id": null, "textType": "CONSTANT", "text": "", "webDisplayText": null, "category": "GENERAL", "generalInputValue": null, "examValues": null, "pdfAttributes": {"fontSize": 10.0, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "LEFT", "verticalTextAlignment": null, "border": null, "padding": {"allSidePadding": null, "leftPadding": null, "rightPadding": null, "topPadding": 2.0, "bottomPadding": 5.0}, "rotationAngleInDegree": 0.0}}, "associatedUserType": "TEACHER", "container": {"id": "s8.c1", "childAlignment": null, "overrideHeight": null, "associatedExamType": null, "childContainers": null, "imageContainer": null, "textElements": null, "table": {"headerRow": [{"headerType": "CONSTANT", "text": "Term", "examType": null, "widthRatio": 0.22, "pdfAttributes": {"fontSize": 14, "bold": true, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"headerType": "EXAM", "text": "Term 1", "examType": "TERM1", "widthRatio": 0.39, "pdfAttributes": {"fontSize": 14, "bold": true, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"headerType": "EXAM", "text": "Term 2", "examType": "TERM2", "widthRatio": 0.39, "pdfAttributes": {"fontSize": 14, "bold": true, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "childAlignment": "HORIZONTAL", "cells": [{"textElement": {"id": "s10.c1.t1.ce1.t1", "textType": "TEXT", "text": "Parent/Guardian", "webDisplayText": "Parent/Guardian", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s10.c1.t1.ce2.t1", "textType": "TEXT", "text": "Class Teacher", "webDisplayText": "Class Teacher", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}, {"textElement": {"id": "s10.c1.t1.ce3.t1", "textType": "TEXT", "text": "Principal", "webDisplayText": "Principal", "category": "EXAM", "generalInputValue": null, "examValues": [{"hpcExamType": "TERM1", "value": null, "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, {"hpcExamType": "TERM2", "value": null, "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": "CENTER", "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}], "pdfAttributes": {"fontSize": 12, "bold": false, "overrideHeight": null, "horizontalTextAlignment": null, "verticalTextAlignment": null, "border": {"allSideBorderWidth": 1.0, "leftBorderWidth": null, "rightBorderWidth": null, "topBorderWidth": null, "bottomBorderWidth": null}, "padding": {"allSidePadding": 5.0, "leftPadding": null, "rightPadding": null, "topPadding": null, "bottomPadding": null}, "rotationAngleInDegree": 0.0}}, "childAlignment": "HORIZONTAL", "childCells": null, "pdfAttributes": null}], "pdfAttributes": null}, "pdfAttributes": null}, "pageBreak": false, "blankSpaceLineCount": 0}]}