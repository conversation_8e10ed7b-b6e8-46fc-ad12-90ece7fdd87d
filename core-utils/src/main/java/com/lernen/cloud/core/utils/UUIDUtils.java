package com.lernen.cloud.core.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.UUID;

public class UUIDUtils {

    public static UUID getUUID(String uuidString) {
        if (StringUtils.isBlank(uuidString)) {
            return null;
        }
        return UUID.fromString(uuidString);
    }

    public static boolean equals(UUID uuid1, UUID uuid2) {
        if (uuid1 == null && uuid2 == null) {
            return true;
        }
        if (uuid1 != null && uuid2 != null) {
            return uuid1.equals(uuid2);
        }
        return false;
    }

    public static UUID generateUUID() {
        return UUID.randomUUID();
    }
}