package com.lernen.cloud.core.utils.aws.lambda;

import com.amazonaws.services.lambda.AWSLambda;
import com.amazonaws.services.lambda.model.InvocationType;
import com.amazonaws.services.lambda.model.InvokeRequest;
import com.amazonaws.services.lambda.model.InvokeResult;
import com.google.gson.Gson;
import com.lernen.cloud.core.utils.SharedConstants;

import java.nio.charset.Charset;
import java.util.Map;

/**
 * <AUTHOR>
 */

public class AWSLambdaInvoker {

    private final AWSLambda awsLambda;
    private static final Gson GSON = SharedConstants.GSON;

    public AWSLambdaInvoker(AWSLambdaClientProvider awsLambdaClientProvider) {
        this.awsLambda = awsLambdaClientProvider.getLambdaClient();
    }

    public String invoke(String functionName, Map<String, String> payload) {
        InvokeRequest lmbRequest = new InvokeRequest()
                .withFunctionName(functionName)
                .withPayload(GSON.toJson(payload));

        lmbRequest.setInvocationType(InvocationType.RequestResponse);

        InvokeResult lmbResult = awsLambda.invoke(lmbRequest);

        return new String(lmbResult.getPayload().array(), Charset.forName("UTF-8"));
    }
}
