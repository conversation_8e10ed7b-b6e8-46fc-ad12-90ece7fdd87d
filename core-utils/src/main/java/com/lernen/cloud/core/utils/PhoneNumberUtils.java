package com.lernen.cloud.core.utils;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.google.i18n.phonenumbers.NumberParseException;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.PhoneNumberUtil.PhoneNumberFormat;
import com.google.i18n.phonenumbers.Phonenumber.PhoneNumber;

/*
 * Utility class for parsing, formatting, and validating international phone numbers
 * 
 * http://codel10n.com/mobile-number-validation-how-to-do-it-right/
 */
public class PhoneNumberUtils {
	private static final Logger logger = LogManager.getLogger(PhoneNumberUtils.class);

	private static final String PHONE_NUMBER_IS_NOT_VALID_FOR_COUNTRY = "phoneNumber is not valid for country {}";
	public static final String DEFAULT_COUNTRY = "IN";

	private static final PhoneNumberUtil phoneNumberUtil = PhoneNumberUtil.getInstance();

	public static boolean isValidNumber(String phoneNumber, String country) {
		if (StringUtils.isBlank(phoneNumber)) {
			return false;
		}
		country = StringUtils.isBlank(country) ? DEFAULT_COUNTRY : country;
		try {
			PhoneNumber number = phoneNumberUtil.parse(phoneNumber, country);
			return phoneNumberUtil.isValidNumber(number);
		} catch (NumberParseException e) {
			logger.error(PHONE_NUMBER_IS_NOT_VALID_FOR_COUNTRY, country);
			return false;
		}
	}

	public static String getE164FormattedNumber(String phoneNumber, String country) {
		if (phoneNumber == null) {
			return null;
		}
		country = StringUtils.isBlank(country) ? DEFAULT_COUNTRY : country;
		try {
			if (!isValidNumber(phoneNumber, country)) {
				return null;
			}
			PhoneNumber number = phoneNumberUtil.parse(phoneNumber, country);
			return phoneNumberUtil.format(number, PhoneNumberFormat.E164);
		} catch (NumberParseException e) {
			logger.error(PHONE_NUMBER_IS_NOT_VALID_FOR_COUNTRY, country);
			return null;
		} catch (Exception e) {
			logger.error("Error while get formatted number {} , country {}", phoneNumber, country);
			return null;
		}
	}

	/**
	 * extract national number from E.164 formatted number e.g. for +12025550100 it
	 * returns 2025550100
	 * 
	 * @param phoneNumber
	 * @param country
	 * @return
	 * @throws NumberParseException
	 */
	public static String getNationalNumber(String phoneNumber, String country) {
		if (StringUtils.isBlank(phoneNumber)) {
			return null;
		}
		country = StringUtils.isBlank(country) ? DEFAULT_COUNTRY : country;
		try {
			PhoneNumber number = phoneNumberUtil.parse(phoneNumber, country);
			return String.valueOf(number.getNationalNumber());
		} catch (NumberParseException e) {
			logger.error(PHONE_NUMBER_IS_NOT_VALID_FOR_COUNTRY, country);
			return null;
		}
	}

	public static String trimPlusSign(String mobileNumber) {
		if (StringUtils.isBlank(mobileNumber)) {
			return mobileNumber;
		}
		if (mobileNumber.contains("+")) {
			return mobileNumber.substring(1);
		}
		return mobileNumber;
	}

	public static String formatTo10Digit(String mobileNumber) {
		if (StringUtils.isBlank(mobileNumber)) {
			return mobileNumber;
		}

		if (mobileNumber.length() > 10) {
			return mobileNumber.substring(mobileNumber.length() - 10, mobileNumber.length());
		}
		return mobileNumber;
	}
}
