package com.lernen.cloud.core.utils.cache;

import com.embrate.cloud.core.api.cache.ICacheKey;

/**
 * <AUTHOR>
 */

public class CacheFactory {

    public <K extends ICacheKey, V> ICache<K, V> getInMemoryCache(ICacheLoader<K, V> cacheLoader) {
        return new InMemoryCache<>(cacheLoader);
    }

    public <K extends ICacheKey, V> ICache<K, V> getInMemoryLRUCache(int capacity, ICacheLoader<K, V> cacheLoader) {
        return new InMemoryLRUCache<>(capacity, cacheLoader);
    }

}
