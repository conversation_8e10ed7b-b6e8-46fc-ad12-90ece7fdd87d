package com.lernen.cloud.core.utils.audio;

import javax.sound.sampled.AudioFormat;
import javax.sound.sampled.AudioInputStream;
import javax.sound.sampled.AudioSystem;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;

/**
 * <AUTHOR>
 */
public class AudioUtils {

    /**
     * Currently only wav is supported
     *
     * @return
     */
    public static Float getAudioFileDuration(ByteArrayOutputStream bos){
        return getAudioFileDuration(bos.toByteArray());
    }

    public static Float getAudioFileDuration(byte [] data){
        try{
            AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(new ByteArrayInputStream(data));
            AudioFormat format = audioInputStream.getFormat();
            long audioFileLength = data.length;
            int frameSize = format.getFrameSize();
            float frameRate = format.getFrameRate();
            float durationInSeconds = (audioFileLength / (frameSize * frameRate));
            return durationInSeconds;
        }catch (Exception e){

        }
        return null;

    }
}
