package com.lernen.cloud.core.utils.communication.service;

import com.embrate.cloud.core.api.audio.AudioFileProperties;
import com.embrate.cloud.core.api.service.communication.templates.CommunicationTemplate;
import com.lernen.cloud.core.api.common.CommunicationServiceProvider;
import com.lernen.cloud.core.api.common.CounterType;
import com.lernen.cloud.core.api.notification.NotificationDetails;
import com.lernen.cloud.core.utils.SharedConstants;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class CommunicationServiceUtils {

    public static CounterType getCounterForService(CommunicationServiceProvider communicationServiceProvider){
        if(communicationServiceProvider == null){
            return null;
        }
        switch (communicationServiceProvider){
            case MSG91_SMS:
            case WEBPAY_SMS:
            case PC_EXPERT_SMS:
                return CounterType.SMS_COUNTER;
            case DATAGEN_AUDIO_VOICECALL:
                return CounterType.AUDIO_VOICE_CALL_COUNTER;
            case DATAGEN_TEXT_VOICECALL:
                return CounterType.TEXT_VOICE_CALL_COUNTER;
            default:
                return null;
        }
    }


    public static Integer getServiceCredits(CommunicationServiceProvider communicationServiceProvider, String messagePayload, Float voiceDuration){
        if(communicationServiceProvider == null){
            return null;
        }
        switch (communicationServiceProvider){
            case MSG91_SMS:
            case WEBPAY_SMS:
            case PC_EXPERT_SMS:
                return NotificationDetails.getSmsCredits(messagePayload);
            case DATAGEN_AUDIO_VOICECALL:
                return  NotificationDetails.getVoiceCallCredits(voiceDuration);
            case DATAGEN_TEXT_VOICECALL:
            default:
                return null;
        }
    }

    public static AudioFileProperties getAudioFileProperties(CommunicationTemplate audioCommunicationTemplate){
        return getAudioFileProperties(audioCommunicationTemplate.getMetadata());
    }

    public static AudioFileProperties getAudioFileProperties(Map<String, Object> metadataMap){
        AudioFileProperties audioFileProperties = SharedConstants.GSON.fromJson(String.valueOf(metadataMap.get(CommunicationTemplate.AUDIO_METADATA)), AudioFileProperties.class);
        return audioFileProperties;
    }

}
