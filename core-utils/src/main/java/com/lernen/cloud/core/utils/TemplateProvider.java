/**
 * 
 */
package com.lernen.cloud.core.utils;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import org.apache.velocity.Template;
import org.apache.velocity.app.VelocityEngine;
import org.apache.velocity.runtime.RuntimeConstants;
import org.apache.velocity.runtime.log.NullLogChute;
import org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader;

/**
 * <AUTHOR>
 *
 */
public class TemplateProvider {
	public static final String RESET_PASSWORD_HTML = "reset_password_html";
	public static final String STUDENT_ADMISSION_WITH_CREDENTIALS_HTML = "student_admission_with_credentials_html";

	private static final Map<String, String> TEMPLATE_FILE_NAMES = new HashMap<>();
	public static final TemplateProvider INSTANCE = new TemplateProvider();

	static {
		TEMPLATE_FILE_NAMES.put(RESET_PASSWORD_HTML, "reset_password_html.vm");
		TEMPLATE_FILE_NAMES.put(STUDENT_ADMISSION_WITH_CREDENTIALS_HTML, "student_admission_with_credentials_html.vm");
	}

	private TemplateProvider() {

	}

	public static Template getTemplate(String templateName) throws IOException {
		VelocityEngine velocityEngine = new VelocityEngine();
		velocityEngine.setProperty(RuntimeConstants.RESOURCE_LOADER, "classpath");
		velocityEngine.setProperty("classpath.resource.loader.class", ClasspathResourceLoader.class.getName());
		velocityEngine.setProperty("runtime.log.logsystem.class", NullLogChute.class.getName());
		velocityEngine.init();
		return velocityEngine.getTemplate(TEMPLATE_FILE_NAMES.get(templateName));
	}
}
