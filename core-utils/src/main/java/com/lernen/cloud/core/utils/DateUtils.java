package com.lernen.cloud.core.utils;

import com.lernen.cloud.core.api.common.EHourMinute;
import com.lernen.cloud.core.api.common.EMonthDay;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.institute.Time;
import com.lernen.cloud.core.api.user.User;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.Days;
import org.joda.time.Period;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.text.DateFormat;
import java.text.DateFormatSymbols;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.Month;
import java.time.format.DateTimeParseException;
import java.util.*;


/**
 * <AUTHOR>
 */
public class DateUtils {
    public static final long TO_LONG = 1000L;

    private final static int SECONDS_IN_DAY = 86400;
    public static final String DEFAULT_DATE_FORMAT = "dd/MMM/yyyy";
    public static final String DATE_FORMAT_WITH_DOT = "dd.MMM.yyyy";
    public static final String ONLY_DATE_FORMAT = "dd";
    public static final String DATE_FORMAT = "yyyy-MMM-dd";
    public static final String CONTINUOUS_DATE_FORMAT = "ddMMyyyy";
    public static final String MONTH_YEAR_DATE_FORMAT = "MM-yyyy";
    public static final String DEFAULT_DATE_TIME_FORMAT = "dd/MMM/yyyy HH:mm:ss";
    public static final String DEFAULT_DATE_TIME_FORMAT_WITH_NUMBER_MONTH = "dd/MM/yyyy HH:mm:ss";
    public static final TimeZone DEFAULT_TIMEZONE = TimeZone.getTimeZone("Asia/Kolkata");
    public static final String DEFAULT_DAY_FORMAT = "EEEE";
    public static final String DATE_FORMAT_WITH_HYPHEN = "dd-MMM-yyyy";

    public static final String AM_PM_TIME_FORMAT = "hh:mm aa";

    private static final String UNIT_PLACE_ARRAY_TILL_NINE[] = {"", "One", "Two", "Three", "Four", "Five", "Six", "Seven", "Eight", "Nine",};
    private static final String HUNDRED_PLACE_ARRAY[] = {"Hundred", "Thousand"};
    private static final String UNIT_PLACE_ARRAY_TILL_NINETEEN[] = {"Ten", "Eleven", "Twelve", "Thirteen", "Fourteen", "Fifteen", "Sixteen", "Seventeen", "Eighteen", "Ninteen",};
    private static final String TEN_PLACE_ARRAY[] = {"Twenty", "Thirty", "Forty", "Fifty", "Sixty", "Seventy", "Eighty", "Ninty"};
    private static String dateInword;

    public static String getDefaultTimezoneFormattedDate(int timeInSec, String format) {
        return getFormattedDate(timeInSec, format, DEFAULT_TIMEZONE);
    }

    public static String getFormattedDate(int timeInSec, String format, TimeZone timeZone) {
        final Date date = new Date(timeInSec * 1000L);
        final DateFormat dateFormat = new SimpleDateFormat(format);
        dateFormat.setTimeZone(timeZone);
        final String formattedDate = dateFormat.format(date);
        return formattedDate;
    }

    public static List<Integer> getDateList(int start, int end) {
        return getDateList(start, end, true);
    }
    public static List<Integer> getDateList(int start, int end, boolean includeEndDate) {
        List<Integer> dateList = new ArrayList<>();
        for (int i = start; i <= (includeEndDate ? end : end - (SECONDS_IN_DAY)); i += SECONDS_IN_DAY) {
            dateList.add(i);
        }
        return dateList;
    }

    public static String getFormattedDate(int timeInSec) {
        return getFormattedDate(timeInSec, DEFAULT_DATE_FORMAT, DEFAULT_TIMEZONE);
    }

    public static String getFormattedDateWithDot(int timeInSec) {
        return getFormattedDate(timeInSec, DATE_FORMAT_WITH_DOT, DEFAULT_TIMEZONE);
    }

    public static int now() {
        return (int) (System.currentTimeMillis() / 1000l);
    }

    public static int getTimestampFromDate(String date, TimeZone timezone, String format) {
        final DateTimeFormatter formatter = DateTimeFormat.forPattern(format)
                .withZone(DateTimeZone.forTimeZone(timezone));
        return (int) (formatter.parseMillis(date) / 1000l);
    }

    public static int getTimestampFromDate(String date) {
        final DateTimeFormatter formatter = DateTimeFormat.forPattern(DEFAULT_DATE_FORMAT)
                .withZone(DateTimeZone.forTimeZone(DEFAULT_TIMEZONE));
        return (int) (formatter.parseMillis(date) / 1000l);
    }

    public static boolean validateDateFormat(String date) {
        DateTimeFormatter dateFormatter = DateTimeFormat.forPattern(DEFAULT_DATE_FORMAT);
        try {
            dateFormatter.parseLocalDate(date);
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }

    public static long getMillisFromDate(String date, TimeZone timezone, String format) {
        final DateTimeFormatter formatter = DateTimeFormat.forPattern(format)
                .withZone(DateTimeZone.forTimeZone(timezone));
        return formatter.parseMillis(date);
    }

    public static long getTimestampFromDate(String date, String format) {
        final DateTimeFormatter formatter = DateTimeFormat.forPattern(format);
        return formatter.parseMillis(date);
    }

    public static DateTime getDefaultTimezoneDayStart(int time) {
        return getDefaultZoneDateTime(getDayStart(time, DEFAULT_TIMEZONE));
    }


    public static int getDayStartDefaultTimezone(int time) {
        return getDayStart(time, DEFAULT_TIMEZONE);
    }

    public static int getDayStartDefaultTimezone(long timeInMillis) {
        return getDayStart((int) (timeInMillis / 1000l), DEFAULT_TIMEZONE);
    }

    public static int getDayStart(int time, TimeZone timezone) {
        DateTime givenDate = new DateTime(time * 1000l, DateTimeZone.forTimeZone(timezone));
        DateTime startDate = givenDate.withHourOfDay(0).withMinuteOfHour(0).withSecondOfMinute(0);
        return (int) (startDate.getMillis() / 1000l);
    }

    public static int getDayStartWithDefaultZone(int time) {
        DateTime givenDate = new DateTime(time * 1000l, DateTimeZone.forTimeZone(DEFAULT_TIMEZONE));
        DateTime startDate = givenDate.withHourOfDay(0).withMinuteOfHour(0).withSecondOfMinute(0);
        return (int) (startDate.getMillis() / 1000l);
    }

    public static long getDayStart(long time, TimeZone timezone) {
        DateTime givenDate = new DateTime(time, DateTimeZone.forTimeZone(timezone));
        DateTime startDate = givenDate.withHourOfDay(0).withMinuteOfHour(0).withSecondOfMinute(0).withMillisOfSecond(0);
        return startDate.getMillis();
    }

    public static int getNextDayStart(int time, TimeZone timezone) {
        DateTime givenDate = new DateTime(time * 1000l, DateTimeZone.forTimeZone(timezone));
        DateTime startDate = givenDate.plusDays(1).withHourOfDay(0).withMinuteOfHour(0).withSecondOfMinute(0).withMillisOfSecond(0);
        return (int) (startDate.getMillis() / 1000l);
    }

    public static int getDayEnd(int time, TimeZone timezone) {
        DateTime givenDate = new DateTime(time * 1000l, DateTimeZone.forTimeZone(timezone));
        DateTime endDate = givenDate.withHourOfDay(23).withMinuteOfHour(59).withSecondOfMinute(59);
        return (int) (endDate.getMillis() / 1000l);
    }

    public static int getDayEndWithDefaultZone(int time) {
        DateTime givenDate = new DateTime(time * 1000l, DateTimeZone.forTimeZone(DEFAULT_TIMEZONE));
        DateTime endDate = givenDate.withHourOfDay(23).withMinuteOfHour(59).withSecondOfMinute(59);
        return (int) (endDate.getMillis() / 1000l);
    }

    public static long getDayEnd(long time, TimeZone timezone) {
        DateTime givenDate = new DateTime(time, DateTimeZone.forTimeZone(timezone));
        DateTime endDate = givenDate.withHourOfDay(23).withMinuteOfHour(59).withSecondOfMinute(59)
                .withMillisOfSecond(999);
        return endDate.getMillis();
    }

    public static int getFirstDayOfMonth(Month month, int year, TimeZone timezone) {
        DateTime now = new DateTime(DateTimeZone.forTimeZone(timezone));
        DateTime firstDate = now.withYear(year).withMonthOfYear(month.ordinal() + 1).withDayOfMonth(1).withHourOfDay(0)
                .withMinuteOfHour(0).withSecondOfMinute(0).withMillisOfSecond(0);
        return (int) (firstDate.getMillis() / 1000l);
    }

    public static int getLastDayOfMonth(Month month, int year, TimeZone timezone) {
        DateTime now = new DateTime(DateTimeZone.forTimeZone(timezone));
        int maxDays = now.withYear(year).withMonthOfYear(month.ordinal() + 1).dayOfMonth().getMaximumValue();

        DateTime lastDate = now.withYear(year).withMonthOfYear(month.ordinal() + 1).withDayOfMonth(maxDays)
                .withHourOfDay(23).withMinuteOfHour(59).withSecondOfMinute(59).withMillisOfSecond(999);

        return (int) (lastDate.getMillis() / 1000l);
    }

    public static Month getMonth (int monthNumber) {
        return Month.of(monthNumber);
    }

    public static int getCurrentYear() {
        return new DateTime(DateTime.now()).getYear();
    }

    public static String getDateInWords(Integer dateTime) {
        return getDateInWords(dateTime, true);
    }

    public static String getDateInWords(Integer dateTime, boolean includeAnd) {

        if (dateTime == null || dateTime <= 0) {
            return null;
        }

        DateTime date = new DateTime(dateTime * 1000L, DateTimeZone.forTimeZone(DEFAULT_TIMEZONE));
        return getUnitPlaceInWords(date.getDayOfMonth(), includeAnd) + " " + getTensPlaceInWords(date.getMonthOfYear()) + " " + getHundredPlaceInWords(date.getYear(), includeAnd);
    }

    private static String getHundredPlaceInWords(int year, boolean includeAnd) {
        return getUnitPlaceInWords(year, includeAnd);
    }

    private static String getTensPlaceInWords(int month) {
        return new DateFormatSymbols().getMonths()[month - 1];
    }

    private static String getUnitPlaceInWords(int day, boolean includeAnd) {
        int n = 1;
        int word;
        dateInword = "";
        while (day != 0) {
            switch (n) {
                case 1:
                    word = day % 100;
                    pass(word);
                    if (day > 100 && day % 100 != 0 && includeAnd) {
                        show("and ");
                    }
                    day /= 100;
                    break;

                case 2:
                    word = day % 10;
                    if (word != 0) {
                        show(" ");
                        show(HUNDRED_PLACE_ARRAY[0]);
                        show(" ");
                        pass(word);
                    }
                    day /= 10;
                    break;

                case 3:
                    word = day % 100;
                    if (word != 0) {
                        show(" ");
                        show(HUNDRED_PLACE_ARRAY[1]);
                        show(" ");
                        pass(word);
                    }
                    day /= 100;
                    break;

                case 4:
                    word = day % 100;
                    if (word != 0) {
                        show(" ");
                        show(HUNDRED_PLACE_ARRAY[2]);
                        show(" ");
                        pass(word);
                    }
                    day /= 100;
                    break;

                case 5:
                    word = day % 100;
                    if (word != 0) {
                        show(" ");
                        show(HUNDRED_PLACE_ARRAY[3]);
                        show(" ");
                        pass(word);
                    }
                    day /= 100;
                    break;

            }
            n++;
        }
        return dateInword;

    }

    public static void show(String s) {
        String st;
        st = dateInword;
        dateInword = s;
        dateInword += st;
    }

    public static void pass(int number) {
        int word, q;
        if (number < 10) {
            show(UNIT_PLACE_ARRAY_TILL_NINE[number]);
        }
        if (number > 9 && number < 20) {
            show(UNIT_PLACE_ARRAY_TILL_NINETEEN[number - 10]);
        }
        if (number > 19) {
            word = number % 10;
            if (word == 0) {
                q = number / 10;
                show(TEN_PLACE_ARRAY[q - 2]);
            } else {
                q = number / 10;
                show(UNIT_PLACE_ARRAY_TILL_NINE[word]);
                show(" ");
                show(TEN_PLACE_ARRAY[q - 2]);
            }
        }
    }

    public static int getYearGap(int startTime, int endTime) {
        Period period = new Period(startTime * 1000l, endTime * 1000l);
        return period.getYears();
    }


    public static String getDayOfWeek(Integer epochSecond) {
        return getFormattedDate(epochSecond, DEFAULT_DAY_FORMAT, DEFAULT_TIMEZONE);
    }

    public static boolean isSunday(Integer epochDate) {
        if(epochDate == null) {
            return false;
        }
        DateTime epochDateTime = new DateTime(epochDate * 1000l, DateTimeZone.forTimeZone(DEFAULT_TIMEZONE));
        DayOfWeek dayOfWeek = DayOfWeek.of(epochDateTime.getDayOfWeek());
        return dayOfWeek == DayOfWeek.SUNDAY;  
    }

    public static int addMinutesCurrentTime(int minutes) {
        DateTime dateTime = new DateTime();
        dateTime = dateTime.plusMinutes(minutes);
        return (int) (dateTime.getMillis() / 1000l);
    }

    public static int addTimeInDate(int epochTime, int hours, int minutes, int seconds) {
        DateTime dateTime = getDefaultZoneDateTime(epochTime);
        dateTime = dateTime.plusHours(hours).plusMinutes(minutes).plusSeconds(seconds);
        return (int) (dateTime.getMillis() / 1000l);
    }
    public static int addMinutes(int epochTime, int minutes) {
        DateTime dateTime = getDefaultZoneDateTime(epochTime);
        dateTime = dateTime.plusMinutes(minutes);
        return (int) (dateTime.getMillis() / 1000l);
    }

    public static int addDaysCurrentTime(int days) {
        DateTime dateTime = new DateTime();
        dateTime = dateTime.plusDays(days);
        return (int) (dateTime.getMillis() / 1000l);
    }

    public static int addDays(Integer epochTime, int days) {
        if (epochTime == null) {
            return 0;
        }
        DateTime dateTime = new DateTime(epochTime * 1000l, DateTimeZone.forTimeZone(DEFAULT_TIMEZONE));
        dateTime = dateTime.plusDays(days);
        return (int) (dateTime.getMillis() / 1000l);
    }

    public static int getMonth(int time, TimeZone timezone) {
        DateTime givenDate = new DateTime(time * 1000L, DateTimeZone.forTimeZone(timezone));
        return givenDate.getMonthOfYear();
    }

    public static int getDay(int time, TimeZone timezone) {
        DateTime givenDate = new DateTime(time * 1000L, DateTimeZone.forTimeZone(timezone));
        return givenDate.getDayOfMonth();
    }

    public static int calculateDateTime(Integer epochDate, Time time) {
        DateTime givenDateTime = new DateTime(epochDate * 1000l, DateTimeZone.forTimeZone(DEFAULT_TIMEZONE));
        DateTime requiredDateTime = givenDateTime.withHourOfDay(time.getHour())
                .withMinuteOfHour(time.getMinute()).withSecondOfMinute(time.getSecond());
        return (int) (requiredDateTime.getMillis() / 1000l);
    }

    public static Time calculateTime(Integer epochDate) {
        DateTime givenDateTime = new DateTime(epochDate * 1000l, DateTimeZone.forTimeZone(DEFAULT_TIMEZONE));
        return new Time(givenDateTime.getHourOfDay(), givenDateTime.getMinuteOfHour(), givenDateTime.getSecondOfMinute());
    }

    public static String getFormattedTime(Time time, boolean showSeconds) {

        int hour = time.getHour();
        String hourStr = ("0" + hour);
        hourStr = hourStr.substring(hourStr.length() - 2);

        int minute = time.getMinute();
        String minuteStr = ("0" + minute);
        minuteStr = minuteStr.substring(minuteStr.length() - 2);

        if (showSeconds) {
            int second = time.getSecond();
            String secondStr = ("0" + second);
            secondStr = secondStr.substring(secondStr.length() - 2);
            return hourStr + ":" + minuteStr + ":" + secondStr;
        }
        return hourStr + ":" + minuteStr;
    } 

    public static String getFormattedAmPmTime(Time time, boolean showSeconds) {
        int hour = time.getHour();
        int formattedHour = hour > 12 ? hour - 12 : hour;
        String hourStr = ("0" + formattedHour);
        hourStr = hourStr.substring(hourStr.length() - 2);

        int minute = time.getMinute();
        String minuteStr = ("0" + minute);
        minuteStr = minuteStr.substring(minuteStr.length() - 2);

        String amPmStr = hour < 12 ? "AM" : hour == 12 && minute == 0 ? "Noon" : "PM";

        if (showSeconds) {
            int second = time.getSecond();
            String secondStr = ("0" + second);
            secondStr = secondStr.substring(secondStr.length() - 2);
            amPmStr = hour < 12 ? "AM" : hour == 12 && minute == 0 && second == 0 ? "Noon" : "PM";
            return hourStr + ":" + minuteStr + ":" + secondStr + " " + amPmStr;
        }
        return hourStr + ":" + minuteStr + " " + amPmStr;
    }

//	public static DayOfWeek getDayOfWeek(Integer epochDate) {
//		DateTime epochDateTime = new DateTime(epochDate * 1000l, DateTimeZone.forTimeZone(DFAULT_TIMEZONE));
//		return DayOfWeek.of(epochDateTime.getDayOfWeek());
//	}

    /**
     * @param totalDurationInSec
     * @return assuming total duration have time in hrs
     */
    public static String getDuration(double totalDurationInSec, boolean showSeconds) {

        int totalDurationInt = (int) totalDurationInSec;

        int hour = totalDurationInt / 3600;
        totalDurationInt %= 3600;
        int min = totalDurationInt / 60;
        totalDurationInt %= 60;
        int sec = totalDurationInt;

        if (showSeconds) {
            return hour + " hr " + min + " min" + sec + " second";
        }
        return hour + " hr " + min + " min";
    }
    public static Month getMonthOfYear(Integer epochDate) {
        DateTime epochDateTime = new DateTime((long) epochDate * 1000L, DateTimeZone.forTimeZone(DEFAULT_TIMEZONE));
        return Month.of(epochDateTime.getMonthOfYear());
    }

    public static int getIntMonthOfYear(Integer epochDate) {
        /**
         * using global timezone in this case due to month and day comparison in birthday flow
         */
        DateTime epochDateTime = new DateTime((long) epochDate * 1000L);
        return epochDateTime.getMonthOfYear();
    }

    public static int getIntDayOfMonth(Integer epochDate) {
        /**
         * using global timezone in this case due to month and day comparison in birthday flow
         */
        DateTime epochDateTime = new DateTime((long) epochDate * 1000L);
        return epochDateTime.getDayOfMonth();
    }

    public static boolean isSameDay(int day1, int day2) {
        Date epochDate1 = new Date((long) DateUtils.getDayStart(day1, DEFAULT_TIMEZONE) * 1000L);
        Date epochDate2 = new Date((long) DateUtils.getDayStart(day2, DEFAULT_TIMEZONE) * 1000L);
        return org.apache.commons.lang.time.DateUtils.isSameDay(epochDate1, epochDate2);
    }

    public static int getDefaultZoneTime(EMonthDay monthDay, int year) {
        DateTime dateTime = new DateTime(DateTimeZone.forTimeZone(DEFAULT_TIMEZONE)).withDate(year, monthDay.getMonth(), monthDay.getDay()).withTime(0, 0, 0, 0);
        return (int) (dateTime.getMillis() / 1000l);
    }

    public static int getDefaultZoneYear(int timeInSec) {
        return getDefaultZoneYear(timeInSec * 1000l);
    }

    public static int getDefaultZoneYear(long timeInMillis) {
        return new DateTime(timeInMillis, DateTimeZone.forTimeZone(DEFAULT_TIMEZONE)).getYear();
    }

    public static DateTime getDefaultZoneDateTime(int timeInSec) {
        return getDefaultZoneDateTime(timeInSec * 1000l);
    }

    public static DateTime getDefaultZoneDateTime(long timeInMillis) {
        return new DateTime(timeInMillis, DateTimeZone.forTimeZone(DEFAULT_TIMEZONE));
    }

    public static int compareHourMinute(EHourMinute a, EHourMinute b) {
        if (a.getHour() < b.getHour()) {
            return -1;
        } else if (a.getHour() > b.getHour()) {
            return 1;
        } else if (a.getMinute() < b.getMinute()) {
            return -1;
        } else if (a.getMinute() > b.getMinute()) {
            return 1;
        } else {
            return 0;
        }
    }

    public static int getWeekOfMonth(int timeInSec) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(timeInSec * 1000l);
        return calendar.get(Calendar.WEEK_OF_MONTH);
    }

    public static int getInSec(long timeInMilliSec) {
        return (int) (timeInMilliSec / 1000l);
    }
    
    public static boolean isSameDayAsToday(long epochTime) {
        final long transactionDateDayStart = DateUtils.getDayStart(epochTime, User.DFAULT_TIMEZONE);
        final long currentTimeDayStart = DateUtils.getDayStart(DateUtils.now() * 1000l, User.DFAULT_TIMEZONE);

        return transactionDateDayStart == currentTimeDayStart;
    }

    /**
     * this function gives 0 days when startDate and endDate is same
     */
    public static int daysBetween(final int start, final int end) {
        return Days.daysBetween(new DateTime(start * TO_LONG, DateTimeZone.forTimeZone(DEFAULT_TIMEZONE)),
                new DateTime(end * TO_LONG,DateTimeZone.forTimeZone(DEFAULT_TIMEZONE))).getDays();
    }

    public static boolean dateLieBetweenTwoDates(final int start, final int end, final int date) {
        return (start <= date) && (end >= date);
    }

    public static int differenceOfYearBetweenTwoDates(final int start, final int end) {
        DateTime startDateTime = new DateTime(start * TO_LONG, DateTimeZone.forTimeZone(DEFAULT_TIMEZONE));
        DateTime endDateTime = new DateTime(end * TO_LONG, DateTimeZone.forTimeZone(DEFAULT_TIMEZONE));
        return endDateTime.getYear() - startDateTime.getYear();
    }

    public static boolean isLeapYear(int year) {
        return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
    }
}
