package com.lernen.cloud.core.utils.oauth2;

import com.google.gson.Gson;
import com.lernen.cloud.core.utils.SharedConstants;
import com.lernen.cloud.core.utils.rest.RestClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.oauth2.common.OAuth2AccessToken;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;

public class OauthUtils {

    private static Logger logger = LoggerFactory.getLogger(OauthUtils.class);

    public static String parseToken(final HttpServletRequest httpRequest) {
        String token = parseHeaderToken(httpRequest);
        // bearer type allows a request parameter as well
        if (token == null) {
            token = httpRequest.getParameter(OAuth2AccessToken.ACCESS_TOKEN);
        }
        return token;
    }

    public static String parseHeaderToken(final HttpServletRequest request) {
        final Enumeration<String> headers = request.getHeaders("Authorization");
        while (headers.hasMoreElements()) { // typically there is only one (most
            // servers enforce that)
            final String value = headers.nextElement();
            if ((value.toLowerCase().startsWith(OAuth2AccessToken.BEARER_TYPE.toLowerCase()))) {
                String authHeaderValue = value.substring(OAuth2AccessToken.BEARER_TYPE.length()).trim();
                final int commaIndex = authHeaderValue.indexOf(',');
                if (commaIndex > 0) {
                    authHeaderValue = authHeaderValue.substring(0, commaIndex);
                }
                return authHeaderValue;
            } else {
                // todo: support additional authorization schemes for different
                // token types, e.g. "MAC" specified by
                // http://tools.ietf.org/html/draft-hammer-oauth-v2-mac-token
            }
        }

        return null;
    }
}
