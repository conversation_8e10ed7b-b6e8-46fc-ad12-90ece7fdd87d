package com.lernen.cloud.core.utils.aws.lambda;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.services.lambda.AWSLambda;
import com.amazonaws.services.lambda.AWSLambdaClientBuilder;
import com.lernen.cloud.core.utils.credentials.IAWSCredentialProviderChain;

public class AWSLambdaClientProvider {

    private final AWSLambda awsLambda;

    public AWSLambdaClientProvider(IAWSCredentialProviderChain awsCredentialProviderChain) {
        awsLambda = AWSLambdaClientBuilder.standard().withRegion(awsCredentialProviderChain.getRegion())
                .withCredentials(awsCredentialProviderChain.getCredentials()).withClientConfiguration(new ClientConfiguration().withSocketTimeout(300000)).build();
    }

    public AWSLambda getLambdaClient() {
        return awsLambda;
    }


}
