package com.lernen.cloud.core.utils.permissions;


import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @created_at 02/05/24 : 16:40
 **/
public class EPermissionUtils {

    public static boolean checkUserAccessibility(Map<UUID, Set<Integer>> userAccessibleStandardSectionDetails, UUID standardId, Integer sectionId) {
        if(CollectionUtils.isEmpty(userAccessibleStandardSectionDetails) || standardId == null) {
            return false;
        }

        if(!userAccessibleStandardSectionDetails.containsKey(standardId)) {
            return false;
        }

        if(CollectionUtils.isEmpty(userAccessibleStandardSectionDetails.get(standardId))) {
            return sectionId == null || sectionId <= 0;
        }

        if(!userAccessibleStandardSectionDetails.get(standardId).contains(sectionId)) {
            return false;
        }

        return true;
    }

    public static boolean checkUserAccessibility(Map<String, Set<UUID>> staffStandardSectionStrCourseDetailsMap, UUID standardId, Set<Integer> sectionIdSet, UUID courseId) {
        if(CollectionUtils.isEmpty(staffStandardSectionStrCourseDetailsMap) || standardId == null) {
            return false;
        }

        Set<String> standardSectionIdStrSet = new HashSet<>();
        if(!CollectionUtils.isEmpty(sectionIdSet)) {
            for(Integer sectionId : sectionIdSet) {
                standardSectionIdStrSet.add(standardId +  (sectionId == null ? "" : ":" + sectionId));
            }
        } else {
            standardSectionIdStrSet.add(standardId.toString());
        }

        for(String standardSectionIdStr : standardSectionIdStrSet) {

            if (!staffStandardSectionStrCourseDetailsMap.containsKey(standardSectionIdStr)) {
                return false;
            }

            /**
             * this case is specific to homework when courseId is null, so if staff is teaching a standard,
             * then it can raise a homework with no course.
             * For exam marks, course id will never to null.
             * for including this method in future flows, make sure this condition is checked properly.
             */
            if (CollectionUtils.isEmpty(staffStandardSectionStrCourseDetailsMap.get(standardSectionIdStr)) && courseId == null) {
                return true;
            }

            if (courseId == null) {
                return true;
            }

            /**
             * this ser empty meaning, staff have no course assign in this standard, so returning false
             */
            if (CollectionUtils.isEmpty(staffStandardSectionStrCourseDetailsMap.get(standardSectionIdStr))) {
                return false;
            }

            if (!staffStandardSectionStrCourseDetailsMap.get(standardSectionIdStr).contains(courseId)) {
                return false;
            }
        }
        return true;
    }

//    public static boolean checkUserAccessibility(Map<String, Set<UUID>> staffStandardSectionStrCourseDetailsMap, UUID standardId, Set<Integer> sectionIdSet, UUID courseId) {
//        if(CollectionUtils.isEmpty(staffStandardSectionStrCourseDetailsMap) || standardId == null) {
//            return false;
//        }
//
//        Set<String> standardSectionIdStrSet = new HashSet<>();
//        if(!CollectionUtils.isEmpty(sectionIdSet)) {
//            for(Integer sectionId : sectionIdSet) {
//                standardSectionIdStrSet.add(standardId +  (sectionId == null ? "" : ":" + sectionId));
//            }
//        } else {
//            standardSectionIdStrSet.add(standardId.toString());
//        }
//
//        for(String standardSectionIdStr : standardSectionIdStrSet) {
//            if (!staffStandardSectionStrCourseDetailsMap.containsKey(standardSectionIdStr)) {
//                return false;
//            }
//
//            if (CollectionUtils.isEmpty(staffStandardSectionStrCourseDetailsMap.get(standardSectionIdStr)) && courseId == null) {
//                return true;
//            }
//
//            if (CollectionUtils.isEmpty(staffStandardSectionStrCourseDetailsMap.get(standardSectionIdStr))) {
//                return false;
//            }
//
//            if(courseId == null) {
//                return true;
//            }
//
//            if (!staffStandardSectionStrCourseDetailsMap.get(standardSectionIdStr).contains(courseId)) {
//                return false;
//            }
//        }
//        return true;
//    }
}
