package com.lernen.cloud.core.utils.exceptions;

import org.apache.commons.lang3.StringUtils;

import com.lernen.cloud.core.api.exception.DatabaseErrorCode;
import com.lernen.cloud.core.api.exception.DatabaseException;
import com.lernen.cloud.core.api.exception.ErrorResponse;

public class ExceptionHandling {
	
	public static String exceptionHandlingMessage(String moduleName, String fieldName) {
		String message = "";
		if(StringUtils.isNotBlank(moduleName) && StringUtils.isNotBlank(fieldName)) {
			message = "Request can't be processed as " + moduleName + " with given "+ fieldName + " already exists.";
		} else if(StringUtils.isNotBlank(moduleName)) {
			message = "Request can't be processed as " + moduleName + " contains duplicate fields.";
		} else {
			message = "Request can't be processed as this process contains duplicate field entry.";
		}
		return message;
	}
	public static void HandleException(Exception ex, String moduleName, String fieldName) {
		if(ex.getMessage().contains("SQLIntegrityConstraintViolationException") && ex.getMessage().contains("Duplicate entry")) {
			String message = exceptionHandlingMessage(moduleName, fieldName);
			throw new DatabaseException(new ErrorResponse(DatabaseErrorCode.DUPLICATE_ENTRY, message));
		} else if(ex.getMessage().contains("SQLIntegrityConstraintViolationException") && ex.getMessage().contains("foreign key constraint fails")) {
			throw new DatabaseException(new ErrorResponse(DatabaseErrorCode.FOREIGN_KEY, "This entry cannot be deleted as it is already in use."));
		}
	}
	
}
