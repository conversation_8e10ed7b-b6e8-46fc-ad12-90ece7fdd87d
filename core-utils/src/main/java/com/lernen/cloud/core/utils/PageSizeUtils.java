package com.lernen.cloud.core.utils;

import com.itextpdf.kernel.geom.PageSize;

public class PageSizeUtils {

    /**
     * Important Points
     * Input strings are case-insensitive ("A4" or "a4" work the same)
     * Spaces are trimmed automatically
     * The "-R" suffix must be directly attached to the size (no space)
     * Default size is A4 if input is invalid
     */
    public static PageSize convertStringToPageSize(String pageSizeStr) {
        if (pageSizeStr == null || pageSizeStr.trim().isEmpty()) {
            return PageSize.A4;
        }

        pageSizeStr = pageSizeStr.toUpperCase().trim();
        boolean rotate = false;

        // Check for rotation suffix
        if (pageSizeStr.endsWith("-R")) {
            rotate = true;
            pageSizeStr = pageSizeStr.substring(0, pageSizeStr.length() - 2).trim();
        }

        PageSize pageSize;

        // Standard sizes
        switch (pageSizeStr) {
            case "A0":
                pageSize = PageSize.A0;
                break;
            case "A1":
                pageSize = PageSize.A1;
                break;
            case "A2":
                pageSize = PageSize.A2;
                break;
            case "A3":
                pageSize = PageSize.A3;
                break;
            case "A4":
                pageSize = PageSize.A4;
                break;
            case "A5":
                pageSize = PageSize.A5;
                break;
            case "A6":
                pageSize = PageSize.A6;
                break;
            case "A7":
                pageSize = PageSize.A7;
                break;
            case "A8":
                pageSize = PageSize.A8;
                break;
            case "LETTER":
                pageSize = PageSize.LETTER;
                break;
            case "LEGAL":
                pageSize = PageSize.LEGAL;
                break;
            case "TABLOID":
                pageSize = PageSize.TABLOID;
                break;
            case "EXECUTIVE":
                pageSize = PageSize.EXECUTIVE;
                break;
            default:
                pageSize = PageSize.A4;
                break;
        }

        return rotate ? pageSize.rotate() : pageSize;
    }
}
