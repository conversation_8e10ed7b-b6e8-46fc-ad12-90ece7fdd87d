package com.embrate.cloud.core.utils.cache;

import java.io.*;
import java.util.List;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

/**
 * <AUTHOR>
 */
public class CacheGZIPSerializer<T extends Serializable> {

	public byte[] serialize(List<T> objects) throws IOException {
		try (ByteArrayOutputStream byteStream = new ByteArrayOutputStream();
			 GZIPOutputStream gzipStream = new GZIPOutputStream(byteStream);
			 ObjectOutputStream objectStream = new ObjectOutputStream(gzipStream)) {

			objectStream.writeObject(objects);
			objectStream.flush();
			gzipStream.finish();
			return byteStream.toByteArray();
		}
	}

	@SuppressWarnings("unchecked")
	public List<T> deserialize(byte[] data) throws IOException, ClassNotFoundException {
		try (ByteArrayInputStream byteStream = new ByteArrayInputStream(data);
			 GZIPInputStream gzipStream = new GZIPInputStream(byteStream);
			 ObjectInputStream objectStream = new ObjectInputStream(gzipStream)) {

			return (List<T>) objectStream.readObject();
		}
	}
}
