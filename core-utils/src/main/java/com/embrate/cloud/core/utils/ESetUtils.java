package com.embrate.cloud.core.utils;

import java.util.HashSet;
import java.util.Iterator;
import java.util.Set;

public class ESetUtils {

    /**
     * Returns the difference between two sets (elements in set1 that are not in set2).
     * Handles null or empty sets gracefully.
     *
     * @param <T>  The type of elements in the sets
     * @param set1 The first set
     * @param set2 The second set
     * @return A new set containing elements in set1 but not in set2
     */
    public static <T> Set<T> getSet1MinusSet2Difference(Set<T> set1, Set<T> set2) {
        // Handle null cases
        if (set1 == null || set1.isEmpty()) {
            return new HashSet<>(); // Return empty set if set1 is null or empty
        }
        if (set2 == null || set2.isEmpty()) {
            return new HashSet<>(set1); // Return a copy of set1 if set2 is null or empty
        }

        // Perform the set difference
        Set<T> result = new HashSet<>(set1);
        result.removeAll(set2); // Remove all elements in set2 from set1
        return result;
    }

    public static Set<String> covertToLowerCase(Set<String> stringSet) {
        if (stringSet == null || stringSet.isEmpty()) {
            return null;
        }

        Set<String> lowerCaseSet = new HashSet<>();
        Iterator<String> iterator = stringSet.iterator();

        while (iterator.hasNext()) {
            String str = iterator.next();
            if (str != null) {
                lowerCaseSet.add(str.toLowerCase());
            }
        }
        return lowerCaseSet;
    }

}
