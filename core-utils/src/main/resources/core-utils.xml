<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
	http://www.springframework.org/schema/beans/spring-beans-3.0.xsd 
	http://www.springframework.org/schema/context
	http://www.springframework.org/schema/context/spring-context-3.0.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd" default-lazy-init="true">

    <context:annotation-config/>

    <context:property-placeholder
            location="classpath*:utils-${lernen_env}.properties"
            system-properties-mode="OVERRIDE" />

    <bean id="s3ClientProvider" class="com.lernen.cloud.core.utils.aws.s3.S3ClientProvider">
        <constructor-arg name="awsCredentialProviderChain" ref="awsCredentialProviderChain"/>
    </bean>

    <bean id="awsLambdaClientProvider" class="com.lernen.cloud.core.utils.aws.lambda.AWSLambdaClientProvider">
        <constructor-arg name="awsCredentialProviderChain" ref="awsCredentialProviderChain"/>
    </bean>

    <bean id="awsLambdaInvoker" class="com.lernen.cloud.core.utils.aws.lambda.AWSLambdaInvoker">
        <constructor-arg name="awsLambdaClientProvider" ref="awsLambdaClientProvider"/>
    </bean>

    <bean id="awsKMSClientProvider" class="com.lernen.cloud.core.utils.aws.kms.AWSKMSClientProvider">
        <constructor-arg name="awsCredentialProviderChain" ref="awsCredentialProviderChain"/>
    </bean>

    <bean lazy-init="true" id="simpleDBClientProvider" class="com.lernen.cloud.core.utils.aws.simpledb.SimpleDBClientProvider">
        <constructor-arg name="awsCredentialProviderChain" ref="awsCredentialProviderChain"/>
    </bean>

    <bean lazy-init="true" id="awsDynamoDBClientProvider" class="com.lernen.cloud.core.utils.aws.dynamodb.AWSDynamoDBClientProvider">
        <constructor-arg name="awsCredentialProviderChain" ref="awsCredentialProviderChain"/>
    </bean>

    <bean id="awsKMSClient" class="com.lernen.cloud.core.utils.crypto.AWSKMSClient">
        <constructor-arg name="awsKMSClientProvider" ref="awsKMSClientProvider"/>
        <constructor-arg name="keyId"
                         value="${aws.kms.key}"/>
    </bean>

    <bean id="envPropertiesKMSClient" class="com.lernen.cloud.core.utils.crypto.AWSKMSClient">
        <constructor-arg name="awsKMSClientProvider" ref="awsKMSClientProvider"/>
        <constructor-arg name="keyId"
                         value="${aws.kms.key}"/>
    </bean>

    <!--	<bean id="defaultKMSClient" class="com.lernen.cloud.core.utils.crypto.DefaultKMSClient">-->
    <!--		<constructor-arg name="ivValue" value="#{'${default.kms.iv.value:}'.length() > 0 ? '${default.kms.iv.value:}': propertyStore.getPropertyValue('default.kms.iv.value')}"/>-->
    <!--		<constructor-arg name="keyId" value="#{'${default.kms.key.id:}'.length() > 0 ? '${default.kms.key.id:}': propertyStore.getPropertyValue('default.kms.key.id')}"/>-->
    <!--	</bean>-->

    <bean id="defaultKMSClient" class="com.lernen.cloud.core.utils.crypto.DefaultKMSClient">
        <constructor-arg name="ivValue" value="${default.kms.iv.value}"/>
        <constructor-arg name="keyId" value="${default.kms.key.id}"/>
    </bean>

    <bean id="restClient" class="com.lernen.cloud.core.utils.rest.RestClient">
    </bean>
    <bean id="restClientWithRetryHandler" class="com.lernen.cloud.core.utils.rest.RestClient"
          autowire-candidate="false">
        <constructor-arg name="withRetryHandler" type="boolean"
                         value="true"/>
    </bean>
    <bean id="restAPIHandler" class="com.lernen.cloud.core.utils.rest.RestAPIHandler">
        <constructor-arg name="restClient" ref="restClientWithRetryHandler"/>
    </bean>

    <bean lazy-init="true" id="amazonSimpleEmailServiceFactory"
          class="com.lernen.cloud.core.utils.aws.ses.AmazonSimpleEmailServiceFactory">
        <constructor-arg name="awsCredentialProviderChain" ref="awsCredentialProviderChain"/>
    </bean>


    <bean id="environmentPropertyProvider" class="com.lernen.cloud.core.utils.spring.EnvironmentPropertyProvider">
        <constructor-arg name="env" value="${lernen_env}"/>
        <constructor-arg name="frontendURL" value="${embrate.fe.url}"/>
        <constructor-arg name="apiServerURL" value="${embrate.api.server.url}"/>
    </bean>

    <bean lazy-init="true" id="simpleDBPropertyStore" class="com.lernen.cloud.core.utils.store.AWSSimpleDBPropertyStore">
        <constructor-arg name="simpleDBClientProvider" ref="simpleDBClientProvider"/>
        <constructor-arg name="kmsClient" ref="envPropertiesKMSClient"/>
        <constructor-arg name="env" value="${lernen_env}"/>
    </bean>

    <bean lazy-init="true" id="propertyStore" class="com.lernen.cloud.core.utils.store.AWSDynamoDBPropertyStore">
        <constructor-arg name="awsDynamoDBClientProvider" ref="awsDynamoDBClientProvider"/>
        <constructor-arg name="kmsClient" ref="envPropertiesKMSClient"/>
        <constructor-arg name="env" value="${lernen_env}"/>
    </bean>

    <bean id="defaultAWSCredentialsProviderChain" class="com.amazonaws.auth.DefaultAWSCredentialsProviderChain"/>

    <bean id="awsCredentialProviderChain"
          class="com.lernen.cloud.core.utils.credentials.AWSDefaultCredentialProviderChain">
        <constructor-arg name="defaultAWSCredentialsProviderChain" ref="defaultAWSCredentialsProviderChain"/>
        <constructor-arg name="region" value="us-east-1"/>
    </bean>

    <bean id="securityDataSource"
          class="org.springframework.jdbc.datasource.DriverManagerDataSource">
        <property name="driverClassName" value="com.mysql.jdbc.Driver"/>
        <property name="url" value="${security_mysql_url}" />
        <property name="username" value="${security_mysql_username}" />
        <property name="password" value="${security_mysql_password}" />
    </bean>

    <bean id="clientDetails"
          class="org.springframework.security.oauth2.provider.JdbcClientDetailsService">
        <constructor-arg index="0">
            <ref bean="securityDataSource"/>
        </constructor-arg>
    </bean>

    <bean id="tokenServices"
          class="org.springframework.security.oauth2.provider.token.DefaultTokenServices">
        <property name="tokenStore" ref="tokenStore"/>
        <property name="supportRefreshToken" value="true"/>
        <property name="clientDetailsService" ref="clientDetails"/>
        <property name="accessTokenValiditySeconds" value="4500"/>
    </bean>

    <bean id="tokenStore"
          class="org.springframework.security.oauth2.provider.token.JdbcTokenStore">
        <constructor-arg ref="securityDataSource"/>
    </bean>

    <bean id="clientDetailsUserService"
          class="org.springframework.security.oauth2.provider.client.ClientDetailsUserDetailsService">
        <constructor-arg ref="clientDetails"/>
    </bean>

    <bean id="clientAuthenticationEntryPoint"
          class="org.springframework.security.oauth2.provider.error.OAuth2AuthenticationEntryPoint">
        <property name="typeName" value="Basic" />
    </bean>

    <bean id="oauthAuthenticationEntryPoint"
          class="org.springframework.security.oauth2.provider.error.OAuth2AuthenticationEntryPoint">
        <property name="realmName" value="test" />
    </bean>

    <bean id="oauthAccessDeniedHandler"
          class="org.springframework.security.oauth2.provider.error.OAuth2AccessDeniedHandler" />


    <bean id="passwordEncoder"
          class="org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder">
        <constructor-arg name="strength" value="11" />
    </bean>

    <bean id="authorizationRequestManager"
          class="org.springframework.security.oauth2.provider.DefaultAuthorizationRequestManager" >
        <constructor-arg ref="clientDetails"/>
    </bean>

    <bean id="securityHeaderFilter"
          class="com.lernen.cloud.core.utils.oauth2.SecurityHeaderFilter">
    </bean>

    <bean id="readOnlyScopeFilter" class="com.lernen.cloud.core.utils.oauth2.ReadOnlyScopeFilter">
        <constructor-arg name="tokenServices" ref="tokenServices" />
    </bean>


    <bean id="oauthTokenManager" class="com.lernen.cloud.core.utils.oauth2.OauthTokenManager">
        <constructor-arg name="restAPIHandler" ref="restAPIHandler" />
        <constructor-arg name="environmentPropertyProvider" ref="environmentPropertyProvider" />
    </bean>

    <bean id="defaultOauthTokenProvider" class="com.lernen.cloud.core.utils.oauth2.DefaultOauthTokenProvider">
        <constructor-arg name="oauthTokenManager" ref="oauthTokenManager" />
        <constructor-arg name="clientId" value="${oauth_defaultClientId}" />
        <constructor-arg name="clientSecret" value="${oauth_defaultClientSecret}" />
        <constructor-arg name="readOnlyClientId" value="${oauth_defaultReadOnlyClientId}" />
        <constructor-arg name="readOnlyClientSecret" value="${oauth_defaultReadOnlyClientSecret}" />
    </bean>

    <bean id="cacheFactory" class="com.lernen.cloud.core.utils.cache.CacheFactory">
    </bean>


    <context:component-scan base-package="com.lernen.cloud.core.utils"/>
</beans>
