package com.lernen.cloud.sms.service;

import com.embrate.cloud.core.api.sms.webpay.WebPaySendSMSResponse;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.lernen.cloud.core.api.exception.SMSRunTimeException;
import com.lernen.cloud.core.api.sms.SMSContentPayload;
import com.lernen.cloud.core.api.sms.SMSPreferences;
import com.lernen.cloud.core.api.sms.SMSSendDetails;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.utils.PhoneNumberUtils;
import com.lernen.cloud.core.utils.SharedConstants;
import com.lernen.cloud.core.utils.StringHelper;
import com.lernen.cloud.core.utils.rest.RestAPIHandler;
import com.lernen.cloud.core.utils.rest.RestClient;
import com.sun.jersey.api.client.ClientHandlerException;
import com.sun.jersey.api.client.ClientResponse;
import com.sun.jersey.api.client.UniformInterfaceException;
import com.sun.jersey.api.client.WebResource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.net.URI;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Details of query params
 * http://smslogin.pcexpert.in/Web/WebAPI/WEBAPIHTTP.aspx Error codes
 * http://smslogin.pcexpert.in/Web/WebAPI/APICodes.aspx
 * 
 * <AUTHOR>
 *
 */
public class WebPaySMSSender implements ISMSSender {

	private static final Logger logger = LogManager.getLogger(WebPaySMSSender.class);

	private static final String SEND_SMS_API = "http://login.webpayservices.in/sms-panel/api/http/index.php";

	private static final String TRANSACTIONAL_ROUTE = "TRANS";
	private static final String NORMAL_MESSAGE_UNICODE_VALUE = "Text";
	private static final String UNICODE_MESSAGE_UNICODE_VALUE = "Unicode";
	private static final String FORMAT_VALUE = "JSON";

//	private static final String USER_NAME = "demo webpay";
//	private static final String USER_NAME = "embrate";
//	private static final String API_KEY = "70D92-C6585";
//
////	private static final String API_KEY = "5ABD1-AED70";
//	private static final String SENDER_ID_6_DIGIT = "EMBRTE";

//	private static final String SENDER_ID_6_DIGIT = "WEBPAY";

	private static final String USER_NAME_QUERY_PARAM_KEY = "username";
	private static final String API_KEY_QUERY_PARAM_KEY = "apikey";
	private static final String SENDER_ID_QUERY_PARAM_KEY = "sender";
	private static final String UNICODE_QUERY_PARAM_KEY = "apirequest";
	private static final String FORMAT_SMS_QUERY_PARAM_KEY = "format";
	private static final String NUMBER_QUERY_PARAM_KEY = "mobile";
	private static final String MESSAGE_QUERY_PARAM_KEY = "message";
	private static final String ROUTE_QUERY_PARAM_KEY = "route";
	private static final String DLT_TEMPLATE_ID_QUERY_PARAM_KEY = "TemplateID";


	private static final String SUCCESS_STATUS = "success";

	private final RestClient restClient;

	private final  UserPreferenceSettings userPreferenceSettings;

	public WebPaySMSSender(RestClient restClient, UserPreferenceSettings userPreferenceSettings) {
		this.restClient = restClient;
		this.userPreferenceSettings = userPreferenceSettings;
	}

	@Override
	public SMSSendDetails sendSMS(int instituteId, String mobileNumber, SMSContentPayload message) {
		Map<String, String> sendSMSQueryParams = createSendSMSQueryParams(instituteId, Arrays.asList(mobileNumber), message);
		if (sendSMSQueryParams == null) {
			logger.error("Invalid sms send details");
			return null;
		}
		ClientResponse response = null;
		try {
			WebResource webResource = restClient.resource(new URI(SEND_SMS_API));
			webResource = RestAPIHandler.addQueryParam(webResource, sendSMSQueryParams);
			logger.info("Sending sms with URI {}", webResource.getURI());
			response = webResource.type(MediaType.APPLICATION_JSON).get(ClientResponse.class);
			if (response.getStatus() == HttpStatus.SC_OK) {
				String responseData = response.getEntity(String.class);
				if (StringUtils.isBlank(responseData)) {
					logger.error("Error response from WebPay service {}. SMS failed due to error {}, status code {}",
							SEND_SMS_API, responseData, response.getStatus());
					throw new SMSRunTimeException("Error response from WebPay service provider.");
				}

				logger.info("web pay responseData = {}", responseData);
				WebPaySendSMSResponse webPaySendSMSResponse = SharedConstants.OBJECT_MAPPER.readValue(responseData,
						WebPaySendSMSResponse.class);

				if (SUCCESS_STATUS.equalsIgnoreCase(webPaySendSMSResponse.getStatus())) {
					return new SMSSendDetails(CollectionUtils.isEmpty(webPaySendSMSResponse.getJobIds()) ? null
							: webPaySendSMSResponse.getJobIds().get(0), mobileNumber);
				}

				logger.error("Unable to send SMS via WebPay {}, response {}", SEND_SMS_API, webPaySendSMSResponse);

				throw new SMSRunTimeException("Error response from WebPay service provider.");
			}
			handleErrorResponse(response);
		} catch (UniformInterfaceException | ClientHandlerException e) {
			logger.error("Unable to perform get call to URL {}", SEND_SMS_API, e);
		} catch (final Exception e) {
			logger.error("Exception occured while performing get call to URL {}", SEND_SMS_API, e);
		} finally {
			if (response != null) {
				response.close();
			}
		}
		return null;
	}

	private void handleErrorResponse(ClientResponse response)
			throws JsonParseException, JsonMappingException, IOException {
		String responseData = response.getEntity(String.class);
		if (StringUtils.isBlank(responseData)) {
			logger.error("Error response from WebPay {}. SMS failed due to error {}, status code {}", SEND_SMS_API,
					responseData, response.getStatus());
			throw new SMSRunTimeException("Error response from WebPay service provider.");
		}

		logger.info("web pay responseData = {}", responseData);

		WebPaySendSMSResponse webPaySendSMSResponse = SharedConstants.OBJECT_MAPPER.readValue(responseData,
				WebPaySendSMSResponse.class);

		logger.error("Unable to send SMS via WebPay {}, response {}", SEND_SMS_API, webPaySendSMSResponse);

		throw new SMSRunTimeException("Error response from WebPay service provider.");
	}

	private Map<String, String> createSendSMSQueryParams(int instituteId, List<String> mobileNumbers, SMSContentPayload message) {
		Map<String, String> sendSMSQueryParams = new HashMap<>();
		String unicodeValue = NORMAL_MESSAGE_UNICODE_VALUE;
		if (StringHelper.isUnicodeMessage(message.getContent())) {
			unicodeValue = UNICODE_MESSAGE_UNICODE_VALUE;
		}

		SMSPreferences smsPreferences = userPreferenceSettings.getSMSPreferences(instituteId);

		sendSMSQueryParams.put(USER_NAME_QUERY_PARAM_KEY, smsPreferences.getSmsSenderServiceUsername());
		sendSMSQueryParams.put(API_KEY_QUERY_PARAM_KEY, smsPreferences.getSmsSenderServiceApikey());
		sendSMSQueryParams.put(SENDER_ID_QUERY_PARAM_KEY, smsPreferences.getSmsSenderServiceSenderId());
		sendSMSQueryParams.put(ROUTE_QUERY_PARAM_KEY, TRANSACTIONAL_ROUTE);
		sendSMSQueryParams.put(UNICODE_QUERY_PARAM_KEY, unicodeValue);
		sendSMSQueryParams.put(FORMAT_SMS_QUERY_PARAM_KEY, FORMAT_VALUE);
		sendSMSQueryParams.put(DLT_TEMPLATE_ID_QUERY_PARAM_KEY, message.getDltTemplateId());
		sendSMSQueryParams.put(MESSAGE_QUERY_PARAM_KEY, message.getContent());

		String mobiles = "";
		boolean first = true;
		for (String mobileNumber : mobileNumbers) {
			if (!validMobileNumber(mobileNumber)) {
				return null;
			}
			String webPayFormattedMobileNumber = PhoneNumberUtils.formatTo10Digit(mobileNumber);
			if (first) {
				mobiles += webPayFormattedMobileNumber;
				first = false;
				continue;
			}
			mobiles = mobiles + "," + webPayFormattedMobileNumber;
		}
		sendSMSQueryParams.put(NUMBER_QUERY_PARAM_KEY, mobiles);

		return sendSMSQueryParams;

	}

	private boolean validMobileNumber(String mobileNumber) {
		if (StringUtils.isBlank(mobileNumber) || mobileNumber.length() != 13) {
			logger.error("Invalid mobile number {}", mobileNumber);
			throw new SMSRunTimeException("Invalid mobile number");
		}
		return true;
	}

}
