package com.lernen.cloud.sms.handler;

import com.embrate.cloud.core.api.service.communication.CommunicationServiceTransactionType;
import com.lernen.cloud.core.api.common.DeliveryMode;
import com.lernen.cloud.core.api.configurations.MetaDataPreferences;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.notification.NotificationType;
import com.lernen.cloud.core.api.sms.SMSContentPayload;
import com.lernen.cloud.core.api.sms.SMSPayloadWrapper;
import com.lernen.cloud.core.api.sms.SMSPreferences;
import com.lernen.cloud.core.api.sms.SMSResponse;
import com.lernen.cloud.core.api.sms.msg91.UserSMSPayload;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.api.user.authentication.RegeneratePasswordPayload;
import com.lernen.cloud.core.api.user.authentication.RegeneratePasswordUserData;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.managers.UserManager;
import com.lernen.cloud.core.utils.Pair;
import com.lernen.cloud.core.utils.PhoneNumberUtils;
import com.lernen.cloud.sms.content.builder.RegenerateUserCredentialSMSContentBuilder;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

/**
 * 
 * <AUTHOR>
 *
 */
public class RegenerateUserCredentialSMSHandler {
	private static final Logger logger = LogManager.getLogger(RegenerateUserCredentialSMSHandler.class);

	private final SMSManager smsManager;
	private final UserManager userManager;
	private final UserPreferenceSettings userPreferenceSettings;
	private final RegenerateUserCredentialSMSContentBuilder regenerateUserCredentialSMSContentBuilder;

	public RegenerateUserCredentialSMSHandler(SMSManager smsManager, UserManager userManager,
			UserPreferenceSettings userPreferenceSettings,
			RegenerateUserCredentialSMSContentBuilder regenerateUserCredentialSMSContentBuilder) {
		this.smsManager = smsManager;
		this.userManager = userManager;
		this.userPreferenceSettings = userPreferenceSettings;
		this.regenerateUserCredentialSMSContentBuilder = regenerateUserCredentialSMSContentBuilder;
	}

	public SMSResponse regenerateUserPassword(final int instituteId, Integer academicSessionId, UUID userId,
			RegeneratePasswordPayload regeneratePasswordPayload) {

		userManager.validateRegeneratePasswordPayload(instituteId, userId, regeneratePasswordPayload);

		if (regeneratePasswordPayload.getDeliveryMode() != DeliveryMode.SMS) {
			logger.error("Only SMS channel is supported, given channel = {}",
					regeneratePasswordPayload.getDeliveryMode());
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
					"Only SMS channel is supported to communicate new credentials"));
		}

		MetaDataPreferences metaDataPreferences = userPreferenceSettings.getMetaDataPreferences(instituteId);
		SMSPreferences smsPreferences = userPreferenceSettings.getSMSPreferences(instituteId);

		if (!metaDataPreferences.isSmsServiceEnabled()) {
			logger.info("SMS service is not enabled for institute {}", instituteId);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
					"SMS service is not enabled for your institute. Please contact support team for enabling this feature."));
		}
		String instituteNameInSMS = metaDataPreferences.getInstituteNameInSMS();
		if (StringUtils.isBlank(instituteNameInSMS)) {
			logger.error("Institute name is not setup for sms sending {}", instituteId);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
					"Institute name is not setup for sms sending"));
		}

		switch (regeneratePasswordPayload.getUserType()) {
		case STUDENT:
			final List<RegeneratePasswordUserData<Student>> regeneratePasswordUserDataList = userManager
					.getStudentUsers(instituteId, regeneratePasswordPayload);
			if(!regeneratePasswordPayload.isSendCommunication()) {
				if(updateUserPassword(regeneratePasswordUserDataList)){
					return SMSResponse.successResponse(0,0);
				}
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Failed to reset credentials. Please try again."));
			}

			final List<RegeneratePasswordUserData<Student>> filteredRegeneratePasswordUserDataList = filterValidStudentUserContactNumber(regeneratePasswordUserDataList);
			SMSResponse smsResponse = smsManager.sendSMSAsync(instituteId,
					getSMSPayloadBuilder(academicSessionId, regeneratePasswordPayload.getUserType(),
							filteredRegeneratePasswordUserDataList, smsPreferences, instituteNameInSMS,
							regeneratePasswordPayload.getBatchName(), true),
					userId);
			if (!smsResponse.isSuccess()) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, smsResponse.getFailureReason()));
			}
			
			return smsResponse;
			
		default:
			logger.error("Invalid user type to regenerate passwords {}", regeneratePasswordPayload.getUserType());
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
					"Invalid user type to regenerate passwords"));
		}

	}

	private <T> ISMSPayloadBuilder getSMSPayloadBuilder(Integer academicSessionId, UserType userType,
			List<RegeneratePasswordUserData<T>> regeneratePasswordUserDataList, SMSPreferences smsPreferences,
			String instituteName, String batchName, boolean useTemplate) {
		return new ISMSPayloadBuilder() {

			@Override
			public boolean previewPayloadIsFinal() {
				return true;
			}

			@Override
			public SMSPayloadWrapper getSMSPreviewPayload() {
				return getStudentSMSPayloadWrapper(academicSessionId, userType, regeneratePasswordUserDataList,
						smsPreferences, instituteName, batchName, useTemplate);
			}

			@Override
			public List<UserSMSPayload> getFinalUserSMSPayload() {
				return null;
			}

			@Override
			public boolean executeSMSAction() {
				return updateUserPassword(regeneratePasswordUserDataList);
			}
		};
	}

	private <T> boolean updateUserPassword(List<RegeneratePasswordUserData<T>> regeneratePasswordUserDataList){
		Map<UUID, Pair<String, String>> userNamePasswordMap = new HashMap<>();
		for (RegeneratePasswordUserData<T> regeneratePasswordUserData : regeneratePasswordUserDataList) {
			userNamePasswordMap.put(regeneratePasswordUserData.getUser().getUuid(), new Pair<>(regeneratePasswordUserData.getNewUserName(), regeneratePasswordUserData.getNewPasswordHash()));
		}

		return userManager.updateUserNameAndPassword(userNamePasswordMap);
	}

	private <T> SMSPayloadWrapper getStudentSMSPayloadWrapper(Integer academicSessionId, UserType userType,
			List<RegeneratePasswordUserData<T>> regeneratePasswordUserDataList, SMSPreferences smsPreferences,
			String instituteName, String batchName, boolean useTemplate) {

		List<UserSMSPayload> userSMSPayloadList = new ArrayList<>();
		for (RegeneratePasswordUserData<T> regeneratePasswordUserData : regeneratePasswordUserDataList) {
			User user = regeneratePasswordUserData.getUser();
			SMSContentPayload userCredText = regenerateUserCredentialSMSContentBuilder.generateTextContent(userType,
					regeneratePasswordUserData, smsPreferences, instituteName, useTemplate);

			switch (userType) {
			case STUDENT:
				Student student = (Student) regeneratePasswordUserData.getUserEntity();
				String contactNumber = student.getStudentBasicInfo().getPrimaryContactNumber();
				userSMSPayloadList
						.add(new UserSMSPayload(user.getUuid(), Arrays.asList(contactNumber), userCredText.getContent(), null, null, userCredText.getDltTemplateId()));
				break;
			default:
				break;
			}
		}

		UUID batchId = UUID.randomUUID();
		return new SMSPayloadWrapper(academicSessionId, userType, NotificationType.RENEW_CREDENTIALS,
				CommunicationServiceTransactionType.RENEW_CREDENTIALS, userSMSPayloadList, batchId, batchName);
	}

	private List<RegeneratePasswordUserData<Student>> filterValidStudentUserContactNumber(
			final List<RegeneratePasswordUserData<Student>> regeneratePasswordUserDataList) {
		List<RegeneratePasswordUserData<Student>> filterList = new ArrayList<>();
		for (final RegeneratePasswordUserData<Student> userEntry : regeneratePasswordUserDataList) {
			final Student student = userEntry.getUserEntity();
			if (StringUtils.isBlank(student.getStudentBasicInfo().getPrimaryContactNumber())
					|| !PhoneNumberUtils.isValidNumber(student.getStudentBasicInfo().getPrimaryContactNumber(),
							PhoneNumberUtils.DEFAULT_COUNTRY)) {
				logger.error("Invalid contact number for user {}. Skipping", student.getStudentId());
			}else{
				filterList.add(userEntry);
			}
		}
		return filterList;
	}

}
