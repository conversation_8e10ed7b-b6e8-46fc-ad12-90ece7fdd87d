package com.lernen.cloud.core.lib.tracking.events;

import java.util.List;

import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.tracking.events.TrackingEventModuleCount;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.lernen.cloud.core.api.common.Channel;
import com.lernen.cloud.core.api.tracking.events.TrackingEventData;
import com.lernen.cloud.core.api.tracking.events.TrackingEventName;
import com.lernen.cloud.core.api.tracking.events.TrackingEventPayload;
import com.lernen.cloud.dao.tier.tracking.events.TrackingEventsDao;

/**
 *
 * <AUTHOR>
 *
 */
public class TrackingEventsManager {

	private static final Logger logger = LogManager.getLogger(TrackingEventsManager.class);

	private final TrackingEventsDao trackingEventsDao;

	public TrackingEventsManager(TrackingEventsDao trackingEventsDao) {
		this.trackingEventsDao = trackingEventsDao;
	}

	public boolean addTrackingEvent(TrackingEventPayload trackingEventPayload) {
		if (trackingEventPayload == null || trackingEventPayload.getChannel() == null
				|| trackingEventPayload.getTrackingEventName() == null || trackingEventPayload.getEventTime() == null
				|| trackingEventPayload.getEventTime() <= 0) {
			logger.error("Invalid tracking payload {}. Skipping...", trackingEventPayload);
			return false;
		}
		return trackingEventsDao.addTrackingEvent(trackingEventPayload);
	}

	public List<TrackingEventData> getTrackingEvents(Channel channel, int start, int end) {
		return trackingEventsDao.getTrackingEvents(channel, start, end);
	}

	public List<TrackingEventData> getTrackingEvents(int instituteId, Channel channel, int start, int end) {
		return trackingEventsDao.getTrackingEvents(instituteId, channel, start, end);
	}

	public List<TrackingEventData> getTrackingEvents(Channel channel, TrackingEventName trackingEventName, int start,
			int end) {
		return trackingEventsDao.getTrackingEvents(channel, trackingEventName, start, end);
	}

	public List<TrackingEventData> getTrackingEvents(int instituteId, Channel channel,
			TrackingEventName trackingEventName, int start, int end) {
		return trackingEventsDao.getTrackingEvents(instituteId, channel, trackingEventName, start, end);
	}

	public List<TrackingEventModuleCount> getTrackingEventModuleUsageCount(int instituteId, int startDate, int endDate) {
		if(instituteId <= 0){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		if(startDate <= 0){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Please ensure the start Date field is correctly formatted and not empty"));
		}
		if(endDate <= 0){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Please ensure the end Date field is correctly formatted and not empty."));
		}
		if(startDate > endDate){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Please ensure the start date occurs before the end date."));
		}
		return trackingEventsDao.getTrackingEventModuleUsageCount(instituteId, startDate, endDate);
	}
}
