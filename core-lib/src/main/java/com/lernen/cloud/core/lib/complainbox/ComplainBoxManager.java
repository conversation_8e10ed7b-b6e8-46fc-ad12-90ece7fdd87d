package com.lernen.cloud.core.lib.complainbox;

import com.lernen.cloud.core.api.complainbox.*;
import com.lernen.cloud.core.api.diary.DatewiseDiaryMetadataDetails;
import com.lernen.cloud.core.api.diary.DiaryRemarkMetadata;
import com.lernen.cloud.core.api.diary.StaffDiaryRemarkDetails;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.staff.StaffLite;
import com.lernen.cloud.core.api.staff.StaffStatus;
import com.lernen.cloud.core.api.student.DownloadDocumentWrapper;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.lib.staff.StaffManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import com.embrate.cloud.core.api.complainbox.ComplainDocumentType;
import com.lernen.cloud.core.api.common.FileData;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.core.lib.document.DocumentManager;
import com.lernen.cloud.core.lib.managers.UserManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.utils.DocumentUtils;
import com.lernen.cloud.dao.tier.complainbox.ComplainBoxDao;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserLite;

import java.util.*;

public class ComplainBoxManager {
    private static final Logger logger = LogManager.getLogger(ComplainBoxManager.class);
    private final UserPermissionManager userPermissionManager;
    private final ComplainBoxDao complainBoxDao;
    private final TransactionTemplate transactionTemplate;
    private final DocumentManager documentManager;
    private final UserManager userManager;
    private final StaffManager staffManager;
    private final StudentManager studentManager;
    private static final String COMPLAIN_DOCUMENT_DIRECTORY_NAME = "complaint_box";

     public ComplainBoxManager(UserPermissionManager userPermissionManager,ComplainBoxDao complainBoxDao, TransactionTemplate transactionTemplate, DocumentManager documentManager, UserManager userManager,
                               StaffManager staffManager, StudentManager studentManager) {
        this.userPermissionManager = userPermissionManager;
        this.complainBoxDao = complainBoxDao;
        this.transactionTemplate = transactionTemplate;
        this.documentManager = documentManager;
        this.userManager = userManager;
        this.staffManager = staffManager;
        this.studentManager = studentManager;
     }

     public boolean addComplainCategory(ComplainCategoryPayload categoryPayload,int instituteId, UUID userId, boolean verifyAuthorisation) {
        if (instituteId <= 0) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD,
                    "Invalid institute id."));
        }
        
        if (userId == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD,
                    "Invalid user Id."));
        }
		if(verifyAuthorisation){
			userPermissionManager.verifyAuthorisation(instituteId, userId,
					AuthorisationRequiredAction.COMPLAIN_BOX_CATEGORY_TYPE);
		}
        validateComplainBoxCategory(categoryPayload,instituteId,false);
        if (getComplainCategoryByName(categoryPayload) != null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD,
                    "Complain Category already exists for this name."));
        }
        return complainBoxDao.addComplainCategory(categoryPayload,userId);
    }

     private ComplainCategoryPayload getComplainCategoryByName(ComplainCategoryPayload categoryPayload) {
        return complainBoxDao.getComplainCategoryByName(categoryPayload);
    }

    private void validateComplainBoxCategory(ComplainCategoryPayload categoryPayload,int instituteId,boolean updateComplainCategory) {
        if (categoryPayload == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_CATEGORY_TYPE, "Invalid Complain Category."));
        }
        if (instituteId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_CATEGORY_TYPE, "Invalid institute id."));
        }

        if (StringUtils.isEmpty(categoryPayload.getCategoryName())){
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_CATEGORY_TYPE, "Invalid Complain Category Name."));
        }
        if(updateComplainCategory) {
            if (categoryPayload.getCategoryId() <= 0) {
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_CATEGORY_TYPE, "Invalid Complain category type id."));
            }
        }
    }

    public List<ComplainCategoryPayload> getComplainCategory(int instituteId) {
        if (instituteId <= 0) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD,
                    "Invalid institute id."));
        }
        
        return complainBoxDao.getComplainCategory(instituteId);
    }


    public boolean updateComplainCategory(ComplainCategoryPayload categoryPayload,int instituteId, UUID userId) {
        if (instituteId <= 0) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD,
                    "Invalid institute id."));
        }
        
        if (userId == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD,
                    "Invalid user Id."));
        }
        userPermissionManager.verifyAuthorisation(instituteId, userId,
                AuthorisationRequiredAction.COMPLAIN_BOX_CATEGORY_TYPE);
        validateComplainBoxCategory(categoryPayload,instituteId,true);
        ComplainCategoryPayload existingComplainCategory = getComplainCategoryById(categoryPayload.getInstituteId(), categoryPayload.getCategoryId());
        if (existingComplainCategory == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD,
                    "Complain Category does not exist"));
        }
        return complainBoxDao.updateComplainCategory(categoryPayload,userId);
    }

     public boolean deleteComplainCategory(int instituteId,int categoryId, UUID userId) {
        if (instituteId <= 0) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD,
                    "Invalid institute id."));
        }
       
        if (userId == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD,
                    "Invalid user Id."));
        }
        userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.COMPLAIN_BOX_CATEGORY_TYPE);

         List<StandardResponseDetails> standardResponseDetailsList = complainBoxDao.getStandardResponseDetails(instituteId,categoryId);

        List<StudentComplaintMetadataPayload> complainMetaDataPayload = complainBoxDao.getComplainMetaDataById(instituteId,categoryId);
        if(!CollectionUtils.isEmpty(complainMetaDataPayload) || !CollectionUtils.isEmpty(standardResponseDetailsList)) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD,
                    "Cannot delete complain category as it is being used in standard response or in a complaint."));
            
        }
        return complainBoxDao.deleteComplainCategory(instituteId,categoryId);
    }

    public ComplainCategoryPayload getComplainCategoryById(int instituteId, int categoryId) {
        return complainBoxDao.getComplainCategoryById(instituteId,categoryId);
    }

    // standard response

    public boolean addStandardResponse(StandardComplainResponse responsePayload, int instituteId, UUID userId){
        if (instituteId <= 0) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD,
                    "Invalid institute id."));
        }
        if (userId == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD,
                    "Invalid user Id."));
        }
        userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.COMPLAIN_BOX_STANDARD_RESPONSE);

        if (responsePayload.getCategoryId() <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD, "Invalid Standard Response Category id."));
        }
        return complainBoxDao.addStandardResponse(responsePayload,userId);
    }

    public boolean updateStandardResponse(StandardComplainResponse responsePayload, int instituteId,  UUID userId){
        if (instituteId <= 0) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD,
                    "Invalid institute id."));
        }
        if (userId == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD,
                    "Invalid user Id."));
        }
       
        userPermissionManager.verifyAuthorisation(instituteId, userId,
                AuthorisationRequiredAction.COMPLAIN_BOX_STANDARD_RESPONSE);
       
        if (responsePayload.getStandardResponseId() == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD, "Invalid StandardRemar Remark id."));
        }
       
        return complainBoxDao.updateStandardResponse(responsePayload,userId);
    }

    public boolean deleteStandardResponse(UUID responseId,int instituteId, UUID userId) {
        if (instituteId <= 0) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD,
                    "Invalid institute id."));
        }
        if (userId == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD,
                    "Invalid user Id."));
        }
        if(responseId == null){
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD,
                    "Invalid response Id."));
        }
        userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.COMPLAIN_BOX_STANDARD_RESPONSE);
        
        return complainBoxDao.deleteStandardResponse(responseId,instituteId);
    }

    public List<StandardResponseDetails> getStandardResponseDetails(int instituteId, int categoryId){
        if (instituteId <= 0) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD,
                    "Invalid institute id."));
        }

        List<StandardResponseDetails> standardResponseDetails =  complainBoxDao.getStandardResponseDetails(instituteId,categoryId);

        return standardResponseDetails;
    }

    // complain metadata

    public UUID addComplainMetadata(StudentComplaintMetadataPayload studentComplainPayload, int instituteId, UUID userId, List<FileData> attachments){
        if (instituteId <= 0) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD,
                    "Invalid institute id."));
        }
        if (userId == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD,
                    "Invalid user Id."));
        }

        validateComplainPayload(studentComplainPayload);
        final UUID complainId = transactionTemplate.execute(new TransactionCallback<UUID>() {
			@Override
			public UUID doInTransaction(TransactionStatus status) {
				

				UUID complainId = complainBoxDao.addComplainMetadata(studentComplainPayload, userId);
				if (complainId == null) {
					return null;
				}
				if (!CollectionUtils.isEmpty(attachments)) {
					if(!uploadComplainAttachments(
							instituteId, complainId, userId, attachments, ComplainDocumentType.COMPLAIN_ATTACHMENTS)) {
						throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD,
								"Error occured while uploading complain attachments. Please try again."));
					}
				}
				return complainId;
			}
		});
        return complainId;
    }

    private void validateComplainPayload(StudentComplaintMetadataPayload studentComplainPayload) {
        if (studentComplainPayload == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD, "Invalid Student Complain Payload."));
        }
		
		if (studentComplainPayload.getInstituteId() <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		
		
		if (studentComplainPayload.getStatus() == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD, "Invalid complain status."));
		}

		if (studentComplainPayload.getCategoryId() <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD, "Invalid complain category."));
		}

		if (StringUtils.isBlank(studentComplainPayload.getTitle())) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD, "Invalid title."));
		}
		
		if (studentComplainPayload.getCreatedBy() == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD, "Invalid created by Id."));
		}
		
	}


    private boolean uploadComplainAttachments(int instituteId, UUID complainId, UUID userId, List<FileData> attachments, 
         ComplainDocumentType complainDocumentType) {
		try {
			final List<Document<ComplainDocumentType>> complainDocumentsUploaded = uploadDocument(
					instituteId, complainId, userId, complainDocumentType, attachments);
			if (CollectionUtils.isEmpty(complainDocumentsUploaded)) {
				logger.error("Unable to upload attachments for complain {}", complainId);
				return false;
			}
			return true;
		} catch (final Exception e) {
			logger.error("Error while uploading attachments for complain {}", complainId, e);
		}
		return false;
	}

    public List<Document<ComplainDocumentType>> uploadDocument(int instituteId, UUID entityId, UUID userId,
    ComplainDocumentType complainDocumentType, List<FileData> attachments) {
		
			return uploadComplainDocument(instituteId, entityId, userId, complainDocumentType, attachments);
	
			
	}

    public List<Document<ComplainDocumentType>> uploadComplainDocument(int instituteId, UUID entityId, UUID userId,
    ComplainDocumentType complainDocumentType, List<FileData> attachments) {
		List<Document<ComplainDocumentType>> finalComplainAttachments = new ArrayList<Document<ComplainDocumentType>>(); 
		for(FileData attachment : attachments) {
			final UUID documentId = UUID.randomUUID();
			final String fileExtension = FilenameUtils.getExtension(attachment.getFileName());
			final Document<ComplainDocumentType> newDocument = new Document<>(complainDocumentType, 
					FilenameUtils.getBaseName(attachment.getFileName()), documentId,
					fileExtension, (int) (System.currentTimeMillis() / 1000l));
			final String s3Path = buildComplainDocumentPath(instituteId, entityId, newDocument);
			final boolean documentUploaded = documentManager.uploadDocument(s3Path, instituteId, attachment);
			if (!documentUploaded) {
				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT,
						"Unable to upload document. Please try again"));
			}
			finalComplainAttachments.add(newDocument);
		}
		return complainBoxDao.updateDocuments(entityId, 
				CollectionUtils.isEmpty(finalComplainAttachments) ? null : finalComplainAttachments) ? finalComplainAttachments : null;

	}

    private String buildComplainDocumentPath(int instituteId, UUID complaintId,
			Document<ComplainDocumentType> complainDocument) {
		final StringBuilder s3Path = new StringBuilder();
		s3Path.append("institute_id=").append(instituteId).append(DocumentUtils.S3_FILE_PATH_DELIMITER)
			.append(COMPLAIN_DOCUMENT_DIRECTORY_NAME).append(DocumentUtils.S3_FILE_PATH_DELIMITER)
			.append("complain_id=").append(complaintId).append(DocumentUtils.S3_FILE_PATH_DELIMITER)
			.append(complainDocument.getDocumentId()).append(".").append(complainDocument.getFileExtension());
		return s3Path.toString();
	}

    public List<Document<ComplainDocumentType>> deleteDocument(int instituteId, StudentComplaintMetadataPayload studentComplainPayload, UUID userId,
        ComplainDocumentType complainDocumentType, UUID documentId) {
		
			return deleteComplainDocument(instituteId, studentComplainPayload, userId, complainDocumentType, documentId);
		
	}

    private List<Document<ComplainDocumentType>> deleteComplainDocument(int instituteId, StudentComplaintMetadataPayload studentComplainPayload, UUID userId,
            ComplainDocumentType complainDocumentType, UUID documentId) {
		if (studentComplainPayload.getComplainId() == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD, "complain does not exists"));
		}

		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid document Information"));
		}
		
		final UUID complainId = studentComplainPayload.getComplainId();
		final List<Document<ComplainDocumentType>> complainAttachments = studentComplainPayload.getAttachments();
		if (CollectionUtils.isEmpty(complainAttachments)) {
			return null;
		}
		
		List<Document<ComplainDocumentType>> finalComplainAttachments = complainAttachments;
		final Iterator<Document<ComplainDocumentType>> iterator = complainAttachments.iterator();
		boolean deleted = false;
		if(documentId == null) {
			while (iterator.hasNext()) {
				final Document<ComplainDocumentType> complainDocuments = iterator.next();
				final String s3Path = buildComplainDocumentPath(instituteId, complainId, complainDocuments);
				if (documentManager.deleteDocument(instituteId, s3Path)) {
					iterator.remove();
					deleted = true;
				}
			}
			if(deleted) {
				finalComplainAttachments = null;
			}
		}
		else {
			while (iterator.hasNext()) {
				final Document<ComplainDocumentType> complainDocuments = iterator.next();
				final String s3Path = buildComplainDocumentPath(instituteId, complainId, complainDocuments);
				if(complainDocuments.getDocumentId().equals(documentId)) {
					if (documentManager.deleteDocument(instituteId, s3Path)) {
						iterator.remove();
						deleted = true;
					}
				}
			}
			if(deleted) {
				finalComplainAttachments = new ArrayList<Document<ComplainDocumentType>>();
				for(Document<ComplainDocumentType> document : complainAttachments) {
					if(document.getDocumentId() != documentId) {
						finalComplainAttachments.add(document);
					}
				}
			}
		}
		
		if (!deleted) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Document does not exists"));
		}
		
		return complainBoxDao.updateDocuments(complainId, CollectionUtils.isEmpty(finalComplainAttachments) ? null : finalComplainAttachments) ? finalComplainAttachments : null;
       
	}
	


    public boolean updateComplainStatus(UUID complainId, int instituteId, UUID userId, ComplainStatus complainStatus){
        if (instituteId <= 0) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD,
                    "Invalid institute id."));
        }

        if (userId == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD,
                    "Invalid user Id."));
        }

        if (complainStatus == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD,
                    "Invalid complain Status."));
        }

        return complainBoxDao.updateComplainStatus(complainId, instituteId, userId, complainStatus);
    }

     public List<StudentComplaintMetadataDetails> getComplainDetails(int instituteId, int categoryId, UUID studentId,
            Set<ComplainStatus> complainStatusSet, int limit, int offset){
            if (instituteId <= 0) {
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD,
                        "Invalid institute id."));
            }

            List<StudentComplaintMetadataDetails> complainDetails =  complainBoxDao.getComplainDetails(
                    instituteId, categoryId, studentId, complainStatusSet, limit, offset);

            if(CollectionUtils.isEmpty(complainDetails)) {
                return complainDetails;
            }

            Set<UUID> userIdSet = new HashSet<>();
            Set<UUID> staffIdSet = new HashSet<>();
            Set<UUID> studentIdSet = new HashSet<>();
             for(StudentComplaintMetadataDetails studentComplaintMetadataDetails : complainDetails) {
                 UUID createdBy = studentComplaintMetadataDetails.getCreatedBy();
                 if(createdBy != null) {
                     userIdSet.add(createdBy);
                     studentIdSet.add(createdBy);
                 }
                 UUID closedBy = studentComplaintMetadataDetails.getClosedBy();
                 if(closedBy != null) {
                     userIdSet.add(closedBy);
                 }
                 UUID complainFor = studentComplaintMetadataDetails.getComplainFor();
                 if(complainFor != null) {
                     staffIdSet.add(complainFor);
                 }
             }


            Map<UUID, UserLite> userLiteMap = getUserMap(userManager.getUsers(instituteId, userIdSet));
            Map<UUID, StaffLite> staffLiteMap = getStaffMap(staffManager.getStaffs(instituteId, staffIdSet));
            Map<UUID, StudentLite> studentLiteMap = getStudentLiteMap(studentManager.getStudentsWithoutSession(new ArrayList<>(studentIdSet)));

            for(StudentComplaintMetadataDetails studentComplaintMetadataDetails : complainDetails) {
                studentComplaintMetadataDetails.setCreatedByUser(userLiteMap.get(
                        studentComplaintMetadataDetails.getCreatedBy() == null ? null : studentComplaintMetadataDetails.getCreatedBy()));
                studentComplaintMetadataDetails.setCreatedByStudent(studentLiteMap.get(
                        studentComplaintMetadataDetails.getCreatedBy() == null ? null : studentComplaintMetadataDetails.getCreatedBy()));
                studentComplaintMetadataDetails.setClosedByUser(userLiteMap.get(
                        studentComplaintMetadataDetails.getClosedBy() == null ? null : studentComplaintMetadataDetails.getClosedBy()));
                studentComplaintMetadataDetails.setComplainForUser(staffLiteMap.get(
                        studentComplaintMetadataDetails.getComplainFor() == null ? null : studentComplaintMetadataDetails.getComplainFor()));
            }
             complainDetails.sort(new Comparator<StudentComplaintMetadataDetails>() {
                @Override
                public int compare(StudentComplaintMetadataDetails s1, StudentComplaintMetadataDetails s2) {
                    return s2.getCreatedAt() - s1.getCreatedAt();
                }
            });

            return  complainDetails;
    }

    private Map<UUID, UserLite> getUserMap(List<User> userIdList) {
        Map<UUID, UserLite> userLiteMap = new HashMap<>();
        for(User user : userIdList) {
            userLiteMap.put(user.getUuid(), User.getUserLite(user));
        }
        return userLiteMap;
    }

    private Map<UUID, StaffLite> getStaffMap(List<Staff> staffLiteList) {
        Map<UUID, StaffLite> userLiteMap = new HashMap<>();
        for(Staff staff : staffLiteList) {
            userLiteMap.put(staff.getStaffId(), Staff.getStaffLite(staff));
        }
        return userLiteMap;
    }

    private Map<UUID, StudentLite> getStudentLiteMap(List<Student> studentList) {
        Map<UUID, StudentLite> userLiteMap = new HashMap<>();
        for(Student student : studentList) {
            userLiteMap.put(student.getStudentId(), Student.getStudentLite(student));
        }
        return userLiteMap;
    }

    public StudentComplaintMetadataDetails getComplainDetailsByComplainId(UUID complainId, int instituteId){
        if (instituteId <= 0) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD,
                    "Invalid institute id."));
        }
        if (complainId == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD,
                    "Invalid complain id."));
        }

        StudentComplaintMetadataDetails complainDetails =  complainBoxDao.getComplainDetailsByComplainId(complainId, instituteId);

        if(complainDetails == null) {
            return null;
        }

        Set<UUID> userIdSet = new HashSet<>();
        Set<UUID> staffIdSet = new HashSet<>();
        Set<UUID> studentIdSet = new HashSet<>();
        UUID createdBy = complainDetails.getCreatedBy();
        if(createdBy != null) {
            userIdSet.add(createdBy);
            studentIdSet.add(createdBy);
        }
        UUID closedBy = complainDetails.getClosedBy();
        if(closedBy != null) {
            userIdSet.add(closedBy);
        }
        UUID complainFor = complainDetails.getComplainFor();
        if(complainFor != null) {
            staffIdSet.add(complainFor);
        }

        Map<UUID, UserLite> userLiteMap = getUserMap(userManager.getUsers(instituteId, userIdSet));
        Map<UUID, StaffLite> staffLiteMap = getStaffMap(staffManager.getStaffs(instituteId, staffIdSet));
        Map<UUID, StudentLite> studentLiteMap = getStudentLiteMap(studentManager.getStudentsWithoutSession(new ArrayList<>(studentIdSet)));

        complainDetails.setCreatedByUser(createdBy == null ? null : userLiteMap.get(createdBy));
        complainDetails.setCreatedByStudent(createdBy == null ? null : studentLiteMap.get(createdBy));
        complainDetails.setClosedByUser(closedBy == null ? null : userLiteMap.get(closedBy));
        complainDetails.setComplainForUser(complainFor == null ? null : staffLiteMap.get(complainFor));

       return complainDetails;
    }

    // complain responses

    public boolean addComplainResponse(int instituteId, StudentComplaintResponses responsePayload, UUID userId) {
        if (instituteId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
        }
        if (userId == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD,
                    "Invalid user Id."));
        }

        validateComplainResponsePayload(responsePayload);
        return complainBoxDao.addComplainResponse(responsePayload, userId);
    }

    private void validateComplainResponsePayload(StudentComplaintResponses responsePayload) {
		if (responsePayload == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD, "Invalid complain response payload."));
		}

		if (responsePayload.getComplainId() == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD, "Invalid complain Id."));
		}

		if (StringUtils.isBlank(responsePayload.getResponse())) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD, "Invalid response."));
		}
		
	}

    public boolean deleteComplainResponse(UUID responseId, UUID userId) {

        if (userId == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD,
                    "Invalid user Id."));
        }

        if (responseId == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD,
                    "Invalid user Id."));
        }

        return complainBoxDao.deleteComplainResponse(responseId);
    }

    public List<StudentComplaintResponses> getComplainResponse(UUID complainId) {
        if (complainId == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD,
                    "Invalid complain id."));
        }
        
        List<StudentComplaintResponses> responseslist = complainBoxDao.getComplainResponse(complainId);
        if(CollectionUtils.isEmpty(responseslist)) {
            return null;
        }
        Collections.sort(responseslist, getStudentComparator());

        return responseslist;
    }

    private Comparator<StudentComplaintResponses> getStudentComparator() {
        return new Comparator<StudentComplaintResponses>() {
            @Override
            public int compare(StudentComplaintResponses r1, StudentComplaintResponses r2) {
                return r2.getResponseAt() - r1.getResponseAt();
            }
        };
    }    


    public StudentComplaintDetails getComplainDetails(UUID complainId, int instituteId) {
        if (instituteId <= 0) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE,
                    "Invalid institute id."));
        }
        if (complainId == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COMPLAIN_PAYLOAD,
                    "Invalid complain id."));
        }
        StudentComplaintMetadataDetails complainDetails = getComplainDetailsByComplainId(complainId, instituteId);
        List<StudentComplaintResponses> responseList = getComplainResponse(complainId);
        return new StudentComplaintDetails(complainDetails, responseList);
    }

    public DownloadDocumentWrapper<Document<ComplainDocumentType>> downloadStudentComplaintDocument(int instituteId, UUID documentId, UUID complaintId) {
        if (documentId == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Invalid document Information"));
        }
        final StudentComplaintDetails studentComplaintDetails = getComplainDetails(complaintId, instituteId);
        if (studentComplaintDetails == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Complaint details does not exists"));
        }

        final List<Document<ComplainDocumentType>> complaintAttachments = studentComplaintDetails.getStudentComplaintMetadata().getAttachments();
        if (org.springframework.util.CollectionUtils.isEmpty(complaintAttachments)) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Document does not exists"));
        }
        for (final Document<ComplainDocumentType> complaintAttachment : complaintAttachments) {
            if (documentId.equals(complaintAttachment.getDocumentId())) {
                final String s3Path = buildComplainDocumentPath(instituteId, complaintId, complaintAttachment);
                return new DownloadDocumentWrapper<>(complaintAttachment, documentManager.downloadDocument(instituteId, s3Path));
            }
        }
        return null;
    }

    public InstituteComplaintCategoryStaffDetails getInstituteComplaintCategoryStaffDetails(int instituteId) {
        if (instituteId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id"));
        }
        return new InstituteComplaintCategoryStaffDetails(getComplainCategory(instituteId),
                staffManager.getStaff(instituteId, StaffStatus.ONBOARD, null));
    }

    public List<DatewiseStudentComplaintMetadataDetails> getDatewiseComplainDetails(int instituteId, int categoryId, UUID studentId,
                                                                    Set<ComplainStatus> complainStatusSet, int limit, int offset){
        List<StudentComplaintMetadataDetails> studentComplaintMetadataDetailsList =  getComplainDetails(
                instituteId, categoryId, studentId, complainStatusSet, limit, offset);

        Map<Integer, List<StudentComplaintMetadataDetails>> dateStudentComplaintMetadataMap = new HashMap<>();
        for (StudentComplaintMetadataDetails studentComplaintMetadataDetails : studentComplaintMetadataDetailsList) {
            Integer createdTimestamp = studentComplaintMetadataDetails.getCreatedAt();
            if (createdTimestamp == null) {
                continue;
            }
            createdTimestamp = DateUtils.getDayStart(createdTimestamp, DateUtils.DEFAULT_TIMEZONE);
            if (!dateStudentComplaintMetadataMap.containsKey(createdTimestamp)) {
                dateStudentComplaintMetadataMap.put(createdTimestamp, new ArrayList<>());
            }
            dateStudentComplaintMetadataMap.get(createdTimestamp).add(studentComplaintMetadataDetails);
        }

        if (CollectionUtils.isEmpty(dateStudentComplaintMetadataMap)) {
            return null;
        }

        List<DatewiseStudentComplaintMetadataDetails> datewiseStudentComplaintMetadataDetailsList = new ArrayList<>();
        for (Map.Entry<Integer, List<StudentComplaintMetadataDetails>> remarkMap : dateStudentComplaintMetadataMap.entrySet()) {
            if (remarkMap == null || remarkMap.getKey() == null || remarkMap.getKey() <= 0) {
                continue;
            }
            Integer date = remarkMap.getKey();
            List<StudentComplaintMetadataDetails> studentComplaintMetadataDetailsList1 = CollectionUtils.isEmpty(remarkMap.getValue()) ? new ArrayList<>() :
                    remarkMap.getValue();
            datewiseStudentComplaintMetadataDetailsList.add(new DatewiseStudentComplaintMetadataDetails(date, studentComplaintMetadataDetailsList1));
        }

        Collections.sort(datewiseStudentComplaintMetadataDetailsList);

        return datewiseStudentComplaintMetadataDetailsList;
    }
}
