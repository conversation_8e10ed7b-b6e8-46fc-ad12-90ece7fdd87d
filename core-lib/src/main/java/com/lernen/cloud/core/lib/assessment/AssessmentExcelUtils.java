package com.lernen.cloud.core.lib.assessment;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.lernen.cloud.core.api.CloneStatus;
import com.lernen.cloud.core.api.assessment.ExcelFileAssessmentValidationDetails;
import com.lernen.cloud.core.api.assessment.QuestionBank;
import com.lernen.cloud.core.api.assessment.QuestionLevel;
import com.lernen.cloud.core.api.assessment.QuestionOption;
import com.lernen.cloud.core.api.assessment.QuestionType;
import com.lernen.cloud.core.api.common.FileData;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.notification.ExcelFileValidationDetails;
import com.lernen.cloud.core.api.report.ReportOutput;
import com.lernen.cloud.core.lib.document.DocumentManager;
import com.lernen.cloud.core.utils.CharsetUtils;
import com.lernen.cloud.core.utils.DocumentUtils;

public class AssessmentExcelUtils {
     private static final Logger logger = LogManager.getLogger(AssessmentExcelUtils.class);
    private static final int COLUMN_WIDTH_PADDING = 4;
    private static final String COMMUNICATION_DIRECTORY_NAME = "assessment";
    private static final String EXCEL_DIRECTORY_NAME = "excel";

    private final DocumentManager documentManager;
    
    public AssessmentExcelUtils (DocumentManager documentManager) {
        this.documentManager = documentManager;
    }

    /**
     *
     * @param instituteId
     * @param templateId
     * @return
     */
    public ReportOutput downloadExcelTemplate(int instituteId) {
        if(instituteId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id"));
        }
        return generateExcelTemplate();
    }

    /**
     *
     * @param 
     * @return
     */
    public ReportOutput generateExcelTemplate() {
        try {
            final Workbook workbook = new HSSFWorkbook();
            final List<Integer> columnWidths = new ArrayList<>();
            final List<String> headingDataList = Arrays.asList(
                "Question *", 
                "Number Of Option *", 
                "Option 1", 
                "Option 2", 
                "Option 3", 
                "Option 4", 
                "Correct Answer (Give The Cell Number Of Option)*", 
                "Marks", 
                "Difficulty Level", 
                "Tags"
            );

            final Font font = workbook.createFont();
            font.setFontHeightInPoints((short) 12);
            font.setColor(IndexedColors.BLACK.getIndex());

            final CellStyle cellStyle = workbook.createCellStyle();
            cellStyle.setFont(font);

            final Sheet sheet = workbook.createSheet("Report");

            final String reportName = "questionBank.xlsx";

            Row row = sheet.createRow(0);
            int cellIndex = 0;
            String value = "Note: 'All star field are mandatary and Difficulty level are EASY, MEDIUM, HARD";
            createCell(row, cellIndex, value, cellStyle);

            final Font headerFont = workbook.createFont();
            headerFont.setFontHeightInPoints((short) 14);
            headerFont.setColor(IndexedColors.BLACK.getIndex());
            headerFont.setBold(true);

            final CellStyle headerCellStyle = workbook.createCellStyle();
            headerCellStyle.setFont(headerFont);


            row = sheet.createRow(1);
            for (String headingData : headingDataList) {
                createCell(row, cellIndex, headingData, headerCellStyle, columnWidths);
                cellIndex++;
            }

            if(columnWidths.size() - 1 != 0) {
                final CellRangeAddress cellRangeAddress = new CellRangeAddress(0, 0, 0, columnWidths.size() - 1);
                sheet.addMergedRegion(cellRangeAddress);
            }

            resizeSheet(sheet, columnWidths);
            return getReportOutput(workbook, reportName);

        } catch (final Exception e) {
            logger.error("Error while generating questionBank report", e);
        }
        return null;
    }

    private void createCell(Row row, int cellIndex, String value, CellStyle cellStyle, List<Integer> columnWidths) {
        Cell cell = row.createCell(cellIndex);
        cell.setCellValue(value);
        cell.setCellStyle(cellStyle);
        columnWidths.add(value.length());
    }

    private void createCell(Row row, int cellIndex, String value, CellStyle cellStyle) {
        Cell cell = row.createCell(cellIndex);
        cell.setCellValue(value);
        cell.setCellStyle(cellStyle);
    }

    private ReportOutput getReportOutput(Workbook workbook, String reportName) throws IOException {
        final ByteArrayOutputStream bos = new ByteArrayOutputStream();
        workbook.write(bos);
        workbook.close();
        return new ReportOutput(reportName, bos);
    }

    private void resizeSheet(final Sheet sheet, final List<Integer> columnWidths) {
        for (int i = 0; i < columnWidths.size(); i++) {
            sheet.setColumnWidth(i, (columnWidths.get(i) + COLUMN_WIDTH_PADDING) * 256);
        }
    }

    /**
     *
     * @param instituteId
     * @param document
     * @return
     *
     * We are assuming that output of this method is one of the two,
     * if validation & upload is correct,
     * then we will get file id ow we will get a list of errors in below list
     *
     */
    public ExcelFileAssessmentValidationDetails uploadValidateTemplate(int instituteId, FileData document) {
        if (instituteId <= 0) {
            logger.error("Invalid institute {}", instituteId);
            return null;
        }

        if (document == null || document.getContent() == null) {
            logger.error("Invalid document for {}", instituteId);
            return null;
        }

        try {
            final ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(document.getContent());
            if (byteArrayInputStream == null) {
                logger.error("Invalid file for institute {}", instituteId);
                return null;
            }

            List<String> errorList = new ArrayList<String>();
            List<QuestionBank> questionBankList = new ArrayList<>();
            validateUploadedDocument(byteArrayInputStream, questionBankList, errorList);
            if(!CollectionUtils.isEmpty(errorList)) {
                return new ExcelFileAssessmentValidationDetails(CloneStatus.COMPLETE_FAILURE, null, errorList);
            }
            return new ExcelFileAssessmentValidationDetails(CloneStatus.COMPLETE_SUCCESS, questionBankList, null);
        } catch(Exception e) {
            logger.error(e);
            return null;
        }
    }

    /**
     *
     * @param instituteId
     * @param filePath
     * @return
     */
    private ByteArrayOutputStream getFileData(int instituteId, String filePath) {
        try {
            final ByteArrayOutputStream fileByteArrayOutputStream = documentManager.downloadDocument(instituteId, filePath);
            if (fileByteArrayOutputStream == null) {
                logger.error("Invalid fileId {} for institute {}", filePath, instituteId);
                return null;
            }
            return fileByteArrayOutputStream;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     *
     * @param inStream
     * @param questionBankList
     * @param errorList
     */
    private void validateUploadedDocument(ByteArrayInputStream inStream, List<QuestionBank> questionBankList,
                                     List<String> errorList) {
        try {
            Workbook wb = new XSSFWorkbook(inStream);
            Sheet sheet = wb.getSheetAt(0);
            if (sheet == null) {
                errorList.add("Uploaded file is empty or contains no sheets.");
                return;
            }
            Iterator<Row> itr = sheet.iterator();
            int rowIndex = -1;
            int correctAnswerIndex = -1;
            Map<String, Integer> columnMap = new HashMap<>();

            // Read headers and store index mappings
            while (itr.hasNext()) {
                if (itr.hasNext()) {
                    Row headerRow = itr.next();
                    rowIndex++;
                    if(rowIndex == 1){
                        for (Cell cell : headerRow) {
                            String header = validateAndGetCellValue(cell);
                            if (StringUtils.isNotEmpty(header)) {
                                columnMap.put(header, cell.getColumnIndex());
                                if (header.startsWith("Correct Answer")) {
                                    correctAnswerIndex = cell.getColumnIndex();
                                }
                            }
                        }
                    break;
                    }
                }
            }
            // Validate mandatory fields are present in headers
            List<String> mandatoryFields = Arrays.asList("Question *", "Number Of Option *", "Correct Answer (Give The Cell Number Of Option)*");
            for (String field : mandatoryFields) {
                if (!columnMap.containsKey(field)) {
                    errorList.add("Missing mandatory column: " + field);
                }
            }

            // Process each row
            while (itr.hasNext()) {
                Row row = itr.next();
                rowIndex++;
                if (rowIndex <= 1 || isRowEmpty(row)) {
                    continue;
                }

                QuestionBank questionBank = new QuestionBank();
                List<QuestionOption> options = new ArrayList<>();
                List<UUID> correctAnswers = new ArrayList<>();
                Map<Integer, UUID> optionUUIDIndexMap = new HashMap<>();
                UUID questionId = UUID.randomUUID();

                for (Map.Entry<String, Integer> entry : columnMap.entrySet()) {
                    String columnName = entry.getKey();
                    int colIndex = entry.getValue();
                    Cell cell = row.getCell(colIndex);
                    String cellValue = validateAndGetCellValue(cell);

                    // Mandatory field validation
                    if (mandatoryFields.contains(columnName) && StringUtils.isEmpty(cellValue)) {
                        errorList.add("Row " + rowIndex + ": Mandatory field '" + columnName + "' cannot be empty.");
                    }

                    // Map column values to QuestionBank fields
                    switch (columnName) {
                        case "Question *":
                            questionBank.setQuestionId(questionId);
                            questionBank.setQuestionText(cellValue);
                            break;

                        case "Number Of Option *":
                            if (cell.getCellTypeEnum() != CellType.NUMERIC) {
                                errorList.add("Row " + rowIndex + ": 'Number Of Option' must be a number.");
                            }
                            break;

                        case "Correct Answer (Give The Cell Number Of Option)*":
                            if (correctAnswerIndex != -1 && StringUtils.isNotEmpty(cellValue)) {
                                correctAnswers.addAll(parseCorrectAnswers(cellValue, correctAnswerIndex, rowIndex, optionUUIDIndexMap, errorList));
                            }
                            break;
                        
                        case "Marks":
                            if(StringUtils.isEmpty(cellValue)){
                                questionBank.setMarks(null);
                                break;
                            }
                            if(cell.getCellTypeEnum() != CellType.NUMERIC) {
                                errorList.add("Row " + rowIndex + ": 'Marks' must be a number.");
                                break;
                            }
                            questionBank.setMarks((int) cell.getNumericCellValue());
                            break;
                        case "Difficulty Level":
                            questionBank.setQuestionLevel(QuestionLevel.getQuestionType(cellValue));
                            break;

                        case "Tags":
                            questionBank.setTags(cellValue);
                            break;

                        default:
                            if (columnName.toLowerCase().startsWith("option")) {
                                UUID optionId = UUID.randomUUID();
                                options.add(new QuestionOption(optionId, questionId, cellValue));
                                optionUUIDIndexMap.put(colIndex, optionId);
                            }
                            break;
                    }
                }
                questionBank.setQuestionType(QuestionType.OBJECTIVE);
                questionBank.setCorrectAnswerId(correctAnswers);
                questionBank.setQuestionOptions(options);
                questionBank.setActive(true);
                
                questionBankList.add(questionBank);
            }
        } catch (IOException e) {
            logger.error("Error while processing uploaded document", e);
        }
    }

    /**
     * Parses "Correct Answer" values like "C2,E2" and converts them to column indexes.
     */
    private List<UUID> parseCorrectAnswers(String value, int correctAnswerIndex, int rowIndex,  Map<Integer, UUID> optionUUIDIndexMap, List<String> errorList) {
        List<UUID> correctAnswers = new ArrayList<>();
        Map<Character, Integer> columnMapping = new HashMap<>();

        for (char ch = 'A'; ch <= 'J'; ch++) {
            columnMapping.put(ch, ch - 'A'); // Maps A=0, B=1, ..., J=9
        }

        String[] answers = value.split(",");
        for (String ans : answers) {
            ans = ans.trim().toUpperCase();

            char colChar = ans.charAt(0);
            if (!columnMapping.containsKey(colChar)) {
                errorList.add("Row " + rowIndex + ": Invalid column reference '" + colChar + "'");
                continue;
            }

            int optionIndex = columnMapping.get(colChar);
            UUID optionId = optionUUIDIndexMap.get(optionIndex);
            if(optionId == null){
                errorList.add("Row " + rowIndex + ": Invalid column reference '" + colChar + "'"+"Option Not Found");
                continue;
            }
            correctAnswers.add(optionId);
        }

        return correctAnswers;
    }


    /**
     *
     * @param row
     * @return
     */
    private boolean isRowEmpty(Row row) {
        if (row == null) {
            return true;
        }
        if (row.getLastCellNum() <= 0) {
            return true;
        }
        for (int c = row.getFirstCellNum(); c < row.getLastCellNum(); c++) {
            Cell cell = row.getCell(c);
            if (cell != null && cell.getCellTypeEnum() != CellType.BLANK)
                return false;
        }
        return true;
    }

    private String validateAndGetCellValue(Cell cell) {
        String finalValue = "";
        switch(cell.getCellTypeEnum()) {
            case STRING:
                String strValue = cell.getStringCellValue();
                finalValue = strValue;
                break;
            case NUMERIC:
                long numericValue = (long) cell.getNumericCellValue();
                finalValue = String.valueOf(numericValue);
                break;
            case BOOLEAN:
                boolean booleanValue = cell.getBooleanCellValue();
                finalValue = String.valueOf(booleanValue);
                break;
            default:
                break;
        }
        return finalValue;
    }

    /**
     *
     * @param instituteId
     * @param fileId
     * @param fileExtension
     * @return
     */
    private String buildDataPath(int instituteId, String fileId, String fileExtension) {
        final StringBuilder s3Path = new StringBuilder();
        s3Path.append("institute_id=").append(instituteId).append(DocumentUtils.S3_FILE_PATH_DELIMITER)
                .append(COMMUNICATION_DIRECTORY_NAME).append(DocumentUtils.S3_FILE_PATH_DELIMITER)
                .append(EXCEL_DIRECTORY_NAME).append(DocumentUtils.S3_FILE_PATH_DELIMITER)
                .append(fileId).append(".").append(fileExtension);
        return s3Path.toString();
    }
}
