package com.lernen.cloud.core.lib.examination;

import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.examination.report.ReportCardVariableDetails;
import com.lernen.cloud.core.api.examination.report.StudentReportCardVariableDetails;
import com.lernen.cloud.core.api.examination.report.greensheet.*;
import com.lernen.cloud.core.api.report.ReportOutput;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.utils.DateUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.*;

public abstract class ExamGreenSheetGenerator implements IExamGreenSheetGenerator {

//    private static final Double TOTAL_MARKS_1ST_TO_4TH = 800d;
//    private static final Double TOTAL_MARKS_6TH_TO_7TH = 1200d;
//    private static final Double TOTAL_MARKS_11TH = 1200d;
//
//    private static final Integer TOTAL_LINE_SPACING_1ST_TO_4TH = 25;
//    private static final Integer TOTAL_LINE_SPACING_6TH_TO_7TH = 25;
//    private static final Integer TOTAL_LINE_SPACING_11TH = 25;
//
//    private static final Integer START_ROW_1ST_TO_4TH = 9;
//    private static final Integer START_ROW_6TH_TO_7TH = 9;
//    private static final Integer START_ROW_11TH = 9;
//
//    private static final Integer START_COLUMN_1ST_TO_4TH = 7;
//    private static final Integer START_COLUMN_6TH_TO_7TH = 7;
//    private static final Integer START_COLUMN_11TH = 7;
//
//    private static final Integer START_ROW_COURSE_WISE_MAP_1ST_TO_4TH = 58;
//    private static final Integer START_ROW_COURSE_WISE_MAP_6TH_TO_7TH = 58;
//    private static final Integer START_ROW_COURSE_WISE_MAP_11TH = 58;
//
//    private static final Integer START_COLUMN_COURSE_WISE_MAP_1ST_TO_4TH = 9;
//    private static final Integer START_COLUMN_COURSE_WISE_MAP_6TH_TO_7TH = 9;
//    private static final Integer START_COLUMN_COURSE_WISE_MAP_11TH = 9;
//
//    private static final Integer ATTENDANCE_COLUMN_1ST_TO_4TH = 65;
//    private static final Integer ATTENDANCE_COLUMN_6TH_TO_7TH = 84;
//    private static final Integer ATTENDANCE_COLUMN_11TH = 71;

    protected static final Integer PER_SHEET_STUDENT_COUNT = 20;
    protected static final double PASSING_THRESHOLD = 0.33d;

    protected static final String GRADE_A = "A";
    protected static final Logger logger = LogManager.getLogger(ExamGreenSheetGenerator.class);
    private static final String GRADE_B = "B";
    private static final String GRADE_C = "C";
    private static final String GRADE_D = "D";
    private static final String GRADE_E = "E";
    private static final String DIVISION_I = "I";
    private static final String DIVISION_II = "II";
    private static final String DIVISION_III = "III";

    private static Map<String, String> shortNameMap = new HashMap<>();

    static {
        addCourse(shortNameMap, "Science", "Sc");
        addCourse(shortNameMap, "ENGLISH", "E");
        addCourse(shortNameMap, "HINDI", "H");
        addCourse(shortNameMap, "MATHS", "M");
        addCourse(shortNameMap, "EVS", "EVS");
        addCourse(shortNameMap, "SANSKRIT", "San");
        addCourse(shortNameMap, "SOCIAL SCIENCE", "SST");
        addCourse(shortNameMap, "BIOLOGY", "B");
        addCourse(shortNameMap, "CHEMISTRY", "C");
        addCourse(shortNameMap, "PHYSICS", "P");
        addCourse(shortNameMap, "English (Comp)", "EC");
        addCourse(shortNameMap, "Geography", "G");
        addCourse(shortNameMap, "Hindi (Comp)", "HC");
        addCourse(shortNameMap, "Hindi Sah.", "HS");
        addCourse(shortNameMap, "History", "His");
        addCourse(shortNameMap, "ACCOUNTANCY", "Acc");
        addCourse(shortNameMap, "BUSS.STUDIES", "BST");
        addCourse(shortNameMap, "COMPUTER SCIENCE", "CS");
        addCourse(shortNameMap, "ECONOMICS", "Eco");
    }

    private static String lowerCase(String str) {
        return str.trim().toLowerCase();
    }

    private static void addCourse(Map<String, String> shortNameMap, String courseName, String shortName) {
        shortNameMap.put(lowerCase(courseName), shortName);
    }

    @Override
    public abstract ReportOutput generateGreenSheet(GreenSheetClassData greenSheetClassData, ReportCardVariableDetails reportCardVariableDetails,
                                                    String greenSheetHeader) throws IOException;

    protected void addInstituteNameOnSheet(Sheet sheet, String instituteNameWithSessionDetailsStr) {
        Cell cell = sheet.getRow(0).getCell(0);
        cell.setCellValue(instituteNameWithSessionDetailsStr);
    }

    protected void addClassNameOnSheet(Sheet sheet, String className) {
        Cell cell = sheet.getRow(3).getCell(0);
        cell.setCellValue(className);
    }

    protected String getDivision(Double percentage) {
        if (percentage == null) {
            return "";
        }
        if (percentage >= 60.0 && percentage <= 100) {
            return DIVISION_I;
        } else if (percentage >= 48.0 && percentage < 60.0) {
            return DIVISION_II;
        } else if (percentage >= 33.0 && percentage < 48) {
            return DIVISION_III;
        }
        return "";
    }

    protected void addTotalDetailsBottom(Sheet worksheet, int totalStudentCount, int rowStart, int columnStart) {

        Cell cell = worksheet.getRow(rowStart).getCell(columnStart);
        cell.setCellValue(totalStudentCount);
        rowStart++;
        cell = worksheet.getRow(rowStart).getCell(columnStart);
        cell.setCellValue(totalStudentCount);
        rowStart++;
        cell = worksheet.getRow(rowStart).getCell(columnStart);
        cell.setCellValue(totalStudentCount);
        rowStart++;
        cell = worksheet.getRow(rowStart).getCell(columnStart);
        cell.setCellValue("100%");

    }

    protected Map<UUID, StudentReportCardVariableDetails> getStudentReportCardVariableDetails(
            ReportCardVariableDetails reportCardVariableDetails) {
        if (reportCardVariableDetails == null || CollectionUtils.isEmpty(reportCardVariableDetails.getStudentReportCardVariableDetails())) {
            return null;
        }
        Map<UUID, StudentReportCardVariableDetails> studentReportCardVariableMap = new HashMap<UUID, StudentReportCardVariableDetails>();
        for (StudentReportCardVariableDetails studentReportCardVariableDetails : reportCardVariableDetails.getStudentReportCardVariableDetails()) {
            if (!studentReportCardVariableMap.containsKey(studentReportCardVariableDetails.getStudent().getStudentId())) {
                studentReportCardVariableMap.put(studentReportCardVariableDetails.getStudent().getStudentId(),
                        studentReportCardVariableDetails);
            }
        }
        return studentReportCardVariableMap;
    }

    protected LinkedHashMap<CourseType, LinkedHashMap<String, LinkedHashMap<String, Integer>>> defineStaticDivisionGradeMap(
            List<GreenSheetCourseExamMarks> greenSheetCourseExamMarksList) {
        if (CollectionUtils.isEmpty(greenSheetCourseExamMarksList)) {
            return null;
        }
        LinkedHashMap<CourseType, LinkedHashMap<String, LinkedHashMap<String, Integer>>> courseTypeDivisionMap = new LinkedHashMap<>();
        courseTypeDivisionMap.put(CourseType.SCHOLASTIC, new LinkedHashMap<>());
        courseTypeDivisionMap.put(CourseType.COSCHOLASTIC, new LinkedHashMap<>());
        for (GreenSheetCourseExamMarks greenSheetCourseExamMarks : greenSheetCourseExamMarksList) {
            String courseName = greenSheetCourseExamMarks.getCourseName();
            if (StringUtils.isBlank(courseName)) {
                continue;
            }
            if (greenSheetCourseExamMarks.getCourseType() == null ||
                    courseTypeDivisionMap.get(greenSheetCourseExamMarks.getCourseType()) == null) {
                continue;
            }
            courseTypeDivisionMap.get(greenSheetCourseExamMarks.getCourseType()).put(courseName, defineStaticDivisionMap());
        }
        return courseTypeDivisionMap;

    }

    protected LinkedHashMap<String, Integer> defineStaticDivisionMap() {
        LinkedHashMap<String, Integer> divisionMap = new LinkedHashMap<>();
        divisionMap.put(DIVISION_I, 0);
        divisionMap.put(DIVISION_II, 0);
        divisionMap.put(DIVISION_III, 0);
        return divisionMap;
    }

    protected LinkedHashMap<CourseType, LinkedHashMap<String, LinkedHashMap<String, Integer>>> defineStaticCourseGradeMap(
            List<GreenSheetCourseExamMarks> greenSheetCourseExamMarksList) {
        if (CollectionUtils.isEmpty(greenSheetCourseExamMarksList)) {
            return null;
        }
        LinkedHashMap<CourseType, LinkedHashMap<String, LinkedHashMap<String, Integer>>> courseTypeGradeMap = new LinkedHashMap<>();
        courseTypeGradeMap.put(CourseType.SCHOLASTIC, new LinkedHashMap<>());
        courseTypeGradeMap.put(CourseType.COSCHOLASTIC, new LinkedHashMap<>());
        for (GreenSheetCourseExamMarks greenSheetCourseExamMarks : greenSheetCourseExamMarksList) {
            String courseName = greenSheetCourseExamMarks.getCourseName();
            if (StringUtils.isBlank(courseName)) {
                continue;
            }
            if (greenSheetCourseExamMarks.getCourseType() == null ||
                    courseTypeGradeMap.get(greenSheetCourseExamMarks.getCourseType()) == null) {
                continue;
            }
            courseTypeGradeMap.get(greenSheetCourseExamMarks.getCourseType()).put(courseName, defineStaticGradeMap());
        }
        return courseTypeGradeMap;
    }

    protected LinkedHashMap<String, Integer> defineStaticGradeMap() {
        LinkedHashMap<String, Integer> gradeMap = new LinkedHashMap<>();
        gradeMap.put(GRADE_A, 0);
        gradeMap.put(GRADE_B, 0);
        gradeMap.put(GRADE_C, 0);
        gradeMap.put(GRADE_D, 0);
        gradeMap.put(GRADE_E, 0);
        return gradeMap;
    }

    protected String getGrade(Double marks) {
        if (marks == null) {
            return "";
        }
        if (marks >= 85.5 && marks <= 100) {
            return GRADE_A;
        } else if (marks >= 70.5 && marks < 85.5) {
            return GRADE_B;
        } else if (marks >= 50.5 && marks < 70.5) {
            return GRADE_C;
        } else if (marks >= 30.5 && marks < 50.5) {
            return GRADE_D;
        } else if (marks >= 0 && marks < 30.5) {
            return GRADE_E;
        }
        return "";
    }

    protected List<GreenSheetColumn> getStudentBasicDetails(StudentGreenSheetData studentGreenSheetData) {
        List<GreenSheetColumn> studentData = new ArrayList<>();

        studentData.add(new GreenSheetColumn(null, studentGreenSheetData
                .getStudent().getStudentBasicInfo().getAdmissionNumber(), ColumnType.STATIC));
        studentData.add(studentGreenSheetData.getStudent().getStudentBasicInfo().getDateOfBirth() == null ?
                new GreenSheetColumn(null, "", ColumnType.STATIC) :
                new GreenSheetColumn(null, DateUtils.getFormattedDate(
                        studentGreenSheetData.getStudent().getStudentBasicInfo().getDateOfBirth(),
                        "dd-MM-yyyy", User.DFAULT_TIMEZONE), ColumnType.STATIC));
        studentData.add(new GreenSheetColumn(null, studentGreenSheetData.getStudent()
                .getStudentAcademicSessionInfoResponse().getRollNumber(), ColumnType.STATIC));
        studentData.add(new GreenSheetColumn(null, studentGreenSheetData.getStudent()
                .getStudentBasicInfo().getName(), ColumnType.STATIC));

        return studentData;
    }

    protected Map<Integer, Integer> getStudentRank(Map<Integer, Double> studentPercentMap) {
        final Set<Double> classPercentage = new HashSet<>();

        for (final Map.Entry<Integer, Double> studentPercentEntry : studentPercentMap.entrySet()) {
            Double percent = studentPercentEntry.getValue();
            if (percent == null) {
                continue;
            }
            classPercentage.add(percent);
        }

        final List<Double> classPercentageList = new ArrayList<>(classPercentage);

        Collections.sort(classPercentageList, Collections.reverseOrder());

        final Map<Double, Integer> classRankMap = new HashMap<>();
        for (int i = 0; i < classPercentageList.size(); i++) {
            classRankMap.put(classPercentageList.get(i), i + 1);
        }

        final Map<Integer, Integer> studentRankMap = new HashMap<>();
        for (final Map.Entry<Integer, Double> studentPercentEntry : studentPercentMap.entrySet()) {
            Double percent = studentPercentEntry.getValue();
            if (percent == null) {
                continue;
            }
            studentRankMap.put(studentPercentEntry.getKey(), classRankMap.get(studentPercentEntry.getValue()));
        }

        return studentRankMap;
    }

    protected void populateStudentRank(List<List<GreenSheetColumn>> greenSheetData, Map<Integer, Double> studentPercentMap) {
        final Map<Integer, Integer> studentRankMap = getStudentRank(studentPercentMap);

        int index = 0;
        for (List<GreenSheetColumn> greenSheetColumns : greenSheetData) {
            Integer rank = studentRankMap.get(index);
            index++;
            if (rank == null) {
                continue;
            }
            for (GreenSheetColumn greenSheetColumn : greenSheetColumns) {
                if (greenSheetColumn.getColumnType() == ColumnType.RANK) {
                    greenSheetColumn.setValue(String.valueOf(rank));
                    break;
                }
            }
        }
    }

    protected String getShortCourseName(String courseName) {
        String key = lowerCase(courseName);
        if (shortNameMap.containsKey(key)) {
            return shortNameMap.get(key);
        }
        return courseName;
    }

    protected void addGreenSheetDate(Sheet worksheet, int row, int col, Integer date) {
        if (date == null) {
            return;
        }
        addCellData(worksheet, row, col, DateUtils.getFormattedDate(date));
    }

    protected void addTotalAttendedDays(Sheet worksheet, int row, int col, String totalAttendedDays) {
        addCellData(worksheet, row, col, totalAttendedDays);
    }

    protected void addCellData(Sheet worksheet, int row, int col, String data) {
        if (StringUtils.isBlank(data)) {
            return;
        }
        Cell cell = worksheet.getRow(row).getCell(col);
        cell.setCellValue(data);
    }

}
