package com.lernen.cloud.core.lib.reports.utils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */

public class ReportRow {
    // 0 indexed
    private final int rowNum;

    private final List<ReportCell> reportCellList;

    public ReportRow(int rowNum) {
        this.rowNum = rowNum;
        this.reportCellList = new ArrayList<>();
    }

    public List<ReportCell> getReportCellList() {
        return reportCellList;
    }

    public ReportCell createCell(int cellNum) {
        if (cellNum < reportCellList.size()) {
            return reportCellList.get(cellNum);
        }

        for (int i = reportCellList.size(); i <= cellNum; i++) {
            reportCellList.add(new ReportCell(i));
        }

        return reportCellList.get(cellNum);
    }

    public int getRowNum() {
        return rowNum;
    }

    public ReportCell getCell(int cellNum) {
        if (cellNum < reportCellList.size()) {
            return reportCellList.get(cellNum);
        }
        return null;
    }

}
