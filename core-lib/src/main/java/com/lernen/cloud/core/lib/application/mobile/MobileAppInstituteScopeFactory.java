package com.lernen.cloud.core.lib.application.mobile;

import com.lernen.cloud.core.utils.NumberUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MobileAppInstituteScopeFactory {

    private static final Map<String, int[]> APP_INSTITUTE_SCOPE = new HashMap<>();

    private static final int[] NAVJYOTI_INSTITUTE_SCOPE = new int [] {110, 700, 701, 702, 705, 102, 103, 104, 10170, 10171};
    private static final String NAVJYOTI_ANDROID_PACKAGE_NAME = "com.embrate.cloud.navjyoti";
    private static final String NAVJYOTI_IOS_PACKAGE_NAME = "com.embrate.cloud.ios.navjyoti";

    private static final int[] SANSKAR_INSTITUTE_SCOPE = new int [] {110, 700, 701, 702, 705, 102, 103, 104, 10190};
    private static final String SANSKAR_ANDROID_PACKAGE_NAME = "com.embrate.cloud.sanskar";
    private static final String SANSKAR_IOS_PACKAGE_NAME = "com.embrate.cloud.ios.sanskar";

    private static final int[] NOSEGAY_INSTITUTE_SCOPE = new int [] {110, 700, 701, 702, 705, 102, 103, 104, 10180};
    private static final String NOSEGAY_ANDROID_PACKAGE_NAME = "com.embrate.cloud.nosegay";
    private static final String NOSEGAY_IOS_PACKAGE_NAME = "com.embrate.cloud.ios.nosegay";


    private static final int[] JIS_INSTITUTE_SCOPE = new int [] {110, 700, 701, 702, 705, 102, 103, 104, 10225};
    private static final String JIS_ANDROID_PACKAGE_NAME = "com.embrate.cloud.jis";
    private static final String JIS_IOS_PACKAGE_NAME = "com.embrate.cloud.ios.jis";


    private static final int[] JAMN_INSTITUTE_SCOPE = new int [] {110, 700, 701, 702, 705, 102, 103, 104, 10226};
    private static final String JAMN_ANDROID_PACKAGE_NAME = "com.embrate.cloud.jamn";
    private static final String JAMN_IOS_PACKAGE_NAME = "com.embrate.cloud.ios.jamn";


    private static final int[] JAGR_INSTITUTE_SCOPE = new int [] {110, 700, 701, 702, 705, 102, 103, 104, 10227};
    private static final String JAGR_ANDROID_PACKAGE_NAME = "com.embrate.cloud.jagr";
    private static final String JAGR_IOS_PACKAGE_NAME = "com.embrate.cloud.ios.jagr";

    private static final int[] TEGSIKAR_INSTITUTE_SCOPE = new int [] {110, 700, 701, 702, 705, 102, 103, 104, 10400, 10401, 10402};
    private static final String TEGSIKAR_ANDROID_PACKAGE_NAME = "com.embrate.cloud.tegsikar";
    private static final String TEGSIKAR_IOS_PACKAGE_NAME = "com.embrate.cloud.ios.tegsikar";

    private static final int[] HORIZON_INSTITUTE_SCOPE = new int [] {110, 700, 701, 702, 705, 102, 103, 104, 10305, 10306};
    private static final String HORIZON_ANDROID_PACKAGE_NAME = "com.embrate.cloud.horizon";
    private static final String HORIZON_IOS_PACKAGE_NAME = "com.embrate.cloud.ios.horizon";

    static {

        APP_INSTITUTE_SCOPE.put(NAVJYOTI_ANDROID_PACKAGE_NAME, NAVJYOTI_INSTITUTE_SCOPE);
        APP_INSTITUTE_SCOPE.put(NAVJYOTI_IOS_PACKAGE_NAME, NAVJYOTI_INSTITUTE_SCOPE);

        APP_INSTITUTE_SCOPE.put(SANSKAR_ANDROID_PACKAGE_NAME, SANSKAR_INSTITUTE_SCOPE);
        APP_INSTITUTE_SCOPE.put(SANSKAR_IOS_PACKAGE_NAME, SANSKAR_INSTITUTE_SCOPE);

        APP_INSTITUTE_SCOPE.put(NOSEGAY_ANDROID_PACKAGE_NAME, NOSEGAY_INSTITUTE_SCOPE);
        APP_INSTITUTE_SCOPE.put(NOSEGAY_IOS_PACKAGE_NAME, NOSEGAY_INSTITUTE_SCOPE);

        APP_INSTITUTE_SCOPE.put(JIS_ANDROID_PACKAGE_NAME, JIS_INSTITUTE_SCOPE);
        APP_INSTITUTE_SCOPE.put(JIS_IOS_PACKAGE_NAME, JIS_INSTITUTE_SCOPE);

        APP_INSTITUTE_SCOPE.put(JAMN_ANDROID_PACKAGE_NAME, JAMN_INSTITUTE_SCOPE);
        APP_INSTITUTE_SCOPE.put(JAMN_IOS_PACKAGE_NAME, JAMN_INSTITUTE_SCOPE);

        APP_INSTITUTE_SCOPE.put(JAGR_ANDROID_PACKAGE_NAME, JAGR_INSTITUTE_SCOPE);
        APP_INSTITUTE_SCOPE.put(JAGR_IOS_PACKAGE_NAME, JAGR_INSTITUTE_SCOPE);

        APP_INSTITUTE_SCOPE.put(TEGSIKAR_ANDROID_PACKAGE_NAME, TEGSIKAR_INSTITUTE_SCOPE);
        APP_INSTITUTE_SCOPE.put(TEGSIKAR_IOS_PACKAGE_NAME, TEGSIKAR_INSTITUTE_SCOPE);

        APP_INSTITUTE_SCOPE.put(HORIZON_ANDROID_PACKAGE_NAME, HORIZON_INSTITUTE_SCOPE);
        APP_INSTITUTE_SCOPE.put(HORIZON_IOS_PACKAGE_NAME, HORIZON_INSTITUTE_SCOPE);

    }

    public List<Integer> getAppInstituteScope(Map<String, Object> deviceData) {
        if(deviceData == null || !deviceData.containsKey("packageName") || deviceData.get("packageName") == null) {
            return null;
        }
        String packageName = deviceData.get("packageName").toString();
        int[] instituteScopeArr = APP_INSTITUTE_SCOPE.get(packageName);
        return NumberUtils.getIntegerList(instituteScopeArr);
    }

}
