package com.lernen.cloud.core.lib.diary;

import com.lernen.cloud.core.api.common.FileData;
import com.lernen.cloud.core.api.common.SearchResultWithPagination;
import com.lernen.cloud.core.api.diary.*;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.student.DownloadDocumentWrapper;
import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserType;

import java.util.List;
import java.util.Set;
import java.util.UUID;

public class StudentDiaryManager {
    private final UserDiaryManager userDiaryManager;

    public StudentDiaryManager(UserDiaryManager userDiaryManager) {
        this.userDiaryManager = userDiaryManager;
    }

    public boolean addDiaryRemarkCategory(int instituteId, int academicSessionId, UUID userId, DiaryRemarkCategory diaryRemarkCategory) {
        return userDiaryManager.addDiaryRemarkCategory(instituteId, academicSessionId, userId, RemarkUserType.STUDENT, AuthorisationRequiredAction.DIARY_REMARK_CATEGORY_TYPE, diaryRemarkCategory);
    }

    public boolean updateDiaryRemarkCategory(int instituteId, int academicSessionId, UUID userId, DiaryRemarkCategory diaryRemarkCategory) {
        return userDiaryManager.updateDiaryRemarkCategory(instituteId, academicSessionId, userId, RemarkUserType.STUDENT, AuthorisationRequiredAction.DIARY_REMARK_CATEGORY_TYPE, diaryRemarkCategory);
    }

    public List<DiaryRemarkCategory> getDiaryRemarkCategory(int instituteId) {
        return userDiaryManager.getDiaryRemarkCategory(instituteId, RemarkUserType.STUDENT);
    }

    public boolean deleteDiaryRemarkCategory(int instituteId, int academicSessionId, int categoryId, UUID userId) {
        return userDiaryManager.deleteDiaryRemarkCategory(instituteId, academicSessionId, categoryId, RemarkUserType.STUDENT, userId, AuthorisationRequiredAction.DIARY_REMARK_CATEGORY_TYPE);
    }

    public boolean addStandardRemarks(int instituteId, UUID userId, StandardRemarks standardRemarks) {
        return userDiaryManager.addStandardRemarks(instituteId, userId, RemarkUserType.STUDENT, AuthorisationRequiredAction.DIARY_REMARK, standardRemarks);
    }

    public boolean updateStandardRemarks(int instituteId, UUID userId, StandardRemarks standardRemarks) {
        return userDiaryManager.updateStandardRemarks(instituteId, userId, RemarkUserType.STUDENT, AuthorisationRequiredAction.DIARY_REMARK, standardRemarks);
    }

    public boolean deleteStandardRemarks(int instituteId, UUID userId, UUID remarkId) {
        return userDiaryManager.deleteStandardRemarks(instituteId, userId, remarkId, RemarkUserType.STUDENT, AuthorisationRequiredAction.DIARY_REMARK);
    }

    public List<StandardRemarkDetails> getStandardRemarkDetails(int instituteId, int categoryId) {
        return userDiaryManager.getStandardRemarkDetails(instituteId, categoryId, RemarkUserType.STUDENT, null);
    }

    public UUID addRemarks(int instituteId, int academicSessionId, UUID userId, DiaryRemarkDetailsPayload diaryRemarkDetailsPayload, List<FileData> attachments) {
        UserDiaryRemarkDetailsPayload userDiaryRemarkDetailsPayload = new UserDiaryRemarkDetailsPayload(
                diaryRemarkDetailsPayload.getInstituteId(), diaryRemarkDetailsPayload.getAcademicSessionId(),
                diaryRemarkDetailsPayload.getRemarkId(), diaryRemarkDetailsPayload.getTitle(), diaryRemarkDetailsPayload.getDescription(),
                diaryRemarkDetailsPayload.getCategoryId(), diaryRemarkDetailsPayload.getStudentId(),
                diaryRemarkDetailsPayload.getRemarkUserType(), diaryRemarkDetailsPayload.getCreatedBy(), diaryRemarkDetailsPayload.getUpdatedBy());
        return userDiaryManager.addRemarks(instituteId, academicSessionId, userId, RemarkUserType.STUDENT, UserType.STUDENT, AuthorisationRequiredAction.DIARY_REMARK, userDiaryRemarkDetailsPayload, attachments);
    }

    public boolean updateRemarks(int instituteId, int academicSessionId, UUID userId, DiaryRemarkDetailsPayload diaryRemarkDetailsPayload, boolean editAttachments, List<FileData> attachments) {
        UserDiaryRemarkDetailsPayload userDiaryRemarkDetailsPayload = new UserDiaryRemarkDetailsPayload(
                diaryRemarkDetailsPayload.getInstituteId(), diaryRemarkDetailsPayload.getAcademicSessionId(),
                diaryRemarkDetailsPayload.getRemarkId(), diaryRemarkDetailsPayload.getTitle(), diaryRemarkDetailsPayload.getDescription(),
                diaryRemarkDetailsPayload.getCategoryId(), diaryRemarkDetailsPayload.getStudentId(),
                diaryRemarkDetailsPayload.getRemarkUserType(), diaryRemarkDetailsPayload.getCreatedBy(), diaryRemarkDetailsPayload.getUpdatedBy());
        return userDiaryManager.updateRemarks(instituteId, academicSessionId, userId, RemarkUserType.STUDENT, UserType.STUDENT, AuthorisationRequiredAction.DIARY_REMARK, userDiaryRemarkDetailsPayload, editAttachments, attachments);
    }

    public boolean deleteRemarks(int instituteId, int academicSessionId, UUID userId, UUID remarkId) {
        return userDiaryManager.deleteRemarks(instituteId, academicSessionId, userId, remarkId, RemarkUserType.STUDENT, AuthorisationRequiredAction.DIARY_REMARK);
    }

    public List<DiaryRemarkDetails> getRemarkDetails(int instituteId, int academicSessionId, String searchText, Set<Integer> categoryIdSet, Set<UUID> userIdSet) {
        return userDiaryManager.getRemarkDetails(instituteId, academicSessionId, RemarkUserType.STUDENT, searchText, categoryIdSet, userIdSet);
    }

    public SearchResultWithPagination<DiaryRemarkDetails> getRemarkDetailsWithPagination(int instituteId, int academicSessionId, String searchText, Set<Integer> categoryIdInt, Set<UUID> userIdList, Integer offset, Integer limit) {
        return userDiaryManager.getRemarkDetailsWithPagination(instituteId, academicSessionId, RemarkUserType.STUDENT, searchText, categoryIdInt, userIdList, offset, limit);
    }

    public DiaryRemarkDetails getDiaryRemarkDetailByRemarkId(int instituteId, UUID remarkId) {
        return userDiaryManager.getDiaryRemarkDetailByRemarkId(instituteId, remarkId, RemarkUserType.STUDENT);
    }

    public List<DiaryRemarkDetails> getRemarkDetails(int instituteId, int academicSessionId, UUID studentId, String searchText, Set<Integer> categoryIdSet, Set<UUID> userIdSet) {
        return userDiaryManager.getRemarkDetails(instituteId, academicSessionId, studentId, RemarkUserType.STUDENT, searchText, categoryIdSet, userIdSet);
    }

    public List<DiaryRemarkMetadata> getRemarkMetadata(int instituteId, int academicSessionId, Integer offset, Integer limit) {
        return userDiaryManager.getRemarkMetadata(instituteId, academicSessionId, RemarkUserType.STUDENT, offset == null ? 0 : offset, limit == null ? 0 : limit);
    }

    public List<DiaryRemarkMetadata> getRemarkMetadata(int instituteId, int academicSessionId, UUID studentId, Integer offset, Integer limit) {
        return userDiaryManager.getRemarkMetadata(instituteId, academicSessionId, studentId, RemarkUserType.STUDENT, offset == null ? 0 : offset, limit == null ? 0 : limit);
    }

    public DownloadDocumentWrapper<Document<DiaryRemarkDocumentType>> downloadDocument(int instituteId, UUID documentId, UUID remarkId) {
        return userDiaryManager.downloadDocument(instituteId, documentId, remarkId, RemarkUserType.STUDENT);
    }

    public List<User> getDiaryRemarkUsers(int instituteId, int academicSessionId, UUID remarkId) {
        return userDiaryManager.getDiaryRemarkUsers(instituteId, academicSessionId, remarkId, RemarkUserType.STUDENT);
    }

    public List<DatewiseDiaryMetadataDetails> getInstituteDiaryDetails(int instituteId, int academicSessionId, int offset, int limit, UUID studentId) {
        return userDiaryManager.getInstituteDiaryDetails(instituteId, academicSessionId, offset, limit, studentId, RemarkUserType.STUDENT);
    }

    public List<CategorywiseDiaryMetadataDetails> getInstituteDiaryDetailsCategoryWise(int instituteId, int academicSessionId, int offset, int limit, UUID studentId) {
        return userDiaryManager.getInstituteDiaryDetailsCategoryWise(instituteId, academicSessionId, RemarkUserType.STUDENT, offset, limit, studentId);
    }

}
