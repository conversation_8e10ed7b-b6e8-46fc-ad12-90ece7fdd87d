package com.lernen.cloud.core.lib.reports.utils;

import com.lernen.cloud.core.utils.EColorUtils;
import org.apache.poi.ss.usermodel.IndexedColors;

/**
 * <AUTHOR>
 */

public class ReportFont {

    private boolean bold;

    private int fontHeightInPoints = 12;

    private String color = EColorUtils.BLACK_COLOR_HEX_CODE;

    public boolean isBold() {
        return bold;
    }

    public void setBold(boolean bold) {
        this.bold = bold;
    }

    public int getFontHeightInPoints() {
        return fontHeightInPoints;
    }

    public void setFontHeightInPoints(int fontHeightInPoints) {
        this.fontHeightInPoints = fontHeightInPoints;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }
}