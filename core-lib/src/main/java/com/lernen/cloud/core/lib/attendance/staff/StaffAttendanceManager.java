package com.lernen.cloud.core.lib.attendance.staff;

import com.embrate.cloud.core.api.attendance.AttendanceInputType;
import com.embrate.cloud.core.api.attendance.AttendanceLog;
import com.embrate.cloud.core.api.calendar.holiday.StaticHoliday;
import com.embrate.cloud.core.lib.calendar.holiday.HolidayCalendarManager;
import com.embrate.cloud.core.utils.EMapUtils;
import com.embrate.cloud.dao.tier.attendance.UserAttendanceDao;
import com.google.common.collect.Lists;
import com.lernen.cloud.core.api.attendance.staff.*;
import com.lernen.cloud.core.api.attendance.staff.v2.*;
import com.lernen.cloud.core.api.attendance.staff.v3.*;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.institute.Time;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.staff.StaffStatus;
import com.lernen.cloud.core.api.staff.StaffTimingDetails;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.NumberUtils;
import com.lernen.cloud.core.utils.Pair;
import com.lernen.cloud.core.utils.RetryableCallable;
import com.lernen.cloud.core.utils.attendance.staff.StaffAttendanceUtils;
import com.lernen.cloud.dao.tier.attendance.staff.StaffAttendanceDao;
import com.lernen.cloud.dao.tier.staff.StaffDao;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import java.time.LocalTime;
import java.util.*;

public class StaffAttendanceManager {

	private static final Logger logger = LogManager.getLogger(StaffAttendanceManager.class);

	private final StaffAttendanceDao staffAttendanceDao;

	private final UserPermissionManager userPermissionManager;

	private final UserAttendanceDao userAttendanceDao;

	private final TransactionTemplate transactionTemplate;

	private final StaffDao staffDao;

	private final InstituteManager instituteManager;

	private final HolidayCalendarManager holidayCalendarManager;

	public StaffAttendanceManager(StaffAttendanceDao staffAttendanceDao, UserPermissionManager userPermissionManager, UserAttendanceDao userAttendanceDao, TransactionTemplate transactionTemplate, StaffDao staffDao,
								  InstituteManager instituteManager, HolidayCalendarManager holidayCalendarManager) {
		this.staffAttendanceDao = staffAttendanceDao;
		this.userPermissionManager = userPermissionManager;
		this.userAttendanceDao = userAttendanceDao;
		this.transactionTemplate = transactionTemplate;
		this.staffDao = staffDao;
		this.instituteManager = instituteManager;
		this.holidayCalendarManager = holidayCalendarManager;
	}

	/**
	 * Save attendance method, should be used from API layer and not directly from Biometric Device/Handler
	 */
	public boolean saveAPIStaffAttendanceDetails(int instituteId, UUID userId, StaffAttendancePayloadV2 staffAttendancePayload) {
		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if (userId == null) {
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_USER, "Invalid user id."));
		}

		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.SAVE_STAFF_ATTENDANCE);

		validateStaffAttendancePayloadV2(staffAttendancePayload);


		int now = DateUtils.now();
		int attendanceDate = DateUtils.getDayStart(staffAttendancePayload.getAttendanceDate(), DateUtils.DEFAULT_TIMEZONE);
		List<AttendanceLog> attendanceLogs = new ArrayList<>();
		for (StaffAttendanceInput staffAttendanceInput : staffAttendancePayload.getStaffAttendanceInputList()) {
			if (staffAttendanceInput.getTimeOfAction() == null) {
				continue;
			}
			attendanceLogs.add(new AttendanceLog(null, null, staffAttendanceInput.getStaffId(), UserType.STAFF, AttendanceInputType.WEB, null,
					now, DateUtils.calculateDateTime(attendanceDate, staffAttendanceInput.getTimeOfAction()), now, userId, null));
		}

		// TODO : handle transaction if required
		boolean eventLogged = userAttendanceDao.addAttendanceLog(instituteId, attendanceLogs);
		if (!eventLogged) {
			logger.error("Unable to log the event institute {}, attendanceLogs {}, payload {}", instituteId,
					attendanceLogs, staffAttendancePayload);
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_REQUEST, "Unable to log attendance register."));
		}

		return saveStaffAttendanceDetailsV2(instituteId, userId, staffAttendancePayload);
	}

	public boolean saveAPIStaffAttendanceStatus(int instituteId, UUID userId, StaffMarkAttendancePayload staffMarkAttendancePayload) {
		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if (userId == null) {
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_USER, "Invalid user id."));
		}

		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.SAVE_STAFF_ATTENDANCE);
		int now = DateUtils.now();
		int attendanceDate = DateUtils.getDayStart(staffMarkAttendancePayload.getAttendanceDate(), DateUtils.DEFAULT_TIMEZONE);
		List<AttendanceLog> attendanceLogs = new ArrayList<>();
		for (StaffAttendanceStatusInput staffAttendanceStatusInput : staffMarkAttendancePayload.getStaffAttendanceStatusInputs()) {
			if (staffAttendanceStatusInput.getStaffAttendanceStatus() == null) {
				continue;
			}
			Map<String, String> metadata = new HashMap<>();
			metadata.put(AttendanceLog.ATTENDANCE_STATUS, staffAttendanceStatusInput.getStaffAttendanceStatus().name());
			attendanceLogs.add(new AttendanceLog(null, null, staffAttendanceStatusInput.getStaffId(), UserType.STAFF, AttendanceInputType.WEB, null,
					now, attendanceDate, now, userId, metadata));
		}

		boolean eventLogged = userAttendanceDao.addAttendanceLog(instituteId, attendanceLogs);
		if (!eventLogged) {
			logger.error("Unable to log the event institute {}, attendanceLogs {}, payload {}", instituteId,
					attendanceLogs, staffMarkAttendancePayload);
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_REQUEST, "Unable to log attendance register."));
		}

		return staffAttendanceDao.saveAPIStaffAttendanceStatus(instituteId, userId, staffMarkAttendancePayload);
	}

	public StaffAttendancePayloadV3 saveAPIStaffAttendanceDetailsV3(int instituteId, UUID userId, StaffAttendancePayloadV3 staffAttendancePayload) {
		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if (userId == null) {
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_USER, "Invalid user id."));
		}

		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.SAVE_STAFF_ATTENDANCE);
		Set<UUID> staffIdSet  = new HashSet<>();
		getStaffIdSet(staffAttendancePayload, staffIdSet);
		List<StaffAttendanceRegisterData> staffAttendanceRegisterDataList = staffAttendanceDao.getStaffAttendanceRegister(instituteId, staffAttendancePayload.getAttendanceDate(), staffIdSet);
		Map<UUID, StaffAttendanceRegisterData> staffAttendanceRegisterDataMap = getStaffAttendanceRegisterMap(staffAttendanceRegisterDataList);
		validateStaffAttendancePayloadV3(staffAttendancePayload);
		StaffAttendancePayloadV2 staffAttendancePayloadV2 = new StaffAttendancePayloadV2();
		StaffAttendancePayloadV3 filteredStaffAttendancePayload = new StaffAttendancePayloadV3();
		Set<UUID> filteredStaffId  = new HashSet<>();
		createStaffPayload(staffAttendancePayload, staffAttendancePayloadV2, filteredStaffId, staffAttendanceRegisterDataMap, filteredStaffAttendancePayload);
		int now = DateUtils.now();
		int attendanceDate = DateUtils.getDayStart(staffAttendancePayload.getAttendanceDate(), DateUtils.DEFAULT_TIMEZONE);
		List<AttendanceLog> attendanceLogs = new ArrayList<>();
		for (StaffAttendanceInput staffAttendanceInput : staffAttendancePayloadV2.getStaffAttendanceInputList()) {
			if (staffAttendanceInput.getTimeOfAction() == null) {
				continue;
			}
			attendanceLogs.add(new AttendanceLog(null, null, staffAttendanceInput.getStaffId(), UserType.STAFF, AttendanceInputType.WEB, null,
					now, DateUtils.calculateDateTime(attendanceDate, staffAttendanceInput.getTimeOfAction()), now, userId, null));
		}

		// TODO : handle transaction if required
		boolean eventLogged = userAttendanceDao.addAttendanceLog(instituteId, attendanceLogs);
		if (!eventLogged) {
			logger.error("Unable to log the event institute {}, attendanceLogs {}, payload {}", instituteId,
					attendanceLogs, staffAttendancePayload);
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_REQUEST, "Unable to log attendance register."));
		}

		return saveStaffAttendanceDetailsV3(instituteId, userId, staffAttendancePayloadV2, filteredStaffId) ? filteredStaffAttendancePayload : null;
	}

	public static Map<UUID, StaffAttendanceRegisterData> getStaffAttendanceRegisterMap(List<StaffAttendanceRegisterData> staffAttendanceRegisterDataList) {
		Map<UUID, StaffAttendanceRegisterData> staffMap = new HashMap<>();
		for (StaffAttendanceRegisterData data : staffAttendanceRegisterDataList) {
			UUID staffId = data.getStaff().getStaffId();
			staffMap.put(staffId, data);
		}
		return staffMap;
	}

	private void getStaffIdSet(StaffAttendancePayloadV3 staffAttendancePayload, Set<UUID> staffIdSet) {
		if (staffAttendancePayload == null || staffAttendancePayload.getStaffAttendanceInputList() == null) {
			return;
		}

		for (StaffAttendanceInputV3 inputV3 : staffAttendancePayload.getStaffAttendanceInputList()) {
			UUID staffId = inputV3.getStaffId();
			staffIdSet.add(staffId);
		}

	}

	private void createStaffPayload(StaffAttendancePayloadV3 staffAttendancePayload, StaffAttendancePayloadV2 staffAttendancePayload2, Set<UUID> filteredStaffId, Map<UUID, StaffAttendanceRegisterData> staffAttendanceRegisterDataMap, StaffAttendancePayloadV3 filteredStaffAttendancePayload) {
		if (staffAttendancePayload == null || staffAttendancePayload.getStaffAttendanceInputList() == null) {
			return;
		}

		staffAttendancePayload2.setAttendanceDate(staffAttendancePayload.getAttendanceDate());
		filteredStaffAttendancePayload.setAttendanceDate(staffAttendancePayload.getAttendanceDate());
		List<StaffAttendanceInput> staffAttendanceInputList = new ArrayList<>();
		List<StaffAttendanceInputV3> filteredInputV3List = new ArrayList<>();
		for (StaffAttendanceInputV3 inputV3 : staffAttendancePayload.getStaffAttendanceInputList()) {
			UUID staffId = inputV3.getStaffId();
			if(inputV3.getStaffTimeDurationList() == null) {continue;}
			boolean skipStaffPayload = skipStaffAttendancePayload(staffAttendanceRegisterDataMap, inputV3);
			if(!skipStaffPayload) {
				filteredStaffId.add(staffId);
				filteredInputV3List.add(inputV3);

				if (inputV3.getStaffTimeDurationList() != null) {
					for (StaffTimeDuration duration : inputV3.getStaffTimeDurationList()) {
						if (duration.getInTime() != null) {
							StaffAttendanceInput input = new StaffAttendanceInput();
							input.setStaffId(staffId);
							input.setTimeOfAction(duration.getInTime());
							staffAttendanceInputList.add(input);
						}
						if (duration.getOutTime() != null) {
							StaffAttendanceInput input = new StaffAttendanceInput();
							input.setStaffId(staffId);
							input.setTimeOfAction(duration.getOutTime());
							staffAttendanceInputList.add(input);
						}
					}
				}
			}
		}
		filteredStaffAttendancePayload.setStaffAttendanceInputList(filteredInputV3List);
		staffAttendancePayload2.setStaffAttendanceInputList(staffAttendanceInputList);
	}

	public static boolean skipStaffAttendancePayload(Map<UUID, StaffAttendanceRegisterData> staffAttendanceRegisterDataMap, StaffAttendanceInputV3 input) {


		StaffAttendanceRegisterData staffAttendanceRegisterData = staffAttendanceRegisterDataMap.get(input.getStaffId());

		if (staffAttendanceRegisterData == null || staffAttendanceRegisterData.getStaffAttendanceDaySummaryList() == null) {
			return false;
		}

		List<StaffAttendanceDaySummary> staffAttendanceDaySummary = staffAttendanceRegisterData.getStaffAttendanceDaySummaryList();
		if (CollectionUtils.isEmpty(staffAttendanceDaySummary)) {
			return false;
		}

		StaffAttendanceDaySummary firstSummary = staffAttendanceDaySummary.get(0);
		if (firstSummary == null || firstSummary.getStaffAttendanceDayMetadata() == null) {
			return false;
		}

		List<StaffTimeDuration> registerTimeDurations = firstSummary.getStaffAttendanceDayMetadata().getStaffTimeDurationList();

		if (CollectionUtils.isEmpty(registerTimeDurations) || CollectionUtils.isEmpty(input.getStaffTimeDurationList())) {
			return false;
		}
		List<StaffTimeDuration> inputTimeDurations = input.getStaffTimeDurationList();

		if (registerTimeDurations.size() != inputTimeDurations.size()) {
			return false;
		}

		for (int i = 0; i < registerTimeDurations.size(); i++) {
			StaffTimeDuration fromRegister = registerTimeDurations.get(i);
			StaffTimeDuration fromInput = inputTimeDurations.get(i);

			if (fromRegister == null || fromInput == null) {
				return false;
			}
			if (fromRegister.getInTime().compareTo(fromInput.getInTime()) != 0) {
				return false;
			}

			Time outRegisterTime = fromRegister.getOutTime();
			Time outInputTime = fromInput.getOutTime();

			if (outRegisterTime == null && outInputTime == null) {
				continue;
			}

			if (outRegisterTime == null || outInputTime == null) {
				return false;
			}

			if (outRegisterTime.compareTo(outInputTime) != 0) {
				return false;
			}
		}

		return true;
	}


	private void validateStaffAttendancePayloadV3(StaffAttendancePayloadV3 staffAttendancePayload) {
		if (staffAttendancePayload.getAttendanceDate() == null || staffAttendancePayload.getAttendanceDate() <= 0) {
			logger.error("Invalid attendance date for {}", staffAttendancePayload);
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_STAFF_ATTENDANCE_DETAILS, "Invalid attendance date."));
		}
		if (CollectionUtils.isEmpty(staffAttendancePayload.getStaffAttendanceInputList())) {
			logger.error("Invalid staff attendance input payload for {}", staffAttendancePayload);
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_STAFF_ATTENDANCE_DETAILS, "Invalid staff attendance input payload."));
		}

		for (StaffAttendanceInputV3 staffAttendanceInput : staffAttendancePayload.getStaffAttendanceInputList()) {
			if (staffAttendanceInput == null || staffAttendanceInput.getStaffId() == null) {
				logger.error("Invalid staff attendance input payload for {}", staffAttendancePayload);
				throw new ApplicationException(new ErrorResponse(
						ApplicationErrorCode.INVALID_STAFF_ATTENDANCE_DETAILS, "Invalid staff attendance input payload."));
			}

			List<StaffTimeDuration> timeDurations = staffAttendanceInput.getStaffTimeDurationList();
			if (timeDurations != null && !timeDurations.isEmpty()) {
				StaffTimeDuration previous = null;

				for (int i = 0; i < timeDurations.size(); i++) {
					StaffTimeDuration current = timeDurations.get(i);

					// in any list in time can not be null, if in time is null then we will not be able to fill out time, means that list should not have existed.
					if (current.getInTime() == null) {
						logger.error("InTime is null for staffId: {} at index {}", staffAttendanceInput.getStaffId(), i);
						throw new ApplicationException(new ErrorResponse(
								ApplicationErrorCode.INVALID_STAFF_ATTENDANCE_DETAILS, "InTime cannot be null."));
					}

					LocalTime inTime = LocalTime.of(current.getInTime().getHour(), current.getInTime().getMinute(), current.getInTime().getSecond());

					LocalTime outTime = null;
					if (current.getOutTime() != null) {
						outTime = LocalTime.of(current.getOutTime().getHour(), current.getOutTime().getMinute(), current.getOutTime().getSecond());
					}

					// if outTime exists, inTime must be before it
					if (outTime != null && !inTime.isBefore(outTime)) {
						logger.error("InTime is not before OutTime for staffId: {} -> inTime: {}, outTime: {}",
								staffAttendanceInput.getStaffId(), inTime, outTime);
						throw new ApplicationException(new ErrorResponse(
								ApplicationErrorCode.INVALID_STAFF_ATTENDANCE_DETAILS, "InTime must be before OutTime."));
					}


					// if previous out time is null then that should be the last entry, new entry will only be added when both in time and out time is already filled in previous entry.
					if (previous != null && previous.getOutTime() == null) {
						logger.error("Found duration after null outTime for staffId: {} at index {}", staffAttendanceInput.getStaffId(), i);
						throw new ApplicationException(new ErrorResponse(
								ApplicationErrorCode.INVALID_STAFF_ATTENDANCE_DETAILS,
								"No durations allowed after an entry with null outTime."));
					}

					//for every entry previous in time should be before current in time to maintain a sequential order i.e in time should be in sorted order.
					if (previous != null && previous.getOutTime() != null) {
						LocalTime prevOut = LocalTime.of(previous.getOutTime().getHour(), previous.getOutTime().getMinute(), previous.getOutTime().getSecond());
						if (prevOut.isAfter(inTime)) {
							logger.error("Previous outTime is not before current inTime for staffId: {} -> prevOut: {}, currIn: {}",
									staffAttendanceInput.getStaffId(), prevOut, inTime);
							throw new ApplicationException(new ErrorResponse(
									ApplicationErrorCode.INVALID_STAFF_ATTENDANCE_DETAILS,
									"OutTime of previous must be before InTime of current."));
						}
					}

					previous = current;
				}
			}
		}

	}

	public boolean recomputeAttendance(int instituteId, UUID userId, int startDate, int endDate) {

		logger.info("Recomputing staff attendance for {}, {}-{}", instituteId, startDate, endDate);
		if (startDate > endDate) {
			logger.error("Invalid date range for {}, {}-{}", instituteId, startDate, endDate);
			return false;
		}

		return transactionTemplate.execute(new TransactionCallback<Boolean>() {
			@Override
			public Boolean doInTransaction(TransactionStatus status) {
				List<Staff> staffList = staffDao.getStaff(instituteId);
				Set<UUID> staffIds = new HashSet<>();
				for (Staff staff : staffList) {
					staffIds.add(staff.getStaffId());
				}

				if (CollectionUtils.isEmpty(staffIds)) {
					logger.warn("No valid staff for attendance update institute {}", instituteId);
					return true;
				}

				int startDay = DateUtils.getDayStart(startDate, DateUtils.DEFAULT_TIMEZONE);
				int endDay = DateUtils.getDayStart(endDate, DateUtils.DEFAULT_TIMEZONE);
				int currentDay = startDay;
				while (currentDay <= endDay) {
					logger.info("Recomputing staff attendance for {}, {}", instituteId, currentDay);
					computeAndPersistStaffAttendanceDetails(instituteId, currentDay, userId, staffIds);
					currentDay = DateUtils.getNextDayStart(currentDay, DateUtils.DEFAULT_TIMEZONE);
				}

				return true;
			}
		});

	}
	public boolean saveStaffAttendanceDetailsV3(int instituteId, UUID userId, StaffAttendancePayloadV2 staffAttendancePayload, Set<UUID> staffIdSet) {

		boolean attendanceLogged = staffAttendanceDao.saveStaffAttendanceLogsV3(instituteId, userId, staffAttendancePayload, staffIdSet);
		if (!attendanceLogged) {
			logger.error("Unable to log the attendance institute {}, user {}, payload {}", instituteId,
					userId, staffAttendancePayload);
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_REQUEST, "Unable to log attendance register."));
		}


		return computeAndPersistStaffAttendanceDetails(instituteId, userId, staffAttendancePayload);
	}

	public boolean saveStaffAttendanceDetailsV2(int instituteId, UUID userId, StaffAttendancePayloadV2 staffAttendancePayload) {

		boolean attendanceLogged = staffAttendanceDao.saveStaffAttendanceLogsV2(instituteId, userId, staffAttendancePayload);
		if (!attendanceLogged) {
			logger.error("Unable to log the attendance institute {}, user {}, payload {}", instituteId,
					userId, staffAttendancePayload);
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_REQUEST, "Unable to log attendance register."));
		}


		return computeAndPersistStaffAttendanceDetails(instituteId, userId, staffAttendancePayload);
	}

	/**
	 * Use this method only if caller class is handling the userAttendanceDao.addAttendanceLog
	 */
	public boolean computeAndPersistStaffAttendanceDetails(int instituteId, UUID userId, StaffAttendancePayloadV2 staffAttendancePayload) {

		RetryableCallable<Boolean> retryableCallable = new RetryableCallable<Boolean>() {
			@Override
			public Boolean retryableCall() throws Exception {
				return transactionTemplate.execute(new TransactionCallback<Boolean>() {
					@Override
					public Boolean doInTransaction(TransactionStatus status) {

						Set<UUID> staffIds = new HashSet<>();
						for (StaffAttendanceInput staffAttendanceInput : staffAttendancePayload.getStaffAttendanceInputList()) {
							if (staffAttendanceInput == null || staffAttendanceInput.getStaffId() == null || staffAttendanceInput.getTimeOfAction() == null) {
								continue;
							}
							staffIds.add(staffAttendanceInput.getStaffId());
						}

						if (CollectionUtils.isEmpty(staffIds)) {
							logger.warn("No valid staff for attendance update institute {}, {}", instituteId, staffAttendancePayload);
							return true;
						}

						computeAndPersistStaffAttendanceDetails(instituteId, staffAttendancePayload.getAttendanceDate(), userId, staffIds);

						return true;
					}
				});
			}
		};

		try {
			return retryableCallable.call();
		} catch (Exception e) {
			logger.error("Unable to compute the staff attendance {}, staffAttendancePayload {}", instituteId, staffAttendancePayload, e);
		}

		return false;
	}

	/**
	 * Only use within transaction and user must handle userAttendanceDao.addAttendanceLog at caller level
	 */
	private void computeAndPersistStaffAttendanceDetails(int instituteId, int attendanceDate, UUID userId, Set<UUID> staffIds) {

		// Fetching with lock in share mode as we need to be consistent and block all the writes when we are updating the register table.
		List<StaffAttendanceLog> staffAttendanceLogList = staffAttendanceDao.getStaffAttendanceLogs(instituteId, attendanceDate, staffIds, true);

		if (staffAttendanceLogList == null) {
			logger.error("Unable to get staff log {}, date {}, staffIds {}", instituteId, attendanceDate, staffIds);
			throw new EmbrateRunTimeException("Unable to get staff logs");
		}

		List<Staff> staffList = staffDao.getStaff(instituteId, staffIds);

		if (staffList == null) {
			logger.error("Unable to get staff {}, staffIds {}", instituteId, staffIds);
			throw new EmbrateRunTimeException("Unable to get staff logs");
		}

		Map<UUID, StaffAttendanceLog> staffAttendanceLogMap = getStaffAttendanceLogMap(staffAttendanceLogList);

		List<StaffAttendanceSummary> staffAttendanceSummaryList = getStaffAttendanceSummary(staffAttendanceLogMap, staffList);

		boolean success = staffAttendanceDao.saveStaffAttendanceDetailsV2(instituteId, userId, staffAttendanceSummaryList);
		if (!success) {
			throw new EmbrateRunTimeException("Error while adding staff attendance register details");
		}
	}

	private static List<StaffAttendanceSummary> getStaffAttendanceSummary(Map<UUID, StaffAttendanceLog> staffAttendanceLogMap, List<Staff> staffList) {
		List<StaffAttendanceSummary> staffAttendanceSummaryList = new ArrayList<>();
		for (Staff staff : staffList) {
			UUID staffId = staff.getStaffId();
			StaffAttendanceLog staffAttendanceLog = staffAttendanceLogMap.get(staffId);
			if (staffAttendanceLog == null || CollectionUtils.isEmpty(staffAttendanceLog.getStaffAttendanceDayLogs())) {
				continue;
			}

			List<StaffAttendanceDaySummary> staffAttendanceDaySummaryList = new ArrayList<>();

			for (StaffAttendanceDayLog staffAttendanceDayLog : staffAttendanceLog.getStaffAttendanceDayLogs()) {
				int attendanceDate = staffAttendanceDayLog.getAttendanceDate();
				Double totalDuration = null;
				List<StaffTimeDuration> staffTimeDurationList = new ArrayList<>();
				List<StaffAttendanceTimeDetails> staffAttendanceTimeDetailsList = staffAttendanceDayLog.getStaffAttendanceTimeDetailsList();
				Collections.sort(staffAttendanceTimeDetailsList);
				List<List<StaffAttendanceTimeDetails>> partitions = Lists.partition(staffAttendanceTimeDetailsList, 2);
				boolean singleLogAvailable = false;
				for (List<StaffAttendanceTimeDetails> staffAttendanceTimeDetailsPartition : partitions) {
					if (staffAttendanceTimeDetailsPartition.size() == 2) {
						Time inTime = staffAttendanceTimeDetailsPartition.get(0).getTimeOfAction();
						Time outTime = staffAttendanceTimeDetailsPartition.get(1).getTimeOfAction();
						staffTimeDurationList.add(new StaffTimeDuration(inTime, outTime));

						int inTimeInSec = DateUtils.calculateDateTime(attendanceDate, inTime);
						int outTimeInSec = DateUtils.calculateDateTime(attendanceDate, outTime);
						totalDuration = NumberUtils.addValues(totalDuration, (outTimeInSec - inTimeInSec) * 1d);
					} else if (staffAttendanceTimeDetailsPartition.size() == 1) {
						singleLogAvailable = true;
						staffTimeDurationList.add(new StaffTimeDuration(staffAttendanceTimeDetailsPartition.get(0).getTimeOfAction(), null));
					}
				}

				StaffTimingDetails staffTimingDetails = staff.getStaffTimingDetails();

				StaffAttendanceStatus staffAttendanceStatus = null;
				if (totalDuration != null) {
					double halfDayDuration = staffTimingDetails == null || staffTimingDetails.getHalfDayDurationInSec() == null ? 0d : staffTimingDetails.getHalfDayDurationInSec();
					double fullDayDuration = staffTimingDetails == null || staffTimingDetails.getFullDayDurationInSec() == null ? 0d : staffTimingDetails.getFullDayDurationInSec();
					if (totalDuration >= fullDayDuration) {
						staffAttendanceStatus = StaffAttendanceStatus.PRESENT;
					} else if (totalDuration >= halfDayDuration) {
						staffAttendanceStatus = StaffAttendanceStatus.HALF_DAY;
					} else {
						staffAttendanceStatus = StaffAttendanceStatus.LEAVE;
					}
				}
				staffAttendanceDaySummaryList.add(new StaffAttendanceDaySummary(attendanceDate, totalDuration, staffAttendanceStatus,
						new StaffAttendanceDayMetadata(staffTimingDetails, staffTimeDurationList, singleLogAvailable ? StaffAttendanceType.IN : StaffAttendanceType.OUT), null));
			}
			staffAttendanceSummaryList.add(new StaffAttendanceSummary(staffId, staffAttendanceDaySummaryList));
		}
		return staffAttendanceSummaryList;
	}

	private Map<UUID, StaffAttendanceLog> getStaffAttendanceLogMap(List<StaffAttendanceLog> staffAttendanceLogList) {
		Map<UUID, StaffAttendanceLog> staffAttendanceLogMap = new HashMap<>();
		for (StaffAttendanceLog staffAttendanceLog : staffAttendanceLogList) {
			staffAttendanceLogMap.put(staffAttendanceLog.getStaffId(), staffAttendanceLog);
		}
		return staffAttendanceLogMap;
	}

	@Deprecated
	public List<String> saveStaffAttendanceDetails(int instituteId, UUID userId, StaffAttendancePayload staffAttendancePayload, boolean skipUserAuthForBiometric) {

		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if (!skipUserAuthForBiometric) {
			if (userId == null) {
				throw new ApplicationException(new ErrorResponse(
						ApplicationErrorCode.INVALID_USER, "Invalid user id."));
			}

			userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.SAVE_STAFF_ATTENDANCE);
		}

		validateStaffAttendancePayload(staffAttendancePayload);

		/**
		 *  fetching details of all the staff which we are entering data of
		 *  we will check following details while adding
		 *  if adding in-time, check the if it overlaps with any existing out-time of the day.
		 *  while adding out-time, there should be an in-time for it,
		 *  also update duration, StaffTimingDetails and attendance_status
		 */

		//Fetching all the staff ids for which we are updating timing for
		Set<UUID> staffIdList = getStaffIds(staffAttendancePayload.getStaffAttendanceStatusPayloadList());
		if (CollectionUtils.isEmpty(staffIdList)) {
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_STAFF_ATTENDANCE_DETAILS, "Invalid staff details."));
		}

		Map<UUID, StaffAttendanceDetails> staffAttendanceDetailsMap = staffAttendanceDao
				.getStaffAttendanceDetailsMap(staffAttendancePayload.getInstituteId(),
						staffAttendancePayload.getAttendanceDate(), staffIdList);

		List<String> errorList = new ArrayList<String>();

		/**
		 * validating that there should not be any out-time if nothing is there in DB
		 */
		if (staffAttendanceDetailsMap == null || CollectionUtils.isEmpty(staffAttendanceDetailsMap.entrySet())) {
			validateNoOutTime(staffAttendancePayload, errorList);
		} else {
			checkForError(staffAttendancePayload, staffAttendanceDetailsMap, errorList);
		}

		if (staffAttendancePayload == null || CollectionUtils.isEmpty(staffAttendancePayload.getStaffAttendanceStatusPayloadList())) {
			return errorList;
		}

		if (!staffAttendanceDao.saveStaffAttendanceDetails(instituteId, userId, staffAttendancePayload)) {
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_STAFF_ATTENDANCE_DETAILS, "Error occurred while adding staff attendance details."));
		}

		return errorList;
	}

	private void validateNoOutTime(StaffAttendancePayload staffAttendancePayload, List<String> errorList) {
		List<StaffAttendanceTypePayload> staffAttendanceTypePayloadList = new ArrayList<StaffAttendanceTypePayload>();
		for (StaffAttendanceTypePayload staffAttendanceTypePayload : staffAttendancePayload.getStaffAttendanceStatusPayloadList()) {
			if (!validateBasicStaffAttendanceTypeInfo(staffAttendanceTypePayload,
					staffAttendanceTypePayload.getStaffId() == null ? "NA" : staffAttendanceTypePayload.getStaffId().toString(), errorList)) {
				continue;
			}
			if (staffAttendanceTypePayload.getType() == StaffAttendanceType.OUT) {
				continue;
			}
			staffAttendanceTypePayloadList.add(staffAttendanceTypePayload);
		}
		if (CollectionUtils.isEmpty(staffAttendanceTypePayloadList)) {
			staffAttendancePayload = null;
		} else {
			staffAttendancePayload.setStaffAttendanceStatusPayloadList(staffAttendanceTypePayloadList);
		}
	}

	private void checkForError(StaffAttendancePayload staffAttendancePayload, Map<UUID, StaffAttendanceDetails> staffAttendanceDetailsMap,
							   List<String> errorList) {

		List<StaffAttendanceTypePayload> staffAttendanceTypePayloadList = new ArrayList<StaffAttendanceTypePayload>();
		for (StaffAttendanceTypePayload staffAttendanceTypePayload : staffAttendancePayload.getStaffAttendanceStatusPayloadList()) {

			StaffAttendanceDetails staffAttendanceDetails = staffAttendanceDetailsMap.get(staffAttendanceTypePayload.getStaffId());
			if (staffAttendanceDetails == null) {
				validateNoOutTime(staffAttendancePayload, errorList);
				continue;
			}

			if (staffAttendanceDetails.getStaff() == null || staffAttendanceDetails.getStaff().getStaffId() == null) {
				continue;
			}

			/**
			 * validating basic info for payload
			 */
			if (!validateBasicStaffAttendanceTypeInfo(staffAttendanceTypePayload,
					staffAttendanceDetails.getStaff().getStaffBasicInfo().getStaffInstituteId(), errorList)) {
				continue;
			}

			/**
			 * Assigning existing values to these variable so if it's an in-time case, data will not be deleted
			 */
			staffAttendanceTypePayload.setTotalDuration(staffAttendanceDetails.getTotalDuration());
			staffAttendanceTypePayload.setStaffAttendanceStatus(staffAttendanceDetails.getStaffAttendanceStatus());
			staffAttendanceTypePayload.setStaffTimingDetails(staffAttendanceDetails.getStaffTimingDetails());

			/**
			 * if adding in-time, check the if it overlaps with any existing out-time of the day.
			 */
			if (staffAttendanceTypePayload.getType() == StaffAttendanceType.IN) {
				if (!validateForInTime(staffAttendanceDetails, errorList, staffAttendanceTypePayload, staffAttendanceTypePayloadList)) {
					continue;
				}
			}

			/**
			 * while adding out-time, check there should be an in-time for it,
			 * also update duration, StaffTimingDetails and attendance_status
			 */
			if (staffAttendanceTypePayload.getType() == StaffAttendanceType.OUT) {
				if (!validateAndUpdateForOutTime(staffAttendanceDetails, errorList, staffAttendanceTypePayload, staffAttendanceTypePayloadList)) {
					continue;
				}
			}

		}
		staffAttendancePayload.setStaffAttendanceStatusPayloadList(staffAttendanceTypePayloadList);
	}

	private boolean validateBasicStaffAttendanceTypeInfo(StaffAttendanceTypePayload staffAttendanceTypePayload, String staffInstituteId, List<String> errorList) {
		if (staffAttendanceTypePayload == null) {
			errorList.add("Invalid staff id for staff : " + staffInstituteId);
			return false;
		}
		if (staffAttendanceTypePayload.getStaffId() == null) {
			errorList.add("Invalid staff id for staff : " + staffInstituteId);
			return false;
		}
		if (staffAttendanceTypePayload.getType() == null) {
			errorList.add("Invalid attendance type for staff : " + staffInstituteId);
			return false;
		}
		if (staffAttendanceTypePayload.getTimeOfAction() == null) {
			errorList.add("Invalid time for staff : " + staffInstituteId);
			return false;
		}
		return true;
	}

	private boolean validateForInTime(StaffAttendanceDetails staffAttendanceDetails, List<String> errorList,
									  StaffAttendanceTypePayload staffAttendanceTypePayload,
									  List<StaffAttendanceTypePayload> staffAttendanceTypePayloadList) {

		List<StaffAttendanceTimeDetails> staffAttendanceTimeDetailsOutList = staffAttendanceDetails.getStaffAttendanceTimeTypeMap() == null ?
				null : staffAttendanceDetails.getStaffAttendanceTimeTypeMap().get(StaffAttendanceType.OUT);

		List<StaffAttendanceTimeDetails> staffAttendanceTimeDetailsInList = staffAttendanceDetails.getStaffAttendanceTimeTypeMap() == null ?
				null : staffAttendanceDetails.getStaffAttendanceTimeTypeMap().get(StaffAttendanceType.IN);

		if (CollectionUtils.isEmpty(staffAttendanceTimeDetailsOutList) && CollectionUtils.isEmpty(staffAttendanceTimeDetailsInList)) {
			staffAttendanceTypePayloadList.add(staffAttendanceTypePayload);
			return true;
		}

		if (CollectionUtils.isEmpty(staffAttendanceTimeDetailsOutList)) {
			errorList.add("Staff : " + staffAttendanceDetails.getStaff().getStaffBasicInfo().getStaffInstituteId() + " already has an in-time.");
			return false;
		}

		if (staffAttendanceTimeDetailsOutList.size() > staffAttendanceTimeDetailsInList.size()) {
			errorList.add("Staff : " + staffAttendanceDetails.getStaff().getStaffBasicInfo().getStaffInstituteId() + " has invalid in-time, out-time details. Please contact support team.");
			return false;
		}

		StaffAttendanceTimeDetails staffAttendanceTimeDetails = staffAttendanceTimeDetailsOutList.get(0);

		if (staffAttendanceTypePayload.getTimeOfAction().compareTo(staffAttendanceTimeDetails.getTimeOfAction()) < 0) {
			errorList.add("Staff : " + staffAttendanceDetails.getStaff().getStaffBasicInfo().getStaffInstituteId() + " in-time cannot be before its out-time.");
			return false;
		}

		staffAttendanceTypePayloadList.add(staffAttendanceTypePayload);
		return true;
	}

	private boolean validateAndUpdateForOutTime(StaffAttendanceDetails staffAttendanceDetails, List<String> errorList,
												StaffAttendanceTypePayload staffAttendanceTypePayload,
												List<StaffAttendanceTypePayload> staffAttendanceTypePayloadList) {

		List<StaffAttendanceTimeDetails> staffAttendanceTimeDetailsListInTime = staffAttendanceDetails.getStaffAttendanceTimeTypeMap() == null
				? null : staffAttendanceDetails.getStaffAttendanceTimeTypeMap().get(StaffAttendanceType.IN);
		if (CollectionUtils.isEmpty(staffAttendanceTimeDetailsListInTime)) {
			errorList.add("Staff : " + staffAttendanceDetails.getStaff().getStaffBasicInfo().getStaffInstituteId() + " doesnot have any in-time.");
			return false;
		}

		List<StaffAttendanceTimeDetails> staffAttendanceTimeDetailsListOutTime =
				staffAttendanceDetails.getStaffAttendanceTimeTypeMap().get(StaffAttendanceType.OUT);

		if (CollectionUtils.isEmpty(staffAttendanceTimeDetailsListOutTime) && staffAttendanceTimeDetailsListInTime.size() == 1) {
			updateDurationStatusDetails(staffAttendanceDetails, staffAttendanceTypePayload);
			staffAttendanceTypePayloadList.add(staffAttendanceTypePayload);
			return true;
		}

		if (staffAttendanceTimeDetailsListOutTime.size() != staffAttendanceTimeDetailsListInTime.size() - 1) {
			errorList.add("Staff : " + staffAttendanceDetails.getStaff().getStaffBasicInfo().getStaffInstituteId() +
					" cannot have an out-time without a proper in-time.");
			return false;
		}

		StaffAttendanceTimeDetails staffAttendanceTimeDetails = staffAttendanceTimeDetailsListInTime.get(0);

		if (staffAttendanceTypePayload.getTimeOfAction().compareTo(staffAttendanceTimeDetails.getTimeOfAction()) < 0) {
			errorList.add("Staff : " + staffAttendanceDetails.getStaff().getStaffBasicInfo().getStaffInstituteId() + " out-time cannot be before its in-time.");
			return false;
		}

		updateDurationStatusDetails(staffAttendanceDetails, staffAttendanceTypePayload);
		staffAttendanceTypePayloadList.add(staffAttendanceTypePayload);
		return true;
	}

	private void updateDurationStatusDetails(StaffAttendanceDetails staffAttendanceDetails, StaffAttendanceTypePayload staffAttendanceTypePayload) {

		StaffAttendanceStatus staffAttendanceStatus = null;
		StaffTimingDetails staffTimingDetails = staffAttendanceDetails.getStaff().getStaffTimingDetails();

		double halfDayDuration = staffTimingDetails == null || staffTimingDetails.getHalfDayDuration() == null ? 0d : staffTimingDetails.getHalfDayDuration();
		double fullDayDuration = staffTimingDetails == null || staffTimingDetails.getFullDayDuration() == null ? 0d : staffTimingDetails.getFullDayDuration();

		double totalDuration = getTotalDuration(staffAttendanceDetails.getStaffAttendanceTimeTypeMap(), staffAttendanceTypePayload.getTimeOfAction());
		if (totalDuration >= fullDayDuration) {
			staffAttendanceStatus = StaffAttendanceStatus.PRESENT;
		} else if (totalDuration >= halfDayDuration) {
			staffAttendanceStatus = StaffAttendanceStatus.HALF_DAY;
		} else {
			staffAttendanceStatus = StaffAttendanceStatus.LEAVE;
		}

		staffAttendanceTypePayload.setTotalDuration(totalDuration);
		staffAttendanceTypePayload.setStaffAttendanceStatus(staffAttendanceStatus);
		staffAttendanceTypePayload.setStaffTimingDetails(staffAttendanceDetails.getStaff().getStaffTimingDetails());

	}

	/**
	 * @return this is assuming that inTime is always less than out-time for any given pair
	 */
	private double getTotalDuration(Time inTime, Time outTime) {

		double totalDuration = 0d;
		if (outTime.getHour() != inTime.getHour()) {
			totalDuration += ((outTime.getHour() - inTime.getHour()));
		}
		if (outTime.getMinute() != inTime.getMinute()) {
			totalDuration += ((outTime.getMinute() - inTime.getMinute()) / 60d);
		}

		if (outTime.getSecond() != inTime.getSecond()) {
			totalDuration += ((outTime.getSecond() - inTime.getSecond()) / 3600d);
		}

		return totalDuration;
	}

	private double getTotalDuration(List<Pair<Time, Time>> timingPair) {
		if (CollectionUtils.isEmpty(timingPair)) {
			return 0d;
		}
		double totalDuration = 0d;
		for (Pair<Time, Time> pair : timingPair) {
			if (pair == null || pair.getFirst() == null || pair.getSecond() == null) {
				continue;
			}
			totalDuration += getTotalDuration(pair.getFirst(), pair.getSecond());
		}
		return totalDuration;
	}

	private double getTotalDuration(Map<StaffAttendanceType, List<StaffAttendanceTimeDetails>> staffAttendanceTimeTypeMap, Time outTime) {
		if (staffAttendanceTimeTypeMap == null || CollectionUtils.isEmpty(staffAttendanceTimeTypeMap.entrySet())) {
			return 0d;
		}

		List<Pair<Time, Time>> timingPairList = new ArrayList<Pair<Time, Time>>();
		List<StaffAttendanceTimeDetails> staffAttendanceTimeDetailsInList = staffAttendanceTimeTypeMap.get(StaffAttendanceType.IN);
		List<StaffAttendanceTimeDetails> staffAttendanceTimeDetailsOutList = staffAttendanceTimeTypeMap.get(StaffAttendanceType.OUT);
		if (CollectionUtils.isEmpty(staffAttendanceTimeDetailsInList)) {
			return 0d;
		}

		timingPairList.add(new Pair<Time, Time>(staffAttendanceTimeDetailsInList.get(0).getTimeOfAction(), outTime));

		/**
		 * we are assuming that the size of in-list is always greater than 1 to than of out-list list
		 */
		for (int i = 1; i < staffAttendanceTimeDetailsInList.size(); i++) {
			timingPairList.add(new Pair<Time, Time>(staffAttendanceTimeDetailsInList.get(i).getTimeOfAction(),
					staffAttendanceTimeDetailsOutList.get(i - 1).getTimeOfAction()));
		}

		return getTotalDuration(timingPairList);
	}

	private Set<UUID> getStaffIds(List<StaffAttendanceTypePayload> staffAttendanceStatusPayloadList) {
		if (CollectionUtils.isEmpty(staffAttendanceStatusPayloadList)) {
			return null;
		}
		Set<UUID> staffIdList = new HashSet<UUID>();
		for (StaffAttendanceTypePayload staffAttendanceTypePayload : staffAttendanceStatusPayloadList) {
			staffIdList.add(staffAttendanceTypePayload.getStaffId());
		}
		return staffIdList;
	}

	private void validateStaffAttendancePayload(StaffAttendancePayload staffAttendancePayload) {
		if (staffAttendancePayload.getInstituteId() <= 0) {
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		if (staffAttendancePayload.getAttendanceDate() == null || staffAttendancePayload.getAttendanceDate() <= 0) {
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_STAFF_ATTENDANCE_DETAILS, "Invalid attendance date."));
		}
		if (CollectionUtils.isEmpty(staffAttendancePayload.getStaffAttendanceStatusPayloadList())) {
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_STAFF_ATTENDANCE_DETAILS, "Invalid staff attendance status payload."));
		}
	}

	private void validateStaffAttendancePayloadV2(StaffAttendancePayloadV2 staffAttendancePayload) {
		if (staffAttendancePayload.getAttendanceDate() == null || staffAttendancePayload.getAttendanceDate() <= 0) {
			logger.error("Invalid attendance date for {}", staffAttendancePayload);
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_STAFF_ATTENDANCE_DETAILS, "Invalid attendance date."));
		}
		if (CollectionUtils.isEmpty(staffAttendancePayload.getStaffAttendanceInputList())) {
			logger.error("Invalid staff attendance input payload for {}", staffAttendancePayload);
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_STAFF_ATTENDANCE_DETAILS, "Invalid staff attendance input payload."));
		}

		for (StaffAttendanceInput staffAttendanceInput : staffAttendancePayload.getStaffAttendanceInputList()) {
			if (staffAttendanceInput == null || staffAttendanceInput.getStaffId() == null) {
				logger.error("Invalid staff attendance input payload for {}", staffAttendancePayload);
				throw new ApplicationException(new ErrorResponse(
						ApplicationErrorCode.INVALID_STAFF_ATTENDANCE_DETAILS, "Invalid staff attendance input payload."));
			}
		}
	}

	public List<StaffAttendanceRegisterData> getStaffAttendanceRegister(int instituteId, Integer attendanceDate) {
		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		if (attendanceDate == null || attendanceDate <= 0) {
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_STAFF_ATTENDANCE_DETAILS, "Invalid attendance date."));
		}
		List<StaffAttendanceRegisterData> staffAttendanceRegisterDataList = staffAttendanceDao.getStaffAttendanceRegister(instituteId, attendanceDate, null);
		Collections.sort(staffAttendanceRegisterDataList);
		return staffAttendanceRegisterDataList;
	}

	public List<StaffAttendanceRegisterData> getStaffAttendanceRegister(int instituteId, Integer attendanceDate, Set<UUID> staffIds) {
		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		if (attendanceDate == null || attendanceDate <= 0) {
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_STAFF_ATTENDANCE_DETAILS, "Invalid attendance date."));
		}
		return staffAttendanceDao.getStaffAttendanceRegister(instituteId, attendanceDate, staffIds);
	}


	public List<StaffAttendanceRegisterData> getStaffAttendanceRegisterForRange(int instituteId, int startDate, int endDate) {
		return getStaffAttendanceRegisterForRangeWithStaffStatuses(instituteId, startDate, endDate, new HashSet<>(Arrays.asList(StaffStatus.ONBOARD)));
	}

	public List<StaffAttendanceRegisterData> getStaffAttendanceRegisterForRangeWithStaffStatuses(int instituteId, int startDate, int endDate, Set<StaffStatus> staffStatusSet) {
		return staffAttendanceDao.getStaffAttendanceRegisterForRange(instituteId, startDate, endDate, null, staffStatusSet);
	}


	@Deprecated
	public List<StaffAttendanceDetails> getStaffAttendanceDetailsList(int instituteId, Integer attendanceDate) {
		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		if (attendanceDate == null || attendanceDate <= 0) {
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_STAFF_ATTENDANCE_DETAILS, "Invalid attendance date."));
		}
		return staffAttendanceDao.getStaffAttendanceDetailsList(instituteId, attendanceDate, null);
	}

	public List<StaffAttendanceDetails> getStaffAttendanceDetailsList(int instituteId, Integer attendanceDate, Set<UUID> staffIdList) {
		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		if (attendanceDate == null || attendanceDate <= 0) {
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_STAFF_ATTENDANCE_DETAILS, "Invalid attendance date."));
		}
		return staffAttendanceDao.getStaffAttendanceDetailsList(instituteId, attendanceDate, staffIdList);
	}

	public Map<Integer, List<StaffAttendanceDetails>> getStaffAttendanceDetailsByDate(int instituteId,
																					  Integer start, Integer end) {
		final Map<Integer, List<StaffAttendanceDetails>> staffAttendanceRecords = staffAttendanceDao.
				getStaffAttendanceDetailsByDate(instituteId, start, end, null);
		if (staffAttendanceRecords == null) {
			return null;
		}


		TreeMap<Integer, List<StaffAttendanceDetails>> staffAttendanceRecordsSorted = new TreeMap<>();
		staffAttendanceRecordsSorted.putAll(staffAttendanceRecords);

		for (Map.Entry<Integer, List<StaffAttendanceDetails>> studentAttendanceRecords : staffAttendanceRecordsSorted.entrySet()) {
			Collections.sort(studentAttendanceRecords.getValue(), new Comparator<StaffAttendanceDetails>() {
				@Override
				public int compare(StaffAttendanceDetails s1, StaffAttendanceDetails s2) {
					return s1.getStaff().getStaffBasicInfo().getName()
							.compareToIgnoreCase(s2.getStaff().getStaffBasicInfo().getName());
				}
			});
		}
		return staffAttendanceRecordsSorted;
	}

	public List<StaffAttendanceDetails> getStaffAttendanceDetailsByDate(int instituteId,
                                                                     Integer start, Integer end, Set<UUID> staffIds) {
		// Retrieve the map from DAO
		final Map<Integer, List<StaffAttendanceDetails>> staffAttendanceRecords = staffAttendanceDao.
				getStaffAttendanceDetailsByDate(instituteId, start, end, staffIds);
		if (staffAttendanceRecords == null) {
			return null;
		}

		// Flatten the Map values into a single List
		List<StaffAttendanceDetails> staffAttendanceDetailsList = new ArrayList<>();
		for (List<StaffAttendanceDetails> attendanceList : staffAttendanceRecords.values()) {
			staffAttendanceDetailsList.addAll(attendanceList);
		}

		// Sort the list by staff name
		Collections.sort(staffAttendanceDetailsList, new Comparator<StaffAttendanceDetails>() {
			@Override
			public int compare(StaffAttendanceDetails s1, StaffAttendanceDetails s2) {
				return s1.getStaff().getStaffBasicInfo().getName()
						.compareToIgnoreCase(s2.getStaff().getStaffBasicInfo().getName());
			}
		});

		return staffAttendanceDetailsList;
	}

	public Map<Integer, List<StaffAttendanceRegisterDetails>> getStaffAttendanceRegisterDetailsByDate(int instituteId,
																									  List<StaffAttendanceStatus> attendanceStatusList, Integer start, Integer end) {
		final Map<Integer, List<StaffAttendanceRegisterDetails>> staffAttendanceRecords = staffAttendanceDao.
				getStaffAttendanceRegisterDetailsByDate(instituteId, attendanceStatusList, start, end);
		if (staffAttendanceRecords == null) {
			return null;
		}


		TreeMap<Integer, List<StaffAttendanceRegisterDetails>> staffAttendanceRecordsSorted = new TreeMap<>();
		staffAttendanceRecordsSorted.putAll(staffAttendanceRecords);

		for (Map.Entry<Integer, List<StaffAttendanceRegisterDetails>> studentAttendanceRecords : staffAttendanceRecordsSorted.entrySet()) {
			Collections.sort(studentAttendanceRecords.getValue(), new Comparator<StaffAttendanceRegisterDetails>() {
				@Override
				public int compare(StaffAttendanceRegisterDetails s1, StaffAttendanceRegisterDetails s2) {
					return s1.getStaff().getStaffBasicInfo().getName()
							.compareToIgnoreCase(s2.getStaff().getStaffBasicInfo().getName());
				}
			});
		}
		return staffAttendanceRecordsSorted;
	}

	public Map<Integer, List<StaffAttendanceDateData>> getStaffAttendanceDateDataMap(int instituteId, int startDate, int endDate, Set<UUID> staffCategoryIdSet,
				Set<StaffStatus> staffStatusSet) {

		List<Staff> staffList = staffDao.getStaffDetailsByCategories(instituteId, staffCategoryIdSet);

		List<StaffAttendanceRegisterData> staffAttendanceRegisterDataList = getStaffAttendanceRegisterForRangeWithStaffStatuses(
				instituteId, startDate, endDate, staffStatusSet);

		if (CollectionUtils.isEmpty(staffAttendanceRegisterDataList) || CollectionUtils.isEmpty(staffList)) {
			return null;
		}

		Pair<Integer, Integer> startEndAcademicSessionIds = getStartEndAcademicSessionIds(instituteId, startDate, endDate);
		int startAcademicSessionId = startEndAcademicSessionIds.getFirst();
		int endAcademicSessionId = startEndAcademicSessionIds.getSecond();

		Map<UUID, List<StaticHoliday>> staticHolidayMap = holidayCalendarManager.getResolvedHolidayByUserType(
				instituteId, startAcademicSessionId, UserType.STAFF);
		if(startAcademicSessionId != endAcademicSessionId) {
			Map<UUID, List<StaticHoliday>> endSessionStaticHolidayMap = holidayCalendarManager.getResolvedHolidayByUserType(
					instituteId, endAcademicSessionId, UserType.STAFF);
			EMapUtils.mergeTwoMaps(staticHolidayMap, endSessionStaticHolidayMap);
		}

		Map<UUID, StaffAttendanceRegisterData> staffAttendanceRegisterDataMap = EMapUtils.getMap(staffAttendanceRegisterDataList, new EMapUtils.MapFunction<StaffAttendanceRegisterData, UUID, StaffAttendanceRegisterData>() {
			@Override
			public UUID getKey(StaffAttendanceRegisterData entry) {
				return entry.getStaff().getStaffId();
			}
			@Override
			public StaffAttendanceRegisterData getValue(StaffAttendanceRegisterData entry) {
				return entry;
			}
		});

		Map<UUID, Staff> staffMap = EMapUtils.getMap(staffList, new EMapUtils.MapFunction<Staff, UUID, Staff>() {
			@Override
			public UUID getKey(Staff entry) {
				return entry.getStaffId();
			}
			@Override
			public Staff getValue(Staff entry) {
				return entry;
			}
		});

		return StaffAttendanceUtils.getStaffAttendanceDateDataMap(startDate, endDate,
				staffCategoryIdSet, staffStatusSet, staffMap, staffAttendanceRegisterDataMap,
				staticHolidayMap);
	}

	private Pair<Integer, Integer> getStartEndAcademicSessionIds(int instituteId, int startDate, int endDate) {
		List<AcademicSession> academicSessionList = instituteManager.getAcademicSessionList(instituteId);

		int startAcademicSessionId = 0;
		int endAcademicSessionId = 0;
		// Loop through the academic sessions to find the matching IDs
		for (AcademicSession academicSession : academicSessionList) {
			if (startAcademicSessionId == 0 &&
					DateUtils.dateLieBetweenTwoDates(academicSession.getPayrollSessionStartTime(), academicSession.getPayrollSessionEndTime(), startDate)) {
				startAcademicSessionId = academicSession.getAcademicSessionId();
			}
			if (endAcademicSessionId == 0 &&
					DateUtils.dateLieBetweenTwoDates(academicSession.getPayrollSessionStartTime(), academicSession.getPayrollSessionEndTime(), endDate)) {
				endAcademicSessionId = academicSession.getAcademicSessionId();
			}
			// Break early if both IDs are found
			if (startAcademicSessionId != 0 && endAcademicSessionId != 0) {
				break;
			}
		}
		return new Pair<>(startAcademicSessionId, endAcademicSessionId);
	}

	public List<StaffAttendanceDateData> getStaffAttendanceDateDataList(int instituteId, int date, Set<UUID> staffCategoryIdSet, Set<StaffStatus> staffStatusSet) {
		int startDate = DateUtils.getDayStart(date, DateUtils.DEFAULT_TIMEZONE);
		int endDate = DateUtils.getDayEnd(date, DateUtils.DEFAULT_TIMEZONE);
		Map<Integer, List<StaffAttendanceDateData>> staffAttendanceDateDataMap = getStaffAttendanceDateDataMap(
				instituteId, startDate, endDate, staffCategoryIdSet, staffStatusSet);
		return CollectionUtils.isEmpty(staffAttendanceDateDataMap) ? null : staffAttendanceDateDataMap.get(startDate);
	}

}
