package com.lernen.cloud.core.lib.templates;

import com.embrate.cloud.dao.tier.service.communication.templates.CommunicationTemplateDao;
import org.apache.commons.codec.digest.DigestUtils;

import com.lernen.cloud.core.api.common.DeliveryMode;
import com.embrate.cloud.core.api.service.communication.templates.TemplateType;
import com.embrate.cloud.core.api.service.communication.templates.CommunicationTemplate;

/**
 * Class to manage velocity template creation and storage.
 *
 * <AUTHOR>
 *
 */
public class VelocityTemplateVersionManager {

	final CommunicationTemplateDao communicationTemplateDao;

	public VelocityTemplateVersionManager(CommunicationTemplateDao communicationTemplateDao) {
		this.communicationTemplateDao = communicationTemplateDao;
	}

	/**
	 * Method to update template version in the database. This method gets the
	 * latest template,compares the Md5Hash with the existing template, if the
	 * values are matching then do nothing and if values don't match then insert
	 * a new row with the incremented version.
	 * @return
	 */
	public boolean updateTemplates(TemplateType templateType, String templateName, String entityID, String locale,
			String templateValue, DeliveryMode deliveryMode) {
//		final CommunicationTemplate latestTemplate = communicationTemplateDao.getLatestTemplate(templateType, templateName,
//				entityID, locale, deliveryMode);
//		final String md5Hex = DigestUtils.md5Hex(templateValue);
//		if (latestTemplate == null) {
//			return communicationTemplateDao.addTemplate(new CommunicationTemplate(templateType, 1, md5Hex, templateValue,
//					templateName, entityID, locale, deliveryMode));
//		}
//
//		if (!md5Hex.equals(latestTemplate.getMd5Hex())) {
//			final int version = latestTemplate.getVersion() + 1;
//			return communicationTemplateDao.addTemplate(new CommunicationTemplate(templateType, version, md5Hex,
//					templateValue, templateName, entityID, locale, deliveryMode));
//		}
		return false;
	}
}
