//package com.lernen.cloud.core.lib.inventory.ingest;
//
//import java.io.BufferedReader;
//import java.io.FileNotFoundException;
//import java.io.FileReader;
//import java.io.IOException;
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.HashSet;
//import java.util.List;
//import java.util.Map;
//import java.util.Map.Entry;
//import java.util.Set;
//import java.util.UUID;
//
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.context.ApplicationContext;
//import org.springframework.context.support.ClassPathXmlApplicationContext;
//
//import com.lernen.cloud.core.api.inventory.BrandInfo;
//import com.lernen.cloud.core.api.inventory.Category;
//import com.lernen.cloud.core.api.inventory.NewProductRequest;
//import com.lernen.cloud.core.api.inventory.ProductDetails;
//import com.lernen.cloud.core.api.inventory.ProductProperties;
//import com.lernen.cloud.core.api.inventory.ProductVariationsGroup;
//import com.lernen.cloud.core.api.inventory.UpdateProductRequest;
//import com.lernen.cloud.core.api.inventory.UserGroup;
//import com.lernen.cloud.core.api.user.Gender;
//import com.lernen.cloud.core.lib.inventory.SKUGenerator;
//import com.lernen.cloud.core.lib.inventory.StoreInventoryManager;
//
///**
// * 
// * <AUTHOR>
// *
// */
//public class StockIngester {
//
//	private static final String CSV_DELIMITER = ",";
//	private static final String NAME_ID_DELIMITER = "|";
//
//	private final StoreInventoryManager storeInventoryManager;
//
//	public StockIngester(StoreInventoryManager storeInventoryManager) {
//		this.storeInventoryManager = storeInventoryManager;
//	}
//
//	public static void main(String args[]) {
//		final ApplicationContext context = new ClassPathXmlApplicationContext("core-lib.xml");
//		StockIngester importExistingStock = new StockIngester(context.getBean(StoreInventoryManager.class));
//		importExistingStock.ingestFile(1, "/Users/<USER>/Lernen/lernen-backend/test.csv");
//	}
//
//	public boolean ingestFile(int instituteId, String fileName) {
//		String csvFile = fileName;
//		System.out.println("Readin file " + fileName);
//		Map<String, ProductVariationsGroup> productVariationMap = new HashMap<>();
//		Map<String, ProductStockInformation> productDetailsMap = new HashMap<>();
//		Set<Category> categories = new HashSet<>();
//		Map<String, BrandData> brandMap = new HashMap<>();
//		try (BufferedReader br = new BufferedReader(new FileReader(csvFile))) {
//			String line = "";
//			while ((line = br.readLine()) != null) {
//				String[] params = line.split(CSV_DELIMITER);
//				if (params.length == 0) {
//					System.out.println("Invalid csv");
//					return false;
//				}
//				List<String> attributes = format(params);
//				ProductProperties productProperties = getProductProperties(attributes);
//				Category category = getCategory(instituteId, productProperties);
//				categories.add(category);
//
//				if (!brandMap.containsKey(productProperties.getBrand())) {
//					brandMap.put(productProperties.getBrand(),
//							new BrandData(productProperties.getBrand(), new HashSet<>()));
//
//				}
//				brandMap.get(productProperties.getBrand()).getCategoryNames().add(category.getCategoryName());
//
//				String nameId = createNameId(productProperties);
//				if (!productVariationMap.containsKey(nameId)) {
//					productVariationMap.put(nameId, new ProductVariationsGroup(productProperties.getProductName(),
//							productProperties.getCategory(), productProperties.getBrand()));
//				}
//				ProductVariationsGroup productVariationsGroup = productVariationMap.get(nameId);
//				updateProductVariationsGroup(productVariationsGroup, productProperties);
//
//				String productId = createProductId(productProperties);
//				if (productDetailsMap.containsKey(productId)) {
//					System.out.println(productId + " already exists");
//					return false;
//				}
//				productDetailsMap.put(productId, new ProductStockInformation(Integer.parseInt(attributes.get(7)),
//						Double.parseDouble(attributes.get(9)), Double.parseDouble(attributes.get(10))));
//			}
//
//			return importProducts(instituteId, productVariationMap, productDetailsMap, categories, brandMap);
//
//		} catch (FileNotFoundException e) {
//			e.printStackTrace();
//		} catch (IOException e) {
//			e.printStackTrace();
//		}
//		return false;
//	}
//
//	private Category getCategory(int instituteId, ProductProperties productProperties) {
//		String categoryName = productProperties.getCategory().replaceAll(" +", Category.FILLER);
//		return new Category(instituteId, categoryName);
//	}
//
//	private boolean importProducts(int instituteId, Map<String, ProductVariationsGroup> productVariationMap,
//			Map<String, ProductStockInformation> productDetailsMap, Set<Category> categories,
//			Map<String, BrandInfo> brandMap) {
//		boolean result = true;
//		System.out.println(productVariationMap);
//		System.out.println("productVariationMap size " + productVariationMap.size());
//		System.out.println(productDetailsMap);
//		System.out.println("productDetailsMap " + productDetailsMap.size());
//		System.out.println(categories);
//		System.out.println(brandMap);
//		System.out.println("Total categories " + categories.size());
//		for (Category category : categories) {
//			result = result & storeInventoryManager.addCategory(category);
//		}
//
//		System.out.println("Total brands " + brandMap.size());
//		for (BrandInfo brand : brandMap.values()) {
//			result = result & storeInventoryManager.addBrand(brand);
//		}
//
//		List<UUID> productIds = new ArrayList<>();
//		for (Entry<String, ProductVariationsGroup> entry : productVariationMap.entrySet()) {
//			NewProductRequest newProductRequest = new NewProductRequest(instituteId, entry.getValue());
//			UUID productId = storeInventoryManager.addNewProduct(newProductRequest);
//			if (productId == null) {
//				return false;
//			}
//			productIds.add(productId);
//		}
//		System.out.println("Total productIds " + productIds.size());
//		int currentTime = (int) (System.currentTimeMillis() / 1000l);
//		for (UUID productId : productIds) {
//			List<ProductDetails> productDetailsList = storeInventoryManager.getProductsById(instituteId, productId);
//			for (ProductDetails productDetails : productDetailsList) {
//				ProductProperties productProperties = new ProductProperties(null, productDetails.getGeneralName(),
//						productDetails.getCategory(), productDetails.getBrand(), productDetails.getSize(),
//						productDetails.getColor(), productDetails.getUserGroup(), productDetails.getGender());
//				String productNameId = createProductId(productProperties);
//				if (productDetailsMap.containsKey(productNameId)) {
//					ProductStockInformation productStockInformation = productDetailsMap.get(productNameId);
//					UpdateProductRequest updateProductRequest = new UpdateProductRequest(instituteId,
//							productDetails.getSkuId(), productStockInformation.getSellingPrice(),
//							productStockInformation.getDiscount(), productStockInformation.getQuantity(), currentTime,
//							null);
//					result = result & storeInventoryManager.updateProduct(updateProductRequest);
//				}
//			}
//		}
//		return result;
//
//	}
//
//	private void updateProductVariationsGroup(ProductVariationsGroup productVariationsGroup,
//			ProductProperties productProperties) {
//		if (StringUtils.isNotBlank(productProperties.getSize())) {
//			productVariationsGroup.getSizes().add(productProperties.getSize());
//		}
//
//		if (StringUtils.isNotBlank(productProperties.getColor())) {
//			productVariationsGroup.getColors().add(productProperties.getColor());
//		}
//
//		if (productProperties.getUserGroup() != null) {
//			productVariationsGroup.getUserGroups().add(productProperties.getUserGroup());
//		}
//
//		if (productProperties.getGender() != null) {
//			productVariationsGroup.getGenders().add(productProperties.getGender());
//		}
//
//	}
//
//	private List<String> format(String[] attributes) {
//		List<String> attributeList = new ArrayList<>();
//		for (String attribute : attributes) {
//			attributeList.add(attribute.trim().toLowerCase());
//		}
//		return attributeList;
//	}
//
//	private String createNameId(ProductProperties productProperties) {
//		StringBuilder id = new StringBuilder();
//		id.append(productProperties.getProductName()).append(NAME_ID_DELIMITER).append(productProperties.getCategory())
//				.append(NAME_ID_DELIMITER).append(productProperties.getBrand());
//		return id.toString();
//	}
//
//	private ProductProperties getProductProperties(List<String> attributes) {
//		UserGroup userGroup = StringUtils.isBlank(attributes.get(5)) ? null
//				: UserGroup.valueOf(attributes.get(5).toUpperCase());
//		// System.out.println(attributes.get(6));
//		Gender gender = StringUtils.isBlank(attributes.get(6)) ? null : Gender.valueOf(attributes.get(6).toUpperCase());
//		return new ProductProperties(null, attributes.get(0), attributes.get(1), attributes.get(2), attributes.get(3),
//				attributes.get(4), userGroup, gender);
//	}
//
//	private String createProductId(ProductProperties productProperties) {
//		return SKUGenerator.generateProductFullName(productProperties) + NAME_ID_DELIMITER
//				+ productProperties.getCategory() + NAME_ID_DELIMITER + productProperties.getBrand();
//	}
//
//	class ProductStockInformation {
//		private final int quantity;
//		private final double sellingPrice;
//		private final double discount;
//
//		public ProductStockInformation(int quantity, double sellingPrice, double discount) {
//			this.quantity = quantity;
//			this.sellingPrice = sellingPrice;
//			this.discount = discount;
//		}
//
//		public int getQuantity() {
//			return quantity;
//		}
//
//		public double getSellingPrice() {
//			return sellingPrice;
//		}
//
//		public double getDiscount() {
//			return discount;
//		}
//
//		@Override
//		public String toString() {
//			return "ProductStockInformation [quantity=" + quantity + ", sellingPrice=" + sellingPrice + ", discount="
//					+ discount + "]";
//		}
//
//	}
//
//	private class BrandData {
//		
//		String brandName;
//		Set<String> categoryNames;
//
//		public BrandData(String brandName, Set<String> categoryNames) {
//			this.brandName = brandName;
//			this.categoryNames = categoryNames;
//		}
//
//		public String getBrandName() {
//			return brandName;
//		}
//
//		public Set<String> getCategoryNames() {
//			return categoryNames;
//		}
//
//	}
//}
