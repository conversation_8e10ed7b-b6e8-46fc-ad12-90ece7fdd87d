package com.lernen.cloud.core.lib.inventory;

import java.util.HashSet;
import java.util.Set;

import com.lernen.cloud.core.api.inventory.ProductProperties;

/**
 * 
 * <AUTHOR>
 *
 */
public class SKUNode {
	
	private final SKUComponent skuComponent;
	
	private final String value;
	
	private final String componentName;
	
	private boolean skipNode;
	
	private ProductProperties productProperties;
	
	private Set<SKUNode> children = new HashSet<SKUNode>();


	public SKUNode(SKUComponent skuComponent, String value, String componentName,Set<SKUNode> children) {
		this.skuComponent = skuComponent;
		this.value = value;
		this.componentName = componentName;
		this.children = children;
	}


	public SKUNode(SKUComponent skuComponent, String value, String componentName) {
		this.skuComponent = skuComponent;
		this.value = value;
		this.componentName = componentName;
	}


	public SKUNode(SKUComponent skuComponent, boolean skipNode, Set<SKUNode> children) {
		this.skuComponent = skuComponent;
		this.value = null;
		this.componentName =  null;
		this.skipNode = skipNode;
		this.children = children;
	}


	public SKUComponent getSkuComponent() {
		return skuComponent;
	}


	public String getValue() {
		return value;
	}


	public Set<SKUNode> getChildren() {
		return children;
	}


	public void setChildren(Set<SKUNode> children) {
		this.children = children;
	}
	
	public void addChild(SKUNode child) {
		this.children.add(child);
	}
	
	public void addChildren(Set<SKUNode> children) {
		this.children.addAll(children);
	}
	

	public boolean skipNode() {
		return skipNode;
	}


	public void setSkipNode(boolean skipNode) {
		this.skipNode = skipNode;
	}


	public String getComponentName() {
		return componentName;
	}

	public ProductProperties getProductProperties() {
		return productProperties;
	}


	public void setProductProperties(ProductProperties productProperties) {
		this.productProperties = productProperties;
	}
	
	
}
