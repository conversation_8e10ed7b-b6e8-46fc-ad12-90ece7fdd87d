package com.embrate.cloud.core.lib.filesystem;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.*;
import com.lernen.cloud.core.api.common.FileData;
import com.lernen.cloud.core.utils.aws.s3.S3ClientProvider;
import org.apache.commons.io.IOUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;

/**
 *
 * <AUTHOR>
 *
 */
public class S3FileSystem {

	private static final Logger logger = LogManager.getLogger(S3FileSystem.class);

	private final AmazonS3 s3client;
	private final boolean s3AccessEnable;

	public S3FileSystem(S3ClientProvider s3ClientProvider, boolean s3AccessEnable) {
		this.s3client = s3ClientProvider.getS3client();
		this.s3AccessEnable = s3AccessEnable;
	}

	public boolean writeFile(String bucketName, String path, FileData fileData) {
		if (!s3AccessEnable) {
			logger.error("S3 access is not enabled. Skipping writing...");
			return false;
		}
		try {
			final ByteArrayInputStream fileStream = new ByteArrayInputStream(fileData.getContent());
			final ObjectMetadata objectMetadata = new ObjectMetadata();
			objectMetadata.setContentLength(fileData.getContent().length);

			final PutObjectResult result = s3client
					.putObject(new PutObjectRequest(bucketName, path, fileStream, objectMetadata));
			return result != null;
		} catch (final Exception e) {
			logger.error("Exception while uploading file for bucketName {}, path {}", bucketName, path, e);
		}

		return false;
	}

	public ByteArrayOutputStream readFile(String bucketName, String path) {
		if (!s3AccessEnable) {
			logger.error("S3 access is not enabled. Skipping reading...");
			return null;
		}

		try {
			final S3Object object = s3client.getObject(bucketName, path);

			final ByteArrayOutputStream bos = new ByteArrayOutputStream();

			IOUtils.copy(object.getObjectContent(), bos);
			return bos;
		} catch (final Exception e) {
			logger.error("Exception while downloading document for bucketName {}, path {}", bucketName, path, e);
		}
		return null;
	}

	public boolean deleteFile(String bucketName, String path) {
		if (!s3AccessEnable) {
			return false;
		}

		try {
			final DeleteObjectsResult deleteObjectsResult = s3client
					.deleteObjects(new DeleteObjectsRequest(bucketName).withKeys(path));
			return deleteObjectsResult != null;
		} catch (final Exception e) {
			logger.error("Exception while deleting document for bucketName {}, path {}", bucketName, path, e);
		}

		return false;
	}
}
