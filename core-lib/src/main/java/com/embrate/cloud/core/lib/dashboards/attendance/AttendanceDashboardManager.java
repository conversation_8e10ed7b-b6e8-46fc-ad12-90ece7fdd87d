package com.embrate.cloud.core.lib.dashboards.attendance;

import com.embrate.cloud.core.api.dashboards.admission.InstituteStaffCountByGender;
import com.embrate.cloud.core.api.dashboards.admission.StaffCountByGender;
import com.embrate.cloud.core.api.dashboards.attendance.*;
import com.embrate.cloud.core.api.dashboards.common.InstituteValue;
import com.embrate.cloud.core.utils.EMapUtils;
import com.embrate.cloud.dao.tier.dashboard.admission.UserDashboardDao;
import com.embrate.cloud.dao.tier.dashboard.attendance.AttendanceDashboardDao;
import com.lernen.cloud.core.api.attendance.AttendanceStatus;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.student.StudentSessionSummary;
import com.lernen.cloud.core.lib.attendance.AttendanceManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.utils.Pair;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

import static com.embrate.cloud.core.utils.institute.DashboardUtils.*;

/**
 * <AUTHOR>
 **/
public class AttendanceDashboardManager {

	private static final Logger logger = LogManager.getLogger(AttendanceDashboardManager.class);

	private final InstituteManager instituteManager;

	private final UserDashboardDao userDashboardDao;

	private final AttendanceDashboardDao attendanceDashboardDao;

	private final AttendanceManager attendanceManager;

	public AttendanceDashboardManager(InstituteManager instituteManager, UserDashboardDao userDashboardDao, AttendanceDashboardDao attendanceDashboardDao, AttendanceManager attendanceManager) {
		this.instituteManager = instituteManager;
		this.userDashboardDao = userDashboardDao;
		this.attendanceDashboardDao = attendanceDashboardDao;
		this.attendanceManager = attendanceManager;
	}

	public StudentSummaryWithAttendanceOrgStats getStudentSummaryWithAttendanceOrgStats(UUID organizationId, String selectedInstituteIds,
																						UUID userId, int date) {
		validatePayload(organizationId, selectedInstituteIds, userId, date, date);

		List<Integer> institutes = getInstituteIds(selectedInstituteIds);
		Map<Integer, String> instituteNameMap = getInstituteBranchNameMap(instituteManager.getInstitutes(institutes));
		Map<Integer, List<AcademicSession>> instituteSessionMap = instituteManager.getInstituteAcademicSessionMap(institutes);
		Set<Integer> requiredAcademicSessions = getRequiredSessions(instituteSessionMap, date, date);

		List<AttendanceStatus> requiredAttendanceStatus = Arrays.asList(AttendanceStatus.PRESENT, AttendanceStatus.LEAVE);
		// Get enrolled students count for total students calculation
		List<StudentSessionSummary> sessionSummaryList = userDashboardDao.getStudentCountBySessionStatus(institutes, requiredAcademicSessions);
		List<InstituteValue> studentCountList = new ArrayList<>();

		int totalStudentCount = 0;
		for (StudentSessionSummary studentSessionSummary : sessionSummaryList) {
			totalStudentCount += studentSessionSummary.getStudentCount();
			studentCountList.add(new InstituteValue(studentSessionSummary.getInstituteId(), instituteNameMap.get(studentSessionSummary.getInstituteId()), studentSessionSummary.getStudentCount()));
		}

		// TODO: Add caching
		List<String> distinctAttendanceTypes = attendanceManager.getDistinctRegularAttendanceTypes(institutes, requiredAcademicSessions);
		if (CollectionUtils.isEmpty(distinctAttendanceTypes)) {
			logger.warn("No attendance types found for institutes {} and sessions {}", institutes, requiredAcademicSessions);
			return new StudentSummaryWithAttendanceOrgStats(studentCountList, totalStudentCount, new ArrayList<>());
		}

		Map<Integer, Integer> instituteTotalCount = getInstituteTotalCount(sessionSummaryList);
		logger.debug("instituteTotalCount {}", instituteTotalCount);
		// Get attendance data for the end date (most recent date in range)
		List<StudentAttendanceCount> studentAttendanceCountList = attendanceDashboardDao.getStudentAttendanceCountByDate(institutes, requiredAcademicSessions, date);
		logger.debug("studentAttendanceCountList {}", studentAttendanceCountList);
		Map<String, Map<Integer, Map<AttendanceStatus, Integer>>> attendanceMap = new HashMap<>();
		for (StudentAttendanceCount studentAttendanceCount : studentAttendanceCountList) {
			populateAttendanceMap(studentAttendanceCount, attendanceMap);
		}
		List<AttendanceTypeOrgCounts> attendanceTypeOrgCounts = new ArrayList<>();
		for (String attendanceType : distinctAttendanceTypes) {
			populateAttendanceTypeEntry(attendanceType, institutes, attendanceMap, instituteTotalCount, attendanceTypeOrgCounts, requiredAttendanceStatus, instituteNameMap);
		}

		return new StudentSummaryWithAttendanceOrgStats(studentCountList, totalStudentCount, attendanceTypeOrgCounts);
	}

	public StaffSummaryWithAttendanceOrgStats getStaffSummaryWithAttendanceOrgStats(UUID organizationId, String selectedInstituteIds,
																					UUID userId, int date) {
		validatePayload(organizationId, selectedInstituteIds, userId, date, date);

		List<Integer> institutes = getInstituteIds(selectedInstituteIds);
		Map<Integer, String> instituteNameMap = getInstituteBranchNameMap(instituteManager.getInstitutes(institutes));
		List<InstituteValue> staffCountList = new ArrayList<>();
		List<AttendanceStatus> requiredAttendanceStatus = Arrays.asList(AttendanceStatus.PRESENT, AttendanceStatus.LEAVE);

		// get staff count by gender
		int totalStaffCount = 0;
		List<StaffCountByGender> staffCountByGenderList = userDashboardDao.getStaffCountByGender(institutes);
		Map<Integer, Integer> instituteStaffCount = new HashMap<>();
		List<InstituteStaffCountByGender> instituteStaffCountByGenders = new ArrayList<>();
		for (StaffCountByGender staffCountByGender : staffCountByGenderList) {
			instituteStaffCountByGenders.add(new InstituteStaffCountByGender(staffCountByGender.getInstituteId(), instituteNameMap.get(staffCountByGender.getInstituteId()), staffCountByGender.getGender(), staffCountByGender.getCount()));
			totalStaffCount += staffCountByGender.getCount();
			if (!instituteStaffCount.containsKey(staffCountByGender.getInstituteId())) {
				instituteStaffCount.put(staffCountByGender.getInstituteId(), 0);
			}
			instituteStaffCount.put(staffCountByGender.getInstituteId(), instituteStaffCount.get(staffCountByGender.getInstituteId()) + staffCountByGender.getCount());
		}

		// get staff attendance
		List<StaffAttendanceCount> staffAttendanceCountList = attendanceDashboardDao.getStaffAttendanceCountByDate(institutes, date);
		Map<Integer, Map<AttendanceStatus, Integer>> staffAttendanceMap = new HashMap<>();
		Map<AttendanceStatus, Integer> orgAttendanceStatusMap = new HashMap<>();

		for (StaffAttendanceCount staffAttendanceCount : staffAttendanceCountList) {
			if (!staffAttendanceMap.containsKey(staffAttendanceCount.getInstituteId())) {
				staffAttendanceMap.put(staffAttendanceCount.getInstituteId(), new HashMap<>());
			}
			staffAttendanceMap.get(staffAttendanceCount.getInstituteId()).put(staffAttendanceCount.getAttendanceStatus(), staffAttendanceCount.getCount());
			if (!orgAttendanceStatusMap.containsKey(staffAttendanceCount.getAttendanceStatus())) {
				orgAttendanceStatusMap.put(staffAttendanceCount.getAttendanceStatus(), 0);
			}
			orgAttendanceStatusMap.put(staffAttendanceCount.getAttendanceStatus(), orgAttendanceStatusMap.get(staffAttendanceCount.getAttendanceStatus()) + staffAttendanceCount.getCount());
		}

		int totalCount = 0;
		List<InstituteAttendanceStats> instituteStats = new ArrayList<>();
		for (Integer institute : institutes) {
			Map<AttendanceStatus, Integer> attendanceStatusMap = staffAttendanceMap.get(institute);
			List<AttendanceCount> staffInstituteAttendanceCountList = new ArrayList<>();
			int staffCount = instituteStaffCount.get(institute);
			staffCountList.add(new InstituteValue(institute, instituteNameMap.get(institute), staffCount));
			totalCount += staffCount;
			for (AttendanceStatus attendanceStatus : requiredAttendanceStatus) {
				if (attendanceStatusMap == null || !attendanceStatusMap.containsKey(attendanceStatus)) {
					staffInstituteAttendanceCountList.add(new AttendanceCount(attendanceStatus, 0, staffCount));
				} else {
					staffInstituteAttendanceCountList.add(new AttendanceCount(attendanceStatus, attendanceStatusMap.get(attendanceStatus), staffCount));
				}
			}
			instituteStats.add(new InstituteAttendanceStats(institute, instituteNameMap.get(institute), staffInstituteAttendanceCountList));
		}
		List<AttendanceCount> orgAttendanceCounts = new ArrayList<>();
		for (AttendanceStatus attendanceStatus : requiredAttendanceStatus) {
			Integer attendanceCount = orgAttendanceStatusMap.get(attendanceStatus);
			orgAttendanceCounts.add(new AttendanceCount(attendanceStatus, attendanceCount == null ? 0 : attendanceCount, totalCount));
		}

		AttendanceTypeOrgCounts staffAttendanceTypeOrgCounts = new AttendanceTypeOrgCounts("Attendance", instituteStats, orgAttendanceCounts);
		return new StaffSummaryWithAttendanceOrgStats(staffCountList, instituteStaffCountByGenders, totalStaffCount, Arrays.asList(staffAttendanceTypeOrgCounts));
	}

	private static void populateAttendanceMap(StudentAttendanceCount studentAttendanceCount, Map<String, Map<Integer, Map<AttendanceStatus, Integer>>> attendanceMap) {
		int instituteId = studentAttendanceCount.getInstituteId();
		int academicSessionId = studentAttendanceCount.getAcademicSessionId();
		String attendanceTypeName = studentAttendanceCount.getAttendanceTypeName();
		AttendanceStatus attendanceStatus = studentAttendanceCount.getAttendanceStatus();
		int count = studentAttendanceCount.getCount();

		if (!attendanceMap.containsKey(attendanceTypeName)) {
			attendanceMap.put(attendanceTypeName, new HashMap<>());
		}
		if (!attendanceMap.get(attendanceTypeName).containsKey(instituteId)) {
			attendanceMap.get(attendanceTypeName).put(instituteId, new HashMap<>());
		}

		Map<AttendanceStatus, Integer> attendanceStatusMap = attendanceMap.get(attendanceTypeName).get(instituteId);
		if (attendanceStatusMap.containsKey(attendanceStatus)) {
			attendanceStatusMap.put(attendanceStatus, attendanceStatusMap.get(attendanceStatus) + count);
		} else {
			attendanceStatusMap.put(attendanceStatus, count);
		}
	}

	private static void populateAttendanceTypeEntry(String attendanceTypeName, List<Integer> institutes,
													Map<String, Map<Integer, Map<AttendanceStatus, Integer>>> attendanceTypeMap,
													Map<Integer, Integer> instituteTotalCount,
													List<AttendanceTypeOrgCounts> attendanceTypeOrgCounts, List<AttendanceStatus> requiredAttendanceStatus,
													Map<Integer, String> instituteNameMap) {
		Map<Integer, Map<AttendanceStatus, Integer>> attendanceTypeNameMap = attendanceTypeMap.get(attendanceTypeName);
		List<InstituteAttendanceStats> instituteStats = new ArrayList<>();
		Map<AttendanceStatus, Pair<Integer, Integer>> orgAttendanceStatusMap = new HashMap<>();

		for (Integer institute : institutes) {
			populateInstituteEntry(institute, instituteTotalCount, attendanceTypeNameMap == null ? null : attendanceTypeNameMap.get(institute), orgAttendanceStatusMap, instituteStats, requiredAttendanceStatus, instituteNameMap);
		}
		List<AttendanceCount> orgAttendanceCounts = new ArrayList<>();
		for (AttendanceStatus attendanceStatus : requiredAttendanceStatus) {
			Pair<Integer, Integer> attendanceCountPair = orgAttendanceStatusMap.get(attendanceStatus);
			orgAttendanceCounts.add(new AttendanceCount(attendanceStatus, attendanceCountPair == null ? 0 : attendanceCountPair.getFirst(), attendanceCountPair == null ? 0 : attendanceCountPair.getSecond()));
		}
		attendanceTypeOrgCounts.add(new AttendanceTypeOrgCounts(attendanceTypeName, instituteStats, orgAttendanceCounts));
	}

	private static void populateInstituteEntry(int instituteId, Map<Integer, Integer> instituteTotalCount,
											   Map<AttendanceStatus, Integer> instituteEntry, Map<AttendanceStatus,
					Pair<Integer, Integer>> orgAttendanceStatusMap, List<InstituteAttendanceStats> instituteStats,
											   List<AttendanceStatus> requiredAttendanceStatus,
											   Map<Integer, String> instituteNameMap) {
		List<AttendanceCount> instAttendanceCounts = new ArrayList<>();
		int totalCount = instituteTotalCount.get(instituteId);
		for (AttendanceStatus attendanceStatus : requiredAttendanceStatus) {
			int count = instituteEntry == null ? 0 : instituteEntry.getOrDefault(attendanceStatus, 0);
			instAttendanceCounts.add(new AttendanceCount(attendanceStatus, count, totalCount));
			if (orgAttendanceStatusMap.containsKey(attendanceStatus)) {
				Pair<Integer, Integer> orgAttendanceCount = orgAttendanceStatusMap.get(attendanceStatus);
				orgAttendanceStatusMap.put(attendanceStatus, new Pair<>(orgAttendanceCount.getFirst() + count, orgAttendanceCount.getSecond() + totalCount));
			} else {
				orgAttendanceStatusMap.put(attendanceStatus, new Pair<>(count, totalCount));
			}
		}
		instituteStats.add(new InstituteAttendanceStats(instituteId, instituteNameMap.get(instituteId), instAttendanceCounts));
	}

	private static Map<Integer, Integer> getInstituteTotalCount(List<StudentSessionSummary> sessionSummaryList) {
		Map<Integer, Integer> instituteTotalCount = EMapUtils.getMap(sessionSummaryList, new EMapUtils.MapFunction<StudentSessionSummary, Integer, Integer>() {
			@Override
			public Integer getKey(StudentSessionSummary entry) {
				return entry.getInstituteId();
			}

			@Override
			public Integer getValue(StudentSessionSummary entry) {
				return entry.getStudentCount();
			}
		});
		return instituteTotalCount;
	}
}