package com.embrate.cloud.core.lib.payment;

import com.embrate.cloud.core.api.payment.PaymentApplicableDiscount;
import com.embrate.cloud.core.api.payment.PaymentDiscountType;
import com.embrate.cloud.core.api.payment.PaymentRequest;
import com.lernen.cloud.core.api.fee.discount.FeeDiscountAssignmentDetails;
import com.lernen.cloud.core.api.fees.FeeConfigurationResponse;
import com.lernen.cloud.core.lib.fees.configuration.FeeConfigurationManager;
import com.lernen.cloud.core.lib.fees.configuration.FeeDiscountConfigurationManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.dao.tier.fees.configuration.FeeConfigurationDao;
import com.lernen.cloud.dao.tier.fees.configuration.FeeDiscountConfigurationDao;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 *
 * Rule for tuition Fee online payment:-
 * 1) Instant discount of Rs 50 if fees is paid before due date (No discount if payment made on due date)
 * 2) In case of multiple monthly fees payment; discount amount will be multiplied by the number of fees paid before due date
 * 3) Instant discount will not be applicable if any pre configured discount is already applied on such fee.
 * 4) Discount will be applicable on following fees:- April 2023, May 2023, June 2023, July 2023, August 2023, September 2023, October 2023, November 2023, December 2023, January 2024, February 2024, March 2024
 * Note : If any Fee name is changed after applicability of rule, then in such cases discount will not be applied.
 *
 * 
 * <AUTHOR>
 */

public class PaymentDiscountCalculator10205 implements IPaymentDiscountCalculator {

    private static final Logger logger = LogManager.getLogger(PaymentDiscountCalculator10205.class);

    private final FeeConfigurationDao feeConfigurationDao;

    private final FeeDiscountConfigurationDao feeDiscountConfigurationDao;

    private static final double INSTANT_DISCOUNT_AMOUNT_BEFORE_DUE_DATE_PAYMENT = 50d;
    public static final Set<String> ALLOWED_FEES_NAME = new HashSet<>();

    static {

        //23-24 session
        ALLOWED_FEES_NAME.add(formatToLower("APRIL 2023"));
        ALLOWED_FEES_NAME.add(formatToLower("MAY 2023"));
        ALLOWED_FEES_NAME.add(formatToLower("JUNE 2023"));
        ALLOWED_FEES_NAME.add(formatToLower("JULY 2023"));
        ALLOWED_FEES_NAME.add(formatToLower("AUGUST 2023"));
        ALLOWED_FEES_NAME.add(formatToLower("SEPTEMBER 2023"));
        ALLOWED_FEES_NAME.add(formatToLower("OCTOBER 2023"));
        ALLOWED_FEES_NAME.add(formatToLower("NOVEMBER 2023"));
        ALLOWED_FEES_NAME.add(formatToLower("DECEMBER 2023"));
        ALLOWED_FEES_NAME.add(formatToLower("JANUARY 2024"));
        ALLOWED_FEES_NAME.add(formatToLower("FEBRUARY 2024"));
        ALLOWED_FEES_NAME.add(formatToLower("MARCH 2024"));

        //24-25 session
        ALLOWED_FEES_NAME.add(formatToLower("APRIL 2024"));
        ALLOWED_FEES_NAME.add(formatToLower("MAY 2024"));
        ALLOWED_FEES_NAME.add(formatToLower("JUNE 2024"));
        ALLOWED_FEES_NAME.add(formatToLower("JULY 2024"));
        ALLOWED_FEES_NAME.add(formatToLower("AUGUST 2024"));
        ALLOWED_FEES_NAME.add(formatToLower("SEPTEMBER 2024"));
        ALLOWED_FEES_NAME.add(formatToLower("OCTOBER 2024"));
        ALLOWED_FEES_NAME.add(formatToLower("NOVEMBER 2024"));
        ALLOWED_FEES_NAME.add(formatToLower("DECEMBER 2024"));
        ALLOWED_FEES_NAME.add(formatToLower("JANUARY 2025"));
        ALLOWED_FEES_NAME.add(formatToLower("FEBRUARY 2025"));
        ALLOWED_FEES_NAME.add(formatToLower("MARCH 2025"));
    }

    public PaymentDiscountCalculator10205(FeeConfigurationDao feeConfigurationDao, FeeDiscountConfigurationDao feeDiscountConfigurationDao) {
        this.feeConfigurationDao = feeConfigurationDao;
        this.feeDiscountConfigurationDao = feeDiscountConfigurationDao;
    }

    @Override
    public PaymentApplicableDiscount calculateDiscount(int instituteId, PaymentRequest paymentRequest) {

        // Academic session is
        if (instituteId != 10205 || paymentRequest == null || paymentRequest.getStudentId() == null
                || CollectionUtils.isEmpty(paymentRequest.getFeeIds())) {
            logger.error("Invalid request for instituteId {}, paymentRequest {}", instituteId, paymentRequest);
            return getNoDiscountResponse();
        }


        List<FeeConfigurationResponse> feeConfigurationResponseList =  feeConfigurationDao.getFeeConfigurations(instituteId, paymentRequest.getFeeIds());

        if(CollectionUtils.isEmpty(feeConfigurationResponseList)){
            logger.error("No fees found for given request instituteId {}, paymentRequest {}", instituteId, paymentRequest);
            return getNoDiscountResponse();
        }

        int academicSessionId = feeConfigurationResponseList.get(0).getAcademicSession().getAcademicSessionId();

        List<FeeDiscountAssignmentDetails> feeDiscountAssignmentDetailsList = feeDiscountConfigurationDao.getStudentFeeDiscountAssignmentDetails(instituteId, academicSessionId,
                paymentRequest.getStudentId());

        // Null implies exception here. So careful with handling.
        if(feeDiscountAssignmentDetailsList == null){
            logger.error("Unable to get discount assignment  for instituteId {}, paymentRequest {}. Skipping discount for this request",
                    instituteId, paymentRequest);
            return getNoDiscountResponse();
        }

        if(!feeDiscountAssignmentDetailsList.isEmpty()){
            logger.info("{} Discounts are assigned for instituteId {}, paymentRequest {}. Skipping discount for this request",
                    feeDiscountAssignmentDetailsList.size(), instituteId, paymentRequest);
            return getNoDiscountResponse();
        }

        // If discounts are not present then providing default discount

        int currentTime = DateUtils.now();
        double totalDiscount = 0d;
        for(FeeConfigurationResponse feeConfigurationResponse : feeConfigurationResponseList){
            String feeName = formatToLower(feeConfigurationResponse.getFeeConfigurationBasicInfo().getFeeName());
            if(!ALLOWED_FEES_NAME.contains(feeName)){
                continue;
            }

            Integer dueDate = feeConfigurationResponse.getFeeConfigurationBasicInfo().getDueDate();
            if(dueDate == null || dueDate <= 0){
                continue;
            }

            int dueDayStart = DateUtils.getDayStart(dueDate, DateUtils.DEFAULT_TIMEZONE);
            if(currentTime < dueDayStart){
                totalDiscount += INSTANT_DISCOUNT_AMOUNT_BEFORE_DUE_DATE_PAYMENT;
            }

        }

        return new PaymentApplicableDiscount(true, PaymentDiscountType.INSTANT, totalDiscount, "", "");
    }

    private static PaymentApplicableDiscount getNoDiscountResponse(){
        return new PaymentApplicableDiscount(false, PaymentDiscountType.INSTANT, 0d, null, null);
    }
    public static String formatToLower(String s){
        return s == null ? null : s.trim().toLowerCase();
    }
}
