package com.embrate.cloud.core.lib.service.payment.gateway.atom;

import com.embrate.cloud.core.api.application.mobile.MobileAppPlatform;
import com.embrate.cloud.core.api.service.payment.gateway.PGTransactionTokenPayload;
import com.embrate.cloud.core.api.service.payment.gateway.PGTransactionTokenResponse;
import com.embrate.cloud.core.api.service.payment.gateway.PaymentGatewayCurrency;
import com.embrate.cloud.core.api.service.payment.gateway.PaymentGatewayStage;
import com.embrate.cloud.core.api.service.payment.gateway.atom.AtomMerchantDetails;
import com.embrate.cloud.core.api.service.payment.gateway.merchants.PGMerchantDetails;
import com.embrate.cloud.core.api.service.payment.gateway.utils.PGPlatform;
import com.embrate.cloud.core.lib.service.payment.gateway.IPaymentGatewayService;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.rest.RestClient;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.NotImplementedException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class AtomPaymentGatewayService implements IPaymentGatewayService {
    private static final Logger logger = LogManager.getLogger(AtomPaymentGatewayService.class);

    private final RestClient restClient;

    public AtomPaymentGatewayService(RestClient restClient) {
        this.restClient = restClient;
    }

    public static String getAtomPayUrl(PGTransactionTokenPayload transactionTokenPayload, AtomMerchantDetails atomMerchantDetails,
                                       PGPlatform pgPlatform, String mobileNumber, String emailId) {

        switch (pgPlatform){
            case IOS:
                return iosAtomPayUrl(transactionTokenPayload, atomMerchantDetails, mobileNumber, emailId);
            case GOOGLE:
                return androidAtomPayUrl(transactionTokenPayload, atomMerchantDetails, mobileNumber, emailId);
            default:
                logger.error("pgPlatform {} is not supported for atom payment gateway. payload {}", pgPlatform, transactionTokenPayload);
                throw new UnsupportedOperationException("This platform is not supported");
        }
    }

    public static String iosAtomPayUrl(PGTransactionTokenPayload transactionTokenPayload, AtomMerchantDetails atomMerchantDetails
            , String mobileNumber, String emailId) {

        String signature_request = AtomGenerateSignature.getEncodedValueWithSha2(
                atomMerchantDetails.getRequestHashKey(), atomMerchantDetails.getLoginId(),
                atomMerchantDetails.getPassword(), atomMerchantDetails.gettType(),
                atomMerchantDetails.getProductId(), transactionTokenPayload.getTransactionId(),
                String.valueOf(transactionTokenPayload.getAmount()), transactionTokenPayload.getCurrency().name());

        byte[] bytes = atomMerchantDetails.getClientCode().getBytes(StandardCharsets.UTF_8);
        String clientCodeUtf8EncodedString = Base64.getEncoder().encodeToString(bytes);

        String currentDate = DateUtils.getFormattedDate(DateUtils.now(), DateUtils.DEFAULT_DATE_TIME_FORMAT_WITH_NUMBER_MONTH,
                DateUtils.DEFAULT_TIMEZONE);

        String encodedStr = "reqenckey=" + atomMerchantDetails.getRequestEncryptionKey()
                + "&resenckey=" + atomMerchantDetails.getResponseDecryptionKey() + "&pass=" + atomMerchantDetails.getPassword();
        byte[] encodedStrBytes = encodedStr.getBytes(StandardCharsets.UTF_8);
        String encodedStrBytesUtf8EncodedString = Base64.getEncoder().encodeToString(encodedStrBytes);
        String udf21 = atomMerchantDetails.getMcc() + "~" + atomMerchantDetails.getMerchantName() + "~" + atomMerchantDetails.getDomainUrl();
        String udf2 = emailId;
        String udf3 = mobileNumber;
        String atomPayUrl = AtomGenerateSignature.ATOM_IOS_BASE_URL
                + "?login=" + atomMerchantDetails.getLoginId()
                + "&ttype=" + atomMerchantDetails.gettType()
                + "&prodid="+ atomMerchantDetails.getProductId()
                + "&amt="+ transactionTokenPayload.getAmount()
                + "&txncurr="+ transactionTokenPayload.getCurrency().name()
                + "&txnscamt="+ atomMerchantDetails.getTxnscamt()
                + "&clientcode="+ clientCodeUtf8EncodedString
                + "&txnid="+ transactionTokenPayload.getTransactionId()
                + "&date="+ currentDate
                + "&custacc="+ atomMerchantDetails.getCustomerAccount()
                + "&udf2="+ udf2
                + "&udf3="+ udf3
                + "&udf21="+ udf21
                + "&encVal=" + encodedStrBytesUtf8EncodedString
                + "&payurl=" + (transactionTokenPayload.getPaymentGatewayStage() == PaymentGatewayStage.PROD
                ? AtomGenerateSignature.ATOM_IOS_PROD_PAY_URL : AtomGenerateSignature.ATOM_IOS_TEST_PAY_URL)
                + "&ru=https://www.atomtech.in/atomfluttersdk/mobilesdk/param/index"
                + "&signature="+signature_request;
        return atomPayUrl;
    }

    public static String androidAtomPayUrl(PGTransactionTokenPayload transactionTokenPayload,
                                           AtomMerchantDetails atomMerchantDetails, String mobileNumber, String emailId) {
        String signature_request = AtomGenerateSignature.getEncodedValueWithSha2(
                atomMerchantDetails.getRequestHashKey(), atomMerchantDetails.getLoginId(),
                atomMerchantDetails.getPassword(), atomMerchantDetails.gettType(),
                atomMerchantDetails.getProductId(), transactionTokenPayload.getTransactionId(),
                String.valueOf(transactionTokenPayload.getAmount()), transactionTokenPayload.getCurrency().name());
        byte[] bytes = atomMerchantDetails.getClientCode().getBytes(StandardCharsets.UTF_8);
        String clientCodeUtf8EncodedString = Base64.getEncoder().encodeToString(bytes);
        String currentDate = DateUtils.getFormattedDate(DateUtils.now(), DateUtils.DEFAULT_DATE_TIME_FORMAT_WITH_NUMBER_MONTH,
                DateUtils.DEFAULT_TIMEZONE);
        String udf21 = atomMerchantDetails.getMcc() + "~" + atomMerchantDetails.getMerchantName() + "~" + atomMerchantDetails.getDomainUrl();
        String udf2 = emailId;
        String udf3 = mobileNumber;
        String atomPayUrl = AtomGenerateSignature.ATOM_ANDROID_BASE_URL
                + "?login=" + atomMerchantDetails.getLoginId()
                + "&pass=" + atomMerchantDetails.getPassword()
                + "&ttype=" + atomMerchantDetails.gettType()
                + "&prodid="+ atomMerchantDetails.getProductId()
                + "&amt="+ transactionTokenPayload.getAmount()
                + "&txncurr="+ transactionTokenPayload.getCurrency().name()
                + "&txnscamt="+ atomMerchantDetails.getTxnscamt()
                + "&clientcode="+ clientCodeUtf8EncodedString
                + "&txnid="+ transactionTokenPayload.getTransactionId()
                + "&date="+ currentDate
                + "&custacc="+ atomMerchantDetails.getCustomerAccount()
                + "&udf2="+ udf2
                + "&udf3="+ udf3
                + "&udf21="+ udf21
                + "&reqenckey=" + atomMerchantDetails.getRequestEncryptionKey()
                + "&reqencsaltkey=" + atomMerchantDetails.getRequestEncryptionKey()
                + "&resenckey=" + atomMerchantDetails.getResponseDecryptionKey()
                + "&resencsaltkey=" + atomMerchantDetails.getResponseDecryptionKey()
                + "&payurl=" + (transactionTokenPayload.getPaymentGatewayStage() == PaymentGatewayStage.PROD
                ? AtomGenerateSignature.ATOM_ANDROID_PROD_PAY_URL : AtomGenerateSignature.ATOM_ANDROID_TEST_PAY_URL)
                + "&ru=https://www.atomtech.in/atomfluttersdk/mobilesdk/param/index"
                + "&signature="+signature_request;
        return atomPayUrl;
    }

    @Override
    public PGTransactionTokenResponse generateTransactionToken(PGTransactionTokenPayload transactionTokenPayload, PGMerchantDetails merchantDetails,
                                                               PGPlatform pgPlatform) {

        Map<String, Object> metaDataMerchantDetails = merchantDetails.getMetadata();
        AtomMerchantDetails atomMerchantDetails = AtomMerchantDetails.getAtomMerchantDetails(metaDataMerchantDetails);
        String clientId = atomMerchantDetails.getLoginId();

        if(transactionTokenPayload == null){
            logger.error("Invalid token payload");
            return PGTransactionTokenResponse.forFailure(null, "Null token payload", clientId,null);
        }

        if(StringUtils.isBlank(transactionTokenPayload.getTransactionId()) ||
                Double.compare(transactionTokenPayload.getAmount(), 0d) <= 0 ||
                transactionTokenPayload.getCurrency() == null || transactionTokenPayload.getPaymentGatewayStage() == null ||
                transactionTokenPayload.getCurrency() != PaymentGatewayCurrency.INR){
            logger.error("Invalid token payload {}", transactionTokenPayload);
            return PGTransactionTokenResponse.forFailure(null, "Invalid token payload", clientId,null);
        }

        String atomPayUrl = getAtomPayUrl(transactionTokenPayload, atomMerchantDetails, pgPlatform, transactionTokenPayload.getUserData().getUserPhone(), transactionTokenPayload.getUserData().getUserEmail());
        if(StringUtils.isBlank(atomPayUrl)) {
            logger.error("Failure response from atom {}. status code {}", atomPayUrl);
            return PGTransactionTokenResponse.forFailure(null, "Invalid pay url", clientId, null);
        }

        logger.info("atomPayUrl {} ", atomPayUrl);
        Map<String, String> metaData = new HashMap<>();
        metaData.put("atomPayUrl", atomPayUrl);
        return PGTransactionTokenResponse.forSuccess(atomPayUrl, clientId, metaData);

    }
}