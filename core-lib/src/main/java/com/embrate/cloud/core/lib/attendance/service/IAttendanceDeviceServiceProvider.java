package com.embrate.cloud.core.lib.attendance.service;

import com.embrate.cloud.core.api.attendance.AttendanceLog;
import com.embrate.cloud.core.api.attendance.DeviceAttendanceData;
import com.embrate.cloud.core.api.attendance.DeviceUpdateUserData;
import com.embrate.cloud.core.api.attendance.IDeviceAttendancePayload;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IAttendanceDeviceServiceProvider {

    DeviceAttendanceData transformInput(IDeviceAttendancePayload deviceAttendancePayload);

    DeviceUpdateUserData transformUserUpdatedInput(IDeviceAttendancePayload deviceAttendancePayload);

}
