package com.embrate.cloud.core.lib.feature.preference.handler;

import com.embrate.cloud.core.api.feature.preference.FeaturePreferenceEntity;
import com.embrate.cloud.core.api.feature.preference.PreferenceDataType;
import com.embrate.cloud.core.api.feature.preference.ProductFeature;
import com.embrate.cloud.core.lib.feature.preference.IFeaturePreferenceGroupBuilder;
import com.lernen.cloud.core.api.attendance.AttendanceStatus;
import com.lernen.cloud.core.api.attendance.preference.AttendancePreferences;
import com.lernen.cloud.core.api.sms.SMSPreferences;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.utils.feature.preference.ProductFeatureUtils;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 */

public class AttendanceFeatureHandler extends ProductFeatureHandler {

    private final InstituteManager instituteManager;

    public AttendanceFeatureHandler(InstituteManager instituteManager) {
        this.instituteManager = instituteManager;
    }

    private ProductFeature attendanceProductFeature = null;

    @Override
    public ProductFeature buildProductFeature() {
        attendanceProductFeature = new ProductFeature("com.embrate.feature.attendance", "Attendance Preferences", "Attendance Preferences");
        addFeaturePreferenceGroup(attendanceProductFeature, getStudentPreferences());
        return attendanceProductFeature;
    }

    @Override
    public ProductFeature getProductFeature() {
        return attendanceProductFeature;
    }

    private IFeaturePreferenceGroupBuilder getStudentPreferences() {
        return new IFeaturePreferenceGroupBuilder() {
            @Override
            public String getGroupId() {
                return "group.preference.student";
            }

            @Override
            public String getGroupName() {
                return "Student Preferences";
            }

            @Override
            public String getGroupDescription() {
                return "Student Preferences";
            }

            @Override
            public List<FeaturePreferenceEntity> getPreferences() {
                
                List<FeaturePreferenceEntity> featurePreferenceEntities = new LinkedList<>();
                featurePreferenceEntities.add(new FeaturePreferenceEntity("device_attendance_enabled", "device_attendance_enabled", "device_attendance_enabled", PreferenceDataType.BOOLEAN, AttendancePreferences.getConfigType(), AttendancePreferences.DEVICE_ATTENDANCE_ENABLED));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("student_attendance_shift_schedule", "student_attendance_shift_schedule", "student_attendance_shift_schedule", PreferenceDataType.STRING, AttendancePreferences.getConfigType(), AttendancePreferences.STUDENT_ATTENDANCE_SHIFT_SCHEDULE));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("send_absent_student_sms", "send_absent_student_sms", "send_absent_student_sms", PreferenceDataType.BOOLEAN, AttendancePreferences.getConfigType(), AttendancePreferences.SEND_SMS_FOR_ABSENT_STUDENT));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("send_student_attendance_push_notification", "send_student_attendance_push_notification", "send_student_attendance_push_notification", PreferenceDataType.BOOLEAN, AttendancePreferences.getConfigType(), AttendancePreferences.SEND_STUDENT_ATTENDANCE_PUSH_NOTIFICATION));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("default_attendance_status", "default_attendance_status", "default_attendance_status", PreferenceDataType.ENUM, AttendancePreferences.getConfigType(), AttendancePreferences.DEFAULT_ATTENDANCE_STATUS, ProductFeatureUtils.getEnumValues(AttendanceStatus.values())));

                featurePreferenceEntities.add(new FeaturePreferenceEntity("enable_student_attendance_sms", "Enable Student Attendance SMS", "Enable this flag to send student attendance SMS.", PreferenceDataType.BOOLEAN, AttendancePreferences.getConfigType(), AttendancePreferences.ENABLE_STUDENT_ATTENDANCE_SMS));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("attendance_statuses_to_send_sms", "Attendance Statues To Send SMS", "Attendance Statuses for which sms needs to be send", PreferenceDataType.STRING, AttendancePreferences.getConfigType(), AttendancePreferences.STUDENT_ATTENDANCE_STATUSES_TO_SEND_SMS));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("attendance_types_to_send_sms", "Attendance Types To Send SMS", "Attendance Types for which sms needs to be send.", PreferenceDataType.STRING, AttendancePreferences.getConfigType(), AttendancePreferences.STUDENT_ATTENDANCE_TYPES_TO_SEND_SMS));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("student_attendance_status_sms_template_id", "Student Attendance Status SMS Template Id", "Template Id of sms for student attendance status.", PreferenceDataType.UUID, AttendancePreferences.getConfigType(), AttendancePreferences.STUDENT_ATTENDANCE_STATUS_SMS_TEMPLATE_ID));

                return featurePreferenceEntities;
            }
        };
    }
}
