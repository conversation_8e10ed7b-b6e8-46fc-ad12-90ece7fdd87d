package com.embrate.cloud.core.lib.feature.preference.handler;

import com.embrate.cloud.core.api.feature.preference.FeaturePreferenceEntity;
import com.embrate.cloud.core.api.feature.preference.PreferenceDataType;
import com.embrate.cloud.core.api.feature.preference.ProductFeature;
import com.embrate.cloud.core.lib.feature.preference.IFeaturePreferenceGroupBuilder;
import com.lernen.cloud.core.api.configurations.ExamAdmitCardPreferences;
import com.lernen.cloud.core.api.configurations.ExaminationPreferences;
import com.lernen.cloud.core.api.examination.report.RankCalculatorType;
import com.lernen.cloud.core.api.fees.fine.FeesFinePreferences;
import com.lernen.cloud.core.api.fees.fine.FineCalculatorType;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.utils.feature.preference.ProductFeatureUtils;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 */

public class ExaminationFeatureHandler extends ProductFeatureHandler {

    private final InstituteManager instituteManager;

    public ExaminationFeatureHandler(InstituteManager instituteManager) {
        this.instituteManager = instituteManager;
    }

    private ProductFeature examinationProductFeature = null;

    @Override
    public ProductFeature buildProductFeature() {
        examinationProductFeature = new ProductFeature("com.embrate.feature.exam.core", "Examination Preferences", "Examination Preferences");
        addFeaturePreferenceGroup(examinationProductFeature, getBasicPreferences());
        return examinationProductFeature;
    }

    @Override
    public ProductFeature getProductFeature() {
        return examinationProductFeature;
    }

    private IFeaturePreferenceGroupBuilder getBasicPreferences() {
        return new IFeaturePreferenceGroupBuilder() {
            @Override
            public String getGroupId() {
                return "group.preference.basic";
            }

            @Override
            public String getGroupName() {
                return "Basic Preferences";
            }

            @Override
            public String getGroupDescription() {
                return "Basic Preferences";
            }

            @Override
            public List<FeaturePreferenceEntity> getPreferences() {
                List<FeaturePreferenceEntity> featurePreferenceEntities = new LinkedList<>();
                featurePreferenceEntities.add(new FeaturePreferenceEntity("green_sheet_enabled", "green_sheet_enabled", "green_sheet_enabled", PreferenceDataType.BOOLEAN, ExaminationPreferences.getConfigType(), ExaminationPreferences.GREEN_SHEET_ENABLED));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("include_dimension_in_datesheet", "include_dimension_in_datesheet", "include_dimension_in_datesheet", PreferenceDataType.BOOLEAN, ExaminationPreferences.getConfigType(), ExaminationPreferences.INCLUDE_DIMENSION_IN_DATESHEET));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("report_card_variable_field_properties", "report_card_variable_field_properties", "Properties of variables to be shown in report card attributes screen", PreferenceDataType.STRING, ExaminationPreferences.getConfigType(), ExaminationPreferences.REPORT_CARD_VARIABLE_FIELD_PROPERTIES));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("report_card_view_only_submitted_marks", "report_card_view_only_submitted_marks", "Enable it when you want to see the submitted marks only in report card", PreferenceDataType.BOOLEAN, ExaminationPreferences.getConfigType(), ExaminationPreferences.REPORT_CARD_VIEW_ONLY_SUBMITTED_MARKS));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("hpc_enabled", "Enable HPC", "This flag should be turned on when HPC needs to be enabled for an institute.", PreferenceDataType.BOOLEAN, ExaminationPreferences.getConfigType(), ExaminationPreferences.HPC_ENABLED));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("parent_hpc_enabled", "Enable Mobile App Parents HPC", "This flag should be turned on when HPC needs to be enabled at parents end.", PreferenceDataType.BOOLEAN, ExaminationPreferences.getConfigType(), ExaminationPreferences.PARENTS_HPC_ENABLED));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("rank_rule", "Rank Rule", "Which rank rule is to be used in report card", PreferenceDataType.ENUM, ExaminationPreferences.getConfigType(), ExaminationPreferences.RANK_RULE, ProductFeatureUtils.getEnumValues(RankCalculatorType.values())));
                return featurePreferenceEntities;
            }
        };
    }
}
