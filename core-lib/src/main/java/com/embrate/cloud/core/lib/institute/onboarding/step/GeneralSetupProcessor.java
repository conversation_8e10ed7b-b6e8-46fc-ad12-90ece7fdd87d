package com.embrate.cloud.core.lib.institute.onboarding.step;

import com.embrate.cloud.core.api.onboarding.institute.setup.ConfigInput;
import com.embrate.cloud.core.api.onboarding.institute.setup.InstituteSetupPayload;
import com.embrate.cloud.core.api.student.finance.StudentFinancePreferences;
import com.embrate.cloud.core.lib.institute.onboarding.IConfigureInstituteStep;
import com.lernen.cloud.core.api.common.CounterType;
import com.lernen.cloud.core.api.configurations.Entity;
import com.lernen.cloud.core.api.configurations.LibraryPreferences;
import com.lernen.cloud.core.api.configurations.MetaDataPreferences;
import com.lernen.cloud.core.api.institute.CounterData;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.lib.configs.ConfigurationManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.utils.SharedConstants;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

/**
 * <AUTHOR>
 */

public class GeneralSetupProcessor implements IModuleSetupProcessor {

    private static final Logger logger = LogManager.getLogger(GeneralSetupProcessor.class);
    private final InstituteManager instituteManager;

    private final ConfigurationManager configurationManager;

    public GeneralSetupProcessor(InstituteManager instituteManager, ConfigurationManager configurationManager) {
        this.instituteManager = instituteManager;
        this.configurationManager = configurationManager;
    }

    @Override
    public Module getModule() {
        return Module.DEFAULT;
    }

    @Override
    public List<IConfigureInstituteStep> getSteps(InstituteSetupPayload instituteSetupPayload) {
        int instituteId = instituteSetupPayload.getInstituteBasicSetupResult().getInstituteId();
        List<IConfigureInstituteStep> instituteSetupStepsList = new ArrayList<>();
        instituteSetupStepsList.add(getCounterStep(instituteId, instituteSetupPayload));
        instituteSetupStepsList.add(getConfigurationStep(instituteId, instituteSetupPayload));
        return instituteSetupStepsList;
    }

    private IConfigureInstituteStep getCounterStep(int instituteId, InstituteSetupPayload instituteSetupPayload){
        return new IConfigureInstituteStep() {
            @Override
            public String getName() {
                return "Institute Counters";
            }

            @Override
            public boolean execute() {
                ConfigInput configInput = instituteSetupPayload.getConfigInput();
                List<CounterData> counterDataList = new ArrayList<>();
                for(CounterType counterType : CounterType.values()){
                    String prefix = counterType.getDefaultPrefix();
                    switch (counterType){
                        case FEE_INVOICE:
                            prefix = configInput.getFeeInvoiceCounterPrefix() == null ? "" : configInput.getFeeInvoiceCounterPrefix().trim();
                            break;
                        case ADMISSION_NUMBER:
                            prefix = configInput.getAdmissionCounterPrefix() == null ? "" : configInput.getAdmissionCounterPrefix().trim();
                            break;
                        case REGISTRATION_NUMBER:
                            prefix = configInput.getRegistrationCounterPrefix() == null ? "" : configInput.getRegistrationCounterPrefix().trim();
                            break;
                        case STAFF_NUMBER:
                            prefix = configInput.getStaffCounterPrefix() == null ? "" : configInput.getStaffCounterPrefix().trim();
                            break;
                        case TRANSFER_CERTIFICATE_NUMBER:
                            prefix = configInput.getTransferCertificateCounterPrefix() == null ? "" : configInput.getTransferCertificateCounterPrefix().trim();
                            break;
                        case STORE_INVOICE:
                            prefix = configInput.getStoreCounterPrefix() == null ? "" : configInput.getStoreCounterPrefix().trim();
                            break;
                        case ONLINE_REGISTRATION_NUMBER:
                            prefix = configInput.getOnlineRegistrationCounterPrefix() == null ? "" : configInput.getOnlineRegistrationCounterPrefix().trim();
                            break;
                        case GATE_PASS_NUMBER:
                            prefix = configInput.getGatepassCounterPrefix() == null ? "" : configInput.getGatepassCounterPrefix().trim();
                            break;
                        case HOSTEL_GATE_PASS_NUMBER:
                            prefix = configInput.getHostelGatepassCounterPrefix() == null ? "" : configInput.getHostelGatepassCounterPrefix().trim();
                            break;
                        case COMPLAIN_NUMBER:
                            prefix = configInput.getComplainCounterPrefix() == null ? "" : configInput.getComplainCounterPrefix().trim();
                            break;
                        case ACCESSION_NUMBER:
                            prefix = configInput.getAccessionNumberCounterPrefix() == null ? "" : configInput.getAccessionNumberCounterPrefix().trim();
                            break;
                        case STUDENTS_FINANCE_EXPENSE_NUMBER:
                            prefix = configInput.getStudentExpenseCounterPrefix() == null ? "" : configInput.getStudentExpenseCounterPrefix().trim();
                            break;
                    }
                    counterDataList.add(new CounterData(instituteId, counterType, counterType.getDefaultInitValue(), prefix));
                }

                return instituteManager.addCounters(instituteId, counterDataList);
            }
        };
    }

    private IConfigureInstituteStep getConfigurationStep(int instituteId, InstituteSetupPayload instituteSetupPayload){
        return new IConfigureInstituteStep() {
            @Override
            public String getName() {
                return "Configurations";
            }

            @Override
            public boolean execute() {
                ConfigInput configInput = instituteSetupPayload.getConfigInput();
                String instituteNameInSMS = configInput.getInstituteNameInSMS();
                String instituteUniqueCode = configInput.getInstituteUniqueCode();
                boolean smsServiceEnabled = configInput.isSmsServiceEnabled();
                boolean accessionNumberCounter = configInput.isAccessionNumberCounter();
                boolean accessionNumberAccess = configInput.isAccessionNumberEnabled();
                boolean studentFinanceExpenseCounter = configInput.isStudentExpenseCounterEnabled();

                Map<String, String> configKVs = new HashMap<>();
                configKVs.put(MetaDataPreferences.ENABLE_PERMISSION, String.valueOf(true));
                configKVs.put(MetaDataPreferences.INSTITUTE_NAME_IN_SMS, instituteNameInSMS);
                configKVs.put(MetaDataPreferences.INSTITUTE_UNIQUE_CODE, instituteUniqueCode);
                configKVs.put(MetaDataPreferences.SMS_SERVICE_ENABLED, String.valueOf(smsServiceEnabled));
                configKVs.put(LibraryPreferences.ENABLE_ACCESSION_NUMBER, String.valueOf(accessionNumberAccess));
                configKVs.put(LibraryPreferences.ACCESSION_NUMBER_COUNTER, String.valueOf(accessionNumberCounter));
                configKVs.put(MetaDataPreferences.REGISTRATION_COUNTER, String.valueOf(configInput.isRegistrationCounterEnabled()));
                configKVs.put(MetaDataPreferences.ADMISSION_COUNTER, String.valueOf(configInput.isAdmissionCounterEnabled()));
                configKVs.put(MetaDataPreferences.STAFF_COUNTER, String.valueOf(configInput.isStaffCounterEnabled()));
                configKVs.put(MetaDataPreferences.TRANSFER_CERTIFICATE_COUNTER, String.valueOf(configInput.isTransferCertificateCounterEnabled()));
                configKVs.put(MetaDataPreferences.DETAILED_TC_ENABLED, String.valueOf(configInput.isDetailedTCEnabled()));
                configKVs.put(StudentFinancePreferences.STUDENT_FINANCE_SERVICE_EXPENSE_COUNTER, String.valueOf(studentFinanceExpenseCounter));

                configKVs.put(MetaDataPreferences.MODULE_ACCESS, SharedConstants.GSON.toJson(instituteSetupPayload.getAuthorizedModules()));

               return configurationManager.upsertConfiguration(Entity.INSTITUTE,String.valueOf(instituteId), MetaDataPreferences.getConfigType(), configKVs);
            }
        };
    }

}
