package com.embrate.cloud.core.lib.feature.preference.handler;

import com.embrate.cloud.core.api.feature.preference.FeaturePreferenceEntity;
import com.embrate.cloud.core.api.feature.preference.PreferenceDataType;
import com.embrate.cloud.core.api.feature.preference.ProductFeature;
import com.embrate.cloud.core.api.scratchcard.RewardsPreferences;
import com.embrate.cloud.core.lib.feature.preference.IFeaturePreferenceGroupBuilder;
import com.lernen.cloud.core.api.configurations.ExaminationPreferences;
import com.lernen.cloud.core.lib.institute.InstituteManager;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 */

public class RewardFeatureHandler extends ProductFeatureHandler {

    private final InstituteManager instituteManager;

    public RewardFeatureHandler(InstituteManager instituteManager) {
        this.instituteManager = instituteManager;
    }

    private ProductFeature productFeature = null;

    @Override
    public ProductFeature buildProductFeature() {
        productFeature = new ProductFeature("com.embrate.feature.reward", "Reward Preferences", "Reward Preferences");
        addFeaturePreferenceGroup(productFeature, getBasicPreferences());
        return productFeature;
    }

    @Override
    public ProductFeature getProductFeature() {
        return productFeature;
    }

    private IFeaturePreferenceGroupBuilder getBasicPreferences() {
        return new IFeaturePreferenceGroupBuilder() {
            @Override
            public String getGroupId() {
                return "group.preference.basic";
            }

            @Override
            public String getGroupName() {
                return "Basic Preferences";
            }

            @Override
            public String getGroupDescription() {
                return "Basic Preferences";
            }

            @Override
            public List<FeaturePreferenceEntity> getPreferences() {
                List<FeaturePreferenceEntity> featurePreferenceEntities = new LinkedList<>();
                featurePreferenceEntities.add(new FeaturePreferenceEntity("reward_enabled", "reward_enabled", "reward_enabled", PreferenceDataType.BOOLEAN, RewardsPreferences.getConfigType(), RewardsPreferences.REWARD_ENABLED));
                return featurePreferenceEntities;
            }
        };
    }
}
