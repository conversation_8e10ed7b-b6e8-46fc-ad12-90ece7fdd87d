package com.embrate.cloud.core.lib.inventory.v2;

import com.embrate.cloud.core.api.inventory.v2.ProductBatchData;
import com.embrate.cloud.core.api.inventory.v2.ProductDetailsV2;
import com.embrate.cloud.core.api.inventory.v2.TradeProductBatchSummary;
import com.embrate.cloud.core.api.inventory.v2.TradeProductSummary;
import com.embrate.cloud.core.api.inventory.v2.transaction.InventoryTransactionSummary;
import com.embrate.cloud.core.api.wallet.WalletTransactionPayload;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.inventory.InventoryTransactionType;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.report.CellIndexes;
import com.lernen.cloud.core.api.report.DownloadFormat;
import com.lernen.cloud.core.api.report.ReportCellDetails;
import com.lernen.cloud.core.api.report.ReportDetails;
import com.lernen.cloud.core.api.report.ReportSheetDetails;
import com.lernen.cloud.core.api.report.ReportType;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.lib.reports.ReportGenerator;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.NumberUtils;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.util.*;

public class InventoryReportGenerationManager extends ReportGenerator {

	private static final Logger logger = LogManager.getLogger(InventoryReportGenerationManager.class);

	private final InventoryProductManager inventoryProductManager;

	private final InventoryTransactionsManager inventoryTransactionsManager;

	private final UserPermissionManager userPermissionManager;

	private final InstituteManager instituteManager;

	private static final String[] STOCK_DETAILS_REPORT_COLUMN = { "Product Name", "Category", "Brand", "Batch",
			"Available Quantity", "Purchase Price", "Selling Price", "MRP", "Discount" };
	private static final String[] SALES_DETAILS_REPORT_COLUMN = { "Date", "Invoice Id", "Payment Mode", "Customer Name",
			"Sold By", "Email", "Reference", "Description", "Product Name", "Batch No.", "Qty Sold", "Selling Price", "Amount",
			"Discount", "Tax", "Additional Cost", "Additional Discount", "Paid Amount", "Wallet Amount", "Total Amount" };

	private static final String[] SALES_RETURN_DETAILS_REPORT_COLUMN = { "Date", "Invoice Id", "Payment Mode",
			"Customer Name", "Returned To", "Email", "Reference", "Description", "Product Name", "Batch No.", "Qty Returned",
			"Selling Price", "Amount", "Discount", "Tax", "Additional Cost", "Additional Discount", "Total Amount" };

	private static final String[] PURCHASE_DETAILS_REPORT_COLUMN = { "Date", "Invoice Id", "Payment Mode",
			"Supplier Name", "Purchased By", "Bill Number", "Description", "Product Name", "Batch No.", "Qty Purchased",
			"Purchase Price", "Amount", "Discount", "Tax", "Additional Cost", "Additional Discount", "Net Amount" };

	private static final String[] PURCHASE_RETURN_DETAILS_REPORT_COLUMN = { "Date", "Invoice Id", "Payment Mode",
			"Supplier Name", "Returned By", "Reference", "Description", "Product Name", "Batch No.", "Qty Returned", "Purchase Price",
			"Amount", "Discount", "Tax", "Additional Cost", "Additional Discount", "Net Amount" };

	private static final String[] ISSUE_DETAILS_REPORT_COLUMN = { "Date", "Invoice Id", "Issued To",
			"Issued By", "Reference", "Description", "Product Name", "Batch No.", "Qty Issued" };

	private static final String EMPTY_TEXT = "";

	public InventoryReportGenerationManager(InventoryProductManager inventoryProductManager,
			UserPermissionManager userPermissionManager,
			InventoryTransactionsManager inventoryTransactionsManager, InstituteManager instituteManager) {
		this.inventoryProductManager = inventoryProductManager;
		this.userPermissionManager = userPermissionManager;
		this.inventoryTransactionsManager = inventoryTransactionsManager;
		this.instituteManager = instituteManager;
	}

	public ReportDetails generateReport(int instituteId, ReportType reportType, UUID userId, int startDate,
										int endDate, DownloadFormat downloadFormat) {

		if(instituteId <= 0){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid institute Id"));
		}
		if(userId == null){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid User Id"));
		}

		if (downloadFormat == DownloadFormat.EXCEL) {
			if (!userPermissionManager.verifyAuthorisation(instituteId, userId,
					AuthorisationRequiredAction.INVENTORY_REPORTS, false)) {
				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
						"You don't have access to download inventory reports in excel!"));
			}
		} else if (downloadFormat == DownloadFormat.PDF) {
			if (!userPermissionManager.verifyAuthorisation(instituteId, userId,
					AuthorisationRequiredAction.INVENTORY_PDF_REPORTS, false)) {
				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
						"You don't have access to download inventory reports in pdf!"));
			}
		}

		switch (reportType) {
			case STOCK_DETAILS_REPORT:
				return generateStockReports(instituteId);
			case SALES_DETAILS_REPORT:
				return generateSalesReports(instituteId, startDate, endDate);
			case SALES_RETURN_DETAILS_REPORT:
				return generateSalesReturnReports(instituteId, startDate, endDate);
			case PURCHASE_DETAILS_REPORT:
				return generatePurchaseReports(instituteId, startDate, endDate);
			case PURCHASE_RETURN_DETAILS_REPORT:
				return generatePurchaseReturnReports(instituteId, startDate, endDate);
			case ISSUE_DETAILS_REPORT:
				return generateIssueReports(instituteId, startDate, endDate);
			default:
				break;
		}
		return null;
	}

	private ReportDetails generateStockReports(int instituteId) {

		try {

			String reportName = "Stock Details Report";
			String sheetName = "StockDetailsReport";

			Institute institute = instituteManager.getInstitute(instituteId);

			if (institute == null) {
				logger.info("Empty report {}", reportName);
				List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<>();
				ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, null,
						null);
				reportSheetDetailsList.add(reportSheetDetails);
				return new ReportDetails(reportName, reportSheetDetailsList);
			}

			List<ProductDetailsV2> productDetailsV2List = inventoryProductManager.searchProducts(instituteId,
					EMPTY_TEXT, false);
			if (CollectionUtils.isEmpty(productDetailsV2List)) {
				logger.info("Empty report {}", reportName);
				List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<>();
				ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, null,
						null);
				reportSheetDetailsList.add(reportSheetDetails);
				return new ReportDetails(reportName, reportSheetDetailsList);
			}

			List<List<ReportCellDetails>> headerReportCellDetails = new ArrayList<List<ReportCellDetails>>();
			List<CellIndexes> headerMergeCellIndexesList = new ArrayList<>();

			List<ReportCellDetails> reportHeaderRow = new ArrayList<ReportCellDetails>();
			reportHeaderRow
					.add(new ReportCellDetails(reportName, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
			headerReportCellDetails.add(reportHeaderRow);

			List<ReportCellDetails> reportCellDetailsHeaderRow = new ArrayList<ReportCellDetails>();
			int i = 0;
			for (i = 0; i < STOCK_DETAILS_REPORT_COLUMN.length; i++) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(STOCK_DETAILS_REPORT_COLUMN[i], STRING,
						CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
			}
			headerReportCellDetails.add(reportCellDetailsHeaderRow);

			int totalColumns = STOCK_DETAILS_REPORT_COLUMN.length;
			CellIndexes cellIndexes = new CellIndexes(0, 0, 0, totalColumns - 1);
			headerMergeCellIndexesList.add(cellIndexes);


			List<List<ReportCellDetails>> reportCellDetails = new ArrayList<List<ReportCellDetails>>();
			int rowNum = 2;
			int colSpan = 0;
			double totalAvailableQuantity = 0d;
			double totalPurchasePrice = 0d;
			double totalSellingPrice = 0d;
			double totalMRP = 0d;
			for (ProductDetailsV2 productDetailsV2 : productDetailsV2List) {
				for (ProductBatchData productBatchData : productDetailsV2.getProductBatchDataList()) {
					List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();

					String productName = StringUtils.isBlank(productDetailsV2.getProductMetadata().getName())
							? EMPTY_TEXT
							: productDetailsV2.getProductMetadata().getName();
					reportCellDetailsRow.add(new ReportCellDetails(productName, STRING,
							CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

					String categoryName = StringUtils.isBlank(productDetailsV2.getCategory().getCategoryName())
							? EMPTY_TEXT
							: productDetailsV2.getCategory().getCategoryName();
					reportCellDetailsRow.add(new ReportCellDetails(categoryName, STRING,
							CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

					String brandName = productDetailsV2.getBrand() == null
							|| StringUtils.isBlank(productDetailsV2.getBrand().getBrandName()) ? EMPTY_TEXT
									: productDetailsV2.getBrand().getBrandName();
					reportCellDetailsRow.add(new ReportCellDetails(brandName, STRING,
							CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

					String batchName = StringUtils.isBlank(productBatchData.getBatchName()) ? EMPTY_TEXT
							: productBatchData.getBatchName();
					reportCellDetailsRow.add(new ReportCellDetails(batchName, STRING,
							CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

					Double totalQuantityDouble = productBatchData.getTotalQuantity() == null ? 0
							: productBatchData.getTotalQuantity();
					reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalQuantityDouble), STRING,
							CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

					totalAvailableQuantity += totalQuantityDouble;

					Double purchasePriceDouble = productBatchData.getPurchasePrice() == null ? 0
							: productBatchData.getPurchasePrice();
					reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(purchasePriceDouble), STRING,
							CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

					totalPurchasePrice += purchasePriceDouble;

					Double sellingPriceDouble = productBatchData.getSellingPrice() == null ? 0
							: productBatchData.getSellingPrice();
					reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(sellingPriceDouble), STRING,
							CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

					totalSellingPrice += sellingPriceDouble;

					Double mrpDouble = productBatchData.getMrp() == null ? 0 : productBatchData.getMrp();

					reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(mrpDouble), STRING,
							CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

					totalMRP += mrpDouble;

					String discount = productBatchData.isDiscountInPercent() ? NumberUtils.formatToIndianCurrency(productBatchData.getDiscount()) + "%"
							: "Rs. " + NumberUtils.formatToIndianCurrency(productBatchData.getDiscount());
					reportCellDetailsRow.add(new ReportCellDetails(discount, STRING,
							CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

					reportCellDetails.add(reportCellDetailsRow);
					rowNum++;
				}
			}

			List<ReportCellDetails> reportCellDetailsTotalRow = new ArrayList<ReportCellDetails>();
			colSpan = 0;
			reportCellDetailsTotalRow.add(new ReportCellDetails(TOTAL, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;

			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;

			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;

			reportCellDetailsTotalRow
					.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalAvailableQuantity), STRING, CONTENT_SIZE,
							BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsTotalRow
					.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalPurchasePrice), STRING, CONTENT_SIZE,
							BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalSellingPrice), STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalMRP), STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			rowNum++;

			reportCellDetails.add(reportCellDetailsTotalRow);
			List<CellIndexes> mergeCellIndexesList = new ArrayList<CellIndexes>();
			cellIndexes = new CellIndexes(rowNum - 1, rowNum - 1, 0, colSpan);
			mergeCellIndexesList.add(cellIndexes);

			List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
//			ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, mergeCellIndexesList,
//					reportCellDetails);

			ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true,
					totalColumns, headerMergeCellIndexesList,
					mergeCellIndexesList, headerReportCellDetails, reportCellDetails, true,
					true, institute, totalColumns);

			reportSheetDetailsList.add(reportSheetDetails);
			return new ReportDetails(reportName, reportSheetDetailsList);

		} catch (final Exception e) {
			logger.error("Error while generating stock report", e);
		}

		return null;
	}

	private ReportDetails generateSalesReports(int instituteId, int startDate, int endDate) {
		try {

			String reportName = "Sales Details Report";
			String sheetName = "SalesDetailsReport";

			Institute institute = instituteManager.getInstitute(instituteId);

			if (institute == null) {
				logger.info("Empty report {}", reportName);
				List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<>();
				ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, null,
						null);
				reportSheetDetailsList.add(reportSheetDetails);
				return new ReportDetails(reportName, reportSheetDetailsList);
			}

			List<InventoryTransactionSummary> inventoryTransactionSummaryList = inventoryTransactionsManager
					.getTransactionDetailsList(instituteId, startDate, endDate, InventoryTransactionType.SALE);
			List<List<ReportCellDetails>> headerReportCellDetails = new ArrayList<List<ReportCellDetails>>();
			List<CellIndexes> headerMergeCellIndexesList = new ArrayList<CellIndexes>();

			if (CollectionUtils.isEmpty(inventoryTransactionSummaryList)) {
				logger.info("Empty report {}", reportName);
				List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<>();
				ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, null,
						null);
				reportSheetDetailsList.add(reportSheetDetails);
				return new ReportDetails(reportName, reportSheetDetailsList);
			}

			List<ReportCellDetails> reportHeaderRow = new ArrayList<ReportCellDetails>();

			reportHeaderRow
					.add(new ReportCellDetails(reportName, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
			headerReportCellDetails.add(reportHeaderRow);

			int totalColumns = SALES_DETAILS_REPORT_COLUMN.length;
			CellIndexes cellIndexes = new CellIndexes(0, 0, 0, totalColumns - 1);
			headerMergeCellIndexesList.add(cellIndexes);

			List<ReportCellDetails> reportCellDetailsHeaderRow = new ArrayList<ReportCellDetails>();
			int i = 0;
			for (i = 0; i < SALES_DETAILS_REPORT_COLUMN.length; i++) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(SALES_DETAILS_REPORT_COLUMN[i], STRING,
						CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
			}
			headerReportCellDetails.add(reportCellDetailsHeaderRow);

//			Collections.sort(inventoryTransactionSummaryList, new Comparator<InventoryTransactionSummary>() {
//				@Override
//				public int compare(InventoryTransactionSummary inventoryTransactionSummary1,
//								   InventoryTransactionSummary inventoryTransactionSummary2) {
//					return ((int) (inventoryTransactionSummary2.getTransactionDate() / 1000L) -
//							(int) (inventoryTransactionSummary1.getTransactionDate() / 1000L));
//				}
//			});

			TreeMap<Integer, List<InventoryTransactionSummary>> dateWiseInventoryTransactionSummaryMap = getDateWiseInventoryTransactionSummaryMap(inventoryTransactionSummaryList);
			int rowNum = 2;
			int colSpan = 0;
			double totalQtySold = 0d;
			double totalSellingPrice = 0d;
			double totalInnerAmount = 0d;
			double totalDiscount = 0d;
			double totalTax = 0d;
			double totalAdditionalCost = 0d;
			double totalAdditionalDiscount = 0d;
			double totalPaidAmount = 0d;
			double totalWalletAmount = 0d;
			double totalTotalAmount = 0d;
			List<Integer> mergedRows = new ArrayList<>();
			List<CellIndexes> mergeCellIndexesList = new ArrayList<CellIndexes>();
			List<List<ReportCellDetails>> reportCellDetails = new ArrayList<>();
			for (final Map.Entry<Integer, List<InventoryTransactionSummary>> inventoryTransactionSummaryEntry : dateWiseInventoryTransactionSummaryMap.entrySet()) {
				Integer transactionDate = inventoryTransactionSummaryEntry.getKey();
				List<InventoryTransactionSummary> inventoryTransactionSummaryList1 = inventoryTransactionSummaryEntry.getValue();
				double dayLevelQtySold = 0d;
				double dayLevelSellingPrice = 0d;
				double dayLevelInnerAmount = 0d;
				double dayLevelDiscount = 0d;
				double dayLevelTax = 0d;
				double dayLevelAdditionalCost = 0d;
				double dayLevelAdditionalDiscount = 0d;
				double dayLevelPaidAmount = 0d;
				double dayLevelWalletAmount = 0d;
				double dayLevelTotalAmount = 0d;
				colSpan = 0;
				for (InventoryTransactionSummary inventoryTransactionSummary : inventoryTransactionSummaryList1) {
					if (inventoryTransactionSummary == null || CollectionUtils.isEmpty(
							inventoryTransactionSummary.getTradeProductSummaryList())) {
						continue;
					}
					UUID transactionId = inventoryTransactionSummary.getTransactionId();
					List<UUID> transactionIdList = new ArrayList<>();
					int productBatchListSize = 0;
					for (TradeProductSummary tradeProductSummary : inventoryTransactionSummary
							.getTradeProductSummaryList()) {
						productBatchListSize += tradeProductSummary.getTradeProductBatchSummaryList().size();
					}

					for (TradeProductSummary tradeProductSummary : inventoryTransactionSummary
							.getTradeProductSummaryList()) {

						if (tradeProductSummary == null || CollectionUtils.isEmpty(
								tradeProductSummary.getTradeProductBatchSummaryList())) {
							continue;
						}
						for (TradeProductBatchSummary tradeProductBatchSummary : tradeProductSummary
								.getTradeProductBatchSummaryList()) {

							if (tradeProductBatchSummary == null) {
								continue;
							}

							List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();

							int currentRowNumber = rowNum;
							int addForMerging = productBatchListSize - 1;

							if (addForMerging > 0 && !mergedRows.contains(currentRowNumber)) {
								for (int j = currentRowNumber; j <= currentRowNumber + addForMerging; j++) {
									mergedRows.add(j);
								}
								for (int j = 0; j < 8; j++) {

									cellIndexes = new CellIndexes(currentRowNumber,
											currentRowNumber + addForMerging, j, j);

									mergeCellIndexesList.add(cellIndexes);
								}
								for (int j = 14; j < 19; j++) {

									cellIndexes = new CellIndexes(currentRowNumber,
											currentRowNumber + addForMerging, j, j);
									mergeCellIndexesList.add(cellIndexes);
								}
							}

							String date = inventoryTransactionSummary.getTransactionDate() <= 0 ? EMPTY_TEXT
									: DateUtils.getFormattedDate(
									(int) (inventoryTransactionSummary.getTransactionDate() / 1000L));
							reportCellDetailsRow.add(new ReportCellDetails(date, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String invoiceId = StringUtils.isBlank(inventoryTransactionSummary.getInvoiceId()) ? EMPTY_TEXT
									: inventoryTransactionSummary.getInvoiceId();
							reportCellDetailsRow.add(new ReportCellDetails(invoiceId, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String paymentMode = StringUtils
									.isBlank(inventoryTransactionSummary.getTransactionMode().getDisplayName()) ? EMPTY_TEXT
									: inventoryTransactionSummary.getTransactionMode().getDisplayName();
							reportCellDetailsRow.add(new ReportCellDetails(paymentMode, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String customerName = inventoryTransactionSummary.getStudentLite() == null
									? inventoryTransactionSummary.getBuyerName()
									: inventoryTransactionSummary.getStudentLite().getName() + " (" + inventoryTransactionSummary.getStudentLite().getAdmissionNumber() + ")";
							reportCellDetailsRow.add(new ReportCellDetails(customerName, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String soldBy = StringUtils.isBlank(inventoryTransactionSummary.getTransactionBy()) ? EMPTY_TEXT
									: inventoryTransactionSummary.getTransactionBy();
							reportCellDetailsRow.add(new ReportCellDetails(soldBy, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String emailId = StringUtils.isBlank(inventoryTransactionSummary.getEmail()) ? EMPTY_TEXT
									: inventoryTransactionSummary.getEmail();
							reportCellDetailsRow.add(new ReportCellDetails(emailId, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String reference = StringUtils.isBlank(inventoryTransactionSummary.getReference()) ? EMPTY_TEXT
									: inventoryTransactionSummary.getReference();
							reportCellDetailsRow.add(new ReportCellDetails(reference, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String description = StringUtils.isBlank(inventoryTransactionSummary.getDescription())
									? EMPTY_TEXT
									: inventoryTransactionSummary.getDescription();

							reportCellDetailsRow.add(new ReportCellDetails(description, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String productName = StringUtils.isBlank(tradeProductSummary.getProductName()) ? EMPTY_TEXT
									: tradeProductSummary.getProductName();

							reportCellDetailsRow.add(new ReportCellDetails(productName, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String batchName = StringUtils.isBlank(tradeProductBatchSummary.getBatchName()) ? EMPTY_TEXT
									: tradeProductBatchSummary.getBatchName();

							reportCellDetailsRow.add(new ReportCellDetails(batchName, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String qtySold = NumberUtils.formatToIndianCurrency(tradeProductBatchSummary.getQuantity());

							reportCellDetailsRow.add(new ReportCellDetails(qtySold, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
							totalQtySold += tradeProductBatchSummary.getQuantity();
							dayLevelQtySold += tradeProductBatchSummary.getQuantity();

							String sellingPrice = NumberUtils.formatToIndianCurrency(tradeProductBatchSummary.getPricePerItem());

							reportCellDetailsRow.add(new ReportCellDetails(sellingPrice, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
							totalSellingPrice += tradeProductBatchSummary.getPricePerItem();
							dayLevelSellingPrice += tradeProductBatchSummary.getPricePerItem();

							String amount = NumberUtils.formatToIndianCurrency(tradeProductBatchSummary.getTotalPrice());

							reportCellDetailsRow.add(new ReportCellDetails(amount, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
							totalInnerAmount += tradeProductBatchSummary.getTotalPrice();
							dayLevelInnerAmount += tradeProductBatchSummary.getTotalPrice();

							String discount = NumberUtils.formatToIndianCurrency(tradeProductBatchSummary.getTotalDiscount());

							reportCellDetailsRow.add(new ReportCellDetails(discount, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
							totalDiscount += tradeProductBatchSummary.getTotalDiscount();
							dayLevelDiscount += tradeProductBatchSummary.getTotalDiscount();

							String tax = NumberUtils.formatToIndianCurrency(tradeProductBatchSummary.getTotalTax());
							reportCellDetailsRow.add(new ReportCellDetails(tax, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
							totalTax += tradeProductBatchSummary.getTotalTax();
							dayLevelTax += tradeProductBatchSummary.getTotalTax();


							String additionalCost = NumberUtils.formatToIndianCurrency(inventoryTransactionSummary.getAdditionalCost());

							reportCellDetailsRow.add(new ReportCellDetails(additionalCost, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
							if (!transactionIdList.contains(transactionId)) {
								totalAdditionalCost += inventoryTransactionSummary.getAdditionalCost();
								dayLevelAdditionalCost += inventoryTransactionSummary.getAdditionalCost();
							}

							String additionalDiscount = NumberUtils.formatToIndianCurrency(inventoryTransactionSummary.getAdditionalDiscount());

							reportCellDetailsRow.add(new ReportCellDetails(additionalDiscount, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
							if (!transactionIdList.contains(transactionId)) {
								totalAdditionalDiscount += inventoryTransactionSummary.getAdditionalDiscount();
								dayLevelAdditionalDiscount += inventoryTransactionSummary.getAdditionalDiscount();
							}

							double paidAmount = inventoryTransactionSummary.getPaidAmount() == null ? 0
									: inventoryTransactionSummary.getPaidAmount();

							reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(paidAmount), STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
							if (!transactionIdList.contains(transactionId)) {
								totalPaidAmount += paidAmount;
								dayLevelPaidAmount += paidAmount;
							}

							double walletAmount = NumberUtils.addValues(inventoryTransactionSummary.getWalletCreditAmount(),
									inventoryTransactionSummary.getUsedWalletAmount());

							reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(walletAmount), STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
							if (!transactionIdList.contains(transactionId)) {
								totalWalletAmount += walletAmount;
								dayLevelWalletAmount += walletAmount;
							}

							double totalAmount = paidAmount + walletAmount;

							reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalAmount), STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
							if (!transactionIdList.contains(transactionId)) {
								totalTotalAmount += totalAmount;
								dayLevelTotalAmount += totalAmount;
							}

							if (!transactionIdList.contains(transactionId)) {
								transactionIdList.add(inventoryTransactionSummary.getTransactionId());
							}
							reportCellDetails.add(reportCellDetailsRow);
							rowNum++;
						}
					}
				}

				List<ReportCellDetails> dayWiseTotalReportCellDetailsRow = new ArrayList<ReportCellDetails>();

				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails("Daywise " + TOTAL + " for: " + DateUtils.getFormattedDate(transactionDate,
						DATE_FORMAT, User.DFAULT_TIMEZONE), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;

				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelQtySold), STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelSellingPrice), STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));

				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelInnerAmount), STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelDiscount), STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelTax), STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelAdditionalCost), STRING, CONTENT_SIZE,
								BLACK_COLOR, WHITE_COLOR, true));

				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelAdditionalDiscount), STRING, CONTENT_SIZE,
								BLACK_COLOR, WHITE_COLOR, true));

				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelPaidAmount), STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));

				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelWalletAmount), STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));

				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelTotalAmount), STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));

				reportCellDetails.add(dayWiseTotalReportCellDetailsRow);

				cellIndexes = new CellIndexes(rowNum, rowNum, 0, colSpan);
				mergeCellIndexesList.add(cellIndexes);
				rowNum++;
			}

			// Total Row Values

			colSpan = 0;
			List<ReportCellDetails> reportCellDetailsTotalRow = new ArrayList<ReportCellDetails>();
			reportCellDetailsTotalRow.add(new ReportCellDetails(TOTAL, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalQtySold), STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalSellingPrice), STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalInnerAmount), STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalDiscount), STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalTax), STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsTotalRow
					.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalAdditionalCost), STRING, CONTENT_SIZE,
							BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsTotalRow
					.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalAdditionalDiscount), STRING, CONTENT_SIZE,
							BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalPaidAmount), STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalWalletAmount), STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalTotalAmount), STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetails.add(reportCellDetailsTotalRow);
			rowNum++;

			cellIndexes = new CellIndexes(rowNum - 1, rowNum - 1, 0, colSpan);

			mergeCellIndexesList.add(cellIndexes);

			List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
//			ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, mergeCellIndexesList,
//					reportCellDetails);

			ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true,
					totalColumns, headerMergeCellIndexesList,
					mergeCellIndexesList, headerReportCellDetails, reportCellDetails, true,
					true, institute, totalColumns);

			reportSheetDetailsList.add(reportSheetDetails);
			return new ReportDetails(reportName, reportSheetDetailsList);

		} catch (final Exception e) {
			logger.error("Error while generating sales report", e);
		}

		return null;
	}

	private ReportDetails generateSalesReturnReports(int instituteId, int startDate, int endDate) {
		try {

			String reportName = "Sales Return Details Report";
			String sheetName = "SalesReturnDetailsReport";

			Institute institute = instituteManager.getInstitute(instituteId);

			if (institute == null) {
				logger.info("Empty report {}", reportName);
				List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<>();
				ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, null,
						null);
				reportSheetDetailsList.add(reportSheetDetails);
				return new ReportDetails(reportName, reportSheetDetailsList);
			}

			List<InventoryTransactionSummary> inventoryTransactionSummaryList = inventoryTransactionsManager
					.getTransactionDetailsList(instituteId, startDate, endDate, InventoryTransactionType.SALES_RETURN);

			List<List<ReportCellDetails>> headerReportCellDetails = new ArrayList<List<ReportCellDetails>>();
			List<CellIndexes> headerMergeCellIndexesList = new ArrayList<>();

			if (CollectionUtils.isEmpty(inventoryTransactionSummaryList)) {
				logger.info("Empty report {}", reportName);
				List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<>();
				ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, null,
						null);
				reportSheetDetailsList.add(reportSheetDetails);
				return new ReportDetails(reportName, reportSheetDetailsList);
			}

			List<ReportCellDetails> reportHeaderRow = new ArrayList<ReportCellDetails>();

			reportHeaderRow
					.add(new ReportCellDetails(reportName, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
			headerReportCellDetails.add(reportHeaderRow);
			List<CellIndexes> mergeCellIndexesList = new ArrayList<CellIndexes>();

			List<ReportCellDetails> reportCellDetailsHeaderRow = new ArrayList<ReportCellDetails>();
			int i = 0;
			for (i = 0; i < SALES_RETURN_DETAILS_REPORT_COLUMN.length; i++) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(SALES_RETURN_DETAILS_REPORT_COLUMN[i], STRING,
						CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
			}
			headerReportCellDetails.add(reportCellDetailsHeaderRow);

			int totalColumns = SALES_RETURN_DETAILS_REPORT_COLUMN.length;
			CellIndexes cellIndexes = new CellIndexes(0, 0, 0, totalColumns - 1);
			headerMergeCellIndexesList.add(cellIndexes);
//
//			Collections.sort(inventoryTransactionSummaryList, new Comparator<InventoryTransactionSummary>() {
//				@Override
//				public int compare(InventoryTransactionSummary inventoryTransactionSummary1,
//						InventoryTransactionSummary inventoryTransactionSummary2) {
//					return ((int) (inventoryTransactionSummary2.getTransactionDate() / 1000L) -
//							(int) (inventoryTransactionSummary1.getTransactionDate() / 1000L));
//				}
//			});

			TreeMap<Integer, List<InventoryTransactionSummary>> dateWiseInventoryTransactionSummaryMap = getDateWiseInventoryTransactionSummaryMap(inventoryTransactionSummaryList);

			List<List<ReportCellDetails>> reportCellDetails = new ArrayList<List<ReportCellDetails>>();
			int rowNum = 2;
			int colSpan = 0;
			double totalQtyReturned = 0d;
			double totalSellingPrice = 0d;
			double totalInnerAmount = 0d;
			double totalDiscount = 0d;
			double totalTax = 0d;
			double totalAdditionalCost = 0d;
			double totalAdditionalDiscount = 0d;
			double totalPaidAmount = 0d;
			List<Integer> mergedRows = new ArrayList<>();
			for (final Map.Entry<Integer, List<InventoryTransactionSummary>> inventoryTransactionSummaryEntry : dateWiseInventoryTransactionSummaryMap.entrySet()) {
				Integer transactionDate = inventoryTransactionSummaryEntry.getKey();
				List<InventoryTransactionSummary> inventoryTransactionSummaryList1 = inventoryTransactionSummaryEntry.getValue();
				double dayLevelQtyReturned = 0d;
				double dayLevelSellingPrice = 0d;
				double dayLevelInnerAmount = 0d;
				double dayLevelDiscount = 0d;
				double dayLevelTax = 0d;
				double dayLevelAdditionalCost = 0d;
				double dayLevelAdditionalDiscount = 0d;
				double dayLevelPaidAmount = 0d;
				colSpan = 0;
				for (InventoryTransactionSummary inventoryTransactionSummary : inventoryTransactionSummaryList1) {
					if (inventoryTransactionSummary == null || CollectionUtils.isEmpty(
							inventoryTransactionSummary.getTradeProductSummaryList())) {
						continue;
					}
					UUID transactionId = inventoryTransactionSummary.getTransactionId();
					List<UUID> transactionIdList = new ArrayList<>();
					int productBatchListSize = 0;
					for (TradeProductSummary tradeProductSummary : inventoryTransactionSummary
							.getTradeProductSummaryList()) {
						productBatchListSize += tradeProductSummary.getTradeProductBatchSummaryList().size();
					}
					for (TradeProductSummary tradeProductSummary : inventoryTransactionSummary
							.getTradeProductSummaryList()) {

						if (tradeProductSummary == null || CollectionUtils.isEmpty(
								tradeProductSummary.getTradeProductBatchSummaryList())) {
							continue;
						}
						for (TradeProductBatchSummary tradeProductBatchSummary : tradeProductSummary
								.getTradeProductBatchSummaryList()) {

							if (tradeProductBatchSummary == null) {
								continue;
							}

							List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();
							int currentRowNumber = rowNum + 1;

							int addForMerging = productBatchListSize - 1;

							if (addForMerging > 0 && !mergedRows.contains(currentRowNumber)) {
								for (int j = currentRowNumber; j <= currentRowNumber + addForMerging; j++) {

									mergedRows.add(j);
								}
								for (int j = 0; j < 8; j++) {
									cellIndexes = new CellIndexes(currentRowNumber,
											currentRowNumber + addForMerging, j, j);

									mergeCellIndexesList.add(cellIndexes);
								}
								for (int j = 14; j < 17; j++) {
									cellIndexes = new CellIndexes(currentRowNumber,
											currentRowNumber + addForMerging, j, j);

									mergeCellIndexesList.add(cellIndexes);
								}
							}

							String date = inventoryTransactionSummary.getTransactionDate() <= 0 ? EMPTY_TEXT
									: DateUtils.getFormattedDate(
									(int) (inventoryTransactionSummary.getTransactionDate() / 1000L));

							reportCellDetailsRow.add(new ReportCellDetails(date, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String invoiceId = StringUtils.isBlank(inventoryTransactionSummary.getInvoiceId()) ? EMPTY_TEXT
									: inventoryTransactionSummary.getInvoiceId();

							reportCellDetailsRow.add(new ReportCellDetails(invoiceId, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String paymentMode = StringUtils
									.isBlank(inventoryTransactionSummary.getTransactionMode().getDisplayName()) ? EMPTY_TEXT
									: inventoryTransactionSummary.getTransactionMode().getDisplayName();

							reportCellDetailsRow.add(new ReportCellDetails(paymentMode, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String customerName = inventoryTransactionSummary.getStudentLite() == null
									? inventoryTransactionSummary.getBuyerName()
									: inventoryTransactionSummary.getStudentLite().getName();

							reportCellDetailsRow.add(new ReportCellDetails(customerName, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String returnTo = StringUtils.isBlank(inventoryTransactionSummary.getTransactionBy())
									? EMPTY_TEXT
									: inventoryTransactionSummary.getTransactionBy();

							reportCellDetailsRow.add(new ReportCellDetails(returnTo, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String emailId = StringUtils.isBlank(inventoryTransactionSummary.getEmail()) ? EMPTY_TEXT
									: inventoryTransactionSummary.getEmail();

							reportCellDetailsRow.add(new ReportCellDetails(emailId, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String reference = StringUtils.isBlank(inventoryTransactionSummary.getReference()) ? EMPTY_TEXT
									: inventoryTransactionSummary.getReference();

							reportCellDetailsRow.add(new ReportCellDetails(reference, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String description = StringUtils.isBlank(inventoryTransactionSummary.getDescription())
									? EMPTY_TEXT
									: inventoryTransactionSummary.getDescription();

							reportCellDetailsRow.add(new ReportCellDetails(description, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String productName = StringUtils.isBlank(tradeProductSummary.getProductName()) ? EMPTY_TEXT
									: tradeProductSummary.getProductName();

							reportCellDetailsRow.add(new ReportCellDetails(productName, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String batchName = StringUtils.isBlank(tradeProductBatchSummary.getBatchName()) ? EMPTY_TEXT
									: tradeProductBatchSummary.getBatchName();

							reportCellDetailsRow.add(new ReportCellDetails(batchName, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String qtyReturned = NumberUtils.formatToIndianCurrency(tradeProductBatchSummary.getQuantity());

							reportCellDetailsRow.add(new ReportCellDetails(qtyReturned, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
							totalQtyReturned += tradeProductBatchSummary.getQuantity();
							dayLevelQtyReturned += tradeProductBatchSummary.getQuantity();

							String sellingPrice = NumberUtils.formatToIndianCurrency(tradeProductBatchSummary.getPricePerItem());

							reportCellDetailsRow.add(new ReportCellDetails(sellingPrice, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
							totalSellingPrice += tradeProductBatchSummary.getPricePerItem();
							dayLevelSellingPrice += tradeProductBatchSummary.getPricePerItem();

							String amount = NumberUtils.formatToIndianCurrency(tradeProductBatchSummary.getTotalPrice());

							reportCellDetailsRow.add(new ReportCellDetails(amount, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
							totalInnerAmount += tradeProductBatchSummary.getTotalPrice();
							dayLevelInnerAmount += tradeProductBatchSummary.getTotalPrice();

							String discount = NumberUtils.formatToIndianCurrency(tradeProductBatchSummary.getTotalDiscount());

							reportCellDetailsRow.add(new ReportCellDetails(discount, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
							totalDiscount += tradeProductBatchSummary.getTotalDiscount();
							dayLevelDiscount += tradeProductBatchSummary.getTotalDiscount();

							String tax = NumberUtils.formatToIndianCurrency(tradeProductBatchSummary.getTotalTax());

							reportCellDetailsRow.add(new ReportCellDetails(tax, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
							totalTax += tradeProductBatchSummary.getTotalTax();
							dayLevelTax += tradeProductBatchSummary.getTotalTax();

							String additionalCost = NumberUtils.formatToIndianCurrency(inventoryTransactionSummary.getAdditionalCost());

							reportCellDetailsRow.add(new ReportCellDetails(additionalCost, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
							if (!transactionIdList.contains(transactionId)) {
								totalAdditionalCost += inventoryTransactionSummary.getAdditionalCost();
								dayLevelAdditionalCost += inventoryTransactionSummary.getAdditionalCost();
							}

							String additionalDiscount = NumberUtils.formatToIndianCurrency(inventoryTransactionSummary.getAdditionalDiscount());

							reportCellDetailsRow.add(new ReportCellDetails(additionalDiscount, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
							if (!transactionIdList.contains(transactionId)) {
								totalAdditionalDiscount += inventoryTransactionSummary.getAdditionalDiscount();
								dayLevelAdditionalDiscount += inventoryTransactionSummary.getAdditionalDiscount();
							}

							double paidAmount = inventoryTransactionSummary.getPaidAmount() == null ? 0
									: inventoryTransactionSummary.getPaidAmount();
							String paidAmountStr = NumberUtils.formatToIndianCurrency(paidAmount);

							reportCellDetailsRow.add(new ReportCellDetails(paidAmountStr, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
							if (!transactionIdList.contains(transactionId)) {
								totalPaidAmount += paidAmount;
								dayLevelPaidAmount += paidAmount;
							}

							if (!transactionIdList.contains(transactionId)) {
								transactionIdList.add(inventoryTransactionSummary.getTransactionId());
							}
							reportCellDetails.add(reportCellDetailsRow);
							rowNum++;
						}
					}
				}

				List<ReportCellDetails> dayWiseTotalReportCellDetailsRow = new ArrayList<ReportCellDetails>();

				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails("Daywise " + TOTAL + " for: " + DateUtils.getFormattedDate(transactionDate,
						DATE_FORMAT, User.DFAULT_TIMEZONE), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;

				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelQtyReturned), STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelSellingPrice), STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));

				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelInnerAmount), STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelDiscount), STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelTax), STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelAdditionalCost), STRING, CONTENT_SIZE,
								BLACK_COLOR, WHITE_COLOR, true));

				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelAdditionalDiscount), STRING, CONTENT_SIZE,
								BLACK_COLOR, WHITE_COLOR, true));

				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelPaidAmount), STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));

				reportCellDetails.add(dayWiseTotalReportCellDetailsRow);


				cellIndexes = new CellIndexes(rowNum, rowNum, 0, colSpan);
				mergeCellIndexesList.add(cellIndexes);
				rowNum++;
			}

			// Total Row Values

			colSpan = 0;
			List<ReportCellDetails> reportCellDetailsTotalRow = new ArrayList<ReportCellDetails>();
			reportCellDetailsTotalRow.add(new ReportCellDetails(TOTAL, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;

			reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalQtyReturned), STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalSellingPrice), STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalInnerAmount), STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalDiscount), STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalTax), STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			reportCellDetailsTotalRow
					.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalAdditionalCost), STRING, CONTENT_SIZE,
							BLACK_COLOR, WHITE_COLOR, true));
			reportCellDetailsTotalRow
					.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalAdditionalDiscount), STRING, CONTENT_SIZE,
							BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalPaidAmount), STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetails.add(reportCellDetailsTotalRow);
			rowNum++;
			cellIndexes = new CellIndexes(rowNum - 1, rowNum - 1, 0, colSpan);

			mergeCellIndexesList.add(cellIndexes);

			List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
//			ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, mergeCellIndexesList,
//					reportCellDetails);

			ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true,
					totalColumns, headerMergeCellIndexesList,
					mergeCellIndexesList, headerReportCellDetails, reportCellDetails, true,
					true, institute, totalColumns);

			reportSheetDetailsList.add(reportSheetDetails);
			return new ReportDetails(reportName, reportSheetDetailsList);

		} catch (final Exception e) {

			logger.error("Error while generating sales return report", e);
		}

		return null;
	}

	private ReportDetails generatePurchaseReports(int instituteId, int startDate, int endDate) {
		try {

			String reportName = "Purchase Details Report";
			String sheetName = "PurchaseDetailsReport";

			Institute institute = instituteManager.getInstitute(instituteId);

			if (institute == null) {
				logger.info("Empty report {}", reportName);
				List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<>();
				ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, null,
						null);
				reportSheetDetailsList.add(reportSheetDetails);
				return new ReportDetails(reportName, reportSheetDetailsList);
			}

			List<InventoryTransactionSummary> inventoryTransactionSummaryList = inventoryTransactionsManager
					.getTransactionDetailsList(instituteId, startDate, endDate, InventoryTransactionType.PURCHASE);

			List<List<ReportCellDetails>> headerReportCellDetails = new ArrayList<List<ReportCellDetails>>();
			List<CellIndexes> headerMergeCellIndexesList = new ArrayList<>();

			if (CollectionUtils.isEmpty(inventoryTransactionSummaryList)) {
				logger.info("Empty report {}", reportName);
				List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<>();
				ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, null,
						null);
				reportSheetDetailsList.add(reportSheetDetails);
				return new ReportDetails(reportName, reportSheetDetailsList);
			}

			List<ReportCellDetails> reportHeaderRow = new ArrayList<ReportCellDetails>();

			reportHeaderRow
					.add(new ReportCellDetails(reportName, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
			headerReportCellDetails.add(reportHeaderRow);
			List<CellIndexes> mergeCellIndexesList = new ArrayList<CellIndexes>();
			List<ReportCellDetails> reportCellDetailsHeaderRow = new ArrayList<ReportCellDetails>();
			int i = 0;
			for (i = 0; i < PURCHASE_DETAILS_REPORT_COLUMN.length; i++) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(PURCHASE_DETAILS_REPORT_COLUMN[i], STRING,
						CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
			}
			headerReportCellDetails.add(reportCellDetailsHeaderRow);

			int totalColumns = PURCHASE_DETAILS_REPORT_COLUMN.length;
			CellIndexes cellIndexes = new CellIndexes(0, 0, 0, totalColumns - 1);
			headerMergeCellIndexesList.add(cellIndexes);

//			Collections.sort(inventoryTransactionSummaryList, new Comparator<InventoryTransactionSummary>() {
//				@Override
//				public int compare(InventoryTransactionSummary inventoryTransactionSummary1,
//						InventoryTransactionSummary inventoryTransactionSummary2) {
//					return ((int) (inventoryTransactionSummary2.getTransactionDate() / 1000L) -
//							(int) (inventoryTransactionSummary1.getTransactionDate() / 1000L));
//				}
//			});

			TreeMap<Integer, List<InventoryTransactionSummary>> dateWiseInventoryTransactionSummaryMap = getDateWiseInventoryTransactionSummaryMap(inventoryTransactionSummaryList);

			List<List<ReportCellDetails>> reportCellDetails = new ArrayList<List<ReportCellDetails>>();
			int rowNum = 2;
			int colSpan = 0;
			double totalQtyPurchased = 0;
			double totalPurchasePrice = 0;
			double totalInnerAmount = 0;
			double totalDiscount = 0;
			double totalTax = 0;
			double totalAdditionalCost = 0;
			double totalAdditionalDiscount = 0;
			double totalNetAmount = 0;
			List<Integer> mergedRows = new ArrayList<>();
			List<UUID> transactionIdList = new ArrayList<>();
			for (final Map.Entry<Integer, List<InventoryTransactionSummary>> inventoryTransactionSummaryEntry : dateWiseInventoryTransactionSummaryMap.entrySet()) {
				Integer transactionDate = inventoryTransactionSummaryEntry.getKey();
				List<InventoryTransactionSummary> inventoryTransactionSummaryList1 = inventoryTransactionSummaryEntry.getValue();
				double dayLevelQtyPurchased = 0;
				double dayLevelPurchasePrice = 0;
				double dayLevelInnerAmount = 0;
				double dayLevelDiscount = 0;
				double dayLevelTax = 0;
				double dayLevelAdditionalCost = 0;
				double dayLevelAdditionalDiscount = 0;
				double dayLevelNetAmount = 0;
				colSpan = 0;
				for (InventoryTransactionSummary inventoryTransactionSummary : inventoryTransactionSummaryList1) {
					if (inventoryTransactionSummary == null || CollectionUtils.isEmpty(
							inventoryTransactionSummary.getTradeProductSummaryList())) {
						continue;
					}
					UUID transactionId = inventoryTransactionSummary.getTransactionId();
					int productBatchListSize = 0;
					for (TradeProductSummary tradeProductSummary : inventoryTransactionSummary
							.getTradeProductSummaryList()) {
						productBatchListSize += tradeProductSummary.getTradeProductBatchSummaryList().size();
					}
					for (TradeProductSummary tradeProductSummary : inventoryTransactionSummary
							.getTradeProductSummaryList()) {

						if (tradeProductSummary == null || CollectionUtils.isEmpty(
								tradeProductSummary.getTradeProductBatchSummaryList())) {
							continue;
						}
						for (TradeProductBatchSummary tradeProductBatchSummary : tradeProductSummary
								.getTradeProductBatchSummaryList()) {

							if (tradeProductBatchSummary == null) {
								continue;
							}

							List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();
							int currentRowNumber = rowNum + 1;
							int addForMerging = productBatchListSize - 1;

							if (addForMerging > 0 && !mergedRows.contains(currentRowNumber)) {
								for (int j = currentRowNumber; j <= currentRowNumber + addForMerging; j++) {
									mergedRows.add(j);
								}
								for (int j = 0; j < 7; j++) {

									cellIndexes = new CellIndexes(currentRowNumber,
											currentRowNumber + addForMerging, j, j);

									mergeCellIndexesList.add(cellIndexes);

								}
								for (int j = 14; j < 17; j++) {

									cellIndexes = new CellIndexes(currentRowNumber,
											currentRowNumber + addForMerging, j, j);

									mergeCellIndexesList.add(cellIndexes);
								}
							}

							String date = inventoryTransactionSummary.getTransactionDate() <= 0 ? EMPTY_TEXT
									: DateUtils.getFormattedDate(
									(int) (inventoryTransactionSummary.getTransactionDate() / 1000L));

							reportCellDetailsRow.add(new ReportCellDetails(date, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String invoiceId = StringUtils.isBlank(inventoryTransactionSummary.getInvoiceId()) ? EMPTY_TEXT
									: inventoryTransactionSummary.getInvoiceId();

							reportCellDetailsRow.add(new ReportCellDetails(invoiceId, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String paymentMode = StringUtils
									.isBlank(inventoryTransactionSummary.getTransactionMode().getDisplayName()) ? EMPTY_TEXT
									: inventoryTransactionSummary.getTransactionMode().getDisplayName();

							reportCellDetailsRow.add(new ReportCellDetails(paymentMode, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String supplierName = inventoryTransactionSummary.getSupplier() == null ? EMPTY_TEXT
									: inventoryTransactionSummary.getSupplier().getSupplierName();

							reportCellDetailsRow.add(new ReportCellDetails(supplierName, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String purchasedBy = StringUtils.isBlank(inventoryTransactionSummary.getTransactionBy())
									? EMPTY_TEXT
									: inventoryTransactionSummary.getTransactionBy();
							reportCellDetailsRow.add(new ReportCellDetails(purchasedBy, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String billNumber = StringUtils.isBlank(inventoryTransactionSummary.getReference()) ? EMPTY_TEXT
									: inventoryTransactionSummary.getReference();

							reportCellDetailsRow.add(new ReportCellDetails(billNumber, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String description = StringUtils.isBlank(inventoryTransactionSummary.getDescription())
									? EMPTY_TEXT
									: inventoryTransactionSummary.getDescription();

							reportCellDetailsRow.add(new ReportCellDetails(description, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String productName = StringUtils.isBlank(tradeProductSummary.getProductName()) ? EMPTY_TEXT
									: tradeProductSummary.getProductName();

							reportCellDetailsRow.add(new ReportCellDetails(productName, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String batchName = StringUtils.isBlank(tradeProductBatchSummary.getBatchName()) ? EMPTY_TEXT
									: tradeProductBatchSummary.getBatchName();
							reportCellDetailsRow.add(new ReportCellDetails(batchName, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String qtyPurchased = NumberUtils.formatToIndianCurrency(tradeProductBatchSummary.getQuantity());

							reportCellDetailsRow.add(new ReportCellDetails(qtyPurchased, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
							totalQtyPurchased += tradeProductBatchSummary.getQuantity();
							dayLevelQtyPurchased += tradeProductBatchSummary.getQuantity();

							String purchasedPrice = NumberUtils.formatToIndianCurrency(tradeProductBatchSummary.getPricePerItem());

							reportCellDetailsRow.add(new ReportCellDetails(purchasedPrice, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
							totalPurchasePrice += tradeProductBatchSummary.getPricePerItem();
							dayLevelPurchasePrice += tradeProductBatchSummary.getPricePerItem();

							String amount = NumberUtils.formatToIndianCurrency(tradeProductBatchSummary.getTotalPrice());

							reportCellDetailsRow.add(new ReportCellDetails(amount, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
							totalInnerAmount += tradeProductBatchSummary.getTotalPrice();
							dayLevelInnerAmount += tradeProductBatchSummary.getTotalPrice();

							String discount = NumberUtils.formatToIndianCurrency(tradeProductBatchSummary.getTotalDiscount());

							reportCellDetailsRow.add(new ReportCellDetails(discount, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
							totalDiscount += tradeProductBatchSummary.getTotalDiscount();
							dayLevelDiscount += tradeProductBatchSummary.getTotalDiscount();

							String tax = NumberUtils.formatToIndianCurrency(tradeProductBatchSummary.getTotalTax());

							reportCellDetailsRow.add(new ReportCellDetails(tax, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
							totalTax += tradeProductBatchSummary.getTotalTax();
							dayLevelTax += tradeProductBatchSummary.getTotalTax();

							String additionalCost = NumberUtils.formatToIndianCurrency(inventoryTransactionSummary.getAdditionalCost());

							reportCellDetailsRow.add(new ReportCellDetails(additionalCost, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
							if (!transactionIdList.contains(transactionId)) {
								totalAdditionalCost += inventoryTransactionSummary.getAdditionalCost();
								dayLevelAdditionalCost += inventoryTransactionSummary.getAdditionalCost();
							}

							String additionalDiscount = NumberUtils.formatToIndianCurrency(inventoryTransactionSummary.getAdditionalDiscount());

							reportCellDetailsRow.add(new ReportCellDetails(additionalDiscount, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
							if (!transactionIdList.contains(transactionId)) {
								totalAdditionalDiscount += inventoryTransactionSummary.getAdditionalDiscount();
								dayLevelAdditionalDiscount += inventoryTransactionSummary.getAdditionalDiscount();
							}

							double paidAmount = inventoryTransactionSummary.getPaidAmount() == null ? 0
									: inventoryTransactionSummary.getPaidAmount();
							String paidAmountStr = NumberUtils.formatToIndianCurrency(paidAmount);

							reportCellDetailsRow.add(new ReportCellDetails(paidAmountStr, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
							if (!transactionIdList.contains(transactionId)) {
								totalNetAmount += paidAmount;
								dayLevelNetAmount += paidAmount;
							}

							if (!transactionIdList.contains(transactionId)) {
								transactionIdList.add(inventoryTransactionSummary.getTransactionId());
							}
							reportCellDetails.add(reportCellDetailsRow);
							rowNum++;
						}
					}
				}

				List<ReportCellDetails> dayWiseTotalReportCellDetailsRow = new ArrayList<ReportCellDetails>();

				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails("Daywise " + TOTAL + " for: " + DateUtils.getFormattedDate(transactionDate,
						DATE_FORMAT, User.DFAULT_TIMEZONE), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;

				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelQtyPurchased), STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));

				dayWiseTotalReportCellDetailsRow
						.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelPurchasePrice), STRING, CONTENT_SIZE,
								BLACK_COLOR, WHITE_COLOR, true));

				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelInnerAmount), STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));

				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelDiscount), STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));

				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelTax), STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));

				dayWiseTotalReportCellDetailsRow
						.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelAdditionalCost), STRING, CONTENT_SIZE,
								BLACK_COLOR, WHITE_COLOR, true));

				dayWiseTotalReportCellDetailsRow
						.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelAdditionalDiscount), STRING, CONTENT_SIZE,
								BLACK_COLOR, WHITE_COLOR, true));

				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelNetAmount), STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));

				reportCellDetails.add(dayWiseTotalReportCellDetailsRow);

				cellIndexes = new CellIndexes(rowNum, rowNum, 0, colSpan);
				mergeCellIndexesList.add(cellIndexes);
				rowNum++;

			}

			// Total Row Values
			colSpan = 0;
			List<ReportCellDetails> reportCellDetailsTotalRow = new ArrayList<ReportCellDetails>();

			reportCellDetailsTotalRow.add(new ReportCellDetails(TOTAL, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;

			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;

			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;

			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;

			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;

			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;

			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;

			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;

			reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalQtyPurchased), STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsTotalRow
					.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalPurchasePrice), STRING, CONTENT_SIZE,
							BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalInnerAmount), STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalDiscount), STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalTax), STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsTotalRow
					.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalAdditionalCost), STRING, CONTENT_SIZE,
							BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsTotalRow
					.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalAdditionalDiscount), STRING, CONTENT_SIZE,
							BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalNetAmount), STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			reportCellDetails.add(reportCellDetailsTotalRow);
			rowNum++;

			cellIndexes = new CellIndexes(rowNum - 1, rowNum - 1, 0, colSpan);

			mergeCellIndexesList.add(cellIndexes);

			List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
//			ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, mergeCellIndexesList,
//					reportCellDetails);
			ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true,
					totalColumns, headerMergeCellIndexesList,
					mergeCellIndexesList, headerReportCellDetails, reportCellDetails, true,
					true, institute, totalColumns);
			reportSheetDetailsList.add(reportSheetDetails);
			return new ReportDetails(reportName, reportSheetDetailsList);

		} catch (final Exception e) {

			logger.error("Error while generating purchase report", e);
		}

		return null;
	}

	private ReportDetails generatePurchaseReturnReports(int instituteId, int startDate, int endDate) {
		try {

			String reportName = "Purchase Return Details Report";
			String sheetName = "PurchaseReturnDetailsReport";

			Institute institute = instituteManager.getInstitute(instituteId);

			if (institute == null) {
				logger.info("Empty report {}", reportName);
				List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<>();
				ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, null,
						null);
				reportSheetDetailsList.add(reportSheetDetails);
				return new ReportDetails(reportName, reportSheetDetailsList);
			}

			List<InventoryTransactionSummary> inventoryTransactionSummaryList = inventoryTransactionsManager
					.getTransactionDetailsList(instituteId, startDate, endDate, InventoryTransactionType.RETURN);

			List<List<ReportCellDetails>> headerReportCellDetails = new ArrayList<List<ReportCellDetails>>();
			List<CellIndexes> headerMergeCellIndexesList = new ArrayList<>();

			if (CollectionUtils.isEmpty(inventoryTransactionSummaryList)) {
				logger.info("Empty report {}", reportName);
				List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<>();
				ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, null,
						null);
				reportSheetDetailsList.add(reportSheetDetails);
				return new ReportDetails(reportName, reportSheetDetailsList);
			}

			List<ReportCellDetails> reportHeaderRow = new ArrayList<ReportCellDetails>();

			reportHeaderRow
					.add(new ReportCellDetails(reportName, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
			headerReportCellDetails.add(reportHeaderRow);

			List<ReportCellDetails> reportCellDetailsHeaderRow = new ArrayList<ReportCellDetails>();
			int i = 0;
			for (i = 0; i < PURCHASE_RETURN_DETAILS_REPORT_COLUMN.length; i++) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(PURCHASE_RETURN_DETAILS_REPORT_COLUMN[i], STRING,
						CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
			}
			headerReportCellDetails.add(reportCellDetailsHeaderRow);

			int totalColumns = PURCHASE_RETURN_DETAILS_REPORT_COLUMN.length;
			CellIndexes cellIndexes = new CellIndexes(0, 0, 0, totalColumns - 1);
			headerMergeCellIndexesList.add(cellIndexes);

//			Collections.sort(inventoryTransactionSummaryList, new Comparator<InventoryTransactionSummary>() {
//				@Override
//				public int compare(InventoryTransactionSummary inventoryTransactionSummary1,
//						InventoryTransactionSummary inventoryTransactionSummary2) {
//					return ((int) (inventoryTransactionSummary2.getTransactionDate() / 1000L) -
//							(int) (inventoryTransactionSummary1.getTransactionDate() / 1000L));
//				}
//			});

			TreeMap<Integer, List<InventoryTransactionSummary>> dateWiseInventoryTransactionSummaryMap = getDateWiseInventoryTransactionSummaryMap(inventoryTransactionSummaryList);

			List<List<ReportCellDetails>> reportCellDetails = new ArrayList<List<ReportCellDetails>>();
			int rowNum = 2;
			int colSpan = 0;
			double totalQtyReturned = 0;
			double totalPurchasePrice = 0;
			double totalInnerAmount = 0;
			double totalDiscount = 0;
			double totalTax = 0;
			double totalAdditionalCost = 0;
			double totalAdditionalDiscount = 0;
			double totalNetAmount = 0;
			List<UUID> transactionIdList = new ArrayList<>();
			List<Integer> mergedRows = new ArrayList<>();
			List<CellIndexes> mergeCellIndexesList = new ArrayList<CellIndexes>();
			for (final Map.Entry<Integer, List<InventoryTransactionSummary>> inventoryTransactionSummaryEntry : dateWiseInventoryTransactionSummaryMap.entrySet()) {
				Integer transactionDate = inventoryTransactionSummaryEntry.getKey();
				List<InventoryTransactionSummary> inventoryTransactionSummaryList1 = inventoryTransactionSummaryEntry.getValue();
				double dayLevelQtyReturned = 0;
				double dayLevelPurchasePrice = 0;
				double dayLevelInnerAmount = 0;
				double dayLevelDiscount = 0;
				double dayLevelTax = 0;
				double dayLevelAdditionalCost = 0;
				double dayLevelAdditionalDiscount = 0;
				double dayLevelNetAmount = 0;
				colSpan = 0;
				for (InventoryTransactionSummary inventoryTransactionSummary : inventoryTransactionSummaryList1) {
					if (inventoryTransactionSummary == null || CollectionUtils.isEmpty(
							inventoryTransactionSummary.getTradeProductSummaryList())) {
						continue;
					}
					UUID transactionId = inventoryTransactionSummary.getTransactionId();
					int productBatchListSize = 0;
					for (TradeProductSummary tradeProductSummary : inventoryTransactionSummary
							.getTradeProductSummaryList()) {
						productBatchListSize += tradeProductSummary.getTradeProductBatchSummaryList().size();
					}
					for (TradeProductSummary tradeProductSummary : inventoryTransactionSummary
							.getTradeProductSummaryList()) {

						if (tradeProductSummary == null || CollectionUtils.isEmpty(
								tradeProductSummary.getTradeProductBatchSummaryList())) {
							continue;
						}
						for (TradeProductBatchSummary tradeProductBatchSummary : tradeProductSummary
								.getTradeProductBatchSummaryList()) {

							if (tradeProductBatchSummary == null) {
								continue;
							}

							List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();
							int currentRowNumber = rowNum + 1;
							int addForMerging = productBatchListSize - 1;

							if (addForMerging > 0 && !mergedRows.contains(currentRowNumber)) {
								for (int j = currentRowNumber; j <= currentRowNumber + addForMerging; j++) {
									mergedRows.add(j);
								}
								for (int j = 0; j < 7; j++) {

									cellIndexes = new CellIndexes(currentRowNumber,
											currentRowNumber + addForMerging, j, j);

									mergeCellIndexesList.add(cellIndexes);

								}
								for (int j = 14; j < 17; j++) {
									cellIndexes = new CellIndexes(currentRowNumber,
											currentRowNumber + addForMerging, j, j);

									mergeCellIndexesList.add(cellIndexes);

								}
							}

							String date = inventoryTransactionSummary.getTransactionDate() <= 0 ? EMPTY_TEXT
									: DateUtils.getFormattedDate(
									(int) (inventoryTransactionSummary.getTransactionDate() / 1000L));

							reportCellDetailsRow.add(new ReportCellDetails(date, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String invoiceId = StringUtils.isBlank(inventoryTransactionSummary.getInvoiceId()) ? EMPTY_TEXT
									: inventoryTransactionSummary.getInvoiceId();

							reportCellDetailsRow.add(new ReportCellDetails(invoiceId, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String paymentMode = StringUtils
									.isBlank(inventoryTransactionSummary.getTransactionMode().getDisplayName()) ? EMPTY_TEXT
									: inventoryTransactionSummary.getTransactionMode().getDisplayName();

							reportCellDetailsRow.add(new ReportCellDetails(paymentMode, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String supplierName = inventoryTransactionSummary.getSupplier() == null ? EMPTY_TEXT
									: inventoryTransactionSummary.getSupplier().getSupplierName();

							reportCellDetailsRow.add(new ReportCellDetails(supplierName, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String returnBy = StringUtils.isBlank(inventoryTransactionSummary.getTransactionBy())
									? EMPTY_TEXT
									: inventoryTransactionSummary.getTransactionBy();

							reportCellDetailsRow.add(new ReportCellDetails(returnBy, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String reference = StringUtils.isBlank(inventoryTransactionSummary.getReference()) ? EMPTY_TEXT
									: inventoryTransactionSummary.getReference();

							reportCellDetailsRow.add(new ReportCellDetails(reference, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String description = StringUtils.isBlank(inventoryTransactionSummary.getDescription())
									? EMPTY_TEXT
									: inventoryTransactionSummary.getDescription();

							reportCellDetailsRow.add(new ReportCellDetails(description, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String productName = StringUtils.isBlank(tradeProductSummary.getProductName()) ? EMPTY_TEXT
									: tradeProductSummary.getProductName();

							reportCellDetailsRow.add(new ReportCellDetails(productName, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String batchName = StringUtils.isBlank(tradeProductBatchSummary.getBatchName()) ? EMPTY_TEXT
									: tradeProductBatchSummary.getBatchName();

							reportCellDetailsRow.add(new ReportCellDetails(batchName, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String qtyReturned = NumberUtils.formatToIndianCurrency(tradeProductBatchSummary.getQuantity());

							reportCellDetailsRow.add(new ReportCellDetails(qtyReturned, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
							totalQtyReturned += tradeProductBatchSummary.getQuantity();
							dayLevelQtyReturned += tradeProductBatchSummary.getQuantity();

							String purchasePrice = NumberUtils.formatToIndianCurrency(tradeProductBatchSummary.getPricePerItem());

							reportCellDetailsRow.add(new ReportCellDetails(purchasePrice, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
							totalPurchasePrice += tradeProductBatchSummary.getPricePerItem();
							dayLevelPurchasePrice += tradeProductBatchSummary.getPricePerItem();

							String amount = NumberUtils.formatToIndianCurrency(tradeProductBatchSummary.getTotalPrice());

							reportCellDetailsRow.add(new ReportCellDetails(amount, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
							totalInnerAmount += tradeProductBatchSummary.getTotalPrice();
							dayLevelInnerAmount += tradeProductBatchSummary.getTotalPrice();

							String discount = NumberUtils.formatToIndianCurrency(tradeProductBatchSummary.getTotalDiscount());

							reportCellDetailsRow.add(new ReportCellDetails(discount, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
							totalDiscount += tradeProductBatchSummary.getTotalDiscount();
							dayLevelDiscount += tradeProductBatchSummary.getTotalDiscount();

							String tax = NumberUtils.formatToIndianCurrency(tradeProductBatchSummary.getTotalTax());

							reportCellDetailsRow.add(new ReportCellDetails(tax, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
							totalTax += tradeProductBatchSummary.getTotalTax();
							dayLevelTax += tradeProductBatchSummary.getTotalTax();

							String additionalCost = NumberUtils.formatToIndianCurrency(inventoryTransactionSummary.getAdditionalCost());

							reportCellDetailsRow.add(new ReportCellDetails(additionalCost, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
							if (!transactionIdList.contains(transactionId)) {
								totalAdditionalCost += inventoryTransactionSummary.getAdditionalCost();
								dayLevelAdditionalCost += inventoryTransactionSummary.getAdditionalCost();
							}

							String additionalDiscount = NumberUtils.formatToIndianCurrency(inventoryTransactionSummary.getAdditionalDiscount());

							reportCellDetailsRow.add(new ReportCellDetails(additionalDiscount, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
							if (!transactionIdList.contains(transactionId)) {
								totalAdditionalDiscount += inventoryTransactionSummary.getAdditionalDiscount();
								dayLevelAdditionalDiscount += inventoryTransactionSummary.getAdditionalDiscount();
							}

							double paidAmount = inventoryTransactionSummary.getPaidAmount() == null ? 0
									: inventoryTransactionSummary.getPaidAmount();
							String paidAmountStr = NumberUtils.formatToIndianCurrency(paidAmount);

							reportCellDetailsRow.add(new ReportCellDetails(paidAmountStr, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
							if (!transactionIdList.contains(transactionId)) {
								totalNetAmount += paidAmount;
								dayLevelNetAmount += paidAmount;
							}

							if (!transactionIdList.contains(transactionId)) {
								transactionIdList.add(inventoryTransactionSummary.getTransactionId());
							}

							reportCellDetails.add(reportCellDetailsRow);
							rowNum++;

						}
					}
				}

				List<ReportCellDetails> dayWiseTotalReportCellDetailsRow = new ArrayList<ReportCellDetails>();

				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails("Daywise " + TOTAL + " for: " + DateUtils.getFormattedDate(transactionDate,
						DATE_FORMAT, User.DFAULT_TIMEZONE), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;

				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelQtyReturned), STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));

				dayWiseTotalReportCellDetailsRow
						.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelPurchasePrice), STRING, CONTENT_SIZE,
								BLACK_COLOR, WHITE_COLOR, true));

				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelInnerAmount), STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));

				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelDiscount), STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));

				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelTax), STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));

				dayWiseTotalReportCellDetailsRow
						.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelAdditionalCost), STRING, CONTENT_SIZE,
								BLACK_COLOR, WHITE_COLOR, true));

				dayWiseTotalReportCellDetailsRow
						.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelAdditionalDiscount), STRING, CONTENT_SIZE,
								BLACK_COLOR, WHITE_COLOR, true));

				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelNetAmount), STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));

				reportCellDetails.add(dayWiseTotalReportCellDetailsRow);

				cellIndexes = new CellIndexes(rowNum, rowNum, 0, colSpan);
				mergeCellIndexesList.add(cellIndexes);
				rowNum++;

			}

			// Total Row Values
			colSpan = 0;
			List<ReportCellDetails> reportCellDetailsTotalRow = new ArrayList<ReportCellDetails>();
			reportCellDetailsTotalRow.add(new ReportCellDetails(TOTAL, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;

			reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalQtyReturned), STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsTotalRow
					.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalPurchasePrice), STRING, CONTENT_SIZE,
							BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalInnerAmount), STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalDiscount), STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalTax), STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsTotalRow
					.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalAdditionalCost), STRING, CONTENT_SIZE,
							BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsTotalRow
					.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalAdditionalDiscount), STRING, CONTENT_SIZE,
							BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalNetAmount), STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetails.add(reportCellDetailsTotalRow);
			rowNum++;

			cellIndexes = new CellIndexes(rowNum - 1, rowNum - 1, 0, colSpan);

			mergeCellIndexesList.add(cellIndexes);
			List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
//			ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, mergeCellIndexesList,
//					reportCellDetails);
			ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true,
					totalColumns, headerMergeCellIndexesList,
					mergeCellIndexesList, headerReportCellDetails, reportCellDetails, true,
					true, institute, totalColumns);
			reportSheetDetailsList.add(reportSheetDetails);
			return new ReportDetails(reportName, reportSheetDetailsList);

		} catch (final Exception e) {

			logger.error("Error while generating purchase return report", e);
		}

		return null;
	}

	private ReportDetails generateIssueReports(int instituteId, int startDate, int endDate) {
		try {

			String reportName = "Issue Details Report";
			String sheetName = "IssueDetailsReport";

			Institute institute = instituteManager.getInstitute(instituteId);

			if (institute == null) {
				logger.info("Empty report {}", reportName);
				List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<>();
				ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, null,
						null);
				reportSheetDetailsList.add(reportSheetDetails);
				return new ReportDetails(reportName, reportSheetDetailsList);
			}

			List<InventoryTransactionSummary> inventoryTransactionSummaryList = inventoryTransactionsManager
					.getTransactionDetailsList(instituteId, startDate, endDate, InventoryTransactionType.ISSUE);

			List<List<ReportCellDetails>> headerReportCellDetails = new ArrayList<List<ReportCellDetails>>();
			List<CellIndexes> headerMergeCellIndexesList = new ArrayList<>();

			if (CollectionUtils.isEmpty(inventoryTransactionSummaryList)) {
				logger.info("Empty report {}", reportName);
				List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<>();
				ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, null,
						null);
				reportSheetDetailsList.add(reportSheetDetails);
				return new ReportDetails(reportName, reportSheetDetailsList);
			}

			List<ReportCellDetails> reportHeaderRow = new ArrayList<ReportCellDetails>();

			reportHeaderRow
					.add(new ReportCellDetails(reportName, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
			headerReportCellDetails.add(reportHeaderRow);

			List<ReportCellDetails> reportCellDetailsHeaderRow = new ArrayList<ReportCellDetails>();
			int i = 0;
			for (i = 0; i < ISSUE_DETAILS_REPORT_COLUMN.length; i++) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(ISSUE_DETAILS_REPORT_COLUMN[i], STRING,
						CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
			}
			headerReportCellDetails.add(reportCellDetailsHeaderRow);

			int totalColumns = ISSUE_DETAILS_REPORT_COLUMN.length;
			CellIndexes cellIndexes = new CellIndexes(0, 0, 0, totalColumns - 1);
			headerMergeCellIndexesList.add(cellIndexes);

//			Collections.sort(inventoryTransactionSummaryList, new Comparator<InventoryTransactionSummary>() {
//				@Override
//				public int compare(InventoryTransactionSummary inventoryTransactionSummary1,
//						InventoryTransactionSummary inventoryTransactionSummary2) {
//					return ((int) (inventoryTransactionSummary2.getTransactionDate() / 1000L) -
//							(int) (inventoryTransactionSummary1.getTransactionDate() / 1000L));
//				}
//			});

			TreeMap<Integer, List<InventoryTransactionSummary>> dateWiseInventoryTransactionSummaryMap = getDateWiseInventoryTransactionSummaryMap(inventoryTransactionSummaryList);

			List<List<ReportCellDetails>> reportCellDetails = new ArrayList<List<ReportCellDetails>>();
			double totalQtyIssued = 0d;
			int rowNum = 2;
			int colSpan = 0;
			List<Integer> mergedRows = new ArrayList<>();
			List<CellIndexes> mergeCellIndexesList = new ArrayList<CellIndexes>();
			for (final Map.Entry<Integer, List<InventoryTransactionSummary>> inventoryTransactionSummaryEntry : dateWiseInventoryTransactionSummaryMap.entrySet()) {
				Integer transactionDate = inventoryTransactionSummaryEntry.getKey();
				List<InventoryTransactionSummary> inventoryTransactionSummaryList1 = inventoryTransactionSummaryEntry.getValue();
				double dayLevelQtyIssued = 0;
				colSpan = 0;
				for (InventoryTransactionSummary inventoryTransactionSummary : inventoryTransactionSummaryList1) {
					if (inventoryTransactionSummary == null || CollectionUtils.isEmpty(
							inventoryTransactionSummary.getTradeProductSummaryList())) {
						continue;
					}
					int productBatchListSize = 0;
					for (TradeProductSummary tradeProductSummary : inventoryTransactionSummary
							.getTradeProductSummaryList()) {
						productBatchListSize += tradeProductSummary.getTradeProductBatchSummaryList().size();
					}
					for (TradeProductSummary tradeProductSummary : inventoryTransactionSummary
							.getTradeProductSummaryList()) {

						if (tradeProductSummary == null || CollectionUtils.isEmpty(
								tradeProductSummary.getTradeProductBatchSummaryList())) {
							continue;
						}
						for (TradeProductBatchSummary tradeProductBatchSummary : tradeProductSummary
								.getTradeProductBatchSummaryList()) {

							if (tradeProductBatchSummary == null) {
								continue;
							}

							List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();
							int currentRowNumber = rowNum + 1;
							int addForMerging = productBatchListSize - 1;

							if (addForMerging > 0 && !mergedRows.contains(currentRowNumber)) {
								for (int j = currentRowNumber; j <= currentRowNumber + addForMerging; j++) {
									mergedRows.add(j);

								}
								for (int j = 0; j < 6; j++) {

									cellIndexes = new CellIndexes(currentRowNumber,
											currentRowNumber + addForMerging, j, j);

									mergeCellIndexesList.add(cellIndexes);

								}
							}

							String date = inventoryTransactionSummary.getTransactionDate() <= 0 ? EMPTY_TEXT
									: DateUtils.getFormattedDate(
									(int) (inventoryTransactionSummary.getTransactionDate() / 1000L));

							reportCellDetailsRow.add(new ReportCellDetails(date, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String invoiceId = StringUtils.isBlank(inventoryTransactionSummary.getInvoiceId()) ? EMPTY_TEXT
									: inventoryTransactionSummary.getInvoiceId();

							reportCellDetailsRow.add(new ReportCellDetails(invoiceId, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String issuedTo = inventoryTransactionSummary.getStudentLite() == null
									? inventoryTransactionSummary.getBuyerName()
									: inventoryTransactionSummary.getStudentLite().getName();

							reportCellDetailsRow.add(new ReportCellDetails(issuedTo, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String issuedBy = StringUtils.isBlank(inventoryTransactionSummary.getTransactionBy())
									? EMPTY_TEXT
									: inventoryTransactionSummary.getTransactionBy();

							reportCellDetailsRow.add(new ReportCellDetails(issuedBy, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String reference = StringUtils.isBlank(inventoryTransactionSummary.getReference()) ? EMPTY_TEXT
									: inventoryTransactionSummary.getReference();

							reportCellDetailsRow.add(new ReportCellDetails(reference, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String description = StringUtils.isBlank(inventoryTransactionSummary.getDescription())
									? EMPTY_TEXT
									: inventoryTransactionSummary.getDescription();

							reportCellDetailsRow.add(new ReportCellDetails(description, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String productName = StringUtils.isBlank(tradeProductSummary.getProductName()) ? EMPTY_TEXT
									: tradeProductSummary.getProductName();

							reportCellDetailsRow.add(new ReportCellDetails(productName, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String batchName = StringUtils.isBlank(tradeProductBatchSummary.getBatchName()) ? EMPTY_TEXT
									: tradeProductBatchSummary.getBatchName();

							reportCellDetailsRow.add(new ReportCellDetails(batchName, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							String qtyIssued = NumberUtils.formatToIndianCurrency(tradeProductBatchSummary.getQuantity());

							reportCellDetailsRow.add(new ReportCellDetails(qtyIssued, STRING,
									CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

							totalQtyIssued += tradeProductBatchSummary.getQuantity();
							dayLevelQtyIssued += tradeProductBatchSummary.getQuantity();
							reportCellDetails.add(reportCellDetailsRow);
							rowNum++;
						}
					}
				}

				List<ReportCellDetails> dayWiseTotalReportCellDetailsRow = new ArrayList<ReportCellDetails>();

				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails("Daywise " + TOTAL + " for: " + DateUtils.getFormattedDate(transactionDate,
						DATE_FORMAT, User.DFAULT_TIMEZONE), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;

				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelQtyIssued), STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));

				reportCellDetails.add(dayWiseTotalReportCellDetailsRow);

				cellIndexes = new CellIndexes(rowNum, rowNum, 0, colSpan);
				mergeCellIndexesList.add(cellIndexes);
				rowNum++;

			}

			// Total Row Values
			colSpan = 0;
			List<ReportCellDetails> reportCellDetailsTotalRow = new ArrayList<ReportCellDetails>();
			reportCellDetailsTotalRow.add(new ReportCellDetails(TOTAL, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalQtyIssued), STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetails.add(reportCellDetailsTotalRow);
			rowNum++;

			cellIndexes = new CellIndexes(rowNum - 1, rowNum - 1, 0, colSpan);

			mergeCellIndexesList.add(cellIndexes);
			List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
//			ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, mergeCellIndexesList,
//					reportCellDetails);
			ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true,
					totalColumns, headerMergeCellIndexesList,
					mergeCellIndexesList, headerReportCellDetails, reportCellDetails, true,
					true, institute, totalColumns);
			reportSheetDetailsList.add(reportSheetDetails);
			return new ReportDetails(reportName, reportSheetDetailsList);

		} catch (final Exception e) {

			logger.error("Error while generating issue report", e);
		}

		return null;
	}

	private TreeMap<Integer, List<InventoryTransactionSummary>> getDateWiseInventoryTransactionSummaryMap(
			List<InventoryTransactionSummary> inventoryTransactionSummaryList) {
		TreeMap<Integer, List<InventoryTransactionSummary>> dateWiseInventoryTransactionSummaryMap = new TreeMap<>(Comparator.reverseOrder());
		for(InventoryTransactionSummary inventoryTransactionSummary : inventoryTransactionSummaryList) {
			Integer transactionDate = (int) (inventoryTransactionSummary.getTransactionDate() / 1000L);
			if(transactionDate <= 0) {
				continue;
			}
			transactionDate = DateUtils.getDayStart(transactionDate, DateUtils.DEFAULT_TIMEZONE);
			if(!dateWiseInventoryTransactionSummaryMap.containsKey(transactionDate)) {
				dateWiseInventoryTransactionSummaryMap.put(transactionDate, new ArrayList<>());
			}
			dateWiseInventoryTransactionSummaryMap.get(transactionDate).add(inventoryTransactionSummary);
		}

		for(List<InventoryTransactionSummary> inventoryTransactionSummaryValueList : new ArrayList<>(dateWiseInventoryTransactionSummaryMap.values())) {
			Collections.sort(inventoryTransactionSummaryValueList, new Comparator<InventoryTransactionSummary>() {
				@Override
				public int compare(InventoryTransactionSummary t1,
								   InventoryTransactionSummary t2) {
					int compare = (int) (t2.getTransactionDate() / 1000L) - (int) (t1.getTransactionDate() / 1000L);
					if(compare != 0) {
						return compare;
					}
					return Math.toIntExact(t2.getTransactionAddedAt() - t1.getTransactionAddedAt());
				}
			});
		}
		return dateWiseInventoryTransactionSummaryMap;

	}
}
