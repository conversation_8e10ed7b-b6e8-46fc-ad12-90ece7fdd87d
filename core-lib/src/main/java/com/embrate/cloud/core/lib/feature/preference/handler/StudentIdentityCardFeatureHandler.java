package com.embrate.cloud.core.lib.feature.preference.handler;

import com.embrate.cloud.core.api.feature.preference.FeaturePreferenceEntity;
import com.embrate.cloud.core.api.feature.preference.PreferenceDataType;
import com.embrate.cloud.core.api.feature.preference.ProductFeature;
import com.embrate.cloud.core.lib.feature.preference.IFeaturePreferenceGroupBuilder;
import com.lernen.cloud.core.api.configurations.MetaDataPreferences;
import com.lernen.cloud.core.api.configurations.StudentIdentityCardPreferences;
import com.lernen.cloud.core.lib.institute.InstituteManager;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 */

public class StudentIdentityCardFeatureHandler extends ProductFeatureHandler {

    private final InstituteManager instituteManager;

    public StudentIdentityCardFeatureHandler(InstituteManager instituteManager) {
        this.instituteManager = instituteManager;
    }

    private ProductFeature productFeature = null;

    @Override
    public ProductFeature buildProductFeature() {
        productFeature = new ProductFeature("com.embrate.feature.identitycard.student", "Student Identity Card Preferences", "Student Identity Card Preferences");
        addFeaturePreferenceGroup(productFeature, getBasicPreferences());
        return productFeature;
    }

    @Override
    public ProductFeature getProductFeature() {
        return productFeature;
    }

    private IFeaturePreferenceGroupBuilder getBasicPreferences() {
        return new IFeaturePreferenceGroupBuilder() {
            @Override
            public String getGroupId() {
                return "group.preference.basic";
            }

            @Override
            public String getGroupName() {
                return "Basic Preferences";
            }

            @Override
            public String getGroupDescription() {
                return "Basic Preferences";
            }

            @Override
            public List<FeaturePreferenceEntity> getPreferences() {
                List<FeaturePreferenceEntity> featurePreferenceEntities = new LinkedList<>();
                featurePreferenceEntities.add(new FeaturePreferenceEntity("institute_name_color", "institute_name_color", "institute_name_color", PreferenceDataType.STRING, StudentIdentityCardPreferences.getConfigType(), StudentIdentityCardPreferences.INSTITUTE_NAME_COLOR));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("institute_name_font_size", "institute_name_font_size", "institute_name_font_size", PreferenceDataType.STRING, StudentIdentityCardPreferences.getConfigType(), StudentIdentityCardPreferences.INSTITUTE_NAME_FONT_SIZE));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("letter_head_1_color", "letter_head_1_color", "letter_head_1_color", PreferenceDataType.STRING, StudentIdentityCardPreferences.getConfigType(), StudentIdentityCardPreferences.LETTER_HEAD_1_COLOR));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("letter_head_1_font_size", "letter_head_1_font_size", "letter_head_1_font_size", PreferenceDataType.STRING, StudentIdentityCardPreferences.getConfigType(), StudentIdentityCardPreferences.LETTER_HEAD_1_FONT_SIZE));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("letter_head_2_color", "letter_head_2_color", "letter_head_2_color", PreferenceDataType.STRING, StudentIdentityCardPreferences.getConfigType(), StudentIdentityCardPreferences.LETTER_HEAD_2_COLOR));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("letter_head_2_font_size", "letter_head_2_font_size", "letter_head_2_font_size", PreferenceDataType.STRING, StudentIdentityCardPreferences.getConfigType(), StudentIdentityCardPreferences.LETTER_HEAD_2_FONT_SIZE));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("institute_logo_height", "institute_logo_height", "institute_logo_height", PreferenceDataType.STRING, StudentIdentityCardPreferences.getConfigType(), StudentIdentityCardPreferences.INSTITUTE_LOGO_HEIGHT));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("institute_logo_width", "institute_logo_width", "institute_logo_width", PreferenceDataType.STRING, StudentIdentityCardPreferences.getConfigType(), StudentIdentityCardPreferences.INSTITUTE_LOGO_WIDTH));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("header_background_color", "header_background_color", "header_background_color", PreferenceDataType.STRING, StudentIdentityCardPreferences.getConfigType(), StudentIdentityCardPreferences.HEADER_BACKGROUND_COLOR));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("student_image_height", "student_image_height", "student_image_height", PreferenceDataType.STRING, StudentIdentityCardPreferences.getConfigType(), StudentIdentityCardPreferences.STUDENT_IMAGE_HEIGHT));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("student_image_width", "student_image_width", "student_image_width", PreferenceDataType.STRING, StudentIdentityCardPreferences.getConfigType(), StudentIdentityCardPreferences.STUDENT_IMAGE_WIDTH));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("i_card_details_text_color", "i_card_details_text_color", "i_card_details_text_color", PreferenceDataType.STRING, StudentIdentityCardPreferences.getConfigType(), StudentIdentityCardPreferences.I_CARD_DETAILS_TEXT_COLOR));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("footer_bar_color", "footer_bar_color", "footer_bar_color", PreferenceDataType.STRING, StudentIdentityCardPreferences.getConfigType(), StudentIdentityCardPreferences.FOOTER_BAR_COLOR));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("session_background_color", "session_background_color", "session_background_color", PreferenceDataType.STRING, StudentIdentityCardPreferences.getConfigType(), StudentIdentityCardPreferences.SESSION_BACKGROUND_COLOR));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("session_font_color", "session_font_color", "session_font_color", PreferenceDataType.STRING, StudentIdentityCardPreferences.getConfigType(), StudentIdentityCardPreferences.SESSION_FONT_COLOR));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("session_font_size", "session_font_size", "session_font_size", PreferenceDataType.STRING, StudentIdentityCardPreferences.getConfigType(), StudentIdentityCardPreferences.SESSION_FONT_SIZE));

                return featurePreferenceEntities;
            }
        };
    }
}
