package com.embrate.cloud.core.lib.institute.onboarding.step;

import com.embrate.cloud.core.api.onboarding.institute.setup.InstituteSetupPayload;
import com.embrate.cloud.core.lib.institute.onboarding.IConfigureInstituteStep;
import com.lernen.cloud.core.api.configurations.Entity;
import com.lernen.cloud.core.api.sms.SMSPreferences;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.lib.configs.ConfigurationManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */

public class CommunicationSetupProcessor implements IModuleSetupProcessor {

    private static final Logger logger = LogManager.getLogger(CommunicationSetupProcessor.class);
    private final InstituteManager instituteManager;

    private final ConfigurationManager configurationManager;

    public CommunicationSetupProcessor(InstituteManager instituteManager, ConfigurationManager configurationManager) {
        this.instituteManager = instituteManager;
        this.configurationManager = configurationManager;
    }

    @Override
    public Module getModule() {
        return Module.COMMUNICATION;
    }

    @Override
    public List<IConfigureInstituteStep> getSteps(InstituteSetupPayload instituteSetupPayload) {
        int instituteId = instituteSetupPayload.getInstituteBasicSetupResult().getInstituteId();
        List<IConfigureInstituteStep> instituteSetupStepsList = new ArrayList<>();
        instituteSetupStepsList.add(getConfigurationStep(instituteId));
        return instituteSetupStepsList;
    }

    private IConfigureInstituteStep getConfigurationStep(int instituteId){
        return new IConfigureInstituteStep() {
            @Override
            public String getName() {
                return "Configurations";
            }

            @Override
            public boolean execute() {
                Map<String, String> configKVs = new HashMap<>();
                configKVs.put(SMSPreferences.BUFFER_SMS_COUNT, String.valueOf(0));

                return configurationManager.upsertConfiguration(Entity.INSTITUTE,String.valueOf(instituteId), SMSPreferences.getConfigType(), configKVs);
            }
        };
    }

}
