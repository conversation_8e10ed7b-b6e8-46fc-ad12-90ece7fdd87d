/**
 *
 */
package com.embrate.cloud.core.lib.salary;

import com.embrate.cloud.core.api.salary.*;
import com.embrate.cloud.core.api.salary.SalaryCycleDetailsPayload;
import com.embrate.cloud.core.api.salary.v2.*;
import com.embrate.cloud.dao.tier.salary.SalaryDao;
import com.lernen.cloud.core.api.common.SearchResultWithPagination;
import com.lernen.cloud.core.api.common.TransactionMode;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.fees.payment.FeePaymentTransactionStatus;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.staff.StaffStatus;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.dao.tier.staff.StaffDao;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.springframework.util.CollectionUtils;

import java.time.Month;
import java.util.*;
import java.util.Map.Entry;

/**
 * <AUTHOR>
 */
public class SalaryConfigurationManager {

    private static final Logger logger = LogManager.getLogger(SalaryConfigurationManager.class);

    private final SalaryDao salaryDao;
    private final UserPermissionManager userPermissionManager;
    private final StaffDao staffDao;
    private final InstituteManager instituteManager;

    public SalaryConfigurationManager(SalaryDao salaryDao, UserPermissionManager userPermissionManager, StaffDao staffDao,
                                      InstituteManager instituteManager) {
        this.salaryDao = salaryDao;
        this.userPermissionManager = userPermissionManager;
        this.staffDao = staffDao;
        this.instituteManager = instituteManager;
    }

    public boolean addPayHeadConfigurations(int instituteId, PayHeadConfiguration payHeadConfiguration,
                                            UUID userId) {
        userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.PAY_HEAD_CONFIGURATION);
        validatePayHeadConfigurations(instituteId, payHeadConfiguration, false);
        return salaryDao.addPayHeadConfigurations(instituteId, payHeadConfiguration);
    }

    public boolean addPayHeadConfigurations(int instituteId, List<PayHeadConfiguration> payHeadConfigurationList) {

        validatePayHeadConfigurationsList(instituteId, payHeadConfigurationList);
        return salaryDao.addPayHeadConfigurations(instituteId, payHeadConfigurationList);
    }

    private void validatePayHeadConfigurationsList (int instituteId, List<PayHeadConfiguration> payHeadConfigurationList) {
        if (CollectionUtils.isEmpty(payHeadConfigurationList)) {
            logger.error("Empty Pay Head configuration");
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_PAY_HEAD_CONFIGURATION,
                    "Empty pay head configuration."));
        }
        if (instituteId <= 0) {
            logger.error("Empty Institute Id {}", instituteId);
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PAY_HEAD_CONFIGURATION, "Empty Institute Id."));
        }
        for (PayHeadConfiguration payHeadConfiguration : payHeadConfigurationList) {
            if (payHeadConfiguration.getPayHeadType() == null) {
                logger.error("Invalid Pay Head Type for institute {}", instituteId);
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_PAY_HEAD_CONFIGURATION, "Invalid Pay Head Type."));
            }
            if (StringUtils.isBlank(payHeadConfiguration.getPayHead())) {
                logger.error("Empty Pay Head for institute {}", instituteId);
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_PAY_HEAD_CONFIGURATION, "Empty pay head."));
            }
        }
    }

    private void validatePayHeadConfigurations(int instituteId, PayHeadConfiguration payHeadConfiguration, Boolean update) {
        if (payHeadConfiguration == null) {
            logger.error("Empty Pay Head configuration");
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_PAY_HEAD_CONFIGURATION,
                    "Empty pay head configuration."));
        }
        if (instituteId <= 0) {
            logger.error("Empty Institute Id {}", instituteId);
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PAY_HEAD_CONFIGURATION, "Empty Institute Id."));
        }
        if (payHeadConfiguration.getPayHeadType() == null) {
            logger.error("Invalid Pay Head Type for institute {}", instituteId);
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PAY_HEAD_CONFIGURATION, "Invalid Pay Head Type."));
        }
        if (StringUtils.isBlank(payHeadConfiguration.getPayHead())) {
            logger.error("Empty Pay Head for institute {}", instituteId);
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PAY_HEAD_CONFIGURATION, "Empty pay head."));
        }

//        if (payHeadConfiguration.getPayHeadAmountRule() != null) {
//            PayHeadAmountRule payHeadAmountRule = payHeadConfiguration.getPayHeadAmountRule();
//            if (payHeadAmountRule.getDependentPayHeadId() <= 0 || Double.compare(payHeadAmountRule.getAmount(), 0d) <= 0
//                    || Double.compare(payHeadAmountRule.getDependentPayHeadAmount(), 0d) < 0 || payHeadAmountRule.getConditionalRuleOperation() == null) {
//                logger.error("Invalid pay head rule for institute {}", payHeadConfiguration.getInstituteId());
//                throw new ApplicationException(
//                        new ErrorResponse(ApplicationErrorCode.INVALID_PAY_HEAD_CONFIGURATION, "Invalid pay head rule."));
//            }
//        }

        if (update) {
            if (payHeadConfiguration.getPayHeadId() == null || payHeadConfiguration.getPayHeadId() <= 0) {
                logger.error("Invalid Pay Head Id for institute {}", instituteId);
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_PAY_HEAD_CONFIGURATION, "Invalid pay head Id."));
            }
        }
    }

    public int updatePayHeadConfiguration(int instituteId, PayHeadConfiguration payHeadConfiguration, UUID userId) {
        userPermissionManager.verifyAuthorisation(instituteId, userId,
                AuthorisationRequiredAction.PAY_HEAD_CONFIGURATION);
        validatePayHeadConfigurations(instituteId, payHeadConfiguration, true);
        return salaryDao.updatePayHeadConfiguration(instituteId, payHeadConfiguration);
    }

    public boolean deletePayHeadConfiguration(int instituteId, int payHeadId, UUID userId) {
        userPermissionManager.verifyAuthorisation(instituteId, userId,
                AuthorisationRequiredAction.PAY_HEAD_CONFIGURATION);
        return salaryDao.deletePayHeadConfiguration(instituteId, payHeadId);
    }

    public List<PayHeadConfiguration> getPayHeadConfiguration(int instituteId) {
        if (instituteId <= 0) {
            logger.error("Invalid instituteId");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PAY_HEAD_CONFIGURATION, "Invalid Institue Id."));
        }
        return salaryDao.getPayHeadConfiguration(instituteId);
    }


    public List<SalaryStructureMetadata> getSalaryStructureTemplatesMetadata(int instituteId, int academicSessionId) {
        if (instituteId <= 0 || academicSessionId <= 0) {
            logger.error("Invalid instituteId {} or academicSessionId {}", instituteId, academicSessionId);
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid institute or session."));
        }
		return salaryDao.getSalaryStructureTemplatesMetadata(instituteId, academicSessionId);
    }

    public SalaryStructureTemplate getSalaryStructureTemplateDetails(int instituteId, UUID structureId) {
        if (instituteId <= 0 || structureId == null) {
            logger.error("Invalid instituteId {} or structureId {}", instituteId, structureId);
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid institute or structure id."));
        }
        return salaryDao.getSalaryStructureTemplate(instituteId, structureId);
    }

    public boolean addSalaryStructureTemplate(int instituteId, SalaryStructureTemplatePayload salaryStructureTemplatePayload, UUID userId) {
        userPermissionManager.verifyAuthorisation(instituteId, userId,
                AuthorisationRequiredAction.SALARY_STRUCTURE_CONFIGURATION);
        validateSalaryStructureTemplate(instituteId, salaryStructureTemplatePayload, false);
        return salaryDao.addSalaryStructureTemplate(instituteId, salaryStructureTemplatePayload);
    }

    public boolean updateSalaryStructureTemplate(int instituteId, SalaryStructureTemplatePayload salaryStructureTemplatePayload, UUID userId) {
        userPermissionManager.verifyAuthorisation(instituteId, userId,
                AuthorisationRequiredAction.SALARY_STRUCTURE_CONFIGURATION);
        validateSalaryStructureTemplate(instituteId, salaryStructureTemplatePayload, true);
        return salaryDao.updateSalaryStructureTemplate(instituteId, salaryStructureTemplatePayload);
    }

    public boolean deleteSalaryStructureTemplate(int instituteId, UUID structureId, UUID userId) {
        if (instituteId <= 0) {
            logger.error("Invalid institute {}", instituteId);
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid institute"));
        }
        if (userId == null) {
            logger.error("Invalid user id");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid user id."));
        }
        if (structureId == null) {
            logger.error("Invalid structure id");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid structure id."));
        }
        userPermissionManager.verifyAuthorisation(instituteId, userId,
                AuthorisationRequiredAction.SALARY_STRUCTURE_CONFIGURATION);

        return salaryDao.deleteSalaryStructureTemplate(instituteId, structureId);
    }

    private void validateSalaryStructureTemplate(int instituteId, SalaryStructureTemplatePayload salaryStructureTemplatePayload, Boolean update) {

        if (salaryStructureTemplatePayload == null) {
            logger.error("Invalid salary structure payload");
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Invalid salary structure payload."));
        }

        if (StringUtils.isBlank(salaryStructureTemplatePayload.getStructureName())) {
            logger.error("Invalid structure name for institute {}", instituteId);
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid structure name."));
        }

        if (salaryStructureTemplatePayload.getAcademicSessionId() <= 0) {
            logger.error("Invalid session");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid session"));
        }


        if (CollectionUtils.isEmpty(salaryStructureTemplatePayload.getSalaryCyclePayloadList())) {
            logger.error("Empty cycle pay head amount mapping for institute {}", instituteId);
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_STAFF_SALARY_STRUCTURE, "Invalid Pay Head Type."));
        }

        if (update) {
            if (salaryStructureTemplatePayload.getStructureId() == null) {
                logger.error("Invalid salary structure id");
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                        "Invalid staff salary structure id."));
            }
        } else {
            Boolean templatesExistsWithName = salaryDao.structureTemplatesExistsWithName(instituteId,
                    salaryStructureTemplatePayload.getAcademicSessionId(), salaryStructureTemplatePayload.getStructureName().trim());
            if (templatesExistsWithName == null) {
                logger.error("Unable to fetch structure existence for institute {}, session {}", instituteId,
                        salaryStructureTemplatePayload.getAcademicSessionId());

                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                        "Unable to fetch structure with given name."));
            }
            if (templatesExistsWithName) {
                logger.error("Structure already present with given name {} for institute {}, session {}.",
                        salaryStructureTemplatePayload.getStructureName().trim(), instituteId, salaryStructureTemplatePayload.getAcademicSessionId());

                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                        "Structure already present with given name."));
            }
        }

        for (SalaryCyclePayload salaryCyclePayload : salaryStructureTemplatePayload.getSalaryCyclePayloadList()) {

            if (salaryCyclePayload == null || salaryCyclePayload.getSalaryCycleId() <= 0 || CollectionUtils.isEmpty(salaryCyclePayload.getPayHeadAmountList())) {
                logger.error("Invalid salaryCyclePayload for institute {}, {}", instituteId, salaryCyclePayload);
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid salary cycle details."));
            }

            for (PayHeadAmountMapping payHeadAmountMapping : salaryCyclePayload.getPayHeadAmountList()) {
                if (payHeadAmountMapping.getPayHeadId() <= 0) {
                    logger.error("Invalid pay head for institute {}", instituteId);
                    throw new ApplicationException(
                            new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid pay head."));
                }
                if (payHeadAmountMapping.getAmount() < 0) {
                    logger.error("Invalid amount for institute {}, pay head {}", instituteId, payHeadAmountMapping.getPayHeadId());
                    throw new ApplicationException(
                            new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid pay head amount."));
                }
            }
        }

    }




















    ////////////////////
    //Staff salary structure
//    public StaffSalaryStructure getEnabledStaffSalaryStructure(int instituteId, UUID staffId, UserStatus structureStatus) {
//        if (instituteId <= 0) {
//            logger.error("Invalid Institue Id");
//            throw new ApplicationException(
//                    new ErrorResponse(ApplicationErrorCode.INVALID_PAY_HEAD_CONFIGURATION, "Invalid Institue Id."));
//        }
//        if (staffId == null) {
//            logger.error("Invalid staff Id");
//            throw new ApplicationException(
//                    new ErrorResponse(ApplicationErrorCode.INVALID_PAY_HEAD_CONFIGURATION, "Invalid staff Id."));
//        }
//        return salaryDao.getStaffSalaryStructureByStatusAndStaffId(instituteId, staffId, structureStatus);
//    }

//    public boolean addStaffSalaryStructure(int instituteId, StaffSalaryStructurePayload staffSalaryStructurePayload, UUID userId) {
//        userPermissionManager.verifyAuthorisation(instituteId, userId,
//                AuthorisationRequiredAction.SALARY_STRUCTURE_CONFIGURATION);
//        validateStaffSalaryStructure(instituteId, staffSalaryStructurePayload, false);
//        return salaryDao.addStaffSalaryStructure(instituteId, staffSalaryStructurePayload);
//    }



//    private void validateStaffSalaryStructure(int instituteId, StaffSalaryStructurePayload staffSalaryStructurePayload, Boolean update) {
//
//        if (staffSalaryStructurePayload == null) {
//            logger.error("Invalid staff salary Structure Payload");
//            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STAFF_SALARY_STRUCTURE,
//                    "Invalid staff salary Structure Payload."));
//        }
//
//        if (update) {
//            if (staffSalaryStructurePayload.getStructureId() == null) {
//                logger.error("Invalid staff salary structure Id");
//                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STAFF_SALARY_STRUCTURE,
//                        "Invalid staff salary Structure Id."));
//            }
//        } else {
//            StaffSalaryStructure staffSalaryStructureName = salaryDao.getStaffSalaryStructureByName(instituteId,
//                    staffSalaryStructurePayload.getStaffId(), staffSalaryStructurePayload.getStructureName());
//
//            if (staffSalaryStructureName != null) {
//                logger.error("Structure already present with given name.");
//                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STAFF_SALARY_STRUCTURE,
//                        "Structure already present with given name."));
//            }
//        }
//
//        if (StringUtils.isBlank(staffSalaryStructurePayload.getStructureName())) {
//            logger.error("Invalid Structure name for institute {}", staffSalaryStructurePayload.getStaffId());
//            throw new ApplicationException(
//                    new ErrorResponse(ApplicationErrorCode.INVALID_STAFF_SALARY_STRUCTURE, "Invalid Structure name."));
//        }
//
//        if (instituteId <= 0) {
//            logger.error("Invalid Institue Id");
//            throw new ApplicationException(
//                    new ErrorResponse(ApplicationErrorCode.INVALID_STAFF_SALARY_STRUCTURE, "Invalid Institue Id."));
//        }
//
//        if (staffSalaryStructurePayload.getStaffId() == null) {
//            logger.error("Invalid Staff Id for institute {}", staffSalaryStructurePayload.getStaffId());
//            throw new ApplicationException(
//                    new ErrorResponse(ApplicationErrorCode.INVALID_STAFF_SALARY_STRUCTURE, "Invalid Staff Id."));
//        }
//
//        if (staffSalaryStructurePayload.getStatus() == null) {
//            logger.error("Invalid structure status for institute {}", staffSalaryStructurePayload.getStaffId());
//            throw new ApplicationException(
//                    new ErrorResponse(ApplicationErrorCode.INVALID_STAFF_SALARY_STRUCTURE, "Invalid structure status."));
//        }
//
//        if (CollectionUtils.isEmpty(staffSalaryStructurePayload.getCyclePayHeadAmountMap())) {
//            logger.error("Empty cycle pay head amount mapping for institute {}", instituteId);
//            throw new ApplicationException(
//                    new ErrorResponse(ApplicationErrorCode.INVALID_STAFF_SALARY_STRUCTURE, "Invalid Pay Head Type."));
//        }
//
//        for (Entry<Integer, List<PayHeadAmountMapping>> cyclePayHeadAmountMapping : staffSalaryStructurePayload.getCyclePayHeadAmountMap().entrySet()) {
//
//            if (cyclePayHeadAmountMapping.getKey() == null || cyclePayHeadAmountMapping.getKey() <= 0) {
//                logger.error("Invalid Salary Cycle details for institute {}", instituteId);
//                throw new ApplicationException(
//                        new ErrorResponse(ApplicationErrorCode.INVALID_STAFF_SALARY_STRUCTURE, "Invalid Salary Cycle details."));
//            }
//
//            for (PayHeadAmountMapping payHeadAmountMapping : cyclePayHeadAmountMapping.getValue()) {
//                if (payHeadAmountMapping.getPayHeadId() <= 0) {
//                    logger.error("Invalid pay head Id for institute {}", instituteId);
//                    throw new ApplicationException(
//                            new ErrorResponse(ApplicationErrorCode.INVALID_STAFF_SALARY_STRUCTURE, "Invalid Pay Head Id."));
//                }
//                if (payHeadAmountMapping.getAmount() <= 0) {
//                    logger.error("Invalid amount for institute {}", instituteId);
//                    throw new ApplicationException(
//                            new ErrorResponse(ApplicationErrorCode.INVALID_STAFF_SALARY_STRUCTURE, "Invalid amount."));
//                }
//            }
//        }
//
//    }

//    public Boolean updateStaffSalaryStructure(int instituteId, StaffSalaryStructurePayload staffSalaryStructurePayload, UUID userId) {
//        userPermissionManager.verifyAuthorisation(instituteId, userId,
//                AuthorisationRequiredAction.SALARY_STRUCTURE_CONFIGURATION);
//        validateStaffSalaryStructure(instituteId, staffSalaryStructurePayload, true);
//        return salaryDao.updateStaffSalaryStructure(instituteId, staffSalaryStructurePayload);
//    }

//    public Boolean updateStaffSalaryStructureStatus(int instituteId, UUID structureId, UUID staffId, UUID userId, UserStatus structureStatus) {
//        if (instituteId <= 0) {
//            logger.error("Invalid Institue Id");
//            throw new ApplicationException(
//                    new ErrorResponse(ApplicationErrorCode.INVALID_STAFF_SALARY_STRUCTURE, "Invalid Institue Id."));
//        }
//        if (staffId == null) {
//            logger.error("Invalid staff Id");
//            throw new ApplicationException(
//                    new ErrorResponse(ApplicationErrorCode.INVALID_STAFF_SALARY_STRUCTURE, "Invalid staff Id."));
//        }
//        if (structureId == null) {
//            logger.error("Invalid structure Id");
//            throw new ApplicationException(
//                    new ErrorResponse(ApplicationErrorCode.INVALID_STAFF_SALARY_STRUCTURE, "Invalid structure Id."));
//        }
//        if (structureStatus == null) {
//            logger.error("Invalid structure status");
//            throw new ApplicationException(
//                    new ErrorResponse(ApplicationErrorCode.INVALID_STAFF_SALARY_STRUCTURE, "Invalid structure status."));
//        }
//        if (structureStatus == UserStatus.ENABLED) {
//            StaffSalaryStructure staffSalaryStructure = salaryDao.getStaffSalaryStructureByStatusAndStaffId(instituteId, staffId, structureStatus);
//            if (staffSalaryStructure != null) {
//                logger.error("There is already an enabled salary structure. Please disable it then create a new one.");
//                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STAFF_SALARY_STRUCTURE,
//                        "There is already an enabled salary structure. Please disable it then create a new one."));
//            }
//        }
//        userPermissionManager.verifyAuthorisation(instituteId, userId,
//                AuthorisationRequiredAction.SALARY_STRUCTURE_CONFIGURATION);
//        return salaryDao.updateStaffSalaryStructureStatus(instituteId, staffId, structureId, structureStatus);
//    }

//    public boolean deleteStaffSalaryStructureByStructureId(int instituteId, UUID structureId, UUID staffId,
//                                                           UUID userId) {
//        if (instituteId <= 0) {
//            logger.error("Invalid Institue Id");
//            throw new ApplicationException(
//                    new ErrorResponse(ApplicationErrorCode.INVALID_STAFF_SALARY_STRUCTURE, "Invalid Institue Id."));
//        }
//        if (staffId == null) {
//            logger.error("Invalid staff Id");
//            throw new ApplicationException(
//                    new ErrorResponse(ApplicationErrorCode.INVALID_STAFF_SALARY_STRUCTURE, "Invalid staff Id."));
//        }
//        if (structureId == null) {
//            logger.error("Invalid structure Id");
//            throw new ApplicationException(
//                    new ErrorResponse(ApplicationErrorCode.INVALID_STAFF_SALARY_STRUCTURE, "Invalid structure Id."));
//        }
//        userPermissionManager.verifyAuthorisation(instituteId, userId,
//                AuthorisationRequiredAction.SALARY_STRUCTURE_CONFIGURATION);
//        return salaryDao.deleteStaffSalaryStructureByStructureId(instituteId, staffId, structureId);
//    }

//    public List<StaffSalaryStructure> getStaffSalaryStructure(int instituteId, UUID staffId) {
//        if (instituteId <= 0) {
//            logger.error("Invalid Institue Id");
//            throw new ApplicationException(
//                    new ErrorResponse(ApplicationErrorCode.INVALID_PAY_HEAD_CONFIGURATION, "Invalid Institue Id."));
//        }
//        if (staffId == null) {
//            logger.error("Invalid staff Id");
//            throw new ApplicationException(
//                    new ErrorResponse(ApplicationErrorCode.INVALID_PAY_HEAD_CONFIGURATION, "Invalid staff Id."));
//        }
//        return salaryDao.getStaffSalaryStructure(instituteId, staffId);
//    }


//    public SalaryPayslip getStaffSalaryByCycle(int instituteId, UUID staffId, int cycle) {
//        if (instituteId <= 0) {
//            logger.error("Invalid Institue Id");
//            throw new ApplicationException(
//                    new ErrorResponse(ApplicationErrorCode.INVALID_PAY_HEAD_CONFIGURATION, "Invalid Institue Id."));
//        }
//        if (staffId == null) {
//            logger.error("Invalid staff Id");
//            throw new ApplicationException(
//                    new ErrorResponse(ApplicationErrorCode.INVALID_PAY_HEAD_CONFIGURATION, "Invalid staff Id."));
//        }
//        if (cycle <= 0) {
//            logger.error("Invalid cycle.");
//            throw new ApplicationException(
//                    new ErrorResponse(ApplicationErrorCode.INVALID_PAY_HEAD_CONFIGURATION, "Invalid cycle."));
//        }
//
//        SalaryPayslip salaryPayslip = salaryDao.getStaffSalaryByCycle(instituteId, staffId, cycle);
//        if (salaryPayslip != null) {
//            salaryPayslip.setAdvanceAmount(salaryDao.getTotalDueAmount(instituteId, staffId));
//        }
//        return salaryPayslip;
//    }

//    public boolean addSalaryPayslip(int instituteId, SalaryPayslipPayload salaryPayslipPayload, UUID userId) {
//        userPermissionManager.verifyAuthorisation(instituteId, userId,
//                AuthorisationRequiredAction.GENERATE_PAYSLIP);
//        validatePaySlipDetails(salaryPayslipPayload);
//        return salaryDao.addSalaryPayslip(instituteId, salaryPayslipPayload, userId);
//    }

//    public boolean addBulkSalaryPayslip(int instituteId, List<SalaryPayslipPayload> salaryPayslipPayloadList, UUID userId) {
//        userPermissionManager.verifyAuthorisation(instituteId, userId,
//                AuthorisationRequiredAction.GENERATE_PAYSLIP);
//        if (salaryPayslipPayloadList == null || salaryPayslipPayloadList.size() <= 0) {
//            logger.error("Invalid Salary Payslip Payload List");
//            throw new ApplicationException(
//                    new ErrorResponse(ApplicationErrorCode.INVALID_PAYSLIP_DETAILS, "Invalid Salary Payslip Payload List."));
//        }
//        for (SalaryPayslipPayload salaryPayslipPayload : salaryPayslipPayloadList) {
//            validatePaySlipDetails(salaryPayslipPayload);
//        }
//
//        return salaryDao.addBulkSalaryPayslip(instituteId, salaryPayslipPayloadList, userId);
//    }

    private void validatePaySlipDetails(SalaryPayslipPayload salaryPayslipPayload) {
        if (salaryPayslipPayload.getInstituteId() <= 0) {
            logger.error("Invalid institute");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PAYSLIP_DETAILS, "Invalid institute."));
        }
        if (salaryPayslipPayload.getStaffId() == null) {
            logger.error("Invalid staff Id for institute {}", salaryPayslipPayload.getInstituteId());
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PAYSLIP_DETAILS, "Invalid staff Id."));
        }
        if (salaryPayslipPayload.getStatus() == null) {
            logger.error("Invalid payslip status for institute {}", salaryPayslipPayload.getInstituteId());
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PAYSLIP_DETAILS, "Invalid payslip status."));
        }
        //TODO: write code to verify cycle
        if (salaryPayslipPayload.getSalaryCycleStart() == null || salaryPayslipPayload.getSalaryCycleStart() <= 0) {
            logger.error("Invalid Salary Cycle details for institute {}", salaryPayslipPayload.getInstituteId());
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PAYSLIP_DETAILS, "Invalid Salary Cycle details."));
        }

        for (PayHeadAmountMapping payHeadAmountMapping : salaryPayslipPayload.getPayHeadAmountMap()) {
            if (payHeadAmountMapping.getPayHeadId() <= 0) {
                logger.error("Invalid pay head Id for institute {}", salaryPayslipPayload.getInstituteId());
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_PAYSLIP_DETAILS, "Invalid Pay Head Id."));
            }
            if (payHeadAmountMapping.getAmount() <= 0) {
                logger.error("Invalid amount for institute {}", salaryPayslipPayload.getInstituteId());
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_PAYSLIP_DETAILS, "Invalid amount."));
            }
        }
    }

    public Boolean cancelPaySlip(UUID payslipId, int instituteId, UUID userId) {
        userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.CANCEL_PAYSLIP);
        if (instituteId <= 0) {
            logger.error("Invalid institute");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PAYSLIP_DETAILS, "Invalid institute."));
        }
        if (payslipId == null) {
            logger.error("Invalid payslipId Id for institute {}", instituteId);
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PAYSLIP_DETAILS, "Invalid payslipId Id."));
        }
        return salaryDao.cancelPaySlip(instituteId, payslipId, userId);
    }

    public List<SalaryPayslip> getPaySlipDetails(int instituteId, UUID staffId, FeePaymentTransactionStatus status) {
        if (instituteId <= 0) {
            logger.error("Invalid institute");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PAYSLIP_DETAILS, "Invalid institute."));
        }
        if (staffId == null) {
            logger.error("Invalid staff Id for institute {}", instituteId);
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PAYSLIP_DETAILS, "Invalid staff Id."));
        }
        if (status == null) {
            logger.error("Invalid payslip status for institute {}", instituteId);
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PAYSLIP_DETAILS, "Invalid payslip status."));
        }
        return salaryDao.getPaySlipDetails(instituteId, staffId, status);
    }

    public boolean addAdvanceTransaction(int instituteId, AdvanceTransactionPayload advanceTransactionPayload, UUID userId) {
        if (instituteId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
        }
        userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.PROVIDE_ADVANCE);
        validateAdvancePayload(advanceTransactionPayload);
        if (advanceTransactionPayload.getAdvanceTransactionCategory() == AdvanceTransactionCategory.ADVANCE) {
            advanceTransactionPayload.setAmount(advanceTransactionPayload.getAmount() * -1);
        }
        return salaryDao.addAdvanceTransaction(advanceTransactionPayload);
    }

    private void validateAdvancePayload(AdvanceTransactionPayload advanceTransactionPayload) {

        if (advanceTransactionPayload.getInstituteId() <= 0) {
            logger.error("Invalid institute id.");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
        }

        if (advanceTransactionPayload.getStaffId() == null) {
            logger.error("Invalid staff Id");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_ADVANCE_DETAILS, "Invalid staff Id."));
        }

        if (StringUtils.isBlank(advanceTransactionPayload.getReason())) {
            logger.error("Invalid Reason");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_ADVANCE_DETAILS, "Invalid reason."));
        }

        if (advanceTransactionPayload.getAdvanceTransactionCategory() == null
                || (advanceTransactionPayload.getAdvanceTransactionCategory() != AdvanceTransactionCategory.ADVANCE &&
                advanceTransactionPayload.getAdvanceTransactionCategory() != AdvanceTransactionCategory.ADVANCE_RETURN)) {
            logger.error("Invalid Transaction Category");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_ADVANCE_DETAILS, "Invalid Transaction Category."));
        }

        if (advanceTransactionPayload.getTransactionMode() == null) {
            logger.error("Invalid Transaction Mode");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_ADVANCE_DETAILS, "Invalid Transaction Mode."));
        }

        if (advanceTransactionPayload.getTransactionDate() == null || advanceTransactionPayload.getTransactionDate() <= 0) {
            logger.error("Invalid Transaction Date");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_ADVANCE_DETAILS, "Invalid Transaction Date."));
        }

        if (advanceTransactionPayload.getTransactionStatus() == null) {
            logger.error("Invalid Transaction Status");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_ADVANCE_DETAILS, "Invalid Transaction Status."));
        }

        if (advanceTransactionPayload.getAmount() <= 0d) {
            logger.error("Invalid Amount");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_ADVANCE_DETAILS, "Invalid advance amount."));
        }

        if (advanceTransactionPayload.getTransactionBy() == null) {
            logger.error("Invalid Transaction By");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_ADVANCE_DETAILS, "Invalid Transaction By."));
        }

        final Staff staffDetails = staffDao.getStaff(advanceTransactionPayload.getStaffId());

        if (staffDetails == null || staffDetails.getStaffStatus() != StaffStatus.ONBOARD) {
            logger.error("Staff {} does not exist or not in onboard state for performing wallet transaction {}",
                    advanceTransactionPayload.getStaffId(), advanceTransactionPayload);
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_ADVANCE_DETAILS, "Staff does not exist or not in onboard state to give advance."));
        }

        if (advanceTransactionPayload.getAdvanceTransactionCategory() == AdvanceTransactionCategory.ADVANCE_RETURN) {
            Double advanceAmount = salaryDao.getTotalDueAmount(advanceTransactionPayload.getInstituteId(), advanceTransactionPayload.getStaffId());
            if (advanceAmount < advanceTransactionPayload.getAmount()) {
                logger.error("Paid amount is greater than total due amount for institute {} staffId {}.", advanceTransactionPayload.getInstituteId(),
                        advanceTransactionPayload.getStaffId());
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_ADVANCE_DETAILS, "Paid amount is greater than total due amount."));
            }
        }
    }

    public boolean cancelAdvance(int instituteId, UUID transactionId, UUID staffId, UUID userId) {
        if (instituteId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
        }
        if (staffId == null) {
            logger.error("Invalid staff Id for institute {}", instituteId);
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PAYSLIP_DETAILS, "Invalid staff Id."));
        }
        if (transactionId == null) {
            logger.error("Invalid transaction Id for institute {}", instituteId);
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PAYSLIP_DETAILS, "Invalid staff Id."));
        }
        userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.CANCEL_ADVANCE);

        AdvanceTransaction advanceTransactionPayload = salaryDao.getAdvanceDetailsByTransactionId(
                instituteId, transactionId);
        if (advanceTransactionPayload.getTransactionMode() == TransactionMode.FROM_SALARY) {
            logger.error("This transaction is added due to payslip. Please cancel the respective payslip to cancel the transaction for institute {} staffId {}.", advanceTransactionPayload.getInstituteId(),
                    advanceTransactionPayload.getStaff().getStaffId());
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_ADVANCE_DETAILS, "This transaction is added due to payslip. "
                            + "Please cancel the respective payslip to cancel the transaction."));
        }
        return salaryDao.cancelAdvance(instituteId, advanceTransactionPayload, userId);
    }

    public FinalAdvanceDetails getFinalAdvanceDetails(int instituteId, UUID staffId) {
        if (instituteId <= 0) {
            logger.error("Invalid instituteId {}", instituteId);
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PAY_HEAD_CONFIGURATION, "Invalid Institue Id."));
        }
        if (staffId == null) {
            logger.error("Invalid staff Id");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PAY_HEAD_CONFIGURATION, "Invalid staff Id."));
        }
        return salaryDao.getFinalAdvanceDetails(instituteId, staffId);
    }

    public List<FinalAdvanceDetails> getAllStaffFinalAdvanceDetails(int instituteId) {
        if (instituteId <= 0) {
            logger.error("Invalid instituteId {}", instituteId);
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PAY_HEAD_CONFIGURATION, "Invalid Institue Id."));
        }

        return salaryDao.getAllStaffFinalAdvanceDetails(instituteId);
    }



    public List<SalaryPayslip> getAllPaySlipDetails(int instituteId, UUID staffId) {
        if (instituteId <= 0) {
            logger.error("Invalid institute");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PAYSLIP_DETAILS, "Invalid institute."));
        }
        if (staffId == null) {
            logger.error("Invalid staff Id for institute {}", instituteId);
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PAYSLIP_DETAILS, "Invalid staff Id."));
        }
        return salaryDao.getAllPaySlipDetails(instituteId, staffId);
    }

    public List<AdvanceTransaction> getAdvanceDetailsList(int instituteId, UUID staffId) {
        if (instituteId <= 0) {
            logger.error("Invalid Institue Id");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PAY_HEAD_CONFIGURATION, "Invalid Institue Id."));
        }
        if (staffId == null) {
            logger.error("Invalid staff Id");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PAY_HEAD_CONFIGURATION, "Invalid staff Id."));
        }
        return salaryDao.getAdvanceDetailsList(instituteId, staffId);
    }

    public SearchResultWithPagination<AdvanceTransaction> getAdvanceTransactionsDetailsList(int instituteId, FeePaymentTransactionStatus feePaymentTransactionStatus, int offset, int limit) {
        if (instituteId <= 0) {
            logger.error("Invalid Institue Id");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PAY_HEAD_CONFIGURATION, "Invalid Institue Id."));
        }
        SearchResultWithPagination<AdvanceTransaction> advanceTransactionResultWithPagination = salaryDao.getAdvanceTransactionsDetailsList(
                instituteId, feePaymentTransactionStatus, offset, limit);
        if (advanceTransactionResultWithPagination == null) {
            return null;
        }
        if (CollectionUtils.isEmpty(advanceTransactionResultWithPagination.getResult())) {
            return new SearchResultWithPagination<>(advanceTransactionResultWithPagination.getPaginationInfo(), new ArrayList<>());
        }
        return advanceTransactionResultWithPagination;

    }

    public List<SalaryPayslip> getLatestPaySlipDetailsOfAllStaff(int instituteId, Integer cycle) {
        if (instituteId <= 0) {
            logger.error("Invalid institute");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PAYSLIP_DETAILS, "Invalid institute."));
        }
        return salaryDao.getPaySlipDetailsByCycle(instituteId, cycle);
    }

    public List<FinalPayslipDetails> getStaffBulkSalaryDetails(int instituteId, Integer cycle, String staffList) {
        if (instituteId <= 0) {
            logger.error("Invalid institute");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PAYSLIP_DETAILS, "Invalid institute."));
        }
        if (cycle <= 0) {
            logger.error("Invalid institute");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PAYSLIP_DETAILS, "Invalid cycle."));
        }

        Map<UUID, SalaryPayslip> salaryPayslipMap = getMapOfStaffPayslip(salaryDao.getPaySlipDetailsByCycle(instituteId, cycle));
        Map<UUID, SalaryPayslip> staffSalaryStructureMap = getMapOfStaffPayslip(salaryDao.getSalaryStructureOfAllStaffByCycle(instituteId, cycle));

        List<SalaryPayslip> finalSalaryPayslipList = new ArrayList<SalaryPayslip>();
        for (Entry<UUID, SalaryPayslip> salaryPayslip : staffSalaryStructureMap.entrySet()) {
            Double advanceAmount = salaryDao.getTotalDueAmount(instituteId, salaryPayslip.getKey());
            salaryPayslip.getValue().setAdvanceAmount(advanceAmount);
            if (salaryPayslipMap.get(salaryPayslip.getKey()) != null && salaryPayslipMap.get(salaryPayslip.getKey()).getPayslipId() == null) {
                finalSalaryPayslipList.add(salaryPayslip.getValue());
            } else if (salaryPayslipMap.containsKey(salaryPayslip.getKey())) {
                finalSalaryPayslipList.add(salaryPayslipMap.get(salaryPayslip.getKey()));
            } else {
                finalSalaryPayslipList.add(salaryPayslip.getValue());
            }
        }
        return getFinalPayslipDetails(instituteId, staffList, finalSalaryPayslipList);
    }

    private List<FinalPayslipDetails> getFinalPayslipDetails(int instituteId, String staffList, List<SalaryPayslip> finalSalaryPayslipList) {
        List<FinalPayslipDetails> finalPayslipDetailsList = new ArrayList<FinalPayslipDetails>();
        for (SalaryPayslip salaryPayslip : finalSalaryPayslipList) {
            if (salaryPayslip.getPayslipId() != null) {
                if (staffList.equalsIgnoreCase("PENDING")) {
                    continue;
                }
            }
            finalPayslipDetailsList.add(getFinalPayslipDetails(instituteId, salaryPayslip));
        }
        return finalPayslipDetailsList;
    }

    public List<FinalPayslipDetails> getPaySlipDetailsByCycle(int instituteId, Integer cycle) {
        if (instituteId <= 0) {
            logger.error("Invalid institute");
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_PAYSLIP_DETAILS, "Invalid institute."));
        }
        List<SalaryPayslip> salaryPayslipList = salaryDao.getPaySlipDetailsByCycle(instituteId, cycle);
        List<FinalPayslipDetails> finalPayslipDetailsList = new ArrayList<FinalPayslipDetails>();
        for (SalaryPayslip salaryPayslip : salaryPayslipList) {
            finalPayslipDetailsList.add(getFinalPayslipDetails(instituteId, salaryPayslip));
        }
        return finalPayslipDetailsList;
    }

    private FinalPayslipDetails getFinalPayslipDetails(int instituteId, SalaryPayslip salaryPayslip) {
        Double totalEarnings = 0.0d;
        Double totalDeductions = 0.0d;
        List<PayHeadAmount> earningPayHeadAmountMap = new ArrayList<PayHeadAmount>();
        List<PayHeadAmount> deductionPayHeadAmountMap = new ArrayList<PayHeadAmount>();
        BulkSalaryPayslip bulkSalaryPayslip = new BulkSalaryPayslip(salaryPayslip.getInstituteId(), salaryPayslip.getPayslipId(),
                salaryPayslip.getStaff(), salaryPayslip.getStatus(), salaryPayslip.getSalaryCycleStart(), earningPayHeadAmountMap,
                deductionPayHeadAmountMap, salaryPayslip.getAdvanceAmount(),
                salaryPayslip.getAdvanceTransactionId(), salaryPayslip.getDescription());
        for (PayHeadAmount payHeadAmount : salaryPayslip.getPayHeadAmountMap()) {
            if (payHeadAmount != null && payHeadAmount.getPayHeadConfiguration() != null && payHeadAmount.getPayHeadConfiguration().getPayHeadType() != null) {
                if (payHeadAmount.getPayHeadConfiguration().getPayHeadType() == PayHeadType.ADDITION) {
                    totalEarnings += payHeadAmount.getAmount();
                    earningPayHeadAmountMap.add(payHeadAmount);
                } else {
                    totalDeductions += payHeadAmount.getAmount();
                    deductionPayHeadAmountMap.add(payHeadAmount);
                }
            }
        }
        if (salaryPayslip.getAdvanceAmount() != null) {
            totalDeductions += salaryPayslip.getAdvanceAmount();
        }
        Double netAmount = totalEarnings - totalDeductions;
        return new FinalPayslipDetails(instituteId, bulkSalaryPayslip, totalEarnings, totalDeductions, netAmount);
    }

    private Map<UUID, SalaryPayslip> getMapOfStaffPayslip(List<SalaryPayslip> salaryPayslipList) {
        Map<UUID, SalaryPayslip> salaryPayslipMap = new HashMap<UUID, SalaryPayslip>();
        for (SalaryPayslip salaryPayslip : salaryPayslipList) {
            salaryPayslipMap.put(salaryPayslip.getStaff().getStaffId(), salaryPayslip);
        }
        return salaryPayslipMap;
    }

    public List<SalaryCycle> getSalaryCycles(int instituteId, int academicSessionId){
        return salaryDao.getSalaryCycles(instituteId, academicSessionId);
    }

    public SalaryCycle getSalaryCycle(int instituteId, int academicSessionId, int cycleId){
        return salaryDao.getSalaryCycle(instituteId, academicSessionId, cycleId);
    }

    public LinkedHashMap<String, String> getCycleDetails(int instituteId, int academicSessionId) {
        if (instituteId <= 0) {
            logger.error("Invalid institute id");
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_PAY_HEAD_CONFIGURATION,
                    "Invalid institute id."));
        }
        if (academicSessionId <= 0) {
            logger.error("Invalid academic session id");
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_PAY_HEAD_CONFIGURATION,
                    "Invalid academic session id."));
        }

        AcademicSession academicSession = instituteManager.getAcademicSessionByAcademicSessionId(academicSessionId);

        long startDateLong = academicSession.getSessionStartTime() * 1000l;
        DateTime startDateTime = new DateTime(startDateLong, DateTimeZone.forTimeZone(DateUtils.DEFAULT_TIMEZONE));
        Date startDate = startDateTime.toDate();
        Calendar start = Calendar.getInstance();
        start.setTime(startDate);

        long endDateLong = academicSession.getSessionEndTime() * 1000l;
        DateTime endDateTime = new DateTime(endDateLong, DateTimeZone.forTimeZone(DateUtils.DEFAULT_TIMEZONE));
        Date endDate = endDateTime.toDate();
        Calendar end = Calendar.getInstance();
        end.setTime(endDate);

        LinkedHashMap<String, String> cycleDetailsMap = new LinkedHashMap<>();

        boolean isFirstMonth = true;
        for (Date date = start.getTime(); start.before(end); start.add(Calendar.MONTH, 1),
                date = start.getTime()) {
            if (isFirstMonth && Month.of(date.getMonth() + 1) != academicSession.getStartMonth()) {
                continue;
            }
            date.setDate(1);
            int month = date.getMonth() + 1;
            int year = 1900 + date.getYear();
            String cycleName = "01-" + Month.of(month).name() + "-" + year;
            String cycleNameWithMonthNum = "1-" + month + "-" + year;
            cycleDetailsMap.put(cycleNameWithMonthNum, cycleName);
            isFirstMonth = false;
        }

        return cycleDetailsMap;
    }

    public boolean addBulkSalaryCycles(int instituteId, int academicSessionId, UUID userId, List<SalaryCycleDetailsReadable> salaryCycleDetailsReadableList) {
        if (instituteId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid institute id."));
        }
        if (userId == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid user id."));
        }
        userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.SALARY_CYCLE);

        if (academicSessionId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid academic session id."));
        }

        List<SalaryCycle> existingSalaryCycle = getSalaryCycles(instituteId, academicSessionId);
        if (!CollectionUtils.isEmpty(existingSalaryCycle)) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_SALARY_CYCLE_DETAILS, "This flow is for fresh setup of cycles for any session, please contact the technical team for salary cycle clean up."));

        }

        validateSalaryCycleDetailsReadable(salaryCycleDetailsReadableList);

        List<SalaryCycleDetailsPayload> salaryCycleDetailsPayloadList = new ArrayList<>();
        for (SalaryCycleDetailsReadable salaryCycles : salaryCycleDetailsReadableList) {

            int startDateUnixTimestamp = DateUtils.getDayStart(DateUtils.getTimestampFromDate(salaryCycles.getStartDate()), DateUtils.DEFAULT_TIMEZONE);
            int endDateUnixTimestamp = DateUtils.getDayEnd(DateUtils.getTimestampFromDate(salaryCycles.getEndDate()), DateUtils.DEFAULT_TIMEZONE);

            if (startDateUnixTimestamp >= endDateUnixTimestamp) {
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_SALARY_CYCLE_DETAILS, "End Date cannot be before Start Date."));
            }

            salaryCycleDetailsPayloadList.add(new SalaryCycleDetailsPayload(startDateUnixTimestamp, endDateUnixTimestamp, salaryCycles.getCycleName()));
        }

        if (CollectionUtils.isEmpty(salaryCycleDetailsPayloadList)) {
            return false;
        }
        if (salaryCycleDetailsReadableList.size() != salaryCycleDetailsPayloadList.size()) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_SALARY_CYCLE_DETAILS, "Invalid start date or end date. Please check and try again."));

        }

        return salaryDao.addBulkSalaryCycles(instituteId, academicSessionId, salaryCycleDetailsPayloadList);
    }

    public boolean addBulkSalaryCycles(int instituteId, int academicSessionId, List<SalaryCycleDetailsPayload> salaryCycleDetailsPayloadList) {
        return salaryDao.addBulkSalaryCycles(instituteId, academicSessionId, salaryCycleDetailsPayloadList);
    }

    public void validateSalaryCycleDetailsReadable(List<SalaryCycleDetailsReadable> salaryCycleDetailsReadableList) {

        if (CollectionUtils.isEmpty(salaryCycleDetailsReadableList)) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_SALARY_CYCLE_DETAILS, "Ensure you provide a data to generate salary cycle"));
        }

        for (SalaryCycleDetailsReadable salaryCycleDetail : salaryCycleDetailsReadableList) {
            if (salaryCycleDetail == null) {
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_SALARY_CYCLE_DETAILS, "The provided salary cycle details are invalid. Please ensure all required fields are correctly filled and the dates are in the correct format."));
            }
            if (StringUtils.isBlank(salaryCycleDetail.getStartDate()) || !DateUtils.validateDateFormat(salaryCycleDetail.getStartDate())) {
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_SALARY_CYCLE_DETAILS, "Start date cannot be null, blank, or incorrectly formatted. Please provide a valid start date,"));
            }
            if (StringUtils.isBlank(salaryCycleDetail.getEndDate()) || !DateUtils.validateDateFormat(salaryCycleDetail.getEndDate())) {
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_SALARY_CYCLE_DETAILS, "End date cannot be null, blank, or incorrectly formatted. Please provide a valid end date."));
            }
            if (StringUtils.isBlank(salaryCycleDetail.getCycleName())) {
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_SALARY_CYCLE_DETAILS, "Cycle name cannot be null or blank."));
            }
        }
    }
}
