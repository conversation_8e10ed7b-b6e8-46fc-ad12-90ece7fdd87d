package com.embrate.cloud.core.lib.scratchcard;

import com.embrate.cloud.core.api.scratchcard.RewardType;
import com.embrate.cloud.core.api.scratchcard.ScratchCardDetails;
import com.embrate.cloud.core.api.wallet.WalletTransactionCategory;
import com.embrate.cloud.core.api.wallet.WalletTransactionMetaDataKeys;
import com.embrate.cloud.core.api.wallet.WalletTransactionPayload;
import com.embrate.cloud.core.lib.scratchcard.rewards.RewardManager;
import com.embrate.cloud.core.lib.wallet.UserWalletManager;
import com.embrate.cloud.dao.tier.scratchcard.ScratchCardDao;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.fees.payment.FeePaymentResponse;
import com.lernen.cloud.core.api.fees.payment.FeePaymentTransactionStatus;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.exceptions.ExceptionHandling;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.dao.DataAccessException;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public class ScratchCardManager {

    private final RewardManager rewardManager;
    private final ScratchCardDao scratchCardDao;
    private final UserPreferenceSettings userPreferenceSettings;
    private final UserWalletManager userWalletManager;
    private final TransactionTemplate transactionTemplate;

    private static final Logger logger = LogManager.getLogger(RewardManager.class);

    public ScratchCardManager(RewardManager rewardManager, ScratchCardDao scratchCardDao,
                              UserPreferenceSettings userPreferenceSettings, UserWalletManager userWalletManager,
                              TransactionTemplate transactionTemplate) {
        this.rewardManager = rewardManager;
        this.scratchCardDao = scratchCardDao;
        this.userPreferenceSettings = userPreferenceSettings;
        this.userWalletManager = userWalletManager;
        this.transactionTemplate = transactionTemplate;
    }

    public List<ScratchCardDetails> addScratchCardDetails(int instituteId, UUID studentId, FeePaymentResponse feePaymentResponse) {
        if (instituteId <= 0) {
            logger.error("Invalid institute.");
        }

        if (feePaymentResponse == null || feePaymentResponse.getTransactionId() == null) {
            logger.error("Invalid feePaymentResponse details.");
        }

        if(!userPreferenceSettings.getRewardsPreferences(instituteId).isRewardEnabled()) {
            logger.warn("Reward is not enable for institute " + instituteId + ".");
            return null;
        }

        List<ScratchCardDetails> scratchCardDetailsList = rewardManager.generateScratchCards(instituteId, studentId, feePaymentResponse);
        if(CollectionUtils.isEmpty(scratchCardDetailsList)) {
            logger.info("Error while allocating the rewards for transaction id " + feePaymentResponse.getTransactionId() + ".");
            return null;
        }

        List<UUID> scratchCardIdList = scratchCardDao.addScratchCardDetails(scratchCardDetailsList);

        if(CollectionUtils.isEmpty(scratchCardIdList)) {
            logger.error("Error while adding reward for transaction "
                    + feePaymentResponse.getTransactionId() + "for student " + studentId);
            return scratchCardDetailsList;
        }

        return scratchCardDetailsList;
    }

    public List<ScratchCardDetails> getScratchCardDetails(int instituteId, UUID userId, UUID transactionId) {
        if (instituteId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute."));
        }
        if (userId == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid user."));
        }
        return scratchCardDao.getScratchCardDetails(instituteId, userId, transactionId);
    }

    public boolean updateScratchCardStatus(int instituteId, UUID scratchCardId) {
        if (instituteId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute."));
        }
        if (scratchCardId == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_SCRATCH_CARD_DETAILS, "Invalid scratch card id."));
        }

        ScratchCardDetails scratchCardDetails = scratchCardDao.getScratchCardDetailsByScratchCardId(instituteId, scratchCardId);
        if(scratchCardDetails == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_SCRATCH_CARD_DETAILS, "Invalid scratch card details."));
        }

        try {
            final Boolean status = transactionTemplate.execute(new TransactionCallback<Boolean>() {

                @Override
                public Boolean doInTransaction(TransactionStatus status) {
                    boolean result = scratchCardDao.updateScratchCardStatus(instituteId, scratchCardId, scratchCardDetails);
                    if(!result) {
                        throw new RuntimeException("Unable to mark scratch card as scratched.");
                    }

                    RewardType rewardType = scratchCardDetails.getRewardDetails().getRewardType();
                    Map<String, Object> metaDataRewards = scratchCardDetails.getRewardDetails().getMetaData();
                    if(rewardType != RewardType.CASHBACK || metaDataRewards == null ||
                            CollectionUtils.isEmpty(metaDataRewards.entrySet())) {
                        return result;
                    }

                    Double amount = 0d;
                    if(metaDataRewards.containsKey("amount")) {
                        amount = Double.parseDouble(metaDataRewards.get("amount").toString());
                    }
                    if (Double.compare(amount, 0) != 0) {
                        Map<String, Object> metaData = new HashMap<String, Object>();
                        metaData.put(WalletTransactionMetaDataKeys.REWARDS_CASHBACK_AGAINST_TRANSACTION_ID, scratchCardDetails.getTransactionId());
                        metaData.put(WalletTransactionMetaDataKeys.REWARDS_CASHBACK_AGAINST_SCRATCH_CARD_ID, scratchCardDetails.getScratchCardId());
                        metaData.put(WalletTransactionMetaDataKeys.REWARDS_CASHBACK_AGAINST_REWARD_ID, scratchCardDetails.getRewardDetails().getRewardId());
                        //TODO:assuming cashback flow is for student for now
                        if (userWalletManager.addUserWalletTransaction(new WalletTransactionPayload(instituteId,
                                scratchCardDetails.getUserId(), UserType.STUDENT,
                                WalletTransactionCategory.REWARDS_CASHBACK_AMOUNT, null, DateUtils.now(), FeePaymentTransactionStatus.ACTIVE,
                                amount, scratchCardDetails.getUserId(), metaData, null)) == null) {
                            throw new RuntimeException("Unable to update student wallet.");
                        }
                    }
                    return result;
                }
            });
        } catch (final DataAccessException dataAccessException) {
            ExceptionHandling.HandleException(dataAccessException, null, null);
            logger.error(dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception while updating scratch card status for scratch card id {}", scratchCardId, e);
        }
        return false;
    }
}
