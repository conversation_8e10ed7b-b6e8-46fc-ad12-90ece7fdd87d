package com.embrate.cloud.core.lib.fees.config;

import com.embrate.cloud.core.api.fee.configuration.FeeConfigurationCloneRequest;
import com.embrate.cloud.core.api.fee.discount.assignment.StudentDiscountStructureAssignmentPayload;
import com.embrate.cloud.core.api.fee.discount.structure.FeeDiscountEntityStructure;
import com.embrate.cloud.core.api.fee.discount.structure.FeeDiscountStructure;
import com.embrate.cloud.core.api.fee.discount.structure.FeeHeadDiscountStructure;
import com.embrate.cloud.core.api.fee.discount.structure.FeeIdDiscountStructure;
import com.embrate.cloud.core.api.fee.setup.FeeStructureCloneClassRequest;
import com.embrate.cloud.core.api.fee.setup.StructureCloneRequest;
import com.embrate.cloud.core.api.fee.setup.StructureCloneType;
import com.embrate.cloud.core.utils.EMapUtils;
import com.embrate.cloud.core.utils.institute.StandardUtils;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.fee.discount.BulkStudentDiscountStructureAssignmentPayload;
import com.lernen.cloud.core.api.fee.discount.DiscountStructureType;
import com.lernen.cloud.core.api.fees.*;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.lib.fees.configuration.FeeConfigurationManager;
import com.lernen.cloud.core.lib.fees.configuration.FeeDiscountConfigurationManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.NumberUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.joda.time.DateTimeConstants;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;


import java.time.Month;
import org.joda.time.DateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.lernen.cloud.core.utils.DateUtils.isLeapYear;

/**
 * <AUTHOR>
 */
public class FeeSetupUtilityManager {

	private static final Logger logger = LogManager.getLogger(FeeSetupUtilityManager.class);

	private final FeeConfigurationManager feeConfigurationManager;

	private final FeeDiscountConfigurationManager feeDiscountConfigurationManager;

	private final InstituteManager instituteManager;

	private final StudentManager studentManager;

	private final TransactionTemplate transactionTemplate;

	public FeeSetupUtilityManager(FeeConfigurationManager feeConfigurationManager,
								  FeeDiscountConfigurationManager feeDiscountConfigurationManager,
								  InstituteManager instituteManager, StudentManager studentManager,
								  TransactionTemplate transactionTemplate) {
		this.feeConfigurationManager = feeConfigurationManager;
		this.feeDiscountConfigurationManager = feeDiscountConfigurationManager;
		this.instituteManager = instituteManager;
		this.studentManager = studentManager;
		this.transactionTemplate = transactionTemplate;
	}

	public boolean cloneFeeStructureAcrossSessions(StructureCloneRequest request) {
		if (request == null || request.getInstituteId() <= 0
				|| request.getSrcAcademicSessionId() <= 0 || request.getDestAcademicSessionId() <= 0
				|| request.getCloneType() == null) {
			logger.error("Invalid request {}", request);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid request"));
		}

		if (request.getCloneType() == StructureCloneType.ALL_STRUCTURES && CollectionUtils.isNotEmpty(request.getStructureNames())) {
			logger.error("Invalid request. cloneType = ALL_STRUCTURES but structure names are provided  {}", request);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid request. cloneType = ALL_STRUCTURES but structure names are provided"));
		}

		if (request.getCloneType() == StructureCloneType.CUSTOM && CollectionUtils.isEmpty(request.getStructureNames())) {
			logger.error("Invalid request. cloneType = CUSTOM but no structure names are provided  {}", request);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid request. cloneType = CUSTOM but no structure names are provided"));
		}

		int instituteId = request.getInstituteId();
		int srcSessionId = request.getSrcAcademicSessionId();
		int destSessionId = request.getDestAcademicSessionId();
		final List<DefaultEntityFeeAssignmentStructure> srcStructureList = feeConfigurationManager
				.getDefaultFeeAssignmentStructure(request.getInstituteId(), srcSessionId);
		if (CollectionUtils.isEmpty(srcStructureList)) {
			logger.error("No structures found in institute & src session {}", request);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "No structures found in institute & src session"));
		}
		Map<UUID, UUID> srcToDestSessionFeeMap = getSrcToDestFeesMap(instituteId, srcSessionId, destSessionId);

		List<DefaultEntityFeeAssignmentStructurePayload> destDefaultEntityFeeAssignmentStructurePayloads = new ArrayList<>();
		for (DefaultEntityFeeAssignmentStructure structure : srcStructureList) {
			if (!cloneStructure(request, structure.getStructureName())) {
				logger.info("Structure {} is not expected to be cloned. So Skipping for request {}", structure.getStructureName(), request);
				continue;
			}
			List<EntityFeeAssignmentPayload> destEntityFeeAssignmentPayloadList = new ArrayList<>();
			for (EntityFeeAssignment entityFeeAssignment : structure.getEntityFeeAssignments()) {
				List<FeeIdFeeHead> feeIdFeeHeadsList = new ArrayList<>();
				for (FeeIdFeeHeadDetails feeIdFeeHeadDetails : entityFeeAssignment.getFeeIdFeeHeadDetailsList()) {
					UUID srcFeeId = feeIdFeeHeadDetails.getFeeConfigurationResponse().getFeeConfigurationBasicInfo().getFeeId();
					if (srcToDestSessionFeeMap.get(srcFeeId) != null) {
						List<FeeHeadAmount> destFeeHeadAmountList = new ArrayList<>();
						for (FeeHeadAmountDetails feeHeadAmountDetails : feeIdFeeHeadDetails.getFeeHeadAmountDetailsList()) {
							destFeeHeadAmountList.add(new FeeHeadAmount(feeHeadAmountDetails.getFeeHeadId(), feeHeadAmountDetails.getAmount(), feeHeadAmountDetails.getFeeEntity()));
						}
						feeIdFeeHeadsList.add(new FeeIdFeeHead(srcToDestSessionFeeMap.get(srcFeeId), destFeeHeadAmountList));
					}
				}
				if (CollectionUtils.isNotEmpty(feeIdFeeHeadsList)) {
					destEntityFeeAssignmentPayloadList.add(new EntityFeeAssignmentPayload(entityFeeAssignment.getEntityId(), entityFeeAssignment.getFeeEntity(), feeIdFeeHeadsList));
				}
			}
			if (CollectionUtils.isNotEmpty(destEntityFeeAssignmentPayloadList)) {
				DefaultEntityFeeAssignmentStructurePayload destStructure = new DefaultEntityFeeAssignmentStructurePayload(structure.getStructureName().trim(), null, structure.getFeeStructureType(), destEntityFeeAssignmentPayloadList);
				destDefaultEntityFeeAssignmentStructurePayloads.add(destStructure);
			}
		}

		if (CollectionUtils.isEmpty(destDefaultEntityFeeAssignmentStructurePayloads)) {
			logger.error("No Fees are matching in src to dest session. So not able to create fee structures in dest session {}", request);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "No Fees are matching in src to dest session. So not able to create fee structures in dest session"));
		}

		return feeConfigurationManager.createDefaultFeeAssignmentStructure(instituteId,
				destSessionId, destDefaultEntityFeeAssignmentStructurePayloads, null, true);
	}

	public boolean cloneFeeStructureClass(FeeStructureCloneClassRequest request) {
		if (request == null || request.getInstituteId() <= 0
				|| request.getAcademicSessionId() <= 0 || StringUtils.isBlank(request.getStructureName())
				|| StringUtils.isBlank(request.getSrcClassName())
				|| CollectionUtils.isEmpty(request.getDestClassNames())) {
			logger.error("Invalid request {}", request);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid request"));
		}

		int instituteId = request.getInstituteId();
		int sessionId = request.getAcademicSessionId();
		String structureName = request.getStructureName();
		String srcClassName = request.getSrcClassName();


		final List<DefaultEntityFeeAssignmentStructure> srcStructureList = feeConfigurationManager
				.getDefaultFeeAssignmentStructure(instituteId, sessionId);
		if (CollectionUtils.isEmpty(srcStructureList)) {
			logger.error("No structures found in institute & src session {}", request);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "No structures found in institute & src session"));
		}

		Map<String, UUID> standardMap = getStandardMap(instituteId, sessionId);
		UUID srcClassId = standardMap.get(srcClassName.trim().toLowerCase());
		DefaultEntityFeeAssignmentStructure reqStructure = null;
		List<EntityFeeAssignmentPayload> destEntityFeeAssignmentPayloadList = new ArrayList<>();

		for (DefaultEntityFeeAssignmentStructure structure : srcStructureList) {
			if (!structure.getStructureName().equalsIgnoreCase(structureName.trim().toLowerCase())) {
				continue;
			}
			logger.info("Found structure with name {}", structureName);
			reqStructure = structure;

			for (EntityFeeAssignment entityFeeAssignment : structure.getEntityFeeAssignments()) {
				List<FeeIdFeeHead> feeIdFeeHeadsList = getFeeIdFeeHeadsCopy(entityFeeAssignment);

				if (entityFeeAssignment.getFeeEntity() != FeeEntity.CLASS || !srcClassId.equals(UUID.fromString(entityFeeAssignment.getEntityId()))) {
					destEntityFeeAssignmentPayloadList.add(new EntityFeeAssignmentPayload(entityFeeAssignment.getEntityId(), entityFeeAssignment.getFeeEntity(), feeIdFeeHeadsList));
				} else {
					destEntityFeeAssignmentPayloadList.add(new EntityFeeAssignmentPayload(entityFeeAssignment.getEntityId(), entityFeeAssignment.getFeeEntity(), feeIdFeeHeadsList));
					for (String destClass : request.getDestClassNames()) {
						UUID destClassId = standardMap.get(destClass.trim().toLowerCase());
						if (destClassId == null) {
							logger.warn("Class name {} not found ", destClass);
							continue;
						}
						destEntityFeeAssignmentPayloadList.add(new EntityFeeAssignmentPayload(destClassId.toString(), entityFeeAssignment.getFeeEntity(), feeIdFeeHeadsList));
					}
				}
			}
			break;
		}

		if (reqStructure == null || CollectionUtils.isEmpty(destEntityFeeAssignmentPayloadList)) {
			logger.error("No structure found or no valid entity found for clone for request {}", request);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "No structure found or no valid entity found for clone for request"));
		}

		DefaultEntityFeeAssignmentStructurePayload destStructure = new DefaultEntityFeeAssignmentStructurePayload(reqStructure.getStructureName().trim(), reqStructure.getStructureId(), reqStructure.getFeeStructureType(), destEntityFeeAssignmentPayloadList);

		return feeConfigurationManager.updateDefaultFeeAssignmentStructure(instituteId,
				sessionId, reqStructure.getStructureId(), Arrays.asList(destStructure), null, true);
	}


	public boolean assignStudentDiscountStructure(int instituteId, StudentDiscountStructureAssignmentPayload payload) {
		if (instituteId <= 0 || payload == null || payload.getAcademicSessionId() <= 0 || MapUtils.isEmpty(payload.getDiscountStructureStudents())) {
			logger.error("Invalid instituteId {} or payload {}", instituteId, payload);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid institute or payload"));
		}
		AcademicSession academicSession = instituteManager.getAcademicSession(instituteId, payload.getAcademicSessionId());
		if (academicSession == null) {
			logger.error("Invalid session {} for instituteId {} or payload {}", payload.getAcademicSessionId(), instituteId);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid session for institute"));
		}
		int academicSessionId = academicSession.getAcademicSessionId();
		Map<String, FeeDiscountStructure> discountStructureMap = getFeeDiscountStructureMap(instituteId, academicSessionId);
		Map<String, Student> studentMap = getStudentMap(instituteId, academicSessionId);
		Map<UUID, Set<UUID>> structureStudentAssignmentMap = new LinkedHashMap<>();
		for (Map.Entry<String, Set<String>> entry : payload.getDiscountStructureStudents().entrySet()) {
			String structureName = entry.getKey().trim().toLowerCase();
			FeeDiscountStructure feeDiscountStructure = discountStructureMap.get(structureName);
			if (feeDiscountStructure == null) {
				logger.error("Invalid structureName {} for instituteId {} or session {}", entry.getKey(), instituteId, academicSessionId);
				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid structureName " + entry.getKey()));
			}

			Set<UUID> studentSet = new HashSet<>();
			for (String entryValue : entry.getValue()) {
				String admissionNumber = entryValue.trim().toLowerCase();
				Student student = studentMap.get(admissionNumber);
				if (student == null) {
					logger.error("Invalid admission number {} for instituteId {} or session {}", admissionNumber, instituteId, academicSessionId);
					throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid admission number " + entryValue));
				}
				studentSet.add(student.getStudentId());
			}
			structureStudentAssignmentMap.put(feeDiscountStructure.getFeeDiscountMetadata().getDiscountStructureId(), studentSet);
		}
		return transactionTemplate.execute(new TransactionCallback<Boolean>() {
			@Override
			public Boolean doInTransaction(TransactionStatus transactionStatus) {
				for (Map.Entry<UUID, Set<UUID>> entry : structureStudentAssignmentMap.entrySet()) {
					try {
						for(UUID studentId : entry.getValue()){
							//Internal implementation support single user only
							final boolean addedStudentDiscountConfig = feeDiscountConfigurationManager.bulkAssignDiscountStructure(
									instituteId, academicSessionId, new BulkStudentDiscountStructureAssignmentPayload(Collections.singleton(studentId), Collections.singleton(entry.getKey())), null, true, true);
							if(!addedStudentDiscountConfig){
								logger.error("Unable to perform assignment of structure {}, institute {}, session {}, studentId {}", entry.getKey(), instituteId, academicSessionId, studentId);
								throw new EmbrateRunTimeException("Unable to assign structure to student");
							}
						}
					} catch (Exception e) {
						logger.error("Error in assignment of structure {}, institute {}, session {}", entry.getKey(), instituteId, academicSessionId, e);
						throw new EmbrateRunTimeException("Error in assignment of structure");
					}
				}
				return true;
			}
		});
	}

	private Map<String, Student> getStudentMap(int instituteId, int academicSessionId) {
		Map<String, Student> studentMap = EMapUtils.getMap(studentManager.getStudentsInAcademicSession(instituteId, academicSessionId), new EMapUtils.MapFunction<Student, String, Student>() {
			@Override
			public String getKey(Student entry) {
				return entry.getStudentBasicInfo().getAdmissionNumber().trim().toLowerCase();
			}

			@Override
			public Student getValue(Student entry) {
				return entry;
			}
		});
		return studentMap;
	}

	private Map<String, FeeDiscountStructure> getFeeDiscountStructureMap(int instituteId, int academicSessionId) {
		List<FeeDiscountStructure> discountStructures = feeDiscountConfigurationManager.getDiscountStructure(instituteId, academicSessionId);
		Map<String, FeeDiscountStructure> discountStructureMap = EMapUtils.getMap(discountStructures, new EMapUtils.MapFunction<FeeDiscountStructure, String, FeeDiscountStructure>() {
			@Override
			public String getKey(FeeDiscountStructure entry) {
				return entry.getFeeDiscountMetadata().getName().trim().toLowerCase();
			}

			@Override
			public FeeDiscountStructure getValue(FeeDiscountStructure entry) {
				return entry;
			}
		});
		return discountStructureMap;
	}


	private static List<FeeIdFeeHead> getFeeIdFeeHeadsCopy(EntityFeeAssignment entityFeeAssignment) {
		List<FeeIdFeeHead> feeIdFeeHeadsList = new ArrayList<>();
		for (FeeIdFeeHeadDetails feeIdFeeHeadDetails : entityFeeAssignment.getFeeIdFeeHeadDetailsList()) {
			UUID srcFeeId = feeIdFeeHeadDetails.getFeeConfigurationResponse().getFeeConfigurationBasicInfo().getFeeId();
			List<FeeHeadAmount> destFeeHeadAmountList = new ArrayList<>();
			for (FeeHeadAmountDetails feeHeadAmountDetails : feeIdFeeHeadDetails.getFeeHeadAmountDetailsList()) {
				destFeeHeadAmountList.add(new FeeHeadAmount(feeHeadAmountDetails.getFeeHeadId(), feeHeadAmountDetails.getAmount(), feeHeadAmountDetails.getFeeEntity()));
			}
			feeIdFeeHeadsList.add(new FeeIdFeeHead(srcFeeId, destFeeHeadAmountList));
		}
		return feeIdFeeHeadsList;
	}

	private boolean cloneStructure(StructureCloneRequest request, String structureName) {
		if (request.getCloneType() == StructureCloneType.ALL_STRUCTURES) {
			return true;
		}
		if (CollectionUtils.isEmpty(request.getStructureNames())) {
			return false;
		}
		return request.getStructureNames().contains(structureName.trim());
	}

	private Map<UUID, UUID> getSrcToDestFeesMap(int instituteId, int srcSessionId, int destSessionId) {
		List<FeeConfigurationResponse> srcFees = feeConfigurationManager.getFeeConfigurationByAcademicYear(instituteId, srcSessionId);
		List<FeeConfigurationResponse> destFees = feeConfigurationManager.getFeeConfigurationByAcademicYear(instituteId, destSessionId);

		EMapUtils.MapFunction fun = new EMapUtils.MapFunction<FeeConfigurationResponse, String, UUID>() {
			@Override
			public String getKey(FeeConfigurationResponse entry) {
				return entry.getFeeConfigurationBasicInfo().getFeeName().toLowerCase().trim();
			}

			@Override
			public UUID getValue(FeeConfigurationResponse entry) {
				return entry.getFeeConfigurationBasicInfo().getFeeId();
			}
		};

		Map<String, UUID> srcFeeMap = EMapUtils.getMap(srcFees, fun);
		Map<String, UUID> destFeeMap = EMapUtils.getMap(destFees, fun);

		Map<UUID, UUID> srcToDestSessionFeeMap = new HashMap<>();
		for (Map.Entry<String, UUID> srcEntry : srcFeeMap.entrySet()) {
			String srcFeeName = srcEntry.getKey();
			UUID destFeeId = null;
			if (destFeeMap.containsKey(srcFeeName)) {
				destFeeId = destFeeMap.get(srcFeeName);
			} else {
				destFeeId = getMatchingFees(destFeeMap, srcFeeName);
			}
			srcToDestSessionFeeMap.put(srcEntry.getValue(), destFeeId);
		}
		return srcToDestSessionFeeMap;
	}

	private UUID getMatchingFees(Map<String, UUID> destFeeMap, String srcFeeName) {
		String[] srcTokens = srcFeeName.split(" ");
		for (Map.Entry<String, UUID> destEntry : destFeeMap.entrySet()) {
			String destFeeName = destEntry.getKey();
			String[] destTokens = destFeeName.split(" ");
			// If fees name is not matched directly, handling for the case where fees name
			// is like "month year" eg: January 2023.
			if (srcTokens.length == destTokens.length) {
				boolean matchFound = true;
				for (int i = 0; i < srcTokens.length; i++) {
					if (srcTokens[i].equalsIgnoreCase(destTokens[i]) ||
							(NumberUtils.isInteger(srcTokens[i]) && NumberUtils.isInteger(destTokens[i]))) {
						continue;
					}
					matchFound = false;
				}
				if (matchFound) {
					return destEntry.getValue();
				}
			}
		}
		return null;
	}

	private Map<String, UUID> getStandardMap(int instituteId, int srcSessionId) {
		List<Standard> standardList = instituteManager.getInstituteStandardDetails(instituteId, srcSessionId);
		return StandardUtils.getStandardIdMap(standardList);
	}

	public boolean cloneDiscountStructureAcrossSessions(StructureCloneRequest request) {
		if (request == null || request.getInstituteId() <= 0
				|| request.getSrcAcademicSessionId() <= 0 || request.getDestAcademicSessionId() <= 0
				|| request.getCloneType() == null) {
			logger.error("Invalid request {}", request);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid request"));
		}

		if (request.getCloneType() == StructureCloneType.ALL_STRUCTURES && CollectionUtils.isNotEmpty(request.getStructureNames())) {
			logger.error("Invalid request. cloneType = ALL_STRUCTURES but structure names are provided  {}", request);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid request. cloneType = ALL_STRUCTURES but structure names are provided"));
		}

		if (request.getCloneType() == StructureCloneType.CUSTOM && CollectionUtils.isEmpty(request.getStructureNames())) {
			logger.error("Invalid request. cloneType = CUSTOM but no structure names are provided  {}", request);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid request. cloneType = CUSTOM but no structure names are provided"));
		}

		int instituteId = request.getInstituteId();
		int srcSessionId = request.getSrcAcademicSessionId();
		int destSessionId = request.getDestAcademicSessionId();
		final List<FeeDiscountStructure> feeDiscountStructureList = feeDiscountConfigurationManager.getDiscountStructure(
				request.getInstituteId(), srcSessionId, DiscountStructureType.CUSTOM);
		if (CollectionUtils.isEmpty(feeDiscountStructureList)) {
			logger.error("No structures found in institute & src session {}", request);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "No structures found in institute & src session"));
		}
		Map<UUID, UUID> srcToDestSessionFeeMap = getSrcToDestFeesMap(instituteId, srcSessionId, destSessionId);

		List<EntityDiscountStructurePayload> defaultEntityDiscountAssignmentStructurePayloadList = new ArrayList<>();
		for (FeeDiscountStructure feeDiscountStructure : feeDiscountStructureList) {
			if (!cloneStructure(request, feeDiscountStructure.getFeeDiscountMetadata().getName())) {
				logger.info("Structure {} is not expected to be cloned. So Skipping for request {}", feeDiscountStructure.getFeeDiscountMetadata().getName(), request);
				continue;
			}
			List<EntityFeeAssignmentPayload> destEntityFeeAssignmentPayloadList = new ArrayList<>();
			for (FeeDiscountEntityStructure feeDiscountEntityStructure : feeDiscountStructure.getFeeDiscountEntityStructures()) {
				List<FeeIdFeeHead> feeIdFeeHeadsList = new ArrayList<>();
				for (FeeIdDiscountStructure feeIdDiscountStructure : feeDiscountEntityStructure.getFeeIdDiscountStructures()) {
					UUID srcFeeId = feeIdDiscountStructure.getFeeConfigurationBasicInfo().getFeeId();
					if (srcToDestSessionFeeMap.get(srcFeeId) != null) {
						List<FeeHeadAmount> destFeeHeadAmountList = new ArrayList<>();
						for (FeeHeadDiscountStructure feeHeadDiscountStructure : feeIdDiscountStructure.getFeeHeadDiscountStructures()) {
							destFeeHeadAmountList.add(new FeeHeadAmount(feeHeadDiscountStructure.getFeeHeadConfiguration().getFeeHeadId(),
									feeHeadDiscountStructure.getAmount(), feeHeadDiscountStructure.isPercent(), feeDiscountEntityStructure.getFeeEntity()));
						}
						feeIdFeeHeadsList.add(new FeeIdFeeHead(srcToDestSessionFeeMap.get(srcFeeId), destFeeHeadAmountList));
					}
				}
				if (CollectionUtils.isNotEmpty(feeIdFeeHeadsList)) {
					destEntityFeeAssignmentPayloadList.add(new EntityFeeAssignmentPayload(feeDiscountEntityStructure.getEntityId(),
							feeDiscountEntityStructure.getFeeEntity(), feeIdFeeHeadsList));
				}
			}
			if (CollectionUtils.isNotEmpty(destEntityFeeAssignmentPayloadList)) {
				EntityDiscountStructurePayload destStructure = new EntityDiscountStructurePayload(
						feeDiscountStructure.getFeeDiscountMetadata().getName().trim(), null,
						feeDiscountStructure.getFeeDiscountMetadata().getDiscountStructureType(),
						feeDiscountStructure.getFeeDiscountMetadata().getDescription(),
						feeDiscountStructure.getFeeDiscountMetadata().getMetadata(),
						destEntityFeeAssignmentPayloadList);
				defaultEntityDiscountAssignmentStructurePayloadList.add(destStructure);
			}
		}

		if (CollectionUtils.isEmpty(defaultEntityDiscountAssignmentStructurePayloadList)) {
			logger.error("No Fees are matching in src to dest session. So not able to create discount structures in dest session {}", request);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "No Fees are matching in src to dest session. So not able to create discount structures in dest session"));
		}

		final List<UUID> discountStructureIdList = feeDiscountConfigurationManager.createDiscountStructure(instituteId, destSessionId,
				defaultEntityDiscountAssignmentStructurePayloadList, null, true, true);

		return defaultEntityDiscountAssignmentStructurePayloadList.size() == discountStructureIdList.size();
	}

	public boolean cloneFeeConfigurationAcrossSession(FeeConfigurationCloneRequest feeConfigurationCloneRequest) {
		validateFeeConfigurationCloneRequest(feeConfigurationCloneRequest);

		List<FeeConfigurationResponse> destFeeConfigurationResponseList = feeConfigurationManager.getFeeConfigurationByAcademicYear(
				feeConfigurationCloneRequest.getInstituteId(),
				feeConfigurationCloneRequest.getDestAcademicSessionId()
		);

		if (!CollectionUtils.isEmpty(destFeeConfigurationResponseList)) {
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_FEE_HEAD_CONFIGURATION, "Fee already configured for this academic session"
			));
		}


		List<FeeConfigurationResponse> srcFeeConfigurationResponseList = feeConfigurationManager.getFeeConfigurationByAcademicYear(
				feeConfigurationCloneRequest.getInstituteId(),
				feeConfigurationCloneRequest.getSrcAcademicSessionId()
		);


		if (CollectionUtils.isEmpty(srcFeeConfigurationResponseList)) {
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_FEE_HEAD_CONFIGURATION, "Fee does not exist in the academic session"
			));
		}

		final int srcStartYear = srcFeeConfigurationResponseList.get(0).getAcademicSession().getStartYear();
		final int srcEndYear = srcFeeConfigurationResponseList.get(0).getAcademicSession().getEndYear();

		AcademicSession destAcademicSession = instituteManager.getAcademicSession(feeConfigurationCloneRequest.getInstituteId(), feeConfigurationCloneRequest.getDestAcademicSessionId());
		if (destAcademicSession == null) {
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_ACADEMIC_SESSION_DETAILS, "Academic session does not exist"
			));
		}

		final Month destStartMonth = destAcademicSession.getStartMonth();
		final Month destEndMonth = destAcademicSession.getEndMonth();
		final int destStartYear = destAcademicSession.getStartYear();
		final int destEndYear = destAcademicSession.getEndYear();

		List<FeeConfigurationBasicInfo> clonedFeeConfigurationBasicInfo = new ArrayList<>();
		for (FeeConfigurationResponse feeConfigurationResponse : srcFeeConfigurationResponseList) {
			FeeConfigurationBasicInfo basicInfo = feeConfigurationResponse.getFeeConfigurationBasicInfo();
			String updatedFeeName = updateFeeNameForNewSession(basicInfo.getFeeName(),
					String.valueOf(srcStartYear), String.valueOf(srcEndYear), String.valueOf(destStartYear), String.valueOf(destEndYear));

			Integer clonedDueDate = clonedDueDateForNewSession(basicInfo.getDueDate(), srcStartYear, destStartYear);
			MonthYear startMonthYear  = feeConfigurationResponse.getFeeConfigurationBasicInfo().getStartMonthYear();
			MonthYear endMonthYear = feeConfigurationResponse.getFeeConfigurationBasicInfo().getEndMonthYear();
			MonthYear newStartMonthYear = updateStartEndYear(startMonthYear, srcStartYear, srcEndYear, destStartYear, destEndYear);
			MonthYear newEndMonthYear = updateStartEndYear(endMonthYear, srcStartYear, srcEndYear, destStartYear, destEndYear);

			FeeConfigurationBasicInfo clonedBasicInfo = new FeeConfigurationBasicInfo(
					basicInfo.getInstituteId(),
					null,
					updatedFeeName,
					clonedDueDate,
					feeConfigurationCloneRequest.getDestAcademicSessionId(),
					basicInfo.getFeeType(),
					newStartMonthYear,
					newEndMonthYear,
					basicInfo.isAllowPendingEnrollment(),
					basicInfo.isFineApplicable(),
					basicInfo.isTransferToWallet(),
					basicInfo.getDescription()
			);

			clonedFeeConfigurationBasicInfo.add(clonedBasicInfo);
		}


		final List<UUID> feeIds = feeConfigurationManager.addFeeConfiguration(clonedFeeConfigurationBasicInfo);

		return !CollectionUtils.isEmpty(feeIds);
	}

	private MonthYear updateStartEndYear(MonthYear input, int srcStartYear, int srcEndYear, int destStartYear, int destEndYear) {
		if (input.getMonth() == null) return null;

		int year = input.getYear();
		int newYear = (year == srcStartYear) ? destStartYear :
				(year == srcEndYear) ? destEndYear : year;

		return new MonthYear(input.getMonth(), newYear);
	}

	private static void validateFeeConfigurationCloneRequest(FeeConfigurationCloneRequest feeConfigurationCloneRequest) {
		if (feeConfigurationCloneRequest.getInstituteId() <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if (feeConfigurationCloneRequest.getSrcAcademicSessionId() <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Invalid source academic session id."));
		}

		if (feeConfigurationCloneRequest.getDestAcademicSessionId() <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Invalid destination academic session id."));
		}
	}

	private String updateFeeNameForNewSession(String feeName, String currentStartYear, String currentEndYear, String newStartYear, String newEndYear) {
		String shortEndYear = currentEndYear.substring(2);
		String newShortEndYear = newEndYear.substring(2);

		String sessionRegex = currentStartYear + "\\s*[-–_]?\\s*(" + currentEndYear + "|" + shortEndYear + ")";

		Pattern pattern = Pattern.compile(sessionRegex);
		Matcher matcher = pattern.matcher(feeName);

		if (matcher.find()) {
			String matched = matcher.group(0);
			String matchedEnd = matcher.group(1);

			String separator = matched.contains("-") ? "-" :
					matched.contains("–") ? "–" :
							matched.contains("_") ? "_" : "-";

			String replacement = newStartYear + separator + (matchedEnd.equals(shortEndYear) ? newShortEndYear : newEndYear);
			feeName = matcher.replaceFirst(replacement);
		} else {
			if (feeName.contains(currentStartYear) && feeName.contains(currentEndYear)) {
				feeName = feeName.replace(currentStartYear, newStartYear).replace(currentEndYear, newEndYear);
			} else if (feeName.contains(currentStartYear)) {
				feeName = feeName.replace(currentStartYear, newStartYear);
			} else if (feeName.contains(currentEndYear)) {
				feeName = feeName.replace(currentEndYear, newEndYear);
			}
		}
		return feeName;
	}

	private Integer clonedDueDateForNewSession(long currentDueDate, int currentStartYear, int newStartYear) {
		DateTime currentDate = new DateTime(currentDueDate * 1000);
		int yearDifference = newStartYear - currentStartYear;
		int newYear = currentDate.getYear() + yearDifference;

		if (currentDate.getMonthOfYear() == DateTimeConstants.FEBRUARY &&
				currentDate.getDayOfMonth() == 29 && !isLeapYear(newYear)) {
			currentDate = currentDate.withMonthOfYear(DateTimeConstants.FEBRUARY).withDayOfMonth(28);
		} else {
			currentDate = currentDate.withYear(newYear);
		}
		long clonedEpochSeconds = currentDate.withTimeAtStartOfDay().getMillis() / 1000;

		return (int) clonedEpochSeconds ;
	}
}
