/**
 *
 */
package com.embrate.cloud.core.lib.discussionboard;

import com.embrate.cloud.core.api.discussionboard.*;
import com.embrate.cloud.dao.tier.discussionboard.DiscussionBoardDao;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.lib.managers.UserManager;
import org.apache.commons.lang3.StringUtils;

import java.util.UUID;

/**
 * <AUTHOR>
 *
 */
public class DiscussionBoardManager {

	private final DiscussionBoardDao discussionBoardDao;

	private final UserManager userManager;

	public DiscussionBoardManager(DiscussionBoardDao discussionBoardDao,
								  UserManager userManager) {
		this.discussionBoardDao = discussionBoardDao;
		this.userManager = userManager;
	}

	public boolean createChannel(int instituteId, UUID userId, ChannelDetailsPayload channelDetailsPayload) {
		channelDetailsPayload.setInstituteId(instituteId);
		channelDetailsPayload.setCreatedBy(userId);
		validateChannelDetails(channelDetailsPayload, false);
		return discussionBoardDao.createChannel(channelDetailsPayload);
	}

	private void validateChannelDetails(ChannelDetailsPayload channelDetailsPayload, boolean update) {
		if (channelDetailsPayload == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_CHANNEL_DETAILS, "Invalid channel payload."));
		}

		if (update) {
			if (channelDetailsPayload.getChannelId() == null) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_CHANNEL_DETAILS, "Invalid channel Id."));
			}
		} else {
			// checking this here as for homework and lecture API will be called
			// from Dao
			if (channelDetailsPayload.getCreatedBy() == null) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_CHANNEL_DETAILS, "Invalid created by Id."));
			}
		}

		if (channelDetailsPayload.getInstituteId() == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute Id."));
		}

		if (channelDetailsPayload.getDiscussionBoardEntity() == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_CHANNEL_DETAILS,
					"Invalid discussion board entity."));
		}

		if (StringUtils.isBlank(channelDetailsPayload.getChannelName())) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_CHANNEL_DETAILS, "Invalid channel name."));
		}

		if (channelDetailsPayload.getUserType() == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_CHANNEL_DETAILS, "Invalid user type."));
		}

	}

	public boolean updateChannelInfo(int instituteId, UUID userId, ChannelDetailsPayload channelDetailsPayload) {
		channelDetailsPayload.setInstituteId(instituteId);
		validateChannelDetails(channelDetailsPayload, true);

		final ChannelDetailsPayload channelDetails = discussionBoardDao.getChannelDetailsByChannelId(instituteId,
				channelDetailsPayload.getChannelId());
		if (channelDetails.getUserType() == UserType.SYSTEM) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_CHANNEL_DETAILS,
					"Sorry you cannot update this channel info " + "as it is system generated."));
		}
		if (!channelDetails.getCreatedBy().equals(userId)) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_CHANNEL_DETAILS,
					"Sorry you cannot update this channel info " + "as you have not created it."));
		}
		return discussionBoardDao.updateChannelInfo(channelDetailsPayload);
	}

	public boolean deleteChannel(int instituteId, UUID userId, UUID channelId) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute Id."));
		}
		if (userId == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid user Id."));
		}
		if (channelId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_CHANNEL_DETAILS, "Invalid channel Id."));
		}
		final ChannelDetailsPayload channelDetails = discussionBoardDao.getChannelDetailsByChannelId(instituteId,
				channelId);
		if (channelDetails.getUserType() == UserType.SYSTEM) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_CHANNEL_DETAILS,
					"Sorry you cannot delete this channel info " + "as it is system generated."));
		}
		if (!channelDetails.getCreatedBy().equals(userId)) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_CHANNEL_DETAILS,
					"Sorry you cannot delete this channel " + "as you have not created it."));
		}
		return discussionBoardDao.deleteChannel(instituteId, userId, channelId);
	}
	
	public UUID createThread(int instituteId, UUID userId, ThreadDetailsPayload threadDetailsPayload) {
		threadDetailsPayload.setCreatedBy(userId);
		validateThreadDetails(threadDetailsPayload, false);
		return discussionBoardDao.createThread(threadDetailsPayload);
	}

	private void validateThreadDetails(ThreadDetailsPayload threadDetailsPayload, boolean update) {
		if (threadDetailsPayload == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_THREAD_DETAILS, "Invalid thread payload."));
		}

		if (update) {
			if (threadDetailsPayload.getThreadId() == null) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_THREAD_DETAILS, "Invalid thread Id."));
			}
		} else {
			// checking this here as for homework and lecture API will be called
			// from Dao
			if (threadDetailsPayload.getCreatedBy() == null) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_THREAD_DETAILS, "Invalid created by Id."));
			}
		}

		if (threadDetailsPayload.getChannelId() == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_THREAD_DETAILS, "Invalid channel Id."));
		}

		if (StringUtils.isBlank(threadDetailsPayload.getName())) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_THREAD_DETAILS, "Invalid channel name."));
		}

		if (threadDetailsPayload.getUserType() == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_THREAD_DETAILS, "Invalid user type."));
		}

	}

	public ChannelDetailsPayload getChannelDetailsByChannelId(int instituteId, UUID channelId) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute Id."));
		}
		if (channelId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_THREAD_DETAILS, "Invalid channel Id."));
		}
		return discussionBoardDao.getChannelDetailsByChannelId(instituteId, channelId);
	}
	
	public boolean updateThreadDetails(int instituteId, UUID userId, ThreadDetailsPayload threadDetailsPayload) {
		validateThreadDetails(threadDetailsPayload, true);
		final ThreadDetailsPayload threadDetails = discussionBoardDao.getThreadDetailsByThreadId(instituteId,
				threadDetailsPayload.getThreadId());
		if (!threadDetails.getCreatedBy().equals(userId)) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_CHANNEL_DETAILS,
					"Sorry you cannot update this thread " + "as you have not created it."));
		}
		return discussionBoardDao.updateThreadDetails(threadDetailsPayload);
	}

	public boolean deleteThread(int instituteId, UUID userId, UUID threadId) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute Id."));
		}
		if (userId == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid user Id."));
		}
		if (threadId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_THREAD_DETAILS, "Invalid thread Id."));
		}
		final ThreadDetailsPayload threadDetails = discussionBoardDao.getThreadDetailsByThreadId(instituteId, threadId);
		if (!threadDetails.getCreatedBy().equals(userId)) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_CHANNEL_DETAILS,
					"Sorry you cannot delete this thread " + "as you have not created it."));
		}
		return discussionBoardDao.deleteThread(instituteId, userId, threadId);
	}

	public UUID createConversation(int instituteId, UUID userId, ConversationPayload conversationPayload) {
		conversationPayload.setCreatedBy(userId);
		validateConversationDetails(conversationPayload, false);
		return discussionBoardDao.createConversation(conversationPayload);
	}

	private void validateConversationDetails(ConversationPayload conversationPayload, boolean update) {
		if (conversationPayload == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_CONVERSATION_DETAILS,
					"Invalid conversation payload."));
		}

		if (update) {
			if (conversationPayload.getConversationId() == null) {
				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_CONVERSATION_DETAILS,
						"Invalid conversation id."));
			}
		} else {
			if (conversationPayload.getCreatedBy() == null) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_CONVERSATION_DETAILS, "Invalid createdBy id."));
			}
		}

		if (conversationPayload.getThreadId() == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_CONVERSATION_DETAILS, "Invalid thread id."));
		}

		if (StringUtils.isBlank(conversationPayload.getMessage())) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_CONVERSATION_DETAILS,
					"Invalid conversation message."));
		}
	}

	public boolean updateConversationDetails(int instituteId, UUID userId, ConversationPayload conversationPayload) {
		validateConversationDetails(conversationPayload, true);
		final ConversationPayload conversationDetails = discussionBoardDao
				.getConversationDetailsByConversationId(instituteId, conversationPayload.getConversationId());
		if (!conversationDetails.getCreatedBy().equals(userId)) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_CHANNEL_DETAILS,
					"Sorry you cannot update this conversation " + "as you have not created it."));
		}
		return discussionBoardDao.updateThreadDetails(conversationPayload);
	}

	public boolean deleteConversation(int instituteId, UUID userId, UUID conversationId) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute Id."));
		}
		if (userId == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid user Id."));
		}
		if (conversationId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_THREAD_DETAILS, "Invalid conversation Id."));
		}
		final ConversationPayload conversationDetails = discussionBoardDao
				.getConversationDetailsByConversationId(instituteId, conversationId);
		if (!conversationDetails.getCreatedBy().equals(userId)) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_CHANNEL_DETAILS,
					"Sorry you cannot delete this conversation " + "as you have not created it."));
		}
		return discussionBoardDao.deleteConversation(instituteId, userId, conversationId);
	}

	public ChannelDetails getThreadsInChannel(int instituteId, UUID channelId) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute Id."));
		}
		if (channelId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_CHANNEL_DETAILS, "Invalid channel Id."));
		}
		return discussionBoardDao.getThreadsInChannel(instituteId, channelId);
	}
	
	public ChannelDetailsPayload getChannelDetailsByThreadId(int instituteId, UUID threadId) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute Id."));
		}
		if (threadId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_CHANNEL_DETAILS, "Invalid channel Id."));
		}
		return discussionBoardDao.getChannelDetailsByThreadId(instituteId, threadId);
	}

	public ChannelDetails getEntityChannelDetails(int instituteId, DiscussionBoardEntity discussionBoardEntity,
			String entityId) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute Id."));
		}
		if (discussionBoardEntity == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_CHANNEL_DETAILS, "Invalid channel entity."));
		}

		if (entityId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_CHANNEL_DETAILS, "Invalid channel entity id."));
		}
		return discussionBoardDao.getEntityChannelDetails(instituteId, discussionBoardEntity, entityId);
	}

	public ThreadConversationDetails getConversationsInThread(int instituteId, UUID threadId) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute Id."));
		}
		if (threadId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_THREAD_DETAILS, "Invalid thread Id."));
		}
		ThreadConversationDetails threadConversationDetails = discussionBoardDao.getConversationsInThread(instituteId, threadId);
		if(threadConversationDetails == null) {
			return null;
		}
		ThreadDetailsPayload threadDetailsPayload = getThreadDetailsByThreadId(instituteId, threadId);
		if(threadDetailsPayload == null) {
			return threadConversationDetails;
		}
		User user = userManager.getUser(threadDetailsPayload.getCreatedBy());
		if(user == null) {
			return threadConversationDetails;
		}
		DiscussionBoardUserDetails discussionBoardUserDetails = new DiscussionBoardUserDetails(
				user.getUuid(), user.getUserInstituteId(), user.getUserType(), user.getFullName(), user.getGender());
		if(threadConversationDetails.getThreadMetaData() == null) {
			return threadConversationDetails;
		}
		threadConversationDetails.getThreadMetaData().setDiscussionBoardUserDetails(discussionBoardUserDetails);
		return threadConversationDetails;
	}
	
	public ChannelDetailsPayload getChannelDetailsByConversationId(int instituteId, UUID conversationId) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute Id."));
		}
		if (conversationId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_CHANNEL_DETAILS, "Invalid channel Id."));
		}
		
		return discussionBoardDao.getChannelDetailsByConversationId(instituteId, conversationId);
	}
	
	public ThreadDetailsPayload getThreadDetailsByConversationId(int instituteId, UUID conversationId) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute Id."));
		}
		if (conversationId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_CHANNEL_DETAILS, "Invalid channel Id."));
		}
		
		return discussionBoardDao.getThreadDetailsByConversationId(instituteId, conversationId);
	}

	public ThreadDetailsPayload getThreadDetailsByThreadId(int instituteId, UUID threadId) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute Id."));
		}
		if (threadId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_CHANNEL_DETAILS, "Invalid channel Id."));
		}
		
		return discussionBoardDao.getThreadDetailsByThreadId(instituteId, threadId);
	}
}
