package com.embrate.cloud.core.lib.homework;

import com.embrate.cloud.core.api.homework.HomeworkDetails;
import com.embrate.cloud.core.api.homework.HomeworkReportType;
import com.embrate.cloud.core.api.homework.HomeworkStatus;
import com.embrate.cloud.core.api.homework.HomeworkType;
import com.lernen.cloud.core.api.common.StringHelper;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.report.*;
import com.lernen.cloud.core.lib.constants.ReportHeaderAttribute;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.lib.reports.ReportGenerator;
import com.lernen.cloud.core.utils.DateUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

/**
 * <AUTHOR>
 */
public class HomeworkReportGenerator extends ReportGenerator {
    private static final Logger logger = LogManager.getLogger(HomeworkReportGenerator.class);

    private static final Map<String, List<ReportHeaderAttribute>> REPORT_HEADERS = new HashMap<>();

    private static final String[] HOMEWORK_CONSOLIDATED_REPORT = {"S.No.", "Status", "Date", "Class", "Section", "Subjects", "Given By", "Type", "Chapter",
            "Due Date", "Title", "Description", "Attachments"};

    private final UserPermissionManager userPermissionManager;
    private final HomeworkManager homeworkManager;
    private final InstituteManager instituteManager;

    public HomeworkReportGenerator(UserPermissionManager userPermissionManager, HomeworkManager homeworkManager, InstituteManager instituteManager) {
        this.userPermissionManager = userPermissionManager;
        this.homeworkManager = homeworkManager;
        this.instituteManager = instituteManager;
    }

    public ReportDetails generateReport(int instituteId, int academicSessionId, UUID standardId,
                                        String sectionIdStr, String courseIdStr, DownloadFormat downloadFormat, UUID userId,
                                        HomeworkReportType homeworkReportType, Integer startDate, Integer endDate, String homeworkStatusStr, String homeworkTypeStr) {

        final Set<Integer> sectionIds = convertStrToIntegerSet(sectionIdStr);
        Set<UUID> courseIdSet = convertStrToUUIDSet(courseIdStr);
        Set<HomeworkStatus> homeworkStatusSet = HomeworkStatus.getHomeworkStatusSetFromStr(homeworkStatusStr);
        Set<HomeworkType> homeworkTypeSet = HomeworkType.getHomeworkTypeSetFromStr(homeworkTypeStr);

        if (instituteId <= 0) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE,
                    "Invalid Institute Id"));
        }

        if (userId == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Invalid User Id"));
        }

        if (downloadFormat == DownloadFormat.EXCEL) {
            if (!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.HOMEWORK_EXCEL_REPORTS, false)) {
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
                        "You don't have access to download course reports in excel!"));
            }
        } else if (downloadFormat == DownloadFormat.PDF) {
            if (!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.HOMEWORK_PDF_REPORTS, false)) {
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
                        "You don't have access to download course reports in pdf!"));
            }
        }

        if (academicSessionId <= 0) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE,
                    "Invalid AcademicSession Id"));
        }

        if (homeworkReportType == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Invalid Report Type"));
        }

        Institute institute = instituteManager.getInstitute(instituteId);
        switch (homeworkReportType) {
            case HOMEWORK_CONSOLIDATED_REPORT:
                userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.GENERATE_HOMEWORK_CONSOLIDATED_REPORT);
                return generateHomeworkConsolidatedReport(institute, academicSessionId, standardId, sectionIds, courseIdSet, startDate, endDate, userId, homeworkStatusSet, homeworkTypeSet);
            default:
                return null;
        }
    }

    public ReportDetails generateHomeworkConsolidatedReport(Institute institute, int academicSessionId, UUID standardId, Set<Integer> sectionIdSet,
                                                            Set<UUID> courseIdSet, Integer startDate, Integer endDate, UUID userId, Set<HomeworkStatus> homeworkStatusSet, Set<HomeworkType> homeworkTypeSet) {

        String reportName = "HW/CW Consolidated Report";
        String sheetName = "HWCWConsolidatedReport";
        int instituteId = institute.getInstituteId();

        try {
            AcademicSession academicSession = instituteManager.getAcademicSession(instituteId, academicSessionId);
            String academicSessionDisplayName = academicSession.getShortYearDisplayName();

            // Title row with heading
            String heading = "";
            if (!StringUtils.isBlank(academicSessionDisplayName))
                heading += " | Session : " + academicSessionDisplayName;
            heading += (!startDate.equals(endDate)) ? " | From : " + DateUtils.getFormattedDate(startDate) + " To : " + DateUtils.getFormattedDate(endDate) : " | Date : " + DateUtils.getFormattedDate(startDate);

            String fullHeading = "HW/CW Consolidated Report" + heading;

            List<List<ReportCellDetails>> reportCellDetails = new ArrayList<>();
            List<List<ReportCellDetails>> headerReportCellDetails = new ArrayList<>();
            List<CellIndexes> mergeCellIndexesList = new ArrayList<>();
            List<CellIndexes> headerMergeCellIndexesList = new ArrayList<CellIndexes>();

            // Title row
            List<ReportCellDetails> titleRow = new ArrayList<>();
            titleRow.add(new ReportCellDetails(fullHeading, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true,
                    ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
            headerReportCellDetails.add(titleRow);

            // Merge title row across all columns (this was missing!)
            headerMergeCellIndexesList.add(new CellIndexes(0, 0, 0, HOMEWORK_CONSOLIDATED_REPORT.length - 1));

            int totalColumns = 0;
            List<ReportCellDetails> reportCellDetailsHeaderRow = new ArrayList<>();
            for (String s : HOMEWORK_CONSOLIDATED_REPORT) {
                reportCellDetailsHeaderRow.add(new ReportCellDetails(s, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
                totalColumns++;
            }
            headerReportCellDetails.add(reportCellDetailsHeaderRow);

            List<HomeworkDetails> homeworkDetailsList = homeworkManager.getHomeworkDetailsByFilters(
                    instituteId, academicSessionId, standardId, sectionIdSet, startDate, endDate, courseIdSet, null, homeworkTypeSet, userId);

            if (CollectionUtils.isEmpty(homeworkDetailsList)) {
                List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
                ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, null,
                        null);
                reportSheetDetailsList.add(reportSheetDetails);
                return new ReportDetails(reportName, reportSheetDetailsList);
            }

            // Sort using the Comparable implementation
            Collections.sort(homeworkDetailsList);

            int index = 1;
            for (HomeworkDetails homework : homeworkDetailsList) {
                if (homework == null) continue;

                if (!CollectionUtils.isEmpty(homeworkStatusSet) && !homeworkStatusSet.contains(homework.getHomeworkStatus())) {
                    continue;
                }

                Set<Integer> sectionIds = homework.getSectionIdList();
                String sectionStr = StandardSections.getSectionsNameStr(sectionIds, homework.getStandard().getStandardSectionList());

                List<ReportCellDetails> row = Arrays.asList(
                        new ReportCellDetails(index++, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false),
                        new ReportCellDetails(homework.getHomeworkStatus() == null ? "" : StringHelper.replaceUnderscoreWithHypen(homework.getHomeworkStatus().name()), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false),
                        homework.getHomeworkStatus() == HomeworkStatus.SAVED ?
                                new ReportCellDetails(homework.getCreatedTimestamp() == null ? EMPTY_TEXT : DateUtils.getFormattedDate(homework.getCreatedTimestamp()), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false) :
                                new ReportCellDetails(homework.getBroadcastedTimestamp() == null ? EMPTY_TEXT : DateUtils.getFormattedDate(homework.getBroadcastedTimestamp()), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false),
                        new ReportCellDetails(homework.getStandard().getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false),
                        new ReportCellDetails(sectionStr, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false),
                        new ReportCellDetails(homework.getCourse() == null ? "" : homework.getCourse().getCourseName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false),
                        new ReportCellDetails(homework.getFacultyName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false),
                        new ReportCellDetails(homework.getHomeworkType() == null ? "" : StringHelper.replaceUnderscoreWithHypen(homework.getHomeworkType().name()), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false),
                        new ReportCellDetails(homework.getChapter(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE),
                        new ReportCellDetails(homework.getDueDate() == null ? EMPTY_TEXT : DateUtils.getFormattedDate(homework.getDueDate()), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false),
                        new ReportCellDetails(homework.getTitle(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE),
                        new ReportCellDetails(homework.getDescription(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE),
                        new ReportCellDetails(!CollectionUtils.isEmpty(homework.getHomeworkAttachments()) ? homework.getHomeworkAttachments().size() : 0, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false)
                );
                reportCellDetails.add(row);
            }

            ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, totalColumns, headerMergeCellIndexesList, mergeCellIndexesList,
                    headerReportCellDetails, reportCellDetails, true, true, institute, totalColumns
            );

            return new ReportDetails(reportName, Collections.singletonList(reportSheetDetails));


        } catch (Exception e) {
            logger.error("Error while generating {} report for instituteId {}", reportName, instituteId, e);
            return null;
        }
    }
}

