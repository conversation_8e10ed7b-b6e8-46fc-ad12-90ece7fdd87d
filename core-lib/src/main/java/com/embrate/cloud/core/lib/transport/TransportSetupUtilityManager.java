package com.embrate.cloud.core.lib.transport;

import com.embrate.cloud.core.lib.fees.config.FeeSetupUtilityManager;
import com.lernen.cloud.core.api.CloneStatus;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.transport.*;
import com.lernen.cloud.core.api.transport.setup.CloneAreaStoppagesRequest;
import com.lernen.cloud.core.api.transport.setup.CloneAreaStoppagesResponse;
import com.lernen.cloud.core.lib.transport.configuration.TransportAssignmentManager;
import com.lernen.cloud.core.lib.transport.configuration.TransportConfigurationManager;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;

import static com.embrate.cloud.core.utils.ESetUtils.covertToLowerCase;

/**
 * <AUTHOR>
 * @created_at 19/04/24 : 11:01
 **/
public class TransportSetupUtilityManager {
    private static final Logger logger = LogManager.getLogger(FeeSetupUtilityManager.class);

    private final TransportConfigurationManager transportConfigurationManager;
    private final TransactionTemplate transactionTemplate;
    private final TransportAssignmentManager transportAssignmentManager;

    public TransportSetupUtilityManager(TransportConfigurationManager transportConfigurationManager,
                                        TransactionTemplate transactionTemplate, TransportAssignmentManager transportAssignmentManager) {
        this.transportConfigurationManager = transportConfigurationManager;
        this.transactionTemplate = transactionTemplate;
        this.transportAssignmentManager = transportAssignmentManager;
    }

    public boolean cloneTransportAreaVehicleAmountsAcrossSession(int srcInstituteId, int destInstituteId, int srcAcademicSessionId, int destAcademicSessionId) {
        if (srcInstituteId <= 0 || destInstituteId <= 0 || srcAcademicSessionId <= 0 || destAcademicSessionId <= 0) {
            logger.error("Invalid srcInstituteId {}, destInstituteId {}, srcAcademicSessionId {}, destAcademicSessionId {}", srcInstituteId, destInstituteId, srcAcademicSessionId, destAcademicSessionId);
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid request"));
        }
        List<TransportAreaDetails> srcTransportAreaAmountList = transportConfigurationManager.getTransportAreaDetailsWithAmount(srcInstituteId,
                srcAcademicSessionId);

        if (CollectionUtils.isEmpty(srcTransportAreaAmountList)) {
            logger.error("No areas amount found in srcInstituteId {} & src session {}", srcInstituteId, srcAcademicSessionId);
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "No service routes found in institute & src session"));
        }

        if(srcInstituteId != destInstituteId) {
            List<TransportAreaDetails> destTransportAreaAmountList = cloneSrcTransportAmountInDestTransportAmount(destInstituteId,srcTransportAreaAmountList);
            srcTransportAreaAmountList = new ArrayList<>(destTransportAreaAmountList);
        }

        List<TransportAreaAmountDetailsPayload> transportAreaAmountDetailsPayloadList = new ArrayList<>();
        for (TransportAreaDetails transportAreaDetails : srcTransportAreaAmountList) {
            int areaId = transportAreaDetails.getArea().getAreaId();
            List<TransportVehicleAmount> transportVehicleAmountsList = transportAreaDetails.getTransportVehicleAmounts();
            if (areaId <= 0 || CollectionUtils.isEmpty(transportVehicleAmountsList)) {
                continue;
            }
            Map<Integer, TransportVehicleAmountPayload> transportVehicleAmountMap = new HashMap<>();
            for (TransportVehicleAmount transportVehicleAmount : transportAreaDetails.getTransportVehicleAmounts()) {
                if (!transportVehicleAmountMap.containsKey(transportVehicleAmount.getVehicleType().getVehicleTypeId())) {
                    transportVehicleAmountMap.put(transportVehicleAmount.getVehicleType().getVehicleTypeId(),
                            new TransportVehicleAmountPayload(transportVehicleAmount.getVehicleType().getVehicleTypeId(), transportVehicleAmount.getAmount()));
                }
            }
            if (transportVehicleAmountMap != null && CollectionUtils.isNotEmpty(transportVehicleAmountMap.entrySet())) {
                TransportAreaAmountDetailsPayload destStructure = new TransportAreaAmountDetailsPayload(destAcademicSessionId, transportAreaDetails.getArea().getAreaId(),
                        new ArrayList<>(transportVehicleAmountMap.values()));
                transportAreaAmountDetailsPayloadList.add(destStructure);
            }
        }

        if (CollectionUtils.isEmpty(transportAreaAmountDetailsPayloadList)) {
            logger.error("No new areas amounts found to setup from src {} to dest session {}. So not able to update area amounts.", srcAcademicSessionId, destAcademicSessionId);
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "No new areas amounts found to setup from src to dest session. So not able to update area amounts."));
        }

        return transportConfigurationManager.bulkUpdateTransportAreaAmount(destInstituteId, transportAreaAmountDetailsPayloadList, null, true);
    }

    private List<TransportAreaDetails> cloneSrcTransportAmountInDestTransportAmount (int destInstituteId, List<TransportAreaDetails> srcTransportAreaAmountList) {
        List<TransportAreaDetails> destTransportAreaAmountList = new ArrayList<>();
        Map<String, TransportArea> destTransportAreaMap = transportConfigurationManager.getTransportAreaMap(destInstituteId);
        if (MapUtils.isEmpty(destTransportAreaMap)) {
            logger.error("No areas found in destInstituteId {} ", destInstituteId);
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "No areas found in destInstituteId"));
        }
        Map<String, VehicleType> destVehicleTypeMap = transportConfigurationManager.getVehicleTypeMap(destInstituteId);
        if (MapUtils.isEmpty(destVehicleTypeMap)) {
            logger.error("No vehicle type found in destInstituteId {} ", destInstituteId);
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "No vehicle type found in destInstituteId"));
        }

        for (TransportAreaDetails transportAreaDetails : srcTransportAreaAmountList) {
            int areaId = transportAreaDetails.getArea().getAreaId();
            List<TransportVehicleAmount> transportVehicleAmountsList = transportAreaDetails.getTransportVehicleAmounts();
            if (areaId <= 0 || CollectionUtils.isEmpty(transportVehicleAmountsList)) {
                continue;
            }
            TransportArea transportArea = destTransportAreaMap.get(transportAreaDetails.getArea().getArea().toLowerCase());
            if (transportArea == null) {
                logger.error("Destination institute don't have transport area same as srcInstitute {} ", destInstituteId);
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Destination institute don't have transport area same as srcInstitute."));
            }
            transportAreaDetails.getArea().setAreaId(transportArea.getAreaId());
            List<TransportVehicleAmount> transportVehicleAmountList = new ArrayList<>();
            for (TransportVehicleAmount transportVehicleAmount : transportAreaDetails.getTransportVehicleAmounts()) {
                VehicleType vehicleTypeName = destVehicleTypeMap.get(transportVehicleAmount.getVehicleType().getVehicleTypeName().toLowerCase());
                if (vehicleTypeName == null) {
                    logger.error("Destination institute don't have same vehicle type as in srcInstitute {} ", destInstituteId);
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Destination institute don't have same vehicle type as in srcInstitute."));
                }
                transportVehicleAmountList.add(new TransportVehicleAmount(vehicleTypeName, transportVehicleAmount.getAmount()));
            }
            destTransportAreaAmountList.add(new TransportAreaDetails(transportArea, transportVehicleAmountList));
        }
        return destTransportAreaAmountList;
    }

    public boolean cloneTransportRoutesAcrossSession(CloneTransportServiceRoutes cloneTransportServiceRoutes) {
        if (cloneTransportServiceRoutes.getSrcInstituteId() <= 0 || cloneTransportServiceRoutes.getDestInstituteId() <= 0 || cloneTransportServiceRoutes.getDestAcademicSessionId() <= 0 || cloneTransportServiceRoutes.getSrcAcademicSessionId() <= 0 ) {
            logger.error("Invalid srcInstitute {}, srcAcademicSessionId {}, destInstituteId {}, destAcademicSessionId {}", cloneTransportServiceRoutes.getSrcInstituteId(), cloneTransportServiceRoutes.getSrcAcademicSessionId(), cloneTransportServiceRoutes.getDestInstituteId(), cloneTransportServiceRoutes.getDestAcademicSessionId());
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid request"));
        }

        List<Vehicle> vehicleList = transportConfigurationManager.getVehicles(cloneTransportServiceRoutes.getDestInstituteId());
        if (CollectionUtils.isEmpty(vehicleList)) {
            logger.error("No vehicles found in dest institute {}", cloneTransportServiceRoutes.getDestInstituteId());
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "No vehicles found in dest institute"));
        }

        final List<StoppageStudentRouteDetails> transportAssignedStudentsList = transportAssignmentManager.getStoppageStudentRouteDetails(cloneTransportServiceRoutes.getDestInstituteId(), cloneTransportServiceRoutes.getDestAcademicSessionId());
        if (CollectionUtils.isEmpty(transportAssignedStudentsList)) {
            logger.error("No stoppages found in dest institute {}", cloneTransportServiceRoutes.getDestInstituteId());
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "No stopagges found in dest institute"));
        }

        final List<TransportServiceRoute> scrAcademicSessionTransportServiceRouteList = transportConfigurationManager.getTransportServiceRoutes(cloneTransportServiceRoutes.getSrcInstituteId(), cloneTransportServiceRoutes.getSrcAcademicSessionId());
        if (CollectionUtils.isEmpty(scrAcademicSessionTransportServiceRouteList)) {
            logger.error("No service routes found in src institute {} & src session {}", cloneTransportServiceRoutes.getSrcInstituteId(), cloneTransportServiceRoutes.getSrcAcademicSessionId());
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "No service routes found in institute & src session"));
        }

        final List<TransportServiceRoute> destAcademicSessionTransportServiceRouteList = transportConfigurationManager.getTransportServiceRoutes(cloneTransportServiceRoutes.getDestInstituteId(), cloneTransportServiceRoutes.getDestAcademicSessionId());
        Set<String> cloneServiceNameSet = covertToLowerCase(cloneTransportServiceRoutes.getServiceNameSet());
        cloneTransportServiceRoutes.setServiceNameSet(cloneServiceNameSet);

        Map<String, Vehicle> vehicleCodeMap = new HashMap<>();
        for (Vehicle vehicle : vehicleList) {
            if (!vehicleCodeMap.containsKey(vehicle.getVehicleCode().toLowerCase())) {
                vehicleCodeMap.put(vehicle.getVehicleCode().toLowerCase(), vehicle);
            }
        }

        Map<String, Integer> stoppagesNameIdMap = new HashMap<>();
        for(StoppageStudentRouteDetails stoppageStudentRouteDetails : transportAssignedStudentsList){
            String stoppage = stoppageStudentRouteDetails.getTransportAreaDetail().getTransportArea().getArea().toLowerCase();
            if(!stoppagesNameIdMap.containsKey(stoppage)){
                stoppagesNameIdMap.put(stoppage, stoppageStudentRouteDetails.getTransportAreaDetail().getTransportArea().getAreaId());
            }
        }

        Map<RouteType, Set<String>> routeTypeNameMap = new HashMap<>();
        for(TransportServiceRoute transportServiceRoute : destAcademicSessionTransportServiceRouteList) {
            RouteType routeType = transportServiceRoute.getTransportServiceRouteMetadata().getRouteType();
            String serviceRouteName = transportServiceRoute.getTransportServiceRouteMetadata().getServiceRouteName().toLowerCase();

            if(routeType == null || StringUtils.isBlank(serviceRouteName)) {
                continue;
            }
            if(!routeTypeNameMap.containsKey(routeType)) {
                routeTypeNameMap.put(routeType, new HashSet<>());
            }
            routeTypeNameMap.get(routeType).add(serviceRouteName);
        }
        List<TransportServiceRoutePayload> transportServiceRoutePayloadList = new ArrayList<>();
        for (TransportServiceRoute transportServiceRoute : scrAcademicSessionTransportServiceRouteList) {
            RouteType routeType = transportServiceRoute.getTransportServiceRouteMetadata().getRouteType();
            String serviceRouteName = transportServiceRoute.getTransportServiceRouteMetadata().getServiceRouteName().toLowerCase();
            String vehicleCode = transportServiceRoute.getTransportServiceRouteMetadata().getVehicle().getVehicleCode();
            if(routeType == null || StringUtils.isBlank(serviceRouteName)) {
                continue;
            }
            /**
             * filtering our service routes which are already created in dest session
             */
            if(!CollectionUtils.isEmpty(routeTypeNameMap.get(routeType))
                    && routeTypeNameMap.get(routeType).contains(serviceRouteName)) {
                continue;
            }

            if(!CollectionUtils.isEmpty(cloneTransportServiceRoutes.getServiceNameSet()) && !cloneTransportServiceRoutes.getServiceNameSet().contains(serviceRouteName)){
                continue;
            }
            if(!CollectionUtils.isEmpty(cloneTransportServiceRoutes.getRouteTypeSet()) && !cloneTransportServiceRoutes.getRouteTypeSet().contains(routeType)){
                continue;
            }
            if(!vehicleCodeMap.containsKey(vehicleCode.toLowerCase())){
                continue;
            }
            Vehicle vehicle = vehicleCodeMap.get(vehicleCode.toLowerCase());


            Map<Integer, TransportServiceRouteStoppagesPayload> transportServiceRouteStoppagesPayloadList = new HashMap<>();
            for (TransportServiceRouteStoppageDetails transportServiceRouteStoppageDetails : transportServiceRoute.getStoppagesList()) {
                if(!stoppagesNameIdMap.containsKey(transportServiceRouteStoppageDetails.getTransportArea().getArea().toLowerCase())){
                    continue;
                }
                Integer areaId = stoppagesNameIdMap.get(transportServiceRouteStoppageDetails.getTransportArea().getArea().toLowerCase());
                if (!transportServiceRouteStoppagesPayloadList.containsKey(transportServiceRouteStoppageDetails.getTransportArea().getAreaId())) {
                    transportServiceRouteStoppagesPayloadList.put(areaId, (new TransportServiceRouteStoppagesPayload(areaId, transportServiceRouteStoppageDetails.getTime())));
                }
            }
            if (transportServiceRouteStoppagesPayloadList != null && CollectionUtils.isNotEmpty(transportServiceRouteStoppagesPayloadList.entrySet())) {
                TransportServiceRoutePayload destStructure = new TransportServiceRoutePayload(transportServiceRoute.getTransportServiceRouteMetadata().getInstituteId(), cloneTransportServiceRoutes.getDestAcademicSessionId(), transportServiceRoute.getTransportServiceRouteMetadata().getServiceRouteId(), transportServiceRoute.getTransportServiceRouteMetadata().getServiceRouteName(), vehicle.getVehicleId(), transportServiceRoute.getTransportServiceRouteMetadata().getRouteType(), transportServiceRoute.getDefaultDriver(), transportServiceRoute.getDefaultConductor(), new ArrayList<>(transportServiceRouteStoppagesPayloadList.values()));
                transportServiceRoutePayloadList.add(destStructure);
            }
        }

        if (CollectionUtils.isEmpty(transportServiceRoutePayloadList)) {
            logger.error("No new service routes found to setup from src {} to dest session {}. So not able to create service routes in dest session.", cloneTransportServiceRoutes.getSrcAcademicSessionId(), cloneTransportServiceRoutes.getDestInstituteId());
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "No new service routes found to setup from src to dest session. So not able to create service routes in dest session."));
        }

        return transportConfigurationManager.bulkAddTransportServiceRoute(cloneTransportServiceRoutes.getDestInstituteId(), cloneTransportServiceRoutes.getDestAcademicSessionId(), transportServiceRoutePayloadList, null, true);

    }


    public CloneAreaStoppagesResponse cloneManageStoppages(CloneAreaStoppagesRequest cloneManageStoppagesRequest) {
        int srcInstituteId = cloneManageStoppagesRequest.getSrcInstituteId();
        int destInstituteId = cloneManageStoppagesRequest.getDestInstituteId();

        if (srcInstituteId <= 0 || destInstituteId <= 0) {
            logger.error("Invalid srcInstituteId {} or destInstituteId {}", srcInstituteId, destInstituteId);
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid src or dest institute id"));
        }

        // Fetch required data BEFORE starting the transaction
        List<TransportArea> srcTransportAreas = transportConfigurationManager.getTransportAreas(srcInstituteId);
        if (org.springframework.util.CollectionUtils.isEmpty(srcTransportAreas)) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Src Institute doesn't have any stoppages"));
        }

        List<TransportParentArea> srcTransportParentAreas = transportConfigurationManager.getTransportParentAreas(srcInstituteId);
        List<TransportParentArea> destTransportParentAreas = transportConfigurationManager.getTransportParentAreas(destInstituteId);
        List<TransportArea> destTransportAreas = transportConfigurationManager.getTransportAreas(destInstituteId);

        List<String> existingTransportArea = new ArrayList<>();
        List<String> existingStoppages = new ArrayList<>();
        List<TransportParentArea> uniqueSrcTransportParentAreas = new ArrayList<>();
        List<TransportArea> uniqueSrcTransportAreas = new ArrayList<>();

        // Convert destination parent areas into a Set for quick lookup
        Set<String> destParentNamesSet = new HashSet<>();
        for (TransportParentArea dest : destTransportParentAreas) {
            destParentNamesSet.add(dest.getParentName().toLowerCase());
        }


        for (TransportParentArea src : srcTransportParentAreas) {
            if (destParentNamesSet.contains(src.getParentName().toLowerCase())) {
                existingTransportArea.add(src.getParentName());
                continue;
            }
            src.setInstituteId(destInstituteId);
            uniqueSrcTransportParentAreas.add(src);
        }


        Set<String> destTransportAreaSet = new HashSet<>();
        for (TransportArea dest : destTransportAreas) {
            destTransportAreaSet.add(dest.getArea().toLowerCase());
        }

        for (TransportArea srcTransportArea : srcTransportAreas) {
            if (destTransportAreaSet.contains(srcTransportArea.getArea().toLowerCase())) {
                existingStoppages.add(srcTransportArea.getArea());
                continue;
            }
            srcTransportArea.setInstituteId(destInstituteId);
            uniqueSrcTransportAreas.add(srcTransportArea);
        }

        // Perform transactional operations
        return transactionTemplate.execute(new TransactionCallback<CloneAreaStoppagesResponse>() {
            @Override
            public CloneAreaStoppagesResponse doInTransaction(TransactionStatus status) {
                Map<UUID, UUID> parentIdMap = transportConfigurationManager.addBulkTransportParentArea(uniqueSrcTransportParentAreas);
                if(cloneManageStoppagesRequest.isSkipStoppageClone()){
                    return new CloneAreaStoppagesResponse(
                            CloneStatus.COMPLETE_SUCCESS,
                            cloneManageStoppagesRequest,
                            existingTransportArea,
                            existingStoppages,
                            "All areas were Cloned successfully."
                    );
                }
                if (org.springframework.util.CollectionUtils.isEmpty(uniqueSrcTransportAreas)) {
                    return new CloneAreaStoppagesResponse(
                            CloneStatus.PARTIAL_SUCCESS,
                            cloneManageStoppagesRequest,
                            existingTransportArea,
                            existingStoppages,
                            "Areas ingested Successfully. No new stoppages found to ingest, skipping stoppages."
                    );
                }

                for (TransportArea srcTransportArea : uniqueSrcTransportAreas) {
                    UUID newParentId = srcTransportArea.getParentId() == null ? null : parentIdMap.get(srcTransportArea.getParentId());
                    srcTransportArea.setParentId(newParentId);
                }

                boolean transportAreaStatus = transportConfigurationManager.addBulkTransportArea(uniqueSrcTransportAreas);
                if (!transportAreaStatus) {
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Failed to insert stoppages"));
                }

                return new CloneAreaStoppagesResponse(
                        CloneStatus.COMPLETE_SUCCESS,
                        cloneManageStoppagesRequest,
                        existingTransportArea,
                        existingStoppages,
                        "All stoppages and areas were Cloned successfully."
                );
            }
        });
    }
}
