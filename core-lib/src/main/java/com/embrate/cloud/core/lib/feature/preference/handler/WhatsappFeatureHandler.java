package com.embrate.cloud.core.lib.feature.preference.handler;

import com.embrate.cloud.core.api.feature.preference.FeaturePreferenceEntity;
import com.embrate.cloud.core.api.feature.preference.PreferenceDataType;
import com.embrate.cloud.core.api.feature.preference.ProductFeature;
import com.embrate.cloud.core.api.whatsapp.WhatsappPreferences;
import com.embrate.cloud.core.lib.feature.preference.IFeaturePreferenceGroupBuilder;
import com.lernen.cloud.core.api.common.CommunicationServiceProvider;
import com.lernen.cloud.core.api.common.NameFormat;
import com.lernen.cloud.core.api.configurations.MetaDataPreferences;
import com.lernen.cloud.core.api.sms.SMSPreferences;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.utils.feature.preference.ProductFeatureUtils;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 */

public class WhatsappFeatureHandler extends ProductFeatureHandler {

    private final InstituteManager instituteManager;

    public WhatsappFeatureHandler(InstituteManager instituteManager) {
        this.instituteManager = instituteManager;
    }

    private ProductFeature productFeature = null;

    @Override
    public ProductFeature buildProductFeature() {
        productFeature = new ProductFeature("com.embrate.feature.whatsapp", "Whatsapp Preferences", "Whatsapp Preferences");
        addFeaturePreferenceGroup(productFeature, getBasicPreferences());
        return productFeature;
    }

    @Override
    public ProductFeature getProductFeature() {
        return productFeature;
    }

    private IFeaturePreferenceGroupBuilder getBasicPreferences() {
        return new IFeaturePreferenceGroupBuilder() {
            @Override
            public String getGroupId() {
                return "group.preference.basic";
            }

            @Override
            public String getGroupName() {
                return "Basic Preferences";
            }

            @Override
            public String getGroupDescription() {
                return "Basic Preferences";
            }

            @Override
            public List<FeaturePreferenceEntity> getPreferences() {
                List<FeaturePreferenceEntity> featurePreferenceEntities = new LinkedList<>();

                featurePreferenceEntities.add(new FeaturePreferenceEntity("whatsapp_service_enabled", "Whatsapp Service Enabled", "Enable this flag to send whatsapp messages.", PreferenceDataType.BOOLEAN, WhatsappPreferences.getConfigType(), WhatsappPreferences.WHATSAPP_SERVICE_ENABLED));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("whatsapp_provider", "Whatsapp Provider", "Select the service provider of whatsapp.", PreferenceDataType.ENUM, WhatsappPreferences.getConfigType(), WhatsappPreferences.WHATSAPP_PROVIDER,  ProductFeatureUtils.getEnumValues(CommunicationServiceProvider.values())));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("buffer_whatsapp_count", "Buffer Whatsapp Count", "Give a buffer count for sending whatsapp messages.", PreferenceDataType.INT, WhatsappPreferences.getConfigType(), WhatsappPreferences.BUFFER_WHATSAPP_COUNT));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("whatsapp_api_key", "Whatsapp API Key", "Give Whatsapp API Key for institute.", PreferenceDataType.STRING, WhatsappPreferences.getConfigType(), WhatsappPreferences.WHATSAPP_API_KEY));
                featurePreferenceEntities.add(new FeaturePreferenceEntity("whatsapp_channel_id", "Whatsapp Channel Id", "Give Whatsapp Channel Id for institute.", PreferenceDataType.STRING, WhatsappPreferences.getConfigType(), WhatsappPreferences.WHATSAPP_CHANNEL_ID));

                return featurePreferenceEntities;
            }
        };
    }
}
