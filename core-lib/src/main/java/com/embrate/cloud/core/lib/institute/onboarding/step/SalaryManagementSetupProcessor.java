package com.embrate.cloud.core.lib.institute.onboarding.step;

import com.embrate.cloud.core.api.onboarding.institute.setup.InstituteSetupPayload;
import com.embrate.cloud.core.api.salary.PayHeadConfiguration;
import com.embrate.cloud.core.api.salary.PayHeadType;
import com.embrate.cloud.core.api.salary.v2.PayHeadTag;
import com.embrate.cloud.core.lib.institute.onboarding.IConfigureInstituteStep;
import com.embrate.cloud.core.lib.salary.SalaryConfigurationManager;
import com.lernen.cloud.core.api.user.Module;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */

public class SalaryManagementSetupProcessor implements IModuleSetupProcessor {

	private static final Logger logger = LogManager.getLogger(SalaryManagementSetupProcessor.class);
	private final SalaryConfigurationManager salaryConfigurationManager;

	public SalaryManagementSetupProcessor(SalaryConfigurationManager salaryConfigurationManager) {
		this.salaryConfigurationManager = salaryConfigurationManager;

	}

	@Override
	public Module getModule() {
		return Module.SALARY_MANAGEMENT;
	}

	@Override
	public List<IConfigureInstituteStep> getSteps(InstituteSetupPayload instituteSetupPayload) {
		int instituteId = instituteSetupPayload.getInstituteBasicSetupResult().getInstituteId();
		List<IConfigureInstituteStep> instituteSetupStepsList = new ArrayList<>();
		instituteSetupStepsList.add(getSalaryManagementPayHeadStep(instituteId));
		return instituteSetupStepsList;
	}

	private IConfigureInstituteStep getSalaryManagementPayHeadStep(int instituteId) {
		return new IConfigureInstituteStep() {
			@Override
			public String getName() {
				return "Salary Pay Head";
			}

			@Override
			public boolean execute() {
				List<PayHeadConfiguration> payHeadConfigurationList = new ArrayList<>();
				payHeadConfigurationList.add(new PayHeadConfiguration(0, "ESIC", PayHeadType.DEDUCTION, true, PayHeadTag.ESIC, false, false, true, null));
				payHeadConfigurationList.add(new PayHeadConfiguration(0, "EPF", PayHeadType.DEDUCTION, true, PayHeadTag.EPF, false, false, true, null));
				return salaryConfigurationManager.addPayHeadConfigurations(instituteId, payHeadConfigurationList);
			}
		};
	}
}
