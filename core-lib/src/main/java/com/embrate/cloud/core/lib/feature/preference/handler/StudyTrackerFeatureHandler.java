package com.embrate.cloud.core.lib.feature.preference.handler;

import com.embrate.cloud.core.api.feature.preference.FeaturePreferenceEntity;
import com.embrate.cloud.core.api.feature.preference.PreferenceDataType;
import com.embrate.cloud.core.api.feature.preference.ProductFeature;
import com.embrate.cloud.core.lib.feature.preference.IFeaturePreferenceGroupBuilder;
import com.lernen.cloud.core.api.configurations.StudyTrackerPreferences;
import com.lernen.cloud.core.lib.institute.InstituteManager;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR> mittal
 */

public class StudyTrackerFeatureHandler extends ProductFeatureHandler {

    private final InstituteManager instituteManager;

    public StudyTrackerFeatureHandler(InstituteManager instituteManager) {
        this.instituteManager = instituteManager;
    }

    private ProductFeature studyTrackerProductFeature = null;

    @Override
    public ProductFeature buildProductFeature() {
        studyTrackerProductFeature = new ProductFeature("com.embrate.feature.studyTracker", "Study Tracker Preferences", "Study Tracker Preferences");
        addFeaturePreferenceGroup(studyTrackerProductFeature, getBasicPreferences());
        return studyTrackerProductFeature;
    }

    @Override
    public ProductFeature getProductFeature() {
        return studyTrackerProductFeature;
    }

    private IFeaturePreferenceGroupBuilder getBasicPreferences() {
        return new IFeaturePreferenceGroupBuilder() {
            @Override
            public String getGroupId() {
                return "group.preference.basic";
            }

            @Override
            public String getGroupName() {
                return "Basic Preferences";
            }

            @Override
            public String getGroupDescription() {
                return "Basic Preferences";
            }

            @Override
            public List<FeaturePreferenceEntity> getPreferences() {
                List<FeaturePreferenceEntity> featurePreferenceEntities = new LinkedList<>();
                 featurePreferenceEntities.add(new FeaturePreferenceEntity("study_tracker_entity_name", "Study Tracker Entity Name", "By default, if not set, the entity name will be 'COURSE'. You also have the option to set the entity name to 'OVERALL'. Only these two values are allowed for the entity name.", PreferenceDataType.STRING, StudyTrackerPreferences.getConfigType(), StudyTrackerPreferences.STUDY_TRACKER_ENTITY_NAME));
                return featurePreferenceEntities;
            }
        };
    }
}
