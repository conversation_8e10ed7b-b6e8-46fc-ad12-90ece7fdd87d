package com.embrate.cloud.core.lib.service.payment.gateway.atom;

import com.embrate.cloud.core.api.service.payment.gateway.PGProcessTransactionPayload;
import com.embrate.cloud.core.api.service.payment.gateway.PaymentGatewayTransactionStatus;
import com.embrate.cloud.core.api.service.payment.gateway.atom.AtomMerchantDetails;
import com.embrate.cloud.core.api.service.payment.gateway.atom.AtomPaymentWebhookResponse;
import com.embrate.cloud.core.api.service.payment.gateway.merchants.PGMerchantDetails;
import com.embrate.cloud.core.lib.service.payment.gateway.IPaymentGatewayTransactionHandler;
import com.lernen.cloud.core.utils.SharedConstants;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 */

public class AtomPaymentGatewayTransactionHandler implements IPaymentGatewayTransactionHandler {
    private static final Logger logger = LogManager.getLogger(AtomPaymentGatewayTransactionHandler.class);

    private static final String SUCCESS_STATUS = "Ok";
    private static final String FAILED_STATUS = "F";
    private static final String CANCELLED_STATUS = "C";

    private static final String SUCCESS_WEBHOOK_STATUS = "SUCCESS";
    private static final String FAILED_WEBHOOK_STATUS = "FAILED";
    private static final String CANCELLED_WEBHOOK_STATUS = "CANCELLED";


    private final PGProcessTransactionPayload processTransactionPayload;
    private final boolean webhook;
    private final AtomPaymentWebhookResponse webhookResponse;
    private final PGMerchantDetails merchantDetails;
    private final boolean invalidPayload;
    public AtomPaymentGatewayTransactionHandler(PGProcessTransactionPayload processTransactionPayload, PGMerchantDetails merchantDetails) {
        this.processTransactionPayload = processTransactionPayload;
        this.merchantDetails = merchantDetails;
        this.invalidPayload = isInvalidPayload(processTransactionPayload);
        AtomPaymentWebhookResponse webhookResponseData = null;
        if(invalidPayload){
            webhook = false;
        }else{
            webhook = processTransactionPayload.isWebhook();
            if(webhook){
                try {
                    webhookResponseData = SharedConstants.OBJECT_MAPPER.readValue(processTransactionPayload.getWebhookData(), AtomPaymentWebhookResponse.class);

//                    webhookResponseData = (AtomPaymentWebhookResponse) processTransactionPayload.getWebhookDataObject();
                }catch (Exception e){
                    e.printStackTrace();
                    logger.error("Error while parsing webhook data {}", processTransactionPayload.getWebhookData());
                }
            }
        }
        webhookResponse = webhookResponseData;
    }

    @Override
    public UUID getTransactionId() {
        if(invalidPayload){
            return null;
        }
        String transactionId;
        if(webhook){
            transactionId = webhookResponse.getMerchantTxnID();
        }else{
            transactionId = processTransactionPayload.getTransactionData().get("mer_txn");
        }
        return UUID.fromString(transactionId);
    }


    @Override
    public PaymentGatewayTransactionStatus getTransactionStatus() {
        if(invalidPayload){
            return null;
        }

        String txStatus;
        if(webhook){
            txStatus = webhookResponse.getVerified();
            if(StringUtils.isBlank(txStatus)){
                return null;
            }
            switch (txStatus.toUpperCase()) {
                case SUCCESS_WEBHOOK_STATUS:
                    return PaymentGatewayTransactionStatus.SUCCESS;
                case FAILED_WEBHOOK_STATUS:
                    return PaymentGatewayTransactionStatus.FAILED;
                case CANCELLED_WEBHOOK_STATUS:
                    return PaymentGatewayTransactionStatus.CANCELLED;
                default:
                    return PaymentGatewayTransactionStatus.UNKNOWN;
            }
        }else{
            txStatus = processTransactionPayload.getTransactionData().get("f_code");
            if(StringUtils.isBlank(txStatus)){
                return null;
            }
            switch (txStatus) {
                case SUCCESS_STATUS:
                    return PaymentGatewayTransactionStatus.SUCCESS;
                case FAILED_STATUS:
                    return PaymentGatewayTransactionStatus.FAILED;
                case CANCELLED_STATUS:
                    return PaymentGatewayTransactionStatus.CANCELLED;
                default:
                    return PaymentGatewayTransactionStatus.UNKNOWN;
            }
        }
    }

    @Override
    public boolean validTransactionSignature() {
        if(invalidPayload){
            return false;
        }

        if(webhook){
            //TODO:no signature in atom callback url
            return true;
//            String timestamp = processTransactionPayload.getWebhookHeaderTimestamp();
//            String signature = processTransactionPayload.getWebhookHeaderSignature();
//            String payload = processTransactionPayload.getWebhookData();
//
//            String signaturePayload = timestamp + payload;
////            String expectedSignature = getEncodedSignatureHash(signaturePayload);
//            //TODO:add correct statement here.
//            String expectedSignature = getSignature();
//            return signature.equals(expectedSignature);
        }else{
            logger.info("Transaction response from Atom {} ", processTransactionPayload.getTransactionData());
            String txStatus = processTransactionPayload.getTransactionData().get("f_code");
            /**
             * In cases of cancelled status no signature is given by atom.
             */
            if(CANCELLED_STATUS.equalsIgnoreCase(txStatus)){
                return true;
            }
            String signature = processTransactionPayload.getTransactionData().get("signature");
            if(StringUtils.isBlank(signature)){
                return false;
            }
            String expectedSignature = getSignature();
            logger.info("expectedSignature {} ", expectedSignature);
            logger.info("signature {} ", signature);
            return signature.equals(expectedSignature);
        }
    }

    @Override
    public Map<String, String> getTransactionData(){
        if(invalidPayload){
            return null;
        }
        return processTransactionPayload.getTransactionData();
    }

    @Override
    public boolean isWebhook() {
        return webhook;
    }

    @Override
    public String getWebhookData() {
        if(invalidPayload){
            return null;
        }
        return processTransactionPayload.getWebhookData();
    }

    private boolean isInvalidPayload(PGProcessTransactionPayload processTransactionPayload){
        if(processTransactionPayload == null){
            return true;
        }
        if(!processTransactionPayload.isWebhook()){
            return MapUtils.isEmpty(processTransactionPayload.getTransactionData());
        }

        return StringUtils.isBlank(processTransactionPayload.getWebhookData());
    }

    private String getSignature(){
        Map<String, String> transactionData = processTransactionPayload.getTransactionData();
        AtomMerchantDetails atomMerchantDetails = AtomMerchantDetails.getAtomMerchantDetails(merchantDetails.getMetadata());
        logger.info("transactionData {} ", transactionData);
        logger.info("atomMerchantDetails {} ", atomMerchantDetails);
        String mmp_txn = transactionData.get("mmp_txn");
        String mer_txn = transactionData.get("mer_txn");
        String f_code = transactionData.get("f_code");
        String prod = transactionData.get("prod");
        String discriminator = transactionData.get("discriminator");
        String amt = transactionData.get("amt");
        String bank_txn = transactionData.get("bank_txn");
        String respHashKey = atomMerchantDetails.getResponseHashKey();
        String signature_response = AtomGenerateSignature.getEncodedValueWithSha2(respHashKey,
                mmp_txn, mer_txn, f_code, prod, discriminator, amt, bank_txn);
        logger.info("signature_response {} ", signature_response);
        return signature_response;
    }
}
