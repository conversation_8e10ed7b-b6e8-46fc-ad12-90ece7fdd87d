package com.embrate.cloud.core.lib.inventory.v2;

import com.embrate.cloud.core.api.inventory.v2.*;
import com.embrate.cloud.core.api.inventory.v2.issue.ProductIssuePayload;
import com.embrate.cloud.core.api.inventory.v2.outlet.InventoryOutlet;
import com.embrate.cloud.core.api.inventory.v2.product.group.TradeProductGroupPayload;
import com.embrate.cloud.core.api.inventory.v2.sale.*;
import com.embrate.cloud.core.api.inventory.v2.supplier.InventorySupplier;
import com.embrate.cloud.core.api.inventory.v2.transaction.InventoryTransactionMetadata;
import com.embrate.cloud.core.api.inventory.v2.transaction.InventoryTransactionSummary;
import com.embrate.cloud.core.api.wallet.WalletPreferences;
import com.embrate.cloud.core.utils.EMapUtils;
import com.embrate.cloud.dao.tier.inventory.v2.InventoryOutletDao;
import com.embrate.cloud.dao.tier.inventory.v2.InventoryTransactionsDao;
import com.lernen.cloud.core.api.common.TransactionMode;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.inventory.*;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentStatus;
import com.lernen.cloud.core.api.student.TaggedActions;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.inventory.ProductGroupManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.lib.staff.StaffManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.NumberUtils;
import com.lernen.cloud.core.utils.RestrictionUtils;
import com.lernen.cloud.core.utils.SharedConstants;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;

import static com.embrate.cloud.core.api.inventory.v2.sale.ExchangeOrderMetadata.EXCHANGE_ORDER_DATA;
import static com.lernen.cloud.core.api.common.TransactionMode.CASH;
import static com.lernen.cloud.core.api.common.TransactionMode.WALLET;
import static com.lernen.cloud.core.api.inventory.InventoryTransactionType.SALE;
import static com.lernen.cloud.core.api.inventory.InventoryTransactionType.SALES_RETURN;

/**
 * <AUTHOR>
 */
public class InventoryTransactionsManager {

	public static final long PAST_TRANSACTION_DAYS_IN_SECONDS = 365 * 24 * 3600;
	private static final Logger logger = LogManager.getLogger(InventoryTransactionsManager.class);
	private final InventoryTransactionsDao inventoryTransactionsDao;
	private final InventoryOutletDao inventoryOutletDao;
	private final ProductGroupManager productGroupManager;
	private final StudentManager studentManager;
	private final StaffManager staffManager;
	private final UserPreferenceSettings userPreferenceSettings;
	private final TransactionTemplate transactionTemplate;
	private final UserPermissionManager userPermissionManager;

	public InventoryTransactionsManager(InventoryTransactionsDao inventoryTransactionsDao,
										InventoryOutletDao inventoryOutletDao, ProductGroupManager productGroupManager, StudentManager studentManager,
										StaffManager staffManager, UserPreferenceSettings userPreferenceSettings,
										TransactionTemplate transactionTemplate, UserPermissionManager userPermissionManager) {
		this.inventoryTransactionsDao = inventoryTransactionsDao;
		this.inventoryOutletDao = inventoryOutletDao;
		this.productGroupManager = productGroupManager;
		this.studentManager = studentManager;
		this.staffManager = staffManager;
		this.userPreferenceSettings = userPreferenceSettings;
		this.transactionTemplate = transactionTemplate;
		this.userPermissionManager = userPermissionManager;
	}

	private InventoryOutlet getOutlet(int instituteId) {
		return InventoryUtils.getOutlet(instituteId, inventoryOutletDao, userPreferenceSettings);
	}

	private UUID getOutletId(int instituteId) {
		return InventoryUtils.getOutletId(instituteId, inventoryOutletDao, userPreferenceSettings);
	}

	// TODO : Add check for user permission
	public UUID addNewPurchaseTransaction(int instituteId, UUID userId, NewPurchasePayload newPurchasePayload) {
		if (instituteId <= 0) {
			logger.error("Invalid institute {}, user {}, {}", instituteId, userId, newPurchasePayload);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_PURCHASE_ORDER, "Invalid institute provided"));
		}

		if (userId == null) {
			logger.error("Invalid user for {}, {}", instituteId, newPurchasePayload);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_PURCHASE_ORDER, "Invalid user provided"));
		}

		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.ADD_PURCHASE_TRANSACTIONS);

		if (!DateUtils.isSameDayAsToday(newPurchasePayload.getTransactionDate())) {
			userPermissionManager.verifyAuthorisation(instituteId, userId,
					AuthorisationRequiredAction.ADD_BACK_DATE_TRANSACTIONS);
		}

		UUID outletId = getOutletId(instituteId);

		//TODO : Add validation brand and category
		if (!isValidNewPurchase(outletId, newPurchasePayload)) {
			logger.error("Invalid purchase for {}, {}", instituteId, newPurchasePayload);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_PURCHASE_ORDER, "Invalid purchase"));
		}
		//Default init, required for tagging the institute in reports
		newPurchasePayload.setInventoryUserInstituteId(instituteId);
		return inventoryTransactionsDao.addNewPurchaseTransaction(outletId, userId, newPurchasePayload);
	}

	private boolean isValidNewPurchase(UUID outletId, NewPurchasePayload newPurchasePayload) {
		if (newPurchasePayload == null) {
			logger.error("Invalid purchase payload for {}", outletId);
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_PURCHASE_ORDER,
					"Invalid purchase transaction"));
		}

		if (newPurchasePayload.getSupplierId() == null) {
			logger.error("Invalid supplier for {}, {}", outletId, newPurchasePayload);
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_PURCHASE_ORDER,
					"Invalid supplier"));
		}

		if (newPurchasePayload.getPaymentStatus() == null || newPurchasePayload
				.getPaymentStatus() == PaymentStatus.FAILED) {
			logger.error("Invalid payment status for {}, {}", outletId, newPurchasePayload);
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_PURCHASE_ORDER,
					"Invalid payment status"));
		}

		if (newPurchasePayload.getTransactionMode() == null) {
			logger.error("Invalid transaction mode for {}, {}", outletId, newPurchasePayload);
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_PURCHASE_ORDER,
					"Invalid transaction payment mode"));
		}

		if (CollectionUtils.isEmpty(newPurchasePayload.getPurchasedProductPayloadList())) {
			logger.error("No product purchased in transaction for {}, {}", outletId, newPurchasePayload);
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_PURCHASE_ORDER,
					"Purchase at least one product"));
		}

		if (newPurchasePayload.getAdditionalCost() != null
				&& newPurchasePayload.getAdditionalCost() < 0d) {
			logger.error("Invalid additional cost for {}, {}", outletId, newPurchasePayload);
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_PURCHASE_ORDER,
					"Additional cost cannot be negative"));
		}

		if (newPurchasePayload.getAdditionalDiscount() != null
				&& newPurchasePayload.getAdditionalDiscount() < 0d) {
			logger.error("Invalid additional discount for {}, {}", outletId, newPurchasePayload);
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_PURCHASE_ORDER,
					"Additional discount cannot be negative"));
		}

		if (!isTransactionWithinRange(newPurchasePayload.getTransactionDate())) {
			logger.error("Invalid transaction date for {}, {}", outletId, newPurchasePayload);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_PURCHASE_ORDER, "Transaction out of valid range"));
		}

		validatePurchasedProducts(outletId, newPurchasePayload);

		if (!isValidSeller(outletId, newPurchasePayload.getSupplierId())) {
			logger.error("Invalid seller for {}, {}", outletId, newPurchasePayload);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_PURCHASE_ORDER, "Supplier does not exists."));
		}

		return true;

	}

	private void validatePurchasedProducts(UUID outletId, NewPurchasePayload newPurchasePayload) {
		Set<UUID> purchasedProductSet = new HashSet<>();
		Set<String> purchasedProductNameSet = new HashSet<>();
		for (TradeProductPayload tradeProductPayload : newPurchasePayload
				.getPurchasedProductPayloadList()) {

			if (tradeProductPayload.getSkuId() == null && (tradeProductPayload.getProductMetadataPayload() == null ||
					StringUtils.isBlank(tradeProductPayload.getProductMetadataPayload().getName()))) {
				logger.error("Invalid product for {}, {}", outletId, newPurchasePayload);
				throw new ApplicationException(new ErrorResponse(
						ApplicationErrorCode.INVALID_PURCHASE_ORDER,
						"Invalid product purchased"));
			}

			if (tradeProductPayload.getSkuId() != null) {
				if (purchasedProductSet.contains(tradeProductPayload.getSkuId())) {
					logger.error("Duplicate product for {}, {}", outletId, newPurchasePayload);
					throw new ApplicationException(new ErrorResponse(
							ApplicationErrorCode.INVALID_PURCHASE_ORDER,
							"Duplicate product purchased"));
				}
				purchasedProductSet.add(tradeProductPayload.getSkuId());
			} else {
				String newProductName = tradeProductPayload.getProductMetadataPayload().getName().trim().toLowerCase();
				if (purchasedProductNameSet.contains(newProductName)) {
					logger.error("Duplicate product for {}, {}", outletId, newPurchasePayload);
					throw new ApplicationException(new ErrorResponse(
							ApplicationErrorCode.INVALID_PURCHASE_ORDER,
							"Duplicate product purchased"));
				}
				purchasedProductNameSet.add(newProductName);

				if (tradeProductPayload.getProductMetadataPayload().getCategoryId() <= 0) {
					logger.error("Invalid category provided for {}, {}", outletId, newPurchasePayload);
					throw new ApplicationException(new ErrorResponse(
							ApplicationErrorCode.INVALID_PURCHASE_ORDER,
							"Invalid category provided"));
				}
			}

			if (CollectionUtils.isEmpty(tradeProductPayload.getTradeProductBatchPayloadList())) {
				logger.error("Invalid product purchase for {}, {}", outletId, newPurchasePayload);
				throw new ApplicationException(new ErrorResponse(
						ApplicationErrorCode.INVALID_PURCHASE_ORDER,
						"Invalid product purchased"));
			}

			validatePurchasedProductBatches(outletId, newPurchasePayload, tradeProductPayload);
		}
	}

	private void validatePurchasedProductBatches(UUID outletId, NewPurchasePayload newPurchasePayload, TradeProductPayload tradeProductPayload) {
		Set<UUID> purchasedProductBatchIdSet = new HashSet<>();
		Set<String> purchasedProductBatchNameSet = new HashSet<>();
		for (TradeProductBatchPayload tradeProductBatchPayload : tradeProductPayload.getTradeProductBatchPayloadList()) {
			if (tradeProductBatchPayload.getBatchId() == null && (tradeProductBatchPayload.getProductBatchPayload() == null ||
					StringUtils.isBlank(tradeProductBatchPayload.getProductBatchPayload().getBatchName()))) {
				logger.error("Invalid batch for product in {}, {}", outletId, newPurchasePayload);
				throw new ApplicationException(new ErrorResponse(
						ApplicationErrorCode.INVALID_PURCHASE_ORDER,
						"Invalid product batch purchased"));
			}

			if (tradeProductBatchPayload.getBatchId() != null) {
				if (purchasedProductBatchIdSet.contains(tradeProductBatchPayload.getBatchId())) {
					logger.error("Duplicate product batch for {}, {}", outletId, newPurchasePayload);
					throw new ApplicationException(new ErrorResponse(
							ApplicationErrorCode.INVALID_PURCHASE_ORDER,
							"Duplicate product batch purchased"));
				}
				purchasedProductBatchIdSet.add(tradeProductBatchPayload.getBatchId());
			} else {
				String newBatchName = tradeProductBatchPayload.getProductBatchPayload().getBatchName().trim().toLowerCase();
				if (purchasedProductBatchNameSet.contains(newBatchName)) {
					logger.error("Duplicate product batch for {}, {}", outletId, newPurchasePayload);
					throw new ApplicationException(new ErrorResponse(
							ApplicationErrorCode.INVALID_PURCHASE_ORDER,
							"Duplicate product batch purchased"));
				}
				purchasedProductBatchNameSet.add(newBatchName);
			}

			if (tradeProductBatchPayload.getQuantity() <= 0) {
				logger.error("Invalid product purchase quantity for {}, {}", outletId, newPurchasePayload);
				throw new ApplicationException(new ErrorResponse(
						ApplicationErrorCode.INVALID_PURCHASE_ORDER,
						"At least 1 quantity should be added for purchase of product"));
			}

			if (tradeProductBatchPayload.getTotalPrice() <= 0d) {
				logger.error("Invalid product purchase total price for {}, {}", outletId, newPurchasePayload);
				throw new ApplicationException(new ErrorResponse(
						ApplicationErrorCode.INVALID_PURCHASE_ORDER,
						"Total price should be greater than zero"));
			}

			if (tradeProductBatchPayload.getTotalDiscount() < 0d) {
				logger.error("Invalid product purchase total discount for {}, {}", outletId, newPurchasePayload);
				throw new ApplicationException(new ErrorResponse(
						ApplicationErrorCode.INVALID_PURCHASE_ORDER,
						"Negative discount is not allowed"));
			}
//
//                if (purchasedProduct.getTotalTax() < 0d) {
//                    throw new ApplicationException(new ErrorResponse(
//                            ApplicationErrorCode.INVALID_PURCHASE_ORDER,
//                            "Negative tax is not allowed"));
//                }
		}
	}

	private boolean isValidSeller(UUID outletId, UUID supplierId) {
		final InventorySupplier supplier = inventoryOutletDao.getSupplier(outletId, supplierId);
		if (supplier == null) {
			logger.error("Invalid supplier for outlet {}, supplierId {}", outletId, supplierId);
			return false;
		}
		return true;

	}

	private boolean isTransactionWithinRange(long transactionDate) {
		final long currentTime = System.currentTimeMillis();
		final long minTransactionTime = DateUtils.getDayStart(currentTime - PAST_TRANSACTION_DAYS_IN_SECONDS * 1000l,
				User.DFAULT_TIMEZONE);
		return !(transactionDate <= 0 || transactionDate > currentTime || transactionDate < minTransactionTime);

	}


	// TODO : Add check for user permission
	public UUID addNewTradeTransaction(int instituteId, UUID userId, NewTradePayload newTradePayload) {
		if (instituteId <= 0) {
			logger.error("Invalid institute {}, user {}, {}", instituteId, userId, newTradePayload);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Invalid institute provided"));
		}

		if (userId == null) {
			logger.error("Invalid user for {}, {}", instituteId, newTradePayload);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Invalid user provided"));
		}

		InventoryOutlet outlet = getOutlet(instituteId);
		if (outlet == null || outlet.getOutletId() == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVENTORY_NOT_ENABLED, "Inventory not enabled for your institute"));
		}
		UUID outletId = outlet.getOutletId();

		if (!isValidNewSale(outletId, newTradePayload)) {
			logger.error("Invalid sale for {}, {}", instituteId, newTradePayload);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Invalid sale"));
		}

		if (newTradePayload.getInventoryTransactionType() == InventoryTransactionType.RETURN) {
			userPermissionManager.verifyAuthorisation(instituteId,
					userId, AuthorisationRequiredAction.ADD_PURCHASE_TRANSACTIONS);
		} else {
			userPermissionManager.verifyAuthorisation(instituteId,
					userId, AuthorisationRequiredAction.ADD_TRADE_TRANSACTIONS);
		}

		if (!DateUtils.isSameDayAsToday(newTradePayload.getTransactionDate())) {
			userPermissionManager.verifyAuthorisation(instituteId, userId,
					AuthorisationRequiredAction.ADD_BACK_DATE_TRANSACTIONS);
		}

		//Default init, required for tagging the institute in reports
		newTradePayload.setInventoryUserInstituteId(instituteId);

		if (newTradePayload.getInventoryUserType() == InventoryUserType.STUDENT && !validateStudentAndUpdateInstituteId(outlet, newTradePayload)) {
			logger.error("Invalid student user for sale for {}, {}", instituteId, newTradePayload);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Invalid user for sale"));
		}

		if (newTradePayload.getInventoryUserType() == InventoryUserType.SELLER
				&& !isValidSeller(outletId, UUID.fromString(newTradePayload.getPurchasedBy()))) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Supplier does not exists."));
		}

		if(newTradePayload.getInventoryUserType() == InventoryUserType.STUDENT && newTradePayload.getInventoryTransactionType() == SALE){
			final Student student = studentManager.getStudentWithoutSession(UUID.fromString(newTradePayload.getPurchasedBy()));
			RestrictionUtils.isActionRestricted(student, TaggedActions.INVENTORY_SALE_RESTRICTED, true);
		}

		//Inventory pref required for institute specific actions which will depend on student institute in scope
		InventoryPreferences instituteScopedInventoryPreferences = userPreferenceSettings.getInventoryPreferences(newTradePayload.getInventoryUserInstituteId());
		WalletPreferences walletPreferences = null;
		if (newTradePayload.getInventoryUserType() == InventoryUserType.STUDENT && newTradePayload.isUseWallet()) {
			walletPreferences = userPreferenceSettings.getStudentWalletPreferences(newTradePayload.getInventoryUserInstituteId());
		}
			return inventoryTransactionsDao.addNewTradeTransaction(outletId, userId, newTradePayload, instituteScopedInventoryPreferences, walletPreferences);
	}

	// TODO : Add check for user permission
	public UUID addProductIssueTransaction(int instituteId, UUID userId, ProductIssuePayload productIssuePayload) {
		if (instituteId <= 0) {
			logger.error("Invalid institute {}, user {}, {}", instituteId, userId, productIssuePayload);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid institute provided"));
		}

		if (userId == null) {
			logger.error("Invalid user for {}, {}", instituteId, productIssuePayload);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid user provided"));
		}

		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.ADD_ISSUE_TRANSACTIONS);

		if (!DateUtils.isSameDayAsToday(productIssuePayload.getTransactionDate())) {
			userPermissionManager.verifyAuthorisation(instituteId, userId,
					AuthorisationRequiredAction.ADD_BACK_DATE_TRANSACTIONS);
		}

		InventoryOutlet outlet = getOutlet(instituteId);
		if (outlet == null || outlet.getOutletId() == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVENTORY_NOT_ENABLED, "Inventory not enabled for your institute"));
		}
		UUID outletId = outlet.getOutletId();

		if (!isValidIssuePayload(outletId, productIssuePayload)) {
			logger.error("Invalid issue payload for {}, {}", instituteId, productIssuePayload);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid issue payload"));
		}
		//Default init, required for tagging the institute in reports
		productIssuePayload.setInventoryUserInstituteId(instituteId);
		return inventoryTransactionsDao.addProductIssueTransaction(outletId, userId, productIssuePayload);
	}

	public ExchangeTransactionResponse addExchangeTransaction(int instituteId, UUID userId, ExchangeOrderPayload exchangeOrderPayload) {
		if (instituteId <= 0) {
			logger.error("Invalid institute {}, user {}, {}", instituteId, userId, exchangeOrderPayload);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Invalid institute provided"));
		}

		if (userId == null) {
			logger.error("Invalid user for {}, {}", instituteId, exchangeOrderPayload);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Invalid user provided"));
		}

		if (exchangeOrderPayload == null || exchangeOrderPayload.getReturnOrderPayload() == null
				|| exchangeOrderPayload.getNewTradePayload() == null
				|| exchangeOrderPayload.getReturnOrderPayload().getReturnTransactionId() == null
				|| CollectionUtils.isEmpty(exchangeOrderPayload.getReturnOrderPayload().getReturnedProductList())
				|| (CollectionUtils.isEmpty(exchangeOrderPayload.getNewTradePayload().getSaleProductPayloadList()) &&
				CollectionUtils.isEmpty(exchangeOrderPayload.getNewTradePayload().getSaleProductGroupPayloadList()))) {
			logger.error("Invalid exchange order for {}, {}", instituteId, exchangeOrderPayload);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Invalid exchange order payload"));
		}
		UUID returnTransactionId = exchangeOrderPayload.getReturnOrderPayload().getReturnTransactionId();
		InventoryTransactionSummary originalReturnTransactionSummary = getTransactionDetails(instituteId, returnTransactionId);
		if (originalReturnTransactionSummary == null || originalReturnTransactionSummary.getTransactionType() != InventoryTransactionType.SALE) {
			logger.error("Invalid sale return transaction id for {}, {}", instituteId, exchangeOrderPayload);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Invalid invoice to exchange"));
		}

		if (originalReturnTransactionSummary.getTransactionDate() > exchangeOrderPayload.getNewTradePayload().getTransactionDate() ) {
			logger.error("Invalid exchange transaction date for original tr {}, current {}", instituteId,
					originalReturnTransactionSummary.getTransactionDate(), exchangeOrderPayload);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Invalid exchange transaction date"));
		}

		Map<UUID, Map<UUID, TradeProductBatchSummary>> skuBatchSummaryMap = getSkuBatchSummaryMap(originalReturnTransactionSummary);

		Map<UUID, TradeProductPayload> tradeProductPayloadMap = new LinkedHashMap<>();
		double refundAmount = 0d;
		for (ReturnedProduct returnedProduct : exchangeOrderPayload.getReturnOrderPayload().getReturnedProductList()) {
			UUID skuId = returnedProduct.getSkuId();
			UUID batchId = returnedProduct.getBatchId();
			int selectedQty = returnedProduct.getSelectedQty();
			if (!skuBatchSummaryMap.containsKey(skuId)) {
				logger.error("Invalid product {} to return for {}, {}", skuId, instituteId, exchangeOrderPayload);
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Invalid product to return in exchange"));
			}

			if (!skuBatchSummaryMap.get(skuId).containsKey(batchId)) {
				logger.error("Invalid product {} batch {} to return for {}, {}", skuId, batchId, instituteId, exchangeOrderPayload);
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Invalid product to return in exchange"));
			}

			TradeProductBatchSummary tradeProductBatchSummary = skuBatchSummaryMap.get(skuId).get(batchId);

			if (tradeProductBatchSummary.getQuantity() < selectedQty * 1d) {
				logger.error("Invalid amount {} out of {} of product {} batch {} to return for {}, {}", selectedQty, tradeProductBatchSummary.getQuantity(), skuId, batchId, instituteId, exchangeOrderPayload);
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Invalid product quantity to return in exchange"));
			}
			double originalQty = tradeProductBatchSummary.getQuantity();
			double originalTotalPrice = tradeProductBatchSummary.getTotalPrice();
			double originalTotalDiscount = tradeProductBatchSummary.getTotalDiscount();
			double originalTotalTax = tradeProductBatchSummary.getTotalTax();

			TradeProductPayload tradeProductPayload = tradeProductPayloadMap.getOrDefault(skuId, new TradeProductPayload(skuId, new ArrayList<>()));

			tradeProductPayload.getTradeProductBatchPayloadList().add(
					new TradeProductBatchPayload(batchId, null, selectedQty,
							getValueInRatio(originalTotalPrice, originalQty, selectedQty),
							getValueInRatio(originalTotalDiscount, originalQty, selectedQty),
							getValueInRatio(originalTotalTax, originalQty, selectedQty)));

			tradeProductPayloadMap.put(skuId, tradeProductPayload);

		}

		NewTradePayload returnNewTradePayload = getReturnNewTradePayload(exchangeOrderPayload, originalReturnTransactionSummary, tradeProductPayloadMap);

		UUID returnOrderTransactionId = addNewTradeTransaction(instituteId, userId, returnNewTradePayload);
		if (returnOrderTransactionId == null) {
			logger.error("Unable to return transaction for exchange order {}, {}", instituteId, exchangeOrderPayload);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Unable to return products in exchange transaction"));
		}
		UUID newTradeTransactionId = null;
		try {
			NewTradePayload newTradePayload = getNewTradeTransactionBasedOnReturnedProducts(exchangeOrderPayload, returnNewTradePayload, originalReturnTransactionSummary);
			logger.info("new trade payload {}", newTradePayload);
			newTradeTransactionId = addNewTradeTransaction(instituteId, userId, newTradePayload);
			if (newTradeTransactionId == null) {
				boolean deleteReturnTransaction = deleteTransaction(instituteId, userId, returnOrderTransactionId, true);
				logger.info("Return transaction status {}, for institute {}, returnOrderTransactionId {}", deleteReturnTransaction, instituteId, returnOrderTransactionId);
				logger.error("Unable to add transaction for exchange order {}, {}", instituteId, exchangeOrderPayload);
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Unable to sale products in exchange transaction"));
			}
		} catch (Exception e) {
			logger.error("Unable to add transaction for exchange order {}, {}", instituteId, exchangeOrderPayload, e);
			boolean deleteReturnTransaction = deleteTransaction(instituteId, userId, returnOrderTransactionId, true);
			logger.info("Delete status of return transaction is {}, for institute {}, returnOrderTransactionId {}", deleteReturnTransaction, instituteId, returnOrderTransactionId);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Unable to sale products in exchange transaction"));
		}


		try {
			updateExistingTransactionMetadata(instituteId, originalReturnTransactionSummary, returnOrderTransactionId, newTradeTransactionId);
		} catch (Exception e) {
			logger.error("Error while updating the transaction metadata for institute {}, existing transaction {}", instituteId, originalReturnTransactionSummary.getTransactionId());
		}
		return new ExchangeTransactionResponse(true, returnOrderTransactionId, newTradeTransactionId);
	}

	private static NewTradePayload getNewTradeTransactionBasedOnReturnedProducts(ExchangeOrderPayload exchangeOrderPayload, NewTradePayload returnNewTradePayload, InventoryTransactionSummary inventoryTransactionSummary) {
		NewTradePayload inputNewTradePayload1 = exchangeOrderPayload.getNewTradePayload();
		NewTradePayload newTradePayload = new NewTradePayload(null, inventoryTransactionSummary.getInventoryUserType(),
				SALE, inputNewTradePayload1.getTransactionMode(), PaymentStatus.PAID,
				inventoryTransactionSummary.getInventoryUserType() == InventoryUserType.STUDENT ? inventoryTransactionSummary.getStudentLite().getStudentId().toString() :
						inventoryTransactionSummary.getBuyerName(),
				inputNewTradePayload1.getReference(), exchangeOrderPayload.getNewTradePayload().getEmail(),
				inputNewTradePayload1.getTransactionDate(),
				inputNewTradePayload1.getDescription(),
				inputNewTradePayload1.getAdditionalCost(),
				inputNewTradePayload1.getAdditionalDiscount(),
				inputNewTradePayload1.isUseWallet(),
				inputNewTradePayload1.getUsedWalletAmount(),
				inputNewTradePayload1.getWalletCreditAmount(),
				inputNewTradePayload1.getPaidAmount(),
				inputNewTradePayload1.getSaleProductPayloadList(),
				inputNewTradePayload1.getSaleProductGroupPayloadList());


		Double netPaidAmount = newTradePayload.getPaidAmount();
		if (netPaidAmount != null && Double.compare(netPaidAmount, 0d) >= 0) {
			if (newTradePayload.getInventoryUserType() == InventoryUserType.STUDENT) {
				newTradePayload.setUseWallet(true);
				newTradePayload.setUsedWalletAmount(
						NumberUtils.addValues(newTradePayload.getUsedWalletAmount(),
								returnNewTradePayload.getNetPayableAmount()));
				// Paid amount remains same
			} else {
				newTradePayload.setUseWallet(false);
				newTradePayload.setUsedWalletAmount(0d);
				newTradePayload.setPaidAmount(newTradePayload.getNetPayableAmount());
			}

		} else {
			if (newTradePayload.getTransactionMode() == WALLET) {
				newTradePayload.setUseWallet(true);
				newTradePayload.setUsedWalletAmount(newTradePayload.getNetPayableAmount());
				// Need to explicitly set the mode as cash if all the amount is transferred from wallet
				newTradePayload.setTransactionMode(CASH);
				newTradePayload.setPaidAmount(0d);
			} else {
				newTradePayload.setUseWallet(false);
				newTradePayload.setUsedWalletAmount(0d);
				newTradePayload.setPaidAmount(newTradePayload.getNetPayableAmount());
			}
		}
		return newTradePayload;
	}

	private void updateExistingTransactionMetadata(int instituteId, InventoryTransactionSummary originalReturnTransactionSummary, UUID returnOrderTransactionId, UUID newTradeTransactionId) {
		Map<String, String> existingMetadata = originalReturnTransactionSummary.getMetadata();
		if (existingMetadata == null) {
			existingMetadata = new HashMap<>();
		}
		String exchangeOrderMetadataStr = existingMetadata.get(EXCHANGE_ORDER_DATA);
		ExchangeOrderMetadata exchangeOrderMetadata = null;
		if (StringUtils.isBlank(exchangeOrderMetadataStr)) {
			exchangeOrderMetadata = new ExchangeOrderMetadata(new ArrayList<>());
		} else {
			exchangeOrderMetadata = SharedConstants.GSON.fromJson(exchangeOrderMetadataStr, ExchangeOrderMetadata.class);
		}
		exchangeOrderMetadata.getExchangeOrderEntryList().add(new ExchangeOrderEntry(returnOrderTransactionId, newTradeTransactionId, DateUtils.now()));

		existingMetadata.put(EXCHANGE_ORDER_DATA, SharedConstants.GSON.toJson(exchangeOrderMetadata));

		inventoryTransactionsDao.updateTransactionMetadata(getOutletId(instituteId), originalReturnTransactionSummary.getTransactionId(), existingMetadata);
	}

	private static NewTradePayload getReturnNewTradePayload(ExchangeOrderPayload exchangeOrderPayload, InventoryTransactionSummary inventoryTransactionSummary, Map<UUID, TradeProductPayload> tradeProductPayloadMap) {
		TransactionMode returnTransactionMode = getReturnTransactionMode(exchangeOrderPayload, inventoryTransactionSummary);

		NewTradePayload returnNewTradePayload = new NewTradePayload(null, inventoryTransactionSummary.getInventoryUserType(),
				SALES_RETURN, returnTransactionMode, PaymentStatus.PAID,
				inventoryTransactionSummary.getInventoryUserType() == InventoryUserType.STUDENT ? inventoryTransactionSummary.getStudentLite().getStudentId().toString() :
						inventoryTransactionSummary.getBuyerName(),
				exchangeOrderPayload.getNewTradePayload().getReference(), exchangeOrderPayload.getNewTradePayload().getEmail(),
				exchangeOrderPayload.getNewTradePayload().getTransactionDate(),
				exchangeOrderPayload.getNewTradePayload().getDescription(), 0d, 0d, false,
				0d, 0d, 0d,
				new ArrayList<>(tradeProductPayloadMap.values()), new ArrayList<>());

		returnNewTradePayload.setPaidAmount(returnNewTradePayload.getNetPayableAmount());
		return returnNewTradePayload;
	}

	private static TransactionMode getReturnTransactionMode(ExchangeOrderPayload exchangeOrderPayload, InventoryTransactionSummary inventoryTransactionSummary) {
		TransactionMode inputTransactionMode = exchangeOrderPayload.getNewTradePayload().getTransactionMode();
		Double netPaidAmount = exchangeOrderPayload.getNewTradePayload().getPaidAmount();
		TransactionMode returnTransactionMode = inputTransactionMode;
		if (netPaidAmount != null && Double.compare(netPaidAmount, 0d) >= 0) {
			// For net positive payment. In case of student amount should always go to wallet.
			// TODO: make sure that if config does not allow wallet payment for institute then how to manage
			returnTransactionMode = inventoryTransactionSummary.getInventoryUserType() == InventoryUserType.STUDENT ? WALLET :
					inputTransactionMode;
		}
		return returnTransactionMode;
	}

	private double getValueInRatio(double value, double originalVal, int selectedQty) {
		return value * selectedQty / originalVal;
	}

	private static Map<UUID, Map<UUID, TradeProductBatchSummary>> getSkuBatchSummaryMap(InventoryTransactionSummary inventoryTransactionSummary) {
		Map<UUID, Map<UUID, TradeProductBatchSummary>> skuBatchMap = EMapUtils.getMap(inventoryTransactionSummary.getTradeProductSummaryList(), new EMapUtils.MapFunction<TradeProductSummary, UUID, Map<UUID, TradeProductBatchSummary>>() {
			@Override
			public UUID getKey(TradeProductSummary entry) {
				return entry.getSkuId();
			}

			@Override
			public Map<UUID, TradeProductBatchSummary> getValue(TradeProductSummary entry) {
				return EMapUtils.getMap(entry.getTradeProductBatchSummaryList(), new EMapUtils.MapFunction<TradeProductBatchSummary, UUID, TradeProductBatchSummary>() {
					@Override
					public UUID getKey(TradeProductBatchSummary entry) {
						return entry.getBatchId();
					}

					@Override
					public TradeProductBatchSummary getValue(TradeProductBatchSummary entry) {
						return entry;
					}
				});
			}
		});
		return skuBatchMap;
	}

	private boolean validateStudentAndUpdateInstituteId(InventoryOutlet outlet, NewTradePayload newTradePayload) {
		UUID studentId = UUID.fromString(newTradePayload.getPurchasedBy());
		final Student student = studentManager.getStudentByLatestAcademicSession(studentId, Arrays.asList(StudentStatus.ENROLLED, StudentStatus.ENROLMENT_PENDING));
		if (student == null) {
			logger.error("Student {} does not exists.", studentId);
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_SALES_ORDER,
					"Student does not exists"));
		}
		int studentInstituteId = student.getStudentAcademicSessionInfoResponse().getAcademicSession().getInstituteId();
		if (!outlet.getInstituteScope().contains(studentInstituteId)) {
			logger.error("Student {}, {} does not belong to allowed scope of institute in inventory outlet {} for .", studentId,
					studentInstituteId, outlet);
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_SALES_ORDER,
					"Sale not allowed for student"));
		}
		newTradePayload.setInventoryUserInstituteId(studentInstituteId);

		InventoryPreferences inventoryPreferences = userPreferenceSettings.getInventoryPreferences(studentInstituteId);
		validateWalletSaleTransaction(studentInstituteId, newTradePayload, inventoryPreferences);
		return true;
	}

	private boolean validateStaffAndUpdateInstituteId(InventoryPreferences inventoryPreferences, SalesOrderPayload salesOrderPayload) {
		String staffId = salesOrderPayload.getPurchasedBy();
		final Staff staff = staffManager.getStaff(UUID.fromString(staffId));
		if (staff == null) {
			logger.error("Staff {} does not exists.", staffId);
			return false;
		}

		if (!inventoryPreferences.getInventoryInstitutesScope().contains(staff.getInstituteId())) {
			logger.error("Staff {} does not belong to allowed scope of inventory institutes {}.", staffId,
					inventoryPreferences.getInventoryInstitutesScope());
			return false;
		}

		salesOrderPayload.setInventoryUserInstituteId(staff.getInstituteId());
		return true;
	}


	private boolean isValidNewSale(UUID outletId, NewTradePayload newTradePayload) {
		if (newTradePayload == null) {
			logger.error("Invalid sale payload for {}", outletId);
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_SALES_ORDER,
					"Invalid sale transaction"));
		}

		if (newTradePayload.getInventoryTransactionType() == null) {
			logger.error("Invalid transaction type for {}, {}", outletId, newTradePayload);
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_SALES_ORDER,
					"Invalid transaction type"));
		}

		if (newTradePayload.getTransactionMode() == null) {
			logger.error("Invalid transaction mode for {}, {}", outletId, newTradePayload);
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_SALES_ORDER,
					"Invalid transaction payment mode"));
		}

		if (CollectionUtils.isEmpty(newTradePayload.getSaleProductPayloadList()) &&
				CollectionUtils.isEmpty(newTradePayload.getSaleProductGroupPayloadList())) {
			logger.error("No product or product group in transaction for {}, {}", outletId, newTradePayload);
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_SALES_ORDER,
					"Sale at least one product or product group."));
		}

		if (newTradePayload.getAdditionalCost() != null
				&& newTradePayload.getAdditionalCost() < 0d) {
			logger.error("Invalid additional cost for {}, {}", outletId, newTradePayload);
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_SALES_ORDER,
					"Additional cost cannot be negative"));
		}

		if (newTradePayload.getAdditionalDiscount() != null
				&& newTradePayload.getAdditionalDiscount() < 0d) {
			logger.error("Invalid additional discount for {}, {}", outletId, newTradePayload);
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_SALES_ORDER,
					"Additional discount cannot be negative"));
		}

		if (newTradePayload.getPaidAmount() != null && Double.compare(newTradePayload.getPaidAmount(), 0d) < 0) {
			logger.error("Invalid paid amount, outletId {}, {} ", outletId, newTradePayload);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Invalid paid amount"));
		}


		if (!isTransactionWithinRange(newTradePayload.getTransactionDate())) {
			logger.error("Invalid transaction date for {}, {}", outletId, newTradePayload);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Transaction out of valid range"));
		}


		validateInventoryUser(newTradePayload);
		validateSaleProducts(outletId, newTradePayload);
		validateSaleProductGroups(outletId, newTradePayload);
		return true;

	}

	private void validateInventoryUser(
			NewTradePayload newTradePayload) {
		InventoryUserType userType = newTradePayload.getInventoryUserType();
		if (userType == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER,
							"Invalid inventory user type"));
		}

		if (userType == InventoryUserType.SELLER
				&& newTradePayload.getInventoryTransactionType() != InventoryTransactionType.RETURN) {
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_SALES_ORDER,
					"Only return transaction is allowed for supplier"));
		}


		if (userType == InventoryUserType.STUDENT || userType == InventoryUserType.STAFF || userType == InventoryUserType.SELLER) {
			try {
				UUID.fromString(newTradePayload.getPurchasedBy());
			} catch (Exception e) {
				throw new ApplicationException(new ErrorResponse(
						ApplicationErrorCode.INVALID_SALES_ORDER,
						"Invalid user for sale"));
			}
		}

		if (userType == InventoryUserType.OTHER
				&& StringUtils.isBlank(newTradePayload.getPurchasedBy())) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER,
							"Invalid user for sale"));
		}
	}


	private void validateSaleProducts(UUID outletId, NewTradePayload newTradePayload) {
		if (CollectionUtils.isEmpty(newTradePayload.getSaleProductPayloadList())) {
			return;
		}
		Set<UUID> saleProductSet = new HashSet<>();
		for (TradeProductPayload tradeProductPayload : newTradePayload
				.getSaleProductPayloadList()) {

			if (tradeProductPayload.getSkuId() == null) {
				logger.error("Invalid product for {}, {}", outletId, newTradePayload);
				throw new ApplicationException(new ErrorResponse(
						ApplicationErrorCode.INVALID_SALES_ORDER,
						"Invalid product sale"));
			}

			if (saleProductSet.contains(tradeProductPayload.getSkuId())) {
				logger.error("Duplicate product for {}, {}", outletId, newTradePayload);
				throw new ApplicationException(new ErrorResponse(
						ApplicationErrorCode.INVALID_SALES_ORDER,
						"Duplicate product sale"));
			}

			saleProductSet.add(tradeProductPayload.getSkuId());

			if (CollectionUtils.isEmpty(tradeProductPayload.getTradeProductBatchPayloadList())) {
				logger.error("Invalid product purchase for {}, {}", outletId, newTradePayload);
				throw new ApplicationException(new ErrorResponse(
						ApplicationErrorCode.INVALID_SALES_ORDER,
						"Invalid product sale"));
			}

			validateSaleProductBatches(outletId, newTradePayload, tradeProductPayload);
		}
	}

	private void validateSaleProductGroups(UUID outletId, NewTradePayload newTradePayload) {
		if (CollectionUtils.isEmpty(newTradePayload.getSaleProductGroupPayloadList())) {
			return;
		}
		Set<UUID> saleProductGroups = new HashSet<>();
		for (TradeProductGroupPayload tradeProductGroupPayload : newTradePayload
				.getSaleProductGroupPayloadList()) {

			if (tradeProductGroupPayload.getGroupId() == null) {
				logger.error("Invalid product group for {}, {}", outletId, newTradePayload);
				throw new ApplicationException(new ErrorResponse(
						ApplicationErrorCode.INVALID_SALES_ORDER,
						"Invalid product group"));
			}

			if (saleProductGroups.contains(tradeProductGroupPayload.getGroupId())) {
				logger.error("Duplicate product group for {}, {}", outletId, newTradePayload);
				throw new ApplicationException(new ErrorResponse(
						ApplicationErrorCode.INVALID_SALES_ORDER,
						"Duplicate product group"));
			}

			saleProductGroups.add(tradeProductGroupPayload.getGroupId());

			if (tradeProductGroupPayload.getQuantity() <= 0) {
				logger.error("Invalid product group sale quantity for {}, {}", outletId, newTradePayload);
				throw new ApplicationException(new ErrorResponse(
						ApplicationErrorCode.INVALID_SALES_ORDER,
						"At least 1 quantity should be added for sale of product"));
			}


			if (tradeProductGroupPayload.getTotalDiscount() < 0d) {
				logger.error("Invalid product sale total discount for {}, {}", outletId, newTradePayload);
				throw new ApplicationException(new ErrorResponse(
						ApplicationErrorCode.INVALID_SALES_ORDER,
						"Negative discount is not allowed"));
			}
		}
	}

	private void validateSaleProductBatches(UUID outletId, NewTradePayload newTradePayload, TradeProductPayload tradeProductPayload) {
		Set<UUID> saleProductBatchIdSet = new HashSet<>();
		for (TradeProductBatchPayload tradeProductBatchPayload : tradeProductPayload.getTradeProductBatchPayloadList()) {
			if (tradeProductBatchPayload.getBatchId() == null) {
				logger.error("Invalid batch for product in {}, {}", outletId, newTradePayload);
				throw new ApplicationException(new ErrorResponse(
						ApplicationErrorCode.INVALID_SALES_ORDER,
						"Invalid product batch sale"));
			}

			if (saleProductBatchIdSet.contains(tradeProductBatchPayload.getBatchId())) {
				logger.error("Duplicate product batch for {}, {}", outletId, newTradePayload);
				throw new ApplicationException(new ErrorResponse(
						ApplicationErrorCode.INVALID_SALES_ORDER,
						"Duplicate product batch sale"));
			}
			saleProductBatchIdSet.add(tradeProductBatchPayload.getBatchId());

			if (tradeProductBatchPayload.getQuantity() <= 0) {
				logger.error("Invalid product sale quantity for {}, {}", outletId, newTradePayload);
				throw new ApplicationException(new ErrorResponse(
						ApplicationErrorCode.INVALID_SALES_ORDER,
						"At least 1 quantity should be added for sale of product"));
			}

			if (tradeProductBatchPayload.getTotalPrice() <= 0d) {
				logger.error("Invalid product sale total price for {}, {}", outletId, newTradePayload);
				throw new ApplicationException(new ErrorResponse(
						ApplicationErrorCode.INVALID_SALES_ORDER,
						"Total price should be greater than zero"));
			}

			if (tradeProductBatchPayload.getTotalDiscount() < 0d) {
				logger.error("Invalid product sale total discount for {}, {}", outletId, newTradePayload);
				throw new ApplicationException(new ErrorResponse(
						ApplicationErrorCode.INVALID_SALES_ORDER,
						"Negative discount is not allowed"));
			}
//
//                if (purchasedProduct.getTotalTax() < 0d) {
//                    throw new ApplicationException(new ErrorResponse(
//                            ApplicationErrorCode.INVALID_PURCHASE_ORDER,
//                            "Negative tax is not allowed"));
//                }
		}
	}

	private void validateWalletSaleTransaction(int instituteId, NewTradePayload newTradePayload, InventoryPreferences inventoryPreferences) {
		if (newTradePayload.isUseWallet() && !inventoryPreferences.isAllowWalletUsage()) {
			logger.error("Wallet usage for inventory is not enabled for institute {} ", instituteId);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER,
					"Wallet usage for inventory is not enabled for institute"));
		}

		if (newTradePayload.isUseWallet() && newTradePayload.getInventoryUserType() != InventoryUserType.STUDENT) {
			logger.error("Wallet transactions not allowed other than student, institute {} ", instituteId);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER,
					"Wallet transactions not allowed other than student"));
		}

		if (newTradePayload.isUseWallet() && (newTradePayload.getUsedWalletAmount() == null
				|| Double.compare(newTradePayload.getUsedWalletAmount(), 0d) < 0)) {
			logger.error("Invalid wallet transactions amount, institute {} ", instituteId);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Invalid wallet transactions amount"));
		}

		if (newTradePayload.isUseWallet() && newTradePayload.getWalletCreditAmount() != null
				&& Double.compare(newTradePayload.getWalletCreditAmount(), 0d) < 0) {
			logger.error("Invalid wallet credit amount, institute {} ", instituteId);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Invalid wallet credit amount"));
		}
	}


	private boolean isValidIssuePayload(UUID outletId, ProductIssuePayload productIssuePayload) {
		if (productIssuePayload == null) {
			logger.error("Invalid payload for {}", outletId);
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_REQUEST, "Invalid issue transaction"));
		}

		if (CollectionUtils.isEmpty(productIssuePayload.getIssueProductPayloadList())) {
			logger.error("No product issued in transaction for {}, {}", outletId, productIssuePayload);
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_REQUEST,
					"Issue at least one product"));
		}


		if (!isTransactionWithinRange(productIssuePayload.getTransactionDate())) {
			logger.error("Invalid transaction date for {}, {}", outletId, productIssuePayload);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Transaction out of valid range"));
		}

		validateIssueProducts(outletId, productIssuePayload);
		return true;

	}

	private void validateIssueProducts(UUID outletId, ProductIssuePayload productIssuePayload) {
		Set<UUID> issueProductSet = new HashSet<>();
		for (TradeProductPayload tradeProductPayload : productIssuePayload
				.getIssueProductPayloadList()) {

			if (tradeProductPayload.getSkuId() == null) {
				logger.error("Invalid product for {}, {}", outletId, productIssuePayload);
				throw new ApplicationException(new ErrorResponse(
						ApplicationErrorCode.INVALID_REQUEST,
						"Invalid product sale"));
			}

			if (issueProductSet.contains(tradeProductPayload.getSkuId())) {
				logger.error("Duplicate product for {}, {}", outletId, productIssuePayload);
				throw new ApplicationException(new ErrorResponse(
						ApplicationErrorCode.INVALID_REQUEST,
						"Duplicate product sale"));
			}

			issueProductSet.add(tradeProductPayload.getSkuId());

			if (CollectionUtils.isEmpty(tradeProductPayload.getTradeProductBatchPayloadList())) {
				logger.error("Invalid product purchase for {}, {}", outletId, productIssuePayload);
				throw new ApplicationException(new ErrorResponse(
						ApplicationErrorCode.INVALID_REQUEST,
						"Invalid product sale"));
			}

			validateIssueProductBatches(outletId, productIssuePayload, tradeProductPayload);
		}
	}

	private void validateIssueProductBatches(UUID outletId, ProductIssuePayload productIssuePayload, TradeProductPayload tradeProductPayload) {
		Set<UUID> issueProductBatchIdSet = new HashSet<>();
		for (TradeProductBatchPayload tradeProductBatchPayload : tradeProductPayload.getTradeProductBatchPayloadList()) {
			if (tradeProductBatchPayload.getBatchId() == null) {
				logger.error("Invalid batch for product in {}, {}", outletId, productIssuePayload);
				throw new ApplicationException(new ErrorResponse(
						ApplicationErrorCode.INVALID_REQUEST,
						"Invalid product batch for issue"));
			}

			if (issueProductBatchIdSet.contains(tradeProductBatchPayload.getBatchId())) {
				logger.error("Duplicate product batch for {}, {}", outletId, productIssuePayload);
				throw new ApplicationException(new ErrorResponse(
						ApplicationErrorCode.INVALID_REQUEST,
						"Duplicate product batch for issue"));
			}
			issueProductBatchIdSet.add(tradeProductBatchPayload.getBatchId());

			if (tradeProductBatchPayload.getQuantity() <= 0) {
				logger.error("Invalid product issue quantity for {}, {}", outletId, tradeProductBatchPayload);
				throw new ApplicationException(new ErrorResponse(
						ApplicationErrorCode.INVALID_REQUEST,
						"At least 1 quantity should be added for issue of product"));
			}
		}
	}

	public List<InventoryTransactionMetadata> getTransactionsMetaData(int instituteId) {
		return inventoryTransactionsDao.getTransactionsMetaData(getOutletId(instituteId));
	}


	public InventoryTransactionSummary getTransactionDetails(int instituteId, UUID transactionId) {
		return inventoryTransactionsDao.getTransactionDetails(getOutletId(instituteId), transactionId);
	}

	public InventoryTransactionSummary getTransactionDetailsByInvoiceId(int instituteId, String invoiceId) {
		return inventoryTransactionsDao.getTransactionDetailsByInvoiceId(getOutletId(instituteId), invoiceId);
	}


	public List<InventoryTransactionSummary> getTransactionDetailsList(int instituteId, Integer startDate, Integer endDate,
																	   InventoryTransactionType inventoryTransactionType) {
		return inventoryTransactionsDao.getTransactionDetailsList(getOutletId(instituteId), startDate, endDate, inventoryTransactionType);
	}

	//    private boolean validateStudentAndUpdateInstituteId(InventoryPreferences inventoryPreferences, SalesOrderPayload salesOrderPayload) {
//        String studentId = salesOrderPayload.getPurchasedBy();
//        final Student student = studentManager.getStudentByLatestAcademicSession(UUID.fromString(studentId),
//                Arrays.asList(StudentStatus.ENROLLED, StudentStatus.ENROLMENT_PENDING));
//        if (student == null) {
//            logger.error("Student {} does not exists.", studentId);
//            return false;
//        }
//        if (!inventoryPreferences.getInventoryInstitutesScope()
//                .contains(student.getStudentAcademicSessionInfoResponse().getAcademicSession().getInstituteId())) {
//            logger.error("Student {} does not belong to allowed scope of inventory institutes {}.", studentId,
//                    inventoryPreferences.getInventoryInstitutesScope());
//            return false;
//        }
//        salesOrderPayload.setInventoryUserInstituteId(student.getStudentAcademicSessionInfoResponse().getAcademicSession().getInstituteId());
//        return true;
//    }
//
//    private boolean validateStaffAndUpdateInstituteId(InventoryPreferences inventoryPreferences, SalesOrderPayload salesOrderPayload) {
//        String staffId = salesOrderPayload.getPurchasedBy();
//        final Staff staff = staffManager.getStaff(UUID.fromString(staffId));
//        if (staff == null) {
//            logger.error("Staff {} does not exists.", staffId);
//            return false;
//        }
//
//        if (!inventoryPreferences.getInventoryInstitutesScope().contains(staff.getInstituteId())) {
//            logger.error("Staff {} does not belong to allowed scope of inventory institutes {}.", staffId,
//                    inventoryPreferences.getInventoryInstitutesScope());
//            return false;
//        }
//
//        salesOrderPayload.setInventoryUserInstituteId(staff.getInstituteId());
//        return true;
//    }
//
//    private boolean isValidPurchasedProducts(List<PurchasedProduct> purchasedProducts) {
//        final Set<PurchasedProduct> purchasedProductSet = new HashSet<>();
//        for (final PurchasedProduct purchasedProduct : purchasedProducts) {
//            if (purchasedProductSet.contains(purchasedProduct)) {
//                return false;
//            }
//            purchasedProductSet.add(purchasedProduct);
//        }
//        return true;
//    }
//
//    public TransactionSummary getTransactionDetails(int instituteId, UUID transactionId) {
//        if (transactionId == null && instituteId < 0) {
//            return null;
//        }
//        return productTransactionsDao.getTransactionDetails(instituteId, transactionId);
//    }
//
//    public TransactionSummary getTransactionDetails(UUID transactionId) {
//        if (transactionId == null) {
//            return null;
//        }
//        return productTransactionsDao.getTransactionDetails(transactionId);
//    }
//
//    public TransactionsStatistics getTransactionsStatsDayWise(int instituteId, int start, int end) {
//        TimeZone timeZone = User.DFAULT_TIMEZONE;
//        TransactionsStatistics transactionsStatistics = productTransactionsDao.getTransactionsStatsDayWise(instituteId, start, end, timeZone);
//        if (transactionsStatistics == null) {
//            logger.error("Invalid transactionsStatistics for instituteId {}, start {}, end {}", instituteId, start, end);
//            return null;
//        }
//        int startDay = DateUtils.getDayStart(start, timeZone);
//        int endDay = DateUtils.getDayStart(end, timeZone);
//        int day = startDay;
//        while (day <= endDay) {
//            if (!transactionsStatistics.getPerDaySaleAmount().containsKey(day)) {
//                transactionsStatistics.getPerDaySaleAmount().put(day, 0d);
//            }
//            if (!transactionsStatistics.getPerDayPurchaseAmount().containsKey(day)) {
//                transactionsStatistics.getPerDayPurchaseAmount().put(day, 0d);
//            }
//            if (!transactionsStatistics.getPerDaySaleQuanitity().containsKey(day)) {
//                transactionsStatistics.getPerDaySaleQuanitity().put(day, 0);
//            }
//            if (!transactionsStatistics.getPerDayPurchaseQuanitity().containsKey(day)) {
//                transactionsStatistics.getPerDayPurchaseQuanitity().put(day, 0);
//            }
//            day = DateUtils.getNextDayStart(day, timeZone);
//        }
//        return transactionsStatistics;
//    }
//
//    public List<TransactionMetaData> getTransactionsMetaData(int instituteId) {
//        return productTransactionsDao.getTransactionsMetaData(instituteId);
//    }
//
//    public UUID returnSalesTransaction(int instituteId, ReturnSalesOrder returnSalesOrder) {
//        if (instituteId <= 0 || returnSalesOrder == null || returnSalesOrder.getTransactionId() == null) {
//            logger.error("Invalid instituteId {} or returnSalesOrder {}", instituteId, returnSalesOrder);
//            return null;
//        }
//        final InventoryPreferences inventoryPreferences = userPreferenceSettings
//                .getInventoryPreferences(instituteId);
//
//        if (CollectionUtils.isEmpty(inventoryPreferences.getInventoryInstitutesScope())) {
//            logger.error("Inventory is not enabled for given institute {} ", instituteId);
//            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER,
//                    "Inventory is not enabled for institute"));
//        }
//
//        TransactionSummary transactionSummary = getTransactionDetails(returnSalesOrder.getTransactionId());
//        int transactionInstituteId = transactionSummary.getInstituteId();
//        if (!inventoryPreferences.getInventoryInstitutesScope().contains(transactionInstituteId)) {
//            logger.error("Not authorized to perform transaction for transactionInstituteId {} given institute {} ", transactionInstituteId, instituteId);
//            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER,
//                    "Not authorized to perform cancel transaction for this order"));
//        }
//
//        if (!isTransactionWithinRange(returnSalesOrder.getReturnDate())) {
//            throw new ApplicationException(
//                    new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Transaction out of valid range"));
//        }
//
//        if (transactionSummary.getTransactionDate() > returnSalesOrder.getReturnDate()) {
//            logger.error("Original transaction date {} >= return date {}", transactionSummary.getTransactionDate(), returnSalesOrder.getReturnDate());
//            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER,
//                    "Return date should be greater than the original sale date"));
//        }
//
//
//        try {
//            final UUID salesReturnTransactionId = transactionTemplate.execute(new TransactionCallback<UUID>() {
//
//                @Override
//                public UUID doInTransaction(TransactionStatus status) {
//                    UUID salesReturnTransactionId = productTransactionsDao.addSalesReturnTransactionNonAtomic(transactionInstituteId, returnSalesOrder, transactionSummary);
//                    if (salesReturnTransactionId == null) {
//                        throw new EmbrateRunTimeException("Unable to return sales order");
//                    }
//
//                    Map<String, Object> metadata = transactionSummary.getMetadata();
//                    if (metadata == null) {
//                        metadata = new HashMap<>();
//                    }
//                    metadata.put(ORDER_RETURNED, true);
//                    metadata.put(RETURN_ORDER_TRANSACTION_ID, salesReturnTransactionId.toString());
//
//                    if (productTransactionsDao.updateTransactionMetadata(returnSalesOrder.getTransactionId(), metadata)) {
//                        return salesReturnTransactionId;
//                    }
//                    logger.error("Unable to update sales order for return transaction {} for {}", salesReturnTransactionId, returnSalesOrder.getTransactionId());
//                    throw new EmbrateRunTimeException("Unable to update sales order for return transaction");
//                }
//            });
//            return salesReturnTransactionId;
//        } catch (final ApplicationException e) {
//            throw new ApplicationException(e.getErrorResponse());
//        } catch (final Exception e) {
//            logger.error("Unable to process sales return order", e);
//            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, e.getMessage()));
//        }
//    }
//
	public boolean deleteTransaction(int instituteId, UUID userId, UUID transactionId) {
		return deleteTransaction(instituteId, userId, transactionId, false);
	}

	public boolean deleteTransaction(int instituteId, UUID userId, UUID transactionId, boolean skipAuth) {
		if (instituteId <= 0 || transactionId == null) {
			logger.error("Invalid instituteId {} or transactionId {}", instituteId, transactionId);
			return false;
		}

		if (!skipAuth) {
			// TODO : add user permission for delete
			userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.DELETE_TRANSACTIONS);
		}

		return inventoryTransactionsDao.cancelTransaction(getOutletId(instituteId), userId, transactionId);
	}
//
//    // TODO : add checks to validate the transactions. Quantity and product
//    // available or not etc
//    public UUID addSalesTransaction(SalesOrderPayload salesOrderPayload) {
//
//        if (salesOrderPayload == null || salesOrderPayload.getInstituteId() <= 0) {
//            throw new ApplicationException(
//                    new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Empty Sales Order"));
//        }
//
//        final InventoryPreferences inventoryPreferences = userPreferenceSettings
//                .getInventoryPreferences(salesOrderPayload.getInstituteId());
//
//        if (CollectionUtils.isEmpty(inventoryPreferences.getInventoryInstitutesScope())) {
//            logger.error("Inventory is not enabled for given institute {} ", salesOrderPayload.getInstituteId());
//            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER,
//                    "Inventory is not enabled for institute"));
//        }
//
//        if (!SalesOrderPayload.isValid(salesOrderPayload)) {
//            return null;
//        }
//
//        try {
//            final UUID salesTransactionId = transactionTemplate.execute(new TransactionCallback<UUID>() {
//
//                @Override
//                public UUID doInTransaction(TransactionStatus status) {
//
//                    validateSalesOrderAndUpdateUserInstituteId(salesOrderPayload, inventoryPreferences);
//
//                    final List<PurchasedProduct> purchasedProductsInGroup = getIndividualProductsFromGroup(
//                            salesOrderPayload);
//                    final List<PurchasedProduct> allPurchasedProducts = mergePurchasedProducts(purchasedProductsInGroup,
//                            salesOrderPayload.getPurchasedProducts());
//
//                    final SalesOrder salesOrder = new SalesOrder(salesOrderPayload.getInstituteId(),
//                            salesOrderPayload.getInventoryTransactionType(), salesOrderPayload.getSoldBy(),
//                            salesOrderPayload.getInventoryUserType(), salesOrderPayload.getPurchasedBy(),
//                            salesOrderPayload.getReturnedToSellerId(), salesOrderPayload.getReference(),
//                            salesOrderPayload.getEmail(), salesOrderPayload.getTransactionDate(),
//                            salesOrderPayload.getPaymentStatus(), salesOrderPayload.getDescription(),
//                            salesOrderPayload.getAdditionalCost(), salesOrderPayload.getAdditionalDiscount(),
//                            salesOrderPayload.getTransactionMode(), salesOrderPayload.isUseWallet(),
//                            salesOrderPayload.getUsedWalletAmount(), salesOrderPayload.getWalletCreditAmount(),
//                            salesOrderPayload.getPaidAmount(), allPurchasedProducts);
//                    salesOrder.setInventoryUserInstituteId(salesOrderPayload.getInventoryUserInstituteId());
//
//                    if (salesOrder.getInventoryUserType() == InventoryUserType.STUDENT && salesOrder.isUseWallet()) {
//
//                        final Double walletAmount = studentManager
//                                .getWalletAmount(UUID.fromString(salesOrder.getPurchasedBy()), DBLockMode.FOR_UPDATE);
//                        if (walletAmount == null) {
//                            logger.error("Invalid wallet amount for student {}", salesOrder.getPurchasedBy());
//                            throw new EmbrateRunTimeException("Invalid wallet amount for student ");
//                        }
//
//                        validateWalletUsage(salesOrder, inventoryPreferences, walletAmount.doubleValue());
//
//                    } else if (!salesOrder.isUseWallet()) {
//
//                        final double netPaybleAmount = salesOrder.getNetPaybleAmount();
//
//                        final double paidAmount = salesOrder.getPaidAmount() == null ? 0d
//                                : salesOrder.getPaidAmount().doubleValue();
//
//                        validateAmountPaid(netPaybleAmount, paidAmount, 0d, 0d, salesOrder.getPurchasedBy());
//                    } else {
//                        logger.error("Wallet usage not supported, institute {}", salesOrderPayload.getInstituteId());
//                        throw new EmbrateRunTimeException("Wallet usage not supported");
//                    }
//
//                    return productTransactionsDao.addSalesTransactionNonAtomic(salesOrder);
//                }
//            });
//            return salesTransactionId;
//        } catch (final ApplicationException e) {
//            throw new ApplicationException(e.getErrorResponse());
//        } catch (final Exception e) {
//            logger.error("Unable to process sales order", e);
//            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, e.getMessage()));
//        }
//    }
//
//    private void validateSalesOrderAndUpdateUserInstituteId(SalesOrderPayload salesOrderPayload,
//                                                            final InventoryPreferences inventoryPreferences) {
//        //Default init
//        salesOrderPayload.setInventoryUserInstituteId(salesOrderPayload.getInstituteId());
//
//        if (!isTransactionWithinRange(salesOrderPayload.getTransactionDate())) {
//            throw new ApplicationException(
//                    new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Transaction out of valid range"));
//        }
//        if (!isValidPurchasedProducts(salesOrderPayload.getPurchasedProducts())) {
//            throw new ApplicationException(
//                    new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Duplicate products in sales order"));
//        }
//        if (salesOrderPayload.getInventoryTransactionType() == InventoryTransactionType.RETURN
//                && !isValidSeller(salesOrderPayload.getInstituteId(), salesOrderPayload.getReturnedToSellerId())) {
//            throw new ApplicationException(
//                    new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Seller does not exists."));
//        }
//
//        if (salesOrderPayload.getInventoryUserType() == InventoryUserType.STUDENT
//                && !validateStudentAndUpdateInstituteId(inventoryPreferences, salesOrderPayload)) {
//            throw new ApplicationException(
//                    new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Seller does not exists."));
//        }
//        if (salesOrderPayload.getInventoryUserType() == InventoryUserType.STAFF
//                && !validateStaffAndUpdateInstituteId(inventoryPreferences, salesOrderPayload)) {
//            throw new ApplicationException(
//                    new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Seller does not exists."));
//        }
//        if (salesOrderPayload.getInventoryTransactionType() == InventoryTransactionType.RETURN
//                && !CollectionUtils.isEmpty(salesOrderPayload.getPurchasedProductGroups())) {
//            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER,
//                    "Product groups are not supported for returning items"));
//        }
//
//        if (salesOrderPayload.isUseWallet()
//                && salesOrderPayload.getInventoryTransactionType() != InventoryTransactionType.SALE) {
//            logger.error("Wallet can be used only for sale, institute {} ", salesOrderPayload.getInstituteId());
//            throw new ApplicationException(
//                    new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Wallet can be used only for sale"));
//        }
//
//        if (salesOrderPayload.isUseWallet() && !inventoryPreferences.isAllowWalletUsage()) {
//            logger.error("Wallet usage for inventory is not enabled for institute {} ",
//                    salesOrderPayload.getInstituteId());
//            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER,
//                    "Wallet usage for inventory is not enabled for institute"));
//        }
//
//        if (salesOrderPayload.isUseWallet() && salesOrderPayload.getInventoryUserType() != InventoryUserType.STUDENT) {
//            logger.error("Wallet transactions not allowed other than student customer, institute {} ",
//                    salesOrderPayload.getInstituteId());
//            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER,
//                    "Wallet transactions not allowed other than student customer"));
//        }
//
//        if (salesOrderPayload.isUseWallet() && (salesOrderPayload.getUsedWalletAmount() == null
//                || Double.compare(salesOrderPayload.getUsedWalletAmount(), 0d) < 0)) {
//            logger.error("Invalid wallet transactions amount, institute {} ", salesOrderPayload.getInstituteId());
//            throw new ApplicationException(
//                    new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Invalid wallet transactions amount"));
//        }
//
//        if (salesOrderPayload.isUseWallet() && salesOrderPayload.getWalletCreditAmount() != null
//                && Double.compare(salesOrderPayload.getWalletCreditAmount(), 0d) < 0) {
//            logger.error("Invalid wallet credit amount, institute {} ", salesOrderPayload.getInstituteId());
//            throw new ApplicationException(
//                    new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Invalid wallet credit amount"));
//        }
//
//        if (salesOrderPayload.getPaidAmount() != null && Double.compare(salesOrderPayload.getPaidAmount(), 0d) < 0) {
//            logger.error("Invalid paid amount, institute {} ", salesOrderPayload.getInstituteId());
//            throw new ApplicationException(
//                    new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Invalid paid amount"));
//        }
//
//    }
//
//    private void validateWalletUsage(SalesOrder salesOrder, final InventoryPreferences inventoryPreferences,
//                                     double walletAmount) {
//
//        if (Double.compare(salesOrder.getUsedWalletAmount(), 0d) > 0
//                && Double.compare(walletAmount, salesOrder.getUsedWalletAmount()) < 0) {
//            logger.error("Insufficient wallet amount {}, transaction wallet amount {}, for user {}", walletAmount,
//                    salesOrder.getUsedWalletAmount(), salesOrder.getPurchasedBy());
//            throw new ApplicationException(
//                    new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Insufficient wallet amount for user"));
//        }
//
//        if (salesOrder.getWalletCreditAmount() != null && Double.compare(salesOrder.getWalletCreditAmount(), 0d) > 0) {
//            if (!inventoryPreferences.isAllowWalletBasedCredit()) {
//                logger.error("Credit on wallet for inventory is not enabled for institute {} ",
//                        salesOrder.getInstituteId());
//                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER,
//                        "Credit on wallet for inventory is not enabled for institute"));
//            }
//
//            final Double creditLimit = inventoryPreferences.getWalletBasedCreditLimit();
//
//            if (creditLimit == null || Double.compare(creditLimit, 0d) < 0) {
//                logger.error("Invalid credit limit on wallet for inventory, institute {} ",
//                        salesOrder.getInstituteId());
//                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER,
//                        "Invalid credit limit on wallet for inventory in institute"));
//            }
//
//            if (Double.compare(walletAmount - salesOrder.getUsedWalletAmount().doubleValue(), 0d) > 0) {
//                logger.error("Wallet will not be empty. Invalid credit requirement, user {} ",
//                        salesOrder.getPurchasedBy());
//                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER,
//                        "Invalid credit requirement as amount is available in wallet."));
//            }
//
//            if (Double.compare(-creditLimit, walletAmount - salesOrder.getUsedWalletAmount().doubleValue()
//                    - salesOrder.getWalletCreditAmount()) > 0) {
//                logger.error("Max credit value {}. Cannot give credit of {} in this transaction, user {} ", creditLimit,
//                        salesOrder.getWalletCreditAmount(), salesOrder.getPurchasedBy());
//                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER,
//                        "Exceeding max credit value " + creditLimit + " in this transaction."));
//            }
//        }
//
//        final double netPaybleAmount = salesOrder.getNetPaybleAmount();
//
//        final double paidAmount = salesOrder.getPaidAmount() == null ? 0d : salesOrder.getPaidAmount().doubleValue();
//
//        final double usedWalletAmount = salesOrder.getUsedWalletAmount().doubleValue();
//
//        final double creditAmount = salesOrder.getWalletCreditAmount() == null ? 0d
//                : salesOrder.getWalletCreditAmount().doubleValue();
//
//        validateAmountPaid(netPaybleAmount, paidAmount, usedWalletAmount, creditAmount, salesOrder.getPurchasedBy());
//
//    }
//
//    private void validateAmountPaid(double netPaybleAmount, double paidAmount, double usedWalletAmount,
//                                    double creditAmount, String purchasedBy) {
//
//        final double netPaidAmount = paidAmount + usedWalletAmount + creditAmount;
//        if (Double.compare(netPaidAmount, netPaybleAmount) != 0) {
//            logger.error("Invalid total paid amount {}, netPaybleAmount {} , user {} ", netPaidAmount, netPaybleAmount,
//                    purchasedBy);
//            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER,
//                    "Invalid total paid amount " + netPaidAmount + ". Expected amount is " + netPaybleAmount));
//        }
//    }
//
//    private List<PurchasedProduct> mergePurchasedProducts(List<PurchasedProduct> purchasedProductsInGroup,
//                                                          List<PurchasedProduct> individualPurchasedProducts) {
//        final Map<UUID, PurchasedProduct> mergedPurchasedProducts = new HashMap<>();
//        final List<PurchasedProduct> allPurchasedProducts = new ArrayList<>(
//                purchasedProductsInGroup.size() + individualPurchasedProducts.size());
//        allPurchasedProducts.addAll(individualPurchasedProducts);
//        allPurchasedProducts.addAll(purchasedProductsInGroup);
//        for (final PurchasedProduct purchasedProduct : allPurchasedProducts) {
//            if (!mergedPurchasedProducts.containsKey(purchasedProduct.getSkuId())) {
//                mergedPurchasedProducts.put(purchasedProduct.getSkuId(),
//                        new PurchasedProduct(purchasedProduct.getSkuId(), purchasedProduct.getProductName(),
//                                purchasedProduct.getQuantity(), purchasedProduct.getTotalPrice(),
//                                purchasedProduct.getTotalDiscount(), purchasedProduct.getTotalTax()));
//                continue;
//            }
//            final PurchasedProduct existingPurchasedProduct = mergedPurchasedProducts.get(purchasedProduct.getSkuId());
//            existingPurchasedProduct
//                    .setQuantity(existingPurchasedProduct.getQuantity() + purchasedProduct.getQuantity());
//            existingPurchasedProduct
//                    .setTotalPrice(existingPurchasedProduct.getTotalPrice() + purchasedProduct.getTotalPrice());
//            existingPurchasedProduct.setTotalDiscount(
//                    existingPurchasedProduct.getTotalDiscount() + purchasedProduct.getTotalDiscount());
//            existingPurchasedProduct
//                    .setTotalTax(existingPurchasedProduct.getTotalTax() + purchasedProduct.getTotalTax());
//        }
//        return new ArrayList<>(mergedPurchasedProducts.values());
//    }
//
//    private List<PurchasedProduct> getIndividualProductsFromGroup(SalesOrderPayload salesOrderPayload) {
//        final List<PurchasedProduct> purchasedProductsInGroup = new ArrayList<>();
//        if (CollectionUtils.isEmpty(salesOrderPayload.getPurchasedProductGroups())) {
//            return purchasedProductsInGroup;
//        }
//        final List<UUID> groupIds = new ArrayList<>(salesOrderPayload.getPurchasedProductGroups().size());
//        final Map<UUID, PurchasedProductGroup> purchasedProductGroupMap = new HashMap<>();
//        for (final PurchasedProductGroup purchasedProductGroup : salesOrderPayload.getPurchasedProductGroups()) {
//            groupIds.add(purchasedProductGroup.getGroupId());
//            purchasedProductGroupMap.put(purchasedProductGroup.getGroupId(), purchasedProductGroup);
//        }
//        final List<ProductGroupData> productGroupDataList = productGroupManager
//                .getProductGroupByGroupIds(salesOrderPayload.getInstituteId(), groupIds);
//        for (final ProductGroupData productGroupData : productGroupDataList) {
//            if (!purchasedProductGroupMap.containsKey(productGroupData.getProductGroupBasicInfo().getGroupId())) {
//                continue;
//            }
//
//            final PurchasedProductGroup purchasedProductGroup = purchasedProductGroupMap
//                    .get(productGroupData.getProductGroupBasicInfo().getGroupId());
//
//            final double groupQuantity = purchasedProductGroup.getQuantity();
//            final double totalGroupSellingPrice = groupQuantity * productGroupData.getSellingPrice();
//            final double totalGroupMRP = groupQuantity * productGroupData.getMrp();
//
//            for (final ProductDataQuantity productDataQuantity : productGroupData.getProductDataQuantityList()) {
//                final ProductDetails productDetails = productDataQuantity.getProductDetails();
//
//                final double totalProductQuantity = groupQuantity * productDataQuantity.getQuantity();
//                final double totalProductPrice = groupQuantity * productDataQuantity.getTotalSellingPrice();
//                final double totalMRP = groupQuantity * productDataQuantity.getMRPTotal();
//                final double itemLevelDiscount = totalMRP - totalProductPrice;
//                final double discountedPriceRatio = totalGroupSellingPrice <= 0 ? 0
//                        : totalProductPrice / totalGroupSellingPrice;
//                final double mrpRatio = totalGroupMRP <= 0 ? 0 : totalMRP / totalGroupMRP;
//
//                // Discount is applied on top of discounted amount of group.
//                // Tax is applied on MRP
//                purchasedProductsInGroup.add(new PurchasedProduct(productDetails.getSkuId(),
//                        productDetails.getProductName(), totalProductQuantity, totalMRP,
//                        itemLevelDiscount + purchasedProductGroup.getTotalDiscount() * discountedPriceRatio,
//                        purchasedProductGroup.getTotalTax() * mrpRatio));
//            }
//
//        }
//        return purchasedProductsInGroup;
//    }
}
