package com.embrate.cloud.core.lib.dashboards;

import com.embrate.cloud.core.api.dashboards.InstituteStats;
import com.lernen.cloud.core.api.attendance.StudentDateAttendanceDetails;
import com.lernen.cloud.core.api.fees.payment.FeePaymentAggregatedData;
import com.lernen.cloud.core.api.fees.payment.FeePaymentTransactionMetaData;
import com.lernen.cloud.core.api.fees.payment.FeePaymentTransactionStatus;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.organisation.Organisation;
import com.lernen.cloud.core.api.staff.StaffStats;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentAdmissionStats;
import com.lernen.cloud.core.api.student.StudentStatus;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.transport.TransportStats;
import com.lernen.cloud.core.lib.transport.configuration.TransportAssignmentManager;
import com.lernen.cloud.core.lib.attendance.AttendanceManager;
import com.lernen.cloud.core.lib.fees.payment.FeePaymentManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.managers.UserManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.lib.staff.StaffManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.Pair;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.UUID;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @created_at 26/06/23 : 11:41
 **/
public class DashboardManager {

    private static final Logger logger = LogManager.getLogger(DashboardManager.class);

    private final InstituteManager instituteManager;

    private final StudentManager studentManager;

    private final AttendanceManager attendanceManager;

    private final FeePaymentManager feePaymentManager;

    private final UserManager userManager; 

    private final StaffManager staffManager;

    private final TransportAssignmentManager transportAssignmentManager;

    public DashboardManager(InstituteManager instituteManager, StudentManager studentManager, AttendanceManager attendanceManager, FeePaymentManager feePaymentManager,
                            UserManager userManager, StaffManager staffManager, TransportAssignmentManager transportAssignmentManager) {
        this.instituteManager = instituteManager;
        this.studentManager = studentManager;
        this.attendanceManager = attendanceManager;
        this.feePaymentManager = feePaymentManager;
        this.userManager = userManager;
        this.staffManager = staffManager;
        this.transportAssignmentManager = transportAssignmentManager;
    }

    public InstituteStats getInstituteStats(int instituteId, int academicSessionId, Integer date) {

        StopWatch stopWatch = new StopWatch();

        date = DateUtils.getDayStart(date, DateUtils.DEFAULT_TIMEZONE);

        stopWatch.start();
        final Institute institute = instituteManager.getInstitute(instituteId);
        stopWatch.stop();
        logger.info("get institute time = {}", stopWatch.getLastTaskTimeMillis());

        stopWatch.start();
        final List<Standard> standardPayloadList = instituteManager.getInstituteStandardDetails(instituteId,
                academicSessionId);
        stopWatch.stop();
        logger.info("get standardPayloadList = {}", stopWatch.getLastTaskTimeMillis());

        stopWatch.start();
        final Pair<FeePaymentAggregatedData, FeePaymentAggregatedData> feeStatsPair  = feePaymentManager
                .getClassPaymentStatsAndTodayStats(instituteId, academicSessionId, date);
        FeePaymentAggregatedData feePaymentAggregatedData = feeStatsPair == null ? null : feeStatsPair.getFirst();
        final FeePaymentAggregatedData dateWiseFeePaymentAggregatedData =  feeStatsPair == null ? null : feeStatsPair.getSecond();
        stopWatch.stop();
        logger.info("get feeStatsPair = {}", stopWatch.getLastTaskTimeMillis());

        stopWatch.start();
        List<Student> enrollRelievePendingStudentList = studentManager.getStudentsInAcademicSession(instituteId, academicSessionId, Arrays.asList(StudentStatus.ENROLLED, StudentStatus.RELIEVED, StudentStatus.ENROLMENT_PENDING, StudentStatus.NSO));
        List<Student> enrollStudentList = new ArrayList<>();
        List<Student> enrollPendingStudentList = new ArrayList<>();
        int relievedStudentsCounts = getEnrolledStudents(enrollRelievePendingStudentList, enrollStudentList, enrollPendingStudentList);
        final StudentAdmissionStats studentAdmissionStats = studentManager
                .getStudentAdmissionStats(instituteId, academicSessionId, enrollRelievePendingStudentList, standardPayloadList);
        Pair<List<Student>, List<Student>> birthdayLists = getBirthDayStudentsList(enrollStudentList);
        List<Student> todayBirthdays = new ArrayList<>();
        List<Student> upcomingBirthdays = new ArrayList<>();
        if (birthdayLists != null) {
            todayBirthdays = birthdayLists.getFirst();
            upcomingBirthdays = birthdayLists.getSecond();
        }
        stopWatch.stop();
        logger.info("get studentAdmissionStats & birthday = {}", stopWatch.getLastTaskTimeMillis());

        stopWatch.start();
        final StudentDateAttendanceDetails studentDateAttendanceDetails = attendanceManager.getStudentDateAttendanceDetails(
                instituteId, academicSessionId, date, null);
        stopWatch.stop();
        logger.info("get studentDateAttendanceDetails = {}", stopWatch.getLastTaskTimeMillis());

        stopWatch.start();
        final List<FeePaymentTransactionMetaData> feePaymentTransactionMetaDatas = feePaymentManager
                .getLastNTransactions(instituteId, academicSessionId, FeePaymentTransactionStatus.ACTIVE, 10);
        stopWatch.stop();
        logger.info("get studentDateAttendanceDetails = {}", stopWatch.getLastTaskTimeMillis());

        stopWatch.start();
        final StaffStats staffStats = staffManager.getStaffStats(instituteId, true, date);
        stopWatch.stop();
        logger.info("get staffStats = {}", stopWatch.getLastTaskTimeMillis());

        stopWatch.start();
        final TransportStats transportStats = getTransportStats(instituteId, academicSessionId, CollectionUtils.isEmpty(enrollStudentList) ? 0 : enrollStudentList.size());
        stopWatch.stop();
        logger.info("get transportStats = {}", stopWatch.getLastTaskTimeMillis());

        return new InstituteStats(institute, standardPayloadList, feePaymentAggregatedData, dateWiseFeePaymentAggregatedData,
                studentAdmissionStats, studentDateAttendanceDetails,
                CollectionUtils.isEmpty(feePaymentTransactionMetaDatas) ? null : feePaymentTransactionMetaDatas,
                todayBirthdays, upcomingBirthdays, staffStats, transportStats, relievedStudentsCounts);

    }

    private int getEnrolledStudents(List<Student> studentsList,  List<Student> students, List<Student> enrollPendingStudentList) {
           int relievedStudentsCount = 0;
            for(Student student : studentsList){
                if(student == null) {
                    continue;
                }
                if(student.getStudentStatus() == StudentStatus.ENROLLED){
                    enrollPendingStudentList.add(student);
                    students.add(student);
                } else if(student.getStudentStatus() == StudentStatus.ENROLMENT_PENDING){
                    enrollPendingStudentList.add(student);
                } else if(student.getStudentStatus() == StudentStatus.RELIEVED){
                    relievedStudentsCount += 1;
                }
            }
            return relievedStudentsCount;
    }

    private Pair<List<Student>, List<Student>> getBirthDayStudentsList(List<Student> students) {
        if(CollectionUtils.isEmpty(students)){
            return null;
        }
        List<Student> todayBirthdays = new ArrayList<>();
        List<Student> upcomingBirthdays = new ArrayList<>();

        /**
         * we will be creating a list with next seven days as dd-MM, without year.
         * then we will compare each student DOB (formatted in dd-MM)
         * if they are same then birthday lies in expected date, ow not
         */

        int now = DateUtils.getDayStart(DateUtils.now(), DateUtils.DEFAULT_TIMEZONE);
        String todayDateString = DateUtils.getFormattedDate(now, "dd-MM", DateUtils.DEFAULT_TIMEZONE);
        List<String> dateStringList = new ArrayList<>();
        for(int i = 1; i <= 7; i++) {
            int dateInt = DateUtils.addDays(now, i);
            String dateString = DateUtils.getFormattedDate(dateInt, "dd-MM", DateUtils.DEFAULT_TIMEZONE);
            dateStringList.add(dateString);
        }
        for (Student student : students) {
            if(student == null) {
                continue;
            }
            Integer dob = student.getStudentBasicInfo() == null ? null : student.getStudentBasicInfo().getDateOfBirth();
            if (dob == null || dob <= 0) {
                continue;
            }

            int birthdayInt = DateUtils.getDayStart(dob, DateUtils.DEFAULT_TIMEZONE);
            String birthdayString = DateUtils.getFormattedDate(birthdayInt, "dd-MM", DateUtils.DEFAULT_TIMEZONE);

            if(birthdayString.equalsIgnoreCase(todayDateString)) {
                todayBirthdays.add(student);
                continue;
            }

            if(dateStringList.contains(birthdayString)) {
                upcomingBirthdays.add(student);
                continue;
            }

//            if (birthday.getMonthOfYear() == now.getMonthOfYear() && birthday.getDayOfMonth() == now.getDayOfMonth()) {
//                todayBirthdays.add(student);
//                continue;
//            }
//
//            if (birthday.getMonthOfYear() == now.getMonthOfYear() && birthday.getDayOfMonth() > now.getDayOfMonth()
//                    && birthday.getDayOfMonth() <= nextSevenDays.getDayOfMonth()) {
//                upcomingBirthdays.add(student);
//                continue;
//            }
        }
        
        Collections.sort(todayBirthdays, getStudentComparator());
        Collections.sort(upcomingBirthdays, getStudentComparator());

        return new Pair<>(todayBirthdays, upcomingBirthdays);
    }


    private Comparator<Student> getStudentComparator() {
        return new Comparator<Student>() {
            @Override
            public int compare(Student s1, Student s2) {
                Integer dob1 = s1.getStudentBasicInfo().getDateOfBirth();
                Integer dob2 = s2.getStudentBasicInfo().getDateOfBirth();

                int dob1Month = DateUtils.getMonth(dob1, DateUtils.DEFAULT_TIMEZONE);
                int dob1Day = DateUtils.getDay(dob1, DateUtils.DEFAULT_TIMEZONE);

                int dob2Month = DateUtils.getMonth(dob2, DateUtils.DEFAULT_TIMEZONE);
                int dob2Day = DateUtils.getDay(dob2, DateUtils.DEFAULT_TIMEZONE);

                int compare = dob1Month - dob2Month;
                
                if (compare != 0) {
                    return compare;
                }
                compare = dob1Day - dob2Day;
                if (compare != 0) {
                        return compare;
                }
                int standard1 = s1.getStudentAcademicSessionInfoResponse().getStandard().getLevel();
                int standard2 = s2.getStudentAcademicSessionInfoResponse().getStandard().getLevel();
                int standardCompare = standard1-standard2;
                if(standardCompare != 0){
                    return standardCompare;
                }
                final List<StandardSections> section1 = s1.getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList();
                final List<StandardSections> section2 = s2.getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList();
                if(!CollectionUtils.isEmpty(section1) && !CollectionUtils.isEmpty(section2) && !StringUtils.isBlank(section1.get(0).getSectionName()) 
                        && !StringUtils.isBlank(section2.get(0).getSectionName())){
                        final int sectionCompare = section1.get(0).getSectionName().compareToIgnoreCase(section2.get(0).getSectionName());
                        if(sectionCompare != 0){
                            return sectionCompare;
                        }
                }

                return s1.getStudentBasicInfo().getName().compareTo(s2.getStudentBasicInfo().getName());
            }
        };
    }    

    private TransportStats getTransportStats(int instituteId, int academicSessionId,  int totalEnrolledStudentCount) {
       
        if (totalEnrolledStudentCount == 0) { 
            return null;
        }
        int totalTransportAssignedStudentCount = transportAssignmentManager.getTransportActiveStudentCount(instituteId, academicSessionId);
        int totalTransportUnassignedStudentCount = totalEnrolledStudentCount - totalTransportAssignedStudentCount;

        return new TransportStats(totalEnrolledStudentCount, totalTransportAssignedStudentCount, totalTransportUnassignedStudentCount);

    }

    public List<InstituteStats> getOrganizationStats(UUID organizationId, UUID userId, Integer date, Integer selectedAcademicSessionId) {

        List<InstituteStats> instituteStatsList = new ArrayList<>();

        if(organizationId == null || userId == null || date == null || date <= 0 || selectedAcademicSessionId == null || selectedAcademicSessionId <= 0) {
            return new ArrayList<>();
        }


        Organisation organisation = instituteManager.getOrganizationById(organizationId);
        User user = userManager.getUser(userId);
        if(organisation == null || CollectionUtils.isEmpty(organisation.getInstitutes())
                || user == null || CollectionUtils.isEmpty(user.getInstituteScope())) {
            return new ArrayList<>();
        }

        date = DateUtils.getDayStart(date, DateUtils.DEFAULT_TIMEZONE);

        List<Institute> instituteList = organisation.getInstitutes();
        List<Integer> instituteScopeList = user.getInstituteScope();

        if(CollectionUtils.isEmpty(instituteList) || CollectionUtils.isEmpty(instituteScopeList)) {
            return new ArrayList<>();
        }

        AcademicSession selectedAcademicSession = instituteManager.getAcademicSessionByAcademicSessionId(selectedAcademicSessionId);

        if(selectedAcademicSession == null) {
            return new ArrayList<>();
        }

        Map<Integer, Integer> instituteIdSessionIdMap = instituteManager.getAllInstituteSessionDetailsBySelectedSession(
                organisation, user, selectedAcademicSession);

        if(CollectionUtils.isEmpty(instituteIdSessionIdMap)) {
            return new ArrayList<>();
        }

        for(Institute institute : instituteList) {
            int instituteId = institute.getInstituteId();
            if(instituteId <= 0) {
                continue;
            }
            if(!instituteScopeList.contains(instituteId)) {
                continue;
            }

            Integer academicSessionId = instituteIdSessionIdMap.get(instituteId);
            if(academicSessionId == null || academicSessionId <= 0) {
                continue;
            }
            InstituteStats instituteStats = getInstituteStats(instituteId, academicSessionId, date);
            if(instituteStats != null) {
                instituteStatsList.add(instituteStats);
            }
        }
        return instituteStatsList;
    }
}