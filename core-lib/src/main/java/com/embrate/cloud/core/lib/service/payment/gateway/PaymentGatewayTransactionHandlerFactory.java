package com.embrate.cloud.core.lib.service.payment.gateway;

import com.embrate.cloud.core.api.service.payment.gateway.PGProcessTransactionPayload;
import com.embrate.cloud.core.api.service.payment.gateway.PaymentGatewayServiceProvider;
import com.embrate.cloud.core.api.service.payment.gateway.merchants.PGMerchantDetails;
import com.embrate.cloud.core.lib.service.payment.gateway.atom.AtomPaymentGatewayTransactionHandler;
import com.embrate.cloud.core.lib.service.payment.gateway.cashfree.CashFreePaymentGatewayService;
import com.embrate.cloud.core.lib.service.payment.gateway.cashfree.CashFreePaymentGatewayTransactionHandler;
import com.embrate.cloud.core.lib.service.payment.gateway.jodo.JodoPaymentGatewayTransactionHandler;
import com.embrate.cloud.core.lib.service.payment.gateway.razorpay.RazorpayPaymentGatewayTransactionHandler;
import com.lernen.cloud.core.utils.rest.RestAPIHandler;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */

public class PaymentGatewayTransactionHandlerFactory {

    private static final Logger logger = LogManager.getLogger(PaymentGatewayTransactionHandlerFactory.class);

    private final CashFreePaymentGatewayService cashFreePaymentGatewayService;
    private final String webhookSecret;

    public PaymentGatewayTransactionHandlerFactory(CashFreePaymentGatewayService cashFreePaymentGatewayService, String webhookSecret) {
        this.cashFreePaymentGatewayService = cashFreePaymentGatewayService;
        this.webhookSecret = webhookSecret;
    }

    public IPaymentGatewayTransactionHandler getPaymentGatewayTransactionHandler(PaymentGatewayServiceProvider serviceProvider, PGProcessTransactionPayload processTransactionPayload, PGMerchantDetails merchantDetails,
                                                                                 RestAPIHandler restAPIHandler){
        if(serviceProvider == null){
            logger.error("Invalid service provider");
            return null;
        }
        switch (serviceProvider){
            case CASH_FREE:
               return new CashFreePaymentGatewayTransactionHandler(cashFreePaymentGatewayService, processTransactionPayload, merchantDetails);
            case ATOM:
                return new AtomPaymentGatewayTransactionHandler(processTransactionPayload, merchantDetails);
            case JODO:
                return new JodoPaymentGatewayTransactionHandler(processTransactionPayload, merchantDetails, restAPIHandler);
            case RAZORPAY:
                return new RazorpayPaymentGatewayTransactionHandler(processTransactionPayload, merchantDetails, webhookSecret);
            default:
                logger.error("PG transaction handler is not supported for {} ", serviceProvider);
                return null;
        }
    }
}
