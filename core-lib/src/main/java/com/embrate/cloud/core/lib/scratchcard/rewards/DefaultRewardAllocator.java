package com.embrate.cloud.core.lib.scratchcard.rewards;

import com.embrate.cloud.core.api.scratchcard.RewardDetails;
import com.embrate.cloud.core.api.scratchcard.ScratchCardDetails;
import com.embrate.cloud.core.api.scratchcard.ScratchCardType;
import com.lernen.cloud.core.api.fees.payment.FeePaymentResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class DefaultRewardAllocator implements IRewardAllocator{

    private static final Logger logger = LogManager.getLogger(DefaultRewardAllocator.class);

    @Override
    public List<ScratchCardDetails> getAllocatedRewards(int instituteId, UUID studentId, FeePaymentResponse feePaymentResponse, List<RewardDetails> rewardDetailsList) {
        List<ScratchCardDetails> scratchCardDetailsList = new ArrayList<ScratchCardDetails>();
        for(RewardDetails rewardDetails : rewardDetailsList) {
            scratchCardDetailsList.add(new ScratchCardDetails(null, feePaymentResponse.getTransactionId(), studentId, ScratchCardType.INSTANT,
                    false, null, rewardDetails));
        }
        return CollectionUtils.isEmpty(scratchCardDetailsList) ? null : scratchCardDetailsList;
    }
}
