package com.embrate.cloud.core.lib.leave.management;

import com.embrate.cloud.core.api.leave.management.LeaveType;
import com.embrate.cloud.core.api.leave.management.balance.UserNetLeaveBalance;
import com.embrate.cloud.core.api.leave.management.policy.*;
import com.embrate.cloud.core.api.leave.management.policy.user.StaffLeavePolicy;
import com.embrate.cloud.core.api.leave.management.policy.user.UserLeavePolicy;
import com.embrate.cloud.core.api.leave.management.policy.user.UserLeavePolicyPayload;
import com.embrate.cloud.core.api.leave.management.policy.user.UserLeavePolicyTemplateAssignPayload;
import com.embrate.cloud.core.api.leave.management.transaction.*;
import com.embrate.cloud.dao.tier.leave.management.LeavePolicyTemplateDao;
import com.embrate.cloud.dao.tier.leave.management.UserLeavePolicyDao;
import com.lernen.cloud.core.api.common.EMonthDay;
import com.lernen.cloud.core.api.staff.FullStaffDetails;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.staff.StaffStatus;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.lib.staff.StaffManager;
import com.lernen.cloud.core.utils.DateUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

import static com.lernen.cloud.core.utils.leave.management.LeavePolicyUtils.validateLeaveTypePolicyDetails;

/**
 * <AUTHOR>
 */

public class UserLeavePolicyManager {
    private static final Logger logger = LogManager.getLogger(UserLeavePolicyManager.class);

    private final UserLeavePolicyDao userLeavePolicyDao;

    private final LeavePolicyTemplateDao leavePolicyTemplateDao;

    private final StaffManager staffManager;


    public UserLeavePolicyManager(UserLeavePolicyDao userLeavePolicyDao, LeavePolicyTemplateDao leavePolicyTemplateDao, StaffManager staffManager) {
        this.userLeavePolicyDao = userLeavePolicyDao;
        this.leavePolicyTemplateDao = leavePolicyTemplateDao;
        this.staffManager = staffManager;
    }

    public List<StaffLeavePolicy> searchStaffLeavePolicies(int instituteId, int academicSessionId, String searchText,
                                                           String categories, String departments, String designations) {
        if (instituteId <= 0 || academicSessionId <= 0) {
            logger.error("Invalid institute {}, session {}", instituteId, academicSessionId);
            return null;
        }

        List<FullStaffDetails> fullStaffDetailsList = staffManager.advanceSearchStaff(instituteId, searchText, StaffStatus.ONBOARD, null, categories, departments, designations);
        if (CollectionUtils.isEmpty(fullStaffDetailsList)) {
            logger.warn("No staff found for institute {} with required filters", instituteId);
            return new ArrayList<>();
        }

        List<UserLeavePolicy> userLeavePolicyList = userLeavePolicyDao.getUserLeavePolicies(instituteId, academicSessionId);
        Map<UUID, UserLeavePolicy> staffPolicyMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(userLeavePolicyList)) {
            for (UserLeavePolicy userLeavePolicy : userLeavePolicyList) {
                staffPolicyMap.put(userLeavePolicy.getUserId(), userLeavePolicy);
            }
        }

        List<StaffLeavePolicy> staffLeavePolicyList = new ArrayList<>();
        for (FullStaffDetails fullStaffDetails : fullStaffDetailsList) {
            UUID staffId = fullStaffDetails.getStaffId();
            UserLeavePolicy userLeavePolicy = staffPolicyMap.get(staffId);
            staffLeavePolicyList.add(new StaffLeavePolicy(fullStaffDetails, userLeavePolicy == null ? null : userLeavePolicy.getLeaveTypePolicyDetailsList()));
        }

        return staffLeavePolicyList;
    }


    public boolean assignStaffLeavePolicyTemplate(int instituteId, UserLeavePolicyTemplateAssignPayload userLeavePolicyTemplateAssignPayload) {
        if (instituteId <= 0 || userLeavePolicyTemplateAssignPayload == null || userLeavePolicyTemplateAssignPayload.getAcademicSessionId() <= 0 ||
                userLeavePolicyTemplateAssignPayload.getTemplateId() == null || CollectionUtils.isEmpty(userLeavePolicyTemplateAssignPayload.getUserList())) {
            logger.error("Invalid institute {} or payload {}", instituteId, userLeavePolicyTemplateAssignPayload);
            return false;
        }

        LeavePolicyTemplate leavePolicyTemplate = leavePolicyTemplateDao.getLeavePolicyTemplate(instituteId, userLeavePolicyTemplateAssignPayload.getTemplateId());
        if (leavePolicyTemplate == null || CollectionUtils.isEmpty(leavePolicyTemplate.getLeaveTypePolicyDetailsList())) {
            logger.error("No leave policy template found for {}, {}", instituteId, userLeavePolicyTemplateAssignPayload.getTemplateId());
            return false;
        }

        List<Staff> staffList = staffManager.getStaff(instituteId);
        if (CollectionUtils.isEmpty(staffList)) {
            logger.error("No staff found for template assignment {}, {}", instituteId, userLeavePolicyTemplateAssignPayload);
            return false;
        }
        Map<UUID, Staff> staffMap = new HashMap<>();
        for (Staff staff : staffList) {
            staffMap.put(staff.getStaffId(), staff);
        }

        for (UUID userId : userLeavePolicyTemplateAssignPayload.getUserList()) {
            if (!staffMap.containsKey(userId)) {
                logger.error("Staff not found {}, {}", instituteId, userId);
                return false;
            }
            Staff staff = staffMap.get(userId);
            if (staff.getStaffStatus() != StaffStatus.ONBOARD) {
                logger.error("Staff not onboard found {}, {}", instituteId, userId);
                return false;
            }
        }
        List<LeaveTypePolicyPayload> leaveTypePolicyPayloadList = new ArrayList<>();
        for (LeaveTypePolicyDetails leaveTypePolicyDetails : leavePolicyTemplate.getLeaveTypePolicyDetailsList()) {
            leaveTypePolicyPayloadList.add(new LeaveTypePolicyPayload(leaveTypePolicyDetails.getLeaveType().getLeaveTypeId(), leaveTypePolicyDetails.getLeaveTypePolicies(), leaveTypePolicyDetails.getMetadata()));
        }
        return userLeavePolicyDao.addUserLeavePolicies(instituteId, userLeavePolicyTemplateAssignPayload.getAcademicSessionId(), userLeavePolicyTemplateAssignPayload.getUserList(), leaveTypePolicyPayloadList);
    }

    public List<UserLeavePolicy> getUserLeavePolicies(int instituteId, int academicSessionId) {
        if (instituteId <= 0 || academicSessionId <= 0) {
            logger.error("Invalid institute {} or academicSessionId {}", instituteId, academicSessionId);
            return null;
        }
        return userLeavePolicyDao.getUserLeavePolicies(instituteId, academicSessionId);
    }

    public UserLeavePolicy getUserLeavePolicy(int instituteId, int academicSessionId, UUID userId) {
        if (instituteId <= 0 || userId == null) {
            logger.error("Invalid institute {} or userId {}", instituteId, userId);
            return null;
        }
        return userLeavePolicyDao.getUserLeavePolicy(instituteId, academicSessionId, userId);
    }


    public boolean updateUserLeavePolicy(int instituteId, UUID userId, UserLeavePolicyPayload userLeavePolicyPayload) {
        if (instituteId <= 0 || userId == null || userLeavePolicyPayload == null || userLeavePolicyPayload.getAcademicSessionId() <= 0 ||
                !validateLeaveTypePolicyDetails(userLeavePolicyPayload.getLeaveTypePolicyPayloadList())) {
            logger.error("Invalid institute {} or userId {} or payload {}", instituteId, userId, userLeavePolicyPayload);
            return false;
        }
        return userLeavePolicyDao.updateUserLeavePolicy(instituteId, userId, userLeavePolicyPayload);
    }

    public boolean deleteUserLeavePolicy(int instituteId, int academicSessionId, UUID userId) {
        if (instituteId <= 0 || academicSessionId <= 0 || userId == null) {
            logger.error("Invalid institute {} or academicSessionId {} or userId {}", instituteId, academicSessionId, userId);
            return false;
        }
        return userLeavePolicyDao.deleteUserLeavePolicy(instituteId, academicSessionId, userId);
    }

    public List<UserLeaveTransactionDetails> getUserLeaveTransactions(int instituteId, int academicSessionId) {
        return userLeavePolicyDao.getUserLeaveTransactions(instituteId, academicSessionId);
    }

    public List<UserLeaveTransactionDetails> getUserLeaveTransactions(int instituteId, int academicSessionId, UUID userId) {
        return userLeavePolicyDao.getUserLeaveTransactions(instituteId, academicSessionId, userId);
    }

    public UserNetLeaveBalance getUserLeaveBalance(int instituteId, int academicSessionId, UUID userId) {
        if (instituteId <= 0 || academicSessionId <= 0 || userId == null) {
            logger.error("Invalid instituteId {} or academicSessionId {} or userId {}",
                    instituteId, academicSessionId, userId);
            return null;
        }
        return userLeavePolicyDao.getUserLeaveBalance(instituteId, academicSessionId, userId);
    }

    public List<UserNetLeaveBalance> getAllUserLeaveBalance(int instituteId, int academicSessionId) {
        if (instituteId <= 0 || academicSessionId <= 0 ) {
            logger.error("Invalid instituteId {} or academicSessionId {}", instituteId, academicSessionId);
            return null;
        }
        return userLeavePolicyDao.getAllUserLeaveBalance(instituteId, academicSessionId);
    }

    public Set<UUID> computeUserLeaves(int instituteId, int academicSessionId, UUID userId, Integer computeDay) {
        List<UserLeaveSchedule> policyUserLeaveScheduleMap = computeLeavesBasedOnPolicy(instituteId, academicSessionId, userId, computeDay);
        List<UserLeaveTransactionSchedule> userLeaveTransactionScheduleMap = getLeavesBasedOnTransactions(instituteId, academicSessionId, userId);

        return updateUserLeaves(instituteId, academicSessionId, computeDay, Collections.singletonMap(userId, policyUserLeaveScheduleMap),
                Collections.singletonMap(userId, userLeaveTransactionScheduleMap));
    }

    public Set<UUID> computeUserLeaves(int instituteId, int academicSessionId, Integer computeDay) {
        Map<UUID, List<UserLeaveSchedule>> policyUserLeaveScheduleMap = computeLeavesBasedOnPolicy(instituteId, academicSessionId, computeDay);
        Map<UUID, List<UserLeaveTransactionSchedule>> userLeaveTransactionScheduleMap = getLeavesBasedOnTransactions(instituteId, academicSessionId);

        return updateUserLeaves(instituteId, academicSessionId, computeDay, policyUserLeaveScheduleMap, userLeaveTransactionScheduleMap);
    }

    private Set<UUID> updateUserLeaves(int instituteId, int academicSessionId, int computeDay, Map<UUID, List<UserLeaveSchedule>> policyUserLeaveScheduleMap, Map<UUID, List<UserLeaveTransactionSchedule>> userLeaveTransactionScheduleMap) {
        Set<UUID> userIds = new HashSet<>();
        if (MapUtils.isNotEmpty(policyUserLeaveScheduleMap)) {
            userIds.addAll(policyUserLeaveScheduleMap.keySet());
        }
        if (MapUtils.isNotEmpty(userLeaveTransactionScheduleMap)) {
            userIds.addAll(userLeaveTransactionScheduleMap.keySet());
        }

        List<UserLeaveUpdatePayload> allUserLeaveUpdatePayloadList = new ArrayList<>();
        for (UUID userId : userIds) {
            List<UserLeaveSchedule> policyUserLeaveScheduleList = policyUserLeaveScheduleMap.getOrDefault(userId, new ArrayList<>());
            List<UserLeaveTransactionSchedule> userLeaveTransactionScheduleList = userLeaveTransactionScheduleMap.getOrDefault(userId, new ArrayList<>());
            UserLeaveUpdatePayload userLeaveUpdatePayload = getUserLeaveUpdatePayload(instituteId, academicSessionId, userId, policyUserLeaveScheduleList, userLeaveTransactionScheduleList);
            allUserLeaveUpdatePayloadList.add(userLeaveUpdatePayload);
        }

        Set<UUID> newUUIDs = userLeavePolicyDao.updateUserLeaveTransactions(instituteId, academicSessionId, allUserLeaveUpdatePayloadList);
        if (newUUIDs == null) {
            logger.error("Failed to update user leave transactions for instituteId {}, academicSessionId {}, computeDay {}", instituteId, academicSessionId, computeDay);
            return null;
        }
        return newUUIDs;
    }

    private UserLeaveUpdatePayload getUserLeaveUpdatePayload(int instituteId, int academicSessionId, UUID userId,
                                                             List<UserLeaveSchedule> policyUserLeaveScheduleList,
                                                             List<UserLeaveTransactionSchedule> userLeaveTransactionScheduleList) {
        boolean policyUserLeaveSchedulePresent = CollectionUtils.isNotEmpty(policyUserLeaveScheduleList);
        boolean userLeaveTransactionSchedulePresent = CollectionUtils.isNotEmpty(userLeaveTransactionScheduleList);
        if (!policyUserLeaveSchedulePresent && !userLeaveTransactionSchedulePresent) {
            logger.info("No policy schedule and transactions are present. Skip any updates");
            return null;
        }

        Set<UUID> deleteTransactionIds = new HashSet<>();
        List<UserLeaveTransactionDetails> newTransactions = new ArrayList<>();

        policyUserLeaveScheduleList = policyUserLeaveScheduleList == null ? new ArrayList<>() : policyUserLeaveScheduleList;
        userLeaveTransactionScheduleList = userLeaveTransactionScheduleList == null ? new ArrayList<>() : userLeaveTransactionScheduleList;

        Map<String, UserLeaveTransactionSchedule> userLeaveTransactionScheduleMap = new HashMap<>();
        for (UserLeaveTransactionSchedule userLeaveTransactionSchedule : userLeaveTransactionScheduleList) {
            String key = getLeaveScheduleKey(userLeaveTransactionSchedule.getUserLeaveSchedule());
            userLeaveTransactionScheduleMap.put(key, userLeaveTransactionSchedule);
            deleteTransactionIds.add(userLeaveTransactionSchedule.getTransactionId());
        }

        for (UserLeaveSchedule policyUserLeaveSchedule : policyUserLeaveScheduleList) {
            String key = getLeaveScheduleKey(policyUserLeaveSchedule);
            if (userLeaveTransactionScheduleMap.containsKey(key)) {
                UserLeaveTransactionSchedule userLeaveTransactionSchedule = userLeaveTransactionScheduleMap.get(key);
                deleteTransactionIds.remove(userLeaveTransactionSchedule.getTransactionId());
            } else {
                /**
                 * currently only spporting user type = STAFF
                 */
                newTransactions.add(new UserLeaveTransactionDetails(new UserLeaveTransactionMetadata(null, academicSessionId,
                        userId, UserType.STAFF, policyUserLeaveSchedule.getTransactionType(), LeaveTransactionStatus.APPROVED,
                        policyUserLeaveSchedule.getCategory(), null, DateUtils.now(),
                        null, null, null, null, null),
                        Arrays.asList(new UserLeaveTypeTransactionData(new LeaveType(policyUserLeaveSchedule.getLeaveTypeId(), null, null, null, null, null, null),
                                policyUserLeaveSchedule.getDayStart(), policyUserLeaveSchedule.getLeaveCount()))));
            }
        }
        return new UserLeaveUpdatePayload(userId, deleteTransactionIds, newTransactions);
    }

    private String getLeaveScheduleKey(UserLeaveSchedule userLeaveSchedule) {
        return userLeaveSchedule.getCategory() + "|" + userLeaveSchedule.getTransactionType() + "|" + userLeaveSchedule.getLeaveTypeId() + "|" +
                userLeaveSchedule.getDayStart() + "|" + userLeaveSchedule.getLeaveCount();
    }

    public Map<UUID, List<UserLeaveSchedule>> computeLeavesBasedOnPolicy(int instituteId, int academicSessionId, Integer computeDay) {
        if (instituteId <= 0 || academicSessionId <= 0 || computeDay == null || computeDay <= 0) {
            logger.error("Invalid instituteId {} or academicSessionId {} or computeDay {}",
                    instituteId, academicSessionId, computeDay);
            return null;
        }
        List<UserLeavePolicy> userLeavePolicies = getUserLeavePolicies(instituteId, academicSessionId);
        if (userLeavePolicies == null || CollectionUtils.isEmpty(userLeavePolicies)) {
            logger.error("No leave policy configured for users of instituteId {}, academicSession {}", instituteId, academicSessionId);
            return null;
        }
        Map<UUID, UserLeavePolicy> userLeavePolicyMap = new HashMap<>();
        for (UserLeavePolicy userLeavePolicy : userLeavePolicies) {
            UUID userId = userLeavePolicy.getUserId();
            userLeavePolicyMap.put(userId, userLeavePolicy);
        }

        Map<UUID, List<UserLeaveSchedule>> userLeaveScheduleMap = new HashMap<>();
        for (Map.Entry<UUID, UserLeavePolicy> entry : userLeavePolicyMap.entrySet()) {
            List<UserLeaveSchedule> userLeaveScheduleList = computeUserLevelPolicyBasedLeaveSchedule(computeDay, entry.getValue());
            userLeaveScheduleMap.put(entry.getKey(), userLeaveScheduleList);
        }

        return userLeaveScheduleMap;
    }

    public List<UserLeaveSchedule> computeLeavesBasedOnPolicy(int instituteId, int academicSessionId, UUID userId, Integer computeDay) {
        if (instituteId <= 0 || academicSessionId <= 0 || userId == null || computeDay == null || computeDay <= 0) {
            logger.error("Invalid instituteId {} or academicSessionId {} or userId {} or computeDay {}",
                    instituteId, academicSessionId, userId, computeDay);
            return null;
        }
        UserLeavePolicy userLeavePolicy = getUserLeavePolicy(instituteId, academicSessionId, userId);
        if (userLeavePolicy == null || CollectionUtils.isEmpty(userLeavePolicy.getLeaveTypePolicyDetailsList())) {
            logger.error("No leave policy configured for user {}, academicSession {}", userId, academicSessionId);
            return null;
        }

        return computeUserLevelPolicyBasedLeaveSchedule(computeDay, userLeavePolicy);
    }

    private List<UserLeaveSchedule> computeUserLevelPolicyBasedLeaveSchedule(int computeDay, UserLeavePolicy userLeavePolicy) {
        List<UserLeaveSchedule> userAllLeaveSchedulesList = new ArrayList<>();
        for (LeaveTypePolicyDetails leaveTypePolicyDetails : userLeavePolicy.getLeaveTypePolicyDetailsList()) {
            List<UserLeaveSchedule> leaveTypeScheduleList = computeLeavesBasedOnPolicyForLeaveType(leaveTypePolicyDetails, computeDay);
            if (leaveTypeScheduleList == null) {
                continue;
            }
            userAllLeaveSchedulesList.addAll(leaveTypeScheduleList);
        }
        return userAllLeaveSchedulesList;
    }

    public List<UserLeaveTransactionSchedule> getLeavesBasedOnTransactions(int instituteId, int academicSessionId, UUID userId) {
        if (instituteId <= 0 || academicSessionId <= 0 || userId == null) {
            logger.error("Invalid instituteId {} or academicSessionId {} or userId {}",
                    instituteId, academicSessionId, userId);
            return null;
        }
        List<UserLeaveTransactionDetails> userLeaveTransactionDetailsList = getUserLeaveTransactions(instituteId, academicSessionId, userId);
        if (CollectionUtils.isEmpty(userLeaveTransactionDetailsList)) {
            logger.error("No leave transactions found for user {}, academicSession {}", userId, academicSessionId);
            return null;
        }
        return getUserLevelLeaveTransactionSchedules(userLeaveTransactionDetailsList);
    }

    public Map<UUID, List<UserLeaveTransactionSchedule>> getLeavesBasedOnTransactions(int instituteId, int academicSessionId) {
        if (instituteId <= 0 || academicSessionId <= 0) {
            logger.error("Invalid instituteId {} or academicSessionId {} ",
                    instituteId, academicSessionId);
            return null;
        }
        List<UserLeaveTransactionDetails> userLeaveTransactionDetailsList = getUserLeaveTransactions(instituteId, academicSessionId);
        if (CollectionUtils.isEmpty(userLeaveTransactionDetailsList)) {
            logger.error("No leave transactions found for instituteId {}, academicSession {}", academicSessionId);
            return new HashMap<>();
        }
        Map<UUID, List<UserLeaveTransactionDetails>> userLeaveTransactionDetailsMap = new HashMap<>();
        for (UserLeaveTransactionDetails userLeaveTransactionDetails : userLeaveTransactionDetailsList) {
            UUID userId = userLeaveTransactionDetails.getMetadata().getUserId();
            if (!userLeaveTransactionDetailsMap.containsKey(userId)) {
                userLeaveTransactionDetailsMap.put(userId, new ArrayList<>());
            }
            userLeaveTransactionDetailsMap.get(userId).add(userLeaveTransactionDetails);
        }

        Map<UUID, List<UserLeaveTransactionSchedule>> userLeaveTransactionScheduleMap = new HashMap<>();
        for (Map.Entry<UUID, List<UserLeaveTransactionDetails>> entry : userLeaveTransactionDetailsMap.entrySet()) {
            UUID userId = entry.getKey();
            List<UserLeaveTransactionSchedule> userAllLeaveSchedulesList = getUserLevelLeaveTransactionSchedules(entry.getValue());
            userLeaveTransactionScheduleMap.put(userId, userAllLeaveSchedulesList);
        }

        return userLeaveTransactionScheduleMap;
    }

    private List<UserLeaveTransactionSchedule> getUserLevelLeaveTransactionSchedules(List<UserLeaveTransactionDetails> userLeaveTransactionDetailsList) {
        List<UserLeaveTransactionSchedule> userAllLeaveSchedulesList = new ArrayList<>();
        for (UserLeaveTransactionDetails userLeaveTransactionDetails : userLeaveTransactionDetailsList) {
            List<UserLeaveTransactionSchedule> leaveTypeScheduleList = getLeavesBasedOnTransactionsForLeaveType(userLeaveTransactionDetails);
            if (leaveTypeScheduleList == null) {
                continue;
            }
            userAllLeaveSchedulesList.addAll(leaveTypeScheduleList);
        }
        return userAllLeaveSchedulesList;
    }

    private List<UserLeaveSchedule> computeLeavesBasedOnPolicyForLeaveType(LeaveTypePolicyDetails leaveTypePolicyDetails, int computeDay) {
        if (leaveTypePolicyDetails == null || leaveTypePolicyDetails.getLeaveTypePolicies() == null ||
                leaveTypePolicyDetails.getLeaveTypePolicies().getLeaveGrantPolicy() == null ||
                leaveTypePolicyDetails.getLeaveTypePolicies().getLeaveGrantPolicy().getLeaveGrantSchedule() == null ||
                CollectionUtils.isEmpty(leaveTypePolicyDetails.getLeaveTypePolicies().getLeaveGrantPolicy().getLeaveGrantSchedule().getMonthlyScheduleList())) {
            logger.error("Invalid leave type grant schedule {}. Skipping", leaveTypePolicyDetails);
            return null;
        }

        LeaveTypePolicies leaveTypePolicies = leaveTypePolicyDetails.getLeaveTypePolicies();
        List<LeaveGrantMonthlySchedule> leaveGrantMonthlyScheduleList = leaveTypePolicies.getLeaveGrantPolicy().getLeaveGrantSchedule().getMonthlyScheduleList();
        List<UserLeaveSchedule> userLeaveTypeSchedulesList = new ArrayList<>();
        for (LeaveGrantMonthlySchedule leaveGrantMonthlySchedule : leaveGrantMonthlyScheduleList) {
            if (leaveGrantMonthlySchedule.isEmpty()) {
                continue;
            }
            int scheduleDayStart = DateUtils.getDefaultZoneTime(new EMonthDay(leaveGrantMonthlySchedule.getDayOfMonth(), leaveGrantMonthlySchedule.getMonth()), leaveGrantMonthlySchedule.getYear());
            if (scheduleDayStart > computeDay) {
                continue;
            }
            userLeaveTypeSchedulesList.add(new UserLeaveSchedule(leaveTypePolicyDetails.getLeaveType().getLeaveTypeId(), LeaveTransactionCategory.POLICY_GRANT, LeaveTransactionType.CREDIT, scheduleDayStart, leaveGrantMonthlySchedule.getLeaveCount()));
            //TODO: Assuming expiry to be next day start
            if (leaveGrantMonthlySchedule.getExpireAt() != null && leaveGrantMonthlySchedule.getExpireAt() <= computeDay) {
                userLeaveTypeSchedulesList.add(new UserLeaveSchedule(leaveTypePolicyDetails.getLeaveType().getLeaveTypeId(), LeaveTransactionCategory.POLICY_EXPIRY, LeaveTransactionType.DEBIT, DateUtils.getDayStartDefaultTimezone(leaveGrantMonthlySchedule.getExpireAt()), leaveGrantMonthlySchedule.getLeaveCount()));
            }
        }
        //TODO : Add carry forward logic at session end
        return userLeaveTypeSchedulesList;
    }

    private List<UserLeaveTransactionSchedule> getLeavesBasedOnTransactionsForLeaveType(UserLeaveTransactionDetails userLeaveTransactionDetails) {
        if (userLeaveTransactionDetails == null || userLeaveTransactionDetails.getMetadata() == null ||
                CollectionUtils.isEmpty(userLeaveTransactionDetails.getLeaveTypeTransactionDataList())) {
            logger.error("Invalid leave transaction data {}. Skipping", userLeaveTransactionDetails);
            return null;
        }
        UUID transactionId = userLeaveTransactionDetails.getMetadata().getTransactionId();
        LeaveTransactionCategory category = userLeaveTransactionDetails.getMetadata().getCategory();
        if (category != LeaveTransactionCategory.POLICY_GRANT && category != LeaveTransactionCategory.POLICY_EXPIRY) {
            logger.info("Skipping transaction {} as it is not generated by policy, {}", category, transactionId);
            return null;
        }
        LeaveTransactionStatus status = userLeaveTransactionDetails.getMetadata().getTransactionStatus();

        if (status == LeaveTransactionStatus.DELETED) {
            logger.info("Skipping transaction as it is in deleted state, {}", transactionId);
            return null;
        }
        LeaveTransactionType type = userLeaveTransactionDetails.getMetadata().getTransactionType();

        List<UserLeaveTypeTransactionData> userLeaveTypeTransactionDataList = userLeaveTransactionDetails.getLeaveTypeTransactionDataList();
        List<UserLeaveTransactionSchedule> userLeaveTypeSchedulesList = new ArrayList<>();
        for (UserLeaveTypeTransactionData userLeaveTypeTransactionData : userLeaveTypeTransactionDataList) {
            int leaveDayStart = DateUtils.getDayStartDefaultTimezone(userLeaveTypeTransactionData.getStartDate());
            userLeaveTypeSchedulesList.add(new UserLeaveTransactionSchedule(transactionId, new UserLeaveSchedule(userLeaveTypeTransactionData.getLeaveType().getLeaveTypeId(), category, type, leaveDayStart, userLeaveTypeTransactionData.getLeaveCount())));
        }

        return userLeaveTypeSchedulesList;
    }
}
