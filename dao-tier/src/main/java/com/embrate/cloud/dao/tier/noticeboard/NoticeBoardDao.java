/**
 *
 */
package com.embrate.cloud.dao.tier.noticeboard;

import com.embrate.cloud.core.api.noticeboard.*;
import com.embrate.cloud.core.api.service.notification.NotificationEntity;
import com.embrate.cloud.dao.tier.noticeboard.mappers.NoticeBoardDetailsRowMapper;
import com.embrate.cloud.dao.tier.noticeboard.mappers.NoticeStudentViewDetailsRowMapper;
import com.embrate.cloud.dao.tier.noticeboard.mappers.UserNoticeDetailsRowMapper;
import com.embrate.cloud.dao.tier.noticeboard.mappers.UserNoticeViewDetailsRowMapper;
import com.embrate.cloud.dao.tier.push.notification.PushNotificationDao;
import com.google.gson.Gson;
import com.lernen.cloud.core.api.common.PaginationInfo;
import com.lernen.cloud.core.api.common.SearchResultWithPagination;
import com.lernen.cloud.core.api.configurations.Entity;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.DatabaseException;
import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.SharedConstants;
import com.lernen.cloud.core.utils.exceptions.ExceptionHandling;
import com.lernen.cloud.dao.tier.user.mappers.UserRowMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.sql.Timestamp;
import java.util.*;
import java.util.Map.Entry;

/**
 * <AUTHOR>
 *
 */
public class NoticeBoardDao {

	private static final Logger logger = LogManager.getLogger(NoticeBoardDao.class);
	private final JdbcTemplate jdbcTemplate;
	private final TransactionTemplate transactionTemplate;
	private final PushNotificationDao pushNotificationDao;
	private static final Gson GSON = SharedConstants.GSON;

	private static final UserRowMapper USER_ROW_MAPPER = new UserRowMapper();
	private static final NoticeBoardDetailsRowMapper NOTICE_BOARD_DETAILS_ROW_MAPPER = new NoticeBoardDetailsRowMapper();
	private static final UserNoticeDetailsRowMapper USER_NOTICE_DETAILS_ROW_MAPPER = new UserNoticeDetailsRowMapper();
	private static final UserNoticeViewDetailsRowMapper USER_NOTICE_VIEW_DETAILS_ROW_MAPPER = new UserNoticeViewDetailsRowMapper();
	private static final NoticeStudentViewDetailsRowMapper NOTICE_STUDENT_VIEW_DETAILS_ROW_MAPPER = new NoticeStudentViewDetailsRowMapper();

	private static final String ADD_SAVED_NOTICE_DETAILS = "insert into notice_details("
			+ " institute_id, academic_session_id, notice_id, title, body, created_by, expiry_date, pinned, pinned_by, pinned_timestamp, status) "
			+ " values(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";

	private static final String ADD_BROADCASTED_NOTICE_DETAILS = "insert into notice_details("
			+ " institute_id, academic_session_id, notice_id, title, body, created_by, expiry_date, pinned, pinned_by, pinned_timestamp, status,"
			+ " broadcasted_by, broadcasted_timestamp) "
			+ " values(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";

	private static final String ADD_NOTICE_ENTITY_MAPPING = " insert into notice_entity_mapping(notice_id, user_type, "
			+ " entity_name, entity_id) values(?, ?, ?, ?) ";

	private static final String UPDATE_NOTICE_BOARD_DOCUMENTS = " update notice_details set attachments = ? "
			+ " where notice_details.notice_id = ? ";

	/**
	 * removed  "and status = 'SAVED'" from where clause,
	 * because now we are allowing updating broadcasted notices expiry date and pin flag
	 */
	private static final String UPDATE_NOTICE_DETAILS = "update notice_details set title = ?, body = ?, updated_by = ?, "
			+ " expiry_date = ?, pinned = ?, pinned_by = ?, pinned_timestamp = ? "
			+ " where  institute_id = ? and notice_id = ? ";

	private static final String GET_NOTICE_DETAILS_BY_ID = "select * from notice_details "
			+ " left join notice_entity_mapping on notice_entity_mapping.notice_id = notice_details.notice_id "
			+ " left join standards on standards.standard_id = notice_entity_mapping.entity_id and  notice_entity_mapping.user_type = 'STUDENT' "
			+ " where notice_details.institute_id = ? and notice_details.notice_id = ? ";

	private static final String DELETE_NOTICE_DETAILS_BY_NOTICE_ID = " delete from notice_details "
			+ " where institute_id = ? and notice_id = ? ";

	private static final String UPDATE_NOTICE_STATUS = "update notice_details set status = ?, updated_by = ?, broadcasted_timestamp = ?, broadcasted_by = ? "
			+ " where institute_id = ? and notice_id in %s";

	private static final String GET_NOTICE_DETAILS_BY_FILTER = "select * from notice_details  "
			+ " left join notice_entity_mapping on notice_entity_mapping.notice_id = notice_details.notice_id "
			+ " left join standards on standards.standard_id = notice_entity_mapping.entity_id and notice_entity_mapping.entity_name = 'CLASS' and  notice_entity_mapping.user_type = 'STUDENT' "
			+ " where notice_details.institute_id = ? and notice_details.status = ? and notice_details.academic_session_id = ? ";

	private static final String LIMIT_OFFSET_CLAUSE = " limit ? offset ?";

	private static final String BROADCATED_DATE_CLAUSE = " and ((notice_details.broadcasted_timestamp between ? and ?) "
			+ " or (notice_details.broadcasted_timestamp is null and notice_details.created_timestamp between ? and ?)) ";

	private static final String DELETE_NOTICE_ENTITY_MAPPING_BY_NOTICE_ID = " delete from notice_entity_mapping where notice_entity_mapping.notice_id = ? ";

	private static final String GET_STUDENT_NOTICE_DETAILS = " select * from notice_details "
			+ " inner join notice_entity_mapping on notice_entity_mapping.notice_id = notice_details.notice_id "
			+ " left join student_academic_session_details on student_academic_session_details.standard_id = notice_entity_mapping.entity_id "
			+ " and student_academic_session_details.academic_session_id = ? "
			+ " where notice_entity_mapping.user_type = 'STUDENT' "
			+ " and notice_details.status = 'BROADCASTED' "
			+ " and notice_details.institute_id = ? "
			+ " and student_academic_session_details.student_id = ? and (notice_details.expiry_date >= ? or expiry_date is null) and notice_details.academic_session_id = ? "
			+ " order by notice_details.pinned desc, notice_details.updated_timestamp desc ";

	private static final String GET_STUDENT_NOTICE_DETAILS_LIMIT_OFFSET = " select * from "
			+ " ( select notice_details.* from notice_details inner join notice_entity_mapping on notice_entity_mapping.notice_id  = notice_details.notice_id where notice_details.status = 'BROADCASTED' and  notice_details.institute_id = ? and (notice_details.expiry_date >= ? or expiry_date is null) and notice_details.academic_session_id = ? and (notice_entity_mapping.entity_id = ? or notice_entity_mapping.entity_id = ? %s) order by notice_details.pinned desc, notice_details.updated_timestamp desc limit ? offset ? ) as notice_details "
			+ " inner join notice_entity_mapping on notice_entity_mapping.notice_id = notice_details.notice_id "
			+ " left join student_academic_session_details on student_academic_session_details.standard_id = notice_entity_mapping.entity_id "
			+ " and student_academic_session_details.academic_session_id = ? "
			+ " where notice_entity_mapping.user_type = 'STUDENT' "
			+ " and notice_details.status = 'BROADCASTED' "
			+ " and notice_details.institute_id = ? "
			+ " and student_academic_session_details.student_id = ? and (notice_details.expiry_date >= ? or expiry_date is null) and notice_details.academic_session_id = ? "
			+ " order by notice_details.pinned desc, notice_details.updated_timestamp desc ";
	private static final String GET_OTHER_USERS_NOTICE_DETAILS = "select * from notice_details "
			+ " inner join notice_entity_mapping on notice_entity_mapping.notice_id = notice_details.notice_id "
			+ " where notice_entity_mapping.user_type = 'STAFF' "
			+ " and notice_details.status = 'BROADCASTED'  "
			+ " and notice_details.institute_id = ? "
			+ " order by notice_details.pinned desc, notice_details.updated_timestamp desc ";

	private static final String GET_USER_NOTICE_DETAILS_BY_NOTICE_ID = " select * from notice_details "
			+ " where notice_details.institute_id = ? "
			+ " and notice_details.notice_id = ? ";

	private static final String GET_NOTICE_DETAILS = "select * from notice_details  "
			+ " left join notice_entity_mapping on notice_entity_mapping.notice_id = notice_details.notice_id "
			+ " where notice_details.institute_id = ? ";

	private static final String LIST_NOTICE_ID = " and notice_details.notice_id in %s ";

	private static final String GET_STUDENT_NOTICE_USERS = " select distinct users.* from notice_details "
			+ " left join notice_entity_mapping on notice_entity_mapping.notice_id = notice_details.notice_id "
			+ " left join student_academic_session_details on student_academic_session_details.standard_id = notice_entity_mapping.entity_id  "
			+ " and student_academic_session_details.academic_session_id = ? "
			+ " join students on student_academic_session_details.student_id = students.student_id and students.final_status = 'ENROLLED' "
			+ " inner join users on students.student_id = users.user_id and user_status = 'ENABLED' "
			+ " where notice_entity_mapping.user_type = 'STUDENT'  "
			+ " and notice_details.status = 'BROADCASTED'  "
			+ " and notice_details.institute_id = ? ";

	private static final String GET_OTHER_NOTICE_USERS = " select distinct users.* from users "
			+ " where user_type in ('STAFF', 'ADMIN') and institute_id = ? and user_status = 'ENABLED' ";

	private static final String GET_USER_NOTICE_VIEW_DETAILS = "select * from user_notice_view_details "
			+ " where notice_id = ? ";

	private static final String GET_NOTICE_STUDENT_VIEW_DETAILS = "select * from user_notice_view_details "
			+ " inner join students on students.student_id =  user_notice_view_details.user_id "
			+ " left join student_academic_session_details on students.student_id = student_academic_session_details.student_id "
			+ " left join academic_session on student_academic_session_details.academic_session_id = academic_session.academic_session_id "
			+ " left join standards on student_academic_session_details.standard_id = standards.standard_id "
			+ " left join standard_section_mapping on standard_section_mapping.standard_id = standards.standard_id and standard_section_mapping.academic_session_id = student_academic_session_details.academic_session_id and student_academic_session_details.section_id = standard_section_mapping.section_id "
			+ " where student_academic_session_details.session_status = 'ENROLLED' and student_academic_session_details.academic_session_id = ? "
			+ " and user_notice_view_details.notice_id = ? order by standards.level, students.name";

	private static final String USER_ID_CLAUSE = " and user_id = ? ";

	private static final String ADD_USER_NOTICE_VIEW_DETAILS = "insert into user_notice_view_details " +
			" (notice_id, user_id, viewed_on) values(?, ?, ?) ";

	private static final String DELETE_USER_NOTICE_VIEW_DETAILS = "delete from user_notice_view_details "
			+ " where notice_id = ? ";

	private static final String GET_ALL_INSTITUTE_NOTICES = "select * from notice_details  "
			+ " left join notice_entity_mapping on notice_entity_mapping.notice_id = notice_details.notice_id "
			+ " left join standards on standards.standard_id = notice_entity_mapping.entity_id and notice_entity_mapping.entity_name = 'CLASS' and  notice_entity_mapping.user_type = 'STUDENT' "
			+ " where notice_details.institute_id = ? ";

	private static final String UPDATE_NOTICE_ACADEMIC_SESSION = " update notice_details set academic_session_id = ? "
			+ " where notice_details.notice_id = ? ";

	public NoticeBoardDao(JdbcTemplate jdbcTemplate, TransactionTemplate transactionTemplate, PushNotificationDao pushNotificationDao) {
		this.jdbcTemplate = jdbcTemplate;
		this.transactionTemplate = transactionTemplate;
		this.pushNotificationDao = pushNotificationDao;
	}

	/*
	 * this method should be used inside a transaction only.
	 */
	public UUID addNoticeNonAtmoic(int instituteId, UUID userId, NoticePayload noticePaylaod, boolean saveOnly) {
		try {
			final UUID noticeId = addNoticeDetails(noticePaylaod, saveOnly);
			if (noticeId == null) {
				logger.error("Error while adding notice details for payload {}, instituteId {} ", noticePaylaod, instituteId);
				throw new RuntimeException("Error while adding notice details.");
			}
			boolean result = addNoticeEntityMappingDetails(noticePaylaod, noticeId);
			if(!result) {
				logger.error("Error while adding notice entity mapping for payload {}, instituteId {} ", noticePaylaod, instituteId);
				throw new RuntimeException("Error while adding notice entity mapping.");
			}
			return noticeId;
		} catch (final DatabaseException | ApplicationException ex) {
			logger.error("Unable to add notice for institute {}", instituteId, ex);
			throw ex;
		} catch (final Exception e) {
			logger.error("Unable to add notice for institute {}", instituteId, e);
		}
		return null;
	}

	private boolean addNoticeEntityMappingDetails(NoticePayload noticePaylaod, UUID noticeId) {

		final List<Object[]> batchInsertArgs = new ArrayList<>();
		int count = 0;
		for(Entry<UserType, List<String>> entityNameIdsEntry : noticePaylaod.getEntityNameAndIdsMapping().entrySet()) {
			for(String entityId : entityNameIdsEntry.getValue()) {
				final List<Object> args = new ArrayList<>();
				args.add(noticeId.toString());
				args.add(entityNameIdsEntry.getKey().name());
				if(entityNameIdsEntry.getKey() == UserType.STAFF) {
					args.add(Entity.INSTITUTE.name());
					args.add(String.valueOf(noticePaylaod.getInstituteId()));
				} else if(entityNameIdsEntry.getKey() == UserType.STUDENT) {
					args.add(Entity.CLASS.name());
					args.add(entityId);
				}
				count++;
				batchInsertArgs.add(args.toArray());
			}
		}
		try {
			final int[] rows = jdbcTemplate.batchUpdate(ADD_NOTICE_ENTITY_MAPPING, batchInsertArgs);
			if (rows.length != count) {
				return false;
			}
			for (final int rowCount : rows) {
				if (rowCount != 1) {
					return false;
				}
			}
			return true;
		} catch (final Exception e) {
			logger.error("Unable to add notice entity mapping for institute {}", noticePaylaod.getInstituteId(), e);
		}

		return false;
	}

	private UUID addNoticeDetails(NoticePayload noticePaylaod, boolean saveOnly) {
		try {
			UUID noticeId = UUID.randomUUID();
			Boolean result = true;
			if (saveOnly) {
				result = jdbcTemplate.update(ADD_SAVED_NOTICE_DETAILS,
						noticePaylaod.getInstituteId(), noticePaylaod.getAcademicSessionId(),
						noticeId.toString(), noticePaylaod.getTitle(),
						noticePaylaod.getBody(), noticePaylaod.getCreatedBy().toString(),
						(noticePaylaod.getExpiryDate() != null) && (noticePaylaod.getExpiryDate() > 0)
								? new Timestamp(noticePaylaod.getExpiryDate() * 1000l) : null,
						noticePaylaod.getPinned(), noticePaylaod.getPinnedBy() == null ? null
								: noticePaylaod.getPinnedBy().toString(),
						noticePaylaod.getPinned() ? new Timestamp(System.currentTimeMillis()) : null,
						NoticeBoardStatus.SAVED.name()) == 1;
			} else {
				result = jdbcTemplate.update(ADD_BROADCASTED_NOTICE_DETAILS,
						noticePaylaod.getInstituteId(), noticePaylaod.getAcademicSessionId(),
						noticeId.toString(), noticePaylaod.getTitle(),
						noticePaylaod.getBody(), noticePaylaod.getCreatedBy().toString(),
						(noticePaylaod.getExpiryDate() != null) && (noticePaylaod.getExpiryDate() > 0)
								? new Timestamp(noticePaylaod.getExpiryDate() * 1000l) : null,
						noticePaylaod.getPinned(), noticePaylaod.getPinnedBy() == null ? null
								: noticePaylaod.getPinnedBy().toString(),
						noticePaylaod.getPinned() ? new Timestamp(System.currentTimeMillis()) : null,
						NoticeBoardStatus.BROADCASTED.name(),
						noticePaylaod.getBroadcastedBy().toString(), new Timestamp(System.currentTimeMillis())) == 1;
			}
			if (!result) {
				logger.error("Error while adding notice details for institute {}. Please try again.",
						noticePaylaod.getInstituteId());
				throw new RuntimeException("Error while adding notice details. Please try again.");
			}
			return noticeId;
		} catch (final DatabaseException | ApplicationException ex) {
			logger.error("Unable to add notice for institute {}", noticePaylaod.getInstituteId(), ex);
			throw ex;
		} catch (final Exception e) {
			logger.error("Unable to add notice for institute {}", noticePaylaod.getInstituteId(), e);
			throw e;
		}
	}

	public boolean uploadNoticeBoardDocument(UUID noticeId, List<Document<NoticeBoardDocumentType>> noticeBoardAttachments) {
		try {
			final Object[] args = { GSON.toJson(noticeBoardAttachments), noticeId.toString() };
			return jdbcTemplate.update(UPDATE_NOTICE_BOARD_DOCUMENTS, args) == 1;
		} catch (final DataAccessException dataAccessException) {
			ExceptionHandling.HandleException(dataAccessException, null, null);
			logger.error("Error while updating notice documents for notice id {}", noticeId, dataAccessException);
		} catch (final Exception e) {
			logger.error("Exception while updating notice documents for notice id {}", noticeId, e);
		}
		return false;
	}

	public NoticeDetails getNoticeDetailsById(int instituteId, UUID noticeId) {
		try {
			final Object[] args = { instituteId, noticeId.toString() };
			return NoticeBoardDetailsRowMapper.getNoticeBoardResponse(
					jdbcTemplate.query(GET_NOTICE_DETAILS_BY_ID, args, NOTICE_BOARD_DETAILS_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Exception while fetching notice details for notice id {}, instituteId {} ", noticeId, instituteId, e);
		}
		return null;
	}

	/*
	 * this method should be used inside a transaction only.
	 */
	public UUID updateNoticeDetailsNonAtmoic(int instituteId, NoticePayload updateNoticePaylaod) {
		try {
			boolean result = jdbcTemplate.update(UPDATE_NOTICE_DETAILS,
					updateNoticePaylaod.getTitle(),
					updateNoticePaylaod.getBody(),
					updateNoticePaylaod.getUpdatedBy().toString(),
					(updateNoticePaylaod.getExpiryDate() != null) && (updateNoticePaylaod.getExpiryDate() > 0)
							? new Timestamp(updateNoticePaylaod.getExpiryDate() * 1000l) : null,
					updateNoticePaylaod.getPinned(), updateNoticePaylaod.getPinnedBy() == null ? null
							: updateNoticePaylaod.getPinnedBy().toString(),
					updateNoticePaylaod.getPinned() ? new Timestamp(System.currentTimeMillis()) : null,
					updateNoticePaylaod.getInstituteId(),
					updateNoticePaylaod.getNoticeId().toString()) == 1;
			if(!result) {
				logger.error("Unable to update notice basic details for institute {}, notice {}",
						instituteId, updateNoticePaylaod.getNoticeId());
				throw new RuntimeException("Unable to update notice basic details.");
			}
			result = deleteNoticeEntityMappingDetails(updateNoticePaylaod.getNoticeId());
			if(!result) {
				logger.error("Unable to delete existing notice entity mapping for institute {}, notice {}",
						instituteId, updateNoticePaylaod.getNoticeId());
				throw new RuntimeException("Unable to delete existing notice entity mapping.");
			}
			result = addNoticeEntityMappingDetails(updateNoticePaylaod, updateNoticePaylaod.getNoticeId());
			if(!result) {
				logger.error("Unable to add new notice entity mapping for institute {}, notice {}",
						instituteId, updateNoticePaylaod.getNoticeId());
				throw new RuntimeException("Unable to add new notice entity mapping.");
			}
			return updateNoticePaylaod.getNoticeId();
		} catch (final Exception e) {
			logger.error("Exception while updating notice details for institute {}, payload {}",
					updateNoticePaylaod.getInstituteId(), updateNoticePaylaod, e);
		}
		return null;
	}

	private boolean deleteNoticeEntityMappingDetails(UUID noticeId) {
		try {
			return jdbcTemplate.update(DELETE_NOTICE_ENTITY_MAPPING_BY_NOTICE_ID,
					noticeId.toString()) >= 0;
		} catch (final Exception e) {
			logger.error("Exception while deleting notice details for noticeId {}",
					noticeId, e);
		}
		return false;
	}

	public boolean deleteNoticeDetails(int instituteId, UUID userId, UUID noticeId) {
		try {
			final Boolean finalResult = transactionTemplate.execute(new TransactionCallback<Boolean>() {
				@Override
				public Boolean doInTransaction(TransactionStatus status) {
					Boolean result = deleteUserNoticeViewDetails(noticeId);
					if(!result) {
						logger.error("Unable to delete user notice view details for institute {}, notice {}",
								instituteId, noticeId);
						throw new RuntimeException("Unable to delete user notice view details.");
					}
					result = deleteNoticeEntityMappingDetails(noticeId);
					if(!result) {
						logger.error("Unable to delete notice entity mapping for institute {}, notice {}",
								instituteId, noticeId);
						throw new RuntimeException("Unable to delete notice entity mapping.");
					}
					result = pushNotificationDao.deleteBellNotificationByEntityDetails(instituteId, noticeId, Arrays.asList(
							NotificationEntity.NOTICE, NotificationEntity.ADMIN_NOTICE));
					if(!result) {
						logger.error("Unable to delete notice bell notification for institute {}, notice {}",
								instituteId, noticeId);
						throw new RuntimeException("Unable to delete notice bell notification.");
					}
					return jdbcTemplate.update(DELETE_NOTICE_DETAILS_BY_NOTICE_ID, instituteId, noticeId.toString()) == 1;
				}
			});
			return finalResult;
		} catch (final Exception e) {
			logger.error("Exception while deleting notice details for institute {}, noticeId {}", instituteId, noticeId, e);
		}
		return false;
	}

	public boolean updateNoticeStatus(int instituteId, UUID userId, List<UUID> noticeIdsList,
									  NoticeBoardStatus noticeBoardStatus) {
		try {
			final StringBuilder inQuery = new StringBuilder();
			final List<Object> args = new ArrayList<>();
			args.add(noticeBoardStatus.name());
			args.add(userId.toString());
			args.add(new Timestamp(System.currentTimeMillis()));
			args.add(userId.toString());
			args.add(instituteId);

			inQuery.append("(");
			boolean first = true;
			for (final UUID noticeId : noticeIdsList) {
				args.add(noticeId.toString());
				if (first) {
					inQuery.append("?");
					first = false;
					continue;
				}
				inQuery.append(", ?");
			}
			inQuery.append(")");

			return jdbcTemplate.update(String.format(UPDATE_NOTICE_STATUS, inQuery.toString()), args.toArray()) >= 1;
		} catch (final Exception e) {
			logger.error("Exception while updating notice status for institute {} and noticeStatus {}", instituteId,
					noticeBoardStatus, e);
		}
		return false;
	}

	public SearchResultWithPagination<NoticeDetails> getNoticeDetails(int instituteId, Integer broadcastedDate,
																	  int offset, int limit, NoticeBoardStatus noticeBoardStatus,
																	  int academicSessionId) {
		try {

			String query = GET_NOTICE_DETAILS_BY_FILTER;
			final List<Object> args = new ArrayList<>();

			args.add(instituteId);
			args.add(noticeBoardStatus.name());
			args.add(academicSessionId);

			String dateFilter = "";
			query += " %s ";
			if (broadcastedDate != null && broadcastedDate > 0) {
				dateFilter = BROADCATED_DATE_CLAUSE;
				Long dateStart = DateUtils.getDayStart(broadcastedDate * 1000l, DateUtils.DEFAULT_TIMEZONE);
				Long dateEnd = DateUtils.getDayEnd(broadcastedDate * 1000l, DateUtils.DEFAULT_TIMEZONE);
				args.add(new Timestamp(dateStart));
				args.add(new Timestamp(dateEnd));
				args.add(new Timestamp(dateStart));
				args.add(new Timestamp(dateEnd));
			}

			List<NoticeDetails> noticeDetailsList =  NoticeBoardDetailsRowMapper.sortNoticeBoardListByCreatedDate(
					NoticeBoardDetailsRowMapper.getNoticeBoardListResponse(
							jdbcTemplate.query(String.format(query, dateFilter), args.toArray(), NOTICE_BOARD_DETAILS_ROW_MAPPER)));

			final PaginationInfo paginationInfo = new PaginationInfo(noticeDetailsList.size(), limit, offset);

			List<NoticeDetails> finalNoticeDetailsList = new ArrayList<NoticeDetails>();
			int count = 0;
			for(int i = offset; i < noticeDetailsList.size(); i++) {
				if(limit > count) {
					finalNoticeDetailsList.add(noticeDetailsList.get(i));
				}
				count++;
			}

			final SearchResultWithPagination<NoticeDetails> resultWithPagination = new SearchResultWithPagination<>(
					paginationInfo, finalNoticeDetailsList);

			return resultWithPagination;

		} catch (final Exception e) {
			logger.error("Exception while getting notice details for instituteId {}", instituteId, e);
		}
		return null;
	}

	public List<UserNoticeDetails> getNoticesList(int instituteId, UserType userType, UUID studentId,
												  int academicSessionId) {
		try {
			if(userType == UserType.STUDENT) {
				final Object[] args = { academicSessionId, instituteId, studentId.toString(), new Timestamp(System.currentTimeMillis()), academicSessionId };
				return jdbcTemplate.query(GET_STUDENT_NOTICE_DETAILS, args, USER_NOTICE_DETAILS_ROW_MAPPER);
			} else {
				final Object[] args = { instituteId };
				return jdbcTemplate.query(GET_OTHER_USERS_NOTICE_DETAILS, args, USER_NOTICE_DETAILS_ROW_MAPPER);
			}
		} catch (final Exception e) {
			logger.error("Exception while getting notice details for institute {}, usertype {}",
					instituteId, userType, e);
		}
		return null;
	}

	public List<UserNoticeDetails> getNoticesListLimitOffset(int instituteId, UserType userType, UUID studentId,
												  UUID standardId, int academicSessionId, Boolean pinned, int limit, int offset) {
		try {
			if(userType != UserType.STUDENT) {
				return null;
			}
            String inQuery = "";
			List<Object> args = new ArrayList<>();
			args.add(instituteId);
			args.add(new Timestamp(System.currentTimeMillis()));
			args.add(academicSessionId);
			args.add(instituteId);
			args.add(standardId.toString());
			if(pinned != null) {
				args.add(pinned);
				inQuery = " and notice_details.pinned = ? ";
			}
			args.add(limit);
			args.add(offset);
			args.add(academicSessionId);
			args.add(instituteId);
			args.add(studentId.toString());
			args.add(new Timestamp(System.currentTimeMillis()));
			args.add(academicSessionId);

			return jdbcTemplate.query(String.format(GET_STUDENT_NOTICE_DETAILS_LIMIT_OFFSET, inQuery), args.toArray(), USER_NOTICE_DETAILS_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Exception while getting notice details for institute {}, usertype {}",
					instituteId, userType, e);
		}
		return null;
	}

	public UserNoticeDetails getNoticeByNoticeId(int instituteId, UUID noticeId) {
		try {
			final Object[] args = { instituteId, noticeId.toString() };
			return jdbcTemplate.queryForObject(GET_USER_NOTICE_DETAILS_BY_NOTICE_ID, args, USER_NOTICE_DETAILS_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Exception while getting notice details for institute {}, noticeId {}",
					instituteId, noticeId, e);
		}
		return null;
	}

	public List<NoticeDetails> getNoticeDetailsByNoticeIdList(int instituteId, HashSet<UUID> noticeIds) {
		try {
			List<Object> args = new ArrayList<>();
			args.add(instituteId);
			StringBuilder inQuery = new StringBuilder();
			inQuery.append(" (");
			String delimiter = "";
			for (UUID noticeId : noticeIds) {
				args.add(noticeId.toString());
				inQuery.append(delimiter + " ?");
				delimiter = ",";
			}
			inQuery.append(") ");
			return NoticeBoardDetailsRowMapper.getNoticeBoardListResponse(
					jdbcTemplate.query(String.format(GET_NOTICE_DETAILS + LIST_NOTICE_ID, inQuery.toString()), args.toArray(),
							NOTICE_BOARD_DETAILS_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Exception while getting notice details for institute {}", instituteId, e);
		}
		return null;
	}

	public List<User> getNoticeBoardUsers(int instituteId, int academicSessionId, List<NoticeDetails> studentNotices,
										  List<NoticeDetails> otherNotices) {
		try {
			List<User> userList = new ArrayList<User>();
			if(!CollectionUtils.isEmpty(studentNotices)) {
				List<Object> args = new ArrayList<>();
				args.add(academicSessionId);
				args.add(instituteId);
				StringBuilder inQuery = new StringBuilder();
				inQuery.append(" (");
				String delimiter = "";
				for (NoticeDetails noticeDetails : studentNotices) {
					args.add(noticeDetails.getNoticeId().toString());
					inQuery.append(delimiter + " ?");
					delimiter = ",";
				}
				inQuery.append(") ");
				userList.addAll(jdbcTemplate.query(String.format(GET_STUDENT_NOTICE_USERS +
						LIST_NOTICE_ID, inQuery.toString()), args.toArray(), USER_ROW_MAPPER));
			}

			if(!CollectionUtils.isEmpty(otherNotices)) {
				List<Object> args = new ArrayList<>();
				args.add(instituteId);
				userList.addAll(jdbcTemplate.query(GET_OTHER_NOTICE_USERS, args.toArray(),
						USER_ROW_MAPPER));
			}
			return userList;
		} catch (final Exception e) {
			logger.error("Exception while getting users for instituteId {}", instituteId, e);
		}
		return null;
	}

	public UserNoticeViewDetails getUserNoticeViewDetails(UUID noticeId, UUID userId) {
		try {
			final Object[] args = { noticeId.toString(), userId.toString() };
			return jdbcTemplate.queryForObject(GET_USER_NOTICE_VIEW_DETAILS + USER_ID_CLAUSE,
					args, USER_NOTICE_VIEW_DETAILS_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Exception while getting notice details for userId {}, noticeId {}",
					userId, noticeId, e);
		}
		return null;
	}

	public List<NoticeStudentViewDetails> getNoticeStudentViewDetailsList(int academicSessionId, UUID noticeId) {
		try {
			final Object[] args = { academicSessionId, noticeId.toString() };
			return jdbcTemplate.query(GET_NOTICE_STUDENT_VIEW_DETAILS, args, NOTICE_STUDENT_VIEW_DETAILS_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Exception while getting notice details for noticeId {}",
					noticeId, e);
		}
		return null;
	}

	public boolean addUserNoticeViewDetails(UUID noticeId, UUID userId) {
		try {
			return jdbcTemplate.update(ADD_USER_NOTICE_VIEW_DETAILS,
					noticeId.toString(), userId.toString(),
					new Timestamp(System.currentTimeMillis())) == 1;
		} catch (final DatabaseException | ApplicationException ex) {
			logger.error("Unable to add notice for noticeId {}, userId {} ", noticeId, userId, ex);
			throw ex;
		} catch (final Exception e) {
			logger.error("Unable to add notice for noticeId {}, userId {} ", noticeId, userId, e);
			throw e;
		}
	}

	private boolean deleteUserNoticeViewDetails(UUID noticeId) {
		try {
			return jdbcTemplate.update(DELETE_USER_NOTICE_VIEW_DETAILS, noticeId.toString()) >= 0;
		} catch (final Exception e) {
			logger.error("Exception while deleting notice details for noticeId {}",
					noticeId, e);
		}
		return false;
	}

	public List<NoticeDetails> getInstituteNoticeDetails(int instituteId, int academicSessionId) {
		try {
			final List<Object> args = new ArrayList<>();
			args.add(instituteId);
			String query = GET_ALL_INSTITUTE_NOTICES;
			if(academicSessionId > 0) {
				query += " and notice_details.academic_session_id = ? ";
				args.add(academicSessionId);
			}
			return NoticeBoardDetailsRowMapper.sortNoticeBoardListByCreatedDate(
					NoticeBoardDetailsRowMapper.getNoticeBoardListResponse(
							jdbcTemplate.query(query, args.toArray(), NOTICE_BOARD_DETAILS_ROW_MAPPER)));
		} catch (final Exception e) {
			logger.error("Exception while getting notice details for instituteId {}", instituteId, e);
		}
		return null;
	}

	public boolean updateNoticeSession(int instituteId, List<NoticeDetails> noticeDetailsList) {
		final List<Object[]> batchInsertArgs = new ArrayList<>();
		int count = 0;
		for(NoticeDetails noticeDetails : noticeDetailsList) {
			final List<Object> args = new ArrayList<>();
			args.add(noticeDetails.getAcademicSessionId());
			args.add(noticeDetails.getNoticeId().toString());
			count++;
			batchInsertArgs.add(args.toArray());
		}
		try {
			final int[] rows = jdbcTemplate.batchUpdate(UPDATE_NOTICE_ACADEMIC_SESSION, batchInsertArgs);
			if (rows.length != count) {
				return false;
			}
			for (final int rowCount : rows) {
				if (rowCount != 1) {
					return false;
				}
			}
			return true;
		} catch (final Exception e) {
			logger.error("Unable to update notice details for institute {}", instituteId, e);
		}
		return false;
	}
}
