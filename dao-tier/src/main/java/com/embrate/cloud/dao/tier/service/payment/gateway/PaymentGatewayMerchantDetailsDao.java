package com.embrate.cloud.dao.tier.service.payment.gateway;

import com.embrate.cloud.core.api.service.payment.gateway.PaymentGatewayServiceProvider;
import com.embrate.cloud.core.api.service.payment.gateway.merchants.PGMerchantDetails;
import com.embrate.cloud.dao.tier.service.payment.gateway.mappers.PaymentGatewayMerchantDetailsRowMapper;
import com.lernen.cloud.core.utils.SharedConstants;
import com.lernen.cloud.core.utils.crypto.CryptoUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 */

public class PaymentGatewayMerchantDetailsDao {

    private static final Logger logger = LogManager.getLogger(PaymentGatewayMerchantDetailsDao.class);
    private static final PaymentGatewayMerchantDetailsRowMapper PAYMENT_GATEWAY_MERCHANT_DETAILS_ROW_MAPPER = new PaymentGatewayMerchantDetailsRowMapper();
    private static final String GET_ACTIVE_MERCHANTS = "select * from payment_gateway_merchant_details where institute_id = ? and payment_gateway = ? and status = 'ACTIVE' ";

    private static final String ADD_MERCHANT = "insert into payment_gateway_merchant_details(merchant_id, institute_id, payment_gateway, merchant_name, " +
            "merchant_key, merchant_secret, status, metadata, description) values (?, ?, ?, ?, ?, ?, ?, ?, ?) ";
    private final JdbcTemplate jdbcTemplate;
    private static final Map<String, List<PGMerchantDetails>> LOCAL_CACHE = new HashMap<>();

    public PaymentGatewayMerchantDetailsDao(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public List<PGMerchantDetails> getAllActiveMerchants(int instituteId, PaymentGatewayServiceProvider paymentGatewayServiceProvider){
        String cacheKey = getCacheKey(instituteId, paymentGatewayServiceProvider);
        if(LOCAL_CACHE.containsKey(cacheKey)){
            return LOCAL_CACHE.get(cacheKey);
        }
        try{
            List<PGMerchantDetails> merchantDetailsList = jdbcTemplate.query(GET_ACTIVE_MERCHANTS, new Object[]{instituteId, paymentGatewayServiceProvider.name()}, PAYMENT_GATEWAY_MERCHANT_DETAILS_ROW_MAPPER);
            LOCAL_CACHE.put(cacheKey, merchantDetailsList);
            return merchantDetailsList;
        }catch (Exception e){
            logger.error("Error while getting the active merchants for instituteId {}, paymentGatewayServiceProvider {}", instituteId, paymentGatewayServiceProvider, e);
        }
        return null;
    }

    public UUID addMerchant(PGMerchantDetails merchantDetails){
        try{
            UUID merchantId = UUID.randomUUID();
            int row = jdbcTemplate.update(ADD_MERCHANT, merchantId.toString(), merchantDetails.getInstituteId(), merchantDetails.getServiceProvider().name(), CryptoUtils.encrypt(merchantDetails.getMerchantName()),
                    CryptoUtils.encrypt(merchantDetails.getMerchantKey()), CryptoUtils.encrypt(merchantDetails.getMerchantSecret()), merchantDetails.getStatus().name(), merchantDetails.getMetadata() == null ? null : CryptoUtils.encrypt(SharedConstants.GSON.toJson(merchantDetails.getMetadata())), merchantDetails.getDescription());
            if(row == 1){
                String cacheKey = getCacheKey(merchantDetails.getInstituteId(), merchantDetails.getServiceProvider());
                if(LOCAL_CACHE.containsKey(cacheKey)){
                    LOCAL_CACHE.remove(cacheKey);
                }
                return merchantId;
            }
            return null;
        }catch (Exception e){
            logger.error("Error while storing merchant details for instituteId {}", merchantDetails.getInstituteId(), e);
        }
        return null;
    }

    private String getCacheKey(int instituteId, PaymentGatewayServiceProvider paymentGatewayServiceProvider){
        StringBuilder sb = new StringBuilder();
        return sb.append(instituteId).append("|").append(paymentGatewayServiceProvider.name()).toString();
    }

}
