/**
 * 
 */
package com.embrate.cloud.dao.tier.timetable.mappers;

import com.embrate.cloud.core.api.timetable.SchoolShiftMetaData;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lernen.cloud.core.api.institute.Time;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import java.lang.reflect.Type;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.DayOfWeek;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 *
 */
public class ShiftMetaDataRowMapper implements RowMapper<SchoolShiftMetaData> {
	
	public static final String INSTITUTE_ID = "shift_details_meta_data.institute_id";
	public static final String ACADEMIC_SESSION_ID = "shift_details_meta_data.academic_session_id";
	public static final String SHIFT_ID = "shift_details_meta_data.shift_id";
	public static final String SHIFT_NAME = "shift_details_meta_data.shift_name";
	public static final String SHIFT_START_TIME = "shift_details_meta_data.shift_start_time";
	public static final String SHIFT_END_TIME = "shift_details_meta_data.shift_end_time";
	public static final String CREATED_ON = "shift_details_meta_data.created_on";
	public static final String CREATED_BY = "shift_details_meta_data.created_by";
	public static final String UPDATED_ON = "shift_details_meta_data.updated_on";
	public static final String UPDATED_BY = "shift_details_meta_data.updated_by";
	public static final String DAYS = "shift_details_meta_data.days";
	
	private static final Gson GSON = new Gson();
	
	@Override
	public SchoolShiftMetaData mapRow(ResultSet rs, int rowNum) throws SQLException {
		if(rs.getString(SHIFT_ID) == null)  {
			return null;
		}
		
		final Timestamp createdOn = rs.getTimestamp(CREATED_ON);
		final Integer createdOnInt = createdOn == null ? null
				: (int) (createdOn.getTime() / 1000l);
		
		final Timestamp updatedOn = rs.getTimestamp(UPDATED_ON);
		final Integer updatedOnInt = updatedOn == null ? null
				: (int) (updatedOn.getTime() / 1000l);
		
		final String days = rs.getString(DAYS);
		List<DayOfWeek> daysList = null;
		if (!StringUtils.isBlank(days)) {
			final Type collectionType = new TypeToken<List<DayOfWeek>>() {
			}.getType();
			daysList = GSON.fromJson(days, collectionType);
		}
		
		return new SchoolShiftMetaData(rs.getInt(INSTITUTE_ID), rs.getInt(ACADEMIC_SESSION_ID), 
				UUID.fromString(rs.getString(SHIFT_ID)), rs.getString(SHIFT_NAME), 
				GSON.fromJson(rs.getString(SHIFT_START_TIME), Time.class),
				GSON.fromJson(rs.getString(SHIFT_END_TIME), Time.class),
				UUID.fromString(rs.getString(CREATED_BY)), createdOnInt, 
				rs.getString(UPDATED_BY) == null ? null : UUID.fromString(rs.getString(UPDATED_BY)),
				updatedOnInt, daysList);
	}
}