package com.embrate.cloud.dao.tier.attendance;

import com.embrate.cloud.core.api.attendance.AttendanceInputType;
import com.embrate.cloud.core.api.attendance.AttendanceLog;
import com.embrate.cloud.dao.tier.attendance.mappers.UserAttendanceLogRowMapper;
import com.lernen.cloud.core.api.common.DBLockMode;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.SharedConstants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */

public class UserAttendanceDao {
    private static final Logger logger = LogManager.getLogger(UserAttendanceDao.class);

    private static final String INSERT_ATTENDANCE_LOG = "insert into user_attendance_log (institute_id, " +
            "user_id, user_type, input_type, log_time, attendance_time, attendance_date, logged_by, metadata) values (?, ?, ?, ?, ?, ?, ?, ?, ?)";

    private static final String GET_ATTENDANCE_LOGS_COUNT_IN_DURATION = "select count(*) from user_attendance_log where " +
            "institute_id = ? and user_id = ? and ((attendance_time >= ? and attendance_time <= ?) or " +
            "(attendance_time >= ? and attendance_time <= ?)) FOR UPDATE ";

    private static final String GET_ATTENDANCE_LOGS_FOR_USER = "select * from user_attendance_log where " +
            "institute_id = ? and user_id = ? and attendance_date = ? %s";

    private static final UserAttendanceLogRowMapper USER_ATTENDANCE_LOG_ROW_MAPPER = new UserAttendanceLogRowMapper();
    private final JdbcTemplate jdbcTemplate;

    private final TransactionTemplate transactionTemplate;

    private static final long SKIP_ATTENDANCE_LOG_DURATION_IN_MILLI_SEC = 60 * 1000L * 2;

    public UserAttendanceDao(JdbcTemplate jdbcTemplate, TransactionTemplate transactionTemplate) {
        this.jdbcTemplate = jdbcTemplate;
        this.transactionTemplate = transactionTemplate;
    }

    public Boolean addAttendanceLog(int instituteId, AttendanceLog attendanceLog) {
        if (instituteId <= 0 || attendanceLog == null) {
            logger.error("Invalid attendance payload");
            return false;
        }

        return transactionTemplate.execute(new TransactionCallback<Boolean>() {
            @Override
            public Boolean doInTransaction(TransactionStatus status) {
                int logCount = jdbcTemplate.queryForInt(GET_ATTENDANCE_LOGS_COUNT_IN_DURATION, instituteId, attendanceLog.getUserId().toString(),
                        new Timestamp(attendanceLog.getAttendanceTime() - SKIP_ATTENDANCE_LOG_DURATION_IN_MILLI_SEC),
                        new Timestamp(attendanceLog.getAttendanceTime()),
                        new Timestamp(attendanceLog.getAttendanceTime()),
                        new Timestamp(attendanceLog.getAttendanceTime() + SKIP_ATTENDANCE_LOG_DURATION_IN_MILLI_SEC));

                if (logCount > 0) {
                    logger.warn("Skipped log event {}, for {} as it lies in skip limit of {}", attendanceLog, instituteId, SKIP_ATTENDANCE_LOG_DURATION_IN_MILLI_SEC);
                    return null;
                }
                return addAttendanceLog(instituteId, Arrays.asList(attendanceLog));
            }
        });
    }

    public boolean addAttendanceLog(int instituteId, List<AttendanceLog> attendanceLogList) {
        if (instituteId <= 0 || CollectionUtils.isEmpty(attendanceLogList)) {
            logger.error("Invalid attendance payload");
            return false;
        }

        List<Object[]> args = new ArrayList<>();
        for (AttendanceLog attendanceLog : attendanceLogList) {
            Timestamp attendanceDate = new Timestamp(DateUtils.getDayStart(attendanceLog.getAttendanceTime(), DateUtils.DEFAULT_TIMEZONE));
            args.add(new Object[]{instituteId, attendanceLog.getUserId().toString(),
                    attendanceLog.getUserType().name(), attendanceLog.getInputType().name(),
                    new Timestamp(attendanceLog.getLogTime()), new Timestamp(attendanceLog.getAttendanceTime()), attendanceDate,
                    attendanceLog.getLoggedBy() == null ? null : attendanceLog.getLoggedBy().toString(),
                    attendanceLog.getMetadata() == null ? null : SharedConstants.GSON.toJson(attendanceLog.getMetadata())});
        }

        try {
            return jdbcTemplate.batchUpdate(INSERT_ATTENDANCE_LOG, args).length == attendanceLogList.size();
        } catch (Exception e) {
            logger.error("Error while adding attendance log for institute {}, {}", instituteId, attendanceLogList, e);
        }
        return false;
    }

    public List<AttendanceLog> getAttendanceLogs(int instituteId, UUID userId, int attendanceDate, boolean forUpdate) {
        try {
            final String forUpdateClause = forUpdate ? DBLockMode.FOR_UPDATE.getCommand() : "";
            Timestamp attendanceDateTimestamp = new Timestamp(DateUtils.getDayStart(attendanceDate, DateUtils.DEFAULT_TIMEZONE) * 1000l);
            return jdbcTemplate.query(String.format(GET_ATTENDANCE_LOGS_FOR_USER, forUpdateClause), new Object[]{instituteId, userId.toString(),
                    attendanceDateTimestamp}, USER_ATTENDANCE_LOG_ROW_MAPPER);
        } catch (Exception e) {
            logger.error("Error while getting attendance log for institute {}, user {}, attendanceDate {}", instituteId, userId, attendanceDate, e);
        }

        return null;
    }
}
