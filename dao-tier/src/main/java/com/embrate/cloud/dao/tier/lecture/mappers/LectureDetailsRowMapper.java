/**
 * 
 */
package com.embrate.cloud.dao.tier.lecture.mappers;

import com.embrate.cloud.core.api.lecture.LectureDetails;
import com.embrate.cloud.core.api.lecture.LectureStatus;
import com.google.gson.reflect.TypeToken;
import com.lernen.cloud.core.api.course.Course;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.dao.tier.course.mappers.CourseRowMapper;
import com.lernen.cloud.dao.tier.institute.mappers.StandardRowDetailsRowMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import java.lang.reflect.Type;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;

import static com.lernen.cloud.core.utils.SharedConstants.GSON;

/**
 * <AUTHOR>
 *
 */
public class LectureDetailsRowMapper implements RowMapper<LectureDetails>{

	private static final StandardRowDetailsRowMapper STANDARD_ROW_DETAILS_ROW_MAPPER = new StandardRowDetailsRowMapper();
	private static final CourseRowMapper COURSE_ROW_MAPPER = new CourseRowMapper();

	protected static final String INSTITUTE_ID = "institute_id";
	protected static final String LECTURE_ID = "lecture_id";
	protected static final String SECTION_IDS = "lecture_details.section_ids";
	protected static final String CHAPTER = "chapter";
	protected static final String TITLE = "title";
	protected static final String VIDEO_LINK = "video_link";
	protected static final String LECTURER_USER_ID = "lecturer_user_id";
	protected static final String LECTURER_NAME = "lecturer_name";
	protected static final String CREATED_USER_ID = "created_user_id";
	protected static final String CREATED_TIMESTAMP = "created_timestamp";
	protected static final String UPDATED_USER_ID = "updated_user_id";
	protected static final String UPDATED_TIMESTAMP = "updated_timestamp";
	protected static final String DESCRIPTION = "description";
	protected static final String RECOMMENDED_VIEW_DATE = "recommended_view_date";
	protected static final String LECTURE_STATUS = "status";
	protected static final String SAVED_AT = "saved_at";
	protected static final String BROADCASTED_AT = "broadcasted_at";
	
	
	@Override
	public LectureDetails mapRow(ResultSet rs, int rowNum) throws SQLException {
		String lectureIdString = rs.getString(LECTURE_ID);
		if(StringUtils.isBlank(lectureIdString)) {
			return null;
		}
		
		final Standard standard = StandardRowDetailsRowMapper.getStandardResponse(STANDARD_ROW_DETAILS_ROW_MAPPER.mapRow(rs, rowNum));
		final Course course = COURSE_ROW_MAPPER.mapRow(rs, rowNum);
		
		final Timestamp createdTimestamp = rs.getTimestamp(CREATED_TIMESTAMP);
		final Integer createdTimestampTime = createdTimestamp == null ? null
				: (int) (createdTimestamp.getTime() / 1000l);
		
		final Timestamp updatedTimestamp = rs.getTimestamp(UPDATED_TIMESTAMP);
		final Integer updatedTimestampTime = updatedTimestamp == null ? null
				: (int) (updatedTimestamp.getTime() / 1000l);
		
		final Timestamp recommendedViewDate = rs.getTimestamp(RECOMMENDED_VIEW_DATE);
		final Integer recommendedViewDateTime = recommendedViewDate == null ? null
				: (int) (recommendedViewDate.getTime() / 1000l);
		
		final Timestamp savedAtDate = rs.getTimestamp(SAVED_AT);
		final Integer savedAtDateTime = savedAtDate == null ? null
				: (int) (savedAtDate.getTime() / 1000l);
		
		final Timestamp broadcastAtDate = rs.getTimestamp(BROADCASTED_AT);
		final Integer broadcastAtDateTime = broadcastAtDate == null ? null
				: (int) (broadcastAtDate.getTime() / 1000l);

		final String sectionIdStr = rs.getString(SECTION_IDS);
		Set<Integer> sectionIdList = null;
		if (!StringUtils.isBlank(sectionIdStr)) {
			final Type collectionType = new TypeToken<Set<Integer>>() {
			}.getType();
			sectionIdList = GSON.fromJson(sectionIdStr, collectionType);
		}

		return new LectureDetails(rs.getInt(INSTITUTE_ID), UUID.fromString(lectureIdString), standard, sectionIdList, course, rs.getString(CHAPTER),
				rs.getString(TITLE), rs.getString(VIDEO_LINK), rs.getString(LECTURER_USER_ID) == null ? null : UUID.fromString(rs.getString(LECTURER_USER_ID)),
				rs.getString(LECTURER_NAME), UUID.fromString(rs.getString(CREATED_USER_ID)), createdTimestampTime, 
				rs.getString(UPDATED_USER_ID) == null ? null : UUID.fromString(rs.getString(UPDATED_USER_ID)), updatedTimestampTime,
				rs.getString(DESCRIPTION), recommendedViewDateTime, LectureStatus.getLectureStatus(rs.getString(LECTURE_STATUS)),
				savedAtDateTime, broadcastAtDateTime);
	}

	public static LectureDetails combineSectionsOfSameStandard(List<LectureDetails> lectureDetailsList) {
		if(CollectionUtils.isEmpty(lectureDetailsList)) {
			return null;
		}
		
		List<StandardSections> standardSections = new ArrayList<StandardSections>(); 
		for(LectureDetails lectureDetails : lectureDetailsList) {
			standardSections.addAll(lectureDetails.getStandard().getStandardSectionList());
		}
		
		lectureDetailsList.get(0).getStandard().setStandardSectionList(standardSections);
		
		LectureDetails lectureDetails = new LectureDetails(lectureDetailsList.get(0).getInstituteId(),
				lectureDetailsList.get(0).getLectureId(), lectureDetailsList.get(0).getStandard(),
				lectureDetailsList.get(0).getSectionIdList(), lectureDetailsList.get(0).getCourse(),
				lectureDetailsList.get(0).getChapter(), lectureDetailsList.get(0).getTitle(), lectureDetailsList.get(0).getVideoLink(),
				lectureDetailsList.get(0).getLecturerUserId(), lectureDetailsList.get(0).getLecturerName(), lectureDetailsList.get(0).getCreatedUserId(),
				lectureDetailsList.get(0).getCreatedTimestamp(), lectureDetailsList.get(0).getUpdatedUserId(), lectureDetailsList.get(0).getUpdatedTimestamp(),
				lectureDetailsList.get(0).getDescription(), lectureDetailsList.get(0).getRecommendedViewDate(), 
				lectureDetailsList.get(0).getLectureStatus(), lectureDetailsList.get(0).getSavedAt(), lectureDetailsList.get(0).getBroadcastedAt());
		
		return lectureDetails;
	}

	/**
	 * converting broadcast date to start of Day as it will have same 
	 * datetime for all the lectures in a day which will help in 
	 * sorting it. 
	 */
	public static List<LectureDetails> getLectureDetailsList(List<LectureDetails> lectureDetailsList) {
		if(CollectionUtils.isEmpty(lectureDetailsList)) {
			return null;
		}
		Map<UUID, LectureDetails> lectureDetailsMap = new HashMap<UUID, LectureDetails>();
		for(LectureDetails lectureDetails : lectureDetailsList) {
			if(lectureDetails.getBroadcastedAt() != null && lectureDetails.getBroadcastedAt() > 0) {
				lectureDetails.setBroadcastedAt(DateUtils.getDayStart(lectureDetails.getBroadcastedAt(), DateUtils.DEFAULT_TIMEZONE));
			}
			if(lectureDetailsMap.containsKey(lectureDetails.getLectureId())) {
				lectureDetailsMap.get(lectureDetails.getLectureId()).getStandard().getStandardSectionList()
				.addAll(lectureDetails.getStandard().getStandardSectionList());
			} else {
				lectureDetailsMap.put(lectureDetails.getLectureId(), lectureDetails);
			}
		}
		return new ArrayList<LectureDetails>(lectureDetailsMap.values());
	}
	
}
