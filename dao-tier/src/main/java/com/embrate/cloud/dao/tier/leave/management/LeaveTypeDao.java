package com.embrate.cloud.dao.tier.leave.management;

import com.embrate.cloud.core.api.leave.management.LeaveType;
import com.embrate.cloud.dao.tier.leave.management.mappers.LeaveTypeRowMapper;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.utils.PlaceholdersUtils;
import com.lernen.cloud.core.utils.SharedConstants;

import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */

public class LeaveTypeDao {
    private static final Logger logger = LogManager.getLogger(LeaveTypeDao.class);

    private static final String INSERT_LEAVE_TYPE = "INSERT INTO leave_type (institute_id, name, short_code, payment_type, user_type, description, metadata) " +
            "VALUES (?, ?, ?, ?, ?, ?, ?)";

    private static final String GET_LEAVE_TYPES_FOR_INSTITUTE = "SELECT * FROM leave_type WHERE institute_id = ? order by name";

    private static final String GET_LEAVE_TYPES_FOR_INSTITUTE_BY_USER_TYPE = "SELECT * FROM leave_type WHERE institute_id = ? and user_type = ? ";

    private static final String UPDATE_LEAVE_TYPE = "UPDATE leave_type SET name = ?, short_code = ?, payment_type = ?, " +
            "description = ?, metadata = ? WHERE institute_id = ? and leave_type_id = ?";

    private static final String DELETE_LEAVE_TYPES_FOR_INSTITUTE = "DELETE FROM leave_type WHERE institute_id = ? AND leave_type_id = ?";

    private static final LeaveTypeRowMapper LEAVE_TYPE_ROW_MAPPER = new LeaveTypeRowMapper();
    private final JdbcTemplate jdbcTemplate;

    public LeaveTypeDao(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    // Create operation
    public boolean createLeaveType(int instituteId, LeaveType leaveType) {
        try {
            return jdbcTemplate.update(INSERT_LEAVE_TYPE, instituteId, leaveType.getName().trim(), leaveType.getShortCode().trim(),
                    leaveType.getPaymentType() == null ? null : leaveType.getPaymentType().name(),
                    leaveType.getUserType().name(), leaveType.getDescription() == null ? null : leaveType.getDescription().trim(),
                    leaveType.getMetadata() == null ? null : SharedConstants.GSON.toJson(leaveType.getMetadata())) == 1;
        } catch (Exception e) {
            logger.error("Error while inserting leave type {}, institute {}", leaveType, instituteId, e);
        }
        return false;
    }

    // Read operation
    public List<LeaveType> getLeaveTypes(int instituteId) {
        try {
            return jdbcTemplate.query(GET_LEAVE_TYPES_FOR_INSTITUTE, new Object[]{instituteId}, LEAVE_TYPE_ROW_MAPPER);
        } catch (Exception e) {
            logger.error("Error while getting leave types for institute {}", instituteId, e);
        }
        return null;

    }

    public List<LeaveType> getLeaveTypeByUserType(int instituteId, UserType userType, List<Integer> leaveIds)  {
        try {
            StringBuilder query = new StringBuilder(GET_LEAVE_TYPES_FOR_INSTITUTE_BY_USER_TYPE);
			List<Object> args = new ArrayList<>();
            args.add(instituteId);
            args.add(userType.name());
            if (!CollectionUtils.isEmpty(leaveIds)) {
				query.append(" AND leave_type_id IN (");
				query.append(PlaceholdersUtils.buildPlaceholders(leaveIds.size()));
				query.append(")");
				
				for(int leaveId : leaveIds){
					args.add(leaveId);
				}
			}
            query.append(" order by name");

            return jdbcTemplate.query(query.toString(), args.toArray(), LEAVE_TYPE_ROW_MAPPER);
        } catch (Exception e) {
            logger.error("Error while getting leave types for institute {}", instituteId, e);
        }
        return null;

    }

    // Update operation
    public boolean updateLeaveType(int instituteId, LeaveType leaveType) {
        try {
            return jdbcTemplate.update(UPDATE_LEAVE_TYPE, leaveType.getName().trim(), leaveType.getShortCode().trim(),
                    leaveType.getPaymentType() == null ? null : leaveType.getPaymentType().name(),
                    leaveType.getDescription() == null ? null : leaveType.getDescription().trim(),
                    leaveType.getMetadata() == null ? null : SharedConstants.GSON.toJson(leaveType.getMetadata()), instituteId, leaveType.getLeaveTypeId()) == 1;
        } catch (Exception e) {
            logger.error("Error while updating leave type {}, institute {}", leaveType, instituteId, e);
        }
        return false;
    }

    // Delete operation
    public boolean deleteLeaveType(int instituteId, int leaveTypeId) {
        try {
            return jdbcTemplate.update(DELETE_LEAVE_TYPES_FOR_INSTITUTE, instituteId, leaveTypeId) == 1;
        } catch (Exception e) {
            logger.error("Error while deleting leave type {} , institute {}", leaveTypeId, instituteId, e);
        }
        return false;
    }
}
