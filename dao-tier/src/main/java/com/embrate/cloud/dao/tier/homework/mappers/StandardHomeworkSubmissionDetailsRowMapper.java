/**
 * 
 */
package com.embrate.cloud.dao.tier.homework.mappers;

import com.embrate.cloud.core.api.homework.*;
import com.lernen.cloud.core.api.course.Course;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.dao.tier.course.mappers.CourseRowMapper;
import com.lernen.cloud.dao.tier.institute.mappers.StandardRowDetailsRowMapper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;


/**
 * <AUTHOR>
 *
 */
public class StandardHomeworkSubmissionDetailsRowMapper implements RowMapper<StandardHomeworkSubmissionDetailsRow> {
	
	private static final StandardRowDetailsRowMapper STANDARD_ROW_DETAILS_ROW_MAPPER = new StandardRowDetailsRowMapper();
	private static final CourseRowMapper COURSE_ROW_MAPPER = new CourseRowMapper();
	private static final HomeworkDetailsRowMapper HOMEWORK_DETAILS_ROW_MAPPER = new HomeworkDetailsRowMapper();
	private static final HomeworkSubmissionDetailsRowMapper HOMEWORK_SUBMISSION_DETAILS_ROW_MAPPER = new HomeworkSubmissionDetailsRowMapper();

	protected static final String INSTITUTE_ID = "standards.institute_id";

	@Override
	public StandardHomeworkSubmissionDetailsRow mapRow(ResultSet rs, int rowNum) throws SQLException {

		final Standard standard = StandardRowDetailsRowMapper
				.getStandardResponse(STANDARD_ROW_DETAILS_ROW_MAPPER.mapRow(rs, rowNum));
		final Course course = COURSE_ROW_MAPPER.mapRow(rs, rowNum);
		final HomeworkDetails homeworkDetails = HOMEWORK_DETAILS_ROW_MAPPER.mapRow(rs, rowNum);
		final HomeworkSubmissionDetails homeworkSubmissionDetails = HOMEWORK_SUBMISSION_DETAILS_ROW_MAPPER.mapRow(rs, rowNum);

		return new StandardHomeworkSubmissionDetailsRow(rs.getInt(INSTITUTE_ID), standard, course, homeworkDetails,
				homeworkSubmissionDetails);
	}
	
	/**
	 * This method expects that all the rows belong to same session and standard
	 * @param <V>
	 * 
	 * @param standardHomeworkSubmissionDetailsRow
	 * @return
	 */
	public static <V> StandardHomeworkSubmissionDetails getHomeworkSubmissionDetailsByStandard(
			List<StandardHomeworkSubmissionDetailsRow> standardHomeworkSubmissionDetailsRow) {

		if (CollectionUtils.isEmpty(standardHomeworkSubmissionDetailsRow)) {
			return null;
		}

		// standard section map
		Map<Integer, StandardSections> standardSectionMap = new HashMap<Integer, StandardSections>();

		// course Id and course
		Map<UUID, Course> courseMap = new HashMap<UUID, Course>();
		// course lecture stats map
		Map<UUID, Map<UUID, HomeworkDetails>> lectureStats = new HashMap<UUID, Map<UUID, HomeworkDetails>>();
		//homeworkId and homework submission detail list
		Map<UUID, Map<UUID, HomeworkSubmissionDetails>> homeworkSubmissionDetailsMap = new HashMap<UUID, Map<UUID, HomeworkSubmissionDetails>>();

		UUID emptyCourseUUID = UUID.randomUUID();
		for (StandardHomeworkSubmissionDetailsRow standardHomeworkSubmissionDetails : standardHomeworkSubmissionDetailsRow) {
			// standard section map
			for (StandardSections standardSection : standardHomeworkSubmissionDetails.getStandard().getStandardSectionList()) {
				if (!standardSectionMap.containsKey(standardSection.getSectionId())) {
					standardSectionMap.put(standardSection.getSectionId(), standardSection);
				}
			}

			/**
			 * For case where there are no lectures for given session and standard. Here just
			 * return the empty lecture list but want to show standard
			 */
//			if (standardHomeworkSubmissionDetails.getCourse() == null) {
//				continue;
//			}
			if (standardHomeworkSubmissionDetails.getHomeworkDetails()== null) {
				continue;
			}

			UUID courseId = standardHomeworkSubmissionDetails.getCourse() == null ? emptyCourseUUID
					: standardHomeworkSubmissionDetails.getCourse().getCourseId();
			// course Map
			if (!courseMap.containsKey(courseId)) {
				courseMap.put(courseId, standardHomeworkSubmissionDetails.getCourse());
			}

			if (!lectureStats.containsKey(courseId)) {
				Map<UUID, HomeworkDetails> lectureAndStats = new HashMap<>();
				lectureAndStats.put(standardHomeworkSubmissionDetails.getHomeworkDetails().getHomeworkId(),
						standardHomeworkSubmissionDetails.getHomeworkDetails());

				lectureStats.put(courseId, lectureAndStats);
			} else if (!lectureStats.get(courseId)
					.containsKey(standardHomeworkSubmissionDetails.getHomeworkDetails().getHomeworkId())) {
				lectureStats.get(courseId).put(standardHomeworkSubmissionDetails.getHomeworkDetails().getHomeworkId(),
						standardHomeworkSubmissionDetails.getHomeworkDetails());
			}
			
			UUID homeworkId = standardHomeworkSubmissionDetails.getHomeworkDetails().getHomeworkId();
			if (!homeworkSubmissionDetailsMap.containsKey(homeworkId)) {
				Map<UUID, HomeworkSubmissionDetails> lectureAndStats = new HashMap<>();
				lectureAndStats.put(standardHomeworkSubmissionDetails.getHomeworkDetails().getHomeworkId(),
						standardHomeworkSubmissionDetails.getHomeworkSubmissionDetails());

				homeworkSubmissionDetailsMap.put(homeworkId, lectureAndStats);
			} else if (!homeworkSubmissionDetailsMap.get(homeworkId)
					.containsKey(standardHomeworkSubmissionDetails.getHomeworkSubmissionDetails().getHomeworkSubmissionId())) {
				homeworkSubmissionDetailsMap.get(homeworkId).put(standardHomeworkSubmissionDetails.getHomeworkDetails().getHomeworkId(),
						standardHomeworkSubmissionDetails.getHomeworkSubmissionDetails());
			}
		}

		List<StandardCoursesHomeworkSubmissionDetails> standardCoursesHomeworkDetailsList = new ArrayList<StandardCoursesHomeworkSubmissionDetails>();
		for (Map.Entry<UUID, Course> courseEntry : courseMap.entrySet()) {			
			List<HomeworkAndSubmissionDetails> homeworkAndSubmissionDetailsList = new ArrayList<HomeworkAndSubmissionDetails>();
			for(Map.Entry<UUID, HomeworkDetails> lectureStatsEntry : lectureStats.get(courseEntry.getKey()).entrySet()) {
				homeworkAndSubmissionDetailsList.add(new HomeworkAndSubmissionDetails(lectureStatsEntry.getValue(),
						new ArrayList<>(homeworkSubmissionDetailsMap.get(lectureStatsEntry.getKey()).values())));
			}
			standardCoursesHomeworkDetailsList
					.add(new StandardCoursesHomeworkSubmissionDetails(courseEntry.getValue(), homeworkAndSubmissionDetailsList));
		}
		List<StandardSections> standardSectionList = new ArrayList<StandardSections>(standardSectionMap.values());
		Standard standard = new Standard(standardHomeworkSubmissionDetailsRow.get(0).getInstituteId(),
				standardHomeworkSubmissionDetailsRow.get(0).getStandard().getAcademicSessionId(),
				standardHomeworkSubmissionDetailsRow.get(0).getStandard().getStandardId(),
				standardHomeworkSubmissionDetailsRow.get(0).getStandard().getStandardName(),
				standardHomeworkSubmissionDetailsRow.get(0).getStandard().getStream(),
				standardHomeworkSubmissionDetailsRow.get(0).getStandard().getLevel(), standardSectionList);

		return getFinalResult(new StandardHomeworkSubmissionDetails(standardHomeworkSubmissionDetailsRow.get(0).getInstituteId(), standard,
				standardCoursesHomeworkDetailsList));
	}

	private static StandardHomeworkSubmissionDetails getFinalResult(
			StandardHomeworkSubmissionDetails standardHomeworkSubmissionDetails) {
		for(StandardCoursesHomeworkSubmissionDetails standardCoursesHomeworkSubmissionDetails : standardHomeworkSubmissionDetails.getStandardCoursesHomeworkSubmissionDetailsList()) {
			for(HomeworkAndSubmissionDetails homeworkAndSubmissionDetails : standardCoursesHomeworkSubmissionDetails.getHomeworkAndSubmissionDetailsList()) {
				homeworkAndSubmissionDetails.setHomeworkSubmissionDetailsList(
						HomeworkSubmissionDetailsRowMapper.getLatestHomeworkDetailsForEveryStudent(homeworkAndSubmissionDetails.getHomeworkSubmissionDetailsList()));
			}
		}
		return standardHomeworkSubmissionDetails;
	}
}