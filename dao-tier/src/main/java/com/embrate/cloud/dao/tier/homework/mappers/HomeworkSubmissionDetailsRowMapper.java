/**
 * 
 */
package com.embrate.cloud.dao.tier.homework.mappers;

import com.embrate.cloud.core.api.homework.*;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.dao.tier.student.mappers.StudentRowMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import java.lang.reflect.Type;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;

/**
 * <AUTHOR>
 *
 */
public class HomeworkSubmissionDetailsRowMapper implements RowMapper<HomeworkSubmissionDetails>{

	private static final HomeworkDetailsRowMapper HOMEWORK_DETAILS_ROW_MAPPER = new HomeworkDetailsRowMapper();
	private static final StudentRowMapper STUDENT_ROW_MAPPER = new StudentRowMapper();
	private static final Gson GSON = new Gson();
	
	protected static final String HOMEWORK_SUBMISSION_ID = "homework_submission_details.homework_submission_id";
	protected static final String HOMEWORK_ID = "homework_submission_details.homework_id";
	protected static final String STUDENT_ID = "homework_submission_details.student_id";
	protected static final String SUBMITTED_BY = "homework_submission_details.submitted_by";
	protected static final String DESCRIPTION = "homework_submission_details.description";
	protected static final String ATTACHMENTS = "homework_submission_details.attachments";
	protected static final String STATUS = "homework_submission_details.status";
	protected static final String SUBMISSION_TIMESTAMP = "homework_submission_details.submission_timestamp";
	protected static final String TEACHER_STATUS_USER_ID = "homework_submission_details.teacher_status_user_id";
	protected static final String TEACHER_STATUS_TIMESTAMP = "homework_submission_details.teachers_status_timestamp";
	protected static final String UPDATED_TIMESTAMP = "homework_submission_details.updated_timestamp";
	protected static final String RESULT = "homework_submission_details.result";
	protected static final String REMARKS = "homework_submission_details.remarks";	
	
	@Override
	public HomeworkSubmissionDetails mapRow(ResultSet rs, int rowNum) throws SQLException {
		String homeworkSubmissionIdStr = rs.getString(HOMEWORK_SUBMISSION_ID);
		if(StringUtils.isBlank(homeworkSubmissionIdStr)) {
			return null;
		}
		
		final HomeworkDetails homeworkDetails = HOMEWORK_DETAILS_ROW_MAPPER.mapRow(rs, rowNum);
		final StudentLite studentLite = Student.getStudentLite(STUDENT_ROW_MAPPER.mapRow(rs, rowNum));
		
		final Timestamp submissionTimestamp = rs.getTimestamp(SUBMISSION_TIMESTAMP);
		final Integer submissionTimestampTime = submissionTimestamp == null ? null
				: (int) (submissionTimestamp.getTime() / 1000l);
		
		final Timestamp updatedTimestamp = rs.getTimestamp(UPDATED_TIMESTAMP);
		final Integer updatedTimestampTime = updatedTimestamp == null ? null
				: (int) (updatedTimestamp.getTime() / 1000l);
		
		final Timestamp teacherStatusTimestamp = rs.getTimestamp(TEACHER_STATUS_TIMESTAMP);
		final Integer teacherStatusTimestampTime = teacherStatusTimestamp == null ? null
				: (int) (teacherStatusTimestamp.getTime() / 1000l);
		
		final String documents = rs.getString(ATTACHMENTS);
		List<Document<HomeworkDocumentType>> homeworkSubmissionAttachments = null;
		if (!StringUtils.isBlank(documents)) {
			final Type collectionType = new TypeToken<List<Document<HomeworkDocumentType>>>() {
			}.getType();
			homeworkSubmissionAttachments = GSON.fromJson(documents, collectionType);
		}
		
		return new HomeworkSubmissionDetails(UUID.fromString(homeworkSubmissionIdStr), homeworkDetails, studentLite,rs.getString(SUBMITTED_BY) == null ? null : UUID.fromString(rs.getString(SUBMITTED_BY)),
				rs.getString(DESCRIPTION), HomeworkSubmissionStatus.getHomeworkSubmissionStatus(rs.getString(STATUS)),
				submissionTimestampTime, teacherStatusTimestampTime,
				rs.getString(TEACHER_STATUS_USER_ID) == null ? null : UUID.fromString(rs.getString(TEACHER_STATUS_USER_ID)),
				updatedTimestampTime, rs.getString(RESULT), rs.getString(REMARKS), homeworkSubmissionAttachments);
	}

	public static HomeworkSubmissionDetails getHomeworkSubmissionResponse(List<HomeworkSubmissionDetails> homeworkSubmissionDetailsList) {
		if(CollectionUtils.isEmpty(homeworkSubmissionDetailsList)) {
			return null;
		}
		homeworkSubmissionDetailsList = getLatestHomeworkDetailsForEveryStudent(homeworkSubmissionDetailsList);
		List<StandardSections> standardSectionList = new ArrayList<StandardSections>();
		for(HomeworkSubmissionDetails homeworkSubmissionDetails : homeworkSubmissionDetailsList) {
			standardSectionList.addAll(homeworkSubmissionDetails.getHomeworkDetails().getStandard().getStandardSectionList());
		}
		homeworkSubmissionDetailsList.get(0).getHomeworkDetails().getStandard().setStandardSectionList(standardSectionList);
		return homeworkSubmissionDetailsList.get(0);
	}

	public static List<HomeworkSubmissionDetails> getLatestHomeworkDetailsForEveryStudent(
			List<HomeworkSubmissionDetails> homeworkSubmissionDetailsList) {
		Map<UUID, HomeworkSubmissionDetails> homeworkSubmissionDetailsMap = new HashMap<>();
		for(HomeworkSubmissionDetails homeworkSubmissionDetails : homeworkSubmissionDetailsList) {
			UUID studentId = homeworkSubmissionDetails.getStudentLite().getStudentId();
			if(homeworkSubmissionDetailsMap.containsKey(studentId)) {
				Integer newSubmissionTimestamp = homeworkSubmissionDetails.getSubmissionTimestamp();
				Integer oldSubmissionTimestamp = homeworkSubmissionDetailsMap.get(studentId).getSubmissionTimestamp();
				if(newSubmissionTimestamp > oldSubmissionTimestamp) {
					homeworkSubmissionDetailsMap.put(studentId, homeworkSubmissionDetails);
				}
			} else {
				homeworkSubmissionDetailsMap.put(studentId, homeworkSubmissionDetails);
			}
		}
		return new ArrayList<>(homeworkSubmissionDetailsMap.values());
	}

	public static List<HomeworkSubmissionDetails> getHomeworkSubmissionDetailsByHomeworkId(
			List<HomeworkSubmissionDetails> homeworkSubmissionDetailsList) {
		if(CollectionUtils.isEmpty(homeworkSubmissionDetailsList)) {
			return null;
		}
		homeworkSubmissionDetailsList = getLatestHomeworkDetailsForEveryStudent(homeworkSubmissionDetailsList);
		Map<UUID, HomeworkSubmissionDetails> homeworkSubmissionMap = new HashMap<UUID, HomeworkSubmissionDetails>();
		for(HomeworkSubmissionDetails homeworkSubmissionDetails : homeworkSubmissionDetailsList) {
			if(homeworkSubmissionMap.containsKey(homeworkSubmissionDetails.getHomeworkSubmissionId())) {
				homeworkSubmissionMap.get(homeworkSubmissionDetails.getHomeworkSubmissionId())
				.getHomeworkDetails().getStandard().getStandardSectionList().
				addAll(homeworkSubmissionDetails.getHomeworkDetails().getStandard().getStandardSectionList());
			}
			else {
				homeworkSubmissionMap.put(homeworkSubmissionDetails.getHomeworkSubmissionId(), homeworkSubmissionDetails);
			}
		}

		return new ArrayList<>(homeworkSubmissionMap.values());
	}
}