package com.embrate.cloud.dao.tier.inventory.v2.mappers;

import com.embrate.cloud.core.api.inventory.v2.brand.InventoryBrand;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public class InventoryBrandRowMapper implements RowMapper<InventoryBrand> {

    private static final String BRAND_ID = "inventory_brand.brand_id";
    private static final String BRAND_NAME = "inventory_brand.brand_name";
    private static final String CREATED_AT = "inventory_brand.created";
    private static final String UPDATED_AT = "inventory_brand.updated";

    @Override
    public InventoryBrand mapRow(ResultSet rs, int rowNum) throws SQLException {
        String brandIdStr = rs.getString(BRAND_ID);
        UUID brandId = null;
        if(StringUtils.isNotBlank(brandIdStr)){
            brandId = UUID.fromString(brandIdStr);
        }
        return new InventoryBrand(brandId, rs.getString(BRAND_NAME));
    }
}
