package com.embrate.cloud.dao.tier.inventory.v2;

import com.embrate.cloud.core.api.inventory.v2.InventoryCounterData;
import com.embrate.cloud.core.api.inventory.v2.InventoryCounterType;
import com.embrate.cloud.core.api.inventory.v2.brand.InventoryBrand;
import com.embrate.cloud.core.api.inventory.v2.category.InventoryCategory;
import com.embrate.cloud.core.api.inventory.v2.outlet.InventoryOutlet;
import com.embrate.cloud.core.api.inventory.v2.supplier.InventorySupplier;
import com.embrate.cloud.dao.tier.inventory.v2.mappers.*;
import com.lernen.cloud.core.api.common.DBLockMode;
import com.lernen.cloud.core.utils.SharedConstants;
import com.lernen.cloud.core.utils.crypto.CryptoUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;

/**
 * <AUTHOR>
 */
public class InventoryOutletDao {

    private static final Logger logger = LogManager.getLogger(InventoryOutletDao.class);

    private static final InventoryOutletRowMapper INVENTORY_OUTLET_ROW_MAPPER = new InventoryOutletRowMapper();
    private static final InventorySupplierRowMapper INVENTORY_SUPPLIER_ROW_MAPPER = new InventorySupplierRowMapper();
    private static final InventoryCategoryRowMapper INVENTORY_CATEGORY_ROW_MAPPER = new InventoryCategoryRowMapper();
    private static final InventoryBrandRowMapper INVENTORY_BRAND_ROW_MAPPER = new InventoryBrandRowMapper();
    private static final InventoryCounterRowMapper INVENTORY_COUNTER_ROW_MAPPER = new InventoryCounterRowMapper();

        private static final String ADD_OUTLET = "insert into inventory_outlet (outlet_id, name, organisation_id, " +
            " institute_scope, description) values(?, ?, ?, ?, ?)";
    private static final String ADD_SUPPLIER = "insert into inventory_supplier"
            + "(outlet_id, supplier_id, supplier_name, contact_name, address, city, state, zipcode, country, email, primary_phone_number, secondary_phone_number, landline )"
            + " values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    private static final String UPDATE_SUPPLIER = "update inventory_supplier set supplier_name = ?, contact_name = ?, address = ?, city = ?, "
            + "state = ?, zipcode = ?, country = ?, email = ?, primary_phone_number = ?, secondary_phone_number = ?, landline = ? " +
            "where outlet_id = ? and supplier_id = ?";


    private static final String GET_SUPPLIER_BY_ID = "select * from inventory_supplier where outlet_id = ? and supplier_id = ?";
    private static final String GET_ALL_SUPPLIER_FOR_OUTLET = "select * from inventory_supplier where outlet_id = ? order by supplier_name";
    private static final String DELETE_SUPPLIER = "delete from inventory_supplier where outlet_id = ? and supplier_id = ?";

    private static final String SEARCH_SUPPLIER = "select * from inventory_supplier ";
    private static final String NAME_SEARCH_CONDITION = " supplier_name like ? ";
    private static final String AND = " and ";


    private static final String ADD_CATEGORY = "insert into inventory_category"
            + "(outlet_id, category_name, description)"
            + " values (?, ?, ?)";

    private static final String UPDATE_CATEGORY = "update inventory_category set "
            + "category_name = ?, description = ? where outlet_id = ? and category_id = ?";

    private static final String GET_ALL_CATEGORY_FOR_OUTLET = "select * from inventory_category where outlet_id = ? order by category_name";
    private static final String DELETE_CATEGORY = "delete from inventory_category where outlet_id = ? and category_id = ?";

    // Brand Queries
    private static final String UPDATE_BRAND_NAME = "update inventory_brand set brand_name = ? where outlet_id = ? and brand_id = ?";
    private static final String ADD_BRAND_INFO = "insert into inventory_brand (outlet_id, brand_id, brand_name) values (?, ?, ?)";
    private static final String GET_BRAND = "select * from inventory_brand where outlet_id = ? and brand_id = ?";
    private static final String GET_OUTLET_BRANDS = "select * from inventory_brand where outlet_id = ?";
    private static final String DELETE_BRAND = "delete from inventory_brand where outlet_id = ? and brand_id = ?";


    private static final String GET_OUTLET_DETAILS = "select * from inventory_outlet where outlet_id = ?";


    private static final String INSERT_OUTLET_COUNTER = "insert into inventory_counter (outlet_id, counter_type, count, counter_prefix) values (?, ?, ?, ?)";

    private static final String GET_OUTLET_COUNTER = "select * from inventory_counter where outlet_id = ? and counter_type = ? %s";
    private static final String UPDATE_OUTLET_COUNTER = "update inventory_counter set count = count + ? where outlet_id = ? and counter_type = ?";


    private static final Map<UUID, InventoryOutlet> INVENTORY_OUTLET_CACHE = new HashMap<>();
    private final JdbcTemplate jdbcTemplate;
    private final TransactionTemplate transactionTemplate;

    public InventoryOutletDao(JdbcTemplate jdbcTemplate, TransactionTemplate transactionTemplate) {
        this.jdbcTemplate = jdbcTemplate;
        this.transactionTemplate = transactionTemplate;
    }

    public UUID addOutlet(InventoryOutlet outlet) {
        UUID outletId = UUID.randomUUID();
        try {
            int rows = jdbcTemplate.update(ADD_OUTLET, outletId.toString(), outlet.getName().trim(),
                    outlet.getOrganisationId() == null ? null : outlet.getOrganisationId().toString(),
                    CollectionUtils.isEmpty(outlet.getInstituteScope()) ? null : SharedConstants.GSON.toJson(outlet.getInstituteScope()),
                    StringUtils.isBlank(outlet.getDescription()) ? null : outlet.getDescription().trim());
            return rows == 1 ? outletId : null;
        } catch (final Exception e) {
            logger.error("Error while adding outlet {}", outlet, e);
        }
        return null;
    }

    public InventoryOutlet getOutlet(UUID outletId) {
        if (outletId == null) {
            logger.error("Invalid outlet request {}", outletId);
            return null;
        }
        if (INVENTORY_OUTLET_CACHE.containsKey(outletId)) {
            return INVENTORY_OUTLET_CACHE.get(outletId);
        }
        try {
            InventoryOutlet inventoryOutlet = jdbcTemplate.queryForObject(GET_OUTLET_DETAILS, new Object[]{outletId.toString()}, INVENTORY_OUTLET_ROW_MAPPER);
            INVENTORY_OUTLET_CACHE.put(outletId, inventoryOutlet);
            return inventoryOutlet;
        } catch (Exception e) {
            logger.error("Error while getting outlet for outletId {}", outletId, e);
        }
        return null;
    }

    public InventoryCounterData getCounter(UUID outletId, InventoryCounterType counterType, boolean forUpdate) {
        if (outletId == null || (counterType == null)) {
            return null;
        }
        final String forUpdateClause = forUpdate ? DBLockMode.FOR_UPDATE.getCommand() : "";
        final Object[] args = {outletId.toString(), counterType.name()};
        try {
            return jdbcTemplate.queryForObject(String.format(GET_OUTLET_COUNTER, forUpdateClause), args,
                    INVENTORY_COUNTER_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Error while fetching the counter for {}, {}, {}", outletId, counterType, forUpdate, e);
        }
        return null;
    }

    public boolean insertCounter(UUID outletId, InventoryCounterData inventoryCounterData) {
        if (outletId == null || (inventoryCounterData == null)) {
            return false;
        }

        final Object[] args = {outletId.toString(), inventoryCounterData.getCounterType().name(), inventoryCounterData.getCount(),
                StringUtils.isBlank(inventoryCounterData.getCounterPrefix()) ? null : inventoryCounterData.getCounterPrefix().trim()};
        try {
            return jdbcTemplate.update(INSERT_OUTLET_COUNTER, args) == 1;
        } catch (final Exception e) {
            logger.error("Error while adding counter for outlet {}, inventoryCounterData {}", outletId,
                    inventoryCounterData, e);
        }

        return false;
    }

    public boolean incrementCounter(UUID outletId, InventoryCounterType counterType) {
        return updateCounter(outletId, counterType, 1);
    }

    public boolean updateCounter(UUID outletId, InventoryCounterType counterType, Integer count) {
        if (outletId == null || (counterType == null)) {
            return false;
        }

        final Object[] args = {count, outletId.toString(), counterType.name()};
        try {
            return jdbcTemplate.update(UPDATE_OUTLET_COUNTER, args) == 1;
        } catch (final Exception e) {
            logger.error("Error while updating counter for outlet {}, counterType {}, count {}", outletId,
                    counterType, count, e);
        }

        return false;
    }

    public UUID addSupplier(UUID outletId, InventorySupplier supplier) {

        if (outletId == null || inValidSupplier(supplier)) {
            logger.error("Invalid outlet or payload {}, {}", outletId, supplier);
            return null;
        }
        UUID supplierId = UUID.randomUUID();
        try {
            int rows = jdbcTemplate.update(ADD_SUPPLIER, outletId.toString(), supplierId.toString(), supplier.getSupplierName().trim(),
                    supplier.getContactName() == null ? null : CryptoUtils.encrypt(supplier.getContactName().trim()),
                    supplier.getAddress() == null ? null : CryptoUtils.encrypt(supplier.getAddress().trim()),
                    supplier.getCity() == null ? null : CryptoUtils.encrypt(supplier.getCity().trim()),
                    supplier.getState() == null ? null : supplier.getState().trim(),
                    supplier.getZipcode() == null ? null : CryptoUtils.encrypt(supplier.getZipcode().trim()),
                    supplier.getCountry() == null ? null : supplier.getCountry().trim(),
                    supplier.getEmail() == null ? null : CryptoUtils.encrypt(supplier.getEmail().trim()),
                    supplier.getPrimaryPhoneNumber() == null ? null : CryptoUtils.encrypt(supplier.getPrimaryPhoneNumber().trim()),
                    supplier.getSecondPhoneNumber() == null ? null : CryptoUtils.encrypt(supplier.getSecondPhoneNumber().trim()),
                    supplier.getLandlineNumber() == null ? null : CryptoUtils.encrypt(supplier.getLandlineNumber().trim()));
            return rows == 1 ? supplierId : null;
        } catch (final Exception e) {
            logger.error("Error while adding supplier {} for outlet {}", supplier, outletId, e);
        }
        return null;
    }

    private boolean inValidSupplier(InventorySupplier supplier) {
        return (supplier == null) || StringUtils.isBlank(supplier.getSupplierName());
    }

    public InventorySupplier getSupplier(UUID outletId, UUID supplierId) {
        if (outletId == null || supplierId == null) {
            return null;
        }
        final Object[] args = {outletId.toString(), supplierId.toString()};
        try {
            return jdbcTemplate.queryForObject(GET_SUPPLIER_BY_ID, args, INVENTORY_SUPPLIER_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Error while getting supplier {} for outlet {}", supplierId, outletId, e);
        }
        return null;
    }

    public List<InventorySupplier> getSuppliers(UUID outletId) {
        if (outletId == null) {
            return null;
        }
        final Object[] args = {outletId.toString()};
        try {
            return jdbcTemplate.query(GET_ALL_SUPPLIER_FOR_OUTLET, args, INVENTORY_SUPPLIER_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Error while getting suppliers for outlet {}", outletId, e);
        }
        return null;
    }

    public List<InventorySupplier> searchSupplier(UUID outletId, String searchText) {
        if (outletId == null) {
            return null;
        }
        try {
            final StringBuilder query = new StringBuilder();
            query.append(SEARCH_SUPPLIER);
            final List<Object> args = new ArrayList<>();
            if (StringUtils.isBlank(searchText)) {
                query.append("where outlet_id = ? order by updated desc");
            } else {
                final String[] keywords = searchText.split(" ");
                boolean first = true;
                for (String keyword : keywords) {
                    keyword = keyword.trim();
                    if (StringUtils.isBlank(keyword)) {
                        continue;
                    }
                    if (first) {
                        query.append("where " + NAME_SEARCH_CONDITION);
                        first = false;
                    } else {
                        query.append(AND).append(NAME_SEARCH_CONDITION);
                    }
                    args.add("%" + keyword + "%");
                }
                if (!args.isEmpty()) {
                    query.append(AND).append(" outlet_id = ? order by updated desc");
                } else {
                    query.append("where outlet_id = ? order by updated desc");
                }
            }
            args.add(outletId.toString());
            return jdbcTemplate.query(query.toString(), args.toArray(), INVENTORY_SUPPLIER_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Error while searching suppliers for {} ", outletId, e);
        }
        return null;
    }

    public boolean deleteSupplier(UUID outletId, UUID supplierId) {
        if (outletId == null || supplierId == null) {
            return false;
        }
        final Object[] args = {outletId.toString(), supplierId.toString()};
        try {
            return jdbcTemplate.update(DELETE_SUPPLIER, args) == 1;
        } catch (final Exception e) {
            logger.error("Error while deleting supplier for {}, {}", outletId, supplierId, e);
        }
        return false;
    }

    public boolean updateSupplier(UUID outletId, InventorySupplier supplier) {
        if (outletId == null || inValidSupplier(supplier) || supplier.getSupplierId() == null) {
            logger.error("Invalid update payload {}, {}", outletId, supplier);
            return false;
        }
        try {
            return jdbcTemplate.update(UPDATE_SUPPLIER, supplier.getSupplierName().trim(),
                    supplier.getContactName() == null ? null : CryptoUtils.encrypt(supplier.getContactName().trim()),
                    supplier.getAddress() == null ? null : CryptoUtils.encrypt(supplier.getAddress().trim()),
                    supplier.getCity() == null ? null : CryptoUtils.encrypt(supplier.getCity().trim()),
                    supplier.getState() == null ? null : supplier.getState().trim(),
                    supplier.getZipcode() == null ? null : CryptoUtils.encrypt(supplier.getZipcode().trim()),
                    supplier.getCountry() == null ? null : supplier.getCountry().trim(),
                    supplier.getEmail() == null ? null : CryptoUtils.encrypt(supplier.getEmail().trim()),
                    supplier.getPrimaryPhoneNumber() == null ? null : CryptoUtils.encrypt(supplier.getPrimaryPhoneNumber().trim()),
                    supplier.getSecondPhoneNumber() == null ? null : CryptoUtils.encrypt(supplier.getSecondPhoneNumber().trim()),
                    supplier.getLandlineNumber() == null ? null : CryptoUtils.encrypt(supplier.getLandlineNumber().trim()),
                    outletId.toString(), supplier.getSupplierId().toString()) == 1;
        } catch (final Exception e) {
            logger.error("Error while updating supplier {}, {}", outletId, supplier, e);
        }
        return false;
    }

    public boolean addCategory(UUID outletId, InventoryCategory category) {

        if (outletId == null || inValidCategory(category)) {
            logger.error("Invalid payload {}, {}", outletId, category);
            return false;
        }

        try {
            return jdbcTemplate.update(ADD_CATEGORY, outletId.toString(),
                    category.getCategoryName().trim(), category.getDescription() == null ?
                            null : category.getDescription().trim()) == 1;
        } catch (final Exception e) {
            logger.error("Error while adding category {}, {}", outletId, category, e);
        }
        return false;
    }

    private boolean inValidCategory(InventoryCategory category) {
        return (category == null) || StringUtils.isBlank(category.getCategoryName());
    }

    public boolean updateCategory(UUID outletId, InventoryCategory category) {
        if (outletId == null || inValidCategory(category) || category.getCategoryId() <= 0) {
            logger.error("Invalid payload {}, {}", outletId, category);
            return false;
        }

        try {
            return jdbcTemplate.update(UPDATE_CATEGORY, category.getCategoryName().trim(),
                    category.getDescription() == null ? null : category.getDescription().trim(),
                    outletId.toString(), category.getCategoryId()) == 1;
        } catch (final Exception e) {
            logger.error("Error while updating category {}, {}", outletId, category, e);
        }
        return false;
    }

    public List<InventoryCategory> getCategories(UUID outletId) {
        if (outletId == null) {
            return null;

        }
        final Object[] args = {outletId.toString()};
        try {
            return jdbcTemplate.query(GET_ALL_CATEGORY_FOR_OUTLET, args, INVENTORY_CATEGORY_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Error while getting categories for {}", outletId, e);
        }
        return null;
    }

    public boolean deleteCategory(UUID outletId, int categoryId) {
        if (outletId == null || categoryId <= 0) {
            return false;
        }
        final Object[] args = {outletId.toString(), categoryId};
        try {
            return jdbcTemplate.update(DELETE_CATEGORY, args) == 1;
        } catch (final Exception e) {
            logger.error("Error while deleting category for {}, {}", outletId, categoryId, e);
        }
        return false;
    }

    public boolean updateBrand(UUID outletId, InventoryBrand inventoryBrand) {
        if (outletId == null || inventoryBrand == null || StringUtils.isBlank(inventoryBrand.getBrandName())) {
            logger.error("Invalid payload {}, {}", outletId, inventoryBrand);
            return false;
        }
        try {
            return jdbcTemplate.update(UPDATE_BRAND_NAME, inventoryBrand.getBrandName().trim(), outletId.toString(),
                    inventoryBrand.getBrandId().toString()) == 1;
        } catch (final Exception e) {
            logger.error("Error while updating brand for {}, {}", outletId, inventoryBrand, e);
        }
        return false;
    }

    public UUID addBrand(UUID outletId, InventoryBrand inventoryBrand) {
        if (outletId == null || inventoryBrand == null || StringUtils.isBlank(inventoryBrand.getBrandName())) {
            logger.error("Invalid payload {}, {}", outletId, inventoryBrand);
            return null;
        }

        try {
            UUID brandId = UUID.randomUUID();
            return jdbcTemplate.update(ADD_BRAND_INFO, outletId.toString(), brandId.toString(), inventoryBrand.getBrandName().trim()) == 1 ? brandId : null;
        } catch (final Exception e) {
            logger.error("Error while adding brand for {}, {}", outletId, inventoryBrand, e);
        }
        return null;
    }

    public List<InventoryBrand> searchBrands(UUID outletId, String searchText) {
        if (outletId == null) {
            return null;
        }
        searchText = searchText == null ? "" : searchText.trim();
        final Object[] args = {outletId.toString(), "%" + searchText + "%"};
        try {
            return jdbcTemplate.query(GET_OUTLET_BRANDS + " and brand_name like ? ", args, INVENTORY_BRAND_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Error while searching brand for {}", outletId, e);
        }
        return null;
    }

    public InventoryBrand getBrand(UUID outletId, UUID brandId) {
        if (outletId == null || (brandId == null)) {
            return null;
        }

        final Object[] args = {outletId.toString(), brandId.toString()};
        try {
            return jdbcTemplate.queryForObject(GET_BRAND, args, INVENTORY_BRAND_ROW_MAPPER);
        } catch (final EmptyResultDataAccessException dataAccessException) {
            return null;
        } catch (final Exception e) {
            logger.error("Error fetching brand for outletId {}, brandId {}", outletId, brandId, e);
        }
        return null;
    }

    public List<InventoryBrand> getBrands(UUID outletId) {
        if (outletId == null) {
            return null;
        }

        final Object[] args = {outletId.toString()};
        try {
            return jdbcTemplate.query(GET_OUTLET_BRANDS, args, INVENTORY_BRAND_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Error fetching brands for outlet {}", outletId, e);
        }
        return null;
    }


    public boolean deleteBrand(UUID outletId, UUID brandId) {
        if (outletId == null || brandId == null) {
            logger.error("Invalid payload {}, {}", outletId, brandId);
            return false;
        }

        try {
            return jdbcTemplate.update(DELETE_BRAND, outletId.toString(), brandId.toString()) == 1;
        } catch (final Exception e) {
            logger.error("Error while deleting brand for {}, {}", outletId, brandId, e);
        }
        return false;
    }
}
