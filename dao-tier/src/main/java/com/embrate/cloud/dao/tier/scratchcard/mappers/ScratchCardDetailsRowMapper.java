package com.embrate.cloud.dao.tier.scratchcard.mappers;

import com.embrate.cloud.core.api.scratchcard.RewardDetails;
import com.embrate.cloud.core.api.scratchcard.ScratchCardDetails;
import com.embrate.cloud.core.api.scratchcard.ScratchCardType;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lernen.cloud.core.utils.SharedConstants;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import java.lang.reflect.Type;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.Map;
import java.util.UUID;

public class ScratchCardDetailsRowMapper implements RowMapper<ScratchCardDetails> {

    private static final Gson GSON =  SharedConstants.GSON;

    protected static final String SCRATCH_CARD_ID = "transaction_scratch_card_rewards_mapping.scratch_card_id";
    protected static final String TRANSACTION_ID = "transaction_scratch_card_rewards_mapping.transaction_id";
    protected static final String USER_ID = "transaction_scratch_card_rewards_mapping.user_id";
    protected static final String SCRATCH_CARD_TYPE = "transaction_scratch_card_rewards_mapping.scratch_card_type";
    protected static final String ADDED_AT = "transaction_scratch_card_rewards_mapping.added_at";
    protected static final String DUE_DATE = "transaction_scratch_card_rewards_mapping.due_date";
    protected static final String EXPIRY_DATE = "transaction_scratch_card_rewards_mapping.expiry_date";
    protected static final String IS_SCRATCHED = "transaction_scratch_card_rewards_mapping.is_scratched";
    protected static final String SCRATCHED_ON = "transaction_scratch_card_rewards_mapping.scratched_on";
    protected static final String REWARD_DETAILS = "transaction_scratch_card_rewards_mapping.rewards_details";
    protected static final String METADATA = "transaction_scratch_card_rewards_mapping.metadata";

    @Override
    public ScratchCardDetails mapRow(ResultSet rs, int rowNum) throws SQLException {
        if (rs.getString(SCRATCH_CARD_ID) == null) {
            return null;
        }
        final Timestamp dueDate = rs.getTimestamp(DUE_DATE);
        final Integer dueDateTime = dueDate == null ? null
                : (int) (dueDate.getTime() / 1000l);

        final Timestamp expiryDate = rs.getTimestamp(EXPIRY_DATE);
        final Integer expiryDateTime = expiryDate == null ? null
                : (int) (expiryDate.getTime() / 1000l);

        final Timestamp addedAt = rs.getTimestamp(ADDED_AT);
        final Integer addedAtTime = addedAt == null ? null
                : (int) (addedAt.getTime() / 1000l);

        final Timestamp scratchedOn = rs.getTimestamp(SCRATCHED_ON);
        final Integer scratchedOnTime = scratchedOn == null ? null
                : (int) (scratchedOn.getTime() / 1000l);

        final String metaDataStr = rs.getString(METADATA);
        Map<String, Object> metaData = null;
        if (!StringUtils.isBlank(metaDataStr)) {
            final Type collectionType = new TypeToken<Map<String, Object>>() {
            }.getType();
            metaData = GSON.fromJson(metaDataStr, collectionType);
        }

        final String rewardDetailsStr = rs.getString(REWARD_DETAILS);
        RewardDetails rewardDetails =  GSON.fromJson(rewardDetailsStr, RewardDetails.class);

        rewardDetails.setDueDate(dueDateTime);
        rewardDetails.setExpiryDate(expiryDateTime);
        return new ScratchCardDetails(UUID.fromString(rs.getString(SCRATCH_CARD_ID)), UUID.fromString(rs.getString(TRANSACTION_ID)),
                    UUID.fromString(rs.getString(USER_ID)), addedAtTime, ScratchCardType.getScratchCardType(rs.getString(SCRATCH_CARD_TYPE)),
                    rs.getBoolean(IS_SCRATCHED), scratchedOnTime, rewardDetails);
    }
}