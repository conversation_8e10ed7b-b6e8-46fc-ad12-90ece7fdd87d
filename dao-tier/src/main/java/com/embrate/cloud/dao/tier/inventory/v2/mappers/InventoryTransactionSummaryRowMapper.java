package com.embrate.cloud.dao.tier.inventory.v2.mappers;

import com.embrate.cloud.core.api.inventory.v2.TradeProductBatchSummary;
import com.embrate.cloud.core.api.inventory.v2.TradeProductSummary;
import com.embrate.cloud.core.api.inventory.v2.supplier.InventorySupplier;
import com.embrate.cloud.core.api.inventory.v2.transaction.InventoryTransactionRowDetails;
import com.embrate.cloud.core.api.inventory.v2.transaction.InventoryTransactionSummary;
import com.google.gson.reflect.TypeToken;
import com.lernen.cloud.core.api.common.TransactionMode;
import com.lernen.cloud.core.api.inventory.InventoryTransactionStatus;
import com.lernen.cloud.core.api.inventory.InventoryTransactionType;
import com.lernen.cloud.core.api.inventory.InventoryUserType;
import com.lernen.cloud.core.api.inventory.PaymentStatus;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.utils.crypto.CryptoUtils;
import com.lernen.cloud.dao.tier.student.mappers.StudentRowMapper;
import com.lernen.cloud.dao.tier.student.mappers.StudentWithoutSessionRowMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Type;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.Map.Entry;

import static com.lernen.cloud.core.utils.SharedConstants.GSON;

/**
 * <AUTHOR>
 */
public class InventoryTransactionSummaryRowMapper implements RowMapper<InventoryTransactionRowDetails> {

    protected static final String INSTITUTE_ID = "institute_id";
    protected static final String TRANSACTION_ID = "inventory_transaction_metadata.transaction_id";
    protected static final String INVOICE_ID = "invoice_id";
    protected static final String REFERENCE = "reference";
    protected static final String EMAIL = "email";
    protected static final String SKU_ID = "product_metadata.sku_id";
    protected static final String PRODUCT_NAME = "product_metadata.name";
    protected static final String BATCH_ID = "product_batch_details.batch_id";
    protected static final String BATCH_NAME = "batch_name";
    protected static final String TRANSACTION_TYPE = "transaction_type";
    protected static final String INVENTORY_USER_TYPE = "inventory_user_type";
    protected static final String QUANTITY = "quantity";
    protected static final String TOTAL_PRICE = "total_price";
    protected static final String DISCOUNT = "total_discount";
    protected static final String TAX = "total_tax";
    protected static final String TRANSACTION_TO = "transaction_to";
    protected static final String TRANSACTION_DATE = "transaction_date";
    protected static final String TRANSACTION_ADDED_TIME = "transaction_added_at";
    protected static final String TRANSACTION_BY = "transaction_by";
    protected static final String PAYMENT_STATUS = "payment_status";
    protected static final String DESCRIPTION = "description";
    protected static final String ADDITIONAL_COST = "additional_cost";
    protected static final String ADDITIONAL_DISCOUNT = "additional_discount";
    protected static final String TRANSACTION_MODE = "payment_mode";
    protected static final String TRANSACTION_STATUS = "transaction_status";

    protected static final String WALLET_DEBIT_AMOUNT = "wallet_debit_amount";
    protected static final String WALLET_BASED_CREDIT_AMOUNT = "wallet_based_credit_amount";
    protected static final String PAID_AMOUNT = "paid_amount";
    protected static final String META_DATA = "meta_data";

    protected static final String TRANSACTION_BY_NAME = "transaction_by_name";
    private static final InventorySupplierRowMapper INVENTORY_SUPPLIER_ROW_MAPPER = new InventorySupplierRowMapper();
    private static final StudentWithoutSessionRowMapper STUDENT_WITHOUT_SESSION_ROW_MAPPER = new StudentWithoutSessionRowMapper();

    /**
     * This method assumes that all elements passed in list belong to same
     * transactionID
     *
     * @return
     */
    public static InventoryTransactionSummary getTransactionSummary(List<InventoryTransactionRowDetails> transactionRowDetailsList) {
        if (CollectionUtils.isEmpty(transactionRowDetailsList)) {
            return null;
        }
        final InventoryTransactionRowDetails firstRow = transactionRowDetailsList.get(0);

        final List<TradeProductSummary> tradeProductSummaryList = new ArrayList<>();
        final Map<UUID, String> tradeProductNameMap = new HashMap<>();
        final Map<UUID, List<TradeProductBatchSummary>> tradeProductBatchSummaryMap = new HashMap<>();

        for (final InventoryTransactionRowDetails transactionRowDetails : transactionRowDetailsList) {
            UUID skuId = transactionRowDetails.getSkuId();
            tradeProductNameMap.put(skuId, transactionRowDetails.getProductName());
            if (!tradeProductBatchSummaryMap.containsKey(skuId)) {
                tradeProductBatchSummaryMap.put(skuId, new ArrayList<>());
            }
            tradeProductBatchSummaryMap.get(skuId).add(new TradeProductBatchSummary(transactionRowDetails.getBatchId(), transactionRowDetails.getBatchName(),
                    transactionRowDetails.getQuantity(), transactionRowDetails.getTotalPrice(), transactionRowDetails.getTotalDiscount(), transactionRowDetails.getTotalTax()));
        }

        for (Entry<UUID, List<TradeProductBatchSummary>> entry : tradeProductBatchSummaryMap.entrySet()) {
            tradeProductSummaryList.add(new TradeProductSummary(entry.getKey(), tradeProductNameMap.get(entry.getKey()), entry.getValue()));
        }

        return new InventoryTransactionSummary(firstRow.getTransactionId(), firstRow.getInvoiceId(), firstRow.getInventoryUserInstituteId(),
                firstRow.getTransactionType(), firstRow.getInventoryUserType(), UUID.fromString(firstRow.getTransactionBy()),
                firstRow.getTransactionByName(), firstRow.getTransactionToName(), firstRow.getEmail(),
                firstRow.getReference(), firstRow.getSupplier(),
                firstRow.getStudentLite(), firstRow.getTransactionDate(), firstRow.getTransactionAddedAt(),
                firstRow.getPaymentStatus(), firstRow.getDescription(), firstRow.getAdditionalCost(),
                firstRow.getAdditionalDiscount(), firstRow.getTransactionMode(),
                firstRow.getInventoryTransactionStatus(), firstRow.getUsedWalletAmount(), firstRow.getWalletCreditAmount(), firstRow.getPaidAmount(), firstRow.getMetadata(), tradeProductSummaryList);
    }

    public static List<InventoryTransactionSummary> getTransactionSummaryList(List<InventoryTransactionRowDetails> transactionRowDetailsList) {
        if (CollectionUtils.isEmpty(transactionRowDetailsList)) {
            return null;
        }

        Map<UUID, List<InventoryTransactionRowDetails>> transactionRowDetailsListMap = new HashMap<>();
        for (InventoryTransactionRowDetails transactionRowDetails : transactionRowDetailsList) {
            if (transactionRowDetailsListMap.containsKey(transactionRowDetails.getTransactionId())) {
                transactionRowDetailsListMap.get(transactionRowDetails.getTransactionId()).add(transactionRowDetails);
            } else {
                List<InventoryTransactionRowDetails> addTransactionRowDetailsList = new ArrayList<>();
                addTransactionRowDetailsList.add(transactionRowDetails);
                transactionRowDetailsListMap.put(transactionRowDetails.getTransactionId(), addTransactionRowDetailsList);
            }
        }

        List<InventoryTransactionSummary> transactionSummaryList = new ArrayList<>();
        for (Entry<UUID, List<InventoryTransactionRowDetails>> transactionRowDetailsListEntrySet : transactionRowDetailsListMap.entrySet()) {
            transactionSummaryList.add(getTransactionSummary(transactionRowDetailsListEntrySet.getValue()));

        }
        return transactionSummaryList;
    }

    @Override
    public InventoryTransactionRowDetails mapRow(ResultSet rs, int rowNum) throws SQLException {
        final InventoryUserType inventoryUserType = InventoryUserType
                .valueOf(rs.getString(INVENTORY_USER_TYPE));
        String transactionToName = null;
        InventorySupplier supplier = null;
        StudentLite studentLite = null;
        if (inventoryUserType == InventoryUserType.SELLER) {
            supplier = INVENTORY_SUPPLIER_ROW_MAPPER.mapRow(rs, rowNum);
        } else if (inventoryUserType == InventoryUserType.STUDENT) {
            studentLite = Student
                    .getStudentLite(StudentRowMapper.getStudent(STUDENT_WITHOUT_SESSION_ROW_MAPPER.mapRow(rs, rowNum)));
        } else {
            transactionToName = rs.getString(TRANSACTION_TO);
        }


        final Type metaDataMap = new TypeToken<Map<String, String>>() {
        }.getType();
        Map<String, String> metaData = new HashMap<>();
        if (!StringUtils.isBlank(rs.getString(META_DATA))) {
            metaData = GSON.fromJson(rs.getString(META_DATA), metaDataMap);
        }

        Integer inventoryUserInstituteId = rs.getInt(INSTITUTE_ID);
        if (rs.wasNull()) {
            inventoryUserInstituteId = null;
        }
        return new InventoryTransactionRowDetails(UUID.fromString(rs.getString(TRANSACTION_ID)),
                rs.getString(INVOICE_ID),
                inventoryUserInstituteId,
                rs.getString(REFERENCE),
                CryptoUtils.decrypt(rs.getString(EMAIL)),
                UUID.fromString(rs.getString(SKU_ID)),
                rs.getString(PRODUCT_NAME),
                UUID.fromString(rs.getString(BATCH_ID)),
                rs.getString(BATCH_NAME),
                InventoryTransactionType.valueOf(rs.getString(TRANSACTION_TYPE)),
                inventoryUserType, rs.getDouble(QUANTITY),
                rs.getDouble(TOTAL_PRICE), rs.getDouble(DISCOUNT),
                rs.getDouble(TAX), rs.getString(TRANSACTION_TO),
                rs.getTimestamp(TRANSACTION_DATE).getTime(),
                rs.getTimestamp(TRANSACTION_ADDED_TIME).getTime(),
                rs.getString(TRANSACTION_BY),
                PaymentStatus.valueOf(rs.getString(PAYMENT_STATUS)),
                rs.getString(DESCRIPTION), rs.getDouble(ADDITIONAL_COST),
                rs.getDouble(ADDITIONAL_DISCOUNT),
                TransactionMode.valueOf(rs.getString(TRANSACTION_MODE)),
                InventoryTransactionStatus.valueOf(rs.getString(TRANSACTION_STATUS)),
                rs.getDouble(WALLET_DEBIT_AMOUNT), rs.getDouble(WALLET_BASED_CREDIT_AMOUNT),
                rs.getDouble(PAID_AMOUNT), metaData,
                transactionToName,
                CryptoUtils.decrypt(rs.getString(TRANSACTION_BY_NAME)), supplier, studentLite);
    }

}
