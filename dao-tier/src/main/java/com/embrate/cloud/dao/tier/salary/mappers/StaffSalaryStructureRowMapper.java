/**
 * 
 */
package com.embrate.cloud.dao.tier.salary.mappers;

import com.embrate.cloud.core.api.salary.*;
import com.lernen.cloud.core.api.fees.payment.FeePaymentTransactionStatus;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.dao.tier.staff.mappers.StaffRowMapper;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.Map.Entry;

/**
 * <AUTHOR>
 *
 */
public class StaffSalaryStructureRowMapper implements RowMapper<StaffSalaryStructureRow> {

	public static final String INSTITUTE_ID = "staff_salary_structure_meta_data.institute_id";
	public static final String STRUCTURE_ID = "staff_salary_structure_meta_data.structure_id";
	public static final String STRUCTURE_NAME = "staff_salary_structure_meta_data.structure_name";
	public static final String SALARY_CYCLE_START = "staff_salary_structure_payhead_details.salary_cycle_start";
	public static final String STATUS = "staff_salary_structure_meta_data.status";
	public static final String PAY_HEAD_ID = "staff_salary_structure_payhead_details.pay_head_id";
	public static final String AMOUNT = "staff_salary_structure_payhead_details.amount";
	
	private static final PayHeadConfigurationRowMapper PAY_HEAD_CONFIGURATION_ROW_MAPPER = new PayHeadConfigurationRowMapper();
	private static final StaffRowMapper STAFF_ROW_MAPPER = new StaffRowMapper();
	
	@Override
	public StaffSalaryStructureRow mapRow(ResultSet rs, int rowNum) throws SQLException {
		
		final PayHeadConfiguration payHeadConfiguration = PAY_HEAD_CONFIGURATION_ROW_MAPPER.mapRow(rs, rowNum);
		final Staff staff = STAFF_ROW_MAPPER.mapRow(rs, rowNum);
		
		return new StaffSalaryStructureRow(rs.getInt(INSTITUTE_ID), UUID.fromString(rs.getString(STRUCTURE_ID)), 
				rs.getString(STRUCTURE_NAME), staff, rs.getInt(SALARY_CYCLE_START), 
				rs.getString(STATUS) == null ? null : StructureStatus.getStructureStatus(rs.getString(STATUS)), 
						payHeadConfiguration, rs.getDouble(AMOUNT));
	}

	public static StaffSalaryStructure getStaffSalaryStructurePayload(List<StaffSalaryStructureRow> staffSalaryStructureRowList) {
		if(staffSalaryStructureRowList == null || staffSalaryStructureRowList.size() <= 0) {
			return null; 
		}
		Map<Integer, List<PayHeadAmount>> cyclePayHeadAmountMap = new HashMap<Integer, List<PayHeadAmount>>();
		for (StaffSalaryStructureRow staffSalaryStructureRow : staffSalaryStructureRowList) {
			if(cyclePayHeadAmountMap.containsKey(staffSalaryStructureRow.getSalaryCycleStart())) {
				cyclePayHeadAmountMap.get(staffSalaryStructureRow.getSalaryCycleStart()).add(new PayHeadAmount(staffSalaryStructureRow.getPayHeadConfiguration(), staffSalaryStructureRow.getAmount()));
			}
			else {
				List<PayHeadAmount> payHeadAmount = new ArrayList<>();
				payHeadAmount.add(new PayHeadAmount(staffSalaryStructureRow.getPayHeadConfiguration(), staffSalaryStructureRow.getAmount()));
				cyclePayHeadAmountMap.put(staffSalaryStructureRow.getSalaryCycleStart(), payHeadAmount);
			}
		}
		return new StaffSalaryStructure(staffSalaryStructureRowList.get(0).getInstituteId(), staffSalaryStructureRowList.get(0).getStructureId(),
				staffSalaryStructureRowList.get(0).getStructureName(), staffSalaryStructureRowList.get(0).getStaff(), 
				staffSalaryStructureRowList.get(0).getStructureStatus(), cyclePayHeadAmountMap);
	}

	public static List<StaffSalaryStructure> getStaffSalaryStructurePayloadList(List<StaffSalaryStructureRow> staffSalaryStructureRowList) {
		Map<UUID, List<StaffSalaryStructureRow>> staffSalaryStructureRowMap = new HashMap<UUID, List<StaffSalaryStructureRow>>();
		for (StaffSalaryStructureRow staffSalaryStructureRow : staffSalaryStructureRowList) {
			if(staffSalaryStructureRowMap.containsKey(staffSalaryStructureRow.getStaff().getStaffId())) {
				staffSalaryStructureRowMap.get(staffSalaryStructureRow.getStaff().getStaffId()).add(staffSalaryStructureRow);
			}
			else {
				List<StaffSalaryStructureRow> staffSalaryStructureList = new ArrayList<StaffSalaryStructureRow>();
				staffSalaryStructureList.add(staffSalaryStructureRow);
				staffSalaryStructureRowMap.put(staffSalaryStructureRow.getStaff().getStaffId(), staffSalaryStructureList);
			}
		}
		List<StaffSalaryStructure> salaryStructureList = new ArrayList<StaffSalaryStructure>();
		for(Entry<UUID, List<StaffSalaryStructureRow>> salaryStructureMap : staffSalaryStructureRowMap.entrySet()) {
			salaryStructureList.add(getStaffSalaryStructurePayload(salaryStructureMap.getValue()));
		}
		return salaryStructureList;
	}
	
	public static List<StaffSalaryStructure> getStaffSalaryStructureList(List<StaffSalaryStructureRow> staffSalaryStructureRowList) {
		Map<UUID, List<StaffSalaryStructureRow>> staffSalaryStructureRowMap = new HashMap<UUID, List<StaffSalaryStructureRow>>();
		for (StaffSalaryStructureRow staffSalaryStructureRow : staffSalaryStructureRowList) {
			if(staffSalaryStructureRowMap.containsKey(staffSalaryStructureRow.getStructureId())) {
				staffSalaryStructureRowMap.get(staffSalaryStructureRow.getStructureId()).add(staffSalaryStructureRow);
			}
			else {
				List<StaffSalaryStructureRow> staffSalaryStructureList = new ArrayList<StaffSalaryStructureRow>();
				staffSalaryStructureList.add(staffSalaryStructureRow);
				staffSalaryStructureRowMap.put(staffSalaryStructureRow.getStructureId(), staffSalaryStructureList);
			}
		}
		List<StaffSalaryStructure> salaryStructureList = new ArrayList<StaffSalaryStructure>();
		for(Entry<UUID, List<StaffSalaryStructureRow>> salaryStructureMap : staffSalaryStructureRowMap.entrySet()) {
			salaryStructureList.add(getStaffSalaryStructurePayload(salaryStructureMap.getValue()));
		}
		
		return salaryStructureList;
	}

	public static SalaryPayslip getStaffSalaryPayload(StaffSalaryStructure staffSalaryStructure) {
		if(staffSalaryStructure == null) {
			return null;
		}
		List<PayHeadAmount> payHeadAmountList = new ArrayList<PayHeadAmount>();
		Integer cycle = null;
		if(staffSalaryStructure.getPayHeadAmount() != null && staffSalaryStructure.getPayHeadAmount().size() > 0) {
			Iterator<Integer> iterator = staffSalaryStructure.getPayHeadAmount().keySet().iterator();
			if(iterator.hasNext()){
				int next = iterator.next();
				cycle = next;
				payHeadAmountList = staffSalaryStructure.getPayHeadAmount().get(next);
			}			
		}
		return new SalaryPayslip(staffSalaryStructure.getInstituteId(), null, staffSalaryStructure.getStaff(),
				FeePaymentTransactionStatus.ACTIVE, cycle, payHeadAmountList, null, null, null);
	}
	
	
	public static List<SalaryPayslip> getStaffSalaryPayloadForAllStaff(List<StaffSalaryStructure> staffSalaryStructureList) {
		List<SalaryPayslip> salaryPayslipList = new ArrayList<SalaryPayslip>();
		for(StaffSalaryStructure staffSalaryStructure : staffSalaryStructureList) {
			salaryPayslipList.add(getStaffSalaryPayload(staffSalaryStructure));
		}
		return salaryPayslipList;
	}

	public static List<StaffSalaryStructureRow> getStaffSalaryPayloadForBulkDetails(
			List<StaffSalaryStructureRow> staffSalaryStructureRowList) {
		Map<UUID, List<StaffSalaryStructureRow>> staffSalaryStructureRowMap = new HashMap<UUID, List<StaffSalaryStructureRow>>();
		for (StaffSalaryStructureRow staffSalaryStructureRow : staffSalaryStructureRowList) {
			if(staffSalaryStructureRow.getStructureStatus() != StructureStatus.DISABLED) {
				if(staffSalaryStructureRowMap.containsKey(staffSalaryStructureRow.getStaff().getStaffId())) {
					staffSalaryStructureRowMap.get(staffSalaryStructureRow.getStaff().getStaffId()).add(staffSalaryStructureRow);
				}
				else {
					List<StaffSalaryStructureRow> staffSalaryStructureList = new ArrayList<StaffSalaryStructureRow>();
					staffSalaryStructureList.add(staffSalaryStructureRow);
					staffSalaryStructureRowMap.put(staffSalaryStructureRow.getStaff().getStaffId(), staffSalaryStructureList);
				}
			}
			else {
				if(staffSalaryStructureRowMap.containsKey(staffSalaryStructureRow.getStaff().getStaffId())) {
					StaffSalaryStructureRow staffSalaryStructureRowArr = new StaffSalaryStructureRow(staffSalaryStructureRow.getInstituteId(),
							null, null, staffSalaryStructureRow.getStaff(), 0,  null, null, 0d);
					staffSalaryStructureRowMap.get(staffSalaryStructureRow.getStaff().getStaffId()).add(staffSalaryStructureRowArr);
				}
				else {
					List<StaffSalaryStructureRow> staffSalaryStructureList = new ArrayList<StaffSalaryStructureRow>();
					StaffSalaryStructureRow staffSalaryStructureRowArr = new StaffSalaryStructureRow(staffSalaryStructureRow.getInstituteId(),
							null, null, staffSalaryStructureRow.getStaff(), 0,  null, null, 0d);
					staffSalaryStructureList.add(staffSalaryStructureRowArr);
					staffSalaryStructureRowMap.put(staffSalaryStructureRow.getStaff().getStaffId(), staffSalaryStructureList);
				}
			}
		}
		List<StaffSalaryStructureRow> salaryStructureList = new ArrayList<StaffSalaryStructureRow>();
		for(Entry<UUID, List<StaffSalaryStructureRow>> salaryStructureMap : staffSalaryStructureRowMap.entrySet()) {
			salaryStructureList.addAll(salaryStructureMap.getValue());
		}

		return salaryStructureList;
	}
}