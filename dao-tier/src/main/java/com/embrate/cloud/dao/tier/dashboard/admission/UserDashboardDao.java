package com.embrate.cloud.dao.tier.dashboard.admission;

import com.embrate.cloud.core.api.dashboards.admission.StaffCountByGender;
import com.embrate.cloud.core.api.dashboards.admission.StudentAdmTCCount;
import com.embrate.cloud.core.api.dashboards.admission.StudentBirthdayInfo;
import com.embrate.cloud.dao.tier.dashboard.admission.mappers.StaffCountByGenderRowMapper;
import com.lernen.cloud.core.api.student.StudentSessionSummary;
import com.embrate.cloud.core.api.dashboards.admission.StudentCountSummary;
import com.lernen.cloud.dao.tier.student.mappers.StudentAdmTCCountRowMapper;
import com.lernen.cloud.dao.tier.student.mappers.StudentSessionSummaryRowMapper;
import com.lernen.cloud.dao.tier.student.mappers.StudentCountSummaryRowMapper;
import com.lernen.cloud.core.utils.DateUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.joda.time.DateTime;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.util.CollectionUtils;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.UUID;
import org.springframework.util.CollectionUtils;

import java.sql.Date;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * DAO class for student session summary queries
 *
 * <AUTHOR>
 */
public class UserDashboardDao {

	private static final Logger logger = LogManager.getLogger(UserDashboardDao.class);

	private static final StudentSessionSummaryRowMapper STUDENT_SESSION_SUMMARY_ROW_MAPPER = new StudentSessionSummaryRowMapper();
	private static final StudentCountSummaryRowMapper STUDENT_COUNT_SUMMARY_ROW_MAPPER = new StudentCountSummaryRowMapper();
	private static final StudentAdmTCCountRowMapper STUDENT_ADM_TC_COUNT_ROW_MAPPER = new StudentAdmTCCountRowMapper();
	private static final StaffCountByGenderRowMapper STAFF_COUNT_BY_GENDER_ROW_MAPPER = new StaffCountByGenderRowMapper();

	// Base queries - placeholders will be dynamically replaced based on parameter count
	private static final String GET_STUDENT_COUNT_BY_SESSION_STATUS_BASE =
			"SELECT institute_id, academic_session_id, session_status, COUNT(student_academic_session_details.student_id) count " +
					"FROM students " +
					"JOIN student_academic_session_details ON students.student_id = student_academic_session_details.student_id " +
					"WHERE institute_id IN (%s) AND academic_session_id IN (%s) AND session_status = 'ENROLLED' " +
					"GROUP BY institute_id, academic_session_id, session_status";

	private static final String GET_NEW_ADMISSIONS_COUNT_BY_DATE_RANGE_BASE =
			"SELECT institute_id, COUNT(student_id) FROM students WHERE institute_id IN (%s) AND " +
					"admission_date BETWEEN ? AND ? GROUP BY institute_id";

	private static final String GET_RELIEVED_STUDENTS_COUNT_BY_DATE_RANGE_BASE =
			"SELECT institute_id, COUNT(student_id) FROM students WHERE institute_id IN (%s) AND " +
					"relieving_date BETWEEN ? AND ? GROUP BY institute_id";

	private static final String GET_ADMISSION_AND_RELIEVED_COUNTS_BY_DATE_RANGE =
			"SELECT institute_id, " +
					"SUM(CASE WHEN admission_date BETWEEN ? AND ? THEN 1 ELSE 0 END) AS new_admissions_count, " +
					"SUM(CASE WHEN relieve_date BETWEEN ? AND ? THEN 1 ELSE 0 END) AS relieved_students_count " +
					"FROM students WHERE institute_id IN (%s) GROUP BY institute_id";

	private static final String GET_STAFF_COUNT_BY_GENDER = "select institute_id, gender, count(staff_id) count from " +
			"staff_details where institute_id in (%s) group by 1, 2";

	private static final String GET_BIRTHDAY_STUDENTS_FOR_DATE_RANGE =
			"SELECT student_id, name, admission_number, date_of_birth, " +
			"  CASE WHEN (MONTH(date_of_birth) = MONTH(CURDATE()) AND DAY(date_of_birth) = DAY(CURDATE())) " +
			"       THEN 1 ELSE 0 END AS is_today_birthday, " +
			"  DATE(CONCAT(YEAR(CURDATE()), '-', LPAD(MONTH(date_of_birth), 2, '0'), '-', LPAD(DAY(date_of_birth), 2, '0'))) AS this_year_birthday " +
			"FROM students " +
			"WHERE institute_id IN (%s) " +
			"AND final_status = 'ENROLLED' " +
			"AND date_of_birth IS NOT NULL " +
			"HAVING this_year_birthday BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY) " +
			"ORDER BY is_today_birthday DESC, this_year_birthday";

	private final JdbcTemplate jdbcTemplate;

	public UserDashboardDao(JdbcTemplate jdbcTemplate) {
		this.jdbcTemplate = jdbcTemplate;
	}

	/**
	 * Helper method to create parameterized IN clause placeholders
	 *
	 * @param size Number of parameters
	 * @return String with comma-separated question marks (e.g., "?,?,?")
	 */
	private String createInClausePlaceholders(int size) {
		return String.join(",", Collections.nCopies(size, "?"));
	}

	/**
	 * Helper method to create parameter array from sets and additional parameters
	 *
	 * @param instituteIds       Set of institute IDs
	 * @param academicSessionIds Set of academic session IDs (can be null)
	 * @param additionalParams   Additional parameters to append
	 * @return Object array with all parameters[
	 */
	private Object[] createParameterArray(List<Integer> instituteIds, Set<Integer> academicSessionIds, Object... additionalParams) {
		List<Object> params = new ArrayList<>();

		// Add institute IDs
		params.addAll(instituteIds);

		// Add academic session IDs if provided
		if (academicSessionIds != null) {
			params.addAll(academicSessionIds);
		}

		// Add additional parameters
		for (Object param : additionalParams) {
			params.add(param);
		}

		return params.toArray();
	}

	/**
	 * Helper method to validate and convert epoch date to SQL Date
	 */
	private Date convertToSqlDate(int epochDate) {
		if (epochDate <= 0) {
			throw new IllegalArgumentException("Date cannot be null or empty");
		}
		return new Date(epochDate * 1000L);
	}

	/**
	 * Query 1: Get student count grouped by institute, academic session and session status
	 *
	 * @param instituteIds       Set of institute IDs to filter by
	 * @param academicSessionIds Set of academic session IDs to filter by
	 * @return List of StudentSessionSummary objects
	 */
	public List<StudentSessionSummary> getStudentCountBySessionStatus(List<Integer> instituteIds, Set<Integer> academicSessionIds) {
		if (CollectionUtils.isEmpty(instituteIds) || CollectionUtils.isEmpty(academicSessionIds)) {
			logger.warn("Institute IDs or Academic Session IDs are empty");
			return null;
		}

		try {
			// Create parameterized query with placeholders
			String institutePlaceholders = createInClausePlaceholders(instituteIds.size());
			String sessionPlaceholders = createInClausePlaceholders(academicSessionIds.size());

			String query = String.format(GET_STUDENT_COUNT_BY_SESSION_STATUS_BASE, institutePlaceholders, sessionPlaceholders);

			// Create parameter array
			Object[] params = createParameterArray(instituteIds, academicSessionIds);

			logger.debug("Executing parameterized query: {} with {} parameters", query, params.length);
			return jdbcTemplate.query(query, params, STUDENT_SESSION_SUMMARY_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Error while getting student count by session status for institutes {} and sessions {}",
					instituteIds, academicSessionIds, e);
		}
		return null;
	}

	public List<StudentAdmTCCount> getNewAdmissionsAndTCCountByDateRange(List<Integer> instituteIds, int startDate,
																		 int endDate) {
		if (CollectionUtils.isEmpty(instituteIds)) {
			logger.warn("Institute IDs are empty");
			return null;
		}

		if (startDate <= 0 || endDate <= 0) {
			logger.warn("Start date or end date is invalid");
			return null;
		}

		try {
			// Create parameterized query with placeholders
			String institutePlaceholders = createInClausePlaceholders(instituteIds.size());
			String query = String.format(GET_ADMISSION_AND_RELIEVED_COUNTS_BY_DATE_RANGE, institutePlaceholders);

			// Convert dates and create parameter array
			Date sqlStartDate = convertToSqlDate(startDate);
			Date sqlEndDate = convertToSqlDate(endDate);
			List<Object> params = new ArrayList<>();
			params.add(sqlStartDate);
			params.add(sqlEndDate);
			params.add(sqlStartDate);
			params.add(sqlEndDate);
			params.addAll(instituteIds);

			logger.debug("Executing parameterized query: {} with {} parameters", query, params.size());
			return jdbcTemplate.query(query, params.toArray(), STUDENT_ADM_TC_COUNT_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Error while getting new admissions & tc count by date range for institutes {} between {} and {}",
					instituteIds, startDate, endDate, e);
		}
		return null;
	}

	/**
	 * Query 2: Get new admissions count by date range
	 *
	 * @param instituteIds Set of institute IDs to filter by
	 * @param startDate    Start date for admission date filter (format: YYYY-MM-DD)
	 * @param endDate      End date for admission date filter (format: YYYY-MM-DD)
	 * @return List of StudentCountSummary objects
	 */
	public List<StudentCountSummary> getNewAdmissionsCountByDateRange(List<Integer> instituteIds,
																	  int startDate,
																	  int endDate) {
		if (CollectionUtils.isEmpty(instituteIds)) {
			logger.warn("Institute IDs are empty");
			return null;
		}

		if (startDate <= 0 || endDate <= 0) {
			logger.warn("Start date or end date is invalid");
			return null;
		}

		try {
			// Create parameterized query with placeholders
			String institutePlaceholders = createInClausePlaceholders(instituteIds.size());
			String query = String.format(GET_NEW_ADMISSIONS_COUNT_BY_DATE_RANGE_BASE, institutePlaceholders);

			// Convert dates and create parameter array
			Date sqlStartDate = convertToSqlDate(startDate);
			Date sqlEndDate = convertToSqlDate(endDate);
			Object[] params = createParameterArray(instituteIds, null, sqlStartDate, sqlEndDate);

			logger.debug("Executing parameterized query: {} with {} parameters", query, params.length);
			return jdbcTemplate.query(query, params, STUDENT_COUNT_SUMMARY_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Error while getting new admissions count by date range for institutes {} between {} and {}",
					instituteIds, startDate, endDate, e);
		}
		return null;
	}

	/**
	 * Query 3: Get relieved students count by date range
	 *
	 * @param instituteIds Set of institute IDs to filter by
	 * @param startDate    Start date for relieving date filter (format: YYYY-MM-DD)
	 * @param endDate      End date for relieving date filter (format: YYYY-MM-DD)
	 * @return List of StudentCountSummary objects
	 */
	public List<StudentCountSummary> getRelievedStudentsCountByDateRange(List<Integer> instituteIds,
																		 int startDate, int endDate) {
		if (CollectionUtils.isEmpty(instituteIds)) {
			logger.warn("Institute IDs are empty");
			return null;
		}

		if (startDate <= 0 || endDate <= 0) {
			logger.warn("Start date or end date is invalid");
			return null;
		}

		try {
			// Create parameterized query with placeholders
			String institutePlaceholders = createInClausePlaceholders(instituteIds.size());
			String query = String.format(GET_RELIEVED_STUDENTS_COUNT_BY_DATE_RANGE_BASE, institutePlaceholders);

			// Convert dates and create parameter array
			Date sqlStartDate = convertToSqlDate(startDate);
			Date sqlEndDate = convertToSqlDate(endDate);
			Object[] params = createParameterArray(instituteIds, null, sqlStartDate, sqlEndDate);

			logger.debug("Executing parameterized query: {} with {} parameters", query, params.length);
			return jdbcTemplate.query(query, params, STUDENT_COUNT_SUMMARY_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Error while getting relieved students count by date range for institutes {} between {} and {}",
					instituteIds, startDate, endDate, e);
		}
		return null;
	}

//	GET_STAFF_COUNT_BY_GENDER
	public List<StaffCountByGender> getStaffCountByGender(List<Integer> instituteIds) {
		if (CollectionUtils.isEmpty(instituteIds)) {
			logger.error("Institute IDs are empty");
			return null;
		}

		try {
			// Create parameterized query with placeholders
			String institutePlaceholders = createInClausePlaceholders(instituteIds.size());
			String query = String.format(GET_STAFF_COUNT_BY_GENDER, institutePlaceholders);

			// Create parameter array
			Object[] params = createParameterArray(instituteIds, null);

			logger.debug("Executing parameterized query: {} with {} parameters", query, params.length);
			return jdbcTemplate.query(query, params, STAFF_COUNT_BY_GENDER_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Error while getting staff count by gender for institutes {}", instituteIds, e);
		}
		return null;
	}

	/**
	 * Get enrolled students with birthdays today and in the next 7 days
	 *
	 * @param instituteIds List of institute IDs to filter by
	 * @return List of StudentBirthdayInfo objects with birthdays in the date range
	 */
	public List<StudentBirthdayInfo> getBirthdayStudentsForDateRange(List<Integer> instituteIds) {
		if (CollectionUtils.isEmpty(instituteIds)) {
			logger.warn("Institute IDs are empty");
			return new ArrayList<>();
		}

		try {
			// Create parameterized query with placeholders
			String institutePlaceholders = createInClausePlaceholders(instituteIds.size());
			String query = String.format(GET_BIRTHDAY_STUDENTS_FOR_DATE_RANGE, institutePlaceholders);

			// Create parameter array with only institute IDs
			Object[] params = createParameterArray(instituteIds, null);

			logger.debug("Executing birthday query: {} with {} parameters", query, params.length);
			return jdbcTemplate.query(query, params, new StudentBirthdayInfoRowMapper());
		} catch (final Exception e) {
			logger.error("Error while getting birthday students for institutes {}", instituteIds, e);
		}
		return new ArrayList<>();
	}

	/**
	 * Row mapper for StudentBirthdayInfo
	 */
	private static class StudentBirthdayInfoRowMapper implements RowMapper<StudentBirthdayInfo> {
		@Override
		public StudentBirthdayInfo mapRow(ResultSet rs, int rowNum) throws SQLException {
			UUID studentId = UUID.fromString(rs.getString("student_id"));
			String studentName = rs.getString("name");
			String admissionNumber = rs.getString("admission_number");
			boolean isTodayBirthday = rs.getInt("is_today_birthday") == 1;

			// Convert timestamp to epoch seconds
			java.sql.Timestamp dobTimestamp = rs.getTimestamp("date_of_birth");
			Integer birthdayDate = dobTimestamp == null ? null : (int) (dobTimestamp.getTime() / 1000L);

			return new StudentBirthdayInfo(studentId, studentName, admissionNumber, birthdayDate, isTodayBirthday);
		}
	}
}
