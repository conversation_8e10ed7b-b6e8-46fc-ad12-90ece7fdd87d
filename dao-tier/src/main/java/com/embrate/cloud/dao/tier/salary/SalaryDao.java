/**
 *
 */
package com.embrate.cloud.dao.tier.salary;

import com.embrate.cloud.core.api.leave.management.transaction.UserLeaveTransactionDetails;
import com.embrate.cloud.core.api.leave.management.transaction.UserLeaveUpdatePayload;
import com.embrate.cloud.core.api.salary.*;
import com.embrate.cloud.core.api.salary.v2.*;
import com.embrate.cloud.core.api.salary.v2.payslip.PayslipDetails;
import com.embrate.cloud.core.api.salary.v2.payslip.PayslipMetadata;
import com.embrate.cloud.core.api.salary.v2.user.StaffSalaryCycleStructureDetails;
import com.embrate.cloud.core.api.salary.v2.user.StaffSalaryStructureDetails;
import com.embrate.cloud.core.api.salary.v2.user.StaffSalaryStructureUpdatePayload;
import com.embrate.cloud.core.api.salary.v2.user.payslip.GeneratedPayslipsPayload;
import com.embrate.cloud.core.api.salary.v2.user.payslip.StaffPayslipPayload;
import com.embrate.cloud.dao.tier.leave.management.UserLeavePolicyDao;
import com.embrate.cloud.dao.tier.salary.mappers.*;
import com.lernen.cloud.core.api.common.PaginationInfo;
import com.lernen.cloud.core.api.common.SearchResultWithPagination;
import com.lernen.cloud.core.api.common.TransactionMode;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.fees.payment.FeePaymentTransactionStatus;
import com.lernen.cloud.core.api.student.StudentAcademicDetails;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.PlaceholdersUtils;
import com.lernen.cloud.core.utils.SharedConstants;
import com.lernen.cloud.core.utils.exceptions.ExceptionHandling;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.sql.Timestamp;
import java.util.*;
import java.util.Map.Entry;

/**
 * <AUTHOR>
 */
public class SalaryDao {

    private static final Logger logger = LogManager.getLogger(SalaryDao.class);

    private final JdbcTemplate jdbcTemplate;

    private final TransactionTemplate transactionTemplate;

    private final UserLeavePolicyDao userLeavePolicyDao;

    private static final PayHeadConfigurationRowMapper PAY_HEAD_CONFIGURATION_ROW_MAPPER = new PayHeadConfigurationRowMapper();
    private static final SalaryCycleRowMapper SALARY_CYCLE_ROW_MAPPER = new SalaryCycleRowMapper();
    private static final PayslipMetadataRowMapper PAYSLIP_METADATA_ROW_MAPPER = new PayslipMetadataRowMapper();
    private static final PayslipDetailsRowMapper PAYSLIP_DETAILS_ROW_MAPPER = new PayslipDetailsRowMapper();


    private static final SalaryStructureMetadataRowMapper SALARY_STRUCTURE_METADATA_ROW_MAPPER = new SalaryStructureMetadataRowMapper();
    private static final SalaryStructureTemplateDetailsRowMapper SALARY_STRUCTURE_TEMPLATE_DETAILS_ROW_MAPPER = new SalaryStructureTemplateDetailsRowMapper();


    private static final StaffSalaryStructureDetailsRowMapper STAFF_SALARY_STRUCTURE_DETAILS_ROW_MAPPER = new StaffSalaryStructureDetailsRowMapper();
    private static final StaffSalaryStructureMetadataRowMapper STAFF_SALARY_STRUCTURE_METADATA_ROW_MAPPER = new StaffSalaryStructureMetadataRowMapper();
    private static final StaffSalaryStructureRowMapper STAFF_SALARY_STRUCTURE_ROW_MAPPER = new StaffSalaryStructureRowMapper();
    private static final StaffSalaryRowMapper STAFF_SALARY_ROW_MAPPER = new StaffSalaryRowMapper();
    private static final StaffPayslipDetailsRowMapper STAFF_PAYSLIP_DETAILS_ROW_MAPPER = new StaffPayslipDetailsRowMapper();
    private static final StaffPayslipRowMapper STAFF_PAYSLIP_ROW_MAPPER = new StaffPayslipRowMapper();
    private static final FinalAdvanceDetailsRowMapper FINAL_ADVANCE_DETAILS_ROW_MAPPER = new FinalAdvanceDetailsRowMapper();
    private static final AdvanceTransactionPayloadRowMapper ADVANCE_DETAILS_ROW_MAPPER = new AdvanceTransactionPayloadRowMapper();

    private static final String PAY_HEAD = "Pay Head";

    // Pay Head Queries
    private static final String ADD_PAY_HEAD_CONFIGURATION = "insert into pay_head_configuration(institute_id, pay_head_name, pay_head_type,"
            + " `system`, pay_head_tag, epf_calc, esic_calc, pro_rata, description) "
            + " values(?, ?, ?, ?, ?, ?, ?, ?, ?) ";

    //    private static final String ADD_PAY_HEAD_CONFIGURATION = "insert into pay_head_configuration(institute_id, pay_head_name, pay_head_type, " +
//            "system, pay_head_tag, epf_calc, esic_calc, pro_rata, description) "
//            + " values(?, ?, ?, ?, ?, ?, ?, ?, ?) ";
    private static final String UPDATE_PAY_HEAD_CONFIGURATION = "update pay_head_configuration set pay_head_name = ?, pay_head_type = ?, " +
            " epf_calc = ?, esic_calc = ?, pro_rata = ?, description = ? where institute_id = ? and pay_head_id = ?";

    private static final String DELETE_PAY_HEAD_CONFIGURATION = "delete from pay_head_configuration where institute_id = ? and pay_head_id = ?";

    private static final String GET_PAY_HEAD_CONFIGURATION_DETAILS_BY_INSTITUTE_ID = "select * from pay_head_configuration "
            + " where institute_id = ?";


    private static final String CHECK_SALARY_STRUCTURE_TEMPLATES_WITH_NAME = "select count(*) from salary_structure_template_metadata "
            + " where institute_id = ? and academic_session_id = ? and structure_name = ?";

    private static final String ADD_SALARY_STRUCTURE_TEMPLATE_METADATA = "insert into salary_structure_template_metadata(institute_id, " +
            " academic_session_id, structure_id, structure_name, status, description) "
            + "values(?, ?, ?, ?, ?, ?)";


    private static final String ADD_SALARY_STRUCTURE_TEMPLATE_MAPPINGS = "insert into salary_structure_template_mapping (institute_id, structure_id, " +
            " cycle_id, pay_head_id, amount) values(?, ?, ?, ?, ?)";

    private static final String DELETE_SALARY_STRUCTURE_TEMPLATE_MAPPINGS_BY_STRUCTURE_ID = "delete from salary_structure_template_mapping "
            + " where institute_id = ? and structure_id = ?";


    private static final String UPDATE_SALARY_STRUCTURE_TEMPLATE_METADATA = "update salary_structure_template_metadata set structure_name = ?, " +
            " description = ? where institute_id = ? and academic_session_id = ? and structure_id = ?";

    private static final String DELETE_SALARY_STRUCTURE_TEMPLATE_METADATA = "delete from salary_structure_template_metadata where " +
            " institute_id = ? and structure_id = ?";


    private static final String GET_SALARY_STRUCTURE_TEMPLATES_METADATA = "select * from" +
            " salary_structure_template_metadata where institute_id = ? and academic_session_id = ?";

    private static final String GET_SALARY_STRUCTURE_TEMPLATE_DETAILS = "select salary_structure_template_metadata.*," +
            " salary_structure_template_mapping.*, salary_cycles.*, pay_head_configuration.* from salary_structure_template_metadata "
            + " join salary_structure_template_mapping on salary_structure_template_metadata.structure_id = salary_structure_template_mapping.structure_id "
            + " join salary_cycles on salary_cycles.cycle_id = salary_structure_template_mapping.cycle_id "
            + " join pay_head_configuration on pay_head_configuration.pay_head_id = salary_structure_template_mapping.pay_head_id "
            + " where salary_structure_template_metadata.institute_id = ? and salary_structure_template_metadata.structure_id = ?";


    /////////////
    // Staff Salary Structure Queries
    private static final String ADD_STAFF_SALARY_STRUCTURE_META_DATA = "insert into staff_salary_structure_metadata(institute_id, academic_session_id, structure_id, structure_name, staff_id, status) "
            + "values(?, ?, ?, ?, ?, ?)";

    private static final String ADD_STAFF_SALARY_STRUCTURE_MAPPING = "insert into staff_salary_structure_mapping(institute_id, structure_id, cycle_id, pay_head_id, amount) "
            + "values(?, ?, ?, ?, ?)";

    private static final String DELETE_STAFF_SALARY_STRUCTURE_META_DATA_BY_STRUCTURE_ID = "delete from staff_salary_structure_metadata "
            + " where institute_id = ? and structure_id = ? and staff_id = ? ";

    private static final String DELETE_STAFF_SALARY_STRUCTURE_MAPPING_BY_STRUCTURE_ID = "delete from staff_salary_structure_mapping "
            + " where structure_id = ? and institute_id = ?";

    private static final String GET_STAFF_SALARY_STRUCTURE_DETAILS = "select staff_salary_structure_metadata.*, " +
            " staff_salary_structure_mapping.*, salary_cycles.*, pay_head_configuration.* from staff_salary_structure_metadata "
            + " inner join staff_salary_structure_mapping on staff_salary_structure_mapping.structure_id = staff_salary_structure_metadata.structure_id "
            + " inner join salary_cycles on salary_cycles.cycle_id = staff_salary_structure_mapping.cycle_id "
            + " inner join pay_head_configuration on pay_head_configuration.pay_head_id = staff_salary_structure_mapping.pay_head_id "
            + " where staff_salary_structure_metadata.institute_id = ? and staff_salary_structure_metadata.academic_session_id = ? " +
            " and staff_salary_structure_metadata.staff_id = ? and status = 'ENABLED'";


    private static final String GET_ALL_STAFF_SALARY_STRUCTURES_DETAILS_BY_CYCLE = "select staff_salary_structure_metadata.*, " +
            " staff_salary_structure_mapping.*, salary_cycles.*, pay_head_configuration.* from staff_salary_structure_metadata "
            + " inner join staff_salary_structure_mapping on staff_salary_structure_mapping.structure_id = staff_salary_structure_metadata.structure_id " +
            " and staff_salary_structure_mapping.cycle_id = ? "
            + " inner join salary_cycles on salary_cycles.cycle_id = staff_salary_structure_mapping.cycle_id "
            + " inner join pay_head_configuration on pay_head_configuration.pay_head_id = staff_salary_structure_mapping.pay_head_id "
            + " where staff_salary_structure_metadata.institute_id = ? and staff_salary_structure_metadata.academic_session_id = ? " +
            " and status = 'ENABLED'";

    private static final String GET_STAFF_SALARY_STRUCTURE_METADATA = "select * from staff_salary_structure_metadata "
            + " where institute_id = ? and academic_session_id = ? and status = 'ENABLED'";


    // Salary cycles
    private static final String GET_SALARY_CYCLES = "select * from salary_cycles where institute_id = ? and academic_session_id = ?";

    private static final String GET_SALARY_CYCLE = "select * from salary_cycles where institute_id = ? and academic_session_id = ? " +
            "and cycle_id = ?";

    private static final String ADD_SALARY_CYCLE =" insert into salary_cycles (institute_id, academic_session_id, cycle_start, cycle_end, name) values (?, ? , ?, ?, ?) ";

    // payslip v2
    private static final String ADD_PAYSLIP_METADATA = "insert into salary_payslip_metadata (institute_id, academic_session_id, payslip_id, cycle_id, " +
            "staff_id, status, advance_repayment, advance_transaction_id, total_earnings, total_deductions, total_days, lop_days, description, added_by, updated_by, metadata) "
            + " values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";

    private static final String ADD_PAYSLIP_MAPPING = "insert into salary_payslip_mapping (institute_id, payslip_id, pay_head_id, pay_head_type, amount) "
            + " values (?, ?, ?, ?, ?) ";


    private static final String GET_STAFF_PAYSLIPS_METADATA = "select * from salary_payslip_metadata where institute_id = ? " +
            " and academic_session_id = ? and cycle_id = ?";

    private static final String GET_PAYSLIP_DETAILS = "select salary_payslip_metadata.*, salary_payslip_mapping.*, " +
            " pay_head_configuration.* from salary_payslip_metadata "
            + " join salary_payslip_mapping on salary_payslip_metadata.payslip_id = salary_payslip_mapping.payslip_id "
            + " join pay_head_configuration on pay_head_configuration.pay_head_id = salary_payslip_mapping.pay_head_id "
            + " where salary_payslip_metadata.institute_id = ? and salary_payslip_metadata.payslip_id = ?";


//    private static final String GET_STAFF_SALARY_STRUCTURE_BY_STAFF_ID = " and staff_salary_structure_meta_data.staff_id = ? ";
//
//    private static final String GET_STAFF_SALARY_STRUCTURE_BY_STATUS = " and staff_salary_structure_meta_data.status = ? ";
//
//    private static final String GET_STAFF_SALARY_STRUCTURE_BY_STRUCTURE_NAME = " and staff_salary_structure_meta_data.structure_name = ? ";
//
//    private static final String GET_STAFF_SALARY_STRUCTURE_BY_PAY_HEAD = " and pay_head_configuration.pay_head_id = ?";
//
//    private static final String GET_STAFF_SALARY_STRUCTURE_BY_CYCLE = " and staff_salary_structure_payhead_details.salary_cycle_start = ? ";

    // PaySip queries
//    private static final String ADD_PAYSLIP_META_DATA = "insert into salary_payslip_meta_data (institute_id, payslip_id, staff_id, salary_cycle_start, status, advance, description, advance_transaction_id) "
//            + " values (?, ?, ?, ?, ?, ?, ?, ?) ";
//
//    private static final String ADD_PAYSLIP_PAYHEAD_DETAILS = "insert into salary_payhead_details (payslip_id, pay_head_id, amount) "
//            + " values (?, ?, ?) ";

    private static final String CANCEL_PAYSLIP = "update salary_payslip_meta_data set status = ? "
            + " where institute_id = ? and payslip_id = ?";

    private static final String GET_STAFF_PAYSLIP_DETAILS = "select * from salary_payslip_meta_data "
            + " inner join salary_payhead_details on salary_payslip_meta_data.payslip_id = salary_payhead_details.payslip_id "
            + " inner join staff_details on salary_payslip_meta_data.staff_id = staff_details.staff_id "
            + " inner join c on pay_head_configuration.pay_head_id = salary_payhead_details.pay_head_id "
            + " where salary_payslip_meta_data.institute_id = ? ";

    private static final String STAFF_ID_CLAUSE = " and salary_payslip_meta_data.staff_id = ? ";

    private static final String PAYSLIP_STATUS_CLAUSE = " and salary_payslip_meta_data.status = ? ";

    private static final String GET_STAFF_PAYSLIP_DETAILS_BY_CYCLE = " and salary_payslip_meta_data.salary_cycle_start = ? ";

    private static final String GET_STAFF_PAYSLIP_DETAILS_BY_PAYSLIP_ID = " and salary_payslip_meta_data.payslip_id = ? ";

    private static final String ORDER_BY_CYCLE_DESC = " order by salary_payslip_meta_data.salary_cycle_start desc ";

    private static final String GET_LATEST_PAYSLIP_OF_ALL_STAFF = "select * from staff_details "
            + " left join salary_payslip_meta_data on (staff_details.staff_id = salary_payslip_meta_data.staff_id and salary_payslip_meta_data.salary_cycle_start = ?) "
            + " left join salary_payhead_details on salary_payhead_details.payslip_id = salary_payslip_meta_data.payslip_id  "
            + " left join pay_head_configuration on pay_head_configuration.pay_head_id = salary_payhead_details.pay_head_id "
            + " where staff_details.institute_id = ? "
            + " and (salary_payslip_meta_data.status = 'ACTIVE' or salary_payslip_meta_data.status is NULL) "
            + " and staff_details.status = 'ONBOARD' ";

    private static final String GET_STAFF_SALARY_STRUCTURE_BY_CYCLE_ID = "select * from staff_details "
            + " left join staff_salary_structure_meta_data on staff_salary_structure_meta_data.staff_id = staff_details.staff_id "
            + " left join staff_salary_structure_payhead_details on (staff_salary_structure_payhead_details.structure_id = staff_salary_structure_meta_data.structure_id and staff_salary_structure_payhead_details.salary_cycle_start = ?) "
            + " left join pay_head_configuration on pay_head_configuration.pay_head_id = staff_salary_structure_payhead_details.pay_head_id "
            + " where staff_details.institute_id = ? and staff_details.status = 'ONBOARD' ";

    // Advance queries
    private static final String INSERT_ADVANCE_TRANSACTION = "insert into staff_advance_transaction_history(institute_id, transaction_id, staff_id, "
            + "reason, transaction_category, transaction_mode, transaction_date, transaction_status, amount, transaction_by, transaction_add_at, payslip_id) "
            + "values(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    private static final String GET_ADVANCE_DETAILS = "select * from staff_advance_transaction_history "
            + " where institute_id = ? and staff_id = ? and transaction_status = ? and transaction_category %s";

    private static final String GET_ALL_STAFF_ADVANCE_DETAILS = "select * from staff_advance_transaction_history "
            + " where institute_id = ? and transaction_status = ? and transaction_category %s";

    private static final String GET_ADVANCE_DETAILS_LIST = "select * from staff_advance_transaction_history "
            + " inner join staff_details on staff_details.staff_id = staff_advance_transaction_history.staff_id "
            + " where staff_advance_transaction_history.institute_id = ? and staff_advance_transaction_history.staff_id = ? and staff_advance_transaction_history.transaction_category %s "
            + " order by staff_advance_transaction_history.transaction_add_at desc ";

    private static final String GET_FULL_ADVANCE_DETAILS_LIST = "select * from staff_advance_transaction_history "
            + " left join staff_details on staff_details.staff_id = staff_advance_transaction_history.staff_id "
            + " where staff_advance_transaction_history.institute_id = ? and staff_advance_transaction_history.transaction_category %s and staff_advance_transaction_history.transaction_status = ? ";

    private static final String GET_COUNT_FULL_ADVANCE_DETAILS_LIST = "select count(*) from staff_advance_transaction_history "
            + " left join staff_details on staff_details.staff_id = staff_advance_transaction_history.staff_id "
            + " where staff_advance_transaction_history.institute_id = ? and staff_advance_transaction_history.transaction_category %s and staff_advance_transaction_history.transaction_status = ? ";

    private static final String LIMIT_OFFSET_CLAUSE = " limit ? offset ?";

    private static final String ORDER_BY_DATE_OF_ADVANCE_DESC = " order by staff_advance_transaction_history.transaction_date desc ";

    private static final String GET_ADVANCE_DETAILS_BY_TRANSACTION_ID = "select * from staff_advance_transaction_history "
            + " inner join staff_details on staff_details.staff_id = staff_advance_transaction_history.staff_id "
            + " where staff_advance_transaction_history.institute_id = ? and staff_advance_transaction_history.transaction_id = ? ";

    private static final String GET_AMOUNT_OF_TRANSACTION = "select amount from staff_advance_transaction_history where institute_id = ? and transaction_id = ? and staff_id = ?";

    private static final String GET_DUE_AMOUNT = "select sum(amount) from staff_advance_transaction_history "
            + " where staff_advance_transaction_history.institute_id = ? and staff_advance_transaction_history.staff_id = ? and staff_advance_transaction_history.transaction_status = 'ACTIVE'"
            + " group by staff_advance_transaction_history.staff_id ";

    private static final String UPDATE_ADVANCE_TRANSACTION_STATUS = "update staff_advance_transaction_history set transaction_status = ?, updated_by = ? "
            + " where institute_id = ? and transaction_id = ? and staff_id = ? ";

    public SalaryDao(JdbcTemplate jdbcTemplate, TransactionTemplate transactionTemplate, UserLeavePolicyDao userLeavePolicyDao) {
        this.jdbcTemplate = jdbcTemplate;
        this.transactionTemplate = transactionTemplate;
        this.userLeavePolicyDao = userLeavePolicyDao;
    }

    // PayHead
    public boolean addPayHeadConfigurations(int instituteId, PayHeadConfiguration payHeadConfiguration) {
        try {
            return jdbcTemplate.update(ADD_PAY_HEAD_CONFIGURATION, instituteId,
                    payHeadConfiguration.getPayHead(), payHeadConfiguration.getPayHeadType().name(),
                    payHeadConfiguration.isSystemPayHead(), payHeadConfiguration.getTag() == null ? null : payHeadConfiguration.getTag().name(),
                    payHeadConfiguration.isEpfApplicable(), payHeadConfiguration.isEsiApplicable(),
                    payHeadConfiguration.isProRataBasis(),
                    payHeadConfiguration.getDescription()) == 1;
        } catch (final DataAccessException dataAccessException) {
            ExceptionHandling.HandleException(dataAccessException, PAY_HEAD, "pay head name");
            logger.error("DataAccessException occurs while updating Pay head details for institute {}, pay Head Id {}",
                    instituteId, payHeadConfiguration.getPayHeadId(), dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception occurs while adding Pay head details for institute {}, pay Head {}",
                    instituteId, payHeadConfiguration.getPayHead(), e);
        }
        return false;
    }

    public boolean addPayHeadConfigurations(int instituteId, List<PayHeadConfiguration> PayHeadConfigurationList) {
        try {
            final List<Object[]> batchInsertArgs = new ArrayList<>();
            int count = 0;
            for (PayHeadConfiguration payHeadConfiguration : PayHeadConfigurationList) {
                final List<Object> args = new ArrayList<>();
                args.add(instituteId);
                args.add(payHeadConfiguration.getPayHead());
                args.add(payHeadConfiguration.getPayHeadType().name());
                args.add(payHeadConfiguration.isSystemPayHead());
                args.add(payHeadConfiguration.getTag().name());
                args.add(payHeadConfiguration.isEpfApplicable());
                args.add(payHeadConfiguration.isEsiApplicable());
                args.add(payHeadConfiguration.isProRataBasis());
                args.add(payHeadConfiguration.getDescription());
                count++;
                batchInsertArgs.add(args.toArray());
            }

            final int[] rows = jdbcTemplate.batchUpdate(ADD_PAY_HEAD_CONFIGURATION, batchInsertArgs);
            if (rows.length != count) {
                return false;

            }
            for (final int rowCount : rows) {
                if (rowCount != 1) {
                    return false;
                }
            }
            return true;
        } catch (final DataAccessException dataAccessException) {
            ExceptionHandling.HandleException(dataAccessException, PAY_HEAD, "pay head name");
            logger.error("DataAccessException occurs while updating Pay head details for institute {}",
                    instituteId, dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception occurs while adding Pay head details for institute {}",
                    instituteId, e);
        }
        return false;
    }

    public int updatePayHeadConfiguration(int instituteId, PayHeadConfiguration payHeadConfiguration) {
        final boolean success = true;
        try {
            jdbcTemplate.update(UPDATE_PAY_HEAD_CONFIGURATION, payHeadConfiguration.getPayHead(),
                    payHeadConfiguration.getPayHeadType().name(),
                    payHeadConfiguration.isEpfApplicable(), payHeadConfiguration.isEsiApplicable(),
                    payHeadConfiguration.isProRataBasis(),
                    payHeadConfiguration.getDescription(),
                    instituteId, payHeadConfiguration.getPayHeadId());
        } catch (final DataAccessException dataAccessException) {
            ExceptionHandling.HandleException(dataAccessException, PAY_HEAD, "pay head name");
            logger.error("DataAccessException occurs while updating Pay head details for institute {}, pay Head Id {}",
                    instituteId, payHeadConfiguration.getPayHeadId(), dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception occurs while updating Pay head details for institute {}, pay Head Id {}",
                    instituteId, payHeadConfiguration.getPayHeadId(), e);
        }
        return success ? payHeadConfiguration.getPayHeadId() : 0;
    }

    public boolean deletePayHeadConfiguration(int instituteId, int payHeadId) {
        try {
            // Not required as there should be foreign key constraints for pay head id
//            List<StaffSalaryStructure> staffSalaryStructure = getStaffSalaryStructureByPayHead(instituteId, payHeadId);
//            if (!CollectionUtils.isEmpty(staffSalaryStructure)) {
//                logger.error(
//                        "Unable to delete this pay head as  staff salary or paySlip is already using this payHead for institute {}",
//                        instituteId);
//                throw new ApplicationException(
//                        new ErrorResponse(ApplicationErrorCode.INVALID_PAY_HEAD_CONFIGURATION,
//                                "Unable to delete the pay head, as it is allotted to some employees."));
//            }
            return jdbcTemplate.update(DELETE_PAY_HEAD_CONFIGURATION, instituteId, payHeadId) == 1;
        } catch (final DataAccessException dataAccessException) {
            ExceptionHandling.HandleException(dataAccessException, PAY_HEAD, "pay head");
            logger.error("DataAccessException occurs while deleting pay head details for institute {}, pay Head Id {}",
                    instituteId, payHeadId, dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception occurs while updating Pay head details for institute {}, pay Head Id {}",
                    instituteId, payHeadId, e);
            throw e;
        }
        return false;
    }

    public List<PayHeadConfiguration> getPayHeadConfiguration(int instituteId) {
        try {
            final Object[] args = {instituteId};
            return jdbcTemplate.query(GET_PAY_HEAD_CONFIGURATION_DETAILS_BY_INSTITUTE_ID, args,
                    PAY_HEAD_CONFIGURATION_ROW_MAPPER);
        } catch (final DataAccessException dataAccessException) {
            logger.error("DataAccessException occurs while fetching Pay head details for institute {}", instituteId,
                    dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception occurs while fetching Pay head details for institute {}", instituteId, e);
        }
        return null;
    }

    public Boolean structureTemplatesExistsWithName(int instituteId, int academicSessionId, String structureName) {
        try {
            final Object[] args = {instituteId, academicSessionId, structureName.trim()};
            return jdbcTemplate.queryForInt(CHECK_SALARY_STRUCTURE_TEMPLATES_WITH_NAME, args) > 0;
        } catch (final Exception e) {
            logger.error("Exception occurs while fetching salary structure for institute {} with name {}", instituteId, structureName, e);
        }
        return null;
    }

    public boolean addSalaryStructureTemplate(int instituteId, SalaryStructureTemplatePayload salaryStructureTemplatePayload) {
        try {
            final Boolean success = transactionTemplate.execute(new TransactionCallback<Boolean>() {
                @Override
                public Boolean doInTransaction(TransactionStatus status) {

                    UUID structureId = UUID.randomUUID();

                    return addSalaryStructureTemplateWithoutTransaction(instituteId, structureId, salaryStructureTemplatePayload);
                }
            });
            return success.booleanValue();
        } catch (final Exception e) {
            logger.error("Unable to add salary structure template for institute {}", instituteId, e);
        }
        return false;
    }

    public boolean updateSalaryStructureTemplate(int instituteId, SalaryStructureTemplatePayload salaryStructureTemplatePayload) {
        try {
            final Boolean success = transactionTemplate.execute(new TransactionCallback<Boolean>() {
                @Override
                public Boolean doInTransaction(TransactionStatus status) {

                    final Object[] deleteArgs = {instituteId, salaryStructureTemplatePayload.getStructureId().toString()};
                    boolean result = jdbcTemplate.update(DELETE_SALARY_STRUCTURE_TEMPLATE_MAPPINGS_BY_STRUCTURE_ID, deleteArgs) > 0;
                    if (!result) {
                        logger.error(
                                "Unable to delete salary structure cycle details for institute {}, structure {}",
                                instituteId, salaryStructureTemplatePayload.getStructureId());
                        throw new EmbrateRunTimeException("Unable to delete salary structure cycle details.");
                    }

                    final List<Object> argsMetaData = new ArrayList<>();
                    argsMetaData.add(salaryStructureTemplatePayload.getStructureName().trim());
                    argsMetaData.add(StringUtils.isBlank(salaryStructureTemplatePayload.getDescription()) ? null :
                            salaryStructureTemplatePayload.getDescription().trim());
                    argsMetaData.add(instituteId);
                    argsMetaData.add(salaryStructureTemplatePayload.getAcademicSessionId());
                    argsMetaData.add(salaryStructureTemplatePayload.getStructureId().toString());

                    boolean metadataUpdate = jdbcTemplate.update(UPDATE_SALARY_STRUCTURE_TEMPLATE_METADATA, argsMetaData.toArray()) == 1;
                    if (!metadataUpdate) {
                        logger.error(
                                "Unable to update salary structure meta data for institute {}, structure {}",
                                instituteId, salaryStructureTemplatePayload.getStructureId());
                        throw new EmbrateRunTimeException("Unable to delete salary structure cycle details.");
                    }

                    return addSalaryStructureCycleDetailsWithoutTransaction(instituteId, salaryStructureTemplatePayload.getStructureId(), salaryStructureTemplatePayload);
                }
            });
            return success.booleanValue();
        } catch (final Exception e) {
            logger.error("Unable to add salary structure template for institute {}", instituteId, e);
        }
        return false;
    }

    public boolean deleteSalaryStructureTemplate(int instituteId, UUID structureId) {
        try {
            final Boolean success = transactionTemplate.execute(new TransactionCallback<Boolean>() {
                @Override
                public Boolean doInTransaction(TransactionStatus status) {

                    final Object[] args = {instituteId, structureId.toString()};
                    boolean result = jdbcTemplate.update(DELETE_SALARY_STRUCTURE_TEMPLATE_MAPPINGS_BY_STRUCTURE_ID, args) > 0;
                    if (!result) {
                        logger.error(
                                "Unable to delete salary structure cycle details for institute {}, structure {}",
                                instituteId, structureId);
                        throw new EmbrateRunTimeException("Unable to delete salary structure cycle details.");
                    }

                    boolean metadataUpdate = jdbcTemplate.update(DELETE_SALARY_STRUCTURE_TEMPLATE_METADATA, args) == 1;
                    if (!metadataUpdate) {
                        logger.error(
                                "Unable to update salary structure meta data for institute {}, structure {}",
                                instituteId, structureId);
                        throw new EmbrateRunTimeException("Unable to delete salary structure cycle details.");
                    }

                    return true;
                }
            });
            return success.booleanValue();
        } catch (final Exception e) {
            logger.error("Unable to add salary structure template for institute {}", instituteId, e);
        }
        return false;
    }

    public boolean addSalaryStructureTemplateWithoutTransaction(int instituteId, UUID structureId,
                                                                SalaryStructureTemplatePayload salaryStructureTemplatePayload) {

        addSalaryStructureTemplateMetadataWithoutTransaction(instituteId, structureId, salaryStructureTemplatePayload);

        return addSalaryStructureCycleDetailsWithoutTransaction(instituteId, structureId, salaryStructureTemplatePayload);
    }

    private boolean addSalaryStructureCycleDetailsWithoutTransaction(int instituteId, UUID structureId, SalaryStructureTemplatePayload salaryStructureTemplatePayload) {
        final List<Object[]> argsList = new ArrayList<>();
        int count = 0;

        for (final SalaryCyclePayload salaryCyclePayload : salaryStructureTemplatePayload.getSalaryCyclePayloadList()) {
            for (final PayHeadAmountMapping payHeadAmountMapping : salaryCyclePayload.getPayHeadAmountList()) {
                final List<Object> args = new ArrayList<>();
                args.add(instituteId);
                args.add(structureId.toString());
                args.add(salaryCyclePayload.getSalaryCycleId());
                args.add(payHeadAmountMapping.getPayHeadId());
                args.add(payHeadAmountMapping.getAmount());
                argsList.add(args.toArray());
                count++;
            }
        }


        final int[] rows = jdbcTemplate.batchUpdate(ADD_SALARY_STRUCTURE_TEMPLATE_MAPPINGS, argsList);
        if (rows.length != count) {
            logger.error("Unable to add salary structure cycle details for institute {} and payload {}", instituteId,
                    salaryStructureTemplatePayload);
            throw new RuntimeException("Unable to add salary structure cycle details");
        }
        return true;
    }

    private void addSalaryStructureTemplateMetadataWithoutTransaction(int instituteId, UUID structureId, SalaryStructureTemplatePayload salaryStructureTemplatePayload) {
        final List<Object[]> argsListMetaData = new ArrayList<>();

        final List<Object> argsMetaData = new ArrayList<>();
        argsMetaData.add(instituteId);
        argsMetaData.add(salaryStructureTemplatePayload.getAcademicSessionId());
        argsMetaData.add(structureId.toString());
        argsMetaData.add(salaryStructureTemplatePayload.getStructureName().trim());
        argsMetaData.add(StructureStatus.ENABLED.name());
        argsMetaData.add(StringUtils.isBlank(salaryStructureTemplatePayload.getDescription()) ? null :
                salaryStructureTemplatePayload.getDescription().trim());

        argsListMetaData.add(argsMetaData.toArray());

        final int[] rowsMetaData = jdbcTemplate.batchUpdate(ADD_SALARY_STRUCTURE_TEMPLATE_METADATA,
                argsListMetaData);
        if (rowsMetaData.length != 1) {
            logger.error("Unable to add salary structure metadata for institute {} and payload {}", instituteId,
                    salaryStructureTemplatePayload);
            throw new EmbrateRunTimeException("Unable to add salary structure template metadata.");
        }
    }


    public List<SalaryStructureMetadata> getSalaryStructureTemplatesMetadata(int instituteId, int academicSessionId) {
        try {
            final Object[] args = {instituteId, academicSessionId};
            return jdbcTemplate.query(GET_SALARY_STRUCTURE_TEMPLATES_METADATA, args, SALARY_STRUCTURE_METADATA_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Exception occurs while fetching salary structure metadata for institute {}", instituteId, e);
        }
        return null;
    }


    public SalaryStructureTemplate getSalaryStructureTemplate(int instituteId, UUID structureId) {
        try {
            final Object[] args = {instituteId, structureId.toString()};
            return SalaryStructureTemplateDetailsRowMapper.getSalaryStructureDetails(
                    jdbcTemplate.query(GET_SALARY_STRUCTURE_TEMPLATE_DETAILS, args, SALARY_STRUCTURE_TEMPLATE_DETAILS_ROW_MAPPER));
        } catch (final Exception e) {
            logger.error("Exception occurs while fetching salary structure for institute {}, structureId {}", instituteId, structureId, e);
        }
        return null;
    }


    ////////////////////
    // Staff salary structure

    public List<StaffIdSalaryStructureMetadata> getStaffSalaryStructureMetadata(int instituteId, int academicSessionId) {
        try {
            final Object[] args = {instituteId, academicSessionId};
            return jdbcTemplate.query(GET_STAFF_SALARY_STRUCTURE_METADATA, args, STAFF_SALARY_STRUCTURE_METADATA_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Exception occurs while fetching staff salary structures for institute {}, academicSessionId {}", instituteId,
                    academicSessionId, e);
        }
        return null;
    }


    public StaffSalaryStructureDetails getStaffSalaryStructureDetails(int instituteId, int academicSessionId, UUID staffId) {
        try {
            final Object[] args = {instituteId, academicSessionId, staffId.toString()};
            return StaffSalaryStructureDetailsRowMapper.getSalaryStructureDetails(
                    jdbcTemplate.query(GET_STAFF_SALARY_STRUCTURE_DETAILS, args, STAFF_SALARY_STRUCTURE_DETAILS_ROW_MAPPER));
        } catch (final Exception e) {
            logger.error("Exception occurs while fetching salary structure for institute {}, academicSessionId {}, staffId {}", instituteId, academicSessionId, staffId, e);
        }
        return null;
    }

    public List<StaffSalaryCycleStructureDetails> getAllStaffSalaryStructureByCycle(int instituteId, int academicSessionId, int salaryCycleId) {
        try {
            final Object[] args = {salaryCycleId, instituteId, academicSessionId};
            return StaffSalaryStructureDetailsRowMapper.getStaffSalaryCycleStructureDetails(
                    jdbcTemplate.query(GET_ALL_STAFF_SALARY_STRUCTURES_DETAILS_BY_CYCLE, args, STAFF_SALARY_STRUCTURE_DETAILS_ROW_MAPPER));
        } catch (final Exception e) {
            logger.error("Exception occurs while fetching salary cycle structure for institute {}, academicSessionId {}, salaryCycleId {}",
                    instituteId, academicSessionId, salaryCycleId, e);
        }
        return null;
    }


    public List<SalaryCycle> getSalaryCycles(int instituteId, int academicSessionId) {
        try {
            final Object[] args = {instituteId, academicSessionId};
            return jdbcTemplate.query(GET_SALARY_CYCLES, args, SALARY_CYCLE_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Exception occurs while fetching salary cycles for institute {}, academicSessionId {}", instituteId,
                    academicSessionId, e);
        }
        return null;
    }

    public boolean addBulkSalaryCycles(int instituteId, int academicSessionId, List<SalaryCycleDetailsPayload> salaryCycleDetailsPayloadList) {
        try {
            final List<Object[]> batchInsertArgs = new ArrayList<>();
            int count = 0;
            for (SalaryCycleDetailsPayload salaryCycleDetai : salaryCycleDetailsPayloadList) {
                final List<Object> args = new ArrayList<>();
                args.add(instituteId);
                args.add(academicSessionId);
                args.add(salaryCycleDetai.getStartDate());
                args.add(salaryCycleDetai.getEndDate());
                args.add(salaryCycleDetai.getCycleName());

                count++;
                batchInsertArgs.add(args.toArray());
            }

            final int[] rows = jdbcTemplate.batchUpdate(ADD_SALARY_CYCLE, batchInsertArgs);
            if (rows.length != count) {
                return false;

            }
            for (final int rowCount : rows) {
                if (rowCount != 1) {
                    return false;
                }
            }
            return true;
        } catch (final Exception e) {
            logger.error("Exception occurs while adding salary cycles for institute {}, academicSessionId {}", instituteId,
                    academicSessionId, e);
        }
        return false;
    }

    public SalaryCycle getSalaryCycle(int instituteId, int academicSessionId, int cycleId) {
        try {
            final Object[] args = {instituteId, academicSessionId, cycleId};
            return jdbcTemplate.queryForObject(GET_SALARY_CYCLE, args, SALARY_CYCLE_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Exception occurs while fetching salary cycles for institute {}, academicSessionId {}", instituteId,
                    academicSessionId, e);
        }
        return null;
    }

//    public StaffSalaryStructure getStaffSalaryStructureByStatusAndStaffId(int instituteId, UUID staffId,
//                                                                          UserStatus structureStatus) {
//        try {
//            final Object[] args = {instituteId, staffId.toString(), structureStatus.name()};
//            return StaffSalaryStructureRowMapper.getStaffSalaryStructurePayload(
//                    jdbcTemplate.query(GET_STAFF_SALARY_STRUCTURE + GET_STAFF_SALARY_STRUCTURE_BY_STAFF_ID
//                            + GET_STAFF_SALARY_STRUCTURE_BY_STATUS, args, STAFF_SALARY_STRUCTURE_ROW_MAPPER));
//        } catch (final DataAccessException dataAccessException) {
//            logger.error("DataAccessException occurs while fetching staff salary structure for institute {}",
//                    instituteId, dataAccessException);
//        } catch (final Exception e) {
//            logger.error("Exception occurs while fetching staff salary structure for institute {}", instituteId, e);
//        }
//        return null;
//    }

//    public StaffSalaryStructure getStaffSalaryStructureByName(Integer instituteId, UUID staffId, String structureName) {
//        try {
//            final Object[] args = {instituteId, staffId.toString(), structureName};
//            return StaffSalaryStructureRowMapper
//                    .getStaffSalaryStructurePayload(jdbcTemplate.query(
//                            GET_STAFF_SALARY_STRUCTURE + GET_STAFF_SALARY_STRUCTURE_BY_STAFF_ID
//                                    + GET_STAFF_SALARY_STRUCTURE_BY_STRUCTURE_NAME,
//                            args, STAFF_SALARY_STRUCTURE_ROW_MAPPER));
//        } catch (final DataAccessException dataAccessException) {
//            logger.error("DataAccessException occurs while fetching staff salary structure for institute {}",
//                    instituteId, dataAccessException);
//        } catch (final Exception e) {
//            logger.error("Exception occurs while fetching staff salary structure for institute {}", instituteId, e);
//        }
//        return null;
//    }

//    private List<StaffSalaryStructure> getStaffSalaryStructureByPayHead(int instituteId, int payHeadId) {
//        try {
//            final Object[] args = {instituteId, payHeadId};
//            return StaffSalaryStructureRowMapper.getStaffSalaryStructurePayloadList(
//                    jdbcTemplate.query(GET_STAFF_SALARY_STRUCTURE + GET_STAFF_SALARY_STRUCTURE_BY_PAY_HEAD, args,
//                            STAFF_SALARY_STRUCTURE_ROW_MAPPER));
//        } catch (final DataAccessException dataAccessException) {
//            logger.error("DataAccessException occurs while fetching staff salary structure for institute {}",
//                    instituteId, dataAccessException);
//        } catch (final Exception e) {
//            logger.error("Exception occurs while fetching staff salary structure for institute {}", instituteId, e);
//        }
//        return null;
//    }

//    public boolean addStaffSalaryStructure(int instituteId, StaffSalaryStructurePayload staffSalaryStructurePayload) {
//        try {
//            final Boolean success = transactionTemplate.execute(new TransactionCallback<Boolean>() {
//                @Override
//                public Boolean doInTransaction(TransactionStatus status) {
//
//                    UUID structureId = staffSalaryStructurePayload.getStructureId();
//
//                    if (structureId == null) {
//                        staffSalaryStructurePayload.setStructureId(UUID.randomUUID());
//                    }
//                    return addStaffSalaryStructureWithoutTransaction(instituteId, Arrays.asList(staffSalaryStructurePayload));
//                }
//            });
//            return success.booleanValue();
//        } catch (final Exception e) {
//            logger.error("Unable to add staff salary structure for institute {} and staff {}", instituteId,
//                    staffSalaryStructurePayload.getStaffId(), e);
//            throw e;
//        }
//    }

    public boolean addStaffSalaryStructure(int instituteId, List<StaffSalaryStructurePayload> staffSalaryStructurePayloads) {
        try {
            final Boolean success = transactionTemplate.execute(new TransactionCallback<Boolean>() {
                @Override
                public Boolean doInTransaction(TransactionStatus status) {
                    return addStaffSalaryStructureWithoutTransaction(instituteId, staffSalaryStructurePayloads);
                }
            });
            return success.booleanValue();
        } catch (final Exception e) {
            logger.error("Unable to add staff salary structure for institute {} and staffSalaryStructurePayloads {}", instituteId,
                    staffSalaryStructurePayloads.size(), e);
        }
        return false;
    }

    public boolean addStaffSalaryStructureWithoutTransaction(int instituteId,
                                                             List<StaffSalaryStructurePayload> staffSalaryStructurePayloads) {

        final List<Object[]> argsListMetaData = new ArrayList<>();
        final List<Object[]> argsList = new ArrayList<>();
        int count = 0;
        for (StaffSalaryStructurePayload staffSalaryStructurePayload : staffSalaryStructurePayloads) {
            final List<Object> argsMetaData = new ArrayList<>();
            argsMetaData.add(instituteId);
            argsMetaData.add(staffSalaryStructurePayload.getAcademicSessionId());
            argsMetaData.add(staffSalaryStructurePayload.getStructureId().toString());
            argsMetaData.add(staffSalaryStructurePayload.getStructureName().trim());
            argsMetaData.add(staffSalaryStructurePayload.getStaffId().toString());
            argsMetaData.add(staffSalaryStructurePayload.getStatus().name());
            argsListMetaData.add(argsMetaData.toArray());

            for (final Entry<Integer, List<PayHeadAmountMapping>> cyclePayHeadAmountMapping : staffSalaryStructurePayload
                    .getCyclePayHeadAmountMap().entrySet()) {
                for (final PayHeadAmountMapping payHeadAmountMapping : cyclePayHeadAmountMapping.getValue()) {
                    final List<Object> args = new ArrayList<>();
                    args.add(instituteId);
                    args.add(staffSalaryStructurePayload.getStructureId().toString());
                    args.add(cyclePayHeadAmountMapping.getKey());
                    args.add(payHeadAmountMapping.getPayHeadId());
                    args.add(payHeadAmountMapping.getAmount());
                    argsList.add(args.toArray());
                    count++;
                }
            }
        }

        final int finalCount = count;

        final int[] rowsMetaData = jdbcTemplate.batchUpdate(ADD_STAFF_SALARY_STRUCTURE_META_DATA,
                argsListMetaData);
        if (rowsMetaData.length != staffSalaryStructurePayloads.size()) {
            logger.error("Unable to add salary structure metadata for institute {}", instituteId);
            throw new EmbrateRunTimeException("Unable to add salary structure metadata.");
        }

        final int[] rows = jdbcTemplate.batchUpdate(ADD_STAFF_SALARY_STRUCTURE_MAPPING, argsList);
        if (rows.length != finalCount) {
            logger.error("Unable to add salary pay head details for institute {}", instituteId);
            throw new EmbrateRunTimeException("Unable to add salary pay head details.");
        }
        return true;

    }
//    public List<StaffSalaryStructure> getStaffSalaryStructure(int instituteId, UUID staffId) {
//        try {
//            final Object[] args = {instituteId, staffId.toString()};
//            return StaffSalaryStructureRowMapper.getStaffSalaryStructureList(
//                    jdbcTemplate.query(GET_STAFF_SALARY_STRUCTURE + GET_STAFF_SALARY_STRUCTURE_BY_STAFF_ID, args,
//                            STAFF_SALARY_STRUCTURE_ROW_MAPPER));
//        } catch (final DataAccessException dataAccessException) {
//            dataAccessException.printStackTrace();
//            logger.error("DataAccessException occurs while fetching staff salary structure for institute {}",
//                    instituteId, dataAccessException);
//        } catch (final Exception e) {
//            e.printStackTrace();
//            logger.error("Exception occurs while fetching staff salary structure for institute {}", instituteId, e);
//        }
//        return null;
//    }

//    public SalaryPayslip getStaffSalaryByCycle(int instituteId, UUID staffId, int cycle) {
//        try {
//            final Object[] args = {instituteId, staffId.toString(), UserStatus.ENABLED.name(), cycle};
//            return StaffSalaryStructureRowMapper.getStaffSalaryPayload(
//                    StaffSalaryStructureRowMapper.getStaffSalaryStructurePayload(jdbcTemplate.query(
//                            GET_STAFF_SALARY_STRUCTURE + GET_STAFF_SALARY_STRUCTURE_BY_STAFF_ID
//                                    + GET_STAFF_SALARY_STRUCTURE_BY_STATUS + GET_STAFF_SALARY_STRUCTURE_BY_CYCLE,
//                            args, STAFF_SALARY_STRUCTURE_ROW_MAPPER)));
//        } catch (final DataAccessException dataAccessException) {
//            logger.error("DataAccessException occurs while fetching staff salary structure for institute {}",
//                    instituteId, dataAccessException);
//        } catch (final Exception e) {
//            logger.error("Exception occurs while fetching staff salary structure for institute {}", instituteId, e);
//        }
//        return null;
//    }

    public Boolean updateStaffSalaryStructureV2(int instituteId, UUID staffId, StaffSalaryStructureUpdatePayload staffSalaryStructureUpdatePayload) {
        try {
            final Boolean success = transactionTemplate.execute(new TransactionCallback<Boolean>() {
                @Override
                public Boolean doInTransaction(TransactionStatus status) {
                    StaffSalaryStructureDetails staffSalaryStructureDetails = getStaffSalaryStructureDetails(instituteId, staffSalaryStructureUpdatePayload.getAcademicSessionId(), staffId);
                    if (staffSalaryStructureDetails == null) {
                        logger.error("No structure exists for staff {} in session {}, {}", staffId, staffSalaryStructureUpdatePayload.getAcademicSessionId(), instituteId);
                        throw new EmbrateRunTimeException("No structure exists for staff");
                    }

                    final Object[] deleteArgs = {staffSalaryStructureDetails.getMetadata().getStructureId().toString(), instituteId};
                    boolean result = jdbcTemplate.update(DELETE_STAFF_SALARY_STRUCTURE_MAPPING_BY_STRUCTURE_ID,
                            deleteArgs) >= 1;
                    if (!result) {
                        logger.error("Unable to delete the pay head mappings for staff {} in session {}, {}", staffId, staffSalaryStructureUpdatePayload.getAcademicSessionId(), instituteId);
                        throw new EmbrateRunTimeException("Unable to delete the pay head mappings");
                    }

                    final Object[] argsMetaData = {instituteId, staffSalaryStructureDetails.getMetadata().getStructureId().toString(), staffId.toString()};
                    result = jdbcTemplate.update(DELETE_STAFF_SALARY_STRUCTURE_META_DATA_BY_STRUCTURE_ID,
                            argsMetaData) == 1;

                    if (!result) {
                        logger.error("Unable to delete the structure metadata for staff {} in session {}, {}", staffId, staffSalaryStructureUpdatePayload.getAcademicSessionId(), instituteId);
                        throw new EmbrateRunTimeException("Unable to delete structure metadata for staff");
                    }

                    Map<Integer, List<PayHeadAmountMapping>> cyclePayHeadAmountMap = new HashMap<>();
                    for (SalaryCyclePayload salaryCyclePayload : staffSalaryStructureUpdatePayload.getSalaryCyclePayloadList()) {
                        List<PayHeadAmountMapping> payHeadAmountMappingList = new ArrayList<>();
                        for (PayHeadAmountMapping payHeadAmount : salaryCyclePayload.getPayHeadAmountList()) {
                            payHeadAmountMappingList.add(new PayHeadAmountMapping(payHeadAmount.getPayHeadId(), payHeadAmount.getAmount()));
                        }
                        cyclePayHeadAmountMap.put(salaryCyclePayload.getSalaryCycleId(), payHeadAmountMappingList);
                    }

                    StaffSalaryStructurePayload staffSalaryStructurePayload = new StaffSalaryStructurePayload(staffSalaryStructureUpdatePayload.getAcademicSessionId(),
                            UUID.randomUUID(), staffSalaryStructureDetails.getMetadata().getStructureName(), staffId, StructureStatus.ENABLED, cyclePayHeadAmountMap);
                    // add new structure for this staff
                    result &= addStaffSalaryStructureWithoutTransaction(instituteId,
                            Arrays.asList(staffSalaryStructurePayload));
                    if (!result) {
                        logger.error(
                                "While updating unable to add staff salary structure for institute {}, session {} and staff {}",
                                instituteId, staffSalaryStructureUpdatePayload.getAcademicSessionId(), staffSalaryStructurePayload.getStaffId());
                        throw new EmbrateRunTimeException("Unable to update staff salary structure.");
                    }
                    return true;
                }
            });
            return success.booleanValue();
        } catch (final Exception e) {
            logger.error("Unable to update staff salary structure for institute {} and staff {}, payload {}",
                    instituteId, staffId, staffSalaryStructureUpdatePayload, e);
            return false;
        }
    }


    public Boolean deleteStaffSalaryStructureV2(int instituteId, int academicSessionId, UUID staffId) {
        try {
            final Boolean success = transactionTemplate.execute(new TransactionCallback<Boolean>() {
                @Override
                public Boolean doInTransaction(TransactionStatus status) {
                    StaffSalaryStructureDetails staffSalaryStructureDetails = getStaffSalaryStructureDetails(instituteId, academicSessionId, staffId);
                    if (staffSalaryStructureDetails == null) {
                        logger.error("No structure exists for staff {} in session {}, {}", staffId, academicSessionId, instituteId);
                        throw new EmbrateRunTimeException("No structure exists for staff");
                    }

                    final Object[] deleteArgs = {staffSalaryStructureDetails.getMetadata().getStructureId().toString(), instituteId};
                    boolean result = jdbcTemplate.update(DELETE_STAFF_SALARY_STRUCTURE_MAPPING_BY_STRUCTURE_ID,
                            deleteArgs) >= 1;
                    if (!result) {
                        logger.error("Unable to delete the pay head mappings for staff {} in session {}, {}", staffId, academicSessionId, instituteId);
                        throw new EmbrateRunTimeException("Unable to delete the pay head mappings");
                    }

                    final Object[] argsMetaData = {instituteId, staffSalaryStructureDetails.getMetadata().getStructureId().toString(), staffId.toString()};
                    result = jdbcTemplate.update(DELETE_STAFF_SALARY_STRUCTURE_META_DATA_BY_STRUCTURE_ID,
                            argsMetaData) == 1;

                    if (!result) {
                        logger.error("Unable to delete the structure metadata for staff {} in session {}, {}", staffId, academicSessionId, instituteId);
                        throw new EmbrateRunTimeException("Unable to delete structure metadata for staff");
                    }

                    return true;
                }
            });
            return success.booleanValue();
        } catch (final Exception e) {
            logger.error("Unable to update staff salary structure for institute {} and staff {}, session {}",
                    instituteId, staffId, academicSessionId, e);
            return false;
        }
    }

//    public Boolean updateStaffSalaryStructure(int instituteId, StaffSalaryStructurePayload staffSalaryStructurePayload) {
//        try {
//            final Boolean success = transactionTemplate.execute(new TransactionCallback<Boolean>() {
//                @Override
//                public Boolean doInTransaction(TransactionStatus status) {
//                    Boolean result = true;
//                    // delete existing active structure for this staff
//                    result &= deleteStaffSalaryStructureByStructureId(instituteId,
//                            staffSalaryStructurePayload.getStaffId(), staffSalaryStructurePayload.getStructureId());
//
//                    if (!result) {
//                        logger.error(
//                                "While updating unable to delete staff salary structure for institute {} and staff {}",
//                                instituteId, staffSalaryStructurePayload.getStaffId());
//                        throw new RuntimeException("Unable to update staff salary structure.");
//                    }
//                    // add new structure for this staff
//                    result &= addStaffSalaryStructureWithoutTransaction(instituteId,
//                            Arrays.asList(staffSalaryStructurePayload));
//                    if (!result) {
//                        logger.error(
//                                "While updating unable to add staff salary structure for institute {} and staff {}",
//                                instituteId, staffSalaryStructurePayload.getStaffId());
//                        throw new RuntimeException("Unable to update staff salary structure.");
//                    }
//                    return result;
//                }
//            });
//            return success.booleanValue();
//        } catch (final Exception e) {
//            logger.error("Unable to update staff salary structure for institute {} and staff {}",
//                    instituteId, staffSalaryStructurePayload.getStaffId(), e);
//            throw e;
//        }
//    }

//    public Boolean updateStaffSalaryStructureStatus(int instituteId, UUID staffId, UUID structureId,
//                                                    UserStatus structureStatus) {
//        try {
//            final Object[] args = {structureStatus.name(), instituteId, structureId.toString(), staffId.toString()};
//            return jdbcTemplate.update(UPDATE_STAFF_SALARY_STRUCTURE_STATUS, args) >= 1;
//        } catch (final Exception e) {
//            logger.error("Exception while fetching student for instituteId {}", instituteId, e);
//        }
//        return false;
//    }

//    public Boolean deleteStaffSalaryStructureByStructureId(Integer instituteId, UUID staffId, UUID structureId) {
//
//        try {
//            final Boolean success = transactionTemplate.execute(new TransactionCallback<Boolean>() {
//                @Override
//                public Boolean doInTransaction(TransactionStatus status) {
//
//                    final Object[] args = {structureId.toString()};
//                    Boolean result = jdbcTemplate.update(DELETE_STAFF_SALARY_STRUCTURE_PAYHEAD_DETAILS_BY_STRUCTURE_ID,
//                            args) >= 1;
//                    if (!result) {
//                        logger.error(
//                                "Unable to delete salary structure details for institute {}, structure {} and staff {}",
//                                instituteId, structureId, staffId);
//                        throw new RuntimeException("Unable to add payslip payhead details.");
//                    }
//
//                    final Object[] argsMetaData = {instituteId, structureId.toString(), staffId.toString()};
//                    result = jdbcTemplate.update(DELETE_STAFF_SALARY_STRUCTURE_META_DATA_BY_STRUCTURE_ID,
//                            argsMetaData) >= 1;
//                    if (!result) {
//                        logger.error(
//                                "Unable to delete salary structure details for institute {}, structure {} and staff {}",
//                                instituteId, structureId, staffId);
//                        throw new RuntimeException("Unable to add payslip payhead details.");
//                    }
//                    return true;
//                }
//            });
//            return success.booleanValue();
//        } catch (final Exception e) {
//            logger.error("Exception while fetching student for instituteId {}", instituteId, e);
//        }
//        return false;
//    }

    // salaryPayslip
//    public boolean addSalaryPayslip(int instituteId, SalaryPayslipPayload salaryPayslipPayload, UUID userId) {
//
//        final List<Object[]> argsListMetaData = new ArrayList<>();
//        final List<Object[]> argsList = new ArrayList<>();
//        int count = 0;
//        final List<Object> argsMetaData = new ArrayList<>();
//        UUID payslipId = UUID.randomUUID();
//
//        // for meta data table
//        argsMetaData.add(salaryPayslipPayload.getInstituteId());
//        argsMetaData.add(payslipId.toString());
//        argsMetaData.add(salaryPayslipPayload.getStaffId().toString());
//        argsMetaData.add(salaryPayslipPayload.getSalaryCycleStart());
//        argsMetaData.add(salaryPayslipPayload.getStatus().name());
//        argsMetaData
//                .add(salaryPayslipPayload.getAdvanceAmount() == null ? null : salaryPayslipPayload.getAdvanceAmount());
//        argsMetaData.add(salaryPayslipPayload.getDescription());
//
//        // for payslip payhead data
//        for (final PayHeadAmountMapping payHeadAmountMapping : salaryPayslipPayload.getPayHeadAmountMap()) {
//            final List<Object> args = new ArrayList<>();
//            args.add(payslipId.toString());
//            args.add(payHeadAmountMapping.getPayHeadId());
//            args.add(payHeadAmountMapping.getAmount());
//            argsList.add(args.toArray());
//            count++;
//        }
//
//        final int finalCount = count;
//        try {
//            final Boolean success = transactionTemplate.execute(new TransactionCallback<Boolean>() {
//                @Override
//                public Boolean doInTransaction(TransactionStatus status) {
//
//                    SalaryPayslip salaryPayslip = getSalaryPayslipByCycle(salaryPayslipPayload.getInstituteId(),
//                            salaryPayslipPayload.getStaffId(), salaryPayslipPayload.getSalaryCycleStart());
//                    if (salaryPayslip != null) {
//                        logger.error(
//                                "There is already a payslip generated for this cycle {} for staff {} of  institute {}",
//                                salaryPayslip.getSalaryCycleStart(), salaryPayslipPayload.getStaffId(), instituteId);
//                        throw new ApplicationException(
//                                new ErrorResponse(ApplicationErrorCode.INVALID_PAYSLIP_DETAILS,
//                                        "The Salary has already been configured for this cycle. Please cancel the previous one to generate again."));
//                    }
//                    UUID transactionId = null;
//                    // deducted the advance amount from userWallet and add transaction in wallet
//                    // Transction
//                    if (salaryPayslipPayload.getAdvanceAmount() != null
//                            && salaryPayslipPayload.getAdvanceAmount() > 0) {
//                        transactionId = addAdvanceTransactionsWithoutTransactions(
//                                new AdvanceTransactionPayload(instituteId, null, salaryPayslipPayload.getStaffId(),
//                                        "Payslip created", AdvanceTransactionCategory.ADVANCE_RETURN,
//                                        TransactionMode.FROM_SALARY, null, FeePaymentTransactionStatus.ACTIVE,
//                                        salaryPayslipPayload.getAdvanceAmount() * 1, userId, null, payslipId));
//                        if (transactionId == null) {
//                            logger.error("Unable to add advance transaction details for institute {} and staff {}",
//                                    instituteId, salaryPayslipPayload.getStaffId());
//                            throw new RuntimeException("Unable to add payslip meta data.");
//                        }
//                    }
//                    argsMetaData.add(transactionId == null ? null : transactionId.toString());
//                    argsListMetaData.add(argsMetaData.toArray());
//                    final int[] rowsMetaData = jdbcTemplate.batchUpdate(ADD_PAYSLIP_META_DATA, argsListMetaData);
//                    if (rowsMetaData.length != 1) {
//                        logger.error("Unable to add payslip payhead details for institute {} and staff {}", instituteId,
//                                salaryPayslipPayload.getStaffId());
//                        throw new RuntimeException("Unable to add payslip meta data.");
//                    }
//
//                    final int[] rows = jdbcTemplate.batchUpdate(ADD_PAYSLIP_PAYHEAD_DETAILS, argsList);
//                    if (rows.length != finalCount) {
//                        logger.error("Unable to add payslip payhead details for institute {} and staff {}", instituteId,
//                                salaryPayslipPayload.getStaffId());
//                        throw new RuntimeException("Unable to add payslip payhead details.");
//                    }
//                    return true;
//                }
//            });
//            return success.booleanValue();
//        } catch (final Exception e) {
//            logger.error("Unable to add payslip payhead details for institute {} and staff {}", instituteId,
//                    salaryPayslipPayload.getStaffId(), e);
//            throw e;
//        }
//    }

    public boolean addBulkSalaryPayslipV2(int instituteId, GeneratedPayslipsPayload generatedPayslipsPayload,
                                          UUID userId) {

        try {
            int academicSessionId = generatedPayslipsPayload.getAcademicSessionId();
            int salaryCycleId = generatedPayslipsPayload.getSalaryCycleId();

            List<AdvanceTransactionPayload> advanceTransactionPayloadList = new ArrayList<AdvanceTransactionPayload>();
            List<UserLeaveUpdatePayload> userLeaveUpdatePayloadList = new ArrayList<>();
            final List<Object[]> argsListMetaData = new ArrayList<>();
            final List<Object[]> argsList = new ArrayList<>();
            int count = 0;
            for (StaffPayslipPayload staffPayslipPayload : generatedPayslipsPayload.getStaffPayslipPayloadList()) {
                UUID staffId = staffPayslipPayload.getStaffId();
                UUID payslipId = UUID.randomUUID();
                UUID advanceTransactionId = null;
                if (Double.compare(staffPayslipPayload.getAdvanceRecoveryAmount(), 0d) > 0) {
                    advanceTransactionId = UUID.randomUUID();
                    advanceTransactionPayloadList.add(
                            new AdvanceTransactionPayload(instituteId, advanceTransactionId, staffPayslipPayload.getStaffId(),
                                    "Advance Repayment By Salary Deduction", AdvanceTransactionCategory.ADVANCE_RETURN,
                                    TransactionMode.FROM_SALARY, null, FeePaymentTransactionStatus.ACTIVE,
                                    staffPayslipPayload.getAdvanceRecoveryAmount(), userId, null, payslipId));
                }

                Set<UUID> leaveTransactionIds = new HashSet<>();
                if(staffPayslipPayload.getUserLeaveUpdatePayload() != null) {
                    for(UserLeaveTransactionDetails userLeaveTransactionDetails : staffPayslipPayload.getUserLeaveUpdatePayload().getUserLeaveTransactionDetailsList()){
                        UUID leaveTransactionId = UUID.randomUUID();
                        userLeaveTransactionDetails.getMetadata().setTransactionId(leaveTransactionId);
                        leaveTransactionIds.add(leaveTransactionId);
                    }
                    userLeaveUpdatePayloadList.add(staffPayslipPayload.getUserLeaveUpdatePayload());
                }

                // Metadata table
                Map<String, String> metadata = new HashMap<>();
                if (staffPayslipPayload.getStaffPayslipAttendanceMetadata() != null) {
                    metadata.put(StaffPayslipPayload.SALARY_ATTENDANCE_METADATA_KEY, SharedConstants.GSON.toJson(staffPayslipPayload.getStaffPayslipAttendanceMetadata()));
                }
                metadata.put(StaffPayslipPayload.LEAVE_TRANSACTIONS_KEY, SharedConstants.GSON.toJson(leaveTransactionIds));


                double totalEarnings = 0d;
                double totalDeductions = 0d;
                for (final PayHeadTypeAmount payHeadAmountMapping : staffPayslipPayload.getPayHeadAmountList()) {
                    if (payHeadAmountMapping.getType() == PayHeadType.ADDITION) {
                        totalEarnings += payHeadAmountMapping.getAmount();
                    } else if (payHeadAmountMapping.getType() == PayHeadType.DEDUCTION) {
                        totalDeductions += payHeadAmountMapping.getAmount();
                    }
                    argsList.add(new Object[]{instituteId, payslipId.toString(), payHeadAmountMapping.getPayHeadId(), payHeadAmountMapping.getType().name(), payHeadAmountMapping.getAmount()});
                    count++;
                }

                argsListMetaData.add(new Object[]{instituteId, academicSessionId, payslipId.toString(), salaryCycleId, staffId.toString(),
                        staffPayslipPayload.getStatus().name(), staffPayslipPayload.getAdvanceRecoveryAmount(), advanceTransactionId == null ?
                        null : advanceTransactionId.toString(), totalEarnings, totalDeductions, staffPayslipPayload.getStaffPayslipAttendanceMetadata() == null ? null :
                        staffPayslipPayload.getStaffPayslipAttendanceMetadata().getTotalDays(), staffPayslipPayload.getStaffPayslipAttendanceMetadata() == null ?
                        null : staffPayslipPayload.getStaffPayslipAttendanceMetadata().getTotalLOPDays(), staffPayslipPayload.getDescription() == null ? null :
                        staffPayslipPayload.getDescription().trim(), userId.toString(), userId.toString(), SharedConstants.GSON.toJson(metadata)
                });

            }

            final int finalCount = count;

            final Boolean success = transactionTemplate.execute(new TransactionCallback<Boolean>() {
                @Override
                public Boolean doInTransaction(TransactionStatus status) {
                    if (CollectionUtils.isNotEmpty(advanceTransactionPayloadList) && !addBulkAdvanceTransactions(advanceTransactionPayloadList)) {
                        logger.error("Unable to add advance transactions for institute {}, session {}, cycle {}", instituteId, academicSessionId, salaryCycleId);
                        throw new EmbrateRunTimeException("Unable to add advance transactions");
                    }

                    if(CollectionUtils.isNotEmpty(userLeaveUpdatePayloadList)){
                        userLeavePolicyDao.updateUserLeaveTransactionsWithoutTransaction(instituteId, academicSessionId, userLeaveUpdatePayloadList, true);
                    }

                    final int[] rowsMetaData = jdbcTemplate.batchUpdate(ADD_PAYSLIP_METADATA, argsListMetaData);
                    if (rowsMetaData.length != argsListMetaData.size()) {
                        logger.error("Unable to add payslip meta data details for institute {}, session {}, cycle {}", instituteId, academicSessionId, salaryCycleId);
                        throw new EmbrateRunTimeException("Unable to add payslip metadata.");
                    }

                    final int[] rows = jdbcTemplate.batchUpdate(ADD_PAYSLIP_MAPPING, argsList);
                    if (rows.length != finalCount) {
                        logger.error("Unable to add payslip mappings details for institute {}, session {}, cycle {}", instituteId, academicSessionId, salaryCycleId);
                        throw new EmbrateRunTimeException("Unable to add payslip metadata details.");
                    }

                    return true;
                }
            });
            return success.booleanValue();
        } catch (final Exception e) {
            logger.error("Unable to add payslips for institute {}, session {}, cycle {}", instituteId, generatedPayslipsPayload.getAcademicSessionId(), generatedPayslipsPayload.getSalaryCycleId(), e);
            return false;
        }
    }

    public List<PayslipMetadata> getSalaryPayslipsByCycleV2(int instituteId, int academicSessionId, int salaryCycleId, Set<UUID> staffIds) {
        try {
            StringBuilder query = new StringBuilder(GET_STAFF_PAYSLIPS_METADATA);
            List<Object> args = new ArrayList<>();
            args.add(instituteId);
            args.add(academicSessionId);
            args.add(salaryCycleId);
            
            // Conditionally add the staff_id clause only if staffIds is not empty
            if (CollectionUtils.isNotEmpty(staffIds)) {
                query.append(" AND staff_id IN (");
                String placeholders = PlaceholdersUtils.buildPlaceholders(staffIds.size());
                query.append(placeholders).append(")");
                
                for (UUID staffId : staffIds) {
                    args.add(staffId.toString()); 
                }
            }

            return jdbcTemplate.query(query.toString(), args.toArray(), PAYSLIP_METADATA_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Exception occurs while fetching staff salary payslips for institute {}, academicSessionId {}, salaryCycleId {}",
                    instituteId, academicSessionId, salaryCycleId, e);
        }
        return null;
    }

    public PayslipDetails getPayslipsDetails(int instituteId, UUID payslipId) {
        try {
            final Object[] args = {instituteId, payslipId.toString()};
            return PayslipDetailsRowMapper.getPayslipDetails(jdbcTemplate.query(GET_PAYSLIP_DETAILS, args, PAYSLIP_DETAILS_ROW_MAPPER));
        } catch (final Exception e) {
            logger.error("Exception occurs while fetching staff salary payslip details for institute {}, payslipId {}",
                    instituteId, payslipId, e);
        }
        return null;
    }


    private SalaryPayslip getSalaryPayslipByCycle(Integer instituteId, UUID staffId, Integer salaryCycleStart) {
        try {
            FeePaymentTransactionStatus transactionStatus = FeePaymentTransactionStatus.ACTIVE;
            final Object[] args = {instituteId, staffId.toString(), transactionStatus.name(), salaryCycleStart};
            return StaffPayslipDetailsRowMapper.getStaffPayslipDetailsPayloadByCycle(
                    jdbcTemplate.query(GET_STAFF_PAYSLIP_DETAILS + STAFF_ID_CLAUSE + PAYSLIP_STATUS_CLAUSE
                            + GET_STAFF_PAYSLIP_DETAILS_BY_CYCLE, args, STAFF_PAYSLIP_DETAILS_ROW_MAPPER));
        } catch (final DataAccessException dataAccessException) {
            logger.error("DataAccessException occurs while fetching staff salary structure for institute {}",
                    instituteId, dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception occurs while fetching staff salary structure for institute {}", instituteId, e);
        }
        return null;
    }

    private SalaryPayslip getSalaryPayslipByPayslipId(Integer instituteId, UUID payslipId) {
        try {
            FeePaymentTransactionStatus transactionStatus = FeePaymentTransactionStatus.ACTIVE;
            final Object[] args = {instituteId, transactionStatus.name(), payslipId.toString()};
            return StaffPayslipDetailsRowMapper.getStaffPayslipDetailsPayloadByCycle(jdbcTemplate.query(
                    GET_STAFF_PAYSLIP_DETAILS + PAYSLIP_STATUS_CLAUSE + GET_STAFF_PAYSLIP_DETAILS_BY_PAYSLIP_ID, args,
                    STAFF_PAYSLIP_DETAILS_ROW_MAPPER));
        } catch (final DataAccessException dataAccessException) {
            logger.error("DataAccessException occurs while fetching staff salary structure for institute {}",
                    instituteId, dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception occurs while fetching staff salary structure for institute {}", instituteId, e);
        }
        return null;
    }

    public Boolean cancelPaySlip(int instituteId, UUID payslipId, UUID userId) {
        try {
            final Boolean success = transactionTemplate.execute(new TransactionCallback<Boolean>() {
                @Override
                public Boolean doInTransaction(TransactionStatus status) {

                    SalaryPayslip salaryPayslip = getSalaryPayslipByPayslipId(instituteId, payslipId);

                    if (salaryPayslip == null) {
                        logger.error("No payslip generated for this for payslipId {} of  institute {}", payslipId,
                                instituteId);
                        throw new RuntimeException("No payslip generated for this for payslipId.");
                    }
                    boolean result = true;

                    // add transaction in advance if advance amount is greater than 0
                    if (salaryPayslip.getAdvanceAmount() != null && salaryPayslip.getAdvanceAmount() > 0) {
                        result = updateAdvanceTransactionStatus(instituteId, salaryPayslip.getAdvanceTransactionId(),
                                salaryPayslip.getStaff().getStaffId(), userId, FeePaymentTransactionStatus.CANCELLED);
                    }

                    FeePaymentTransactionStatus transactionStatus = FeePaymentTransactionStatus.CANCELLED;
                    if (result) {
                        return jdbcTemplate.update(CANCEL_PAYSLIP, transactionStatus.name(), instituteId,
                                payslipId.toString()) == 1;
                    }
                    return result;
                }
            });
            return success.booleanValue();
        } catch (final Exception e) {
            logger.error("Exception occurs while updating Pay head details for institute {}, pay Head Id {}",
                    instituteId, payslipId, e);
            throw e;
        }
    }

    public List<SalaryPayslip> getPaySlipDetails(int instituteId, UUID staffId, FeePaymentTransactionStatus status) {
        try {
            final Object[] args = {instituteId, staffId.toString(), status.name()};
            return StaffPayslipDetailsRowMapper.getStaffPayslipDetailsPayload(jdbcTemplate.query(
                    GET_STAFF_PAYSLIP_DETAILS + STAFF_ID_CLAUSE + PAYSLIP_STATUS_CLAUSE + ORDER_BY_CYCLE_DESC, args,
                    STAFF_PAYSLIP_DETAILS_ROW_MAPPER));
        } catch (final DataAccessException dataAccessException) {
            logger.error("DataAccessException occurs while fetching staff salary structure for institute {}",
                    instituteId, dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception occurs while fetching staff salary structure for institute {}", instituteId, e);
        }
        return null;
    }

    public List<SalaryPayslip> getAllPaySlipDetails(int instituteId, UUID staffId) {
        try {
            final Object[] args = {instituteId, staffId.toString()};
            return StaffPayslipDetailsRowMapper.getStaffPayslipDetailsPayload(
                    jdbcTemplate.query(GET_STAFF_PAYSLIP_DETAILS + STAFF_ID_CLAUSE + ORDER_BY_CYCLE_DESC, args,
                            STAFF_PAYSLIP_DETAILS_ROW_MAPPER));
        } catch (final DataAccessException dataAccessException) {
            logger.error("DataAccessException occurs while fetching staff salary structure for institute {}",
                    instituteId, dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception occurs while fetching staff salary structure for institute {}", instituteId, e);
        }
        return null;
    }

    public List<SalaryPayslip> getPaySlipDetailsByCycle(int instituteId, Integer cycle) {
        try {
            final Object[] args = {cycle, instituteId};
            return StaffPayslipRowMapper.getLatestPaySlipDetailsOfAllStaff(
                    jdbcTemplate.query(GET_LATEST_PAYSLIP_OF_ALL_STAFF, args, STAFF_PAYSLIP_ROW_MAPPER));
        } catch (final DataAccessException dataAccessException) {
            logger.error("DataAccessException occurs while fetching staff salary structure for institute {}",
                    instituteId, dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception occurs while fetching staff salary structure for institute {}", instituteId, e);
        }
        return null;
    }

    public List<SalaryPayslip> getSalaryStructureOfAllStaffByCycle(int instituteId, Integer cycle) {
        try {
            final Object[] args = {cycle, instituteId};
            return StaffSalaryStructureRowMapper
                    .getStaffSalaryPayloadForAllStaff(StaffSalaryStructureRowMapper.getStaffSalaryStructurePayloadList(
                            StaffSalaryStructureRowMapper.getStaffSalaryPayloadForBulkDetails(jdbcTemplate
                                    .query(GET_STAFF_SALARY_STRUCTURE_BY_CYCLE_ID, args, STAFF_SALARY_ROW_MAPPER))));
        } catch (final DataAccessException dataAccessException) {
            logger.error("DataAccessException occurs while fetching staff salary structure for institute {}",
                    instituteId, dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception occurs while fetching staff salary structure for institute {}", instituteId, e);
        }
        return null;
    }

    public Boolean addAdvanceTransaction(AdvanceTransactionPayload advanceTransactionPayload) {
        try {
            final Boolean success = transactionTemplate.execute(new TransactionCallback<Boolean>() {

                @Override
                public Boolean doInTransaction(TransactionStatus status) {
                    UUID transactionId = addAdvanceTransactionsWithoutTransactions(advanceTransactionPayload);
                    if (transactionId != null) {
                        return true;
                    }
                    logger.error("Unable to add advance transaction details");
                    throw new RuntimeException("Unable to add advance transaction details.");
                }

            });
            return success;
        } catch (final Exception e) {
            logger.error("Unable to add advance transaction for institute {}, user {} ",
                    advanceTransactionPayload.getInstituteId(), advanceTransactionPayload.getStaffId(), e);
            throw e;
        }
    }

    private UUID addAdvanceTransactionsWithoutTransactions(AdvanceTransactionPayload advanceTransactionPayload) {
        UUID transactionId = UUID.randomUUID();

        // in case of payslip transactions
        Timestamp transactionDate = new Timestamp(DateUtils.now() * 1000l);
        if (advanceTransactionPayload.getTransactionDate() != null) {
            transactionDate = new Timestamp(advanceTransactionPayload.getTransactionDate() * 1000l);
        }
        try {
            boolean result = jdbcTemplate.update(INSERT_ADVANCE_TRANSACTION, advanceTransactionPayload.getInstituteId(),
                    transactionId.toString(), advanceTransactionPayload.getStaffId().toString(),
                    advanceTransactionPayload.getReason(),
                    advanceTransactionPayload.getAdvanceTransactionCategory().name(),
                    advanceTransactionPayload.getTransactionMode() == null ? null
                            : advanceTransactionPayload.getTransactionMode().name(),
                    transactionDate,
                    advanceTransactionPayload.getTransactionStatus() == null ? null
                            : advanceTransactionPayload.getTransactionStatus().name(),
                    advanceTransactionPayload.getAmount(), advanceTransactionPayload.getTransactionBy().toString(),
                    new Timestamp(DateUtils.now() * 1000l), advanceTransactionPayload.getPayslipId() == null ? null
                            : advanceTransactionPayload.getPayslipId().toString()) == 1;
            if (result) {
                return transactionId;
            }
            return null;
        } catch (final Exception e) {
            logger.error("Unable add advance transaction for staff {}", advanceTransactionPayload.getStaffId(), e);
        }
        return null;
    }

    public boolean addBulkAdvanceTransactions(List<AdvanceTransactionPayload> advanceTransactionPayloadList) {
        try {
            final List<Object[]> argsList = new ArrayList<>();
            int count = 0;
            for (AdvanceTransactionPayload advanceTransactionPayload : advanceTransactionPayloadList) {
                Timestamp transactionDate = new Timestamp(DateUtils.now() * 1000l);
                if (advanceTransactionPayload.getTransactionDate() != null) {
                    transactionDate = new Timestamp(advanceTransactionPayload.getTransactionDate() * 1000l);
                }
                final List<Object> args = new ArrayList<>();
                args.add(advanceTransactionPayload.getInstituteId());
                args.add(advanceTransactionPayload.getTransactionId().toString());
                args.add(advanceTransactionPayload.getStaffId().toString());
                args.add(advanceTransactionPayload.getReason());
                args.add(advanceTransactionPayload.getAdvanceTransactionCategory().name());
                args.add(advanceTransactionPayload.getTransactionMode().name());
                args.add(transactionDate);
                args.add(advanceTransactionPayload.getTransactionStatus().name());
                args.add(advanceTransactionPayload.getAmount());
                args.add(advanceTransactionPayload.getTransactionBy().toString());
                args.add(new Timestamp(DateUtils.now() * 1000l));
                args.add(advanceTransactionPayload.getPayslipId() == null ? null
                        : advanceTransactionPayload.getPayslipId().toString());
                argsList.add(args.toArray());
                count++;
            }
            final int[] rows = jdbcTemplate.batchUpdate(INSERT_ADVANCE_TRANSACTION, argsList);
            if (rows.length != count) {
                logger.error("Unable to add payslip payhead details");
                throw new RuntimeException("Unable to add payslip payhead details.");
            }
            return true;
        } catch (final Exception e) {
            logger.error("Unable to update student wallet amount", e);
        }
        return false;
    }

    public FinalAdvanceDetails getFinalAdvanceDetails(int instituteId, UUID staffId) {
        try {
            final List<Object> args = new ArrayList<>();
            args.add(instituteId);
            args.add(staffId.toString());
            args.add(FeePaymentTransactionStatus.ACTIVE.name());

            final StringBuilder inQuery = new StringBuilder();
            inQuery.append("in ('" + AdvanceTransactionCategory.ADVANCE.name() + "', '"
                    + AdvanceTransactionCategory.ADVANCE_RETURN + "')");

            return FinalAdvanceDetailsRowMapper
                    .getAdvanceDetails(jdbcTemplate.query(String.format(GET_ADVANCE_DETAILS, inQuery),
                            args.toArray(), FINAL_ADVANCE_DETAILS_ROW_MAPPER));

        } catch (final Exception e) {
            logger.error("Exception occurs while fetching final advance details for institute {}, staffId {}", instituteId, staffId, e);
        }
        return null;
    }

    public List<FinalAdvanceDetails> getAllStaffFinalAdvanceDetails(int instituteId) {
        try {
            final List<Object> args = new ArrayList<>();
            args.add(instituteId);
            args.add(FeePaymentTransactionStatus.ACTIVE.name());

            final StringBuilder inQuery = new StringBuilder();
            inQuery.append("in ('" + AdvanceTransactionCategory.ADVANCE.name() + "', '"
                    + AdvanceTransactionCategory.ADVANCE_RETURN + "')");

            return FinalAdvanceDetailsRowMapper
                    .getAllStaffAdvanceDetails(jdbcTemplate.query(String.format(GET_ALL_STAFF_ADVANCE_DETAILS, inQuery),
                            args.toArray(), FINAL_ADVANCE_DETAILS_ROW_MAPPER));

        } catch (final Exception e) {
            logger.error("Exception occurs while fetching final advance details for institute {}", instituteId, e);
        }
        return null;
    }

    public List<AdvanceTransaction> getAdvanceDetailsList(int instituteId, UUID staffId) {
        try {
            final Object[] args = {instituteId, staffId.toString()};

            final StringBuilder inQuery = new StringBuilder();
            inQuery.append("in ('" + AdvanceTransactionCategory.ADVANCE.name() + "', '"
                    + AdvanceTransactionCategory.ADVANCE_RETURN + "')");

            return AdvanceTransactionPayloadRowMapper.getPositiveAdvanceValues(jdbcTemplate.query(
                    String.format(GET_ADVANCE_DETAILS_LIST, inQuery.toString()), args, ADVANCE_DETAILS_ROW_MAPPER));

        } catch (final DataAccessException dataAccessException) {
            logger.error("DataAccessException occurs while fetching Pay head details for institute {}", instituteId,
                    dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception occurs while fetching Pay head details for institute {}", instituteId, e);
        }
        return null;
    }

    public SearchResultWithPagination<AdvanceTransaction> getAdvanceTransactionsDetailsList(int instituteId,
                                                                                            FeePaymentTransactionStatus feePaymentTransactionStatus, int offset, int limit) {
        try {
            final List<Object> args = new ArrayList<>();
            args.add(instituteId);
            args.add(feePaymentTransactionStatus.name());

            final StringBuilder inQuery = new StringBuilder();
            inQuery.append("in ('" + AdvanceTransactionCategory.ADVANCE.name() + "', '"
                    + AdvanceTransactionCategory.ADVANCE_RETURN + "')");

            final int totalResultCount = jdbcTemplate.queryForObject(
                    String.format(GET_COUNT_FULL_ADVANCE_DETAILS_LIST, inQuery.toString()), Integer.class, args.toArray());

            final PaginationInfo paginationInfo = new PaginationInfo(totalResultCount, limit, offset);

            final List<Object> argsMeta = new ArrayList<>();
            argsMeta.add(instituteId);
            argsMeta.add(feePaymentTransactionStatus.name());
            argsMeta.add(limit);
            argsMeta.add(offset);

            final StringBuilder inQueryMeta = new StringBuilder();
            inQueryMeta.append("in ('" + AdvanceTransactionCategory.ADVANCE.name() + "', '"
                    + AdvanceTransactionCategory.ADVANCE_RETURN + "')");

            List<AdvanceTransaction> advanceTransactions = AdvanceTransactionPayloadRowMapper
                    .getPositiveAdvanceValues(jdbcTemplate.query(
                            String.format(GET_FULL_ADVANCE_DETAILS_LIST + ORDER_BY_DATE_OF_ADVANCE_DESC + LIMIT_OFFSET_CLAUSE,
                                    inQueryMeta.toString()), argsMeta.toArray(), ADVANCE_DETAILS_ROW_MAPPER));

            final SearchResultWithPagination<AdvanceTransaction> resultWithPagination = new SearchResultWithPagination<>(
                    paginationInfo, advanceTransactions);

            return resultWithPagination;

        } catch (final DataAccessException dataAccessException) {
            logger.error("DataAccessException occurs while fetching Pay head details for institute {}", instituteId,
                    dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception occurs while fetching Pay head details for institute {}", instituteId, e);
        }
        return null;
    }

    public boolean cancelAdvance(int instituteId, AdvanceTransaction advanceTransactionPayload, UUID userId) {
        try {
            final Boolean success = transactionTemplate.execute(new TransactionCallback<Boolean>() {
                @Override
                public Boolean doInTransaction(TransactionStatus status) {

                    // To cancel
                    // payslip generated after advance is given or any payment done to clear advance
                    // if yes - check if cancelled advance amount is less than or equal to current
                    // due amount
                    // if no - then cancel advance

                    Double advanceAmount = advanceTransactionPayload.getAmount();

                    Double dueAmount = getTotalDueAmount(instituteId,
                            advanceTransactionPayload.getStaff().getStaffId());

                    if (advanceTransactionPayload.getAdvanceTransactionCategory() == AdvanceTransactionCategory.ADVANCE) {
                        if (Math.abs(advanceAmount) > Math.abs(dueAmount)) {
                            logger.error(
                                    "You cannot cancel this transaction as this amount or a part of this amount is already been paid for institute {} staffId {}.",
                                    instituteId, advanceTransactionPayload.getStaff().getStaffId());
                            throw new ApplicationException(new ErrorResponse(
                                    ApplicationErrorCode.INVALID_ADVANCE_DETAILS,
                                    "You cannot cancel this transaction as this amount or a part of this amount is already been paid."));
                        }
                    }

                    Boolean result = updateAdvanceTransactionStatus(instituteId,
                            advanceTransactionPayload.getTransactionId(),
                            advanceTransactionPayload.getStaff().getStaffId(), userId,
                            FeePaymentTransactionStatus.CANCELLED);
                    if (!result) {
                        logger.error("Unable to update user wallet amount for staff {} of  institute {}",
                                advanceTransactionPayload.getStaff().getStaffId(), instituteId);
                        throw new ApplicationException(
                                new ErrorResponse(ApplicationErrorCode.INVALID_PAYSLIP_DETAILS, "Invalid cycle."));
                    }

                    return true;
                }

            });
            return success.booleanValue();
        } catch (final Exception e) {
            logger.error("Unable to cancel advance of user {} of institute {}",
                    advanceTransactionPayload.getStaff().getStaffId(), instituteId, e);
            throw e;
        }
    }

    public AdvanceTransaction getAdvanceDetailsByTransactionId(int instituteId, UUID transactionId) {
        try {
            final Object[] args = {instituteId, transactionId.toString()};
            return jdbcTemplate.queryForObject(GET_ADVANCE_DETAILS_BY_TRANSACTION_ID, args, ADVANCE_DETAILS_ROW_MAPPER);

        } catch (final DataAccessException dataAccessException) {
            logger.error("DataAccessException occurs while fetching Pay head details for institute {}", instituteId,
                    dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception occurs while fetching Pay head details for institute {}", instituteId, e);
        }
        return null;
    }

    public Double getTotalDueAmount(int instituteId, UUID staffId) {
        try {
            Double amount = jdbcTemplate.queryForObject(GET_DUE_AMOUNT,
                    new Object[]{instituteId, staffId.toString()}, Double.class);
            return amount == null ? 0d : amount < 0 ? amount * -1 : amount;
        } catch (final Exception e) {
            logger.error("Unable get wallet amount for user {}", staffId, e);
        }
        return null;
    }

    public Boolean updateAdvanceTransactionStatus(int instituteId, UUID transactionId, UUID staffId, UUID userId,
                                                  FeePaymentTransactionStatus status) {
        try {
            final Object[] args = {status.name(), userId.toString(), instituteId, transactionId.toString(), staffId.toString()};
            return jdbcTemplate.update(UPDATE_ADVANCE_TRANSACTION_STATUS, args) >= 1;
        } catch (final Exception e) {
            logger.error("Exception while fetching student for instituteId {}", instituteId, e);
        }
        return false;
    }
}
