package com.embrate.cloud.dao.tier.service.payment.gateway.mappers;

import com.embrate.cloud.core.api.service.payment.gateway.PaymentGatewayServiceProvider;
import com.embrate.cloud.core.api.service.payment.gateway.merchants.PGMerchantDetails;
import com.embrate.cloud.core.api.service.payment.gateway.merchants.PGMerchantStatus;
import com.embrate.cloud.core.api.wallet.WalletTransactionCategory;
import com.embrate.cloud.core.api.wallet.WalletTransactionPayload;
import com.google.gson.reflect.TypeToken;
import com.lernen.cloud.core.api.common.TransactionMode;
import com.lernen.cloud.core.api.fees.payment.FeePaymentTransactionStatus;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.utils.crypto.CryptoUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import java.lang.reflect.Type;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static com.lernen.cloud.core.utils.SharedConstants.GSON;

/**
 *
 * <AUTHOR>
 *
 */
public class PaymentGatewayMerchantDetailsRowMapper implements RowMapper<PGMerchantDetails> {

	static final String INSTITUTE_ID = "institute_id";
	static final String PAYMENT_GATEWAY = "payment_gateway";
	static final String MERCHANT_ID = "merchant_id";
	static final String MERCHANT_KEY = "merchant_key";
	static final String MERCHANT_SECRET = "merchant_secret";
	static final String MERCHANT_NAME = "merchant_name";
	static final String STATUS = "status";
	static final String ADDED_AT = "added_at";
	static final String METADATA = "metadata";
	static final String DESCRIPTION = "description";


	@Override
	public PGMerchantDetails mapRow(ResultSet rs, int rowNum) throws SQLException {

		final Type metaDataMap = new TypeToken<Map<String, Object>>() {
		}.getType();
		String metadataStr = rs.getString(METADATA);
		Map<String, Object> metaData = new HashMap<>();
		if (!StringUtils.isBlank(metadataStr)) {
			metaData = GSON.fromJson(CryptoUtils.decrypt(metadataStr), metaDataMap);
		}
		String merchantKey = CryptoUtils.decrypt(rs.getString(MERCHANT_KEY));
		String merchantSecret =CryptoUtils.decrypt(rs.getString(MERCHANT_SECRET));
		String merchantName =CryptoUtils.decrypt(rs.getString(MERCHANT_NAME));

		return new PGMerchantDetails(rs.getInt(INSTITUTE_ID), PaymentGatewayServiceProvider.getPaymentGatewayServiceProvider(rs.getString(PAYMENT_GATEWAY)), UUID.fromString(rs.getString(MERCHANT_ID)),
				merchantName, merchantKey, merchantSecret,
				PGMerchantStatus.getStatus(rs.getString(STATUS)), rs.getString(DESCRIPTION),
				metaData, (int) (rs.getTimestamp(ADDED_AT).getTime()/1000l));
	}
}
