package com.embrate.cloud.dao.tier.noticeboard.mappers;

import com.embrate.cloud.core.api.noticeboard.UserNoticeViewDetails;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.UUID;

public class UserNoticeViewDetailsRowMapper  implements RowMapper<UserNoticeViewDetails> {

    protected static final String NOTICE_ID = "user_notice_view_details.notice_id";
    protected static final String USER_ID = "user_notice_view_details.user_id";
    protected static final String VIEWED_ON = "user_notice_view_details.viewed_on";

    @Override
    public UserNoticeViewDetails mapRow(ResultSet rs, int rowNum) throws SQLException {
        String noticeIdStr = rs.getString(NOTICE_ID);
        if(StringUtils.isBlank(noticeIdStr)) {
            return null;
        }

        final Timestamp viewedOn = rs.getTimestamp(VIEWED_ON);
        final Integer viewedOnTime = viewedOn == null ? null
                : (int) (viewedOn.getTime() / 1000l);

        return new UserNoticeViewDetails(UUID.fromString(noticeIdStr),
                UUID.fromString(rs.getString(USER_ID)), viewedOnTime);
    }

}