package com.embrate.cloud.dao.tier.frontdesk.mappers;

import com.embrate.cloud.core.api.frontdesk.GatePassMetadata;
import com.embrate.cloud.core.api.frontdesk.GatePassStatus;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.UUID;

public class GatePassMetadataRowMapper implements <PERSON><PERSON>apper<GatePassMetadata> {

    protected static final String INSTITUTE_ID = "gate_pass_metadata.institute_id";
    protected static final String ACADEMIC_SESSION_ID = "gate_pass_metadata.academic_session_id";
    protected static final String GATE_PASS_ID = "gate_pass_metadata.gate_pass_id";
    protected static final String GATE_PASS_NUMBER = "gate_pass_metadata.gate_pass_number";
    protected static final String GATE_PASS_DATE = "gate_pass_metadata.gate_pass_date";
    protected static final String NAME = "gate_pass_metadata.name";
    protected static final String CONTACT_NUMBER = "gate_pass_metadata.contact_number";
    protected static final String EMAIL_ID = "gate_pass_metadata.email_id";
    protected static final String ADDRESS = "gate_pass_metadata.address";
    protected static final String REASON = "gate_pass_metadata.reason";
    protected static final String RELATION = "gate_pass_metadata.relation";
    protected static final String STATUS = "gate_pass_metadata.status";

    @Override
    public GatePassMetadata mapRow(ResultSet rs, int rowNum) throws SQLException {
        if(rs.getString(GATE_PASS_ID) == null) {
            return null;
        }
        final Timestamp gatePassDateTimestamp = rs.getTimestamp(GATE_PASS_DATE);
        final Integer gatePassDateTimestampTime = gatePassDateTimestamp == null ? null
                : (int) (gatePassDateTimestamp.getTime() / 1000l);

        return new GatePassMetadata(rs.getInt(INSTITUTE_ID), rs.getInt(ACADEMIC_SESSION_ID),
                UUID.fromString(rs.getString(GATE_PASS_ID)), rs.getString(GATE_PASS_NUMBER), gatePassDateTimestampTime,
                rs.getString(NAME), rs.getString(CONTACT_NUMBER), rs.getString(EMAIL_ID),
                rs.getString(ADDRESS), rs.getString(REASON), rs.getString(RELATION),
                GatePassStatus.getGatePassStatus(rs.getString(STATUS)));
    }
}
