package com.embrate.cloud.dao.tier.calendar.holiday.mappers;

import com.embrate.cloud.core.api.calendar.holiday.template.HolidayTemplate;
import com.embrate.cloud.core.api.calendar.holiday.template.assignment.HolidayTemplateDuration;
import com.embrate.cloud.core.api.calendar.holiday.template.assignment.UserHolidayTemplateData;
import com.lernen.cloud.core.api.user.UserType;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

/**
 * <AUTHOR>
 */
public class HolidayTemplateAssignmentRowMapper implements <PERSON>Mapper<UserHolidayTemplateData> {

    private static final String INSTITUTE_ID = "user_holiday_template_assignment.institute_id";
    private static final String ACADEMIC_SESSION_ID = "user_holiday_template_assignment.academic_session_id";
    private static final String TEMPLATE_ID = "user_holiday_template_assignment.template_id";
    private static final String USER_ID = "user_holiday_template_assignment.user_id";
    private static final String USER_TYPE = "user_holiday_template_assignment.user_type";
    private static final String START_TIME = "user_holiday_template_assignment.start_time";
    private static final String END_TIME = "user_holiday_template_assignment.end_time";

    private static final HolidayTemplateRowMapper HOLIDAY_TEMPLATE_ROW_MAPPER = new HolidayTemplateRowMapper();


    @Override
    public UserHolidayTemplateData mapRow(ResultSet rs, int rowNum) throws SQLException {
        HolidayTemplate holidayTemplate = HOLIDAY_TEMPLATE_ROW_MAPPER.mapRow(rs, rowNum);
        UUID userId = UUID.fromString(rs.getString(USER_ID));
        UserType userType = UserType.getUserType(rs.getString(USER_TYPE));
        int startTime = rs.getInt(START_TIME);
        int endTime = rs.getInt(END_TIME);
        return new UserHolidayTemplateData(userId, userType, Arrays.asList(new HolidayTemplateDuration(holidayTemplate, startTime, endTime)));
    }

    public static List<UserHolidayTemplateData> getUserHolidayTemplateDataList(List<UserHolidayTemplateData> holidayTemplateDataList) {
        if (CollectionUtils.isEmpty(holidayTemplateDataList)) {
            return new ArrayList<>();
        }
        List<UserHolidayTemplateData> finalUserHolidayTemplateDataList = new ArrayList<>();
        Map<UUID, List<UserHolidayTemplateData>> userHolidayTemplateDataMap = new HashMap<>();
        for (UserHolidayTemplateData userHolidayTemplateData : holidayTemplateDataList) {
            UUID userId = userHolidayTemplateData.getUserId();
            if (!userHolidayTemplateDataMap.containsKey(userId)) {
                userHolidayTemplateDataMap.put(userId, new ArrayList<>());
            }
            userHolidayTemplateDataMap.get(userId).add(userHolidayTemplateData);
        }

        for (List<UserHolidayTemplateData> userHolidayTemplateDataList : userHolidayTemplateDataMap.values()) {
            UserHolidayTemplateData firstEntry = userHolidayTemplateDataList.get(0);
            List<HolidayTemplateDuration> holidayTemplateDurationList = new ArrayList<>();
            for (UserHolidayTemplateData userHolidayTemplateData : userHolidayTemplateDataList) {
                holidayTemplateDurationList.addAll(userHolidayTemplateData.getHolidayTemplateDurationList());
            }
            finalUserHolidayTemplateDataList.add(new UserHolidayTemplateData(firstEntry.getUserId(), firstEntry.getUserType(), holidayTemplateDurationList));
        }

        return finalUserHolidayTemplateDataList;
    }

    // All rows are expected for same user id
    public static UserHolidayTemplateData getUserHolidayTemplateData(List<UserHolidayTemplateData> holidayTemplateDataList) {
        if (CollectionUtils.isEmpty(holidayTemplateDataList)) {
            return null;
        }

        UserHolidayTemplateData firstEntry = holidayTemplateDataList.get(0);
        List<HolidayTemplateDuration> holidayTemplateDurationList = new ArrayList<>();
        for (UserHolidayTemplateData userHolidayTemplateData : holidayTemplateDataList) {
            holidayTemplateDurationList.addAll(userHolidayTemplateData.getHolidayTemplateDurationList());
        }

        return  new UserHolidayTemplateData(firstEntry.getUserId(), firstEntry.getUserType(), holidayTemplateDurationList);
    }
}