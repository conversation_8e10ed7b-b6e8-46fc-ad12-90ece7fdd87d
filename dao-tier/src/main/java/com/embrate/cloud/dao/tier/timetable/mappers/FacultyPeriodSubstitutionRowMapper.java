package com.embrate.cloud.dao.tier.timetable.mappers;

import com.embrate.cloud.core.api.timetable.FacultyPeriodSubstitutionDetails;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 *
 */
public class FacultyPeriodSubstitutionRowMapper implements RowMapper<FacultyPeriodSubstitutionDetails>{
	
//	public static final String INSTITUTE_ID = "timetable_meta_data.institute_id";
//	public static final String ACADEMIC_SESSION_ID = "timetable_meta_data.academic_session_id";
//
//	public static final String SUBSTITUTION_ID = "faculty_substitution.substitution_id";
//
//	public static final String DAY = "faculty_substitution.day";
//
//	public static final String TIMETABLE_ID = "timetable_meta_data.timetable_id";
//
//	private static final StandardRowDetailsRowMapper STANDARD_ROW_DETAILS_ROW_MAPPER = new StandardRowDetailsRowMapper();
//	private static final CourseRowMapper COURSE_ROW_MAPPER = new CourseRowMapper();
//	private static final StaffRowMapper STAFF_ROW_MAPPER = new StaffRowMapper();
//	private static final ShiftPeriodDetailsRowMapper SHIFT_PERIOD_DETAILS_ROW_MAPPER = new ShiftPeriodDetailsRowMapper();
	
	
	@Override
	public FacultyPeriodSubstitutionDetails mapRow(ResultSet rs, int rowNum) throws SQLException {
		
//		final Standard standard = StandardRowDetailsRowMapper.
//				getStandardResponse(STANDARD_ROW_DETAILS_ROW_MAPPER.mapRow(rs, rowNum));
//		final Staff staff = STAFF_ROW_MAPPER.mapRow(rs, rowNum);
//		final Course course = COURSE_ROW_MAPPER.mapRow(rs, rowNum);
//		final ShiftPeriodDetails shiftPeriodDetails = SHIFT_PERIOD_DETAILS_ROW_MAPPER.mapRow(rs, rowNum);
//
//		return new FacultyPeriodSubstitutionDetails(rs.getInt(INSTITUTE_ID), rs.getInt(ACADEMIC_SESSION_ID),
//				rs.getString(SUBSTITUTION_ID) == null ? null : UUID.fromString(rs.getString(SUBSTITUTION_ID)),
//				staff, new FacultyPeriodDetails(UUID.fromString(rs.getString(TIMETABLE_ID)),
//				rs.getString(DAY) == null ? null : DayOfWeek.valueOf(rs.getString(DAY)),
//				shiftPeriodDetails, standard, course));
		return null;
	}
}