/**
 * 
 */
package com.embrate.cloud.dao.tier.lecture.mappers;

import com.embrate.cloud.core.api.lecture.LectureDetailsPayload;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import java.lang.reflect.Type;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.Set;
import java.util.UUID;

import static com.lernen.cloud.core.utils.SharedConstants.GSON;

/**
 * <AUTHOR>
 *
 */
public class LectureDetailsPayloadRowMapper implements RowMapper<LectureDetailsPayload> {

	protected static final String INSTITUTE_ID = "lecture_details.institute_id";
	protected static final String LECTURE_ID = "lecture_details.lecture_id";
	protected static final String STANDARD_ID = "lecture_details.standard_id";
	protected static final String SECTION_IDS = "lecture_details.section_ids";
	protected static final String COURSE_ID = "lecture_details.course_id";
	protected static final String CHAPTER = "lecture_details.chapter";
	protected static final String TITLE = "lecture_details.title";
	protected static final String VIDEO_LINK = "lecture_details.video_link";
	protected static final String LECTURER_USER_ID = "lecture_details.lecturer_user_id";
	
	protected static final String LECTURER_NAME = "lecture_details.lecturer_name";
	protected static final String CREATED_USER_ID = "lecture_details.created_user_id";
	protected static final String CREATED_TIMESTAMP = "lecture_details.created_timestamp";
	protected static final String UPDATED_USER_ID = "lecture_details.updated_user_id";
	
	protected static final String UPDATED_TIMESTAMP = "lecture_details.updated_timestamp";
	protected static final String DESCRIPTION = "lecture_details.description";
	protected static final String RECOMMENDED_VIEW_DATE = "lecture_details.recommended_view_date";
	protected static final String STATUS = "lecture_details.status";

	@Override
	public LectureDetailsPayload mapRow(ResultSet rs, int rowNum) throws SQLException {
		if (rs.getString(LECTURE_ID) == null) {
			return null;
		}
		
		final Timestamp createdTimestamp = rs.getTimestamp(CREATED_TIMESTAMP);
		final Integer createdTimestampTime = createdTimestamp == null ? null
				: (int) (createdTimestamp.getTime() / 1000l);
		
		final Timestamp updatedTimestamp = rs.getTimestamp(UPDATED_TIMESTAMP);
		final Integer updatedTimestampTime = updatedTimestamp == null ? null
				: (int) (updatedTimestamp.getTime() / 1000l);
		
		final Timestamp recommendedViewDate = rs.getTimestamp(RECOMMENDED_VIEW_DATE);
		final Integer recommendedViewDateTime = recommendedViewDate == null ? null
				: (int) (recommendedViewDate.getTime() / 1000l);

		final String sectionIdStr = rs.getString(SECTION_IDS);
		Set<Integer> sectionIdList = null;
		if (!StringUtils.isBlank(sectionIdStr)) {
			final Type collectionType = new TypeToken<Set<Integer>>() {
			}.getType();
			sectionIdList = GSON.fromJson(sectionIdStr, collectionType);
		}

		return new LectureDetailsPayload(rs.getInt(INSTITUTE_ID), UUID.fromString(rs.getString(LECTURE_ID)), 
				UUID.fromString(rs.getString(STANDARD_ID)), sectionIdList, UUID.fromString(rs.getString(COURSE_ID)),
				rs.getString(CHAPTER), rs.getString(TITLE), rs.getString(VIDEO_LINK), 
				rs.getString(LECTURER_USER_ID) == null ? null : UUID.fromString(rs.getString(LECTURER_USER_ID)),
				rs.getString(LECTURER_NAME), 
				rs.getString(CREATED_USER_ID) == null ? null : UUID.fromString(rs.getString(CREATED_USER_ID)),
				createdTimestampTime, 
				rs.getString(UPDATED_USER_ID) == null ? null : UUID.fromString(rs.getString(UPDATED_USER_ID)),
				updatedTimestampTime, rs.getString(DESCRIPTION), recommendedViewDateTime);
		
	}
}
