package com.lernen.cloud.dao.tier.client.payment.mappers;

import java.lang.reflect.Type;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lernen.cloud.core.api.client.payment.ClientBillingData;
import com.lernen.cloud.core.api.client.payment.ClientPaymentStatus;
import com.lernen.cloud.core.utils.SharedConstants;

/**
 *
 * <AUTHOR>
 *
 */
public class ClientPaymentRowMapper implements RowMapper<ClientBillingData> {

	private static final Gson GSON = SharedConstants.GSON;

	private static final String INSTITUTE_ID = "institute_id";
	private static final String BILLSTART = "billstart";
	private static final String BILLEND = "billend";
	private static final String AMOUNT = "amount";
	private static final String PAYMENT_STATUS = "payment_status";
	private static final String SILENT_DAYS = "silent_days";
	private static final String SOFT_REMINDER_DAYS = "soft_reminder_days";
	private static final String HARD_REMINDER_DAYS = "hard_reminder_days";
	private static final String META_DATA = "meta_data";
	private static final String DISPLAY_MESSAGE = "display_message";


	@Override
	public ClientBillingData mapRow(ResultSet rs, int rowNum) throws SQLException {
		String metaDataStr = rs.getString(META_DATA);
		Map<String, Object> metaData = null;
		
		if (!StringUtils.isBlank(metaDataStr)) {
			Type type = new TypeToken<Map<String, Object>>() {
			}.getType();
			metaData = GSON.fromJson(metaDataStr, type);
		}
		final ClientBillingData clientBillingData = new ClientBillingData(rs.getInt(INSTITUTE_ID), rs.getInt(BILLSTART),
				rs.getInt(BILLEND), rs.getDouble(AMOUNT), ClientPaymentStatus.valueOf(rs.getString(PAYMENT_STATUS)),
				rs.getInt(SILENT_DAYS), rs.getInt(SOFT_REMINDER_DAYS), rs.getInt(HARD_REMINDER_DAYS), metaData,
				rs.getString(DISPLAY_MESSAGE));

		return clientBillingData;
	}

}
