package com.lernen.cloud.dao.tier.student.mappers;

import com.lernen.cloud.core.api.student.StudentSessionSummary;
import com.lernen.cloud.core.api.student.StudentStatus;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Row mapper for StudentSessionSummary
 * 
 * <AUTHOR>
 */
public class StudentSessionSummaryRowMapper implements RowMapper<StudentSessionSummary> {

    protected static final String INSTITUTE_ID = "institute_id";
    protected static final String ACADEMIC_SESSION_ID = "academic_session_id";
    protected static final String SESSION_STATUS = "session_status";
    protected static final String STUDENT_COUNT = "count";

    @Override
    public StudentSessionSummary mapRow(ResultSet rs, int rowNum) throws SQLException {
        int instituteId = rs.getInt(INSTITUTE_ID);
        int academicSessionId = rs.getInt(ACADEMIC_SESSION_ID);
        StudentStatus sessionStatus = StudentStatus.getStudentStatus(rs.getString(SESSION_STATUS));
        int studentCount = rs.getInt(STUDENT_COUNT);

        return new StudentSessionSummary(instituteId, academicSessionId, sessionStatus, studentCount);
    }
}
