package com.lernen.cloud.dao.tier.complainbox;

import java.sql.Timestamp;
import java.util.*;

import com.lernen.cloud.core.api.user.UserCategory;
import com.lernen.cloud.core.utils.DateUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.support.TransactionTemplate;

import com.embrate.cloud.core.api.complainbox.ComplainDocumentType;
import com.google.gson.Gson;
import com.lernen.cloud.core.api.common.CounterType;
import com.lernen.cloud.core.api.complainbox.ComplainCategoryPayload;
import com.lernen.cloud.core.api.complainbox.ComplainStatus;
import com.lernen.cloud.core.api.complainbox.StandardComplainResponse;
import com.lernen.cloud.core.api.complainbox.StandardResponseDetails;
import com.lernen.cloud.core.api.complainbox.StudentComplaintMetadataPayload;
import com.lernen.cloud.core.api.complainbox.StudentComplaintMetadataDetails;
import com.lernen.cloud.core.api.complainbox.StudentComplaintResponses;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.DatabaseException;
import com.lernen.cloud.core.api.institute.CounterData;
import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.core.utils.SharedConstants;
import com.lernen.cloud.core.utils.exceptions.ExceptionHandling;
import com.lernen.cloud.dao.tier.complainbox.mappers.ComplainCategoryRowMapper;
import com.lernen.cloud.dao.tier.complainbox.mappers.ComplainMetadataRowMapper;
import com.lernen.cloud.dao.tier.complainbox.mappers.ComplainRowMapper;
import com.lernen.cloud.dao.tier.complainbox.mappers.ComplaintResponsesRowMapper;
import com.lernen.cloud.dao.tier.complainbox.mappers.StandardComplainResponseDetailsRowMapper;
import com.lernen.cloud.dao.tier.institute.InstituteDao;
import org.springframework.util.CollectionUtils;


public class ComplainBoxDao {
    private static final Logger logger = LogManager.getLogger(ComplainBoxDao.class);
    private static final ComplainCategoryRowMapper COMPLAIN_CATEGORY_TYPE_ROW_MAPPER = new ComplainCategoryRowMapper();
    private static final ComplainRowMapper COMPLAIN_METADATA_ROW_MAPPER = new ComplainRowMapper();
    private static final ComplainCategoryRowMapper COMPLAIN_CATEGORY_ROW_MAPPER = new ComplainCategoryRowMapper();
    private static final StandardComplainResponseDetailsRowMapper STANDARD_RESPONSE_DETAILS_ROW_MAPPER = new StandardComplainResponseDetailsRowMapper();
    private static final ComplainMetadataRowMapper COMPLAIN_DETAILS_ROW_MAPPER = new ComplainMetadataRowMapper();
    private static final ComplaintResponsesRowMapper COMPLAIN_RESPONSE_ROW_MAPPER = new ComplaintResponsesRowMapper();
    private static final Gson GSON = SharedConstants.GSON;

    private static final String ADD_STUDENT_COMPLAIN_CATEGORY = "insert into complain_category (institute_id, complain_category_id, complaint_category_name, created_by) "
            + " values (?, ?, ?, ?) ";

    private static final String UPDATE_COMPLAIN_CATEGORY = " update complain_category "
            + " set complaint_category_name = ?,  updated_by = ? where institute_id = ? and complain_category_id = ? ";

    private static final String GET_COMPLAIN_METADATA_BY_ID = " select * from complain_metadata "
            + " where institute_id = ? and category_id  = ? ";

    private static final String GET_COMPLAIN_CATEGORY_TYPE_BY_ID = " select * from complain_category "
            + " where institute_id = ? and complain_category_id  = ? ";

    private static final String DELETE_COMPLAIN_CATEGORY_TYPE = " delete from complain_category"
            + " where institute_id = ?  and complain_category_id = ? ";

    private static final String GET_COMPLAIN_CATEGORY_TYPE = " select * from complain_category "
            + " where institute_id = ? order by complain_category.complaint_category_name asc ";

    private static final String GET_COMPLAIN_CATEGORY_TYPE_BY_NAME = " select * from complain_category "
            + " where institute_id = ?  and complaint_category_name = ? ";

    // standard response

    private static final String ADD_STANDARD_RESPONSE_DETAILS = "insert into standard_complain_response(institute_id,standard_complain_response_id ,standard_response ,complain_category_id ) values (?,?,?,?)";

    private static final String UPDATE_STANDARD_RESPONSE_DETAILS = "update standard_complain_response set  " +
            "standard_response  = ?, complain_category_id  = ? where standard_complain_response_id  = ? and institute_id = ? ";

    private static final String DELETE_STANDARD_RESPONSE_DETAILS = "delete from standard_complain_response where standard_complain_response_id = ? and institute_id = ?";

    private static final String GET_STANDARD_RESPONSE_DETAILS = "select * from standard_complain_response "
            + " join complain_category on standard_complain_response.complain_category_id = complain_category.complain_category_id "
            + " where standard_complain_response.institute_id = ? ";

    // complain metadata

    private static final String ADD_COMPLAIN_METADATA = "insert into complain_metadata(institute_id, complain_id, complain_number, title, description, complaint_by, complaint_for, status, category_id, created_by) " +
            " values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    private static final String UPDATE_COMPLAIN_STATUS = "update complain_metadata set status = ?, " +
            "closed_by = ?, closed_at = ? where complain_id = ? and institute_id = ? ";

    private static final String GET_COMPLAIN_DETAILS = "select * from complain_metadata "
            + " join complain_category on complain_metadata.category_id = complain_category.complain_category_id "
            + " where complain_metadata.institute_id = ? ";

    private static final String GET_COMPLAIN_DETAIL = "select * from complain_metadata "
            + " join complain_category on complain_metadata.category_id = complain_category.complain_category_id "
            + " where complain_metadata.complain_id = ? and complain_metadata.institute_id = ? ";

    private static final String UPDATE_COMPLAIN_ATTACHMENTS = " update complain_metadata set attachments = ? where complain_id  = ? ";


    // complain response

    private static final String ADD_COMPLAIN_RESPONSE = "insert into complain_responses (response_id, response , response_by, response_at, complain_id ) "
            + " values (?, ?, ?, ?, ?) ";

    private static final String DELETE_COMPLAIN_RESPONSE= " delete from complain_responses"
            + " where response_id = ? ";

    private static final String GET_COMPLAIN_RESPONSE = " select * from complain_responses "
            + " where complain_id  = ? order by complain_responses.response  asc ";

    private final JdbcTemplate jdbcTemplate;
    private final TransactionTemplate transactionTemplate;
    private final InstituteDao instituteDao;

    public ComplainBoxDao(JdbcTemplate jdbcTemplate, TransactionTemplate transactionTemplate, InstituteDao instituteDao) {
        this.jdbcTemplate = jdbcTemplate;
        this.transactionTemplate = transactionTemplate;
        this.instituteDao = instituteDao;
    }

    // complain category

    public boolean addComplainCategory(ComplainCategoryPayload categoryPayload, UUID userId) {
        try {
            return jdbcTemplate.update(ADD_STUDENT_COMPLAIN_CATEGORY, categoryPayload.getInstituteId(),
                    categoryPayload.getCategoryId(), categoryPayload.getCategoryName(), userId.toString()) == 1;
        } catch (final DataAccessException dataAccessException) {
            logger.error("DataAccessException occurred while adding complain category name for institute {}",
                    categoryPayload.getInstituteId(), dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception occurred while adding complain category name for institute {}", categoryPayload.getInstituteId(), e);
        }
        return false;
    }

    public ComplainCategoryPayload getComplainCategoryByName(ComplainCategoryPayload categoryPayload) {
        try {
            final Object[] args = {categoryPayload.getInstituteId(),categoryPayload.getCategoryName()};
            return jdbcTemplate.queryForObject(GET_COMPLAIN_CATEGORY_TYPE_BY_NAME, args,  COMPLAIN_CATEGORY_TYPE_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Exception occurred while getting complain category name for institute {} {}",
                    categoryPayload.getInstituteId(), categoryPayload.getCategoryName(), e);
        }
        return null;
    }

    public List<ComplainCategoryPayload> getComplainCategory(int instituteId) {
        try {
            final Object[] args = {instituteId};
            return jdbcTemplate.query(GET_COMPLAIN_CATEGORY_TYPE, args, COMPLAIN_CATEGORY_TYPE_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Exception occurred while getting the diary remark category information for institute {}", instituteId, e);
        }
        return null;
    }

    public boolean updateComplainCategory(ComplainCategoryPayload categoryPayload,UUID userId) {
        try {
            return jdbcTemplate.update(UPDATE_COMPLAIN_CATEGORY, categoryPayload.getCategoryName(),userId.toString(), categoryPayload.getInstituteId(),
                  categoryPayload.getCategoryId()) == 1;
        } catch (final DataAccessException dataAccessException) {
            // ExceptionHandling.HandleException(dataAccessException, DIARY_REMARKS_CATEGORY, "diary remark category type");
            logger.error("DataAccessException occurred while updating complain category type for institute {} ",
                    categoryPayload.getInstituteId(), dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception occurred while updating complain category type for institute {} ",
                    categoryPayload.getInstituteId(), e);
        }
        return false;
    }

    public List<StudentComplaintMetadataPayload> getComplainMetaDataById(int instituteId, int categoryId) {
        try {
            final Object[] args = {instituteId, categoryId};
            return jdbcTemplate.query(GET_COMPLAIN_METADATA_BY_ID, args, COMPLAIN_METADATA_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Exception occurred while getting complain category for institute {}, categoryId {}",
                    instituteId, categoryId, e);
        }
        return null;
    }

    public ComplainCategoryPayload getComplainCategoryById(int instituteId, int categoryId) {
        try {
            final Object[] args = {instituteId, categoryId};
            return jdbcTemplate.queryForObject(GET_COMPLAIN_CATEGORY_TYPE_BY_ID, args, COMPLAIN_CATEGORY_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Exception occurred while getting complain category for institute {}, categoryId {}",
                    instituteId, categoryId, e);
        }
        return null;
    }

    public boolean deleteComplainCategory(int instituteId,int categoryId) {
        try {
            final Object[] args = {instituteId, categoryId};
            return jdbcTemplate.update(DELETE_COMPLAIN_CATEGORY_TYPE, args) >= 0;
        } catch (final Exception e) {
            logger.error("Exception occurred while deleting complain category type for institute {} and categoryId {}",
                    instituteId, categoryId, e);
        }
        return false;
    }

    // Standard response

    public boolean addStandardResponse(StandardComplainResponse responsePayload,UUID userId){
        try {
             
        final UUID responseId = UUID.randomUUID();
        
        return jdbcTemplate.update(ADD_STANDARD_RESPONSE_DETAILS, responsePayload.getInstituteId(),
                responseId.toString(),responsePayload.getResponse(),
                responsePayload.getCategoryId()) ==1;
          
        } catch (final DatabaseException | ApplicationException ex) {
            throw ex;
        } catch (final Exception e) {
            logger.error("Unable to add standard response", e);
        }
        return false;
    }

    public boolean updateStandardResponse(StandardComplainResponse responsePayload,UUID userId){
        try {
          
        return jdbcTemplate.update(UPDATE_STANDARD_RESPONSE_DETAILS,
                responsePayload.getResponse(), responsePayload.getCategoryId(),
                responsePayload.getStandardResponseId().toString(), responsePayload.getInstituteId()) >= 0;
                           
        } catch (final DatabaseException | ApplicationException ex) {
            throw ex;
        } catch (final Exception e) {
            logger.error("Unable to update standard response", e);
        }
        return false;
    }

    public boolean deleteStandardResponse(UUID responseId, int instituteId){
        try {
           
            return jdbcTemplate.update(DELETE_STANDARD_RESPONSE_DETAILS, responseId.toString(), instituteId)==1;
             
        } catch (final DatabaseException | ApplicationException ex) {
            throw ex;
        } catch (final Exception e) {
            logger.error("Unable to delete standard remark", e);
        }
        return false;
    }

    public List<StandardResponseDetails> getStandardResponseDetails(int instituteId,int categoryId){
        try {
            final List<Object> args = new ArrayList<>();
            args.add(instituteId);
            StringBuilder query = new StringBuilder(GET_STANDARD_RESPONSE_DETAILS);
            if(categoryId > 0) {
                 query.append(" and  standard_complain_response.complain_category_id = ? ");
                 args.add(categoryId);
            }
            return jdbcTemplate.query(query.toString(), args.toArray(),STANDARD_RESPONSE_DETAILS_ROW_MAPPER);
            
        } catch (final Exception e) {
            logger.error("Error while fetching student standard remarks for {}", instituteId, e);
        }
        return null;
    }

    // complain metadata

    public UUID addComplainMetadata(StudentComplaintMetadataPayload studentComplainPayload,UUID userId){
        try {
            final UUID complainId = UUID.randomUUID();
            studentComplainPayload.setComplainId(complainId);

            final CounterType counterType = CounterType.COMPLAIN_NUMBER;
            String complainNumber = studentComplainPayload.getComplainNumber();
            String finalComplainNumber = complainNumber;

            if(StringUtils.isBlank(complainNumber)) {
                final CounterData invoiceCounterData = instituteDao.getCounter(studentComplainPayload.getInstituteId(), counterType, true);
                if (invoiceCounterData == null) {
                    logger.error( "{} counter is not present for institute {}", counterType.name() , studentComplainPayload.getInstituteId());
                    return null;
                }
                finalComplainNumber = invoiceCounterData.getFullCounterValue();
            }
            
            boolean result = jdbcTemplate.update(ADD_COMPLAIN_METADATA, studentComplainPayload.getInstituteId(),
                    complainId.toString(), finalComplainNumber, studentComplainPayload.getTitle(),
                    studentComplainPayload.getDescription(),
                    studentComplainPayload.getComplainBy(), studentComplainPayload.getComplainFor() == null
                            ? null : studentComplainPayload.getComplainFor().toString(),
                    studentComplainPayload.getStatus().toString(),
                    studentComplainPayload.getCategoryId(), userId.toString()) == 1;

            if (StringUtils.isBlank(complainNumber) && !instituteDao.incrementCounter(studentComplainPayload.getInstituteId(), counterType)) {
                throw new RuntimeException("Unable to increment Complain counter");
            }
            if(!result) {
                return null;
            }
            return complainId;
        } catch (final DatabaseException | ApplicationException ex) {
            throw ex;
        } catch (final Exception e) {
            logger.error("Unable to execute transaction", e);
        }
        return null;
    }

    public boolean updateComplainStatus(UUID complainId, int instituteId, UUID userId, ComplainStatus complainStatus){
        try {
            final int now = DateUtils.now();
            boolean result = jdbcTemplate.update(UPDATE_COMPLAIN_STATUS,
                    complainStatus.name(),
                    complainStatus == ComplainStatus.CLOSED ? (userId == null ? null : userId.toString()) : null,
                    complainStatus == ComplainStatus.CLOSED ? (new Timestamp(now * 1000L)) : null,
                    complainId.toString(),
                    instituteId) == 1;
            if (!result) {
                throw new RuntimeException("Unable to close Complain.");
            }
           return true;
           
        } catch (final DatabaseException | ApplicationException ex) {
            throw ex;
        } catch (final Exception e) {
            logger.error("Unable to execute transaction", e);
        }
        return false;
    }
    public List<StudentComplaintMetadataDetails>  getComplainDetails(int instituteId, int categoryId, UUID studentId,
            Set<ComplainStatus> complainStatusSet, int limit, int offset) {
        try {
            final List<Object> args = new ArrayList<>();
            args.add(instituteId);
           
            StringBuilder query = new StringBuilder(GET_COMPLAIN_DETAILS);

            if(categoryId > 0) {
                query.append(" and complain_metadata.category_id =  ? " );
                 args.add(categoryId);
            }

             if(studentId != null) {
                 query.append(" and complain_metadata.created_by = ? ");
                 args.add(studentId.toString());
                 /**
                  * is studentid is not null, meaning call is from student mobile app,
                  * and we are not showing cancelled list in student mobile app.
                  */
                 query.append(" and complain_metadata.status <> 'CANCELLED' ");
             }

            if(!CollectionUtils.isEmpty(complainStatusSet)) {
                final StringBuilder inQuery = new StringBuilder();
                inQuery.append("(");
                boolean first = true;
                for (final ComplainStatus complainStatus : complainStatusSet) {
                    args.add(complainStatus.name());
                    if (first) {
                        inQuery.append("?");
                        first = false;
                        continue;
                    }
                    inQuery.append(", ?");
                }
                inQuery.append(")");
                query.append(String.format(" and complain_metadata.status in %s ", inQuery));
            }

            if(limit > 0 && offset >= 0) {
                query.append(" limit ? offset ? " );
                args.add(limit);
                args.add(offset);
            }

            logger.error(query);
            for(Object arg : args) {
                logger.error(arg);
            }

            return jdbcTemplate.query(query.toString(), args.toArray(), COMPLAIN_DETAILS_ROW_MAPPER);
            

        } catch (final Exception e) {
            logger.error("Error while fetching student remarks for {}", instituteId, e);
        }
        return null;
    }

    public StudentComplaintMetadataDetails  getComplainDetailsByComplainId(UUID complainId, int instituteId){
        try {
            final List<Object> args = new ArrayList<>();
            args.add(complainId.toString());
            args.add(instituteId);
            StringBuilder query = new StringBuilder(GET_COMPLAIN_DETAIL);
          
            return jdbcTemplate.queryForObject(query.toString(), args.toArray(), COMPLAIN_DETAILS_ROW_MAPPER);
            

        } catch (final Exception e) {
            
            logger.error("Error while fetching complain details for {}",complainId, e);
        }
        return null;
    }

    public boolean updateDocuments(UUID complainId, List<Document<ComplainDocumentType>> homeworkAttachments) {
		try {
			final Object[] args = { GSON.toJson(homeworkAttachments), complainId.toString() };
			return jdbcTemplate.update(UPDATE_COMPLAIN_ATTACHMENTS, args) == 1;
		} catch (final DataAccessException dataAccessException) {
			ExceptionHandling.HandleException(dataAccessException, null, null);
			logger.error("Error while updating complain documents for complain id {}", complainId);
		} catch (final Exception e) {
			logger.error("Exception while updating complain documents for complain id {}", complainId, e);
		}
		return false;
	}

    // complain response

    public boolean addComplainResponse(StudentComplaintResponses responsePayload, UUID userId) {
        try {
            final UUID responseId = UUID.randomUUID();
            final int responseAt = DateUtils.now();
            return jdbcTemplate.update(ADD_COMPLAIN_RESPONSE, responseId.toString(),
            responsePayload.getResponse(), userId.toString(), new Timestamp(responseAt * 1000L),responsePayload.getComplainId().toString()) == 1;
        } catch (final DataAccessException dataAccessException) {
            logger.error("DataAccessException occurred while adding complain response for Complain {}",
            responsePayload.getComplainId(), dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception occurred while adding complain response for Complain {}", responsePayload.getComplainId(), e);
        }
        return false;
    }

    public boolean deleteComplainResponse(UUID responseId) {
        try {
            final Object[] args = {responseId.toString()};
            return jdbcTemplate.update(DELETE_COMPLAIN_RESPONSE, args) >= 0;
        } catch (final Exception e) {
            logger.error("Exception occurred while deleting complain category type for responseId {}", responseId, e);
        }
        return false;
    }

    public List<StudentComplaintResponses> getComplainResponse(UUID complainId) {
        try {
            final Object[] args = {complainId.toString()};
            return jdbcTemplate.query(GET_COMPLAIN_RESPONSE, args, COMPLAIN_RESPONSE_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Exception occurred while getting the response for complain {}", complainId, e);
        }
        return null;
    }

}
