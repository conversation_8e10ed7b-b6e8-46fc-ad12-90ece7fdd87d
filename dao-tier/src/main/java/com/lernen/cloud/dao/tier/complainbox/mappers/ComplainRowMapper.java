package com.lernen.cloud.dao.tier.complainbox.mappers;

import java.lang.reflect.Type;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.List;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import com.embrate.cloud.core.api.complainbox.ComplainDocumentType;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lernen.cloud.core.api.complainbox.ComplainStatus;
import com.lernen.cloud.core.api.complainbox.StudentComplaintMetadataPayload;
import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.core.utils.SharedConstants;

public class ComplainRowMapper implements RowMapper<StudentComplaintMetadataPayload>{
    private static final String CATEGORY_ID = "complain_metadata.category_id";
    private static final String INSTITUTE_ID = "complain_metadata.institute_id";
    private static final String COMPLAIN_ID = "complain_metadata.complain_id";
    private static final String COMPLAIN_NUMBER = "complain_metadata.complain_number";
    private static final String TITLE = "complain_metadata.title";
    private static final String DESCRIPTION = "complain_metadata.description";
    private static final String COMPLAIN_BY = "complain_metadata.complaint_by";
    private static final String COMPLAIN_FOR = "complain_metadata.complaint_for";
    private static final String STATUS = "complain_metadata.status";
    private static final String CLOSED_BY = "complain_metadata.closed_by";
    private static final String CREATED_BY = "complain_metadata.created_by";
    private static final String CLOSED_AT = "complain_metadata.closed_at";
    private static final String CREATED_AT = "complain_metadata.created_at";
    protected static final String ATTACHMENTS = "complain_metadata.attachments";
    private static final Gson GSON = SharedConstants.GSON;

    @Override
    public StudentComplaintMetadataPayload mapRow(ResultSet rs, int rowNum) throws SQLException {
        if(rs.getString(COMPLAIN_ID) == null) {
            return null;
        }
        final String documents = rs.getString(ATTACHMENTS);
		List<Document<ComplainDocumentType>> complainAttachments = null;
		if (!StringUtils.isBlank(documents)) {
			final Type collectionType = new TypeToken<List<Document<ComplainDocumentType>>>() {
			}.getType();
			complainAttachments = GSON.fromJson(documents, collectionType);
		}
        final Timestamp closedAt = rs.getTimestamp(CLOSED_AT);
		final Integer closedAtTime = closedAt == null ? null
				: (int) (closedAt.getTime() / 1000l);
        final Timestamp createdAt = rs.getTimestamp(CREATED_AT);
		final Integer createdAtTime = createdAt == null ? null
				: (int) (createdAt.getTime() / 1000l);
        return new StudentComplaintMetadataPayload(rs.getInt(INSTITUTE_ID), UUID.fromString(rs.getString(COMPLAIN_ID)),
                rs.getString(TITLE), rs.getString(DESCRIPTION), rs.getString(COMPLAIN_NUMBER),
                rs.getString(COMPLAIN_BY), StringUtils.isEmpty(rs.getString(COMPLAIN_FOR)) ? null : UUID.fromString(rs.getString(COMPLAIN_FOR)),
                ComplainStatus.getComplainStatus(rs.getString(STATUS)), rs.getInt(CATEGORY_ID),
                StringUtils.isEmpty(rs.getString(CLOSED_BY)) ? null : UUID.fromString(rs.getString(CLOSED_BY)),
                UUID.fromString(rs.getString(CREATED_BY)), createdAtTime, closedAtTime, complainAttachments);
    }
}
