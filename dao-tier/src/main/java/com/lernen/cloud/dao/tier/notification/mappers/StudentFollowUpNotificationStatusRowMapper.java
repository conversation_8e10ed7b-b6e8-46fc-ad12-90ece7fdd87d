package com.lernen.cloud.dao.tier.notification.mappers;

import com.embrate.cloud.core.api.service.notification.StudentBellNotificationDetails;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lernen.cloud.core.api.common.CommunicationServiceProvider;
import com.lernen.cloud.core.api.common.DeliveryMode;
import com.lernen.cloud.core.api.notification.NotificationDetails;
import com.lernen.cloud.core.api.notification.NotificationStatus;
import com.lernen.cloud.core.api.notification.NotificationType;
import com.lernen.cloud.core.api.notification.StudentFollowUpNotificationDetails;
import com.lernen.cloud.core.api.student.StudentDetailedRow;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.dao.tier.student.mappers.StudentWithoutSessionRowMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import java.lang.reflect.Type;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class StudentFollowUpNotificationStatusRowMapper implements RowMapper<StudentFollowUpNotificationDetails>{

    private static final String NOTIFICATION_ID = "notification_id";
    private static final String INSTITUTE_ID = "institute_id";
    private static final String USER_ID = "user_id";
    private static final String USER_TYPE = "user_type";
    private static final String ACADEMIC_SESSION_ID = "academic_session_id";
    private static final String SERVICE_PROVIDER = "service_provider";
    private static final String NOTIFICATION_TYPE = "notification_type";
    private static final String DELIVERY_MODE = "delivery_mode";
    private static final String DELIVERY_DESTINATION = "delivery_destination";
    private static final String BATCH_ID = "batch_id";
    private static final String BATCH_NAME = "batch_name";
    private static final String NOTIFICATION_TITLE = "notification_title";
    private static final String NOTIFICATION_CONTENT = "notification_content";
    private static final String STATUS = "status";
    private static final String DELIVERED = "delivered";
    private static final String GENERATION = "generation";
    private static final String LAST_OPENED = "last_opened";
    private static final String LAST_CLICKED = "last_clicked";
    private static final String EXTERNAL_UNIQUE_ID = "external_unique_id";
    private static final String META_DATA = "meta_data";
    private static final String CREDITS_USED = "credits_used";
    private static final String CREDIT_TRANSACTION_ID = "credits_transaction_id";
    private static final String REFUNDED_CREDITS = "refunded_credits";

    private static final Gson GSON = new Gson();
    private static final StudentWithoutSessionRowMapper STUDENT_WITHOUT_SESSION_ROW_MAPPER = new StudentWithoutSessionRowMapper();

    @Override
    public StudentFollowUpNotificationDetails mapRow(ResultSet rs, int rowNum) throws SQLException {
        StudentDetailedRow studentDetailedRow = STUDENT_WITHOUT_SESSION_ROW_MAPPER.mapRow(rs, rowNum);
        final Type metaDataMap = new TypeToken<Map<String, Object>>() {
        }.getType();
        Map<String, Object> metaData = new HashMap<>();
        if (!StringUtils.isBlank(rs.getString(META_DATA))) {
            metaData = GSON.fromJson(rs.getString(META_DATA), metaDataMap);
        }
        String creditTransactionIdStr = rs.getString(CREDIT_TRANSACTION_ID);
        UUID creditTransactionId =  StringUtils.isBlank(creditTransactionIdStr) ? null : UUID.fromString(creditTransactionIdStr);;

        String batchIdString = rs.getString(BATCH_ID);
        UUID batchId = StringUtils.isBlank(batchIdString) ? null : UUID.fromString(batchIdString);


        return new StudentFollowUpNotificationDetails(UUID.fromString(rs.getString(NOTIFICATION_ID)), rs.getInt(INSTITUTE_ID),
                rs.getString(USER_ID) == null ? null : UUID.fromString(rs.getString(USER_ID)), UserType.getUserType(rs.getString(USER_TYPE)),
                rs.getInt(ACADEMIC_SESSION_ID), CommunicationServiceProvider.valueOf(rs.getString(SERVICE_PROVIDER)),
                NotificationType.valueOf(rs.getString(NOTIFICATION_TYPE)),
                DeliveryMode.valueOf(rs.getString(DELIVERY_MODE)), rs.getString(DELIVERY_DESTINATION), batchId,
                rs.getString(BATCH_NAME), rs.getString(NOTIFICATION_TITLE), rs.getString(NOTIFICATION_CONTENT),
                NotificationStatus.valueOf(rs.getString(STATUS)),
                rs.getTimestamp(DELIVERED) == null ? null : (int) (rs.getTimestamp(DELIVERED).getTime() / 1000l),
                (int) (rs.getTimestamp(GENERATION).getTime() / 1000l),
                rs.getTimestamp(LAST_OPENED) == null ? null : (int) (rs.getTimestamp(LAST_OPENED).getTime() / 1000l),
                rs.getTimestamp(LAST_CLICKED) == null ? null : (int) (rs.getTimestamp(LAST_CLICKED).getTime() / 1000l),
                rs.getString(EXTERNAL_UNIQUE_ID), metaData, rs.getInt(CREDITS_USED), creditTransactionId,  rs.getInt(REFUNDED_CREDITS),studentDetailedRow);

    }
}
