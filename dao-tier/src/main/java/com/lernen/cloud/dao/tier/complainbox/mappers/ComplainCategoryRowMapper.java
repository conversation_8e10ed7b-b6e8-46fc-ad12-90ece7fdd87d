package com.lernen.cloud.dao.tier.complainbox.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.complainbox.ComplainCategoryPayload;

public class ComplainCategoryRowMapper implements RowMapper<ComplainCategoryPayload> {
    private static final String INSTITUTE_ID = "complain_category.institute_id";
    private static final String CATEGORY_ID = "complain_category.complain_category_id";
    private static final String CATEGORY_NAME = "complain_category.complaint_category_name";

    @Override
    public ComplainCategoryPayload mapRow(ResultSet rs, int rowNum) throws SQLException {
        if(rs.getInt(CATEGORY_ID) <= 0) {
            return null;
        }
        return new ComplainCategoryPayload(rs.getInt(INSTITUTE_ID), rs.getInt(CATEGORY_ID), rs.getString(CATEGORY_NAME));
    }
}
