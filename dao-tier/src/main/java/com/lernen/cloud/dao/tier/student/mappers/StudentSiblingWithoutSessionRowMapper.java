package com.lernen.cloud.dao.tier.student.mappers;

import com.lernen.cloud.core.api.student.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

public class StudentSiblingWithoutSessionRowMapper implements RowMapper<StudentSiblingDetailsRow> {

    private static final StudentWithoutSessionRowMapper STUDENT_WITHOUT_SESSION_ROW_MAPPER = new StudentWithoutSessionRowMapper();
    private static final String SIBLING_GROUP_ID = "students.sibling_group_id";
    protected static final String INSTITUTE_ID = "students.institute_id";

    @Override
    public StudentSiblingDetailsRow mapRow(ResultSet rs, int rowNum) throws SQLException {
        int instituteId = rs.getInt(INSTITUTE_ID);
        UUID siblingGroupId = rs.getString(SIBLING_GROUP_ID) == null ? null :
                UUID.fromString(rs.getString(SIBLING_GROUP_ID));
        if(siblingGroupId == null) {
            return null;
        }
        final StudentDetailedRow studentDetailedRow = STUDENT_WITHOUT_SESSION_ROW_MAPPER.mapRow(rs, rowNum);
        final Student student = StudentRowMapper.getStudent(studentDetailedRow);
        return new StudentSiblingDetailsRow(instituteId, siblingGroupId, Student.getStudentLite(student));
    }

    /**
     *
     * @param studentSiblingDetailsRowList
     * @return studentSiblingDetailsList
     * Contains list of all the students which belong to a sibling group
     */
    public static List<StudentSiblingDetails> getStudentSiblingDetailsList(List<StudentSiblingDetailsRow> studentSiblingDetailsRowList) {
        if(CollectionUtils.isEmpty(studentSiblingDetailsRowList)) {
            return null;
        }
        Map<UUID, StudentSiblingDetails> studentSiblingDetailsMap = new HashMap<UUID, StudentSiblingDetails>();
        for(StudentSiblingDetailsRow studentSiblingDetailsRow : studentSiblingDetailsRowList) {
            UUID siblingGroupId = studentSiblingDetailsRow.getSiblingGroupId();
            if(studentSiblingDetailsMap.containsKey(siblingGroupId)) {
                studentSiblingDetailsMap.get(siblingGroupId).getStudentList().add(studentSiblingDetailsRow.getStudent());
            } else {
                List<StudentLite> studentList = new ArrayList<StudentLite>();
                studentList.add(studentSiblingDetailsRow.getStudent());
                StudentSiblingDetails studentSiblingDetails = new StudentSiblingDetails(
                        studentSiblingDetailsRow.getInstituteId(), studentSiblingDetailsRow.getSiblingGroupId(),
                        studentList);
                studentSiblingDetailsMap.put(siblingGroupId, studentSiblingDetails);
            }
        }
        return new ArrayList<>(studentSiblingDetailsMap.values());
    }

    /**
     *
     * @param studentSiblingDetailsRowList
     * @return studentSiblingDetails
     * Contains list of students which belong to a single sibling group id
     */
    public static StudentSiblingDetails getStudentSiblingDetails(List<StudentSiblingDetailsRow> studentSiblingDetailsRowList) {
        if(CollectionUtils.isEmpty(studentSiblingDetailsRowList)) {
            return null;
        }
        Map<UUID, StudentLite> studentMap = new HashMap<>();
        for(StudentSiblingDetailsRow studentSiblingDetailsRow : studentSiblingDetailsRowList) {
            if(!studentMap.containsKey(studentSiblingDetailsRow.getStudent().getStudentId())) {
                studentMap.put(studentSiblingDetailsRow.getStudent().getStudentId(), studentSiblingDetailsRow.getStudent());
            }
        }
        return new StudentSiblingDetails(studentSiblingDetailsRowList.get(0).getInstituteId(),
                studentSiblingDetailsRowList.get(0).getSiblingGroupId(), new ArrayList<>(studentMap.values()));
    }
}
