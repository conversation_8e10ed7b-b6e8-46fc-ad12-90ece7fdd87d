package com.lernen.cloud.dao.tier.inventory;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import com.embrate.cloud.core.api.inventory.v2.brand.InventoryBrand;
import com.embrate.cloud.dao.tier.inventory.v2.mappers.InventoryBrandRowMapper;
import com.lernen.cloud.dao.tier.inventory.mappers.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.inventory.BrandDetails;
import com.lernen.cloud.core.api.inventory.BrandInfo;
import com.lernen.cloud.core.api.inventory.Category;
import com.lernen.cloud.core.api.inventory.Color;
import com.lernen.cloud.core.api.inventory.Vendor;
import com.lernen.cloud.core.utils.crypto.CryptoUtils;

/**
 *
 * <AUTHOR>
 *
 */
public class StoreInventoryDao {

	private static final Logger logger = LogManager.getLogger(StoreInventoryDao.class);

	private final JdbcTemplate jdbcTemplate;
	private final TransactionTemplate transactionTemplate;

	public StoreInventoryDao(JdbcTemplate jdbcTemplate, TransactionTemplate transactionTemplate) {
		this.jdbcTemplate = jdbcTemplate;
		this.transactionTemplate = transactionTemplate;
	}

	private static final VendorRowMapper VENDOR_ROW_MAPPER = new VendorRowMapper();

	private static final CategoryRowMapper CATEGORY_ROW_MAPPER = new CategoryRowMapper();

	private static final InventoryBrandRowMapper BRAND_METADATA_ROW_MAPPER = new InventoryBrandRowMapper();

	private static final BrandRowMapper BRAND_ROW_MAPPER = new BrandRowMapper();

	private static final BrandCategoryRowMapper BRAND_CATEGORY_ROW_MAPPER = new BrandCategoryRowMapper();

	private static final ColorRowMapper COLOR_ROW_MAPPER = new ColorRowMapper();

	// Vendor Queries

	private static final String ADD_VENDOR = "insert into vendors"
			+ "(institute_id, vendor_name, contact_name, address, city, state, zipcode, country, email, primary_phone_number, secondary_phone_number, landline )"
			+ " values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

	private static final String UPDATE_VENDOR = "update vendors set institute_id = ?, vendor_name = ?, contact_name = ?, address = ?, city = ?, "
			+ "state = ?, zipcode = ?, country = ?, email = ?, primary_phone_number = ?, secondary_phone_number = ?, landline = ? where vendor_id = ?";

	private static final String GET_VENDOR_BY_VENDOR_ID = "select * from vendors where vendor_id = ?";

	private static final String GET_ALL_VENDORS_FOR_INSTITUTE = "select * from vendors where institute_id = ? order by updated desc";

	private static final String GET_ALL_VENDORS = "select * from vendors order by updated desc";

	private static final String DELETE_VENDOR_FROM_VENDOR_ID = "delete from vendors where vendor_id = ?";

	private static final String SEARCH_VENDOR = "select * from vendors ";
	private static final String NAME_SEARCH_CONDITION = " vendor_name like ? ";
	private static final String AND = " and ";

	// Category Queries

	private static final String ADD_CATEGORY = "insert into categories"
			+ "(institute_id, category_name, user_groups, colors, sizes, genders, description)"
			+ " values (?, ?, ?, ?, ?, ?, ?)";

	private static final String UPDATE_CATEGORY = "update categories set "
			+ "category_name = ?, suser_groups = ?, colors = ?, sizes = ?, genders = ?, description = ? where institute_id = ? and category_id = ?";

	private static final String GET_ALL_CATEGORY_FOR_INSTITUTE = "select * from categories where institute_id = ? order by updated desc";

	private static final String GET_ALL_CATEGORY = "select * from categories order by updated desc";

	private static final String DELETE_CATEGORY_FROM_CATEGORY_ID = "delete from categories where institute_id = ? and category_id = ?";

	// Brand Queries
	private static final String UPDATE_BRAND_NAME = "update brands set brand_name = ?, updated = ? where institute_id = ? and brand_id = ?";

	private static final String ADD_BRAND_INFO = "insert into brands (institute_id, brand_id, brand_name) values (?, ?, ?)";

	private static final String ADD_BRAND_CATEGORIES_MAPPING = "insert into brand_category_mapping (brand_id, category_id) values (?, ?)";

	private static final String GET_ALL_BRANDS_FOR_INSTITUTE = "select brands.*, brand_category_mapping.*, categories.* from brands "
			+ "join brand_category_mapping on brands.brand_id = brand_category_mapping.brand_id join categories on "
			+ "brand_category_mapping.category_id = categories.category_id where brands.institute_id = ? and brand_name like ? order by brands.updated desc";

	private static final String GET_BRAND_METADATA_FOR_INSTITUTE = "select * from brands where institute_id = ? and brand_id = ?";

	private static final String GET_INSTITUTE_BRANDS = "select * from brands where institute_id = ?";

	private static final String GET_BRAND_FOR_INSTITUTE = "select brands.*, brand_category_mapping.* from brands join "
			+ "brand_category_mapping on brands.brand_id = brand_category_mapping.brand_id  where institute_id = ? and brands.brand_id = ? "
			+ "order by brands.updated desc";

	private static final String GET_BRAND_WITH_CATEGORY_FOR_INSTITUTE = "select brands.*, brand_category_mapping.*, categories.* from brands "
			+ "join brand_category_mapping on brands.brand_id = brand_category_mapping.brand_id join categories on "
			+ "brand_category_mapping.category_id = categories.category_id where brands.institute_id = ? and brands.brand_id = ? order by brands.updated desc";

	private static final String DELETE_BRAND = "delete from brands where institute_id = ? and brand_id = ?";

	private static final String DELETE_BRAND_CATEGORY = "delete from brand_category_mapping where brand_id = ? and category_id = ?";

	private static final String DELETE_ALL_CATEGORY_OF_BRAND = "delete from brand_category_mapping where brand_id = ?";

	private static final String GET_ALL_COLORS = "select * from colors where institute_id = ?";

	public boolean addVendor(Vendor vendor) {

		if (inValidVendor(vendor)) {
			System.out.println("Invalid vendor");
			return false;
		}

		int rows = 0;
		try {
			rows = jdbcTemplate.update(ADD_VENDOR, vendor.getInstituteId(), CryptoUtils.encrypt(vendor.getVendorName()),
					CryptoUtils.encrypt(vendor.getContactName()), CryptoUtils.encrypt(vendor.getAddress()),
					CryptoUtils.encrypt(vendor.getCity()), vendor.getState(), vendor.getZipcode(), vendor.getCountry(),
					CryptoUtils.encrypt(vendor.getEmail()), CryptoUtils.encrypt(vendor.getPrimaryPhoneNumber()),
					CryptoUtils.encrypt(vendor.getSecondPhoneNumber()),
					CryptoUtils.encrypt(vendor.getLandlineNumber()));
		} catch (final Exception e) {
			System.out.println("Exception " + e.getMessage());
		}
		System.out.println("Rows " + rows);
		return rows == 1;
	}

	private boolean inValidVendor(Vendor vendor) {
		return (vendor == null) || (vendor.getInstituteId() <= 0) || StringUtils.isBlank(vendor.getVendorName());

	}

	public Vendor getVendor(Integer vendorId) {
		if (vendorId == null) {
			return null;
		}
		final Object[] args = { vendorId };
		try {
			return jdbcTemplate.queryForObject(GET_VENDOR_BY_VENDOR_ID, args, VENDOR_ROW_MAPPER);
		} catch (final DataAccessException dataAccessException) {
			System.out.println(dataAccessException);
			dataAccessException.printStackTrace();
		} catch (final Exception e) {
			System.out.println("Exception" + e.getMessage());
		}

		return null;
	}

	public List<Vendor> getAllVendors() {
		try {
			return jdbcTemplate.query(GET_ALL_VENDORS, VENDOR_ROW_MAPPER);
		} catch (final DataAccessException dataAccessException) {
			System.out.println("No vendors found for ");
		} catch (final Exception e) {
			System.out.println("Exception" + e.getMessage());
		}

		return null;
	}

	public List<Vendor> getAllVendors(Integer instituteId) {
		if (instituteId <= 0) {
			return new ArrayList<>();
		}
		final Object[] args = { instituteId };
		try {
			return jdbcTemplate.query(GET_ALL_VENDORS_FOR_INSTITUTE, args, VENDOR_ROW_MAPPER);
		} catch (final DataAccessException dataAccessException) {
			System.out.println("No vendors found for " + instituteId);
		} catch (final Exception e) {
			System.out.println("Exception" + e.getMessage());
		}

		return new ArrayList<>();
	}

	@Deprecated
	// Since fields are encrypted partial search does not work
	public List<Vendor> searchVendor(Integer instituteId, String searchText) {
		if (instituteId <= 0) {
			return null;
		}
		try {
			final StringBuilder query = new StringBuilder();
			query.append(SEARCH_VENDOR);
			final List<Object> args = new ArrayList<>();
			if (StringUtils.isBlank(searchText)) {
				query.append("where institute_id = ? order by updated desc");
				args.add(instituteId);
			} else {
				final String[] keywords = searchText.split(" ");
				boolean first = true;
				for (String keyword : keywords) {
					keyword = keyword.trim();
					if (StringUtils.isBlank(keyword)) {
						continue;
					}
					if (first) {
						query.append("where " + NAME_SEARCH_CONDITION);
						first = false;
					} else {
						query.append(AND).append(NAME_SEARCH_CONDITION);
					}
					args.add("%" + keyword + "%");
				}
				query.append(AND).append(" institute_id = ? order by updated desc");
				args.add(instituteId);
			}
			return jdbcTemplate.query(query.toString(), args.toArray(), VENDOR_ROW_MAPPER);
		} catch (final DataAccessException e) {
			System.out.println("error while fetching results from vendor table");
			e.printStackTrace();
		}
		return null;
	}

	public boolean deleteVendor(Integer vendorId) {
		if (vendorId <= 0) {
			return false;
		}
		final Object[] args = { vendorId };
		try {
			return jdbcTemplate.update(DELETE_VENDOR_FROM_VENDOR_ID, args) == 1;
		} catch (final DataAccessException dataAccessException) {
			System.out.println("Could not delete vendor with id : " + vendorId);
		} catch (final Exception e) {
			System.out.println("Exception" + e.getMessage());
		}

		return false;
	}

	public boolean updateVendor(Vendor vendor) {
		if (inValidVendor(vendor)) {
			System.out.println("Invalid vendor");
			return false;
		}

		int rows = 0;
		try {
			rows = jdbcTemplate.update(UPDATE_VENDOR, vendor.getInstituteId(),
					CryptoUtils.encrypt(vendor.getVendorName()), CryptoUtils.encrypt(vendor.getContactName()),
					CryptoUtils.encrypt(vendor.getAddress()), CryptoUtils.encrypt(vendor.getCity()), vendor.getState(),
					vendor.getZipcode(), vendor.getCountry(), CryptoUtils.encrypt(vendor.getEmail()),
					CryptoUtils.encrypt(vendor.getPrimaryPhoneNumber()),
					CryptoUtils.encrypt(vendor.getSecondPhoneNumber()), CryptoUtils.encrypt(vendor.getLandlineNumber()),
					vendor.getVendorId());
		} catch (final Exception e) {
			System.out.println("Exception " + e.getMessage());
		}
		System.out.println("Rows " + rows);
		return rows == 1;
	}

	public boolean addCategory(Category category) {

		if (inValidCategory(category)) {
			System.out.println("Invalid category");
			return false;
		}

		int rows = 0;
		try {
			rows = jdbcTemplate.update(ADD_CATEGORY, category.getInstituteId(),
					category.getCategoryName().toLowerCase(), category.isUserGroups(), category.isColors(),
					category.isSizes(), category.isGenders(), category.getDescription());
		} catch (final Exception e) {
			System.out.println("Exception " + e.getMessage());
		}
		System.out.println("Rows " + rows);
		return rows == 1;
	}

	private boolean inValidCategory(Category category) {
		return (category == null) || (category.getInstituteId() <= 0)
				|| StringUtils.isBlank(category.getCategoryName());
	}

	public Boolean updateCategory(Category category) {
		if (inValidCategory(category) || (category.getCategoryId() <= 0)) {
			System.out.println("Invalid category");
			return null;
		}

		int rows = 0;
		try {
			rows = jdbcTemplate.update(UPDATE_CATEGORY, category.getCategoryName().toLowerCase(),
					category.isUserGroups(), category.isColors(), category.isSizes(), category.isGenders(),
					category.getDescription(), category.getInstituteId(), category.getCategoryId());
		} catch (final Exception e) {
			System.out.println("Exception " + e.getMessage());
		}
		System.out.println("Rows " + rows);
		return rows == 1;
	}

	public List<Category> getAllCategory() {
		try {
			return jdbcTemplate.query(GET_ALL_CATEGORY, CATEGORY_ROW_MAPPER);
		} catch (final DataAccessException dataAccessException) {
			System.out.println("No category found  ");
		} catch (final Exception e) {
			System.out.println("Exception" + e.getMessage());
		}

		return new ArrayList<>();
	}

	public List<Category> getAllCategory(Integer instituteId) {
		if (instituteId <= 0) {
			return null;

		}
		final Object[] args = { instituteId };
		try {
			return jdbcTemplate.query(GET_ALL_CATEGORY_FOR_INSTITUTE, args, CATEGORY_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Error while getting categories for institute {}", instituteId);
		}
		return null;
	}

	public Boolean deleteCategory(int instituteId, int categoryId) {
		if ((instituteId <= 0) || (categoryId <= 0)) {
			return Boolean.FALSE;
		}
		final Object[] args = { instituteId, categoryId };
		try {
			return jdbcTemplate.update(DELETE_CATEGORY_FROM_CATEGORY_ID, args) == 1;
		} catch (final DataAccessException dataAccessException) {
			System.out.println("Could not delete category with id : " + categoryId);
		} catch (final Exception e) {
			System.out.println("Exception" + e.getMessage());
		}

		return Boolean.FALSE;
	}

	private boolean updateBrandName(BrandInfo brandInfo) {
		try {
			return jdbcTemplate.update(UPDATE_BRAND_NAME, brandInfo.getBrandName(),
					new Timestamp(System.currentTimeMillis()), brandInfo.getInstituteId(),
					brandInfo.getBrandId().toString()) > 0;
		} catch (final DataAccessException dataAccessException) {
			System.out.println("Could not update brand with id : " + brandInfo.getBrandId());
		} catch (final Exception e) {
			System.out.println("Exception" + e.getMessage());
		}
		return false;
	}

	public UUID addBrand(final BrandInfo brandInfo) {
		if (inValidBrand(brandInfo)) {
			System.out.println("Invalid brand");
			return null;
		}
		try {
			final UUID brandId = transactionTemplate.execute(new TransactionCallback<UUID>() {
				@Override
				public UUID doInTransaction(TransactionStatus status) {
					final UUID brandId = UUID.randomUUID();

					final int row = jdbcTemplate.update(ADD_BRAND_INFO, brandInfo.getInstituteId(), brandId.toString(),
							brandInfo.getBrandName());
					if (row != 1) {
						throw new RuntimeException("Unable to add brand.");
					}

					if (addBrandCategoriesMapping(brandId, brandInfo.getCategoryIds())) {
						return brandId;
					}
					throw new RuntimeException("Unable to add brand categories.");
				}
			});
			return brandId;
		} catch (final Exception e) {
			System.out.println("Unable to add brands");
			e.printStackTrace();
		}
		return null;
	}

	private boolean addBrandCategoriesMapping(UUID brandId, Set<Integer> categoryIds) {
		final List<Object[]> argsList = new ArrayList<>();
		for (final Integer categoryId : categoryIds) {
			final List<Object> args = new ArrayList<>();
			args.add(brandId.toString());
			args.add(categoryId);
			argsList.add(args.toArray());
		}
		final int[] rows = jdbcTemplate.batchUpdate(ADD_BRAND_CATEGORIES_MAPPING, argsList);
		if (rows.length != categoryIds.size()) {
			throw new RuntimeException("Unable to add brand category mappings.");
		}
		for (final int rowCount : rows) {
			if (rowCount != 1) {
				throw new RuntimeException("Unable to add brand category mapping.");
			}
		}
		return true;
	}

	private boolean inValidBrand(BrandInfo brandPayload) {
		return (brandPayload == null) || (brandPayload.getInstituteId() <= 0)
				|| StringUtils.isBlank(brandPayload.getBrandName())
				|| CollectionUtils.isEmpty(brandPayload.getCategoryIds());
	}

	public boolean updateBrand(final BrandInfo brandInfo) {
		if (inValidBrand(brandInfo) || (brandInfo.getBrandId() == null)) {
			System.out.println("Invalid brand");
			return false;
		}

		try {
			final Boolean success = transactionTemplate.execute(new TransactionCallback<Boolean>() {
				@Override
				public Boolean doInTransaction(TransactionStatus status) {
					final BrandInfo existingBrand = getBrand(brandInfo.getInstituteId(), brandInfo.getBrandId());
					if (existingBrand == null) {
						throw new RuntimeException("Unable to update brands as no brand exist with given name.");
					}
					final Set<Integer> categoriesToRemove = new HashSet<>(existingBrand.getCategoryIds());
					categoriesToRemove.removeAll(brandInfo.getCategoryIds());

					final Set<Integer> newCategories = new HashSet<>(brandInfo.getCategoryIds());
					newCategories.removeAll(existingBrand.getCategoryIds());

					if (!updateBrandName(brandInfo)) {
						throw new RuntimeException("Unable to update name new brand ");
					}

					if (!CollectionUtils.isEmpty(categoriesToRemove)
							&& !deleteBrandCategory(brandInfo.getBrandId(), categoriesToRemove)) {
						throw new RuntimeException("Unable to remove brands categories " + categoriesToRemove);
					}
					if (!CollectionUtils.isEmpty(newCategories)
							&& (!addBrandCategoriesMapping(brandInfo.getBrandId(), newCategories))) {
						throw new RuntimeException("Unable to add new brands categories " + categoriesToRemove);
					}
					return true;
				}
			});
			return success;
		} catch (final Exception e) {
			System.out.println("Unable to update brands");
			e.printStackTrace();
		}
		return false;
	}

	// public List<Brand> getAllBrands() {
	// try {
	// return jdbcTemplate.query(GET_ALL_BRANDS, BRAND_ROW_MAPPER);
	// } catch (DataAccessException dataAccessException) {
	// System.out.println("No brand found for ");
	// } catch (Exception e) {
	// System.out.println("Exception" + e.getMessage());
	// }
	//
	// return null;
	// }

	public List<BrandDetails> getAllBrands(Integer instituteId, String searchText) {
		if (instituteId <= 0) {
			return new ArrayList<>();
		}
		searchText = searchText == null ? searchText = "" : searchText;
		final Object[] args = { instituteId, "%" + searchText + "%" };
		try {
			return BrandCategoryRowMapper
					.getBrandDetails(jdbcTemplate.query(GET_ALL_BRANDS_FOR_INSTITUTE, args, BRAND_CATEGORY_ROW_MAPPER));
		} catch (final DataAccessException dataAccessException) {
			System.out.println("No brand found for " + instituteId);
			dataAccessException.printStackTrace();
		} catch (final Exception e) {
			System.out.println("Exception " + e.getMessage());
			e.printStackTrace();
		}
		return new ArrayList<>();
	}

	public BrandInfo getBrand(Integer instituteId, UUID brandId) {
		if ((instituteId <= 0) || (brandId == null)) {
			return null;
		}

		final Object[] args = { instituteId, brandId.toString() };
		try {
			final List<BrandInfo> brands = BrandRowMapper
					.getBrandDetails(jdbcTemplate.query(GET_BRAND_FOR_INSTITUTE, args, BRAND_ROW_MAPPER));
			if (CollectionUtils.isEmpty(brands)) {
				return null;
			}
			return brands.get(0);
		} catch (final DataAccessException dataAccessException) {
			System.out.println("No brand found for " + instituteId);
		} catch (final Exception e) {
			System.out.println("Exception " + e.getMessage());
			e.printStackTrace();
		}
		return null;
	}

	public InventoryBrand getBrandMetadata(Integer instituteId, UUID brandId) {
		if ((instituteId <= 0) || (brandId == null)) {
			return null;
		}

		final Object[] args = { instituteId, brandId.toString() };
		try {
			return jdbcTemplate.queryForObject(GET_BRAND_METADATA_FOR_INSTITUTE, args, BRAND_METADATA_ROW_MAPPER);
		} catch (final EmptyResultDataAccessException dataAccessException) {
			return null;
		} catch (final Exception e) {
			logger.error("Error fetching brand for instituteId {}, brandId {}", instituteId, brandId, e);
		}
		return null;
	}

	public List<InventoryBrand> getInstituteBrands(Integer instituteId) {
		if (instituteId <= 0) {
			return null;
		}

		final Object[] args = { instituteId};
		try {
			return jdbcTemplate.query(GET_INSTITUTE_BRANDS, args, BRAND_METADATA_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Error fetching brands for instituteId {}", instituteId, e);
		}
		return null;
	}



	public BrandDetails getBrandWithCategories(Integer instituteId, UUID brandId) {
		if ((instituteId <= 0) || (brandId == null)) {
			return null;
		}

		final Object[] args = { instituteId, brandId.toString() };
		try {
			final List<BrandDetails> brandDetails = BrandCategoryRowMapper.getBrandDetails(
					jdbcTemplate.query(GET_BRAND_WITH_CATEGORY_FOR_INSTITUTE, args, BRAND_CATEGORY_ROW_MAPPER));
			if (CollectionUtils.isEmpty(brandDetails)) {
				return null;
			}
			return brandDetails.get(0);
		} catch (final DataAccessException dataAccessException) {
			System.out.println("No brand found for " + instituteId);
		} catch (final Exception e) {
			System.out.println("Exception " + e.getMessage());
			e.printStackTrace();
		}
		return null;
	}

	public boolean deleteBrand(Integer instituteId, UUID brandId) {
		if ((instituteId <= 0) || (brandId == null)) {
			return false;
		}
		try {
			System.out.println(DELETE_BRAND + " " + instituteId + " " + brandId);
			final Boolean success = transactionTemplate.execute(new TransactionCallback<Boolean>() {
				@Override
				public Boolean doInTransaction(TransactionStatus status) {
					if (!deleteBrandCategory(brandId)) {
						throw new RuntimeException("Unable to delete brand categories with id." + brandId.toString());
					}
					final int row = jdbcTemplate.update(DELETE_BRAND, instituteId, brandId.toString());
					if (row != 1) {
						throw new RuntimeException("Unable to delete brands with id." + brandId.toString());
					}
					return true;
				}
			});
			return success;
		} catch (final Exception e) {
			System.out.println("Unable to update brands");
			e.printStackTrace();
		}
		return false;
	}

	public boolean deleteBrandCategory(UUID brandId, Set<Integer> categories) {
		if ((brandId == null) || (categories == null)) {
			return false;
		}
		try {
			final List<Object[]> argsList = new ArrayList<>();
			for (final Integer categoryId : categories) {
				final List<Object> args = new ArrayList<>();
				args.add(brandId.toString());
				args.add(categoryId);
				argsList.add(args.toArray());
			}
			return jdbcTemplate.batchUpdate(DELETE_BRAND_CATEGORY, argsList).length == categories.size();

		} catch (final DataAccessException dataAccessException) {
			System.out.println("Could not delete brand with brand  : " + brandId.toString());
			dataAccessException.printStackTrace();
		} catch (final Exception e) {
			System.out.println("Exception" + e.getMessage());
		}

		return false;
	}

	public boolean deleteBrandCategory(UUID brandId) {
		if (brandId == null) {
			return false;
		}
		try {
			return jdbcTemplate.update(DELETE_ALL_CATEGORY_OF_BRAND, brandId.toString()) > 0;

		} catch (final DataAccessException dataAccessException) {
			System.out.println("Could not delete brand with brand  : " + brandId.toString());
			dataAccessException.printStackTrace();
		} catch (final Exception e) {
			System.out.println("Exception" + e.getMessage());
		}

		return false;
	}

	public List<Color> getColors(Integer instituteId) {
		if (instituteId <= 0) {
			return new ArrayList<>();

		}
		final Object[] args = { instituteId };
		try {
			return jdbcTemplate.query(GET_ALL_COLORS, args, COLOR_ROW_MAPPER);
		} catch (final DataAccessException dataAccessException) {
			System.out.println("No color found for " + instituteId);
			dataAccessException.printStackTrace();
		} catch (final Exception e) {
			System.out.println("Exception" + e.getMessage());
			e.printStackTrace();
		}
		return new ArrayList<>();
	}
}
