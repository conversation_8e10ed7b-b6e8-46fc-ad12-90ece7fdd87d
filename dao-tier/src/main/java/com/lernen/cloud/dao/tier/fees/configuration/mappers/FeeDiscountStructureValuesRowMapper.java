/**
 *
 */
package com.lernen.cloud.dao.tier.fees.configuration.mappers;

import com.embrate.cloud.core.api.fee.discount.structure.FeeDiscountStructureData;
import com.lernen.cloud.core.api.fees.FeeEntity;
import com.lernen.cloud.core.api.fees.FeeHeadAmount;
import com.lernen.cloud.core.api.fees.FeeIdFeeHead;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.util.CollectionUtils;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.Map.Entry;

/**
 * <AUTHOR>
 *
 */
public class FeeDiscountStructureValuesRowMapper implements RowMapper<FeeDiscountStructureData> {

    private static final String STRUCTURE_ID = "fee_discount_structure_metadata.structure_id";
    private static final String ENTITY_ID = "entity_id";
    private static final String ENTITY_NAME = "entity_name";
    private static final String FEE_ID = "fee_discount_structure.fee_id";
    private static final String FEE_HEAD_ID = "fee_discount_structure.fee_head_id";
    private static final String AMOUNT = "fee_discount_structure.amount";
    private static final String IS_PERCENTAGE = "fee_discount_structure.is_percent";


    @Override
    public FeeDiscountStructureData mapRow(ResultSet rs, int rowNum) throws SQLException {
        UUID structureId = UUID.fromString(rs.getString(STRUCTURE_ID));
        UUID feeId = UUID.fromString(rs.getString(FEE_ID));
        int feeHeadId = rs.getInt(FEE_HEAD_ID);
        boolean isPercentage = rs.getBoolean(IS_PERCENTAGE);
        double amount = rs.getDouble(AMOUNT);
        return new FeeDiscountStructureData(structureId, Arrays.asList(new FeeIdFeeHead(feeId, Arrays.asList(new FeeHeadAmount(feeHeadId, amount, isPercentage, FeeEntity.INSTITUTE)))));

    }

    // Assumes single structure id
    public static FeeDiscountStructureData getFeeDiscountStructureData(
            List<FeeDiscountStructureData> rows) {

        if (CollectionUtils.isEmpty(rows)) {
            return null;
        }

        UUID structureId = rows.get(0).getStructureId();
        Map<UUID, List<FeeHeadAmount>> feeIdMap = new HashMap<>();
        for (FeeDiscountStructureData row : rows) {
            // Only single row is expected
            UUID feeId = row.getFeeIdFeeHeadList().get(0).getFeeId();
            if (!feeIdMap.containsKey(feeId)) {
                feeIdMap.put(feeId, new ArrayList<>());
            }
            feeIdMap.get(feeId).addAll(row.getFeeIdFeeHeadList().get(0).getFeeHeadAmountList());
        }

        List<FeeIdFeeHead> feeIdFeeHeadList = new ArrayList<>();
        for (Entry<UUID, List<FeeHeadAmount>> entry : feeIdMap.entrySet()) {
            feeIdFeeHeadList.add(new FeeIdFeeHead(entry.getKey(), entry.getValue()));
        }
        return new FeeDiscountStructureData(structureId, feeIdFeeHeadList);
    }

}
