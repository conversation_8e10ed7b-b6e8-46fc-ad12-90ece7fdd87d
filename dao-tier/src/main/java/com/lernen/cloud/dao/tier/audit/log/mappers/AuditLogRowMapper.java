package com.lernen.cloud.dao.tier.audit.log.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.UUID;

import org.springframework.jdbc.core.RowMapper;

import com.google.gson.Gson;
import com.lernen.cloud.core.api.audit.log.AuditLog;
import com.lernen.cloud.core.api.audit.log.AuditLogActionData;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.utils.SharedConstants;
import com.lernen.cloud.dao.tier.user.mappers.UserRowMapper;

/**
 *
 * <AUTHOR>
 *
 */
public class AuditLogRowMapper implements RowMapper<AuditLog> {

	private static final Gson GSON = SharedConstants.GSON;

	private static final String INSTITUTE_ID = "institute_id";
	private static final String LOG_ID = "log_id";
	private static final String USER_ID = "user_id";
	private static final String LOG_TIME = "log_timestamp";
	private static final String ACTION_DATA = "action_data";
	private static final String PREV_STATE = "previous_state";
	private static final String FINAL_STATE = "final_state";
	private static final String MINI_LOG = "mini_log_statement";
	private static final String DETAILED_LOG = "detailed_log_statement";

	private static final UserRowMapper USER_ROW_MAPPER = new UserRowMapper();

	@Override
	public AuditLog mapRow(ResultSet rs, int rowNum) throws SQLException {

		final User user = USER_ROW_MAPPER.mapRow(rs, rowNum);
		final UUID logId = UUID.fromString(rs.getString(LOG_ID));
		final Integer logTime = (int) (rs.getTimestamp(LOG_TIME).getTime() / 1000l);
		final AuditLog aduitLogPayload = new AuditLog(rs.getInt(INSTITUTE_ID), logId, user,
				GSON.fromJson(rs.getString(ACTION_DATA), AuditLogActionData.class), rs.getString(MINI_LOG),
				rs.getString(DETAILED_LOG), logTime);

		return aduitLogPayload;
	}

}
