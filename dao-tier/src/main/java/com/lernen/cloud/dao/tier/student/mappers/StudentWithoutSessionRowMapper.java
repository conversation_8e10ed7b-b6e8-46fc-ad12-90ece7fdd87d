package com.lernen.cloud.dao.tier.student.mappers;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lernen.cloud.core.api.student.*;
import com.lernen.cloud.core.api.user.BloodGroup;
import com.lernen.cloud.core.api.user.Document;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import java.lang.reflect.Type;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public class StudentWithoutSessionRowMapper implements RowMapper<StudentDetailedRow> {

	private static final StudentBasicInfoRowMapper STUDENT_BASIC_INFO_ROW_MAPPER = new StudentBasicInfoRowMapper();
	private static final Gson GSON = new Gson();

	@Override
	public StudentDetailedRow mapRow(ResultSet rs, int rowNum) throws SQLException {
		if(rs.getString(StudentRowMapper.STUDENT_ID) == null){
			return null;
		}
		final StudentBasicInfo studentBasicInfo = STUDENT_BASIC_INFO_ROW_MAPPER.mapRow(rs, rowNum);

		final Timestamp dateOfPhysicalExamination = rs.getTimestamp(StudentRowMapper.DATE_OF_PHYSICAL_EXAMINATION);
		final Integer dateOfPhysicalExaminationTime = dateOfPhysicalExamination == null ? null
				: (int) (dateOfPhysicalExamination.getTime() / 1000l);

		final String documents = rs.getString(StudentRowMapper.STUDENT_DOCUMENTS);
		List<Document<StudentDocumentType>> studentDocuments = null;
		if (!StringUtils.isBlank(documents)) {
			final Type collectionType = new TypeToken<List<Document<StudentDocumentType>>>() {
			}.getType();
			studentDocuments = GSON.fromJson(documents, collectionType);
		}

		final String tcVariablesStr = rs.getString(StudentRowMapper.TC_VARIABLES);
		Map<TransferCertificateVariables, String> tcVariables = null;
		if (!StringUtils.isBlank(tcVariablesStr)) {
			final Type collectionType = new TypeToken<Map<TransferCertificateVariables, String>>() {
			}.getType();
			tcVariables = GSON.fromJson(tcVariablesStr, collectionType);
		}

		boolean isAdmissionTcBased = rs.getBoolean(StudentRowMapper.IS_ADMISSION_TC_BASED);
		if (rs.wasNull()) {
			isAdmissionTcBased = false;
		}

		String previousSchoolTcNumber = rs.getString(StudentRowMapper.PREVIOUS_SCHOOL_TC_NUMBER);

		final String tcDetailsStr = rs.getString(StudentRowMapper.TC_DETAILS);
		StudentTransferCertificateDetails studentTransferCertificateDetails = null;
		if (!StringUtils.isBlank(tcDetailsStr)) {
			studentTransferCertificateDetails = GSON.fromJson(tcDetailsStr, StudentTransferCertificateDetails.class);
		}

		final String tagDetailsStr = rs.getString(StudentRowMapper.TAG_DETAILS);
		List<StudentTaggedDetails> studentTaggedDetailsList = null;
		if (!StringUtils.isBlank(tagDetailsStr)) {
			studentTaggedDetailsList = GSON.fromJson(tagDetailsStr,
					new TypeToken<List<StudentTaggedDetails>>(){}.getType());
		}

		return new StudentDetailedRow(rs.getInt(StudentRowMapper.INSTITUTE_ID), UUID.fromString(rs.getString(StudentRowMapper.STUDENT_ID)), studentBasicInfo,
				rs.getString(StudentRowMapper.MOTHER_NAME), rs.getString(StudentRowMapper.FATHER_NAME),
				rs.getString(StudentRowMapper.MOTHER_QUALIFICATION), rs.getString(StudentRowMapper.FATHER_QUALIFICATION),
				rs.getString(StudentRowMapper.MOTHER_CONTACT_NUMBER),
				rs.getString(StudentRowMapper.FATHER_CONTACT_NUMBER), rs.getString(StudentRowMapper.MOTHER_OCCUPATION),
				rs.getString(StudentRowMapper.MOTHER_ANNUAL_INCOME), rs.getString(StudentRowMapper.FATHER_OCCUPATION),
				rs.getString(StudentRowMapper.FATHER_ANNUAL_INCOME), rs.getString(StudentRowMapper.MOTHER_AADHAR_NUMBER),
				rs.getString(StudentRowMapper.FATHER_AADHAR_NUMBER),rs.getString(StudentRowMapper.MOTHER_PAN_CARD_DETAILS),
				rs.getString(StudentRowMapper.FATHER_PAN_CARD_DETAILS),
				rs.getString(StudentRowMapper.APPROX_FAMILY_INCOME), rs.getString(StudentRowMapper.GUARDIANS_INFO_LIST),
				rs.getString(StudentRowMapper.SCHOOL_NAME), rs.getString(StudentRowMapper.CLASS_PASSED),
				rs.getString(StudentRowMapper.MEDIUM), rs.getString(StudentRowMapper.PERCENTAGE),
				rs.getString(StudentRowMapper.RESULT), rs.getInt(StudentRowMapper.YEAR_OF_PASSING),
				BloodGroup.getBloodGroup(rs.getString(StudentRowMapper.BLOOD_GROUP)),
				rs.getString(StudentRowMapper.BLOOD_PRESSURE), rs.getString(StudentRowMapper.PULSE),
				rs.getString(StudentRowMapper.HEIGHT), rs.getString(StudentRowMapper.WEIGHT),
				dateOfPhysicalExaminationTime, null, null,
				StudentStatus.getStudentStatus(rs.getString(StudentRowMapper.FINAL_STATUS)),
				rs.getInt(StudentRowMapper.ADMISSION_ACADEMIC_SESSION), studentDocuments, tcVariables,
				isAdmissionTcBased, previousSchoolTcNumber, rs.getString(StudentRowMapper.DEVICE_USER_ID), null,
				studentTransferCertificateDetails, studentTaggedDetailsList);
	}
}
