package com.lernen.cloud.dao.tier.student.mappers;

import com.embrate.cloud.core.api.dashboards.admission.StudentCountSummary;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Row mapper for StudentCountSummary
 * Maps database results that contain only institute_id and count columns
 * 
 * <AUTHOR>
 */
public class StudentCountSummaryRowMapper implements RowMapper<StudentCountSummary> {

    protected static final String INSTITUTE_ID = "institute_id";
    protected static final String STUDENT_COUNT = "COUNT(student_id)";

    @Override
    public StudentCountSummary mapRow(ResultSet rs, int rowNum) throws SQLException {
        int instituteId = rs.getInt(INSTITUTE_ID);
        long studentCount = rs.getLong(STUDENT_COUNT);

        return new StudentCountSummary(instituteId, studentCount);
    }
}
