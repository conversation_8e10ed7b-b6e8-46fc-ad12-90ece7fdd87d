package com.lernen.cloud.dao.tier.fees.configuration.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.fees.EntityFeeAssignment;
import com.lernen.cloud.core.api.fees.FeeAssignmentDetailsRow;
import com.lernen.cloud.core.api.fees.FeeConfigurationResponse;
import com.lernen.cloud.core.api.fees.FeeEntity;
import com.lernen.cloud.core.api.fees.FeeHeadAmountDetails;
import com.lernen.cloud.core.api.fees.FeeHeadConfiguration;
import com.lernen.cloud.core.api.fees.FeeIdFeeHeadDetails;
import com.lernen.cloud.core.api.user.Module;

public class FeeAssignmentRowMapper implements RowMapper<FeeAssignmentDetailsRow> {

	private static final String INSTITUTE_ID = "institute_id";
	private static final String ENTITY_ID = "entity_id";
	private static final String ENTITY_NAME = "entity_name";
	private static final String AMOUNT = "amount";
	private static final String MODULE_NAME = "module_name";

	private static final FeeHeadConfigurationRowMapper FEE_HEAD_CONFIGURATION_ROW_MAPPER = new FeeHeadConfigurationRowMapper();
	private static final FeeConfigurationRowMapper FEE_CONFIGURATION_ROW_MAPPER = new FeeConfigurationRowMapper();

	@Override
	public FeeAssignmentDetailsRow mapRow(ResultSet rs, int rowNum) throws SQLException {
		final FeeConfigurationResponse feeConfigurationResponse = FEE_CONFIGURATION_ROW_MAPPER.mapRow(rs, rowNum);
		final FeeHeadConfiguration feeHeadConfiguration = FEE_HEAD_CONFIGURATION_ROW_MAPPER.mapRow(rs, rowNum);

		final String moduleName = rs.getString(MODULE_NAME);
		final Module module = StringUtils.isBlank(moduleName) ? null : Module.valueOf(moduleName);

		return new FeeAssignmentDetailsRow(rs.getInt(INSTITUTE_ID), feeConfigurationResponse, rs.getString(ENTITY_ID),
				FeeEntity.valueOf(rs.getString(ENTITY_NAME)), feeHeadConfiguration, rs.getDouble(AMOUNT), module);
	}

	/**
	 * This method assumes that all elements passed in list belong to same entity id
	 *
	 * @return
	 */
	public static EntityFeeAssignment getFeeAssignmentForEntity(
			List<FeeAssignmentDetailsRow> feeAssignmentDetailsRows) {
		if (CollectionUtils.isEmpty(feeAssignmentDetailsRows)) {
			return null;
		}
		final FeeAssignmentDetailsRow firstRow = feeAssignmentDetailsRows.get(0);
		final List<FeeConfigurationResponse> feeConfigurationResponses = new ArrayList<>();
		for (final FeeAssignmentDetailsRow feeAssignmentDetailsRow : feeAssignmentDetailsRows) {
			feeConfigurationResponses.add(feeAssignmentDetailsRow.getFeeConfigurationResponse());
		}
		final Map<UUID, FeeConfigurationResponse> feeConfigurationResponseMap = getFeeConfigurationResponseMap(
				feeConfigurationResponses);

		final Map<UUID, Set<FeeHeadAmountDetails>> feeIdVsFeeHeadAmountSetMap = getFeeIdVsFeeHeadAmountSetMap(
				feeAssignmentDetailsRows);

		final List<FeeIdFeeHeadDetails> feeIdFeeHeadDetailsList = new ArrayList<>();
		for (final Entry<UUID, FeeConfigurationResponse> feeConfigurationEntry : feeConfigurationResponseMap
				.entrySet()) {
			final UUID feeId = feeConfigurationEntry.getKey();
			// This case should not occur
			if (!feeIdVsFeeHeadAmountSetMap.containsKey(feeId)) {
				continue;
			}
			final Set<FeeHeadAmountDetails> feeHeadAmountDetailsSet = feeIdVsFeeHeadAmountSetMap.get(feeId);
			feeIdFeeHeadDetailsList.add(new FeeIdFeeHeadDetails(feeConfigurationEntry.getValue(),
					new ArrayList<>(feeHeadAmountDetailsSet)));

		}

		return new EntityFeeAssignment(firstRow.getInstituteId(), firstRow.getEntityId(), firstRow.getFeeEntity(),
				feeIdFeeHeadDetailsList);
	}

	private static Map<UUID, FeeConfigurationResponse> getFeeConfigurationResponseMap(
			List<FeeConfigurationResponse> feeConfigurationResponses) {
		final Map<UUID, FeeConfigurationResponse> feeConfigurationResponseMap = new HashMap<>();
		for (final FeeConfigurationResponse feeConfigurationResponse : feeConfigurationResponses) {
			feeConfigurationResponseMap.put(feeConfigurationResponse.getFeeConfigurationBasicInfo().getFeeId(),
					feeConfigurationResponse);
		}
		return feeConfigurationResponseMap;
	}

	/**
	 * Fetches assignment for all sessions
	 *
	 * @param feeAssignmentDetailsRows
	 * @return
	 */
	public static Map<Integer, Map<String, EntityFeeAssignment>> getAllSessionFeeAssignments(
			List<FeeAssignmentDetailsRow> feeAssignmentDetailsRows) {
		if (CollectionUtils.isEmpty(feeAssignmentDetailsRows)) {
			return new HashMap<>();
		}
		final Map<Integer, Map<String, EntityFeeAssignment>> allSessionEntityFeeAssignmentMap = new HashMap<>();
		final Map<Integer, List<FeeAssignmentDetailsRow>> sessionRowsMap = new HashMap<>();

		for (final FeeAssignmentDetailsRow feeAssignmentDetailsRow : feeAssignmentDetailsRows) {
			final Integer academicSessionId = feeAssignmentDetailsRow.getFeeConfigurationResponse().getAcademicSession()
					.getAcademicSessionId();
			if (!sessionRowsMap.containsKey(academicSessionId)) {
				sessionRowsMap.put(academicSessionId, new ArrayList<>());
			}
			sessionRowsMap.get(academicSessionId).add(feeAssignmentDetailsRow);
		}

		for (final Entry<Integer, List<FeeAssignmentDetailsRow>> entry : sessionRowsMap.entrySet()) {
			allSessionEntityFeeAssignmentMap.put(entry.getKey(), getFeeAssignments(entry.getValue()));
		}
		return allSessionEntityFeeAssignmentMap;
	}

	public static Map<UUID, Map<String, EntityFeeAssignment>> getFeeAssignmentOfStandard(
			List<FeeAssignmentDetailsRow> feeAssignmentDetailsRows) {
		if (CollectionUtils.isEmpty(feeAssignmentDetailsRows)) {
			return new HashMap<>();
		}
		final Map<UUID, List<FeeAssignmentDetailsRow>> feeAssignmentDetailsRowListMap = new LinkedHashMap<>();
		List<FeeAssignmentDetailsRow> classLevelFees = new ArrayList<FeeAssignmentDetailsRow>();
		List<FeeAssignmentDetailsRow> instituteLevelFees = new ArrayList<FeeAssignmentDetailsRow>();
		for(FeeAssignmentDetailsRow feeAssignmentDetailsRow : feeAssignmentDetailsRows) {
			if(feeAssignmentDetailsRow.getFeeEntity() == FeeEntity.INSTITUTE) {
				instituteLevelFees.add(feeAssignmentDetailsRow);
				continue;
			}
			
			if(feeAssignmentDetailsRow.getFeeEntity() == FeeEntity.CLASS) {
				classLevelFees.add(feeAssignmentDetailsRow);
				continue;
			}
			
			/**
			 * assuming only student level assignment will come till here
			 */
			UUID studentId = UUID.fromString(feeAssignmentDetailsRow.getEntityId());
			if(!feeAssignmentDetailsRowListMap.containsKey(studentId)) {
				List<FeeAssignmentDetailsRow> feeAssignmentDetailsRowList = new ArrayList<FeeAssignmentDetailsRow>();
				feeAssignmentDetailsRowList.add(feeAssignmentDetailsRow);
				feeAssignmentDetailsRowListMap.put(studentId, feeAssignmentDetailsRowList);
				continue;
			}
			
			feeAssignmentDetailsRowListMap.get(studentId).add(feeAssignmentDetailsRow);
		}
		
		if(CollectionUtils.isEmpty(feeAssignmentDetailsRowListMap)) {
			return null;
		}
		
		final Map<UUID, Map<String, EntityFeeAssignment>> studentEntityFeeAssignment = new HashMap<UUID, Map<String, EntityFeeAssignment>>();
		for(Entry<UUID, List<FeeAssignmentDetailsRow>> feeAssignmentDetailsRowEntrySet : feeAssignmentDetailsRowListMap.entrySet()) {
			List<FeeAssignmentDetailsRow> feeAssignmentDetailsRowList = feeAssignmentDetailsRowEntrySet.getValue();
			
			/**
			 * adding class and institute level fees for each student
			 */
			if(CollectionUtils.isEmpty(feeAssignmentDetailsRowEntrySet.getValue())) {
				feeAssignmentDetailsRowEntrySet.setValue(new ArrayList<FeeAssignmentDetailsRow>());
			}
			feeAssignmentDetailsRowList.addAll(classLevelFees);
			feeAssignmentDetailsRowList.addAll(instituteLevelFees);
			
			studentEntityFeeAssignment.put(feeAssignmentDetailsRowEntrySet.getKey(), getFeeAssignments(feeAssignmentDetailsRowList));
		}
		return studentEntityFeeAssignment;
	}

	public static Map<String, EntityFeeAssignment> getFeeAssignments(
			List<FeeAssignmentDetailsRow> feeAssignmentDetailsRows) {
		if (CollectionUtils.isEmpty(feeAssignmentDetailsRows)) {
			return new HashMap<>();
		}
		final Map<String, EntityFeeAssignment> entityFeeAssignments = new LinkedHashMap<>();
		final Map<String, List<FeeAssignmentDetailsRow>> entityIdVsFeeAssignmentRowListMap = getEntityIdVsFeeConfigRowList(
				feeAssignmentDetailsRows);

		final Iterator<Map.Entry<String, List<FeeAssignmentDetailsRow>>> entityIdVsFeeAssignmentRowListMapItr = entityIdVsFeeAssignmentRowListMap
				.entrySet().iterator();
		while (entityIdVsFeeAssignmentRowListMapItr.hasNext()) {
			final Map.Entry<String, List<FeeAssignmentDetailsRow>> entry = entityIdVsFeeAssignmentRowListMapItr.next();
			final EntityFeeAssignment entityFeeAssignment = getFeeAssignmentForEntity(entry.getValue());
			entityFeeAssignments.put(entry.getKey(), entityFeeAssignment);
		}

		return entityFeeAssignments;
	}

	private static Map<UUID, Set<FeeHeadAmountDetails>> getFeeIdVsFeeHeadAmountSetMap(
			List<FeeAssignmentDetailsRow> feeAssignmentDetailsRows) {
		final Map<UUID, Set<FeeHeadAmountDetails>> feeIdVsFeeHeadAmountSetMap = new HashMap<>();

		final Map<UUID, Map<Integer, FeeHeadAmountDetails>> feeIdFeeHeadAmountMap = new HashMap<>();
		for (final FeeAssignmentDetailsRow feeAssignmentRow : feeAssignmentDetailsRows) {
			final UUID feeId = feeAssignmentRow.getFeeConfigurationResponse().getFeeConfigurationBasicInfo().getFeeId();
			final int feeHeadId = feeAssignmentRow.getFeeHeadConfiguration().getFeeHeadId();

			if (!feeIdFeeHeadAmountMap.containsKey(feeId)) {
				feeIdFeeHeadAmountMap.put(feeId, new HashMap<>());
			}
			if (!feeIdFeeHeadAmountMap.get(feeId).containsKey(feeHeadId)) {
				final Set<Module> authorizedModules = new HashSet<>();
				final Module module = feeAssignmentRow.getModule() == null ? Module.FEES : feeAssignmentRow.getModule();
				authorizedModules.add(module);

				final FeeHeadAmountDetails feeHeadAmountDetails = new FeeHeadAmountDetails(
						feeAssignmentRow.getFeeHeadConfiguration(), feeAssignmentRow.getAmount(), authorizedModules);
				feeIdFeeHeadAmountMap.get(feeId).put(feeHeadId, feeHeadAmountDetails);
				continue;
			}
			final Module module = feeAssignmentRow.getModule() == null ? Module.FEES : feeAssignmentRow.getModule();
			feeIdFeeHeadAmountMap.get(feeId).get(feeHeadId).getAuthorizedModules().add(module);
		}

		for (final Entry<UUID, Map<Integer, FeeHeadAmountDetails>> entry : feeIdFeeHeadAmountMap.entrySet()) {
			feeIdVsFeeHeadAmountSetMap.put(entry.getKey(), new HashSet<>(entry.getValue().values()));
		}
		return feeIdVsFeeHeadAmountSetMap;

	}

	private static Map<String, List<FeeAssignmentDetailsRow>> getEntityIdVsFeeConfigRowList(
			List<FeeAssignmentDetailsRow> feeAssignmentDetailsRows) {
		final Map<String, List<FeeAssignmentDetailsRow>> entityIdVsFeeAssignmentRowListMap = new LinkedHashMap<>();
		for (final FeeAssignmentDetailsRow feeAssignmentRow : feeAssignmentDetailsRows) {
			if (entityIdVsFeeAssignmentRowListMap.containsKey(feeAssignmentRow.getEntityId())) {
				entityIdVsFeeAssignmentRowListMap.get(feeAssignmentRow.getEntityId()).add(feeAssignmentRow);
			} else {
				final List<FeeAssignmentDetailsRow> entityFeeAssignmentRows = new ArrayList<>();
				entityFeeAssignmentRows.add(feeAssignmentRow);
				entityIdVsFeeAssignmentRowListMap.put(feeAssignmentRow.getEntityId(), entityFeeAssignmentRows);
			}
		}
		return entityIdVsFeeAssignmentRowListMap;
	}
}
