package com.lernen.cloud.dao.tier.attendance.mappers;

import com.lernen.cloud.core.api.attendance.AttendanceType;
import com.lernen.cloud.core.api.attendance.AttendanceTypeMetadata;
import com.lernen.cloud.core.api.attendance.AttendanceTypeRow;
import com.lernen.cloud.core.utils.SharedConstants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

public class AttendanceTypeRowMapper implements RowMapper<AttendanceType> {

    private static final String INSTITUTE_ID = "institute_id";
    private static final String ACADEMIC_SESSION_ID = "academic_session_id";
    private static final String ATTENDANCE_TYPE_ID = "attendance_type_id";
    private static final String NAME = "name";
    private static final String DESCRIPTION = "description";

    @Override
    public AttendanceType mapRow(ResultSet rs, int rowNum) throws SQLException {
        if (rs.getInt(ATTENDANCE_TYPE_ID) <= 0) {
            return null;
        }

        return new AttendanceType(rs.getInt(INSTITUTE_ID), rs.getInt(ACADEMIC_SESSION_ID),
                rs.getInt(ATTENDANCE_TYPE_ID), rs.getString(NAME), rs.getString(DESCRIPTION), null, null);
    }
}
