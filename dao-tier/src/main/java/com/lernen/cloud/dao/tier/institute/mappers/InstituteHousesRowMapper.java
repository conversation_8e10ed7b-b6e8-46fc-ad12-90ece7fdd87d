/**
 * 
 */
package com.lernen.cloud.dao.tier.institute.mappers;

import com.lernen.cloud.core.api.institute.InstituteHouse;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.UUID;

/**
 * <AUTHOR>
 *
 */
public class InstituteHousesRowMapper implements RowMapper<InstituteHouse> {

	private static final String INSTITUTE_ID = "institute_houses.institute_id";
	private static final String HOUSE_ID = "institute_houses.house_id";
	private static final String HOUSE_NAME = "institute_houses.name";

	@Override
	public InstituteHouse mapRow(ResultSet rs, int rowNum) throws SQLException {
		return new InstituteHouse(rs.getInt(INSTITUTE_ID), UUID.fromString(rs.getString(HOUSE_ID)),
				rs.getString(HOUSE_NAME));
	}

}
