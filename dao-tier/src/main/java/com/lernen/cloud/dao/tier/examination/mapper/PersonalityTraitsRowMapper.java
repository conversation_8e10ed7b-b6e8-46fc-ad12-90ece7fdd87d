package com.lernen.cloud.dao.tier.examination.mapper;

import com.lernen.cloud.core.api.examination.PersonalityTraitsDetails;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.UUID;

/**
 * <AUTHOR>
 * @created_at 05/03/24 : 16:53
 **/
public class PersonalityTraitsRowMapper implements RowMapper<PersonalityTraitsDetails> {

    public static final String INSTITUTE_ID = "personality_traits_details.institute_id";
    private static final String ACADEMIC_SESSION_ID = "personality_traits_details.academic_session_id";
    private static final String PERSONALITY_TRAIT_ID = "personality_traits_details.personality_trait_id";
    public static final String PERSONALITY_TRAIT_NAME = "personality_traits_details.personality_trait_name";
    private static final String STANDARD_ID = "personality_traits_details.standard_id";
    private static final String PERSONALITY_TRAIT_SEQUENCE = "personality_traits_details.personality_trait_sequence";

    @Override
    public PersonalityTraitsDetails mapRow(ResultSet rs, int rowNum) throws SQLException {
        String personalityTraitId = rs.getString(PERSONALITY_TRAIT_ID);
        if(StringUtils.isBlank(personalityTraitId)) {
            return null;
        }
        return new PersonalityTraitsDetails(rs.getInt(INSTITUTE_ID), rs.getInt(ACADEMIC_SESSION_ID), UUID.fromString(rs.getString(PERSONALITY_TRAIT_ID)),
                rs.getString(PERSONALITY_TRAIT_NAME), UUID.fromString(rs.getString(STANDARD_ID)), rs.getInt(PERSONALITY_TRAIT_SEQUENCE));
    }
}