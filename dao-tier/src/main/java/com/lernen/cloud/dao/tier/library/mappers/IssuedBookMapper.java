package com.lernen.cloud.dao.tier.library.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Base64;
import java.util.UUID;

import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.library.BookDetails;
import com.lernen.cloud.core.api.library.BookDetailsRow;
import com.lernen.cloud.core.api.library.BookDetailsPayload;
import com.lernen.cloud.core.api.library.IndividualBook;
import com.lernen.cloud.core.api.library.IssueStatus;
import com.lernen.cloud.core.api.library.IssuedBook;
import com.lernen.cloud.core.api.library.IssuedBookEntry;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.staff.StaffCategory;
import com.lernen.cloud.core.api.staff.StaffLite;
import com.lernen.cloud.core.api.staff.StaffStatus;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.student.StudentStatus;
import com.lernen.cloud.core.api.user.BloodGroup;
import com.lernen.cloud.core.api.user.Gender;
import com.lernen.cloud.core.api.user.UserCategory;
import com.lernen.cloud.core.api.user.UserLite;
import com.lernen.cloud.core.api.user.UserStatus;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.utils.crypto.CryptoUtils;
import com.lernen.cloud.core.utils.crypto.DefaultKMSClient;
import com.lernen.cloud.dao.tier.staff.StaffDao;
import com.lernen.cloud.dao.tier.staff.mappers.StaffRowMapper;
import com.lernen.cloud.dao.tier.student.mappers.StudentRowMapper;


/**
 * 
 * <AUTHOR>
 * @updated-by vasav mittal
 */
public class IssuedBookMapper implements RowMapper<IssuedBook> {

    // Constants for column names
    public static final BookDetailsMapper BOOK_DETAILS_MAPPER = new BookDetailsMapper(); 
    public static final StudentRowMapper STUDENT_ROW_MAPPER = new StudentRowMapper();
    public static final StaffRowMapper STAFF_ROW_MAPPER = new StaffRowMapper();

    private static final String LIBRARY_BOOK_ID = "library_ledger.book_id";
    private static final String LIBRARY_INSTITUTE_ID = "library_ledger.institute_id";
    private static final String LIBRARY_ACADEMIC_SESSION_ID = "library_ledger.academic_session_id";
    private static final String LIBRARY_TRANSACTION_ID = "library_ledger.transaction_id";
    private static final String LIBRARY_USER_TYPE = "library_ledger.user_type";
    private static final String LIBRARY_ACCESSION_ID = "library_ledger.accession_id";
    private static final String LIBRARY_DURATION = "library_ledger.duration";
    private static final String LIBRARY_ISSUED_AT = "library_ledger.issued_timestamp";
    private static final String LIBRARY_RETURNED_AT = "library_ledger.returned_timestamp";
    private static final String LIBRARY_STATUS = "library_ledger.status";

    private static final String ISSUED_BY_USER_ID = "ib.user_id";
    private static final String ISSUED_BY_USER_INSTITUTE_ID = "ib.user_institute_id";
    private static final String ISSUED_BY_INSTITUTE_ID = "ib.institute_id";
    private static final String ISSUED_BY_USER_NAME = "ib.user_name";
    private static final String ISSUED_BY_USER_TYPE = "ib.user_type";
    private static final String ISSUED_BY_USER_STATUS = "ib.user_status";
    private static final String ISSUED_BY_NAME = "ib.first_name";

    private static final String RECEIVED_BY_USER_ID = "rb.user_id";
    private static final String RECEIVED_BY_USER_INSTITUTE_ID = "rb.user_institute_id";
    private static final String RECEIVED_BY_INSTITUTE_ID = "rb.institute_id";
    private static final String RECEIVED_BY_USER_NAME = "rb.user_name";
    private static final String RECEIVED_BY_USER_TYPE = "rb.user_type";
    private static final String RECEIVED_BY_USER_STATUS = "rb.user_status";
    private static final String RECEIVED_BY_NAME = "rb.first_name";

    public IssuedBook mapRow(ResultSet rs, int rowNum) throws SQLException {
        // Extract BookDetails
        BookDetailsRow bookDetails = BOOK_DETAILS_MAPPER.getBookDetailWithoutTags(rs, rowNum);
        StaffLite staffLite = null;
        StudentLite studentLite = null;
        UserLite issuedLite = null;
        UserLite receviedLite = null;
        if(UserType.valueOf(rs.getString(LIBRARY_USER_TYPE).toUpperCase()) == UserType.STUDENT){
            Student student = STUDENT_ROW_MAPPER.mapRow(rs, rowNum);
            studentLite = Student.getStudentLite(student);
        }
        else{
            Staff staff = STAFF_ROW_MAPPER.mapRow(rs, rowNum);
            staffLite = Staff.getStaffLite(staff);
        }

        issuedLite = new UserLite(UUID.fromString(rs.getString(ISSUED_BY_USER_ID)), rs.getString(ISSUED_BY_USER_INSTITUTE_ID), rs.getInt(ISSUED_BY_INSTITUTE_ID), CryptoUtils.decrypt(rs.getString(ISSUED_BY_NAME)), UserType.getUserType(rs.getString(ISSUED_BY_USER_TYPE).toUpperCase()), UserStatus.getUserStatus(rs.getString(ISSUED_BY_USER_STATUS)), rs.getString(ISSUED_BY_USER_NAME));
        if(rs.getString(RECEIVED_BY_USER_ID) != null){
            receviedLite = new UserLite(UUID.fromString(rs.getString(RECEIVED_BY_USER_ID)), rs.getString(RECEIVED_BY_USER_INSTITUTE_ID), rs.getInt(RECEIVED_BY_INSTITUTE_ID), CryptoUtils.decrypt(rs.getString(RECEIVED_BY_NAME)), UserType.getUserType(rs.getString(RECEIVED_BY_USER_TYPE).toUpperCase()), UserStatus.getUserStatus(rs.getString(RECEIVED_BY_USER_STATUS)), rs.getString(RECEIVED_BY_USER_NAME));
        }
        IssuedBookEntry issuedBookEntry = new IssuedBookEntry(
            rs.getInt(LIBRARY_INSTITUTE_ID),
            rs.getInt(LIBRARY_ACADEMIC_SESSION_ID),
            UUID.fromString(rs.getString(LIBRARY_TRANSACTION_ID)),
            studentLite,
            staffLite,
            UserType.getUserType(rs.getString(LIBRARY_USER_TYPE).toUpperCase()), 
            UUID.fromString(rs.getString(LIBRARY_BOOK_ID)),
            rs.getString(LIBRARY_ACCESSION_ID),
            rs.getInt(LIBRARY_DURATION),
            rs.getTimestamp(LIBRARY_ISSUED_AT) != null ? (int) (rs.getTimestamp(LIBRARY_ISSUED_AT).getTime() / 1000l) : null,
            issuedLite,
            rs.getTimestamp(LIBRARY_RETURNED_AT) != null ? (int) (rs.getTimestamp(LIBRARY_RETURNED_AT).getTime() / 1000l) : null,
           receviedLite,
            IssueStatus.getStatusFromString(rs.getString(LIBRARY_STATUS)) 
        );
        return new IssuedBook(bookDetails, issuedBookEntry);
    }
}
