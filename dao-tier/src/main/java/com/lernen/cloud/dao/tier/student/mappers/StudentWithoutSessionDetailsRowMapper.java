package com.lernen.cloud.dao.tier.student.mappers;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lernen.cloud.core.api.common.AreaType;
import com.lernen.cloud.core.api.common.ChildCategoryCriteria;
import com.lernen.cloud.core.api.student.*;
import com.lernen.cloud.core.api.user.BloodGroup;
import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.core.api.user.Gender;
import com.lernen.cloud.core.api.user.UserCategory;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.text.WordUtils;
import org.springframework.jdbc.core.RowMapper;

import java.lang.reflect.Type;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * creating this row mapper because we are getting error 'Column 'students.name' not found.'
 * when joining with courses tables check query 'GET_STUDENT_ALL_COURSES_ASSIGNMENT_DETAILS' in CoursesDao
 * for reference
 */
public class StudentWithoutSessionDetailsRowMapper implements RowMapper<StudentDetailedRow> {

	public static final String INSTITUTE_ID = "institute_id";
	private static final String REGISTRATION_REQUEST_NUMBER = "registration_request_number";
	private static final String REGISTRATION_NUMBER = "registration_number";
	private static final String ONLINE_REGISTRATION_NUMBER = "online_registration_number";
	public static final String ADMISSION_NUMBER = "admission_number";
	public static final String PEN_NUMBER = "pen_number";
	public static final String APAAR_ID = "apaar_id";
	private static final String ONLINE_REGISTRATION_DATE = "online_registration_date";
	private static final String REGISTRATION_DATE = "registration_date";
	private static final String ADMISSION_DATE = "admission_date";
	private static final String RELIEVE_DATE = "relieve_date";

	public static final String NAME = "name";
	private static final String DATE_OF_BIRTH = "date_of_birth";
	private static final String BIRTH_PLACE = "birth_place";
	private static final String GENDER = "gender";
	private static final String CATEGORY = "category";
	private static final String MOTHER_TONGUE = "mother_tongue";
	private static final String AREA_TYPE = "area_type";
	private static final String SPECIALLY_ABLED = "specially_abled";
	private static final String BPL = "bpl";
	private static final String RELIGION = "religion";
	private static final String RTE = "rte";
	private static final String AADHAR_NUMBER = "aadhar_number";
	private static final String PERMANENT_ADDRESS = "permanent_address";
	private static final String PERMANENT_CITY = "permanent_city";
	private static final String PERMANENT_STATE = "permanent_state";
	private static final String PERMANENT_POST_OFFICE = "permanent_post_office";
	private static final String PERMANENT_POLICE_STATION = "permanent_police_station";
	private static final String PERMANENT_ZIPCODE = "permanent_zipcode";
	private static final String PERMANENT_COUNTRY = "permanent_country";
	private static final String PRESENT_ADDRESS = "present_address";
	private static final String PRESENT_CITY = "present_city";
	private static final String PRESENT_STATE = "present_state";
	private static final String PRESENT_POST_OFFICE = "present_post_office";
	private static final String PRESENT_POLICE_STATION = "present_police_station";
	private static final String PRESENT_ZIPCODE = "present_zipcode";
	private static final String PRESENT_COUNTRY = "present_country";
	private static final String PRIMARY_CONTACT_NUMBER = "primary_contact_number";
	private static final String NATIONALITY = "nationality";
	private static final String PRIMARY_EMAIL = "primary_email";
	private static final String CASTE = "caste";
	private static final String RELIEVE_REASON = "relieve_reason";
	private static final String IS_SPONSORED = "is_sponsored";
	private static final String IS_HOSTELLER = "is_hosteller";
	private static final String WHATSAPP_NUMBER = "whatsapp_number";
	private static final String SIBLING_GROUP_ID = "sibling_group_id";
	private static final String RELIEVED_METADATA = "relieved_metadata";
	private static final String INSTITUTE_HOUSE_ID = "house_id";
	private static final String ADMISSION_IN_CLASS = "admission_class";
	private static final String SPECIALLY_ABLED_TYPE = "specially_abled_type";
	private static final String STUDENT_NAME_AS_PER_AADHAR = "student_name_as_per_aadhar";
	private static final String CHILD_CATEGORY_CRITERIA = "child_category_criteria";
	protected static final String HEIGHT = "height";
	protected static final String WEIGHT = "weight";
	protected static final String FINAL_STATUS = "final_status";
	protected static final String STUDENT_DOCUMENTS = "student_documents";
	protected static final String TC_VARIABLES = "tc_variables";
	protected static final String TC_DETAILS = "tc_details";

	protected static final String IS_ADMISSION_TC_BASED = "is_admission_tc_based";
	protected static final String PREVIOUS_SCHOOL_TC_NUMBER = "previous_school_tc_number";

	protected static final String DEVICE_USER_ID = "device_user_id";
	protected static final String TAG_DETAILS = "student_tags";
	private static final Gson GSON = new Gson();

	@Override
	public StudentDetailedRow mapRow(ResultSet rs, int rowNum) throws SQLException {

		final Timestamp onlineRegistrationTimestamp = rs.getTimestamp(ONLINE_REGISTRATION_DATE);
		final Integer onlineRegistrationTime = onlineRegistrationTimestamp == null ? null
				: (int) (onlineRegistrationTimestamp.getTime() / 1000l);

		final Timestamp registrationTimestamp = rs.getTimestamp(REGISTRATION_DATE);
		final Integer registrationTime = registrationTimestamp == null ? null : (int) (registrationTimestamp.getTime() / 1000l);

		final Timestamp admissionTimestamp = rs.getTimestamp(ADMISSION_DATE);
		final Integer admissionTime = admissionTimestamp == null ? null : (int) (admissionTimestamp.getTime() / 1000l);

		final Timestamp relieveTimestamp = rs.getTimestamp(RELIEVE_DATE);
		final Integer relieveTime = relieveTimestamp == null ? null : (int) (relieveTimestamp.getTime() / 1000l);

		final Timestamp dobTimestamp = rs.getTimestamp(DATE_OF_BIRTH);
		final Integer dobTime = dobTimestamp == null ? null : (int) (dobTimestamp.getTime() / 1000l);

		Boolean bpl = rs.getBoolean(BPL);
		if (rs.wasNull()) {
			bpl = null;
		}

		Boolean speciallyAbled = rs.getBoolean(SPECIALLY_ABLED);
		if (rs.wasNull()) {
			speciallyAbled = null;
		}

		Boolean isSponsored = rs.getBoolean(IS_SPONSORED);
//		if (rs.wasNull()) {
//			isSponsored = null;
//		}

		final String relievedMetadataStr = rs.getString(RELIEVED_METADATA);
		Map<RelievedMetadataVariables, String> relievedMetadata = null;
		if (!StringUtils.isBlank(relievedMetadataStr)) {
			final Type collectionType = new TypeToken<Map<RelievedMetadataVariables, String>>() {
			}.getType();
			relievedMetadata = GSON.fromJson(relievedMetadataStr, collectionType);
		}

		StudentBasicInfo studentBasicInfo = new StudentBasicInfo(rs.getString(REGISTRATION_REQUEST_NUMBER), rs.getString(REGISTRATION_NUMBER),
				rs.getString(ONLINE_REGISTRATION_NUMBER), rs.getString(ADMISSION_NUMBER), rs.getString(PEN_NUMBER), onlineRegistrationTime,
				registrationTime, admissionTime, relieveTime, WordUtils.capitalizeFully(rs.getString(NAME)), dobTime,
				WordUtils.capitalizeFully(rs.getString(BIRTH_PLACE)), Gender.getGender(rs.getString(GENDER)),
				UserCategory.getCategory(rs.getString(CATEGORY)),
				WordUtils.capitalizeFully(rs.getString(MOTHER_TONGUE)), AreaType.getAreaType(rs.getString(AREA_TYPE)),
				bpl, speciallyAbled, rs.getString(SPECIALLY_ABLED_TYPE), WordUtils.capitalizeFully(rs.getString(RELIGION)), rs.getBoolean(RTE),
				rs.getString(AADHAR_NUMBER), WordUtils.capitalizeFully(rs.getString(PERMANENT_ADDRESS)),
				WordUtils.capitalizeFully(rs.getString(PERMANENT_CITY)),
				WordUtils.capitalizeFully(rs.getString(PERMANENT_STATE)),
				WordUtils.capitalizeFully(rs.getString(PERMANENT_POST_OFFICE)),
				WordUtils.capitalizeFully(rs.getString(PERMANENT_POLICE_STATION)),
				rs.getString(PERMANENT_ZIPCODE),
				rs.getString(PERMANENT_COUNTRY), WordUtils.capitalizeFully(rs.getString(PRESENT_ADDRESS)),
				WordUtils.capitalizeFully(rs.getString(PRESENT_CITY)),
				WordUtils.capitalizeFully(rs.getString(PRESENT_STATE)),
				WordUtils.capitalizeFully(rs.getString(PRESENT_POST_OFFICE)),
				WordUtils.capitalizeFully(rs.getString(PRESENT_POLICE_STATION)),
				rs.getString(PRESENT_ZIPCODE),
				rs.getString(PRESENT_COUNTRY), rs.getString(NATIONALITY), rs.getString(PRIMARY_CONTACT_NUMBER),
				rs.getString(PRIMARY_EMAIL), rs.getString(CASTE), rs.getString(RELIEVE_REASON), isSponsored, rs.getBoolean(IS_HOSTELLER),
				rs.getString(WHATSAPP_NUMBER),
				rs.getString(SIBLING_GROUP_ID) == null ? null : UUID.fromString(rs.getString(SIBLING_GROUP_ID)), relievedMetadata,
				rs.getString(INSTITUTE_HOUSE_ID) == null ? null : UUID.fromString(rs.getString(INSTITUTE_HOUSE_ID)),
				rs.getString(ADMISSION_IN_CLASS),
				rs.getString(STUDENT_NAME_AS_PER_AADHAR), ChildCategoryCriteria.getChildCategoryCriteria(rs.getString(CHILD_CATEGORY_CRITERIA)),
				rs.getString(APAAR_ID)
				);

		final Timestamp dateOfPhysicalExamination = rs.getTimestamp(StudentRowMapper.DATE_OF_PHYSICAL_EXAMINATION);
		final Integer dateOfPhysicalExaminationTime = dateOfPhysicalExamination == null ? null
				: (int) (dateOfPhysicalExamination.getTime() / 1000l);

		final String documents = rs.getString(STUDENT_DOCUMENTS);
		List<Document<StudentDocumentType>> studentDocuments = null;
		if (!StringUtils.isBlank(documents)) {
			final Type collectionType = new TypeToken<List<Document<StudentDocumentType>>>() {
			}.getType();
			studentDocuments = GSON.fromJson(documents, collectionType);
		}

		final String tcVariablesStr = rs.getString(TC_VARIABLES);
		Map<TransferCertificateVariables, String> tcVariables = null;
		if (!StringUtils.isBlank(tcVariablesStr)) {
			final Type collectionType = new TypeToken<Map<TransferCertificateVariables, String>>() {
			}.getType();
			tcVariables = GSON.fromJson(tcVariablesStr, collectionType);
		}

		boolean isAdmissionTcBased = rs.getBoolean(IS_ADMISSION_TC_BASED);
		if (rs.wasNull()) {
			isAdmissionTcBased = false;
		}

		String previousSchoolTcNumber = rs.getString(PREVIOUS_SCHOOL_TC_NUMBER);

		final String tcDetailsStr = rs.getString(TC_DETAILS);
		StudentTransferCertificateDetails studentTransferCertificateDetails = null;
		if (!StringUtils.isBlank(tcDetailsStr)) {
			studentTransferCertificateDetails = GSON.fromJson(tcDetailsStr, StudentTransferCertificateDetails.class);
		}

		final String tagDetailsStr = rs.getString(TAG_DETAILS);
		List<StudentTaggedDetails> studentTaggedDetailsList = null;
		if (!StringUtils.isBlank(tagDetailsStr)) {
			studentTaggedDetailsList = GSON.fromJson(tagDetailsStr,
					new TypeToken<List<StudentTaggedDetails>>(){}.getType());
		}

		return new StudentDetailedRow(rs.getInt(INSTITUTE_ID), UUID.fromString(rs.getString(StudentRowMapper.STUDENT_ID)), studentBasicInfo,
				rs.getString(StudentRowMapper.MOTHER_NAME), rs.getString(StudentRowMapper.FATHER_NAME),
				rs.getString(StudentRowMapper.MOTHER_QUALIFICATION), rs.getString(StudentRowMapper.FATHER_QUALIFICATION),
				rs.getString(StudentRowMapper.MOTHER_CONTACT_NUMBER),
				rs.getString(StudentRowMapper.FATHER_CONTACT_NUMBER), rs.getString(StudentRowMapper.MOTHER_OCCUPATION),
				rs.getString(StudentRowMapper.MOTHER_ANNUAL_INCOME), rs.getString(StudentRowMapper.FATHER_OCCUPATION),
				rs.getString(StudentRowMapper.FATHER_ANNUAL_INCOME), rs.getString(StudentRowMapper.MOTHER_AADHAR_NUMBER),
				rs.getString(StudentRowMapper.FATHER_AADHAR_NUMBER),rs.getString(StudentRowMapper.MOTHER_PAN_CARD_DETAILS),
				rs.getString(StudentRowMapper.FATHER_PAN_CARD_DETAILS),
				rs.getString(StudentRowMapper.APPROX_FAMILY_INCOME), rs.getString(StudentRowMapper.GUARDIANS_INFO_LIST),
				rs.getString(StudentRowMapper.SCHOOL_NAME), rs.getString(StudentRowMapper.CLASS_PASSED),
				rs.getString(StudentRowMapper.MEDIUM), rs.getString(StudentRowMapper.PERCENTAGE),
				rs.getString(StudentRowMapper.RESULT), rs.getInt(StudentRowMapper.YEAR_OF_PASSING),
				BloodGroup.getBloodGroup(rs.getString(StudentRowMapper.BLOOD_GROUP)),
				rs.getString(StudentRowMapper.BLOOD_PRESSURE), rs.getString(StudentRowMapper.PULSE),
				rs.getString(HEIGHT), rs.getString(WEIGHT),
				dateOfPhysicalExaminationTime, null, null,
				StudentStatus.getStudentStatus(rs.getString(FINAL_STATUS)),
				rs.getInt(StudentRowMapper.ADMISSION_ACADEMIC_SESSION), studentDocuments, tcVariables,
				isAdmissionTcBased, previousSchoolTcNumber, rs.getString(DEVICE_USER_ID), null,
				studentTransferCertificateDetails, studentTaggedDetailsList);
	}
}
