package com.lernen.cloud.dao.tier.library;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;

import org.springframework.dao.DataAccessException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.checkerframework.checker.units.qual.s;

import com.google.gson.Gson;
import com.lernen.cloud.core.api.common.PaginationInfo;
import com.lernen.cloud.core.api.common.SearchResultWithPagination;
import com.lernen.cloud.core.api.configurations.Entity;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.DatabaseException;
import com.lernen.cloud.core.api.library.Author;
import com.lernen.cloud.core.api.library.BookDetails;
import com.lernen.cloud.core.api.library.BookDetailsRow;
import com.lernen.cloud.core.api.library.BookDetailsPayload;
import com.lernen.cloud.core.api.library.BookDocumentType;
import com.lernen.cloud.core.api.library.BookPayloadWrapper;
import com.lernen.cloud.core.api.library.Genre;
import com.lernen.cloud.core.api.library.IndividualBook;
import com.lernen.cloud.core.api.library.IssueBookPayload;
import com.lernen.cloud.core.api.library.IssueStatus;
import com.lernen.cloud.core.api.library.IssuedBook;
import com.lernen.cloud.core.api.library.IssuedBookEntry;
import com.lernen.cloud.core.api.library.LibraryLedgerPayload;
import com.lernen.cloud.core.api.library.LibraryType;
import com.lernen.cloud.core.api.library.Publication;
import com.lernen.cloud.core.api.library.Publisher;
import com.lernen.cloud.core.api.library.ReturnBookPayload;
import com.lernen.cloud.core.api.library.IndividualBookDetailsPayload;
import com.lernen.cloud.core.api.library.Vendor;
import com.lernen.cloud.core.api.student.StudentSessionStatusDetails;
import com.lernen.cloud.core.api.user.DataUpdationAction;
import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.PlaceholdersUtils;
import com.lernen.cloud.core.utils.SharedConstants;
import com.lernen.cloud.core.utils.crypto.CryptoUtils;
import com.lernen.cloud.core.utils.exceptions.ExceptionHandling;
import com.lernen.cloud.dao.tier.library.mappers.AuthorMapper;
import com.lernen.cloud.dao.tier.library.mappers.BookDetailsMapper;
import com.lernen.cloud.dao.tier.library.mappers.GenreMapper;
import com.lernen.cloud.dao.tier.library.mappers.IssuedBookMapper;
import com.lernen.cloud.dao.tier.library.mappers.LibraryLedgerMapper;
import com.lernen.cloud.dao.tier.library.mappers.LibraryTypeMapper;
import com.lernen.cloud.dao.tier.library.mappers.PublicationMapper;
import com.lernen.cloud.dao.tier.library.mappers.PublisherMapper;
import com.lernen.cloud.dao.tier.library.mappers.VendorMapper;


/**
 * 
 * <AUTHOR>
 *
 */
public class LibraryDao {

	private final JdbcTemplate jdbcTemplate;
	private final TransactionTemplate transactionTemplate;
	private static final Logger logger = LogManager.getLogger(LibraryDao.class);
	private static final Gson GSON = SharedConstants.GSON;

	private static final String INSERT_BOOK_DETAILS = "INSERT INTO book_details (book_id, institute_id, genre_id, book_title, book_no, book_tags, author_id, isbn_number, publication_id, publisher_id, edition, no_of_copies, publish_year, language, type_of_binding, number_of_pages)"
    	+ " VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

	private static final String INSERT_PUBLICATION_DETAILS = "INSERT INTO publication (publication_id, institute_id, publication_name, address, phone_number, email, website) VALUES(?, ?, ?, ?, ?, ?, ?)";
	
	private static final String INSERT_PUBLISHER_DETAILS = "INSERT INTO publisher (publisher_id, institute_id, publisher_name, contact_information, affiliation) VALUES(?, ?, ?, ?, ?)";

	private static final String INSERT_GENRE_DETAILS = "INSERT INTO genre (genre_id, entity_id, entity_name, genre_name, classification_number) VALUES(?, ?, ?, ?, ?)";

	private static final String INSERT_VENDOR_DETAILS = "INSERT INTO vendor (vendor_id, institute_id, vendor_name, address, phone_number, email, gst_number, account_type, bank_name, account_holder_name, account_number, ifsc_code) VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

	private static final String INSERT_AUTHOR_DETAILS = "INSERT INTO author (author_id, institute_id, author_name, nationality, date_of_birth, associated_genres, short_biography) VALUES(?, ?, ?, ?, ?, ?, ?)";

	private static final String INSERT_LIBRARY_TYPE_DETAILS = "INSERT INTO library_type (library_type_id, institute_id, library_type_name) VALUES(?, ?, ?)";

	private static final String UPDATE_PUBLICATION_DETAILS = "UPDATE publication SET publication_name = ?, address = ?, phone_number = ?, email = ?, website = ? WHERE publication_id = ? AND institute_id = ?";

	private static final String UPDATE_PUBLISHER_DETAILS = "UPDATE publisher SET publisher_name = ?, contact_information = ?, affiliation = ? WHERE publisher_id = ? AND institute_id = ?";

	private static final String UPDATE_GENRE_DETAILS = "UPDATE genre SET genre_name = ?, classification_number = ? WHERE genre_id = ? AND entity_id = ? AND entity_name = ?";

	private static final String UPDATE_VENDOR_DETAILS = "UPDATE vendor SET vendor_name = ?, address = ?, phone_number = ?, email = ?, gst_number = ?, account_type = ?, bank_name = ?, account_holder_name = ?, account_number = ?, ifsc_code = ? WHERE vendor_id = ? AND institute_id = ?";

	private static final String UPDATE_AUTHOR_DETAILS = "UPDATE author SET author_name = ?, nationality = ?, date_of_birth = ?, associated_genres = ?, short_biography = ? WHERE author_id = ? AND institute_id = ?";

	private static final String UPDATE_LIBRARY_TYPE_DETAILS = "UPDATE library_type SET library_type_name = ? WHERE library_type_id = ? AND institute_id = ?";

	private static final String DELETE_PUBLICATION_DETAILS = "DELETE FROM publication WHERE publication_id = ? AND institute_id = ?";

	private static final String DELETE_PUBLISHER_DETAILS = "DELETE FROM publisher WHERE publisher_id = ? AND institute_id = ?";

	private static final String DELETE_GENRE_DETAILS = "DELETE FROM genre WHERE genre_id = ? AND entity_id = ?";

	private static final String DELETE_VENDOR_DETAILS = "DELETE FROM vendor WHERE vendor_id = ? AND institute_id = ?";

	private static final String DELETE_AUTHOR_DETAILS = "DELETE FROM author WHERE author_id = ? AND institute_id = ?";

	private static final String DELETE_LIBRARY_TYPE_DETAILS = "DELETE FROM library_type WHERE library_type_id = ? AND institute_id = ?";

	private static final String GET_PUBLICATION_DETAILS = "SELECT * FROM publication WHERE institute_id = ?";

	private static final String GET_PUBLISHER_DETAILS = "SELECT * FROM publisher WHERE institute_id = ?";

	private static final String GET_GENRE_DETAILS = "SELECT * FROM genre WHERE";

	private static final String GET_VENDOR_DETAILS = "SELECT * FROM vendor WHERE institute_id = ?";

	private static final String GET_AUTHOR_DETAILS = "SELECT * FROM author WHERE institute_id = ?";

	private static final String GET_LIBRARY_TYPE_DETAILS = "SELECT * FROM library_type WHERE institute_id = ?";

	private static final String UPDATE_BOOK_DETAILS = "UPDATE book_details SET genre_id = ?, book_title = ?, book_no = ?, book_tags = ?, author_id = ?, isbn_number = ?, publication_id = ?, publisher_id = ? , edition = ?, publish_year = ?, language = ?, type_of_binding = ?, number_of_pages = ? WHERE institute_id = ? and book_id = ?";

	private static final String UPDATE_BOOK_NUMBER_OF_COPIES = "UPDATE book_details SET no_of_copies = ? WHERE institute_id = ? and book_id = ?";

	private static final String DELETE_BOOK_DETAILS = "DELETE FROM book_details WHERE institute_id = ? and book_id = ?";

	//private static final String INSERT_BOOK_TAGS = "insert into book_tags (tag, book_id) values (?, ?)";

	private static final String INSERT_INDIVIDUAL_BOOK_DETAILS = "insert into individual_book_details (accession_id, institute_id, book_id, accession_number, rack, price, bill_number, date_of_purchase, vendor_id, status, volume, library_type_id, remark)"
		+"VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

	private static final String UPDATE_INDIVIDUAL_BOOK_DETAILS = "UPDATE individual_book_details SET accession_number = ?, rack = ?, price = ?, bill_number = ?, date_of_purchase = ?, vendor_id = ?, status = ?, volume = ?, library_type_id = ?, remark = ? WHERE institute_id = ? AND accession_id = ?";

	//private static final String DELETE_BOOK_TAGS = "DELETE FROM book_tags WHERE book_id = ?";

	private static final String DELETE_INDIVIDUAL_BOOK_DETAILS = "DELETE FROM individual_book_details WHERE institute_id = ? and book_id = ?";

	private static final String DELETE_INDIVIDUAL_BOOK_DETAIL = "DELETE FROM individual_book_details WHERE institute_id = ? ";

	//private static final String GET_BOOK_DETAILS = "select book_details.*, GROUP_CONCAT(book_tags.tag) AS tags FROM book_details LEFT JOIN book_tags ON book_tags.book_id = book_details.book_id ";

	private static final String GET_NUMBER_OF_BOOK_COPIES = " SELECT no_of_copies from book_details where institute_id = ? and book_id = ?";

	private static final String COUNT_ISSUED_BOOK_DATA = " SELECT COUNT(*) FROM library_ledger WHERE institute_id = ?";

	private static final String GET_BOOK_DETAILS = " SELECT book_details.*, publication.*, publisher.*, genre.*, author.*, individual_book_details.*, vendor.* , library_type.* FROM book_details "
													+"LEFT JOIN publication ON book_details.publication_id = publication.publication_id "
													+"LEFT JOIN publisher ON book_details.publisher_id = publisher.publisher_id "
													+"LEFT JOIN genre ON book_details.genre_id = genre.genre_id " 
													+"LEFT JOIN author ON book_details.author_id = author.author_id " 
													+"INNER JOIN individual_book_details ON book_details.book_id = individual_book_details.book_id "
													+"LEFT JOIN  vendor ON vendor.vendor_id = individual_book_details.vendor_id "
													+"LEFT JOIN library_type ON library_type.library_type_id = individual_book_details.library_type_id ";

	private static final String COUNT_GET_BOOK_DETAILS = "SELECT COUNT(*) FROM ( SELECT book_details.* FROM book_details ";

	private static final String BOOK_SEARCH_CONDITION = " CONCAT_WS('/', book_details.book_title, book_details.book_no, book_details.isbn_number, book_details.book_tags, individual_book_details.accession_number, library_type.library_type_name, author.author_name, genre.genre_name, genre.classification_number, publisher.publisher_name, publication.publication_name) LIKE ? ";

	private static final String COUNT_BOOK_SEARCH_CONDITION = " CONCAT_WS('/', book_details.book_title, book_details.book_no, book_details.isbn_number, book_details.book_tags) LIKE ? ";

	private static final String BOOK_ISSUED_STATUS =" SELECT library_ledger.* FROM library_ledger WHERE institute_id = ? AND status = ?";
	
	private static final String DELETE_LIBRARY_LEDGER_DATA = " DELETE FROM library_ledger where institute_id = ? and academic_session_id = ? AND transaction_id = ?";

	private static final String UPDATE_ISSUED_BOOK_DATE = " UPDATE library_ledger set issued_timestamp = ?, updated_at = ? where institute_id = ? and academic_session_id = ? AND transaction_id = ?";
	
	private static final String UPDATE_RETURNED_BOOK_DATE = " UPDATE library_ledger set returned_timestamp = ?, updated_at = ? where institute_id = ? and academic_session_id = ? AND transaction_id = ?";

	private static final String GET_ISSUED_BOOKS_FOR_USER ="SELECT library_ledger.*, students.*, staff_details.*, standard_section_mapping.*, student_academic_session_details.*,"
															+ "academic_session.*, standards.*, ib.*, rb.*, book_details.*, publication.*, publisher.*, genre.*, author.*, individual_book_details.*, vendor.* , library_type.* FROM library_ledger "
															+ "LEFT JOIN students ON library_ledger.user_id = students.student_id AND library_ledger.user_type = 'STUDENT' "
															+ "LEFT JOIN student_academic_session_details ON students.student_id = student_academic_session_details.student_id "
															+ "LEFT JOIN academic_session ON student_academic_session_details.academic_session_id = academic_session.academic_session_id "
															+ "LEFT JOIN standards ON student_academic_session_details.standard_id = standards.standard_id "
															+ "LEFT JOIN standard_section_mapping ON standard_section_mapping.standard_id = standards.standard_id "
															+ "AND standard_section_mapping.academic_session_id = student_academic_session_details.academic_session_id "
															+ "AND student_academic_session_details.section_id = standard_section_mapping.section_id "
															+ "LEFT JOIN staff_details ON library_ledger.user_id = staff_details.staff_id AND library_ledger.user_type = 'STAFF' "
															+ "LEFT JOIN users ib ON library_ledger.issued_by = ib.user_id "
															+ "LEFT JOIN users rb ON library_ledger.received_by = rb.user_id "
															+ "LEFT JOIN book_details ON library_ledger.book_id = book_details.book_id "
															+ "LEFT JOIN publication ON book_details.publication_id = publication.publication_id "
															+ "LEFT JOIN publisher ON book_details.publisher_id = publisher.publisher_id "
															+ "LEFT JOIN genre ON book_details.genre_id = genre.genre_id " 
															+ "LEFT JOIN author ON book_details.author_id = author.author_id " 
															+ "INNER JOIN individual_book_details ON library_ledger.accession_id = individual_book_details.accession_id "
															+ "LEFT JOIN  vendor ON vendor.vendor_id = individual_book_details.vendor_id "
															+ "LEFT JOIN library_type ON library_type.library_type_id = individual_book_details.library_type_id "
															+ "WHERE library_ledger.institute_id = ?";
	
	private static final String COUNT_GET_ISSUED_BOOKS_FOR_USER = "SELECT count(*) FROM library_ledger "
															+ "LEFT JOIN students ON library_ledger.user_id = students.student_id AND library_ledger.user_type = 'STUDENT' "
															+ "LEFT JOIN student_academic_session_details ON students.student_id = student_academic_session_details.student_id "
															+ "LEFT JOIN academic_session ON student_academic_session_details.academic_session_id = academic_session.academic_session_id "
															+ "LEFT JOIN standards ON student_academic_session_details.standard_id = standards.standard_id "
															+ "LEFT JOIN standard_section_mapping ON standard_section_mapping.standard_id = standards.standard_id "
															+ "AND standard_section_mapping.academic_session_id = student_academic_session_details.academic_session_id "
															+ "AND student_academic_session_details.section_id = standard_section_mapping.section_id "
															+ "LEFT JOIN staff_details ON library_ledger.user_id = staff_details.staff_id AND library_ledger.user_type = 'STAFF' "
															+ "LEFT JOIN users ib ON library_ledger.issued_by = ib.user_id "
															+ "LEFT JOIN users rb ON library_ledger.received_by = rb.user_id "
															+ "LEFT JOIN book_details ON library_ledger.book_id = book_details.book_id "
															+ "LEFT JOIN publication ON book_details.publication_id = publication.publication_id "
															+ "LEFT JOIN publisher ON book_details.publisher_id = publisher.publisher_id "
															+ "LEFT JOIN genre ON book_details.genre_id = genre.genre_id " 
															+ "LEFT JOIN author ON book_details.author_id = author.author_id " 
															+ "INNER JOIN individual_book_details ON library_ledger.accession_id = individual_book_details.accession_id "
															+ "LEFT JOIN  vendor ON vendor.vendor_id = individual_book_details.vendor_id "
															+ "LEFT JOIN library_type ON library_type.library_type_id = individual_book_details.library_type_id "
															+ "WHERE library_ledger.institute_id = ?";
	
	private static final String ISSUE_BOOK = "insert into library_ledger (institute_id, academic_session_id, transaction_id, user_id, user_type, book_id, accession_id, duration, issued_timestamp, issued_by, status ) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

	private static final String RETURN_BOOK = "update library_ledger set received_by = ? , returned_timestamp = ?, updated_at = ?, status = ? where institute_id = ? and academic_session_id = ? AND transaction_id = ?";

	private static final String UPDATE_BOOK_DOCUMENTS = "UPDATE book_details set book_documents = ? WHERE  institute_id = ? AND book_id = ?";
	
	private static final String WHERE_CLAUSE_WITH_GROUP_STRING = "WHERE book_details.institute_id = ? GROUP BY book_details.book_id, publication.publication_id, publisher.publisher_id, genre.genre_id, author.author_id, individual_book_details.accession_id, vendor.vendor_id, library_type.library_type_id ";

	private static final String COUNT_WHERE_CLAUSE_WITH_GROUP_STRING = "WHERE book_details.institute_id = ? GROUP BY book_details.book_id ";

	private static final String GROUP_NAME_STRING = ") AS grouped_books ";

	private static final String WHERE_CLAUSE_BOOK_ID = "WHERE book_details.institute_id = ? AND book_details.book_id = ? GROUP BY book_details.book_id, publication.publication_id, publisher.publisher_id, genre.genre_id, author.author_id, individual_book_details.accession_id, vendor.vendor_id, library_type.library_type_id ";

	private static final String COUNT_WHERE_CLAUSE_BOOK_ID = "WHERE book_details.institute_id = ? AND book_details.book_id = ? GROUP BY book_details.book_id ";

	private static final LibraryLedgerMapper LIBRARY_LEDGER_MAPPER = new LibraryLedgerMapper();

	private static final BookDetailsMapper BOOK_DETAILS_MAPPER = new BookDetailsMapper();

	private static final IssuedBookMapper ISSUE_BOOK_MAPPER = new IssuedBookMapper();

	private static final PublicationMapper PUBLICATION_MAPPER = new PublicationMapper();

	private static final PublisherMapper PUBLISHER_MAPPER = new PublisherMapper();

	private static final GenreMapper GENRE_MAPPER = new GenreMapper();

	private static final VendorMapper VENDOR_MAPPER = new VendorMapper();

	private static final AuthorMapper AUTHOR_MAPPER = new AuthorMapper();

	private static final LibraryTypeMapper LIBRARY_TYPE_MAPPER = new LibraryTypeMapper();


	public LibraryDao(JdbcTemplate jdbcTemplate, TransactionTemplate transactionTemplate) {
		this.jdbcTemplate = jdbcTemplate;
		this.transactionTemplate = transactionTemplate;
	}



	public boolean addPublication(int instituteId, Publication publication){
		final UUID publicationId = UUID.randomUUID();
		try{

			int rows = jdbcTemplate.update(INSERT_PUBLICATION_DETAILS, publicationId.toString(), instituteId, publication.getPublicationName(), publication.getAddress(), publication.getPhoneNumber(), publication.getEmail(), publication.getWebsite());
			if(rows != 1){
				throw new RuntimeException("Failed to insert Publication Details") ;
			}

			return true;

		}catch (final DatabaseException | ApplicationException ex) {
			throw ex;
		} catch (Exception ex) {
			logger.error("Failed to add Publication Details", ex); // Rethrow for proper error handling
		}
		return false;
	}

	public boolean updatePublication(int instituteId, Publication publication){
		try{

			UUID publicationId = publication.getPublicationId();
					// Update publication details
			int rows = jdbcTemplate.update(UPDATE_PUBLICATION_DETAILS, 
											publication.getPublicationName(), 
											publication.getAddress(),
											publication.getPhoneNumber(),
											publication.getEmail(),
											publication.getWebsite(),
											publicationId.toString(),
											instituteId);
			if (rows != 1) {
				throw new RuntimeException("Failed to update publication details");
			}
			return true;

		}catch (final DatabaseException | ApplicationException ex) {
			throw ex;
		} catch (Exception ex) {
			logger.error("Failed to update Publication Details", ex);
		}
		return false;
	}

	public boolean deletePublication(int instituteId, UUID publicationId) {
		try {
			// Delete publication details
			int rows = jdbcTemplate.update(DELETE_PUBLICATION_DETAILS, publicationId.toString(), instituteId);
			if (rows != 1) {
				throw new RuntimeException("Failed to delete publication details");
			}
			return true;
	
		} catch (final DatabaseException | ApplicationException ex) {
			throw ex;
		} catch (Exception ex) {
			logger.error("Failed to delete Publication Details", ex);
		}
		return false;
	}
	
	public List<Publication> getPublicationData(int instituteId, UUID publicationId, String publicationName) {
		try {
			StringBuilder query = new StringBuilder(GET_PUBLICATION_DETAILS);
			List<Object> args = new ArrayList<>();
			args.add(instituteId);

			if (publicationId != null) {
				query.append(" AND publication_id = ?");
				args.add(publicationId.toString());
			}
			if(!StringUtils.isAllEmpty(publicationName)){
				query.append(" AND LOWER(publication_name) = ?");
				args.add(publicationName.toLowerCase());
			}

			return jdbcTemplate.query(query.toString(), args.toArray(), PUBLICATION_MAPPER);
		} catch (DataAccessException de) {
			logger.error("No publication data found for the provided criteria.");
		} catch (Exception e) {
			logger.error("Error while fetching publication data", e);
		}
		return null;
	}

	public boolean addPublisher(int instituteId, Publisher publisher) {
		final UUID publisherId = UUID.randomUUID();
		try {
			int rows = jdbcTemplate.update(INSERT_PUBLISHER_DETAILS, publisherId.toString(), instituteId, publisher.getPublisherName(), publisher.getContactInformation(), publisher.getAffiliation());
			if (rows != 1) {
				throw new RuntimeException("Failed to insert Publisher Details");
			}
			return true;
		} catch (final DatabaseException | ApplicationException ex) {
			throw ex;
		} catch (Exception ex) {
			logger.error("Failed to add Publisher Details", ex);
		}
		return false;
	}
	public boolean updatePublisher(int instituteId, Publisher publisher) {
		try {
			UUID publisherId = publisher.getPublisherId();
			// Update publisher details
			int rows = jdbcTemplate.update(UPDATE_PUBLISHER_DETAILS,
											publisher.getPublisherName(),
											publisher.getContactInformation(),
											publisher.getAffiliation(),
											publisherId.toString(),
											instituteId);
			if (rows != 1) {
				throw new RuntimeException("Failed to update publisher details");
			}
			return true;
	
		} catch (final DatabaseException | ApplicationException ex) {
			throw ex;
		} catch (Exception ex) {
			logger.error("Failed to update Publisher Details", ex);
		}
		return false;
	}
	public boolean deletePublisher(int instituteId, UUID publisherId) {
		try {
			// Delete publisher details
			int rows = jdbcTemplate.update(DELETE_PUBLISHER_DETAILS, publisherId.toString(), instituteId);
			if (rows != 1) {
				throw new RuntimeException("Failed to delete publisher details");
			}
			return true;
	
		} catch (final DatabaseException | ApplicationException ex) {
			throw ex;
		} catch (Exception ex) {
			logger.error("Failed to delete Publisher Details", ex);
		}
		return false;
	}
	
	public List<Publisher> getPublisherData(int instituteId, UUID publisherId, String publisherName) {
		try {
			StringBuilder query = new StringBuilder(GET_PUBLISHER_DETAILS);
			List<Object> args = new ArrayList<>();
			args.add(instituteId);
	
			if (publisherId != null) {
				query.append(" AND publisher_id = ?");
				args.add(publisherId.toString());
			}

			if(!StringUtils.isBlank(publisherName)){
				query.append(" AND LOWER(publisher_name) = ?");
				args.add(publisherName.toLowerCase());
			}
	
			return jdbcTemplate.query(query.toString(), args.toArray(), PUBLISHER_MAPPER);
		} catch (DataAccessException de) {
			logger.error("No publisher data found for the provided criteria.");
		} catch (Exception e) {
			logger.error("Error while fetching publisher data", e);
		}
		return null;
	}
	

	public boolean addGenre(Genre genre) {
		final UUID genreId = UUID.randomUUID();
		try {
			int rows = jdbcTemplate.update(INSERT_GENRE_DETAILS, genreId.toString(), genre.getEntityId(), genre.getEntityName().name(), genre.getGenreName(), genre.getClassificationNumber());
			if (rows != 1) {
				throw new RuntimeException("Failed to insert Genre Details");
			}
			return true;
		} catch (final DatabaseException | ApplicationException ex) {
			throw ex;
		} catch (Exception ex) {
			logger.error("Failed to add Genre Details", ex);
		}
		return false;
	}

	public boolean updateGenre(Genre genre) {
		try {
			final UUID genreId = genre.getGenreId();
			// Update genre details
			int rows = jdbcTemplate.update(UPDATE_GENRE_DETAILS,
											genre.getGenreName(),
											genre.getClassificationNumber(),
											genreId.toString(),
											genre.getEntityId(),
											genre.getEntityName().name());
			if (rows != 1) {
				throw new RuntimeException("Failed to update genre details");
			}
			return true;
	
		} catch (final DatabaseException | ApplicationException ex) {
			throw ex;
		} catch (Exception ex) {
			logger.error("Failed to update Genre Details", ex);
		}
		return false;
	}
	
	public boolean deleteGenre(String entityId, UUID genreId) {
		try {
			// Delete genre details
			int rows = jdbcTemplate.update(DELETE_GENRE_DETAILS, genreId.toString(), entityId);
			if (rows != 1) {
				throw new RuntimeException("Failed to delete genre details");
			}
			return true;
	
		} catch (final DatabaseException | ApplicationException ex) {
			throw ex;
		} catch (Exception ex) {
			logger.error("Failed to delete Genre Details", ex);
		}
		return false;
	}

	public List<Genre> getGenreData(String entityId, String genreName) {
		try {
			StringBuilder query = new StringBuilder(GET_GENRE_DETAILS);
			List<Object> args = new ArrayList<>();
			if (!StringUtils.isBlank(entityId) && StringUtils.isBlank(genreName)) {
				query.append(" genre.entity_id = 'GLOBAL' OR genre.entity_id = ?");
				args.add(entityId);
			}
			if(StringUtils.isBlank(entityId) && !StringUtils.isBlank(genreName)){
				query.append(" genre.entity_id = 'GLOBAL' AND LOWER(genre.genre_name) = ?");
				args.add(genreName.toLowerCase());
			}
			if (!StringUtils.isBlank(entityId) && !StringUtils.isBlank(genreName)) {
				query.append(" (genre.entity_id = ? OR genre.entity_id = 'GLOBAL') AND LOWER(genre.genre_name) = ?");
				args.add(entityId);
				args.add(genreName.toLowerCase());
			}
			return jdbcTemplate.query(query.toString(), args.toArray(), GENRE_MAPPER);
		} catch (DataAccessException de) {
			logger.error("No genre data found for the provided criteria.");
		} catch (Exception e) {
			logger.error("Error while fetching genre data", e);
		}
		return null;
	}	

	public boolean addVendor(int instituteId, Vendor vendor) {
		final UUID vendorId = UUID.randomUUID();
		try {
			int rows = jdbcTemplate.update(INSERT_VENDOR_DETAILS, vendorId.toString(), instituteId, vendor.getVendorName(), vendor.getAddress(), vendor.getPhoneNumber(), vendor.getEmail(), CryptoUtils.encrypt(vendor.getGstNumber()), CryptoUtils.encrypt(vendor.getAccountType()), CryptoUtils.encrypt(vendor.getBankName()), CryptoUtils.encrypt(vendor.getAccountHolderName()), CryptoUtils.encrypt(vendor.getAccountNumber()), CryptoUtils.encrypt(vendor.getIfscCode()));
			if (rows != 1) {
				throw new RuntimeException("Failed to insert Vendor Details");
			}
			return true;
		} catch (final DatabaseException | ApplicationException ex) {
			throw ex;
		} catch (Exception ex) {
			logger.error("Failed to add Vendor Details", ex);
		}
		return false;
	}

	public boolean updateVendor(int instituteId, Vendor vendor) {
		try {
			UUID vendorId = vendor.getVendorId();
			// Update vendor details
			int rows = jdbcTemplate.update(UPDATE_VENDOR_DETAILS,
											vendor.getVendorName(),
											vendor.getAddress(),
											vendor.getPhoneNumber(),
											vendor.getEmail(),
											CryptoUtils.encrypt(vendor.getGstNumber()),
											CryptoUtils.encrypt(vendor.getAccountType()),
											CryptoUtils.encrypt(vendor.getBankName()),
											CryptoUtils.encrypt(vendor.getAccountHolderName()),
											CryptoUtils.encrypt(vendor.getAccountNumber()),
											CryptoUtils.encrypt(vendor.getIfscCode()),
											vendorId.toString(),
											instituteId);
			if (rows != 1) {
				throw new RuntimeException("Failed to update vendor details");
			}
			return true;
	
		} catch (final DatabaseException | ApplicationException ex) {
			throw ex;
		} catch (Exception ex) {
			logger.error("Failed to update Vendor Details", ex);
		}
		return false;
	}
	
	public boolean deleteVendor(int instituteId, UUID vendorId) {
		try {
			// Delete vendor details
			int rows = jdbcTemplate.update(DELETE_VENDOR_DETAILS, vendorId.toString(), instituteId);
			if (rows != 1) {
				throw new RuntimeException("Failed to delete vendor details");
			}
			return true;
	
		} catch (final DatabaseException | ApplicationException ex) {
			throw ex;
		} catch (Exception ex) {
			logger.error("Failed to delete Vendor Details", ex);
		}
		return false;
	}
	
	public List<Vendor> getVendorData(int instituteId, UUID vendorId, String vendorName) {
		try {
			StringBuilder query = new StringBuilder(GET_VENDOR_DETAILS);
			List<Object> args = new ArrayList<>();
			args.add(instituteId);
	
			if (vendorId != null) {
				query.append(" AND vendor_id = ?");
				args.add(vendorId.toString());
			}

			if(!StringUtils.isBlank(vendorName)){
				query.append(" AND LOWER(vendor_name) = ?");
				args.add(vendorName.toLowerCase());
			}
	
			return jdbcTemplate.query(query.toString(), args.toArray(), VENDOR_MAPPER);
		} catch (DataAccessException de) {
			logger.error("No vendor data found for the provided criteria.");
		} catch (Exception e) {
			logger.error("Error while fetching vendor data", e);
		}
		return null;
	}
	

	public boolean addAuthor(int instituteId, Author author) {
		final UUID authorId = UUID.randomUUID();
		try {
			int rows = jdbcTemplate.update(INSERT_AUTHOR_DETAILS, authorId.toString(), instituteId, author.getAuthorName(), author.getNationality(), author.getDateOfBirth(), author.getAssociatedGeners(), author.getShortBiography());
			if (rows != 1) {
				throw new RuntimeException("Failed to insert Author Details");
			}
			return true;
		} catch (final DatabaseException | ApplicationException ex) {
			throw ex;
		} catch (Exception ex) {
			logger.error("Failed to add Author Details", ex);
		}
		return false;
	}

	public boolean updateAuthor(int instituteId, Author author) {
		try {
			UUID authorId = author.getAuthorId();
			// Update author details
			int rows = jdbcTemplate.update(UPDATE_AUTHOR_DETAILS,
											author.getAuthorName(),
											author.getNationality(),
											author.getDateOfBirth(),
											author.getAssociatedGeners(),
											author.getShortBiography(),
											authorId.toString(),
											instituteId);
			if (rows != 1) {
				throw new RuntimeException("Failed to update author details");
			}
			return true;
	
		} catch (final DatabaseException | ApplicationException ex) {
			throw ex;
		} catch (Exception ex) {
			logger.error("Failed to update Author Details", ex);
		}
		return false;
	}
	
	public boolean deleteAuthor(int instituteId, UUID authorId) {
		try {
			// Delete author details
			int rows = jdbcTemplate.update(DELETE_AUTHOR_DETAILS, authorId.toString(), instituteId);
			if (rows != 1) {
				throw new RuntimeException("Failed to delete author details");
			}
			return true;
	
		} catch (final DatabaseException | ApplicationException ex) {
			throw ex;
		} catch (Exception ex) {
			logger.error("Failed to delete Author Details", ex);
		}
		return false;
	}
	
	public List<Author> getAuthorData(int instituteId, UUID authorId, String authorName) {
		try {
			StringBuilder query = new StringBuilder(GET_AUTHOR_DETAILS);
			List<Object> args = new ArrayList<>();
			args.add(instituteId);
	
			if (authorId != null) {
				query.append(" AND author_id = ?");
				args.add(authorId.toString());
			}

			if(!StringUtils.isBlank(authorName)){
				query.append(" AND LOWER(author_name) = ?");
				args.add(authorName.toLowerCase());
			}
	
			return jdbcTemplate.query(query.toString(), args.toArray(), AUTHOR_MAPPER);
		} catch (DataAccessException de) {
			logger.error("No author data found for the provided criteria.");
		} catch (Exception e) {
			logger.error("Error while fetching author data", e);
		}
		return null;
	}
	

	public boolean addLibraryType(int instituteId, LibraryType libraryType) {
		final UUID libraryTypeId = UUID.randomUUID();
		try {
			int rows = jdbcTemplate.update(INSERT_LIBRARY_TYPE_DETAILS, libraryTypeId.toString(), instituteId, libraryType.getLibraryName());
			if (rows != 1) {
				throw new RuntimeException("Failed to insert Library Type Details");
			}
			return true;
		} catch (final DatabaseException | ApplicationException ex) {
			throw ex;
		} catch (Exception ex) {
			logger.error("Failed to add Library Type Details", ex);
		}
		return false;
	}

	public boolean updateLibraryType(int instituteId, LibraryType libraryType) {
		try {
			UUID libraryTypeId = libraryType.getLibraryTypeId();
			// Update library type details
			int rows = jdbcTemplate.update(UPDATE_LIBRARY_TYPE_DETAILS,
											libraryType.getLibraryName(),
											libraryTypeId.toString(),
											instituteId);
			if (rows != 1) {
				throw new RuntimeException("Failed to update library type details");
			}
			return true;
	
		} catch (final DatabaseException | ApplicationException ex) {
			throw ex;
		} catch (Exception ex) {
			logger.error("Failed to update Library Type Details", ex);
		}
		return false;
	}
	
	public boolean deleteLibraryType(int instituteId, UUID libraryTypeId) {
		try {
			// Delete library type details
			int rows = jdbcTemplate.update(DELETE_LIBRARY_TYPE_DETAILS, libraryTypeId.toString(), instituteId);
			if (rows != 1) {
				throw new RuntimeException("Failed to delete library type details");
			}
			return true;
	
		} catch (final DatabaseException | ApplicationException ex) {
			throw ex;
		} catch (Exception ex) {
			logger.error("Failed to delete Library Type Details", ex);
		}
		return false;
	}
	
	public List<LibraryType> getLibraryTypeData(int instituteId, UUID libraryTypeId, String libraryTypeName) {
		try {
			StringBuilder query = new StringBuilder(GET_LIBRARY_TYPE_DETAILS);
			List<Object> args = new ArrayList<>();
			args.add(instituteId);
	
			if (libraryTypeId != null) {
				query.append(" AND library_type_id = ?");
				args.add(libraryTypeId.toString());
			}

			if(!StringUtils.isBlank(libraryTypeName)){
				query.append(" AND LOWER(library_type_name) = ?");
				args.add(libraryTypeName.toLowerCase());
			}
	
			return jdbcTemplate.query(query.toString(), args.toArray(), LIBRARY_TYPE_MAPPER);
		} catch (DataAccessException de) {
			logger.error("No library type data found for the provided criteria.");
		} catch (Exception e) {
			logger.error("Error while fetching library type data", e);
		}
		return null;
	}
	


	/*
	 * TODO Make sure not to have duplicate entries for same type of book
	 * 
	 */

	public UUID addBook(BookPayloadWrapper bookPayloadWrapper) {
		final UUID bookId = UUID.randomUUID();
		try {
			BookDetailsPayload bookDetails = bookPayloadWrapper.getBookDetailPayload();
			
			Set<String> tags = bookPayloadWrapper.getTags();
			String tagsString = null;
			if (!CollectionUtils.isEmpty(tags)) {
				tagsString = String.join(", ", tags);
			}
			// Insert book details
			int rows = jdbcTemplate.update(INSERT_BOOK_DETAILS, bookId.toString(),
					bookDetails.getInstituteId(), bookDetails.getGenreId() != null ? bookDetails.getGenreId().toString() : null,
					bookDetails.getBookTitle(), bookDetails.getBookNo(), tagsString, bookDetails.getAuthorId() != null ? bookDetails.getAuthorId().toString() : null, bookDetails.getIsbn(),
					bookDetails.getPublicationId() != null ? bookDetails.getPublicationId().toString() : null, bookDetails.getPublisherId() != null ? bookDetails.getPublisherId().toString() : null, bookDetails.getEdition(), bookDetails.getNoOfCopies(),
					bookDetails.getPublishYear(), bookDetails.getLanguage(),
					bookDetails.getTypeOfBinding(), bookDetails.getNumberOfPages());
					
			if(rows != 1){
				throw new RuntimeException("Failed to insert Book Details");
			}
			
			List<IndividualBookDetailsPayload> individualBookDetailsPayloads = bookPayloadWrapper.getIndividualBookDetailsPayloads();
			if(CollectionUtils.isEmpty(individualBookDetailsPayloads)){
				throw new RuntimeException("Failed to insert book details");
			}
			boolean addIndividualBookDetails = addIndividualBookDetails(bookDetails.getInstituteId(), bookId, individualBookDetailsPayloads);
			
			if(!addIndividualBookDetails){
				throw new RuntimeException("Failed to insert Individual Book Book Details");
			}

			return bookId;
			
		}catch (final DatabaseException | ApplicationException ex) {
			throw ex;
		} catch (Exception ex) {
			logger.error("Failed to add book", ex); // Rethrow for proper error handling
			throw ex;
		}
	}

	public boolean updateBook(BookPayloadWrapper bookWrapper) {
		return transactionTemplate.execute(new TransactionCallback<Boolean>() {
			public Boolean doInTransaction(TransactionStatus status) {
				try {
					BookDetailsPayload bookDetails = bookWrapper.getBookDetailPayload();
					UUID bookId = bookDetails.getBookId();
					Set<String> tags = bookWrapper.getTags();
					String tagsString = null;
					if (!CollectionUtils.isEmpty(tags)) {
						tagsString = String.join(", ", tags);
					}
					// Update book details
					int rows = jdbcTemplate.update(UPDATE_BOOK_DETAILS,
							bookDetails.getGenreId() != null ? bookDetails.getGenreId().toString() : null,
							bookDetails.getBookTitle(),
							bookDetails.getBookNo(),
							tagsString,
							bookDetails.getAuthorId() != null ? bookDetails.getAuthorId().toString() : null,
							bookDetails.getIsbn(),
							bookDetails.getPublicationId() != null ? bookDetails.getPublicationId().toString() : null,
							bookDetails.getPublisherId() != null ? bookDetails.getPublisherId().toString() : null,
							bookDetails.getEdition(),
							bookDetails.getPublishYear(),
							bookDetails.getLanguage(),
							bookDetails.getTypeOfBinding(),
							bookDetails.getNumberOfPages(),        
							bookDetails.getInstituteId(),
							bookId.toString());   
	
					if (rows != 1) {
						throw new RuntimeException("Failed to update Book Details");
					}

					List<IndividualBookDetailsPayload> individualBookDetailsPayloads = bookWrapper.getIndividualBookDetailsPayloads();
					if(!CollectionUtils.isEmpty(individualBookDetailsPayloads)){
						boolean updateIndividualBookDetails = updateIndividualBookDetails(bookDetails.getInstituteId(), individualBookDetailsPayloads);
						if(!updateIndividualBookDetails){
							throw new RuntimeException("Failed to update Individual Book Details");
						}
					}
					return true;
	
				}catch (final DatabaseException | ApplicationException ex) {
					throw ex;
				} catch (Exception ex) {
					throw new RuntimeException("Failed to update book", ex); // Rethrow for proper error handling
				}
			}
		});
	}

	public boolean updateNumberOfBook(int instituteId, UUID bookId, int numberOfCopies, List<IndividualBookDetailsPayload> individualBookDetailsPayloads, List<UUID> accessionIdList, DataUpdationAction dataUpdationAction){
		try{
			int rows = jdbcTemplate.update(UPDATE_BOOK_NUMBER_OF_COPIES,
					numberOfCopies,
					instituteId,
					bookId.toString());

			switch (dataUpdationAction) {
				case ADD: {
					boolean addIndividualBook = addIndividualBookDetails(instituteId, individualBookDetailsPayloads.get(0).getBookId(), individualBookDetailsPayloads);
					if (rows != 1 || !addIndividualBook) {
						throw new RuntimeException("Failed to update Book Details");
					}
					return true;
				}
				case DELETE: {
					boolean deleteIndividualBook = deleteIndividualBookDetails(instituteId, accessionIdList);
					if (rows != 1 || !deleteIndividualBook) {
						throw new RuntimeException("Failed to update Book Details");
					}
					return true;
				}
				default:
					break;
			}
		}catch (final DatabaseException | ApplicationException ex) {
			throw ex;
		} catch (Exception ex) {
			logger.error("Failed to update book", ex); // Rethrow for proper error handling
			throw ex;
		}
		return false;
	}

	public int getNumberOfBookCopies(int instituteId, UUID bookId){
		
		List<Object> args = new ArrayList<>();
		args.add(instituteId);
		args.add(bookId.toString());

		return jdbcTemplate.queryForObject(GET_NUMBER_OF_BOOK_COPIES, args.toArray(), Integer.class);
	}

	public int getCountOfIssuedBook(int instituteId, List<UUID> accessionIdList){
		if (CollectionUtils.isEmpty(accessionIdList)) {
			throw new RuntimeException("No Book Details found");
		}
		StringBuilder query = new StringBuilder(COUNT_ISSUED_BOOK_DATA);
		query.append(" AND accession_id IN (");
		query.append(PlaceholdersUtils.buildPlaceholders(accessionIdList.size()));
		query.append(")");
		List<Object> args = new ArrayList<>();
		args.add(instituteId);
		for(UUID accessionId : accessionIdList){
			args.add(accessionId.toString());
		}
		return jdbcTemplate.queryForObject(query.toString(), args.toArray(), Integer.class);
	}

	public boolean deleteBook(int instituteId, UUID bookId) {
		return transactionTemplate.execute(new TransactionCallback<Boolean>() {
			public Boolean doInTransaction(TransactionStatus status) {
				try {
					jdbcTemplate.update(DELETE_INDIVIDUAL_BOOK_DETAILS, instituteId, bookId.toString());
					// Delete the book details where both book_id and institute_id match
					int rows = jdbcTemplate.update(DELETE_BOOK_DETAILS,
							instituteId, bookId.toString());
	
					// Check if the deletion was successful
					if (rows != 1) {
						throw new RuntimeException("Failed to delete Book Details") ;
					}
	
					return true;
	
				}catch (final DatabaseException | ApplicationException ex) {
					throw ex;
				} catch (Exception ex) {
					logger.error("Failed to delete book", ex); // Rethrow for proper error handling
				}
				return false;
			}
		});
	}

	public boolean addIndividualBookDetails(int instituteId, UUID bookId, List<IndividualBookDetailsPayload> individualBookDetailsPayloads){
		try{
			List<Object[]> individualBookDetailsPayloadList = new ArrayList<>();
			for(IndividualBookDetailsPayload individualBookDetailsPayload : individualBookDetailsPayloads){
				final UUID accessionId = UUID.randomUUID();
				Timestamp purchaseDate = null;
				if(individualBookDetailsPayload.getDateOfPurchase() != null){
					purchaseDate = new Timestamp((long)individualBookDetailsPayload.getDateOfPurchase() * 1000L);
				}
				individualBookDetailsPayloadList.add(new Object[]{accessionId.toString(), instituteId, bookId.toString(), individualBookDetailsPayload.getAccessionNumber(), individualBookDetailsPayload.getRack(), individualBookDetailsPayload.getPrice(), individualBookDetailsPayload.getBillNumber(), purchaseDate, individualBookDetailsPayload.getVendorId() != null ? individualBookDetailsPayload.getVendorId().toString() : null, individualBookDetailsPayload.getStatus().name(), individualBookDetailsPayload.getVolume(), individualBookDetailsPayload.getLibraryTypeId() != null ? individualBookDetailsPayload.getLibraryTypeId().toString() : null, individualBookDetailsPayload.getRemarks()});
			}
			int[] individualBookDetailsPayloadRows = jdbcTemplate.batchUpdate(INSERT_INDIVIDUAL_BOOK_DETAILS, individualBookDetailsPayloadList);
			if(individualBookDetailsPayloadRows.length != individualBookDetailsPayloadList.size()){
				throw new RuntimeException("Failed to Add individual book details");
			}
			return true;
		}catch (DuplicateKeyException e) {
			logger.error("Duplicate Accession Number detected", e);
			throw new RuntimeException("Duplicate Accession Number not allowed.", e);
    	}
		catch (final DatabaseException | ApplicationException ex) {
			throw ex;
		} catch (Exception ex) {
			logger.error("Failed to insert Accession Number book", ex); // Rethrow for proper error handling
			throw ex;
		}
	}

	public boolean updateIndividualBookDetails(int instituteId, List<IndividualBookDetailsPayload> individualBookDetailsPayloads){
		try{
			List<Object[]> individualBookDetailsPayloadList = new ArrayList<>();
			for(IndividualBookDetailsPayload individualBookDetailsPayload : individualBookDetailsPayloads){
				individualBookDetailsPayloadList.add(new Object[]{ individualBookDetailsPayload.getAccessionNumber(), individualBookDetailsPayload.getRack(), individualBookDetailsPayload.getPrice(), individualBookDetailsPayload.getBillNumber(), individualBookDetailsPayload.getDateOfPurchase() != null ? new Timestamp((long)individualBookDetailsPayload.getDateOfPurchase() * 1000L) : null, individualBookDetailsPayload.getVendorId() != null ? individualBookDetailsPayload.getVendorId().toString() : null, individualBookDetailsPayload.getStatus().name(), individualBookDetailsPayload.getVolume(), individualBookDetailsPayload.getLibraryTypeId() != null ? individualBookDetailsPayload.getLibraryTypeId().toString() : null, individualBookDetailsPayload.getRemarks(), instituteId, individualBookDetailsPayload.getAccessionId().toString()});
			}
			int[] individualBookDetailsPayloadRows = jdbcTemplate.batchUpdate(UPDATE_INDIVIDUAL_BOOK_DETAILS, individualBookDetailsPayloadList);
			if(individualBookDetailsPayloadRows.length != individualBookDetailsPayloadList.size()){
				throw new RuntimeException("Failed to Update individual book details");
			}
			return true;
		}catch (DuplicateKeyException e) {
			logger.error("Duplicate Accession Number detected", e);
			throw new RuntimeException("Duplicate Accession Number not allowed.", e);
    	}
		catch (final DatabaseException | ApplicationException ex) {
			throw ex;
		} catch (Exception ex) {
			logger.error("Failed to update Accession Number book", ex); // Rethrow for proper error handling
		}
		return false;
	}
	 public boolean deleteIndividualBookDetails(int instituteId, List<UUID> accessionIdList){
		if (CollectionUtils.isEmpty(accessionIdList)) {
			throw new RuntimeException("No Book Details found");
		}
		StringBuilder query = new StringBuilder(DELETE_INDIVIDUAL_BOOK_DETAIL);
		query.append(" AND accession_id IN (");
		query.append(PlaceholdersUtils.buildPlaceholders(accessionIdList.size()));
		query.append(")");

		List<Object> args = new ArrayList<>();
		args.add(instituteId);
		for(UUID accessionId : accessionIdList){
			args.add(accessionId.toString());
		}
		int rowsAffected = jdbcTemplate.update(query.toString(), args.toArray());
		return rowsAffected > 0;
	 }

	public boolean updateDocuments(int instituteId, UUID bookId, List<Document<BookDocumentType>> bookDocuments){
		try{
			final Object[] args = {GSON.toJson(bookDocuments), instituteId, bookId.toString()};
			return jdbcTemplate.update(UPDATE_BOOK_DOCUMENTS, args) == 1;
		}catch (final DataAccessException dataAccessException) {
			ExceptionHandling.HandleException(dataAccessException, null, null);
			logger.error("Error while updating book documents {}", bookId);
		} catch (final Exception e) {
			logger.error("Exception while updating book documents {}", bookId, e);
		}
		return false;
	}

	public boolean updateLibraryLedgerDate(int instituteId, int academicSessionId, UUID transactionId, Integer issueDate, IssueStatus issueStatus){
		Timestamp currentTimestamp = new Timestamp(System.currentTimeMillis());
		try{
			final Object[] args = {new Timestamp((long)issueDate * 1000L), currentTimestamp, instituteId, academicSessionId, transactionId.toString()};
			if(issueStatus == IssueStatus.ISSUED){
				return jdbcTemplate.update(UPDATE_ISSUED_BOOK_DATE, args) == 1;
			}
			if(issueStatus == IssueStatus.RETURNED){
				return jdbcTemplate.update(UPDATE_RETURNED_BOOK_DATE, args) == 1;
			}
		}catch(final Exception e){
			logger.error("Failed to to update date book", e);
		}
		return false;
	}

	public List<LibraryLedgerPayload> getLibraryLedgerData(int instituteId, Integer academicSessionId, IssueStatus issueStatus, UUID bookId, UUID transactionId) {
		try {
			StringBuilder query = new StringBuilder(BOOK_ISSUED_STATUS);
			List<Object> args = new ArrayList<>();
			args.add(instituteId);
			args.add(issueStatus.name());
			if(academicSessionId != null){
				query.append(" AND academic_session_id = ?");
				args.add(academicSessionId);
			}
			if(transactionId != null){
				query.append(" AND transaction_id = ?");
				args.add(transactionId.toString());
			}
			if(bookId != null){
				query.append(" AND book_id = ?");
				args.add(bookId.toString());
			}
			return jdbcTemplate.query(query.toString(), args.toArray(), LIBRARY_LEDGER_MAPPER);
		} catch (DataAccessException de) {
			logger.error("book not issued to any users ");
		} catch (Exception e) {
			logger.error("Error message", e);
		}
		return null;
	}

	public boolean issueBooks(final List<IssueBookPayload> issueBookPayloadList) {
		try{
			final List<Object[]> batchInsertArgs = new ArrayList<>();
		int count = 0;
		for(IssueBookPayload issueBookPayload : issueBookPayloadList) {
			final List<Object> args2 = new ArrayList<>();
			UUID transactionId = UUID.randomUUID();

			args2.add(issueBookPayload.getInstituteId());
			args2.add(issueBookPayload.getAcademicSessionId());
			args2.add(transactionId.toString());
			args2.add(issueBookPayload.getIssuedToUserId().toString());
			args2.add(issueBookPayload.getIssuedToUserType().toString());
			args2.add(issueBookPayload.getBookId().toString());
			args2.add(issueBookPayload.getAccessionId().toString());
			args2.add(issueBookPayload.getDuration());
			args2.add(new Timestamp(issueBookPayload.getIssuedAt() * 1000l));
			args2.add(issueBookPayload.getIssuedBy().toString());
			args2.add(IssueStatus.ISSUED.name());				
			count++;
			batchInsertArgs.add(args2.toArray());
		}
		final int[] rows = jdbcTemplate.batchUpdate(ISSUE_BOOK, batchInsertArgs);

		// If any of the books fails to be issued, rollback the entire transaction
		if (rows.length != count) {
			throw new RuntimeException("Failed to issue book") ;
		}
		for (final int rowCount : rows) {
			if (rowCount != 1) {
				return false;
			}
		}
		// If all books were successfully issued, return true
		return true;
		}catch (Exception ex) {
			logger.error("Failed to issue book", ex); 
		}
		return false;
	}
	

	public SearchResultWithPagination<BookDetails> getBookDetails(int instituteId, UUID bookId, String searchText, Integer offSet, Integer limit){
		
		StringBuilder query = new StringBuilder(GET_BOOK_DETAILS);
		StringBuilder countQuery = new StringBuilder(COUNT_GET_BOOK_DETAILS);

		List<Object> args = new ArrayList<>();
		args.add(instituteId);
		if(bookId != null){
			query.append(WHERE_CLAUSE_BOOK_ID);
			countQuery.append(COUNT_WHERE_CLAUSE_BOOK_ID);
			args.add(bookId.toString());
		}else{
			query.append(WHERE_CLAUSE_WITH_GROUP_STRING);
			countQuery.append(COUNT_WHERE_CLAUSE_WITH_GROUP_STRING);
		}
		if (!StringUtils.isBlank(searchText)) {
			final String[] keywords = searchText.toLowerCase().split(" ");
			boolean first = true;
			for (String keyword : keywords) {
				keyword = keyword.trim();
				if (StringUtils.isBlank(keyword)) {
					continue;
				}
				if(first){
					query.append("HAVING").append(BOOK_SEARCH_CONDITION);
					countQuery.append("HAVING").append(COUNT_BOOK_SEARCH_CONDITION);
					first = false;
				}
				else{
					query.append("AND").append(BOOK_SEARCH_CONDITION);
					countQuery.append("AND").append(COUNT_BOOK_SEARCH_CONDITION);
				}
				args.add("%" + keyword + "%");
			}
		}
		countQuery.append(GROUP_NAME_STRING);
		final int totalResultCount = jdbcTemplate.queryForObject(countQuery.toString(), Integer.class, args.toArray());

		final PaginationInfo paginationInfo = new PaginationInfo(totalResultCount, limit == null ? 0 : limit, offSet == null ? 0 : offSet);
			
		if (limit != null && limit > 0) {
			query.append(" LIMIT ?");
			args.add(limit);
		}
		if (offSet != null && offSet >= 0) {
			query.append(" OFFSET ?");
			args.add(offSet);
		}
		List<BookDetailsRow> bookDetailsMapperDataList = jdbcTemplate.query(query.toString(), args.toArray(), BOOK_DETAILS_MAPPER);
		List<BookDetails> bookDetailsList = BookDetailsMapper.getBookDetailsList(bookDetailsMapperDataList);
		return new SearchResultWithPagination<>(paginationInfo, bookDetailsList);
	}
	public SearchResultWithPagination<IssuedBook> getIssuedBooks(int instituteId, Integer academicSessionId, UUID bookId , IssueStatus status, Set<UUID> issuedTo, Integer issuedTimestamp, Set<UUID> issuedBy, Set<UUID> receivedBy, Integer offSet, Integer limit) {
		try {
			StringBuilder query = new StringBuilder(GET_ISSUED_BOOKS_FOR_USER);
			StringBuilder countQuery = new StringBuilder(COUNT_GET_ISSUED_BOOKS_FOR_USER);

			List<Object> args = new ArrayList<>();
			// Add instituteId and academicSessionId as parameters
			args.add(instituteId);
			// adding condition to query based on status
			if(academicSessionId != null){
				query.append(" AND library_ledger.academic_session_id = ?");
				countQuery.append(" AND library_ledger.academic_session_id = ?");
				args.add(academicSessionId);
			}
			if(status != null){
				query.append(" AND library_ledger.status = ? ");
				countQuery.append(" AND library_ledger.status = ? ");
				args.add(status.name());
			}
			// Adding conditions and placeholders to the query based on issuedBy
			if (!CollectionUtils.isEmpty(issuedBy)) {
				query.append(" AND library_ledger.issued_by IN (");
				query.append(PlaceholdersUtils.buildPlaceholders(issuedBy.size()));
				query.append(")");
				countQuery.append(" AND library_ledger.issued_by IN (");
				countQuery.append(PlaceholdersUtils.buildPlaceholders(issuedBy.size()));
				countQuery.append(")");
				for(UUID issuedby : issuedBy){
					args.add(issuedby.toString());
				}
			}
	
			// Adding conditions and placeholders to the query based on receivedBy
			if (!CollectionUtils.isEmpty(receivedBy)) {
				query.append(" AND library_ledger.received_by IN (");
				query.append(PlaceholdersUtils.buildPlaceholders(receivedBy.size()));
				query.append(")");
				countQuery.append(" AND library_ledger.received_by IN (");
				countQuery.append(PlaceholdersUtils.buildPlaceholders(receivedBy.size()));
				countQuery.append(")");
				for(UUID receivedby : receivedBy){
					args.add(receivedby.toString());
				}
			}
	
			// Adding conditions and placeholders to the query based on issuedTo
			if (!CollectionUtils.isEmpty(issuedTo)) {
				query.append(" AND library_ledger.user_id IN (");
				query.append(PlaceholdersUtils.buildPlaceholders(issuedTo.size()));
				query.append(")");
				countQuery.append(" AND library_ledger.user_id IN (");
				countQuery.append(PlaceholdersUtils.buildPlaceholders(issuedTo.size()));
				countQuery.append(")");
				for(UUID issuedto : issuedTo){
					args.add(issuedto.toString());
				}
			}
			// adding condition to query based on book id
			if(bookId != null){
				query.append(" AND library_ledger.book_id = ?");
				countQuery.append(" AND library_ledger.book_id = ?");
				args.add(bookId.toString());
			}
			if(issuedTimestamp != null && issuedTimestamp >0){
				query.append(" AND library_ledger.issued_timestamp >= ? AND library_ledger.issued_timestamp <= ?");
				countQuery.append(" AND library_ledger.issued_timestamp >= ? AND library_ledger.issued_timestamp <= ?");
				args.add(new Timestamp(DateUtils.getDayStart(issuedTimestamp, DateUtils.DEFAULT_TIMEZONE) * 1000L));
				args.add(new Timestamp(DateUtils.getDayEnd(issuedTimestamp, DateUtils.DEFAULT_TIMEZONE) * 1000L));
			}
			final int totalResultCount = jdbcTemplate.queryForObject(countQuery.toString(), Integer.class, args.toArray());
			
			final PaginationInfo paginationInfo = new PaginationInfo(totalResultCount, limit == null ? 0 : limit, offSet == null ? 0 : offSet);
		
			if (limit != null && limit > 0) {
				query.append(" LIMIT ?");
				args.add(limit);
			}
			if (offSet != null && offSet >= 0) {
				query.append(" OFFSET ?");
				args.add(offSet);
			}
			// Execute the query with the arguments
			List<IssuedBook> issuedBooksList = jdbcTemplate.query(query.toString(), args.toArray(), ISSUE_BOOK_MAPPER);
			
			return new SearchResultWithPagination<>(paginationInfo, issuedBooksList);
			
	
		} catch (DataAccessException de) {
			logger.error("No book issued to users " + issuedBy);
		} catch (Exception e) {
			logger.error("Failed to get issued book "+ e);
			
		}
		return null;
	}

	public boolean returnBooks(ReturnBookPayload returnBookPayload) {
		return transactionTemplate.execute(new TransactionCallback<Boolean>() {
			public Boolean doInTransaction(TransactionStatus status) {
				try{
					final Set<UUID> transactionIdSet = returnBookPayload.getTransactionIds();
					Timestamp currentTimestamp = new Timestamp(System.currentTimeMillis());

					List<Object[]> batchArgs = new ArrayList<>();
					for (UUID transactionId : transactionIdSet) {
						batchArgs.add(new Object[] {
							returnBookPayload.getReceivedBy().toString(),
							new Timestamp(returnBookPayload.getReturnDate() * 1000L),
							currentTimestamp,
							returnBookPayload.getIssueStatus().name(),
							returnBookPayload.getInstituteId(),
							returnBookPayload.getAcademicSessionId(),
							transactionId.toString()
						});
					}
		
					// Execute batch update
					int[] rowsAffected = jdbcTemplate.batchUpdate(RETURN_BOOK, batchArgs);
		
					for (int row : rowsAffected) {
						if (row != 1) {
							return false; 
						}
					}
					return true; 
				}catch (final DatabaseException | ApplicationException ex) {
					throw ex;
				} catch (final Exception e) {
					logger.error("Unable to execute transaction", e);
				}
				return null;
			}
		});
	}
	public boolean deleteIssuedBook(int instituteId, int academicSessionId, UUID transactionId) {
		try {
				// Delete the Issued book details 
				int rows = jdbcTemplate.update(DELETE_LIBRARY_LEDGER_DATA,
						instituteId, academicSessionId, transactionId.toString());
	
				// Check if the deletion was successful
				if (rows != 1) {
					throw new DataAccessException("Failed to delete Issued Book Details") {};
				}
	
				return true;
	
			} catch (Exception ex) {
				logger.error("Failed to delete book", ex); 
			}
			return false;
	}	
}
