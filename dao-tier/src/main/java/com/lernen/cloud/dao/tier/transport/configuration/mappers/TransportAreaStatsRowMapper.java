package com.lernen.cloud.dao.tier.transport.configuration.mappers;

import com.lernen.cloud.core.api.transport.TransportArea;
import com.lernen.cloud.core.api.transport.TransportAreaStats;
import com.lernen.cloud.core.api.transport.TransportServiceRouteResponse;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.UUID;

/**
 *
 * <AUTHOR>
 *
 */
public class TransportAreaStatsRowMapper implements RowMapper<TransportAreaStats> {

	private static final String STUDENT_COUNT = "area_count.student_count";
	private static final TransportAreaRowMapper TRANSPORT_AREA_ROW_MAPPER = new TransportAreaRowMapper();
	@Override
	public TransportAreaStats mapRow(ResultSet rs, int rowNum) throws SQLException {
		TransportArea transportArea = TRANSPORT_AREA_ROW_MAPPER.mapRow(rs, rowNum);
		return new TransportAreaStats(transportArea, rs.getInt(STUDENT_COUNT));
	}
}
