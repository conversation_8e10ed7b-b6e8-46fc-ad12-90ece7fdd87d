package com.lernen.cloud.dao.tier.assessment.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.UUID;

import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.assessment.QuestionOption;

/**
 * Mapper class for QuestionOption entity.
 */
public class QuestionOptionMapper implements RowMapper<QuestionOption> {

    private static final String OPTIONS_ID = "question_options.options_id";
    private static final String QUESTION_ID = "question_options.question_id";
    private static final String OPTION_TEXT = "question_options.option_text";

    @Override
    public QuestionOption mapRow(ResultSet rs, int rowNum) throws SQLException {
        if (rs.getString(OPTIONS_ID) == null || rs.getString(QUESTION_ID) == null) {
            return null;
        }

        return new QuestionOption(
            UUID.fromString(rs.getString(OPTIONS_ID)),
            UUID.fromString(rs.getString(QUESTION_ID)),
            rs.getString(OPTION_TEXT)
        );
    }
}
