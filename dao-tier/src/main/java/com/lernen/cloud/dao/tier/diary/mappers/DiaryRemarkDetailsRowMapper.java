package com.lernen.cloud.dao.tier.diary.mappers;

import com.amazonaws.util.CollectionUtils;
import com.embrate.cloud.core.api.homework.HomeworkDocumentType;
import com.google.gson.reflect.TypeToken;
import com.lernen.cloud.core.api.diary.*;

import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.student.StudentSessionDataLite;
import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserLite;
import com.lernen.cloud.dao.tier.student.mappers.StudentRowMapper;
import com.lernen.cloud.dao.tier.user.mappers.UserRowMapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.jdbc.core.RowMapper;

import java.lang.reflect.Type;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;

import static com.lernen.cloud.core.utils.SharedConstants.GSON;

public class DiaryRemarkDetailsRowMapper implements RowMapper<DiaryRemarkDetailsRow> {
    private static final Logger logger = LogManager.getLogger(DiaryRemarkDetailsRowMapper.class);

    private static final UserRowMapper USER_ROW_MAPPER = new UserRowMapper();
    private static final DiaryRemarkCategoryRowMapper DIARY_REMARK_CATEGORY_ROW_MAPPER = new DiaryRemarkCategoryRowMapper();
    private static final StudentRowMapper STUDENT_ROW_MAPPER = new StudentRowMapper();

    private static final String INSTITUTE_ID = "remark_details.institute_id";
    private static final String ACADEMIC_SESSION_ID = "remark_details.academic_session_id";
    private static final String DATE = "remark_details.date";
    private static final String REMARK_ID = "remark_details.remark_id";
    private static final String TITLE = "remark_details.title";
    private static final String DESCRIPTION = "remark_details.description";

    protected static final String ATTACHMENTS = "remark_details.attachments";

    protected static final String REMARK_USER_TYPE = "remark_details.remark_user_type";

    @Override
    public DiaryRemarkDetailsRow mapRow(ResultSet rs, int rowNum) throws SQLException {
        final User user = USER_ROW_MAPPER.mapRow(rs, rowNum);
        final UserLite userLite = User.getUserLite(user);
        final DiaryRemarkCategory diaryRemarkCategory = DIARY_REMARK_CATEGORY_ROW_MAPPER.mapRow(rs, rowNum);
        final Student student = STUDENT_ROW_MAPPER.mapRow(rs, rowNum);

        final Timestamp remarkDate = rs.getTimestamp(DATE);
        final Integer remarkDateTime = remarkDate == null ? null
                : (int) (remarkDate.getTime() / 1000l);
        StudentLite studentLite = Student.getStudentLite(student);

        int attachmentCount = 10;
        long attachmentSize = 1024 * 1024 * 5;
        String allowedMimeTypes = "image/*,application/pdf";

        final String documents = rs.getString(ATTACHMENTS);
        List<Document<DiaryRemarkDocumentType>> diaryRemarkAttachments = null;
        if (!StringUtils.isBlank(documents)) {
            final Type collectionType = new TypeToken<List<Document<DiaryRemarkDocumentType>>>() {
            }.getType();
            diaryRemarkAttachments = GSON.fromJson(documents, collectionType);
        }

        return new DiaryRemarkDetailsRow(rs.getInt(INSTITUTE_ID), rs.getInt(ACADEMIC_SESSION_ID), remarkDateTime, UUID.fromString(rs.getString(REMARK_ID)),
                rs.getString(TITLE), rs.getString(DESCRIPTION), studentLite, RemarkUserType.getRemarkUserType(rs.getString(REMARK_USER_TYPE)),
                userLite, diaryRemarkCategory, attachmentCount, attachmentSize, allowedMimeTypes, diaryRemarkAttachments);
    }

    public static List<DiaryRemarkDetails> diaryRemarkStudentsDataList(List<DiaryRemarkDetailsRow> diaryRemarkDetailsRows) {
        if(CollectionUtils.isNullOrEmpty(diaryRemarkDetailsRows)) {
            return null;
        }
        Map<UUID, List<StudentLite>> remarkStudentMap = new HashMap<>();
        Map<UUID, List<StudentLite>> remarkStudentIdMap = new HashMap<>();
        List<DiaryRemarkDetails> diaryRemarkDetails = new ArrayList<>();
        for (DiaryRemarkDetailsRow diaryRemarkDetailsRow : diaryRemarkDetailsRows) {
            StudentLite studentLite = diaryRemarkDetailsRow.getStudentLite();
            UUID studentId = studentLite.getStudentId();
            UUID remarkId = diaryRemarkDetailsRow.getRemarkId();
            if (remarkStudentMap.containsKey(remarkId)) {
                if (!remarkStudentIdMap.containsKey(studentId)) {
                    List<StudentLite> studentLites = remarkStudentMap.get(remarkId);
                    remarkStudentIdMap.put(studentId, studentLites);
                    studentLites.add(studentLite);
                }
            } else {
                remarkStudentIdMap = new HashMap<>();
                List<StudentLite> studentLites = new ArrayList<>();
                studentLites.add(studentLite);
                remarkStudentIdMap.put(studentId, studentLites);
                remarkStudentMap.put(remarkId, studentLites);
                diaryRemarkDetails.add(new DiaryRemarkDetails(diaryRemarkDetailsRow.getInstituteId(), diaryRemarkDetailsRow.getAcademicSessionId(),diaryRemarkDetailsRow.getDate(), diaryRemarkDetailsRow.getRemarkId(),
                        diaryRemarkDetailsRow.getTitle(), diaryRemarkDetailsRow.getDescription(),
                        remarkStudentMap.get(remarkId), diaryRemarkDetailsRow.getRemarkUserType(), diaryRemarkDetailsRow.getCreatedByUser(), diaryRemarkDetailsRow.getDiaryRemarkCategory(),
                        diaryRemarkDetailsRow.getAttachmentCount(), diaryRemarkDetailsRow.getAttachmentSize(),
                        diaryRemarkDetailsRow.getAllowedMimeTypes(), diaryRemarkDetailsRow.getDiaryRemarkAttachments()));
            }
        }
        return diaryRemarkDetails;
    }

    public static List<DiaryRemarkDetails> diaryRemarkSingleStudentsDataList(List<DiaryRemarkDetailsRow> diaryRemarkDetailsRows){
        if(CollectionUtils.isNullOrEmpty(diaryRemarkDetailsRows)) {
            return null;
        }
        Map<UUID, List<StudentLite>> remarkStudentMap = new HashMap<>();
        Map<UUID, List<StudentLite>> remarkStudentIdMap = new HashMap<>();
        List<DiaryRemarkDetails> diaryRemarkDetails = new ArrayList<>();
        for (DiaryRemarkDetailsRow diaryRemarkDetailsRow : diaryRemarkDetailsRows) {
            StudentLite studentLite = diaryRemarkDetailsRow.getStudentLite();
            UUID studentId = studentLite.getStudentId();
            UUID remarkId = diaryRemarkDetailsRow.getRemarkId();
            if (remarkStudentMap.containsKey(remarkId)) {
                if (!remarkStudentIdMap.containsKey(studentId)) {
                    List<StudentLite> studentLites = remarkStudentMap.get(remarkId);
                    remarkStudentIdMap.put(studentId, studentLites);
                    studentLites.add(studentLite);
                }
            } else {
                List<StudentLite> studentLites = new ArrayList<>();
                studentLites.add(studentLite);
                remarkStudentIdMap.put(studentId, studentLites);
                remarkStudentMap.put(remarkId, studentLites);
                diaryRemarkDetails.add(new DiaryRemarkDetails(diaryRemarkDetailsRow.getInstituteId(), diaryRemarkDetailsRow.getAcademicSessionId(),diaryRemarkDetailsRow.getDate(), diaryRemarkDetailsRow.getRemarkId(),
                        diaryRemarkDetailsRow.getTitle(), diaryRemarkDetailsRow.getDescription(),
                        remarkStudentMap.get(remarkId), diaryRemarkDetailsRow.getRemarkUserType(), diaryRemarkDetailsRow.getCreatedByUser(), diaryRemarkDetailsRow.getDiaryRemarkCategory(),
                        diaryRemarkDetailsRow.getAttachmentCount(), diaryRemarkDetailsRow.getAttachmentSize(),
                        diaryRemarkDetailsRow.getAllowedMimeTypes(), diaryRemarkDetailsRow.getDiaryRemarkAttachments()));
            }
        }
        return diaryRemarkDetails;
    }

    public static DiaryRemarkDetails getDiaryRemarkDetailOfARemark(List<DiaryRemarkDetailsRow> diaryRemarkDetailsRows) {
        if(CollectionUtils.isNullOrEmpty(diaryRemarkDetailsRows)) {
            return null;
        }
        Map<UUID, StudentLite> studentLiteMap = new HashMap<>();
        for (DiaryRemarkDetailsRow diaryRemarkDetailsRow : diaryRemarkDetailsRows) {
            StudentLite studentLite = diaryRemarkDetailsRow.getStudentLite();
            UUID studentId = studentLite.getStudentId();
            if(!studentLiteMap.containsKey(studentId)) {
                studentLiteMap.put(studentId, studentLite);
            }
        }

        DiaryRemarkDetailsRow diaryRemarkDetailsRow = diaryRemarkDetailsRows.get(0);
        return new DiaryRemarkDetails(diaryRemarkDetailsRow.getInstituteId(), diaryRemarkDetailsRow.getAcademicSessionId(),
                diaryRemarkDetailsRow.getDate(), diaryRemarkDetailsRow.getRemarkId(), diaryRemarkDetailsRow.getTitle(),
                diaryRemarkDetailsRow.getDescription(), new ArrayList<>(studentLiteMap.values()), diaryRemarkDetailsRow.getRemarkUserType(),
                diaryRemarkDetailsRow.getCreatedByUser(), diaryRemarkDetailsRow.getDiaryRemarkCategory(),
                diaryRemarkDetailsRow.getAttachmentCount(), diaryRemarkDetailsRow.getAttachmentSize(),
                diaryRemarkDetailsRow.getAllowedMimeTypes(), diaryRemarkDetailsRow.getDiaryRemarkAttachments());
    }
}
