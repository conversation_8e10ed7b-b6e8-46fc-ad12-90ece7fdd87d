package com.lernen.cloud.dao.tier.library.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.UUID;

import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.library.Author;

/**
 * Mapper class for Author entity.
 */
public class AuthorMapper implements RowMapper<Author> {

    private static final String AUTHOR_ID = "author.author_id";
    private static final String AUTHOR_NAME = "author.author_name";
    private static final String NATIONALITY = "author.nationality";
    private static final String DATE_OF_BIRTH = "author.date_of_birth";
    private static final String ASSOCIATED_GENRES = "author.associated_genres";
    private static final String SHORT_BIOGRAPHY = "author.short_biography";

    @Override
    public Author mapRow(ResultSet rs, int rowNum) throws SQLException {
        if(rs.getString(AUTHOR_ID) == null){
            return null;
        }
        return new Author(
            rs.getString(AUTHOR_ID) != null ? UUID.fromString(rs.getString(AUTHOR_ID)) : null,
            rs.getString(AUTHOR_NAME),
            rs.getString(NATIONALITY),
            rs.getTimestamp(DATE_OF_BIRTH) != null ? (int) (rs.getTimestamp(DATE_OF_BIRTH).getTime() / 1000l) : null,
            rs.getString(ASSOCIATED_GENRES),
            rs.getString(SHORT_BIOGRAPHY)
        );
    }
}
