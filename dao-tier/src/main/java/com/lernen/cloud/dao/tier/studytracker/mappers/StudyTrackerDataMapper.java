package com.lernen.cloud.dao.tier.studytracker.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.UUID;

import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.studytracker.StudyTrackerEntity;
import com.lernen.cloud.core.api.studytracker.StudyTrackerResponseRow;
import com.lernen.cloud.core.api.studytracker.StudyTrackerTypeResponse;

public class StudyTrackerDataMapper implements RowMapper<StudyTrackerResponseRow>{

    private static final String ENTITY_ID = "study_tracker_entity_id";
    private static final String ENTITY_NAME = "study_tracker_entity_name";
    private static final String ENTITY_NAME_VALUE = "study_tracker_entity_name_value";
    private static final String TYPE_ID = "study_tracker_type.type_id";
    private static final String TYPE_NAME = "study_tracker_type.type_name";
    private static final String VALUE = "study_tracker.value";
    private static final String STUDENT_REMARK = "study_tracker.student_remark";
    private static final String FACULTY_REMARK = "study_tracker.faculty_remark";

    @Override
    public StudyTrackerResponseRow mapRow(ResultSet rs, int rowNum) throws SQLException {
        if(rs.getString(ENTITY_ID) == null || rs.getString(TYPE_ID) == null){
            return null;
        }
        String entityId = rs.getString(ENTITY_ID);
        UUID typeId = UUID.fromString(rs.getString(TYPE_ID));
        StudyTrackerEntity entityName = StudyTrackerEntity.getStatusFromString(rs.getString(ENTITY_NAME));
        String entityNameValue = rs.getString(ENTITY_NAME_VALUE);
        String typeName = rs.getString(TYPE_NAME);
        Double value = null;
        if(rs.getString(VALUE) != null){
            value = rs.getDouble(VALUE);
        }
        String studentRemarks = rs.getString(STUDENT_REMARK);
        String facultyRemarks = rs.getString(FACULTY_REMARK);
        StudyTrackerTypeResponse studyTrackerTypeResponse = new StudyTrackerTypeResponse(typeId, typeName, value, studentRemarks, facultyRemarks);
        
        return new StudyTrackerResponseRow(entityId, entityName, entityNameValue, studyTrackerTypeResponse);
    }
    
}
