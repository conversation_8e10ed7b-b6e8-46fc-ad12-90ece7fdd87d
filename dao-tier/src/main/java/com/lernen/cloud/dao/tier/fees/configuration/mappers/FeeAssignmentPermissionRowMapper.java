package com.lernen.cloud.dao.tier.fees.configuration.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.UUID;

import org.springframework.jdbc.core.RowMapper;
import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.fees.FeeAssignmentPermission;
import com.lernen.cloud.core.api.fees.FeeAssignmentPermissionRow;
import com.lernen.cloud.core.api.fees.FeeHeadAssginmentPermission;
import com.lernen.cloud.core.api.fees.ModuleFeeProportion;
import com.lernen.cloud.core.api.user.Module;

/**
 *
 * <AUTHOR>
 *
 */
public class FeeAssignmentPermissionRowMapper implements RowMapper<FeeAssignmentPermissionRow> {
	public static final String FEE_ID = "fee_id";
	public static final String FEE_HEAD_ID = "fee_head_id";
	public static final String MODULE_NAME = "module_name";
	public static final String INSTITUTE_ID = "institute_id";
	public static final String PROPORTION_NUMERATOR = "proportion_numerator";
	public static final String PROPORTION_DENOMINATOR = "proportion_denominator";

	@Override
	public FeeAssignmentPermissionRow mapRow(ResultSet rs, int rowNum) throws SQLException {
		Double proportionNumerator = rs.getDouble(PROPORTION_NUMERATOR);
		if (rs.wasNull()) {
			proportionNumerator = null;
		}

		Double proportionDenominator = rs.getDouble(PROPORTION_DENOMINATOR);
		if (rs.wasNull()) {
			proportionDenominator = null;
		}
		return new FeeAssignmentPermissionRow(UUID.fromString(rs.getString(FEE_ID)), rs.getInt(FEE_HEAD_ID),
				Module.valueOf(rs.getString(MODULE_NAME)), rs.getInt(INSTITUTE_ID), proportionNumerator,
				proportionDenominator);
	}

	/**
	 * This method assumes that all rows belongs to same institute
	 *
	 * @param feeAssignmentPermissionRows
	 * @return
	 */
	public static List<FeeAssignmentPermission> getFeeAssignmentPermission(
			List<FeeAssignmentPermissionRow> feeAssignmentPermissionRows) {
		final List<FeeAssignmentPermission> feeAssignmentPermissions = new ArrayList<>();
		if (CollectionUtils.isEmpty(feeAssignmentPermissionRows)) {
			return feeAssignmentPermissions;
		}
		final Map<UUID, Map<Integer, FeeHeadAssginmentPermission>> feePermissionMap = new HashMap<>();

		for (final FeeAssignmentPermissionRow feeAssignmentPermissionRow : feeAssignmentPermissionRows) {
			final UUID feeId = feeAssignmentPermissionRow.getFeeId();
			final int feeHeadId = feeAssignmentPermissionRow.getFeeHeadId();

			if (!feePermissionMap.containsKey(feeId)) {
				feePermissionMap.put(feeId, new HashMap<>());
			}
			if (!feePermissionMap.get(feeId).containsKey(feeHeadId)) {
				final List<ModuleFeeProportion> moduleFeeProportions = new ArrayList<>();

				moduleFeeProportions.add(new ModuleFeeProportion(feeAssignmentPermissionRow.getModule(),
						feeAssignmentPermissionRow.getFeeProportionNumerator(),
						feeAssignmentPermissionRow.getFeeProportionDenominator()));
				feePermissionMap.get(feeId).put(feeHeadId,
						new FeeHeadAssginmentPermission(feeHeadId, moduleFeeProportions));
			} else {
				feePermissionMap.get(feeId).get(feeHeadId).getModuleFeeProportions()
						.add(new ModuleFeeProportion(feeAssignmentPermissionRow.getModule(),
								feeAssignmentPermissionRow.getFeeProportionNumerator(),
								feeAssignmentPermissionRow.getFeeProportionDenominator()));
			}
		}

		for (final Entry<UUID, Map<Integer, FeeHeadAssginmentPermission>> entry : feePermissionMap.entrySet()) {
			final UUID feeId = entry.getKey();
			final List<FeeHeadAssginmentPermission> feeHeadAssginmentPermissions = new ArrayList<>();
			for (final Entry<Integer, FeeHeadAssginmentPermission> feeHeadEntry : entry.getValue().entrySet()) {
				feeHeadAssginmentPermissions.add(feeHeadEntry.getValue());
			}
			feeAssignmentPermissions.add(new FeeAssignmentPermission(feeId, feeHeadAssginmentPermissions));
		}
		return feeAssignmentPermissions;
	}
}
