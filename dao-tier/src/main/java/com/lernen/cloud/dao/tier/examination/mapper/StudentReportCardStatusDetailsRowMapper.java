package com.lernen.cloud.dao.tier.examination.mapper;

import com.lernen.cloud.core.api.examination.*;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.dao.tier.student.mappers.StudentRowMapper;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @created_at 28/08/24 : 19:06
 **/
public class StudentReportCardStatusDetailsRowMapper implements RowMapper<StudentReportCardStatusDetails> {

    protected static final StudentRowMapper STUDENT_ROW_MAPPER = new StudentRowMapper();
    protected static final ExamReportCardTypeRowMapper EXAM_REPORT_CARD_TYPE_ROW_MAPPER = new ExamReportCardTypeRowMapper();
    private static final String STATUS = "student_report_card_mapping.status";

    @Override
    public StudentReportCardStatusDetails mapRow(ResultSet rs, int rowNum) throws SQLException {
        final Student student = STUDENT_ROW_MAPPER.mapRow(rs, rowNum);
        if (student == null) {
            return null;
        }
        final ExamReportCardMetadata examReportCardMetadata = EXAM_REPORT_CARD_TYPE_ROW_MAPPER.mapRow(rs, rowNum);
        return new StudentReportCardStatusDetails(Student.getStudentLite(student), examReportCardMetadata,
                StudentExamDisplayDataStatus.getStudentExamDisplayDataStatus(rs.getString(STATUS)));
    }
}
