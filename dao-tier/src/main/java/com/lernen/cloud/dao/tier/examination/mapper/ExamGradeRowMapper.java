package com.lernen.cloud.dao.tier.examination.mapper;

import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.examination.ExamGrade;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.UUID;

public class ExamGradeRowMapper implements RowMapper<ExamGrade> {

	private static final String INSTITUTE_ID = "institute_id";
	private static final String STANDARD_ID = "standard_id";
	private static final String ACADEMIC_SESSION_ID = "academic_session_id";
	private static final String COURSE_TYPE = "course_type";
	private static final String GRADE_ID = "grade_id";
	private static final String GRADE_VALUE = "grade_value";
	private static final String MARKS_RANGE_START = "marks_range_start";
	private static final String MARKS_RANGE_END = "marks_range_end";
	private static final String GRADE_NAME = "grade_name";
	private static final String RANGE_DISPLAY_NAME = "range_display_name";
	private static final String REMARKS = "remarks";
	private static final String CREDIT_SCORE = "credit_score";

	@Override
	public ExamGrade mapRow(ResultSet rs, int rowNum) throws SQLException {
		Integer gradeId = rs.getInt(GRADE_ID);
		if (rs.wasNull()) {
			gradeId = null;
		}

		Double gradeValue = rs.getDouble(GRADE_VALUE);
		if (rs.wasNull()) {
			gradeValue = null;
		}

		Double marksRangeStart = rs.getDouble(MARKS_RANGE_START);
		if (rs.wasNull()) {
			marksRangeStart = null;
		}

		Double marksRangeEnd = rs.getDouble(MARKS_RANGE_END);
		if (rs.wasNull()) {
			marksRangeEnd = null;
		}

		final String standardIdString = rs.getString(STANDARD_ID);
		UUID standardId = null;
		if (StringUtils.isNotBlank(standardIdString)) {
			standardId = UUID.fromString(standardIdString);
		}

		Integer creditScore = rs.getInt(CREDIT_SCORE);
		if (rs.wasNull()) {
			creditScore = null;
		}
		return new ExamGrade(rs.getInt(INSTITUTE_ID), rs.getInt(ACADEMIC_SESSION_ID), standardId,
				CourseType.getCourseType(rs.getString(COURSE_TYPE)), gradeId, rs.getString(GRADE_NAME), marksRangeStart,
				marksRangeEnd, gradeValue, rs.getString(RANGE_DISPLAY_NAME), rs.getString(REMARKS), creditScore);
	}

}
