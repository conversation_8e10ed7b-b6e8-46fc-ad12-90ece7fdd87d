package com.lernen.cloud.dao.tier.examination.mapper;

import com.lernen.cloud.core.api.examination.PersonalityTraitsDetails;
import com.lernen.cloud.core.api.examination.StudentPersonalityTraitsDetails;
import com.lernen.cloud.core.api.examination.StudentPersonalityTraitsDetailsRow;
import com.lernen.cloud.core.api.examination.StudentPersonalityTraitsResponse;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.dao.tier.student.mappers.StudentRowMapper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

/**
 * <AUTHOR>
 * @created_at 05/03/24 : 18:21
 **/
public class StudentPersonalityTraitsDetailsRowMapper implements RowMapper<StudentPersonalityTraitsDetailsRow> {

    protected static final StudentRowMapper STUDENT_ROW_MAPPER = new StudentRowMapper();

    private static final String REMARKS = "personality_traits_student_mapping.remarks";
    protected static final PersonalityTraitsRowMapper PERSONALITY_TRAITS_ROW_MAPPER = new PersonalityTraitsRowMapper();

    @Override
    public StudentPersonalityTraitsDetailsRow mapRow(ResultSet rs, int rowNum) throws SQLException {
        final Student student = STUDENT_ROW_MAPPER.mapRow(rs, rowNum);
        if (student == null) {
            return null;
        }
        final PersonalityTraitsDetails personalityTraitsDetails = PERSONALITY_TRAITS_ROW_MAPPER.mapRow(rs, rowNum);
        return new StudentPersonalityTraitsDetailsRow(student, rs.getString(REMARKS), personalityTraitsDetails);
    }
}