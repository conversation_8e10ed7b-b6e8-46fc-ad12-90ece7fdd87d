package com.lernen.cloud.dao.tier.institute.mappers;

import com.lernen.cloud.core.api.institute.InstituteHouse;
import com.lernen.cloud.core.api.institute.InstituteHousesWithCount;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

public class InstituteHousesWithCountRowMapper implements RowMapper<InstituteHousesWithCount> {

    private static final String STUDENT_HOUSE_COUNT = "student_house_count";
    private static final InstituteHousesRowMapper INSTITUTE_HOUSES_ROW_MAPPER = new InstituteHousesRowMapper();

    @Override
    public InstituteHousesWithCount mapRow(ResultSet rs, int rowNum) throws SQLException {
        InstituteHouse instituteHouse = INSTITUTE_HOUSES_ROW_MAPPER.mapRow(rs, rowNum);
        return new InstituteHousesWithCount(instituteHouse, rs.getInt(STUDENT_HOUSE_COUNT));
    }

}