package com.lernen.cloud.dao.tier.fees.payment.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.UUID;

import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.fees.FeeEntity;
import com.lernen.cloud.core.api.fees.payment.FeePaymentAggregatedAssignedAmount;
import com.lernen.cloud.core.api.institute.Stream;

/**
 *
 * <AUTHOR>
 *
 */
public class FeePaymentAssignedAmountRowMapper implements RowMapper<FeePaymentAggregatedAssignedAmount> {

	private static final String INSTITUTE_ID = "institute_id";
	private static final String ENTITY_TYPE = "entity_name";
	private static final String FEE_ID = "fee_id";
	private static final String FEE_HEAD_ID = "fee_head_id";
	private static final String ENTITY_ID = "entity_id";
	private static final String ASSIGNED_AMOUNT = "assigned_amount";
	private static final String ENTITY_DISPLAY_NAME = "entity_display_name";
	private static final String STREAM = "stream";
	private static final String STUDENT_COUNT = "count";
	private static final String STANDARD_LEVEL = "level";

	@Override
	public FeePaymentAggregatedAssignedAmount mapRow(ResultSet rs, int rowNum) throws SQLException {
		return new FeePaymentAggregatedAssignedAmount(rs.getInt(INSTITUTE_ID),
				FeeEntity.valueOf(rs.getString(ENTITY_TYPE)), rs.getString(ENTITY_ID),
				rs.getString(ENTITY_DISPLAY_NAME), Stream.valueOf(rs.getString(STREAM)), rs.getInt(STANDARD_LEVEL),
				UUID.fromString(rs.getString(FEE_ID)), rs.getInt(FEE_HEAD_ID), rs.getInt(STUDENT_COUNT),
				rs.getDouble(ASSIGNED_AMOUNT));
	}

}
