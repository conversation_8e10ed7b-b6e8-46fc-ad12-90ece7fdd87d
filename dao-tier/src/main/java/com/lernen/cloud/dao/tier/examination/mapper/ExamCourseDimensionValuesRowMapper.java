package com.lernen.cloud.dao.tier.examination.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.examination.ExamCoursePublishedStatus;
import com.lernen.cloud.core.api.examination.ExamDimension;
import com.lernen.cloud.core.api.examination.ExamDimensionValues;

/**
 *
 * <AUTHOR>
 *
 */
public class ExamCourseDimensionValuesRowMapper implements RowMapper<ExamDimensionValues> {

	private static final String MAX_MARKS = "max_marks";
	private static final String MIN_MARKS = "min_marks";
	private static final String MAX_GRADE = "max_grade";
	private static final String MIN_GRADE = "min_grade";
	private static final String STATUS = "exam_courses_assignment.status";

	private static final ExamDimensionRowMapper EXAM_DIMENSION_ROW_MAPPER = new ExamDimensionRowMapper();

	@Override
	public ExamDimensionValues mapRow(ResultSet rs, int rowNum) throws SQLException {
		final ExamDimension examDimension = EXAM_DIMENSION_ROW_MAPPER.mapRow(rs, rowNum);

		Double maxMarks = rs.getDouble(MAX_MARKS);
		if (rs.wasNull()) {
			maxMarks = null;
		}

		Double minMarks = rs.getDouble(MIN_MARKS);
		if (rs.wasNull()) {
			minMarks = null;
		}

		return new ExamDimensionValues(examDimension, maxMarks, maxMarks, minMarks, null, null,
				ExamCoursePublishedStatus.getExamCoursePublishedStatus(rs.getString(STATUS)));
	}

}
