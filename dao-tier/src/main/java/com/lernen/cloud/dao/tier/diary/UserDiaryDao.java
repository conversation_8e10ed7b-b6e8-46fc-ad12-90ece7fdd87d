package com.lernen.cloud.dao.tier.diary;

import com.amazonaws.util.CollectionUtils;
import com.lernen.cloud.core.api.common.PaginationInfo;
import com.lernen.cloud.core.api.common.SearchResultWithPagination;
import com.lernen.cloud.core.api.diary.*;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.DatabaseException;
import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.utils.exceptions.ExceptionHandling;
import com.lernen.cloud.dao.tier.diary.mappers.*;
import com.lernen.cloud.dao.tier.user.mappers.UserRowMapper;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import static com.lernen.cloud.core.utils.SharedConstants.GSON;

public class UserDiaryDao {
    private static final Logger logger = LogManager.getLogger(UserDiaryDao.class);
    private static final DiaryRemarkCategoryRowMapper DIARY_REMARK_CATEGORY_TYPE_ROW_MAPPER = new DiaryRemarkCategoryRowMapper();
    private static final DiaryRemarkDetailsRowMapper DIARY_REMARK_DETAILS_ROW_MAPPER = new DiaryRemarkDetailsRowMapper();
    private static final StandardRemarkDetailsRowMapper STANDARD_REMARK_DETAILS_ROW_MAPPER = new StandardRemarkDetailsRowMapper();
    private static final DiaryRemarkMetadataRowMapper DIARY_REMARK_METADATA_ROW_MAPPER = new DiaryRemarkMetadataRowMapper();
    private static final UserRowMapper USER_ROW_MAPPER = new UserRowMapper();
    private static final StaffDiaryRemarkDetailsRowMapper STAFF_DIARY_REMARK_DETAILS_ROW_MAPPER = new StaffDiaryRemarkDetailsRowMapper();
    private static final String DIARY_REMARKS_CATEGORY = "Diary Remarks Category";

    private static final String ADD_DIARY_REMARK_CATEGORY = " insert into diary_remarks_category (institute_id, category_id, category_name, remark_user_type, created_by) " + " values (?, ?, ?, ?, ?) ";
    private static final String UPDATE_DIARY_REMARK_CATEGORY = " update diary_remarks_category "
            + " set category_name = ?,  updated_by = ? where institute_id = ? and category_id = ? and diary_remarks_category.remark_user_type = ? ";
    private static final String GET_DIARY_REMARK_CATEGORY_TYPE = " select * from diary_remarks_category "
            + " where institute_id = ?  and diary_remarks_category.remark_user_type = ? order by diary_remarks_category.category_name asc ";
    private static final String GET_DIARY_REMARK_CATEGORY_TYPE_BY_ID = " select * from diary_remarks_category "
            + " where institute_id = ? and category_id = ? and diary_remarks_category.remark_user_type = ? ";
    private static final String GET_DIARY_REMARK_CATEGORY_TYPE_BY_NAME = " select * from diary_remarks_category "
            + " where institute_id = ?  and category_name = ?  and diary_remarks_category.remark_user_type = ? ";
    private static final String DELETE_DIARY_REMARK_CATEGORY_TYPE = " delete from diary_remarks_category"
            + " where institute_id = ?  and category_id = ? and diary_remarks_category.remark_user_type = ? ";

    private static final String UPDATE_STANDARD_REMARKS_DETAILS = " update standard_remarks set title = ?, "
            + "description = ?, diary_remark_category_id = ?, updated_by = ?, updated_at = ? where standard_remark_id = ? ";

    private static final String DELETE_STANDARD_REMARKS_DETAILS = " delete from standard_remarks where standard_remark_id = ? and institute_id = ? and standard_remarks.remark_user_type = ? ";
    private static final String ADD_STANDARD_REMARKS_DETAILS = " insert into standard_remarks(institute_id, standard_remark_id, title, description, diary_remark_category_id, remark_user_type, created_by) "
            + " values (?, ?, ?, ?, ?, ?, ?) ";
    private static final String GET_STANDARD_REMARK_DETAILS = "select * from standard_remarks "
            + " inner join diary_remarks_category on standard_remarks.diary_remark_category_id = diary_remarks_category.category_id"
            + " where standard_remarks.institute_id = ? and standard_remarks.remark_user_type = ? ";

    private static final String ADD_REMARKS_DETAILS = " insert into remark_details(institute_id, academic_session_id, remark_id, title, description, category_id, remark_user_type, created_by) "
            + " values (?, ?, ?, ?, ?, ?, ?, ?) ";
    private static final String UPDATE_REMARKS_DETAILS = " update remark_details set title = ?, "
            + "description = ?,category_id = ?, updated_by = ? where remark_id = ? and academic_session_id = ? ";
    private static final String GET_DIARY_REMARK_DETAILS_BY_CATEGORY_ID = " select * from remark_details where institute_id = ? and category_id = ? and remark_details.remark_user_type = ? ";
    private static final String DELETE_REMARKS_DETAILS = " delete from remark_details where remark_id = ? and academic_session_id = ? and remark_details.remark_user_type = ? ";
    private static final String ADD_REMARK_USER_MAPPING = "insert into remark_user_mappings(remark_id, user_id, user_type) "
            + " values (?, ?, ?)";
    private static final String DELETE_REMARK_USER_MAPPING = "delete from remark_user_mappings where remark_id = ? ";
    private static final String GET_REMARK_DETAILS = "select * from remark_user_mappings "
            + " join students on remark_user_mappings.user_id = students.student_id and remark_user_mappings.user_type = 'STUDENT' "
            + " join remark_details on remark_user_mappings.remark_id = remark_details.remark_id "
            + " join diary_remarks_category on remark_details.category_id = diary_remarks_category.category_id "
            + " join users on remark_details.created_by = users.user_id "
            + " join academic_session on remark_details.academic_session_id = academic_session.academic_session_id "
            + " join student_academic_session_details on remark_user_mappings.user_id = student_academic_session_details.student_id and remark_user_mappings.user_type = 'STUDENT' "
            + " and remark_details.academic_session_id = student_academic_session_details.academic_session_id "
            + " join standards on student_academic_session_details.standard_id = standards.standard_id "
            + " left join standard_section_mapping on standards.standard_id = standard_section_mapping.standard_id "
            + " and remark_details.academic_session_id = standard_section_mapping.academic_session_id and standard_section_mapping.section_id = student_academic_session_details.section_id "
            + " where remark_details.institute_id = ? and remark_details.remark_user_type = ? ";
    private static final String GET_REMARK_DETAILS_LIMIT_OFFSET = "select * from "
            + " (select * FROM remark_details where institute_id = ? and academic_session_id = ? %s %s %s order by created_at desc limit ? offset ?)  remark_details "
            + " join remark_user_mappings on remark_details.remark_id = remark_user_mappings.remark_id "
            + " join students on remark_user_mappings.user_id = students.student_id and remark_user_mappings.user_type = 'STUDENT' "
            + " join diary_remarks_category on remark_details.category_id = diary_remarks_category.category_id "
            + " join users on remark_details.created_by = users.user_id "
            + " join academic_session on remark_details.academic_session_id = academic_session.academic_session_id "
            + " join student_academic_session_details ON remark_user_mappings.user_id = student_academic_session_details.student_id and remark_user_mappings.user_type = 'STUDENT'"
            + " and remark_details.academic_session_id = student_academic_session_details.academic_session_id "
            + " join standards on student_academic_session_details.standard_id = standards.standard_id "
            + " left join standard_section_mapping ON standards.standard_id = standard_section_mapping.standard_id "
            + " and remark_details.academic_session_id = standard_section_mapping.academic_session_id "
            + " and standard_section_mapping.section_id = student_academic_session_details.section_id "
            + " where remark_details.institute_id = ? and remark_details.academic_session_id = ? and remark_details.remark_user_type = ? ";
    private static final String GET_REMARK_DETAILS_COUNT = "select count(*) from remark_details "
            + " where remark_details.institute_id = ? and remark_details.academic_session_id = ?  and remark_details.remark_user_type = ? %s %s %s ";
    private static final String GET_REMARK_METADATA_DETAILS = "select * from remark_details "
            + " join diary_remarks_category on remark_details.category_id = diary_remarks_category.category_id "
            + " join users on remark_details.created_by = users.user_id "
            + " where remark_details.institute_id = ? and remark_details.academic_session_id = ?  and remark_details.remark_user_type = ? ";
    private static final String GET_REMARK_METADATA_DETAILS_BY_USER_ID = "select * from remark_user_mappings "
            + " join remark_details on remark_user_mappings.remark_id = remark_details.remark_id "
            + " join diary_remarks_category on remark_details.category_id = diary_remarks_category.category_id "
            + " join users on remark_details.created_by = users.user_id "
            + " where remark_details.institute_id = ? and remark_details.academic_session_id = ? "
            + " and remark_user_mappings.user_id = ? and remark_details.remark_user_type = ? ";
    private static final String STUDENT_ID_CLAUSE = " and students.student_id = ? ";
    private static final String GET_DIARY_REMARK_USERS = "select distinct users.* from remark_details "
            + " join remark_user_mappings on remark_user_mappings.remark_id = remark_details.remark_id "
            + " join users on remark_user_mappings.user_id = users.user_id "
            + " where users.institute_id = ? and users.user_status = 'ENABLED' "
            + " and remark_details.remark_id = ? and remark_details.remark_user_type = ? ";
    private static final String ORDER_CREATED_AT_CLAUSE = " order by remark_details.created_at desc ";
    private static final String UPDATE_DIARY_REMARK_DOCUMENTS = " update remark_details set attachments = ? "
            + " where remark_id = ? and remark_details.remark_user_type = ? ";
    private static final String GET_STAFF_REMARK_DETAILS_LIMIT_OFFSET = "select * from "
            + " (select * FROM remark_details where institute_id = ? and academic_session_id = ? and remark_details.remark_user_type = ? %s %s %s order by created_at desc limit ? offset ?)  remark_details "
            + " join remark_user_mappings on remark_details.remark_id = remark_user_mappings.remark_id "
            + " join staff_details on remark_user_mappings.user_id = staff_details.staff_id and remark_user_mappings.user_type = 'STAFF' "
            + " join diary_remarks_category on remark_details.category_id = diary_remarks_category.category_id "
            + " join users on remark_details.created_by = users.user_id "
            + " where remark_details.institute_id = ? and remark_details.academic_session_id = ? and remark_details.remark_user_type = ? ";
    private static final String GET_STAFF_REMARK_DETAILS = "select * from remark_user_mappings "
            + " join staff_details on remark_user_mappings.user_id = staff_details.staff_id and remark_user_mappings.user_type = 'STAFF' "
            + " join remark_details on remark_user_mappings.remark_id = remark_details.remark_id "
            + " join diary_remarks_category on remark_details.category_id = diary_remarks_category.category_id "
            + " join users on remark_details.created_by = users.user_id "
            + " where remark_details.institute_id = ? and remark_details.remark_user_type = ? ";
    private static final String STAFF_ID_CLAUSE = " and staff_details.staff_id = ? ";


    private final JdbcTemplate jdbcTemplate;
    private final TransactionTemplate transactionTemplate;

    public UserDiaryDao(JdbcTemplate jdbcTemplate, TransactionTemplate transactionTemplate) {
        this.jdbcTemplate = jdbcTemplate;
        this.transactionTemplate = transactionTemplate;
    }

    public boolean addDiaryRemarkCategory(DiaryRemarkCategory diaryRemarkCategory, UUID userId) {
        try {
            return jdbcTemplate.update(ADD_DIARY_REMARK_CATEGORY, diaryRemarkCategory.getInstituteId(), diaryRemarkCategory.getCategoryId(), diaryRemarkCategory.getCategoryName(), diaryRemarkCategory.getRemarkUserType().name(), userId.toString()) == 1;
        } catch (final DataAccessException dataAccessException) {
            ExceptionHandling.HandleException(dataAccessException, DIARY_REMARKS_CATEGORY, "diary remark category name");
            logger.error("DataAccessException occurred while adding diary remark category name for institute {}", diaryRemarkCategory.getInstituteId(), dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception occurred while adding diary remark category name for institute {}", diaryRemarkCategory.getInstituteId(), e);
        }
        return false;
    }

    public UUID addRemarksWithoutTransaction(UserDiaryRemarkDetailsPayload userDiaryRemarkDetailsPayload, UserType userType) {
        try {
            final UUID remarkId = UUID.randomUUID();
            userDiaryRemarkDetailsPayload.setRemarkId(remarkId);
            boolean result = jdbcTemplate.update(ADD_REMARKS_DETAILS, userDiaryRemarkDetailsPayload.getInstituteId(), userDiaryRemarkDetailsPayload.getAcademicSessionId(), remarkId.toString(), userDiaryRemarkDetailsPayload.getTitle(), userDiaryRemarkDetailsPayload.getDescription(), userDiaryRemarkDetailsPayload.getCategoryId(), userDiaryRemarkDetailsPayload.getRemarkUserType().name(), userDiaryRemarkDetailsPayload.getCreatedBy().toString()) == 1;
            if (!result) {
                throw new RuntimeException("Unable to add remark");
            }
            result = addRemarkUserMapping(userDiaryRemarkDetailsPayload, userType);
            if (!result) {
                throw new RuntimeException("Unable to add remark user mapping");
            }
            return remarkId;
        } catch (final DatabaseException | ApplicationException ex) {
            ex.printStackTrace();
            throw ex;
        } catch (final Exception e) {
            e.printStackTrace();
            logger.error("Unable to execute transaction", e);
        }
        return null;
    }

    public boolean addStandardRemarks(StandardRemarks standardRemarks) {
        try {

            final UUID remarkId = UUID.randomUUID();

            return jdbcTemplate.update(ADD_STANDARD_REMARKS_DETAILS, standardRemarks.getInstituteId(), remarkId.toString(), standardRemarks.getTitle(), standardRemarks.getDescription(), standardRemarks.getCategoryId(), standardRemarks.getRemarkUserType().name(), standardRemarks.getAddedBy().toString()) == 1;

        } catch (final DatabaseException | ApplicationException ex) {
            throw ex;
        } catch (final Exception e) {
            e.printStackTrace();
            logger.error("Unable to add standard remark", e);
        }
        return false;
    }

    public boolean addRemarkUserMapping(UserDiaryRemarkDetailsPayload userDiaryRemarkDetailsPayload, UserType userType) {
        try {
            List<Object[]> args = new ArrayList<>();
            for (UUID userId : userDiaryRemarkDetailsPayload.getUserId()) {
                args.add(new Object[]{userDiaryRemarkDetailsPayload.getRemarkId().toString(), userId.toString(), userType.name()});
            }
            int[] rows = jdbcTemplate.batchUpdate(ADD_REMARK_USER_MAPPING, args);
            return rows.length == userDiaryRemarkDetailsPayload.getUserId().size();
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("Unable to execute transaction", e);
        }
        return false;
    }


    public boolean updateRemark(UserDiaryRemarkDetailsPayload userDiaryRemarkDetailsPayload, UserType userType) {
        try {
            boolean result = jdbcTemplate.update(UPDATE_REMARKS_DETAILS, userDiaryRemarkDetailsPayload.getTitle(), userDiaryRemarkDetailsPayload.getDescription(), userDiaryRemarkDetailsPayload.getCategoryId(), userDiaryRemarkDetailsPayload.getUpdatedBy().toString(), userDiaryRemarkDetailsPayload.getRemarkId().toString(), userDiaryRemarkDetailsPayload.getAcademicSessionId()) == 1;
            if (!result) {
                throw new RuntimeException("Unable to update remark");
            }
            List<Object[]> args = new ArrayList<>();
            args.add(new Object[]{userDiaryRemarkDetailsPayload.getRemarkId().toString()});
            jdbcTemplate.batchUpdate(DELETE_REMARK_USER_MAPPING, args);
            result = addRemarkUserMapping(userDiaryRemarkDetailsPayload, userType);
            if (!result) {
                throw new RuntimeException("Unable to update remark");
            }
            return true;
        } catch (final DatabaseException | ApplicationException ex) {
            throw ex;
        } catch (final Exception e) {
            logger.error("Unable to execute transaction", e);
        }
        return false;
    }

    public boolean updateStandardRemarks(StandardRemarks standardRemarks) {
        try {
            return jdbcTemplate.update(UPDATE_STANDARD_REMARKS_DETAILS, standardRemarks.getTitle(), standardRemarks.getDescription(), standardRemarks.getCategoryId(), standardRemarks.getUpdatedBy().toString(), new Timestamp(System.currentTimeMillis()), standardRemarks.getStandardRemarkId().toString()) >= 0;
        } catch (final DatabaseException | ApplicationException ex) {
            ex.printStackTrace();
            throw ex;
        } catch (final Exception e) {
            e.printStackTrace();
            logger.error("Unable to update standard remark", e);
        }
        return false;
    }

    public boolean deleteRemarks(int academicSessionId, UUID remarkId, RemarkUserType remarkUserType) {
        try {
            final boolean status = transactionTemplate.execute(new TransactionCallback<Boolean>() {
                @Override
                public Boolean doInTransaction(TransactionStatus status) {
                    List<Object[]> args = new ArrayList<>();
                    args.add(new Object[]{remarkId.toString()});
                    jdbcTemplate.batchUpdate(DELETE_REMARK_USER_MAPPING, args);
                    boolean result = jdbcTemplate.update(DELETE_REMARKS_DETAILS, remarkId.toString(), academicSessionId, remarkUserType.name()) == 1;
                    if (!result) {
                        throw new RuntimeException("Unable to delete remark");
                    }
                    return true;
                }
            });
            return status;
        } catch (final DatabaseException | ApplicationException ex) {
            throw ex;
        } catch (final Exception e) {
            logger.error("Unable to execute transaction", e);
        }
        return false;
    }

    public boolean deleteStandardRemarks(int instituteId, UUID remarkId, RemarkUserType remarkUserType) {
        try {

            return jdbcTemplate.update(DELETE_STANDARD_REMARKS_DETAILS, remarkId.toString(), instituteId, remarkUserType.name()) == 1;

        } catch (final DatabaseException | ApplicationException ex) {
            throw ex;
        } catch (final Exception e) {
            logger.error("Unable to delete standard remark", e);
        }
        return false;
    }

    public List<DiaryRemarkDetails> getRemarkDetails(int instituteId, int academicSessionId, Set<Integer> categoryIdSet, Set<UUID> userIdSet, RemarkUserType remarkUserType) {
        try {
            final List<Object> args = new ArrayList<>();
            args.add(instituteId);
            args.add(remarkUserType.name());

            StringBuilder query = new StringBuilder(GET_REMARK_DETAILS);

            if (!CollectionUtils.isNullOrEmpty(categoryIdSet)) {
                query.append(" and diary_remarks_category.category_id in  ");
                query.append("(");
                boolean first = true;
                for (final int categoryId : categoryIdSet) {
                    args.add(categoryId);
                    if (first) {
                        query.append("?");
                        first = false;
                        continue;
                    }
                    query.append(", ?");
                }
                query.append(")");
            }
            if (!CollectionUtils.isNullOrEmpty(userIdSet)) {
                query.append(" and users.user_id in ( ");
                boolean first = true;
                for (UUID userId : userIdSet) {
                    args.add(userId.toString());
                    if (first) {
                        query.append("?");
                        first = false;
                        continue;
                    }
                    query.append(", ?");
                }
                query.append(")");
            }

            query.append(" and remark_details.academic_session_id = ? ");
            args.add(academicSessionId);

            return DiaryRemarkDetailsRowMapper.diaryRemarkStudentsDataList(jdbcTemplate.query(query.toString(), args.toArray(), DIARY_REMARK_DETAILS_ROW_MAPPER));


        } catch (final Exception e) {

            logger.error("Error while fetching remarks for {}", instituteId, e);
        }
        return null;
    }


    public SearchResultWithPagination<DiaryRemarkDetails> getRemarkDetailsWithPagination(int instituteId, int academicSessionId,
                                                                                         Set<Integer> categoryIdInt, Set<UUID> userIdList, int offset, int limit, String searchText, RemarkUserType remarkUserType) {
        try {
            final List<Object> args = new ArrayList<>();
            final List<Object> countargs = new ArrayList<>();
            String countQuery = GET_REMARK_DETAILS_COUNT;

            args.add(instituteId);
            args.add(academicSessionId);
            countargs.add(instituteId);
            countargs.add(academicSessionId);
            countargs.add(remarkUserType.name());

            String searchQuery = "";
            StringBuilder categoryFiltering = new StringBuilder();
            StringBuilder userFiltering = new StringBuilder();

            if (!StringUtils.isBlank(searchText)) {
                String search = searchText.toLowerCase();
                searchQuery = "and lower(title) like '%" + search + "%'";
            }

            if (!CollectionUtils.isNullOrEmpty(categoryIdInt)) {
                categoryFiltering.append(" and category_id in  ");
                categoryFiltering.append("(");
                boolean first = true;
                for (final int categoryId : categoryIdInt) {
                    args.add(categoryId);
                    countargs.add(categoryId);
                    if (first) {
                        categoryFiltering.append("?");
                        first = false;
                        continue;
                    }
                    categoryFiltering.append(", ?");
                }
                categoryFiltering.append(")");
            }

            if (!CollectionUtils.isNullOrEmpty(userIdList)) {
                userFiltering.append(" and created_by in ( ");
                boolean first = true;
                for (UUID userId : userIdList) {
                    args.add(userId.toString());
                    countargs.add(userId.toString());
                    if (first) {
                        userFiltering.append("?");
                        first = false;
                        continue;
                    }
                    userFiltering.append(", ?");
                }
                userFiltering.append(")");
            }

            args.add(limit);
            args.add(offset);
            args.add(instituteId);
            args.add(academicSessionId);
            args.add(remarkUserType.name());

            StringBuilder query = new StringBuilder(GET_REMARK_DETAILS_LIMIT_OFFSET);

            if (!CollectionUtils.isNullOrEmpty(categoryIdInt)) {
                query.append(" and diary_remarks_category.category_id in  ");
                query.append("(");
                boolean first = true;
                for (final int categoryId : categoryIdInt) {
                    args.add(categoryId);
                    if (first) {
                        query.append("?");
                        first = false;
                        continue;
                    }
                    query.append(", ?");
                }
                query.append(")");
            }
            if (!CollectionUtils.isNullOrEmpty(userIdList)) {
                query.append(" and users.user_id in ( ");
                boolean first = true;
                for (UUID userId : userIdList) {
                    args.add(userId.toString());
                    if (first) {
                        query.append("?");
                        first = false;
                        continue;
                    }
                    query.append(", ?");
                }
                query.append(")");
            }

            final int totalResultCount = jdbcTemplate.queryForObject(String.format(countQuery, searchQuery, categoryFiltering, userFiltering), Integer.class, countargs.toArray());
            final PaginationInfo paginationInfo = new PaginationInfo(totalResultCount, limit, offset);
            List<DiaryRemarkDetails> diaryRemarkDetails = DiaryRemarkDetailsRowMapper.diaryRemarkStudentsDataList(jdbcTemplate.query(String.format(query.toString(), searchQuery, categoryFiltering.toString(), userFiltering.toString()), args.toArray(), DIARY_REMARK_DETAILS_ROW_MAPPER));
            return new SearchResultWithPagination<>(paginationInfo, diaryRemarkDetails);

        } catch (final Exception e) {
            e.printStackTrace();
            logger.error("Error while fetching remarks for {}", instituteId, e);
        }
        return null;
    }


    public List<StandardRemarkDetails> getStandardRemarkDetails(int instituteId, int categoryId, RemarkUserType remarkUserType, UUID userId) {
        try {
            final List<Object> args = new ArrayList<>();
            args.add(instituteId);
            args.add(remarkUserType.name());
            StringBuilder query = new StringBuilder(GET_STANDARD_REMARK_DETAILS);
            if (categoryId > 0) {
                query.append(" and standard_remarks.category_id = ?");
                args.add(categoryId);
            }
            //If user id is not null then filter standard remarks based on userId
            if (userId != null) {
                query.append(" and standard_remarks.created_by = ?");
                args.add(userId.toString());
            }
            return jdbcTemplate.query(query.toString(), args.toArray(), STANDARD_REMARK_DETAILS_ROW_MAPPER);

        } catch (final Exception e) {
            logger.error("Error while fetching standard remarks for {}", instituteId, e);
        }
        return null;
    }


    public List<DiaryRemarkMetadata> getRemarkMetadata(int instituteId, int academicSessionId, int offset, int limit, RemarkUserType remarkUserType) {
        try {
            List<Object> args = new ArrayList<>();
            args.add(instituteId);
            args.add(academicSessionId);
            args.add(remarkUserType.name());

            String query = GET_REMARK_METADATA_DETAILS;
            query += ORDER_CREATED_AT_CLAUSE;
            String limitClause = "";
            String offsetClause = "";
            if (limit > 0) {
                limitClause = " limit " + limit;
                query += limitClause;
                offsetClause = " offset " + offset;
                query += offsetClause;
            }

            return jdbcTemplate.query(query, args.toArray(), DIARY_REMARK_METADATA_ROW_MAPPER);

        } catch (final Exception e) {
            logger.error("Error while fetching remarks for {}", instituteId, e);
        }
        return null;
    }

    public DiaryRemarkDetails getDiaryRemarkDetailByRemarkId(int instituteId, UUID remarkId, RemarkUserType remarkUserType) {
        try {
            final List<Object> args = new ArrayList<>();
            StringBuilder query = new StringBuilder(GET_REMARK_DETAILS);
            args.add(instituteId);
            args.add(remarkUserType.name());
            query.append(" and remark_details.remark_id = ? ");
            args.add(remarkId.toString());
            return DiaryRemarkDetailsRowMapper.getDiaryRemarkDetailOfARemark(jdbcTemplate.query(query.toString(), args.toArray(), DIARY_REMARK_DETAILS_ROW_MAPPER));
        } catch (final Exception e) {
            e.printStackTrace();
            logger.error("Error while fetching remarks for {}", instituteId, e);
        }
        return null;
    }

    public List<DiaryRemarkDetails> getRemarkDetails(int instituteId, int academicSessionId, UUID studentId, Set<Integer> categoryIdSet, Set<UUID> userIdSet, RemarkUserType remarkUserType) {
        try {
            final List<Object> args = new ArrayList<>();
            args.add(instituteId);
            args.add(remarkUserType.name());
            StringBuilder query = new StringBuilder(GET_REMARK_DETAILS);

            if (studentId != null) {
                query.append(STUDENT_ID_CLAUSE);
                args.add(studentId.toString());
            }
            if (!CollectionUtils.isNullOrEmpty(categoryIdSet)) {
                query.append(" and diary_remarks_category.category_id in  ");
                query.append("(");
                boolean first = true;
                for (final int categoryId : categoryIdSet) {
                    args.add(categoryId);
                    if (first) {
                        query.append("?");
                        first = false;
                        continue;
                    }
                    query.append(", ?");
                }
                query.append(")");
            }
            if (!CollectionUtils.isNullOrEmpty(userIdSet)) {
                query.append(" and users.user_id in ( ");
                boolean first = true;
                for (UUID userId : userIdSet) {
                    args.add(userId.toString());
                    if (first) {
                        query.append("?");
                        first = false;
                        continue;
                    }
                    query.append(", ?");
                }
                query.append(")");
            }
            query.append(" and remark_details.academic_session_id = ? ");
            args.add(academicSessionId);

            return DiaryRemarkDetailsRowMapper.diaryRemarkSingleStudentsDataList(jdbcTemplate.query(query.toString(), args.toArray(), DIARY_REMARK_DETAILS_ROW_MAPPER));

        } catch (final Exception e) {
            logger.error("Error while fetching specific remarks for {}", instituteId, e);
        }
        return null;
    }

    public List<DiaryRemarkMetadata> getRemarkMetadata(int instituteId, int academicSessionId, UUID studentId, int offset, int limit, RemarkUserType remarkUserType) {
        try {
            List<Object> args = new ArrayList<>();
            args.add(instituteId);
            args.add(academicSessionId);
            args.add(studentId.toString());
            args.add(remarkUserType.name());

            String query = GET_REMARK_METADATA_DETAILS_BY_USER_ID;
            query += ORDER_CREATED_AT_CLAUSE;
            String limitClause = "";
            String offsetClause = "";
            if (limit > 0) {
                limitClause = " limit " + limit;
                query += limitClause;
                offsetClause = " offset " + offset;
                query += offsetClause;
            }
            return jdbcTemplate.query(query, args.toArray(), DIARY_REMARK_METADATA_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Error while fetching specific remarks for {}", instituteId, e);
        }
        return null;
    }

    public List<DiaryRemarkCategory> getDiaryRemarkCategory(int instituteId, RemarkUserType remarkUserType) {
        try {
            final Object[] args = {instituteId, remarkUserType.name()};
            return jdbcTemplate.query(GET_DIARY_REMARK_CATEGORY_TYPE, args, DIARY_REMARK_CATEGORY_TYPE_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Exception occurred while getting the diary remark category information for institute {}", instituteId, e);
        }
        return null;
    }

    public DiaryRemarkCategory getDiaryRemarkCategoryByName(int instituteId, String categoryName, RemarkUserType remarkUserType) {
        try {
            final Object[] args = {instituteId, categoryName, remarkUserType.name()};
            return jdbcTemplate.queryForObject(GET_DIARY_REMARK_CATEGORY_TYPE_BY_NAME, args, DIARY_REMARK_CATEGORY_TYPE_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Exception occurred while getting diary remark category name for institute {}", instituteId, e);
        }
        return null;
    }

    public DiaryRemarkCategory getDiaryRemarkCategoryById(int instituteId, int categoryId, RemarkUserType remarkUserType) {
        try {
            final Object[] args = {instituteId, categoryId, remarkUserType.name()};
            return jdbcTemplate.queryForObject(GET_DIARY_REMARK_CATEGORY_TYPE_BY_ID, args, DIARY_REMARK_CATEGORY_TYPE_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Exception occurred while getting remark category for institute {}, categoryId {}", instituteId, categoryId, e);
        }
        return null;
    }

    public DiaryRemarkDetailsRow getDiaryRemarkDetailsByCategoryId(int instituteId, int categoryId, RemarkUserType remarkUserType) {
        try {
            final Object[] args = {instituteId, categoryId, remarkUserType.name()};
            return jdbcTemplate.queryForObject(GET_DIARY_REMARK_DETAILS_BY_CATEGORY_ID, args, DIARY_REMARK_DETAILS_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Exception occurred while getting remark category details for institute {}, categoryId {}", instituteId, categoryId, e);
        }
        return null;
    }

    public boolean updateDiaryRemarkCategory(DiaryRemarkCategory diaryRemarkCategory, UUID userId) {
        try {
            return jdbcTemplate.update(UPDATE_DIARY_REMARK_CATEGORY, diaryRemarkCategory.getCategoryName(), userId.toString(), diaryRemarkCategory.getInstituteId(), diaryRemarkCategory.getCategoryId(), diaryRemarkCategory.getRemarkUserType().name()) == 1;
        } catch (final DataAccessException dataAccessException) {
            ExceptionHandling.HandleException(dataAccessException, DIARY_REMARKS_CATEGORY, "diary remark category type");
            logger.error("DataAccessException occurred while updating diary remark category type for institute {} ", diaryRemarkCategory.getInstituteId(), dataAccessException);
        } catch (final Exception e) {
            logger.error("Exception occurred while updating diary remark category type for institute {} ", diaryRemarkCategory.getInstituteId(), e);
        }
        return false;
    }

    public boolean deleteDiaryRemarkCategory(int instituteId, int categoryId, RemarkUserType remarkUserType) {
        try {
            final Object[] args = {instituteId, categoryId, remarkUserType.name()};
            return jdbcTemplate.update(DELETE_DIARY_REMARK_CATEGORY_TYPE, args) >= 0;
        } catch (final Exception e) {
            logger.error("Exception occurred while deleting diary remark category type for institute {} and categoryId {}", instituteId, categoryId, e);
        }
        return false;
    }

    public List<User> getDiaryRemarkUsers(int instituteId, UUID remarkId, RemarkUserType remarkUserType) {
        try {
            final Object[] args = {instituteId, remarkId.toString(), remarkUserType.name()};
            return jdbcTemplate.query(GET_DIARY_REMARK_USERS, args, USER_ROW_MAPPER);
        } catch (final Exception e) {
            logger.error("Exception while getting users for instituteId {}, remarkId {}", instituteId, remarkId, e);
        }
        return null;
    }

    public boolean updateDocuments(UUID remarkId, List<Document<DiaryRemarkDocumentType>> diaryRemarkAttachmentList, RemarkUserType remarkUserType) {
        try {
            final Object[] args = {GSON.toJson(diaryRemarkAttachmentList), remarkId.toString(), remarkUserType.name()};
            return jdbcTemplate.update(UPDATE_DIARY_REMARK_DOCUMENTS, args) == 1;
        } catch (final DataAccessException dataAccessException) {
            ExceptionHandling.HandleException(dataAccessException, null, null);
            logger.error("Error while updating remark documents for remark id {}", remarkId);
        } catch (final Exception e) {
            logger.error("Exception while updating remark documents for remark id {}", remarkId, e);
        }
        return false;
    }


    public SearchResultWithPagination<StaffDiaryRemarkDetails> getStaffRemarkDetailsWithPagination(int instituteId, int academicSessionId,
                                                                                                   Set<Integer> categoryIdInt, Set<UUID> createdUserIdSet, int offset, int limit, String searchText, RemarkUserType remarkUserType) {
        try {
            final List<Object> args = new ArrayList<>();
            final List<Object> countargs = new ArrayList<>();
            String countQuery = GET_REMARK_DETAILS_COUNT;

            args.add(instituteId);
            args.add(academicSessionId);
            args.add(remarkUserType.name());
            countargs.add(instituteId);
            countargs.add(academicSessionId);
            countargs.add(remarkUserType.name());

            String searchQuery = "";
            StringBuilder categoryFiltering = new StringBuilder();
            StringBuilder userFiltering = new StringBuilder();

            if (!StringUtils.isBlank(searchText)) {
                String search = searchText.toLowerCase();
                searchQuery = "and lower(title) like '%" + search + "%'";
            }

            if (!CollectionUtils.isNullOrEmpty(categoryIdInt)) {
                categoryFiltering.append(" and category_id in  ");
                categoryFiltering.append("(");
                boolean first = true;
                for (final int categoryId : categoryIdInt) {
                    args.add(categoryId);
                    countargs.add(categoryId);
                    if (first) {
                        categoryFiltering.append("?");
                        first = false;
                        continue;
                    }
                    categoryFiltering.append(", ?");
                }
                categoryFiltering.append(")");
            }

            if (!CollectionUtils.isNullOrEmpty(createdUserIdSet)) {
                userFiltering.append(" and created_by in ( ");
                boolean first = true;
                for (UUID userId : createdUserIdSet) {
                    args.add(userId.toString());
                    countargs.add(userId.toString());
                    if (first) {
                        userFiltering.append("?");
                        first = false;
                        continue;
                    }
                    userFiltering.append(", ?");
                }
                userFiltering.append(")");
            }

            args.add(limit);
            args.add(offset);
            args.add(instituteId);
            args.add(academicSessionId);
            args.add(remarkUserType.name());

            StringBuilder query = new StringBuilder(GET_STAFF_REMARK_DETAILS_LIMIT_OFFSET);

            if (!CollectionUtils.isNullOrEmpty(categoryIdInt)) {
                query.append(" and diary_remarks_category.category_id in  ");
                query.append("(");
                boolean first = true;
                for (final int categoryId : categoryIdInt) {
                    args.add(categoryId);
                    if (first) {
                        query.append("?");
                        first = false;
                        continue;
                    }
                    query.append(", ?");
                }
                query.append(")");
            }
            if (!CollectionUtils.isNullOrEmpty(createdUserIdSet)) {
                query.append(" and remark_details.created_by in ( ");
                boolean first = true;
                for (UUID userId : createdUserIdSet) {
                    args.add(userId.toString());
                    if (first) {
                        query.append("?");
                        first = false;
                        continue;
                    }
                    query.append(", ?");
                }
                query.append(")");
            }

            final int totalResultCount = jdbcTemplate.queryForObject(String.format(countQuery, searchQuery, categoryFiltering, userFiltering), Integer.class, countargs.toArray());
            final PaginationInfo paginationInfo = new PaginationInfo(totalResultCount, limit, offset);


            List<StaffDiaryRemarkDetails> diaryRemarkDetails = StaffDiaryRemarkDetailsRowMapper.diaryRemarkStaffsDataList(
                    jdbcTemplate.query(String.format(query.toString(), searchQuery, categoryFiltering.toString(),
                            userFiltering.toString()), args.toArray(), STAFF_DIARY_REMARK_DETAILS_ROW_MAPPER));
            return new SearchResultWithPagination<>(paginationInfo, diaryRemarkDetails);

        } catch (final Exception e) {
            e.printStackTrace();
            logger.error("Error while fetching remarks for {}", instituteId, e);
        }
        return null;
    }

    public StaffDiaryRemarkDetails getStaffDiaryRemarkDetailByRemarkId(int instituteId, UUID remarkId, RemarkUserType remarkUserType) {
        try {
            final List<Object> args = new ArrayList<>();
            StringBuilder query = new StringBuilder(GET_STAFF_REMARK_DETAILS);
            args.add(instituteId);
            args.add(remarkUserType.name());
            query.append(" and remark_details.remark_id = ? ");
            args.add(remarkId.toString());
            return StaffDiaryRemarkDetailsRowMapper.getDiaryRemarkDetailOfARemark(
                    jdbcTemplate.query(query.toString(), args.toArray(), STAFF_DIARY_REMARK_DETAILS_ROW_MAPPER));
        } catch (final Exception e) {
            logger.error("Error while fetching remarks for {}", instituteId, e);
        }
        return null;
    }

    public List<StaffDiaryRemarkDetails> getRemarksBySendToId(int instituteId, int academicSessionId, UUID sendToUUID,
                                                              Set<Integer> categoryIdSet, Set<UUID> userIdSet, RemarkUserType remarkUserType) {
        try {
            final List<Object> args = new ArrayList<>();
            args.add(instituteId);
            args.add(remarkUserType.name());
            StringBuilder query = new StringBuilder(GET_STAFF_REMARK_DETAILS);

            if (sendToUUID != null) {
                query.append(STAFF_ID_CLAUSE);
                args.add(sendToUUID.toString());
            }
            if (!CollectionUtils.isNullOrEmpty(categoryIdSet)) {
                query.append(" and diary_remarks_category.category_id in  ");
                query.append("(");
                boolean first = true;
                for (final int categoryId : categoryIdSet) {
                    args.add(categoryId);
                    if (first) {
                        query.append("?");
                        first = false;
                        continue;
                    }
                    query.append(", ?");
                }
                query.append(")");
            }
            if (!CollectionUtils.isNullOrEmpty(userIdSet)) {
                query.append(" and users.user_id in ( ");
                boolean first = true;
                for (UUID userId : userIdSet) {
                    args.add(userId.toString());
                    if (first) {
                        query.append("?");
                        first = false;
                        continue;
                    }
                    query.append(", ?");
                }
                query.append(")");
            }
            query.append(" and remark_details.academic_session_id = ? ");
            args.add(academicSessionId);

            return StaffDiaryRemarkDetailsRowMapper.diaryRemarkSingleStaffsDataList(jdbcTemplate.query(query.toString(), args.toArray(), STAFF_DIARY_REMARK_DETAILS_ROW_MAPPER));

        } catch (final Exception e) {
            e.printStackTrace();
            logger.error("Error while fetching specific remarks for {}", instituteId, e);
        }
        return null;
    }
}
