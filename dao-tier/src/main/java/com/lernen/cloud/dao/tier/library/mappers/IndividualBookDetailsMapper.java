package com.lernen.cloud.dao.tier.library.mappers;
import java.lang.reflect.Type;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.lernen.cloud.core.api.library.Author;
import com.lernen.cloud.core.api.library.BookDetails;
import com.lernen.cloud.core.api.library.BookDocumentType;
import com.lernen.cloud.core.api.library.BookStatus;
import com.lernen.cloud.core.api.library.Genre;
import com.lernen.cloud.core.api.library.LibraryType;
import com.lernen.cloud.core.api.library.Publication;
import com.lernen.cloud.core.api.library.Publisher;
import com.lernen.cloud.core.api.library.IndividualBookDetails;
import com.lernen.cloud.core.api.library.Vendor;
import com.lernen.cloud.core.api.user.Document;

public class IndividualBookDetailsMapper implements RowMapper<IndividualBookDetails> {

    private static final VendorMapper VENDOR_MAPPER = new VendorMapper();
    private static final LibraryTypeMapper LIBRARY_TYPE_MAPPER = new LibraryTypeMapper();

    private static final String ACCESSION_ID = "individual_book_details.accession_id";
    private static final String INSTITUTE_ID = "individual_book_details.institute_id";
    private static final String BOOK_ID = "individual_book_details.book_id";
    private static final String ACCESSION_NUMBER = "individual_book_details.accession_number";
    private static final String RACK = "individual_book_details.rack";
    private static final String PRICE = "individual_book_details.price";
    private static final String BILL_NUMBER = "individual_book_details.bill_number";
    private static final String DATE_OF_PURCHAGE = "individual_book_details.date_of_purchase";
    private static final String VENDOR_ID = "individual_book_details.vendor_id";
    private static final String STATUS = "individual_book_details.status";
    private static final String VOLUME = "individual_book_details.volume";
    private static final String LIBRARY_TYPE_ID = "individual_book_details.library_type_id";
    private static final String REMARK = "individual_book_details.remark";

    public IndividualBookDetails mapRow(ResultSet rs, int rowNum) throws SQLException {
		 
        Vendor vendor = VENDOR_MAPPER.mapRow(rs, rowNum);
        LibraryType libraryType = LIBRARY_TYPE_MAPPER.mapRow(rs, rowNum);

        return new IndividualBookDetails(UUID.fromString(rs.getString(ACCESSION_ID)), rs.getInt(INSTITUTE_ID), UUID.fromString(rs.getString(BOOK_ID)), 
                        rs.getString(ACCESSION_NUMBER), rs.getString(RACK), rs.getDouble(PRICE), rs.getString(BILL_NUMBER),  
                        rs.getTimestamp(DATE_OF_PURCHAGE) != null ? (int) (rs.getTimestamp(DATE_OF_PURCHAGE).getTime() / 1000l) : null, 
                        vendor, BookStatus.getStatusFromString(rs.getString(STATUS)), rs.getString(VOLUME), libraryType, rs.getString(REMARK));
	}
}
