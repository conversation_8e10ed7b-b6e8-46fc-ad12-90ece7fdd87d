import sys
import time
from datetime import date
import subprocess
import requests
import json
import os.path
import time
import boto3


# ACCESS_KEY = ""
# SECRET_KEY = ""
API_SERVER_IP_ADDRESS = "**************"
API_URL = "https://api.embrate.com/systime"
API_HEALTH_CHECK_RETRY_COUNT_BEFORE_RESTART = 3
API_HEALTH_CHECK_RETRY_COUNT_AFTER_RESTART = 8

ACCESS_KEY = ""
SECRET_KEY = ""
# API_SERVER_IP_ADDRESS = "**************"
# API_URL = "https://api2.embrate.com/systime"
# API_HEALTH_CHECK_RETRY_COUNT_BEFORE_RESTART = 0
# API_HEALTH_CHECK_RETRY_COUNT_AFTER_RESTART = 0


FILE_DIR = "/tmp/api_health_check"
FILE_PATH = FILE_DIR + "/" + "process_data.json"
LOG_FILE_PATH = FILE_DIR + "/" + "logs.txt"

LOCK_WAIT_THRESHOLD_IN_SEC = 600
lock_acquired_key = 'lock_acquired'
process_id_key = 'process_id'
last_update_key = 'last_update'

def run():
	process_data = get_process_data()

	lock_acquired = False
	if lock_acquired_key in process_data:
		lock_acquired = process_data[lock_acquired_key]

	if lock_acquired:
		process_id = process_data[process_id_key]
		last_update = process_data[last_update_key]
		curr_time = int(time.time())
		if curr_time - last_update <= LOCK_WAIT_THRESHOLD_IN_SEC:
			print(process_id + " already holding lock. Process data = " + str(process_data))
			return
		else:
			print("Overriding the lock as it exceeds the waiting time " + str(LOCK_WAIT_THRESHOLD_IN_SEC))

	acquire_lock()

	tmp_logs = []
	server_up = poll_for_api_server_running_state(tmp_logs, API_HEALTH_CHECK_RETRY_COUNT_BEFORE_RESTART)

	if server_up:
		release_lock()
		return

	curr_time = int(time.time())
	restart_logs = []
	log(restart_logs, "Restarting server at " + str(curr_time))

	restart_api_server(restart_logs)
	poll_for_api_server_running_state(restart_logs, API_HEALTH_CHECK_RETRY_COUNT_AFTER_RESTART)
	write_server_restart_logs(restart_logs)
	release_lock()

def log(logs, line):
	print(line)
	logs.append(str(line))

def poll_for_api_server_running_state(logs, max_try):
	try_count = 0
	while try_count < max_try:
		try_count += 1
		status_data = get_api_health_check()
		if status_data['server_up']:
			break
		else:
			log(logs, "Server is not responding, retrying # " + str(try_count))
			time.sleep(10)

	if try_count < max_try:
		log(logs, "Server is successfully running")
		return True
	else:
		log(logs, "Server still not responding")
		return False


def get_api_health_check():
	try:
		response = requests.get(API_URL, timeout=10)
		if response.status_code == 200:
			systime = response.text
			return {'server_up' : True, 'systime' : systime}
		else:
			return {'server_up' : False, 'timeout' : False, 'status_code' : response.status_code}
	except:
		return {'server_up' : False, 'timeout' : True}



def get_process_data():
	file_exists = os.path.isfile(FILE_PATH)
	process_data = {}
	if file_exists:
		status_file = open(FILE_PATH, 'r')
		process_data = json.load(status_file)
		status_file.close()

	return process_data

def acquire_lock():
	file_exists = os.path.isfile(FILE_PATH)
	if not file_exists:
		os.mkdir(FILE_DIR)

	status_file = open(FILE_PATH, 'w+')
	curr_time = int(time.time())
	process_data = {lock_acquired_key : True, process_id_key : 'pid_'+ str(curr_time), last_update_key : curr_time}
	status_file.write(json.dumps(process_data))
	status_file.close()
	print('lock acquired')

def release_lock():
	status_file = open(FILE_PATH, 'w')
	curr_time = int(time.time())
	process_data = {lock_acquired_key : False, last_update_key : curr_time}
	status_file.write(json.dumps(process_data))
	status_file.close()
	print('lock released')

def write_server_restart_logs(logs):
	log_file = open(LOG_FILE_PATH, 'a')
	for line in logs:
		log_file.write(line+"\n")
	log_file.close()

def restart_api_server(logs):
	ec2 = boto3.client('ec2', aws_access_key_id=ACCESS_KEY, aws_secret_access_key=SECRET_KEY, region_name= 'us-east-1')
	api_server_instance = get_api_server_instance(ec2)
	if api_server_instance is None:
		log(logs, "No server found ")
		return
	instance_id = api_server_instance['InstanceId']
	status = api_server_instance['State']
	log(logs, status)

	stop_api_server(logs, ec2, instance_id)
	# instance_id = "i-06346da10d1ec8f74"
	status_code = poll_for_server_final_state(logs, ec2, instance_id, 64, 80)
	log(logs, "server final state = " + str(status_code))

	start_api_server(logs, ec2, instance_id)
	status_code = poll_for_server_final_state(logs, ec2, instance_id, 0, 16)
	log(logs, "server final state = " + str(status_code))


def poll_for_server_final_state(logs, ec2, instance_id, pending_code, final_code):
	while True:
		api_server_instance = get_api_server_instance_by_id(ec2, instance_id)
		status = api_server_instance['State']
		log(logs, str(status))
		status_code = status['Code']
		if status_code == pending_code:
			time.sleep(10)

		elif status_code == final_code:
			return status_code

		else:
			log(logs, "Unknown status code " + str(status_code))
		# return status_code

def stop_api_server(logs, ec2, instance_id):
	try:
		log(logs, "stopping " + instance_id)
		response = ec2.stop_instances(InstanceIds=[instance_id])
		log(logs, response)
	except:
		log(logs, "Exception during stop")


def start_api_server(logs, ec2, instance_id):
	try:
		log(logs, "starting " + instance_id)
		response = ec2.start_instances(InstanceIds=[instance_id])
		log(logs, response)
	except:
		log(logs, "Exception during start")


def get_api_server_instance_by_id(ec2, instance_id):
	response = ec2.describe_instances(InstanceIds=[instance_id])
	return get_server_instance_details(response, "InstanceId", instance_id)


def get_api_server_instance(ec2):
	response = ec2.describe_instances()
	return get_server_instance_details(response, "PublicIpAddress", API_SERVER_IP_ADDRESS)


def get_server_instance_details(response, attr_key, attr_val):
	api_server_instance = None
	for reservation in response['Reservations']:
		for instance in reservation['Instances']:
			if attr_key in instance and attr_val == instance[attr_key]:
				api_server_instance = instance
				break

	if api_server_instance is None:
		print("No server instance found with attr_key,  attr_val = " + attr_key + ", " + attr_val)
		return None

	# print(api_server_instance)
	return api_server_instance


run()