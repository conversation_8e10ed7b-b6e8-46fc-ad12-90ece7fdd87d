import sys
import time
from datetime import date
import subprocess

def backup():
	today = date.today()
	now = int(time.time())
	formatted_date = today.strftime("%d-%m-%Y")
	backup_file_name = "/home/<USER>/mysql-backups/lernen-mysql-backup-" + formatted_date +"-"+str(now)+".sql"

	backup_cmd = "mysqldump -h lernen-prod.c5m2qecqy0qe.us-east-1.rds.amazonaws.com -P 3306 -u lernenprod lernen -p > " + backup_file_name
	print(backup_cmd)
	subprocess.call(backup_cmd, shell=True)
	print("Local backup created :"+backup_file_name)
	upload_to_s3(backup_file_name)
	delete_local_backup(backup_file_name)

def upload_to_s3(backup_file_name):
	print("Uploading backup file to s3 : " + str(backup_file_name) + " ....")
	upload_cmd = "aws s3 cp "+backup_file_name+" s3://lernen-database-backup-v5/ --profile mysql-backup-2024"
	print(upload_cmd)
	subprocess.call(upload_cmd, shell=True)
	print("Uploaded "+backup_file_name+ " to s3://lernen-database-backup-v5/")

def delete_local_backup(backup_file_name):
	print("Deleting local backup file: " + str(backup_file_name) + " ....")
	delete_cmd = "rm " + backup_file_name
	print(delete_cmd)
	subprocess.call(delete_cmd, shell=True)

backup()