GET_STUDENT_API_URL = BASE_URL + "/2.0/student/institute-students/{institute_id}/{academic_session_id}?access_token={access_token}"


def get_students(institute_id, academic_session_id):
    response = requests.get(GET_STUDENT_API_URL.format(institute_id = institute_id,  academic_session_id = academic_session_id, access_token = ACCESS_TOKEN), headers=headers)
    data = json.loads(response.content)
    student_map = {}
    for student in data:
        student_map[student['studentBasicInfo']['admissionNumber'].lower()] = student['studentId']

    print(student_map)
    return student_map
