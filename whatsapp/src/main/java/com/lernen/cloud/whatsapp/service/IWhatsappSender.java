package com.lernen.cloud.whatsapp.service;

import com.embrate.cloud.core.api.whatsapp.WhatsappContentPayload;
import com.embrate.cloud.core.api.whatsapp.WhatsappSendDetails;
import com.lernen.cloud.core.api.common.FileData;

/**
 * Interface class to send whatsapp
 * 
 * <AUTHOR>
 *
 */
public interface IWhatsappSender {

	public Double balance(int instituteId);

	public String uploadMedia(int instituteId, FileData fileData);

	public WhatsappSendDetails sendWhatsappMessage(int instituteId, WhatsappContentPayload message);
}
