package com.lernen.cloud.whatsapp.content.builder;

import com.embrate.cloud.core.api.service.communication.templates.TemplateVariable;
import com.embrate.cloud.core.api.service.communication.templates.TemplateVariableType;
import com.embrate.cloud.core.api.whatsapp.WhatsappContentPayload;
import com.embrate.cloud.core.api.whatsapp.WhatsappTemplateContent;
import com.embrate.cloud.core.lib.templates.notification.NotificationTemplateManager;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.whatsapp.handler.CustomWhatsappHandler;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 
 * <AUTHOR>
 *
 */
public class CustomWhatsappContentBuilder extends WhatsappContentBuilder {
	private static final Logger logger = LogManager.getLogger(CustomWhatsappContentBuilder.class);

	public CustomWhatsappContentBuilder(NotificationTemplateManager notificationTemplateManager) {
		super(notificationTemplateManager);
	}

	public WhatsappContentPayload generateTextContent(UUID templateId, Map<String, String> customVariables,
												 CustomWhatsappHandler.WhatsappUserMetadata whatsappUserMetadata, String instituteName) {

		String customWhatsappText = null;
		String dltTemplateId = null;
		WhatsappTemplateContent whatsappTemplateContent = getWhatsappTemplateContent(templateId);
		if(whatsappTemplateContent != null){
			customWhatsappText = whatsappTemplateContent.getTemplate();
			dltTemplateId = whatsappTemplateContent.getDltTemplateId();
			if(whatsappTemplateContent.getTemplateVariableDetails() == null ||
					CollectionUtils.isEmpty(whatsappTemplateContent.getTemplateVariableDetails().getTemplateVariableList())) {
				return new WhatsappContentPayload(customWhatsappText, dltTemplateId, null, null, null);
			}
			HashMap<TemplateVariableType, String> systemVarMap = getSystemVariables(whatsappTemplateContent.getTemplateVariableDetails(), whatsappUserMetadata,
					instituteName);
			HashMap<String, String> systemVarStringMap = new HashMap<>();
			if(!CollectionUtils.isEmpty(systemVarMap)) {
				for(Map.Entry<TemplateVariableType, String> entry : systemVarMap.entrySet()) {
					systemVarStringMap.put(entry.getKey().name(), entry.getValue());
				}
			}
			List<String> variableValues = new ArrayList<>();
			for(TemplateVariable templateVariable : whatsappTemplateContent.getTemplateVariableDetails().getTemplateVariableList()) {
				if(templateVariable.getTemplateVariableType() != TemplateVariableType.CUSTOM) {
					String variableName = templateVariable.getTemplateVariableType().name();
					if(!CollectionUtils.isEmpty(systemVarStringMap)) {
						variableValues.add(systemVarStringMap.get(variableName));
					}
				} else {
					String variableName = templateVariable.getVarName();
					if(!CollectionUtils.isEmpty(customVariables)) {
						variableValues.add(customVariables.get(variableName));
					}
				}
			}

			return new WhatsappContentPayload(renderTemplate(customWhatsappText, whatsappTemplateContent.getTemplateVariableDetails(),
					systemVarMap, customVariables), dltTemplateId, null, null, variableValues);
		}


		if (StringUtils.isBlank(customWhatsappText)) {
			logger.error("Whatsapp template is not configured for templateId {}", templateId);
			throw new EmbrateRunTimeException("Whatsapp template is not configured for institute");
		}
		return new WhatsappContentPayload(customWhatsappText, dltTemplateId, null,
				null, null);
	}
}
