package com.lernen.cloud.whatsapp.handler;

import com.embrate.cloud.core.api.service.communication.CommunicationServiceActionResponse;
import com.embrate.cloud.core.api.service.communication.UserCommunicationServicePayload;
import com.embrate.cloud.core.api.whatsapp.WhatsappContentPayload;
import com.embrate.cloud.core.api.whatsapp.WhatsappSendDetails;
import com.embrate.cloud.core.lib.service.communication.ICommunicationServiceHandler;
import com.lernen.cloud.core.api.exception.WhatsappRunTimeException;
import com.lernen.cloud.core.utils.PhoneNumberUtils;
import com.lernen.cloud.whatsapp.service.IWhatsappSender;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import static com.lernen.cloud.core.utils.PhoneNumberUtils.DEFAULT_COUNTRY;

/**
 * <AUTHOR>
 */
public class WhatsappServiceHandler implements ICommunicationServiceHandler {
    private static final Logger logger = LogManager.getLogger(WhatsappServiceHandler.class);

    private final IWhatsappSender whatsappSender;

    public WhatsappServiceHandler(IWhatsappSender whatsappSender) {
        this.whatsappSender = whatsappSender;
    }

    @Override
    public  <T extends UserCommunicationServicePayload> CommunicationServiceActionResponse executeServiceAction(int instituteId, String destinationChannelId, T payload) {
        try {
//            if (StringUtils.isBlank(payload.getMessagePayload())) {
//                logger.error("Empty whatsapp content for instituteId {}. Skipping whatsapp!", instituteId);
//                return null;
//            }

            final String e164PhoneNumber = PhoneNumberUtils.getE164FormattedNumber(destinationChannelId, DEFAULT_COUNTRY);
            if (StringUtils.isBlank(e164PhoneNumber)) {
                logger.error("Invalid mobile number {} for message {}", e164PhoneNumber, payload.getMessagePayload());
                return null;
            }

            WhatsappSendDetails whatsappSendDetails = whatsappSender.sendWhatsappMessage(instituteId, new WhatsappContentPayload(e164PhoneNumber,
                    payload.getDltTemplateId(), payload.getImageLink(), payload.getHeaderVariableValues(), payload.getBodyVariableValues()));
            if(whatsappSendDetails == null){
                return null;
            }
            return new CommunicationServiceActionResponse(whatsappSendDetails.getMaskId(),
                    whatsappSendDetails.getMobileNumber(), null, null);
        } catch (WhatsappRunTimeException e) {
            logger.error("Unable to send whatsapp to on number {}", destinationChannelId, e);
        } catch (Exception e) {
            logger.error("Unknown error occurred while sending whatsapp on number {}", destinationChannelId, e);
        }
        return null;

    }
}
